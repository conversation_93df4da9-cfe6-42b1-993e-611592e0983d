#!/usr/bin/env python
"""Default Django settings for Booksy API Django related stuff."""
import os

from django.utils.translation import gettext_lazy as _


from settings.outbox import get_outbox_config

SUIT_CONFIG = {
    'ADMIN_NAME': 'Booksy Admin',
    'SHOW_REQUIRED_ASTERISK': True,
    'CONFIRM_UNSAVED_CHANGES': True,
}
PROJECT_PATH = os.path.realpath(os.path.join(os.path.dirname(__file__), '..'))

DEBUG = False

ADMINS = (('admin', '<EMAIL>'),)
MANAGERS = ADMINS

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'booksy',
        # USER and PASSWORD are set in local.py with SecretManager
        'HOST': os.environ.get('host', 'localhost'),
        'PORT': '5432',
        'CONN_MAX_AGE': 30,
        'OPTIONS': {
            'connect_timeout': 3,
        },
    },
    # REPORTS and REPORTS_READ_ONLY_DB are set in local.py via YAML_CONFIG files
    # 'adyen' DB is magically set to the same value as 'default' in local.py
    # READ_ONLY_DB is set in local.py via YAML_CONFIG files
}

DATABASE_ROUTERS = [
    'lib.models.AdyenRouter',
    'lib.db.PaymentsRouter',
    'lib.db.DraftsRouter',
    'lib.db.UsingDBForReadsRouter',
    'lib.db.ReportsReplicaRouter',
]

SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')


# Local time zone for this installation. Choices can be found here:
# http://en.wikipedia.org/wiki/List_of_tz_zones_by_name
# although not all choices may be available on all operating systems.
# In a Windows environment this must be set to your system time zone.
TIME_ZONE = 'UTC'
USE_DEPRECATED_PYTZ = False

# Language code for this installation. All choices can be found here:
# http://www.i18nguy.com/unicode/language-identifiers.html
LANGUAGE_CODE = 'en-us'
LANGUAGES = (
    ('en', _('English')),
    ('en-gb', _('English (United Kingdom)')),
    ('es', _('Spanish')),
    ('es-es', _('Spanish (Spain)')),
    ('fr', _('French')),
    ('pl', _('Polish')),
    ('pt', _('Portuguese')),
    ('ru', _('Russian')),
    ('uk', _('Ukrainian')),
    ('vi', _('Vietnamese')),
    ('zh', _('Chinese')),
    # DEPRECATED LANGUAGES
    # ('fi', _('Finnish')),
    # ('sv', _('Swedish')),
    # ('de', _('German')),
    # ('ja', _('Japanese')),
)

SITE_ID = 1

# If you set this to False, Django will make some optimizations so as not
# to load the internationalization machinery.
_ENV_USE_I18N = os.environ.get('USE_I18N', '1')
_ENV_USE_I18N = int(_ENV_USE_I18N) if _ENV_USE_I18N.isdigit() else _ENV_USE_I18N
USE_I18N = bool(_ENV_USE_I18N)


# If you set this to False, Django will not format dates, numbers and
# calendars according to the current locale.
USE_L10N = True

FORMAT_MODULE_PATH = ['settings.locale']

# If you set this to False, Django will not use timezone-aware datetimes.
USE_TZ = True

# Absolute filesystem path to the directory that will hold user-uploaded files.
# Example: "/home/<USER>/media.lawrence.com/media/"
MEDIA_ROOT = ''

# URL that handles the media served from MEDIA_ROOT. Make sure to use a
# trailing slash.
# Examples: "http://media.lawrence.com/media/", "http://example.com/media/"
MEDIA_URL = '/media/'

# Absolute path to the directory static files should be collected to.
# Don't put anything in this directory yourself; store your static files
# in apps' "static/" subdirectories and in STATICFILES_DIRS.
# Example: "/home/<USER>/media.lawrence.com/static/"

STATIC_ROOT = os.path.join(PROJECT_PATH, '../statics')

# URL prefix for static files.
# Example: "http://media.lawrence.com/static/"
# STATIC_URL = '/statics/'  # this is parametrized in local.py

STATICFILES_DIRS = (
    # Put strings here, like "/home/<USER>/static" or "C:/www/django/static".
    # Always use forward slashes, even on Windows.
    # Don't forget to use absolute paths, not relative paths.
    os.path.join(PROJECT_PATH, 'statics'),
)

LOCALE_PATHS = (os.path.join(PROJECT_PATH, 'locale'),)

# List of finder classes that know how to find static files in
# various locations.
STATICFILES_FINDERS = (
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
    #    'django.contrib.staticfiles.finders.DefaultStorageFinder',
)

# FILE_UPLOAD_PERMISSIONS = 0o777

# Make this unique, and don't share it with anybody.
SECRET_KEY = 'e)0yokzs2_e@=c6e147)b!tnkld=t3)ycv*_@@)sl^+r*#kd7+'


MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.locale.LocaleMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'lib.dispatch_context.middleware.DispatchContextMiddleware',
    'drf_api_utils.middleware.HighMemoryAllocationMiddleware',
]


PASSWORD_HASHERS = [
    'lib.hashers.PBKDF2PasswordHasher',
    'django.contrib.auth.hashers.PBKDF2SHA1PasswordHasher',
    'django.contrib.auth.hashers.Argon2PasswordHasher',
    'django.contrib.auth.hashers.BCryptSHA256PasswordHasher',
    'django.contrib.auth.hashers.ScryptPasswordHasher',
]  # values from django/conf/global_settings.py


INTERNAL_IPS = ('127.0.0.1',)
# enum for public or private mode

# Python dotted path to the WSGI application used by Django's runserver.
WSGI_APPLICATION = 'wsgi.application'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [
            # added in settings/local.py
        ],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.contrib.auth.context_processors.auth',
                'django.template.context_processors.debug',
                'django.template.context_processors.i18n',
                'django.template.context_processors.media',
                'django.template.context_processors.static',
                'django.template.context_processors.tz',
                'django.template.context_processors.request',
                'django.contrib.messages.context_processors.messages',
                'webapps.admin_extra.context_processors.countries',
            ],
            'debug': DEBUG,
            'libraries': {
                'booksy_admin_menu': 'templatetags.booksy_admin_menu',
            },
        },
    },
]

INSTALLED_APPS = [
    'service',
    'service.search.v2.infrastructure.apps.VenueSearchConfig',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.sites',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.postgres',
    'allauth',
    'allauth.account',
    'allauth.socialaccount',
    'django_saml2_auth',
    'webapps.allauth_google',
    'webapps.suit',
    'django.contrib.admin.apps.SimpleAdminConfig',
    'django_extensions',
    'django_countries',
    'pgtrigger',
    'adminplus',  # for custom admin pages not tied to particular model
    # Uncomment the next line to enable admin documentation:
    # 'django.contrib.admindocs',
    'django_admin_lightweight_date_hierarchy',
    'rangefilter',
    'rest_framework',
    'dal',
    'dal_select2',
    'collectfast',
    'django_socio_grpc',
    'lib',  # test purposes
    'corsheaders',
    'django_filters',
    'ckeditor',
    'qr_code',
    'lib.es_history',
    'lib.protobuf',
    'encrypted_secrets',
    'django_jsonform',
    'django_dbz_admin',
    'webapps.appointment_drafts',
    'webapps.adyen',
    'webapps.b2b_referral',
    'webapps.billing',
    'webapps.booking',
    'webapps.booksy_med',
    'webapps.b2b_acc_deletion',
    'webapps.session.apps.SessionConfig',
    'webapps.boost',
    'webapps.braintree_app',
    'webapps.business',
    'webapps.business_related',
    'webapps.business_consents',
    'webapps.best_of_booksy',
    'webapps.business_calendar',
    'webapps.french_certification',
    'webapps.donation',
    'webapps.photo',
    'webapps.user',
    'webapps.structure',
    'webapps.marketing',
    'webapps.notification',
    'webapps.onboarding_space.infrastructure.apps.OnboardingSpaceConfig',
    'webapps.elasticsearch',
    'webapps.reports',
    'webapps.reviews',
    'webapps.registrationcode',
    'webapps.statistics',
    'webapps.images',
    'webapps.pos',
    'webapps.premium_services',
    'webapps.payment_gateway',
    'webapps.payment_providers',
    'webapps.payments',
    'webapps.point_of_sale',
    'webapps.commission',
    'webapps.printer_api',
    'webapps.profile_completeness',
    'webapps.profile_setup',
    'webapps.public_partners',
    'webapps.purchase',
    'webapps.register',
    'webapps.feeds',
    'webapps.feeds.google',
    'webapps.pop_up_notification',
    'webapps.family_and_friends',
    'webapps.feeds.groupon',
    'webapps.feedback',
    'webapps.experiment',
    'webapps.experiment_v3',
    'webapps.search_engine_tuning',
    'webapps.segment',
    'webapps.sequencing_number',
    'webapps.marketplace',
    'webapps.schedule',
    'webapps.pattern',
    'webapps.partners',
    'webapps.survey',
    'webapps.text_invite',
    'webapps.third_tier_wait_list',
    'webapps.consents',
    'webapps.r_and_d',
    'webapps.kill_switch',
    'webapps.admin_query_fields',
    'webapps.c2b_referral',
    'webapps.df_creator',
    'webapps.wait_list',
    'webapps.market_pay',
    'webapps.metrics',
    'webapps.warehouse',
    'webapps.invoice',
    'webapps.celery',
    'webapps.script_runner',
    'webapps.utt',
    'webapps.zoom',
    'webapps.invoicing',
    'webapps.message_blast',
    'webapps.hints',
    'webapps.help_center',
    'webapps.intro_screen',
    'webapps.navision',
    'webapps.voucher',
    'webapps.qr_code_origami',
    'webapps.stats_and_reports',
    'webapps.contact_us',
    'webapps.stripe_app',
    'webapps.stripe_integration',
    'webapps.stripe_terminal',
    'webapps.whats_new',
    'webapps.versum_migration',
    'webapps.turntracker',
    'webapps.pubsub',
    'webapps.google_sign_in',
    'webapps.google_business_profile.infrastructure.apps.GoogleBusinessProfileConfig',
    'webapps.google_places.infrastructure.apps.GooglePlacesConfig',
    'webapps.subdomain_grpc',
    'webapps.ecommerce',
    'webapps.security_blacklist',
    'webapps.booksy_gift_cards',
    'webapps.booksy_pay',
    'webapps.visibility_promotion',
    'bo_django_outbox',
    # temporary we disable creation of replication sets
    # 'webapps.database_publications',
    'webapps.admin_extra',  # must be last - unregisters some admins,
    'oauth2_provider',
]


MIGRATION_MODULES = {'oauth2_provider': None}


SESSION_ENGINE = 'webapps.session.session_store'
SESSION_SERIALIZER = 'django.contrib.sessions.serializers.JSONSerializer'
SESSION_CACHE_ALIAS = 'session'
SESSION_AGE_DAYS = 60
SESSION_COOKIE_AGE = 60 * 60 * 24 * SESSION_AGE_DAYS
SESSION_BUMP_AFTER = 1 * 24 * 60 * SESSION_AGE_DAYS
SUPERUSER_SESSION_COOKIE_AGE = 60 * 30
SUPERUSER_SESSION_BUMP_AFTER = 60 * 5


# CELERY
EMAIL_BACKEND = 'lib.email.backend.BooksyQueueEmailBackendRedisFIFO'
BOOKSY_CELERY_EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
BOOKSY_QUEUE_MESSAGE_LIFETIME = 86399  # seconds
BOOKSY_CELERY_EMAIL_TASK_EXPIRES = 59  # seconds
BOOKSY_CELERY_EMAIL_BATCH_SIZE = 500
BOOKSY_CELERY_EMAIL_GROUP_SIZE = 50
CELERY_EMAIL_TASK_CONFIG = {
    'ignore_result': True,
}


ALLOWED_HOSTS = [
    '127.0.0.1',
    # api domain is added in settings/local.py
]
POD_IP = os.environ.get('POD_IP')
if POD_IP:
    ALLOWED_HOSTS.append(POD_IP)

DEFAULT_AUTO_FIELD = 'django.db.models.AutoField'

# We cannot override this settings because in this mode
# migrations and all django machinary stops work
# 'user.User'
AUTH_USER_MODEL = 'auth.User'  # pylint: disable=hard-coded-auth-user

PUSH_NOTIFICATION_TEST = False

# Google sign in
ACCOUNT_USER_MODEL_USERNAME_FIELD = 'email'
ACCOUNT_LOGOUT_ON_GET = True
ACCOUNT_SESSION_REMEMBER = True
SOCIALACCOUNT_LOGIN_ON_GET = True
SOCIALACCOUNT_QUERY_EMAIL = True
SOCIALACCOUNT_ADAPTER = 'webapps.allauth_google.account_adapter.CustomAccountAdapter'

STATICFILES_STORAGE = 'settings.storage.StaticRootS3Boto3Storage'

CORS_ALLOWED_ORIGINS = [
    'https://booksy.com',
    'https://stats-and-reports.booksy.com',  # added for performance reasons
    'https://billing.booksy.com',  # added for performance reasons
    'https://uat.booksy.pm',
    'https://b.booksy.net',
    'http://localhost:8000',
    'http://localhost:8888',
    'https://cx-onboarding-microfrontend.booksy.com',
]
CORS_ALLOWED_ORIGIN_REGEXES = [
    r"^https:\/\/(?:[\w\-\_]+\.)+booksy\.com$",
    r"^https:\/\/(?:[\w\-\_]+\.)+uat\.booksy\.pm$",
    r"^https:\/\/(?:[\w\-\_]+\.)+t1\.booksy\.pm$",
    r"^https:\/\/(?:[\w\-\_]+\.)+booksy\.pm$",
    r"^https:\/\/(?:[\w\-\_]+\.)+booksy\.net$",
    r"^https:\/\/(?:[\w\-\_]+\.)+b\.booksy\.net$",
    r"^https?:\/\/(?:[\w\-\_]+\.)*localhost(:\d+)?$",
    r"^https?:\/\/(?:[\w\-\_]+\.)*local(:\d+)?$",
]
CORS_ALLOW_METHODS = [
    'GET',
    'POST',
    'PUT',
    'PATCH',
    'DELETE',
    'OPTIONS',
]
CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'accept-language',
    'authorization',
    'b3',
    'baggage',
    'bksreqid',
    'cache-control',
    'content-disposition',
    'content-type',
    'cookie',
    'if-modified-since',
    'pragma',
    'sentry-trace',
    'Tracestate',
    'traceparent',
    'x-access-token',
    'x-analytics-tokens',
    'x-api-key',
    'x-app-version',
    'x-appsflyer-user-id',
    'x-b3-sampled',
    'x-b3-spanid',
    'x-b3-traceid',
    'x-booksy-opts',
    'x-booksy-user',
    'x-business-timezone',
    'x-datadog-origin',
    'x-datadog-parent-id',
    'x-datadog-sampling-priority',
    'x-datadog-trace-id',
    'x-fingerprint',
    'x-hcaptcha-token',
    'x-ignore-403',
    'x-nethone-attempt-reference',
    'x-otp-code',
    'x-recaptcha-site-key',
    'x-recaptcha-token',
    'x-requested-with',
    'x-user-pseudo-id',
]
CORS_EXPOSE_HEADERS = [
    'Cache-Control',
    'Content-Language',
    'Content-Length',
    'Content-Type',
    'Content-Disposition',
    'Expires',
    'Last-Modified',
    'Location',
    'Pragma',
    'X-SessionAuthOrigin',
]

CKEDITOR_CONFIGS = {
    "default": {
        "removePlugins": "flash",
    }
}

# Event Outbox Configuration
# Lazy configuration to avoid circular imports
OUTBOX_SETTINGS = get_outbox_config()
