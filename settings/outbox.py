import os
from functools import lru_cache


@lru_cache(maxsize=1)
def get_outbox_config():
    """
    Lazily loaded and cached outbox configuration.
    Using existing project settings variables directly.
    Imports inside function to avoid circular imports.
    """
    # Lazy import to avoid circular dependency
    from settings.local import LOCAL_DEPLOYMENT, API_COUNTRY, PYTEST

    # Basic configuration - Required settings
    config = {
        "AUTO_REGISTER": os.getenv("OUTBOX_AUTO_REGISTER", "False").lower() == "true",
        "DEFAULT_NAMESPACE": os.getenv("KAFKA_RESOURCE_NAMESPACE", f"localhost.{API_COUNTRY}"),
        "DEFAULT_SOURCE": "core",
        "INVALID_EVENT_BEHAVIOR": "error",  # or 'warn'
        "KAFKA_BOOTSTRAP_SERVERS": os.getenv("KAFKA_BOOTSTRAP_SERVERS", "localhost:9092"),
        "SCHEMA_REGISTRY_URL": os.getenv("SCHEMA_REGISTRY_URL", "http://localhost:18081"),
        "KAFKA_CONNECT_URL": os.getenv("KAFKA_CONNECT_URL", "http://localhost:8083"),
        "KAFKA_OAUTH_TOKEN_URL": os.getenv("KAFKA_OAUTH_TOKEN_URL"),
        "DATABASE_HOSTNAME": os.getenv("OUTBOX_DATABASE_HOSTNAME", "localhost"),
        "DATABASE_NAME": os.getenv("OUTBOX_DATABASE_NAME", "booksy"),
        "DATABASE_USERNAME": os.getenv("OUTBOX_DATABASE_USERNAME", "postgres"),
        "DATABASE_PASSWORD": os.getenv("OUTBOX_DATABASE_PASSWORD", "postgres"),
        "DATABASE_REPLICATION_SLOT": os.getenv(
            "OUTBOX_DATABASE_REPLICATION_SLOT", f"redpanda_outbox_{API_COUNTRY}"
        ),
        "DATABASE_PUBLICATION_NAME": os.getenv(
            "OUTBOX_DATABASE_PUBLICATION_NAME", f"redpanda_outbox_publication_{API_COUNTRY}"
        ),
        "DATABASE_PUBLICATION_AUTOCREATE": os.getenv(
            "OUTBOX_DATABASE_PUBLICATION_AUTOCREATE", "False"
        ).lower()
        == "true",
    }

    if PYTEST:
        config["SCHEMA_REGISTRY_URL"] = "mock://localhost:18081"

    # Production configuration with authentication (for live and test environments, not dev)
    if not LOCAL_DEPLOYMENT:  # This means live or test environment
        from env_secrets.v2.secrets_providers import (
            GCPAdaptiveSecretsProvider,
            GCPSecretsConfig,
        )

        kafka_secrets_backend = GCPAdaptiveSecretsProvider(
            GCPSecretsConfig(
                project_id=os.environ.get("KAFKA_RESOURCES_SECRET_MANAGER_PROJECT_ID"),
                region_prefix='booksy-kafka-',
            )
        )

        kafka_username = kafka_secrets_backend.read_secret("outbox-username")
        kafka_password = kafka_secrets_backend.read_secret("outbox-password")
        db_username = kafka_secrets_backend.read_secret("postgres-core-outbox-username")
        db_password = kafka_secrets_backend.read_secret("postgres-core-outbox-password")
        oauth_client_id = kafka_secrets_backend.read_secret("oauth-client-id")
        oauth_client_secret = kafka_secrets_backend.read_secret("oauth-client-secret")

        config.update(
            {
                "KAFKA_SECURITY_PROTOCOL": "SASL_SSL",
                "KAFKA_SASL_MECHANISM": "SCRAM-SHA-256",
                "KAFKA_SASL_USERNAME": kafka_username,
                "KAFKA_SASL_PASSWORD": kafka_password,
                "KAFKA_OAUTH_CLIENT_ID": oauth_client_id,
                "KAFKA_OAUTH_CLIENT_SECRET": oauth_client_secret,
                "DATABASE_USERNAME": db_username,
                "DATABASE_PASSWORD": db_password,
            }
        )

    return config
