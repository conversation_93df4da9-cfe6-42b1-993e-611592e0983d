#!/usr/bin/env python
"""Handle machine specific settings.

Phases:
 - ENVIRON HANDLING - some settings are based on enviroment settings
 - MAGIC IF (deprecated) - conditional settings
 - YAML HANDLING - settings loaded from machine specific YAML file

"""

# pylint: disable=too-many-lines
import copy
import datetime
import json
import os
import re
import sys
import typing
from dataclasses import dataclass
from io import StringIO
from urllib.parse import urlparse

import braintree
import yaml
from yaml import Loader, SafeLoader

from country_config import CountryConfig
from country_config.enums import Country
from env_secrets.v1.secrets_manager import SecretsManager
from env_secrets.v2.base_core_secret_provider_factory import get_base_secrets_provider
from env_secrets.enums import SecretName
from lib.enums import AppleService, SMSPaidEnum, SMSTypeEnum
from lib.jwk import load_jwk_private_key, load_jwk_public_key
from lib.utils import str_to_bool
from settings._django import (
    ALLOWED_HOSTS,
    CORS_ALLOWED_ORIGINS,
    CORS_ALLOWED_ORIGIN_REGEXES,
    DATABASES,
    LANGUAGES,
    PROJECT_PATH,
    <PERSON>AT<PERSON><PERSON>LES_DIRS,
    TEMPLATES,
)
from settings.billing import (  # pylint: disable=unused-import
    BILLING_ATTEMPTS_LIMIT_MAX_COUNT,
    BILLING_ATTEMPTS_LIMIT_RESET_MINUTES,
    BILLING_FIRST_N_SMS_LIMITS_NON_TRIAL_DEFAULT,
    BILLING_FIRST_N_SMS_LIMITS_NON_TRIAL_PER_COUNTRY,
    BILLING_STRIPE_PAYMENT_METHODS_DEFAULT,
    BRAINTREE_PAYMENT_METHODS_ALL,
    BRAINTREE_PAYMENT_METHODS_BY_COUNTRY,
    BRAINTREE_PAYMENT_METHODS_DEFAULT,
    COUNTRY__BILLING_CURRENCIES,
    COUNTRY__BILLING_STRIPE_PAYMENT_METHODS,
    SMS_COST_ALERT_DEFAULT_LEVEL,
    OFFLINE_TO_ONLINE_MIGRATION_CAMPAIGN_VALID_DAYS,
    OFFLINE_TO_ONLINE_MIGRATION_CAMPAIGN_BUFFER_HOURS,
    BILLING_STAFF_UNLIMITED_QTY,
    BILLING_LONG_SUBSCRIPTION_STAFFER_COUNT_LIMIT,
    BILLING_COUNTRIES_ALLOW_AUTO_SUBSCRIPTION_CANCELLATION,
    BILLING_COUNTRIES_ALLOW_CHANGE_CARD_NETWORK,
    BILLING_COUNTRIES_AUTO_RETRY_CHARGE_DAYS,
    BILLING_COUNTRIES_MIGRATED_SUBSCRIPTION_INITIAL_TASK,
)
from settings.message_blast import (  # pylint: disable=unused-import
    DOUBLE_OPT_IN_COUNTRIES,
)
from settings.defaults import (
    API_VERSION,
    B2B_REFERRAL_ENABLED,
    BOOKSY_PAY_CASHBACK_PER_COUNTRY,
    BUSINESSES_MULTI_CATEGORIES,
    BUSINESS_GLOBAL_PAYOUT_METHOD_CHANGE_ALLOWED,
    CAN_CREATE_UMBRELLA,
    CONSENT_FORM_SMS_REQUEST,
    CORS__ALLOW_ALL_ORIGINS,
    COUNTRY__BIZ__SMS_INVITATION_PERIOD_LIMIT,
    COUNTRY__CHARGE_FOR_STAFFERS_ENABLED,
    COUNTRY__DISCOUNT_AFTER_POB_ENABLED,
    COUNTRY__INVOICE_DOWNLOAD_ENABLED,
    COUNTRY__SHOW_TRIAL_INFO,
    COUNTRY__STAFFERS_PLAN_SWITCH_ENABLED,
    COUNTRY__STATUS_FLOW__TRIAL_COUNT_FROM_DATE_CREATED,
    COUNTRY__SUBSCRIPTION_PURCHASE_LIMIT_STAFF_NUM,
    COUNTRY__SUBSCRIPTION_TEST_BUSINESSES,
    CURRENT_BUSINESS_ANDROID_FORCE_UPDATE,
    CURRENT_BUSINESS_ANDROID_VERSION,
    CURRENT_BUSINESS_IOS_FORCE_UPDATE,
    CURRENT_BUSINESS_IOS_VERSION,
    CURRENT_CUSTOMER_ANDROID_FORCE_UPDATE,
    CURRENT_CUSTOMER_ANDROID_VERSION,
    CURRENT_CUSTOMER_IOS_FORCE_UPDATE,
    CURRENT_CUSTOMER_IOS_VERSION,
    ELEARNING_ENABLED,
    FAMILY_AND_FRIENDS_ENABLED,
    GDPR_ANNEX_COUNTRIES,
    GDPR_ANNEX_SETTINGS,
    GROUP_PERMISSIONS_ENABLED,
    IAP_ANDROID,
    IAP_BRAINTREE,
    IAP_IOS,
    INVOICES_ENABLED,
    MARKET_PAY_B2B_REFERRAL_ENABLED,
    MARKET_PAY_ENABLED,
    MAX_MESSAGE_BLAST_PARTS_COUNTRY_SMS,
    NOSHOW_FEATURE_EDU_MAIL,
    NOSHOW_MAIL,
    ONBOARDING_ENABLED,
    POPUP_NOTIFICATIONS_ENABLED,
    POPUP_PHASE2,
    POPUP_PHASE3,
    POPUP_PHASE4,
    POS,
    POS__APPLE_PAY,
    POS__BSX,
    POS__DEFAULT_PAYMENT_PROVIDER_V2,
    POS__GOOGLE_PAY,
    POS__PAY_BY_APP,
    POS__PLANS_PER_COUNTRY,
    POS__PLANS_PER_COUNTRY_LEGACY,
    POS__PREPAYMENTS,
    POS__REFUNDS,
    POS__SQUARE,
    POS__FAST_PAYOUTS,
    POS__STRIPE_TERMINAL,
    POS__TAP_TO_PAY,
    POS__TAP_TO_PAY_MIN_IOS_VERSION,
    POS__BOOKSY_PAY,
    POS__BLIK,
    POS__KLARNA,
    STATUS_FLOW__TRIAL_DURATION,
    STATUS_FLOW__TRIAL_EXTENSION,
    STRIPE_ALLOW_TIPS_WITHOUT_FEE,
    STRIPE_SKIP_RADAR_IN_SUBSCRIPTIONS,
    UNVERIFIED_PUSH_PAYMENTS,
    VOUCHERS_ENABLED,
    WAITLIST_ENABLED,
    POS__KEYED_IN_PAYMENT,
)  # app force version stuff
from settings.sms import (
    ALL_ZERO_LIMIT,
    COUNTRY__BIZ__SMS_LIMIT_CHANGE_BLOCKED,
    COUNTRY__BIZ__SMS_LIMIT_EXTEND_BLOCKED,
    COUNTRY__SMS_LIMITS_APPLY,
    COUNTRY__SMS_LIMITS_IN_SUBSCRIPTION,
    DEFAULT_FIRST_N_SMS,
    DEFAULT_TOTAL_M_SMS,
    SMS_BUSINESS_MONTHLY_LIMITS_CHOICES,
    SMS_SETTINGS_PER_COUNTRY,
    VONAGE_COUNTRY_SECRETS,
)
from settings.structure import REGION_STRUCTURE_PER_COUNTRY
from settings.yaml_admin import YamlAdminManager
from sre_performance.tools import set_performance_test_flag
from webapps.navision.enums import InvoiceDetailsFormsEnum, InvoicePaymentSource, InvoiceService
from webapps.notification.enums import NotificationService
from webapps.structure.enums import RegionType


def construct_yaml_str(self, node):
    # Override the default string handling function
    # to always return unicode objects
    return self.construct_scalar(node)


# force yaml loader to work with unicode
Loader.add_constructor('tag:yaml.org,2002:str', construct_yaml_str)
SafeLoader.add_constructor('tag:yaml.org,2002:str', construct_yaml_str)


################################
####### ENVIRON HANDLING #######
################################

if 'BOOKSY_VARIANT' in os.environ:
    _deployment_variant = os.environ['BOOKSY_VARIANT']
    API_COUNTRY = os.environ['BOOKSY_COUNTRY_CODE']
    BOOKSY_DOMAIN = os.environ['ENVIRONMENT_DOMAIN']
    YAML_BASE_FILE = os.environ['CONF_YAML']
    YAML_ADMIN_FILE = ''
    if _deployment_variant == 'live':
        # do not use admin.yaml - use country specific live yamls from repo
        YAML_ADMIN_FILE = os.path.join(PROJECT_PATH, 'config', 'live', f'{API_COUNTRY}.yaml')
else:
    # developer's host
    _deployment_variant = 'dev'
    API_COUNTRY = os.environ.get('BOOKSY_COUNTRY_CODE', 'us')
    BOOKSY_DOMAIN = 'localhost:8888'
    YAML_BASE_FILE = os.path.join(PROJECT_PATH, 'config/dev', f'{API_COUNTRY}.yaml')
    YAML_ADMIN_FILE = os.path.join(PROJECT_PATH, 'config', 'admin.yaml')
    if FX_API_CONF_FILE := os.getenv('FX_API_CONF_FILE'):
        YAML_BASE_FILE = os.path.join(PROJECT_PATH, FX_API_CONF_FILE)

CI_TEST = str_to_bool(os.environ.get('CI_TEST', 'False'))
PYTEST = 'PYTEST' in os.environ or any(x in sys.argv[0] for x in ('py.test', 'pytest'))
SAVE_HISTORY = not PYTEST
RUN_MODE = 'uwsgi' if 'UWSGI_ORIGINAL_PROC_NAME' in os.environ else 'tornado'
# in case -n0 will be default
# in other cases will be some integer
# for more info read
# https://pypi.org/project/pytest-xdist/
DEFAULT_PYTEST_WORKER = 'master'
PYTEST_WORKER = os.environ.get(
    'PYTEST_XDIST_WORKER',
    DEFAULT_PYTEST_WORKER,
)
PYTEST_XDIST_WORKER_COUNT = int(os.environ.get('PYTEST_XDIST_WORKER_COUNT', 1))
CELERY_ALWAYS_EAGER = False

if CI_TEST:
    YAML_ADMIN_FILE = os.path.join(PROJECT_PATH, 'config', 'admin.yaml')

if PYTEST:
    # pytest environment
    # use YAML_BASE_FILE from legit settings to get DB etc.
    # but override it with pytest.yaml to fix api country etc.
    YAML_ADMIN_FILE = os.path.join(PROJECT_PATH, 'config', 'pytest.yaml')
    CELERY_ALWAYS_EAGER = True
    PUSH_NOTIFICATION_TEST = True
    DISABLE_STOP_FREEZETIME_IN_TEST = True

BAKER_CUSTOM_CLASS = 'lib.booksy_baker.BooksyBaker'

if not API_COUNTRY:
    raise NotImplementedError('API_COUNTRY is required for service to function correctly')

BASE_TEMPLATE_DIR = os.path.join(PROJECT_PATH, 'templates')
STATICS_DIR = os.path.join(PROJECT_PATH, 'statics')


###############################
######## YAML HANDLING ########
###############################

# base config designed by deploy manager
with open(YAML_BASE_FILE, 'rt', encoding='utf-8') as f:
    # render yaml file with environ variables
    _yaml = f.read()
    for _key in [
        'BOOKSY_COUNTRY_CODE',
        'BOOKSY_REDIS_DB',
        'BASE_URL',
        'ENVIRONMENT_DOMAIN',
        'NAMESPACE',
    ]:
        if _key not in os.environ:
            continue
        _yaml = _yaml.replace(f'${_key}', os.environ[_key])
    f = StringIO(_yaml)
    # load rendered yaml
    YAML_BASE_CONFIG = yaml.safe_load(f)

YAML_CONFIG = YAML_BASE_CONFIG.copy()

LIVE_DEPLOYMENT = _deployment_variant == 'live'
LOCAL_DEPLOYMENT = _deployment_variant == 'dev'

if _deployment_variant in ['live', 'dev']:
    DEPLOYMENT_LEVEL = _deployment_variant
    LEGACY_DEPLOYMENT_LEVEL = _deployment_variant
elif _deployment_variant in ['test']:
    DEPLOYMENT_LEVEL = BOOKSY_DOMAIN
    pattern = re.compile(r'(.+)\.booksy\.')
    LEGACY_DEPLOYMENT_LEVEL = f'test_{pattern.search(BOOKSY_DOMAIN).group(1).replace(".", "-")}'
else:
    raise EnvironmentError('BOOKSY_VARIANT should be live, test or dev')

if LIVE_DEPLOYMENT and PYTEST:
    raise RuntimeError(  # pylint: disable=broad-exception-raised
        'DO NOT RUN TESTS ON PRODUCTION, BRO!'
    )

AUTOMATION_TESTS_ENV = BOOKSY_DOMAIN in [
    'android.t1.booksy.pm',
    'automation-data-prep.t1.booksy.pm',
    'automation-tests.t1.booksy.pm',
    'e2e-android-biz.t1.booksy.pm',
    'e2e-android-customer.t1.booksy.pm',
    'e2e-auto-biz.t1.booksy.pm',
    'e2e-ios-biz.t1.booksy.pm',
    'e2e-ios-customer.t1.booksy.pm',
    'e2e-prov-boar-web.t1.booksy.pm',
    'e2e-prov-marketing.t1.booksy.pm',
    'e2e-px-conversion.t1.booksy.pm',
    'e2e-cx-onboarding.t1.booksy.pm',
    'e2e-customer-search.t1.booksy.pm',
    'wgancarczyk1.t1.booksy.pm',
    'e2e-px-engagement.t1.booksy.pm',
    'e2e-fin-services.t1.booksy.pm',
    'android.env.booksy.pm',
    'automation-data-prep.env.booksy.pm',
    'e2e-android-biz.env.booksy.pm',
    'e2e-android-customer.env.booksy.pm',
    'e2e-auto-biz.env.booksy.pm',
    'e2e-ios-biz.env.booksy.pm',
    'e2e-ios-customer.env.booksy.pm',
    'e2e-prov-boar-web.env.booksy.pm',
    'e2e-prov-marketing.env.booksy.pm',
    'e2e-px-conversion.env.booksy.pm',
    'e2e-cx-onboarding.env.booksy.pm',
    'e2e-customer-search.env.booksy.pm',
    'wgancarczyk1.env.booksy.pm',
    'e2e-px-engagement.env.booksy.pm',
    'e2e-fin-services.env.booksy.pm',
    'e2e-android-biz-canary.env.booksy.pm',
    'e2e-android-cust-canary.env.booksy.pm',
]
UAT_ENV = BOOKSY_DOMAIN in [
    'uat.booksy.pm',
]

if LOCAL_DEPLOYMENT:
    STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'
    STATICFILES_DIRS = [os.path.join(PROJECT_PATH, 'statics')] + list(STATICFILES_DIRS)

# initiate SecretProvider
USE_NEW_SECRETS_MANAGER = str_to_bool(os.environ.get('USE_NEW_SECRETS_MANAGER', 'False'))
SecretsProvider = (
    get_base_secrets_provider() if USE_NEW_SECRETS_MANAGER else SecretsManager().provider
)

# admin config edited in admin panel secret URL
S3_YAML_AWS_ACCESS_KEY = SecretsProvider.read_secret('S3_YAML_AWS_ACCESS_KEY')
S3_YAML_AWS_SECRET = SecretsProvider.read_secret('S3_YAML_AWS_SECRET')
manager = YamlAdminManager(
    deployment_level=LEGACY_DEPLOYMENT_LEVEL,
    api_country=API_COUNTRY,
    yaml_admin_file=YAML_ADMIN_FILE,
    ci_test=CI_TEST,
    aws_access_key_id=S3_YAML_AWS_ACCESS_KEY,
    aws_secret_access_key=S3_YAML_AWS_SECRET,
)
YAML_ADMIN_CONFIG = manager.get_yaml_admin()

YAML_CONFIG.update(YAML_ADMIN_CONFIG or {})

# DB, ES and Redis
if CI_TEST and PYTEST:
    worker_pattern = re.compile(r'\d+')
    if PYTEST_XDIST_WORKER_COUNT > 1 and (result_match := worker_pattern.search(PYTEST_WORKER)):
        _id = int(result_match.group(0))
    else:
        _id = 0
    base = 10 * _id
    BROKER_URL = f'redis://localhost:6379/{base + 1}'
    REDIS_RIVER = f'redis://localhost:6379/{base + 2}'
    DJANGO_REDIS_CACHE_URL = f'redis://localhost:6379/{base + 3}'
    CELERY_BACKEND_URL = f'redis://localhost:6379/{base + 4}'
    CELERY_BEAT_URL = BROKER_URL
    DJANGO_REDIS_STORE_CACHE_URL = DJANGO_REDIS_CACHE_URL
    REDIS_FIFO = f'redis://localhost:6379/{base + 5}'
    REDIS_CALENDAR = f'redis://localhost:6379/{base + 6}'
    REDIS_REQUEST_COUNTER = f'redis://localhost:6379/{base + 7}'
    REDIS_SUBDOMAINS = f'redis://localhost:6379/{base + 8}'
    REDIS_SIMPLIFIED_BOOKING_PROVIDER_FEATURE_CHECKLIST = f'redis://localhost:6379/{base + 9}'
else:
    BROKER_URL = YAML_CONFIG.get('celery_broker_url')
    REDIS_RIVER = YAML_CONFIG.get('river_redis', 'redis://localhost:6379/2')
    DJANGO_REDIS_CACHE_URL = YAML_CONFIG.get('celery_bulk_cache') or 'redis://redis:6379/2'
    CELERY_BACKEND_URL = YAML_CONFIG.get('celery_backend_url')
    CELERY_BEAT_URL = YAML_CONFIG.get('celery_beat_url') or BROKER_URL
    DJANGO_REDIS_STORE_CACHE_URL = YAML_CONFIG.get('store_redis_url') or DJANGO_REDIS_CACHE_URL
    REDIS_FIFO = YAML_CONFIG.get('redis_fifo') or DJANGO_REDIS_CACHE_URL
    REDIS_CALENDAR = YAML_CONFIG.get('redis_calendar') or DJANGO_REDIS_CACHE_URL
    REDIS_REQUEST_COUNTER = YAML_CONFIG.get('redis_request_counter') or DJANGO_REDIS_CACHE_URL
    REDIS_SUBDOMAINS = YAML_CONFIG.get('subdomain_cache_url') or DJANGO_REDIS_CACHE_URL
    REDIS_SIMPLIFIED_BOOKING_PROVIDER_FEATURE_CHECKLIST = (
        YAML_CONFIG.get('simplified_booking_redis_url') or DJANGO_REDIS_CACHE_URL
    )


if LIVE_DEPLOYMENT:
    REDIS_THROTTLING = YAML_CONFIG.get('throttling_redis')
    THROTTLING_PREVIEW_MODE = False
else:
    THROTTLING_PREVIEW_MODE = False
    REDIS_THROTTLING = DJANGO_REDIS_CACHE_URL
THROTTLING_DEBUG_MODE = False


REDIS_RIVER_CLIENT_CONNECTION_CONFIGURATION = {
    'max_connections': 100,
    'health_check_interval': 50,  # should be less than timeout on server
    'socket_timeout': 4,
    'socket_connect_timeout': 4,
    'retry_on_timeout': True,
}

REDIS_SUBDOMAINS_CLIENT_CONNECTION_CONFIGURATION = {
    'max_connections': 100,
    'health_check_interval': 50,
    'socket_timeout': 4,
    'socket_connect_timeout': 4,
    'retry_on_timeout': True,
}

CACHES = {
    # email bulk cache
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": DJANGO_REDIS_CACHE_URL,
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "COMPRESSOR": "django_redis.compressors.zlib.ZlibCompressor",
            "CONNECTION_POOL_KWARGS": {
                "max_connections": 100,
                "health_check_interval": 50,  # should be less than timeout on server
                "retry_on_timeout": True,
            },
            "REDIS_CLIENT_KWARGS": {
                "health_check_interval": 50,  # should be less than timeout on server
            },
            "IGNORE_EXCEPTIONS": False,
            "SOCKET_TIMEOUT": 4,
            "SOCKET_CONNECT_TIMEOUT": 4,
        },
    },
    "cache_fifo": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": REDIS_FIFO,
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "COMPRESSOR": "django_redis.compressors.zlib.ZlibCompressor",
            "CONNECTION_POOL_KWARGS": {
                "max_connections": 100,
                "health_check_interval": 50,  # should be less than timeout on server
                "retry_on_timeout": True,
            },
            "REDIS_CLIENT_KWARGS": {
                "health_check_interval": 50,  # should be less than timeout on server
            },
            "IGNORE_EXCEPTIONS": False,
            "SOCKET_TIMEOUT": 4,
            "SOCKET_CONNECT_TIMEOUT": 4,
        },
    },
    "session": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": DJANGO_REDIS_CACHE_URL,
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "COMPRESSOR": "django_redis.compressors.zlib.ZlibCompressor",
            "CONNECTION_POOL_KWARGS": {
                "max_connections": 100,
                "health_check_interval": 50,  # should be less than timeout on server
                "retry_on_timeout": True,
            },
            "REDIS_CLIENT_KWARGS": {
                "health_check_interval": 50,  # should be less than timeout on server
            },
            "IGNORE_EXCEPTIONS": False,
            "SOCKET_TIMEOUT": 4,
            "SOCKET_CONNECT_TIMEOUT": 4,
        },
        "TIMEOUT": 60 * 60,
    },
    'throttling': {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": REDIS_THROTTLING,
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "COMPRESSOR": "django_redis.compressors.zlib.ZlibCompressor",
            "CONNECTION_POOL_KWARGS": {
                "max_connections": 100,
                "health_check_interval": 50,  # should be less than timeout on server
                "retry_on_timeout": True,
            },
            "REDIS_CLIENT_KWARGS": {
                "health_check_interval": 50,  # should be less than timeout on server
            },
            "IGNORE_EXCEPTIONS": False,
            'SOCKET_TIMEOUT': 1,
            'SOCKET_CONNECT_TIMEOUT': 1,
        },
    },
    "storage": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": DJANGO_REDIS_STORE_CACHE_URL,
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "COMPRESSOR": "django_redis.compressors.zlib.ZlibCompressor",
            "CONNECTION_POOL_KWARGS": {"max_connections": 100},
            "IGNORE_EXCEPTIONS": False,
            "SOCKET_TIMEOUT": 4,
            "SOCKET_CONNECT_TIMEOUT": 4,
        },
    },
    "celery_backend": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": CELERY_BACKEND_URL,
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "COMPRESSOR": "django_redis.compressors.zlib.ZlibCompressor",
            "CONNECTION_POOL_KWARGS": {
                "max_connections": 100,
                "health_check_interval": 50,  # should be less than timeout on server
                "retry_on_timeout": True,
            },
            "REDIS_CLIENT_KWARGS": {
                "health_check_interval": 50,  # should be less than timeout on server
            },
            "IGNORE_EXCEPTIONS": False,
        },
        "TIMEOUT": 60 * 60 * 24 * 2,  # 48h
    },
}

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {
            'min_length': 8,
        },
    },
    {
        'NAME': 'lib.validators.BooksyPasswordValidator',
    },
]

RAISE_CACHE_ERRORS = False

# UPDATE DATABASE SETTINGS
_DEFAULT_DB_USER = SecretName.CORE_DEFAULT_DB_USER
_DEFAULT_DB_PASSWORD = SecretName.CORE_DEFAULT_DB_PASSWORD
# Cloud SQL instances
_PAYMENT_DB_USER = SecretName.PAYMENT_DB_USER
_PAYMENT_DB_PASSWORD = SecretName.PAYMENT_DB_PASSWORD
_DRAFTS_DB_USER = SecretName.DRAFTS_DB_USER
_DRAFTS_DB_PASSWORD = SecretName.DRAFTS_DB_PASSWORD

if LIVE_DEPLOYMENT:
    match os.environ.get('COMPONENT_NAME', ''):
        case 'celery':
            _DEFAULT_DB_USER = SecretName.CELERY_DB_USER
            _DEFAULT_DB_PASSWORD = SecretName.CELERY_DB_PASSWORD
        case 'admin':
            _DEFAULT_DB_USER = SecretName.ADMIN_DB_USER
            _DEFAULT_DB_PASSWORD = SecretName.ADMIN_DB_PASSWORD
        case 'grpc-api':
            _DEFAULT_DB_USER = SecretName.GRPC_API_DB_USER
            _DEFAULT_DB_PASSWORD = SecretName.GRPC_API_DB_PASSWORD
        case 'public-api':
            _DEFAULT_DB_USER = SecretName.PUBLIC_API_DB_USER
            _DEFAULT_DB_PASSWORD = SecretName.PUBLIC_API_DB_PASSWORD
        case 'reports-api':
            _DEFAULT_DB_USER = SecretName.REPORTS_API_DB_USER
            _DEFAULT_DB_PASSWORD = SecretName.REPORTS_API_DB_PASSWORD
        case 'search-api':
            _DEFAULT_DB_USER = SecretName.SEARCH_API_DB_USER
            _DEFAULT_DB_PASSWORD = SecretName.SEARCH_API_DB_PASSWORD

# Primary database
DATABASES['default'].update(YAML_CONFIG['database'])

# Collection to round-robin between read-only replicas
READ_ONLY_DBS = []

# Read-only replica
# lib.db.READ_ONLY_DB = 'read-only'
DATABASES['read-only'] = copy.deepcopy(DATABASES['default'])
DATABASES['read-only'].update(YAML_CONFIG.get('database-read-only') or {})
DATABASES['read-only']['OPTIONS']['options'] = '-c default_transaction_read_only=on'
READ_ONLY_DBS.append('read-only')

if YAML_CONFIG.get('secondary-database-read-only'):
    # Alloy Read-only replica
    # lib.db.SECONDARY_READ_ONLY_DB = 'secondary-read-only'
    DATABASES['secondary-read-only'] = copy.deepcopy(DATABASES.get('read-only'))
    DATABASES['secondary-read-only'].update(YAML_CONFIG.get('secondary-database-read-only') or {})
    for _ in range(9):
        READ_ONLY_DBS.append('secondary-read-only')

# Logical replica
DATABASES['reports'] = copy.deepcopy(DATABASES.get('read-only') or DATABASES['default'])
DATABASES['reports'].update(YAML_CONFIG.get('reports-database', {}))
# Dedicated physical replica, initialized with ordinary replica or default db
DATABASES['reports-read-only'] = copy.deepcopy(DATABASES.get('read-only') or DATABASES['default'])
DATABASES['reports-read-only'].update(YAML_CONFIG.get('reports-database-read-only', {}))
# Adyen uses a parallel connection to primary database
# so it can write adyen related stuff even when we do a rollback on the default connection
DATABASES['adyen'] = copy.deepcopy(DATABASES['default'])
DATABASES['adyen']['CONN_MAX_AGE'] = 0  # always salvage this connection at the end of the request

DATABASES['payments-database'] = copy.deepcopy(DATABASES['default'])
DATABASES['payments-database'].update(YAML_CONFIG.get('payments-database', {}))

DATABASES['drafts-database'] = copy.deepcopy(DATABASES['default'])
DATABASES['drafts-database'].update(YAML_CONFIG.get('drafts-database', {}))

# use secrets from secret manager
DATABASES['default']['USER'] = SecretsProvider.read_secret(_DEFAULT_DB_USER)
DATABASES['default']['PASSWORD'] = SecretsProvider.read_secret(_DEFAULT_DB_PASSWORD)
DATABASES['read-only']['USER'] = SecretsProvider.read_secret(_DEFAULT_DB_USER)
DATABASES['read-only']['PASSWORD'] = SecretsProvider.read_secret(_DEFAULT_DB_PASSWORD)
if YAML_CONFIG.get('secondary-database-read-only'):
    DATABASES['secondary-read-only']['USER'] = SecretsProvider.read_secret(_DEFAULT_DB_USER)
    DATABASES['secondary-read-only']['PASSWORD'] = SecretsProvider.read_secret(_DEFAULT_DB_PASSWORD)
DATABASES['reports']['USER'] = SecretsProvider.read_secret(_DEFAULT_DB_USER)
DATABASES['reports']['PASSWORD'] = SecretsProvider.read_secret(_DEFAULT_DB_PASSWORD)
DATABASES['reports-read-only']['USER'] = SecretsProvider.read_secret(_DEFAULT_DB_USER)
DATABASES['reports-read-only']['PASSWORD'] = SecretsProvider.read_secret(_DEFAULT_DB_PASSWORD)
DATABASES['adyen']['USER'] = SecretsProvider.read_secret(_DEFAULT_DB_USER)
DATABASES['adyen']['PASSWORD'] = SecretsProvider.read_secret(_DEFAULT_DB_PASSWORD)
DATABASES['payments-database']['USER'] = SecretsProvider.read_secret(_PAYMENT_DB_USER)
DATABASES['payments-database']['PASSWORD'] = SecretsProvider.read_secret(_PAYMENT_DB_PASSWORD)
DATABASES['drafts-database']['USER'] = SecretsProvider.read_secret(_DRAFTS_DB_USER)
DATABASES['drafts-database']['PASSWORD'] = SecretsProvider.read_secret(_DRAFTS_DB_PASSWORD)

if LOCAL_DEPLOYMENT:
    for _db in DATABASES.values():
        _db['CONN_MAX_AGE'] = 0  # required by django dev server

ELASTIC_HOSTS = YAML_CONFIG['elasticsearch']
ELASTIC_TIMEOUT = int(
    os.environ.get('ELASTIC_TIMEOUT', 0)
    or YAML_CONFIG.get(
        'elasticsearch_timeout',
        # TODO increase timeout after  temporarly
        #  until number of shards is not changed
        10 if LOCAL_DEPLOYMENT else 5,
    )
)
REQUESTS_TIMEOUT = YAML_CONFIG.get(
    'requests_timeout',
    3.0,
)


# ES_CLUSTER_TYPES
ES_CLUSTER_SINGLE_NODE = 'single_node'
ES_CLUSTER_MULTI_NODE = 'multi_node'
ES_CLUSTER_MULTITENANT = 'multitenant'

ES_CLUSTER_TYPE = ES_CLUSTER_SINGLE_NODE
if LIVE_DEPLOYMENT:
    ES_CLUSTER_TYPE = ES_CLUSTER_MULTI_NODE

ES_DEFAULT_CLUSTER_SETTINGS = {
    ES_CLUSTER_SINGLE_NODE: {'number_of_shards': 1, 'number_of_replicas': 0},
    ES_CLUSTER_MULTI_NODE: {'number_of_shards': 6, 'number_of_replicas': 2},
    ES_CLUSTER_MULTITENANT: {'number_of_shards': 3, 'number_of_replicas': 2},
}


# PATHS
# on non-local deployments those aren't really used - only bucket file storages are supported
LOG_DIRECTORY = '/shared/log'
MEDIA_ROOT = '/shared/media'
STATIC_ROOT = '/shared/media/static'
if LOCAL_DEPLOYMENT:
    LOG_DIRECTORY = '/home/<USER>/logs'
    MEDIA_ROOT = '/home/<USER>/media'
if LOCAL_DEPLOYMENT and PYTEST:
    LOG_DIRECTORY = os.path.expanduser('~/.pytest/logs')
    MEDIA_ROOT = os.path.expanduser('~/.pytest/media')
BUSINESS_FILES_ROOT = os.path.join(MEDIA_ROOT, 'business-files')
REPORTS_DIR = os.path.join(MEDIA_ROOT, 'reports')
TEMP_ADMIN_FILE = os.path.join(MEDIA_ROOT, 'private/tmp_admin_import')
VAGARO_UPLOAD_FOLDER = os.path.join(MEDIA_ROOT, 'vagaro_imports/')


# URLS
API_URL = f'https://{API_COUNTRY}.{BOOKSY_DOMAIN}/'
STATIC_URL = f'/statics/{API_COUNTRY}/'
MEDIA_URL = f'https://{BOOKSY_DOMAIN}/media/{API_COUNTRY}/'
REPORTS_URL = f'{MEDIA_URL}reports/'
MARKETPLACE_URL = f'https://{BOOKSY_DOMAIN}'
BIZ_WEB_2_APP_URL = f'https://{BOOKSY_DOMAIN}/biz-app/'
WEB_USER_RESET_URL = f'https://{BOOKSY_DOMAIN}/user-reset/'
FRONTDESK_APP_URL = f'https://{BOOKSY_DOMAIN}/pro/'
WIDGET_URL = f'https://{BOOKSY_DOMAIN}/widget/'
WIDGET_2021_URL = f'https://{BOOKSY_DOMAIN}/widget-2021/'
MERGER_URL = os.environ.get('MERGER_URL')
if LOCAL_DEPLOYMENT:
    API_URL = 'http://localhost:8888/'
    STATIC_URL = f'/statics/{API_COUNTRY}/'
    MEDIA_URL = 'http://localhost:8888/media/'
    REPORTS_URL = f'{MEDIA_URL}reports/'
    MARKETPLACE_URL = 'http://localhost:8600'
    BIZ_WEB_2_APP_URL = 'http://localhost:8067/'
    WEB_USER_RESET_URL = 'http://localhost:8068/user-reset/'
    FRONTDESK_APP_URL = 'http://localhost:8080/pro/'
    WIDGET_URL = 'http://localhost:8700/widget/'
    WIDGET_2021_URL = 'http://localhost:8721/widget-2021/'
    MERGER_URL = 'pl-merger.sandbox.t1.booksy.pm:443'

# Legal documents URLS
BOOKSY_DISCLOSURE_OBLIGATION_URL_B2B = f'{FRONTDESK_APP_URL}info/{API_COUNTRY}.html'

if not LOCAL_DEPLOYMENT:
    ALLOWED_HOSTS.append(urlparse(API_URL).netloc)
    TEMPLATE_API_URL = f'https://{{country_code}}.{BOOKSY_DOMAIN}'
else:
    TEMPLATE_API_URL = API_URL

PUBLIC_API_URL = f'{API_URL}/public-api/{API_COUNTRY}/'
PUBLIC_API_OAUTH2_AUTHORIZE_URL = f'{API_URL}/public-api/{API_COUNTRY}/oauth2/authorize/'
PUBLIC_API_OAUTH2_TOKEN_URL = f'{API_URL}/public-api/{API_COUNTRY}/oauth2/token/'

ROUTE_URL = f'https://route.{BOOKSY_DOMAIN}'
if LOCAL_DEPLOYMENT:
    ROUTE_URL = API_URL

if not LOCAL_DEPLOYMENT:
    ADMIN_URL = f'https://{API_COUNTRY}.{BOOKSY_DOMAIN}/admin/{API_COUNTRY}/'
    ADMIN_VITE_URL = f'https://admin-vite.{BOOKSY_DOMAIN}'
else:
    ADMIN_URL = 'https://localhost:9092'
    ADMIN_VITE_URL = 'https://localhost:9092'

NOTIFICATIONS_WEBHOOKS_URL = f'https://notifications-webhooks.{BOOKSY_DOMAIN}/'
NOTIFICATIONS_WEBHOOKS_PROJECT_ID = f'notifications-webhooks-{"prd" if LIVE_DEPLOYMENT else "dev"}'

SUBDOMAIN_HOST = os.environ.get('SUBDOMAIN_HOST')
INAPP_HOST = os.environ.get('INAPP_HOST')

if not LOCAL_DEPLOYMENT:
    SEARCH_SERVICE_URL = YAML_CONFIG.get('search-service-host')
else:
    SEARCH_SERVICE_URL = "search_service:8080"

# Branding & templates
BRAND_TEMPLATE_DIR = os.path.join(BASE_TEMPLATE_DIR, 'booksy')
STATICPAGES_PATH = os.path.join(BRAND_TEMPLATE_DIR, 'staticcontent', 'pages')
TEMPLATES[0]['DIRS'].append(BRAND_TEMPLATE_DIR)

# PAYPAL
if 'paypal' in YAML_CONFIG:
    PAYPAL_NVP_API_URL = YAML_CONFIG['paypal']['nvp-api-url']
    PAYPAL_CHECKOUT_URL = YAML_CONFIG['paypal']['checkout-url']
# otherwise, PayPal settings_defaults are used

# EMAILS
EMAIL_HOST = YAML_CONFIG['email']['host']
EMAIL_PORT = YAML_CONFIG['email']['port']
EMAIL_HOST_USER = SecretsProvider.read_secret('SMTP_EMAIL_USER')
EMAIL_HOST_PASSWORD = SecretsProvider.read_secret('SMTP_EMAIL_PASSWORD')
EMAIL_USE_TLS = YAML_CONFIG['email']['use-tls']


EMAIL_API_KEY = SecretsProvider.read_secret('ITERABLE_API_KEY')
EMAIL_NO_REPLY_CAMPAIGN = 1151690
if LIVE_DEPLOYMENT:
    EMAIL_NO_REPLY_CAMPAIGN = 1738441

# GOOGLE TAG MANAGER
TAG_MANAGER_DEBUG_HEADER = {
    'X-Gtm-Server-Preview': ('ZW52LTN8T2JmZXU5YnE5Y2VTRW1FQUlDT253QXwxN2ZmZjIxNmQwZTU5OTFhNGYzMzc=')
}
TAG_MANAGER_URL = 'https://gtm-dev.booksy.net/'
if LIVE_DEPLOYMENT:
    TAG_MANAGER_URL = 'https://gtm.booksy.com/'
    TAG_MANAGER_DEBUG_HEADER = {}

# FIREBASE
FIREBASE_WEB_MEASUREMENT_ID = SecretsProvider.read_secret('FIREBASE_WEB_MEASUREMENT_ID')
FIREBASE_IOS_APP_ID = SecretsProvider.read_secret('FIREBASE_IOS_APP_ID')
FIREBASE_ANDROID_APP_ID = SecretsProvider.read_secret('FIREBASE_ANDROID_APP_ID')

FIREBASE_WEB_API_SECRET = SecretsProvider.read_secret('FIREBASE_WEB_API_SECRET')
FIREBASE_IOS_API_SECRET = SecretsProvider.read_secret('FIREBASE_IOS_API_SECRET')
FIREBASE_ANDROID_API_SECRET = SecretsProvider.read_secret('FIREBASE_ANDROID_API_SECRET')

# APPSFLYER
APPSFLYER_IOS_APP_ID = SecretsProvider.read_secret('APPSFLYER_IOS_APP_ID')
APPSFLYER_ANDROID_APP_ID = SecretsProvider.read_secret('APPSFLYER_ANDROID_APP_ID')

APPSFLYER_IOS_DEV_KEY = SecretsProvider.read_secret('APPSFLYER_IOS_DEV_KEY')
APPSFLYER_ANDROID_DEV_KEY = SecretsProvider.read_secret('APPSFLYER_ANDROID_DEV_KEY')
APPSFLYER_WEB_DEV_KEY = SecretsProvider.read_secret('APPSFLYER_WEB_DEV_KEY')

# DJANGO ADMIN COOKIE PATHS
CSRF_COOKIE_PATH = f'/admin/{API_COUNTRY}/'
SESSION_COOKIE_PATH = f'/admin/{API_COUNTRY}/'
LANGUAGE_COOKIE_PATH = '/'  # global

# DJANGO COOKIE SECURITY
if not LOCAL_DEPLOYMENT:
    CSRF_COOKIE_SECURE = True
    SESSION_COOKIE_SECURE = True

# FEATURES & DEBUG
DEBUG = YAML_CONFIG.get('debug', False)

BUSINESSES_MULTI_CATEGORIES = YAML_CONFIG.get(
    'businesses_multi_categories', BUSINESSES_MULTI_CATEGORIES
)

GROUP_PERMISSIONS_ENABLED = YAML_CONFIG.get('group_permissions_enabled', GROUP_PERMISSIONS_ENABLED)

CAN_CREATE_UMBRELLA = YAML_CONFIG.get('can_create_umbrella', CAN_CREATE_UMBRELLA)

FAMILY_AND_FRIENDS_ENABLED = YAML_CONFIG.get(
    'family_and_friends_enabled', FAMILY_AND_FRIENDS_ENABLED
)

POS = YAML_CONFIG.get('pos', POS)
POS__PAY_BY_APP = YAML_CONFIG.get('pos__pay_by_app', POS__PAY_BY_APP)
POS__PREPAYMENTS = YAML_CONFIG.get('pos__prepayments', POS__PREPAYMENTS)
POS__REFUNDS = YAML_CONFIG.get('pos__refunds', POS__REFUNDS)
POS__GOOGLE_PAY = YAML_CONFIG.get('pos__google_pay', POS__GOOGLE_PAY)
POS__APPLE_PAY = YAML_CONFIG.get('pos__apple_pay', POS__APPLE_PAY)
POS__SQUARE = YAML_CONFIG.get('pos__square', POS__SQUARE)
POS__STRIPE_TERMINAL = YAML_CONFIG.get(
    'pos__stripe_terminal',
    POS__STRIPE_TERMINAL,
)
POS__TAP_TO_PAY = YAML_CONFIG.get(
    'pos__tap_to_pay',
    POS__TAP_TO_PAY,
)
POS__TAP_TO_PAY_MIN_IOS_VERSION = YAML_CONFIG.get(
    'pos__tap_to_pay_min_ios_version',
    POS__TAP_TO_PAY_MIN_IOS_VERSION,
)
POS__KEYED_IN_PAYMENT = YAML_CONFIG.get(
    'pos__keyed_in_payment',
    POS__KEYED_IN_PAYMENT,
)
POS__BLIK = YAML_CONFIG.get('pos__blik', POS__BLIK)
POS__KLARNA = YAML_CONFIG.get('pos__klarna', POS__KLARNA)
POS__FAST_PAYOUTS = YAML_CONFIG.get('pos__fast_payouts', POS__FAST_PAYOUTS)
STRIPE_ALLOW_TIPS_WITHOUT_FEE = YAML_CONFIG.get(
    'stripe_allow_tips_without_fee',
    STRIPE_ALLOW_TIPS_WITHOUT_FEE,
)
STRIPE_SKIP_RADAR_IN_SUBSCRIPTIONS = YAML_CONFIG.get(
    'stripe_skip_radar_in_subscriptions',
    STRIPE_SKIP_RADAR_IN_SUBSCRIPTIONS,
)
POS__BSX = YAML_CONFIG.get('pos__bsx', POS__BSX)
POS__DEFAULT_PAYMENT_PROVIDER_V2 = YAML_CONFIG.get(
    'pos__default_payment_provider_v2',
    POS__DEFAULT_PAYMENT_PROVIDER_V2,
)
POS__BOOKSY_PAY = YAML_CONFIG.get(
    'pos__booksy_pay',
    POS__BOOKSY_PAY,
)

IAP_ANDROID = YAML_CONFIG.get('iap_android', IAP_ANDROID)
IAP_IOS = YAML_CONFIG.get('iap_ios', IAP_IOS)
IAP_BRAINTREE = YAML_CONFIG.get('iap_braintree', IAP_BRAINTREE)
WAITLIST_ENABLED = YAML_CONFIG.get('waitlist_enabled', WAITLIST_ENABLED)
PARALLEL_CLIENTS = YAML_CONFIG.get('parallel_clients', 50)

UNVERIFIED_PUSH_PAYMENTS = YAML_CONFIG.get(
    'unverified_push_payments',
    UNVERIFIED_PUSH_PAYMENTS,
)
NOSHOW_MAIL = YAML_CONFIG.get('noshow_mail', NOSHOW_MAIL)
NOSHOW_FEATURE_EDU_MAIL = YAML_CONFIG.get(
    'noshow_feature_edu_mail',
    NOSHOW_FEATURE_EDU_MAIL,
)

CURRENT_BUSINESS_IOS_VERSION = YAML_CONFIG.get(
    'current_business_ios_version',
    CURRENT_BUSINESS_IOS_VERSION,
)
CURRENT_BUSINESS_IOS_FORCE_UPDATE = YAML_CONFIG.get(
    'current_business_ios_force_update',
    CURRENT_BUSINESS_IOS_FORCE_UPDATE,
)
CURRENT_BUSINESS_ANDROID_VERSION = YAML_CONFIG.get(
    'current_business_android_version',
    CURRENT_BUSINESS_ANDROID_VERSION,
)
CURRENT_BUSINESS_ANDROID_FORCE_UPDATE = YAML_CONFIG.get(
    'current_business_android_force_update',
    CURRENT_BUSINESS_ANDROID_FORCE_UPDATE,
)
CURRENT_CUSTOMER_IOS_VERSION = YAML_CONFIG.get(
    'current_customer_ios_version',
    CURRENT_CUSTOMER_IOS_VERSION,
)
CURRENT_CUSTOMER_IOS_FORCE_UPDATE = YAML_CONFIG.get(
    'current_customer_ios_force_update',
    CURRENT_CUSTOMER_IOS_FORCE_UPDATE,
)
CURRENT_CUSTOMER_ANDROID_VERSION = YAML_CONFIG.get(
    'current_customer_android_version',
    CURRENT_CUSTOMER_ANDROID_VERSION,
)
CURRENT_CUSTOMER_ANDROID_FORCE_UPDATE = YAML_CONFIG.get(
    'current_customer_android_force_update',
    CURRENT_CUSTOMER_ANDROID_FORCE_UPDATE,
)

DEBUG_ES = YAML_CONFIG.get('debug_es', False)
DEBUG_PDF = YAML_CONFIG.get('debug_pdf', False)

BUSINESS_REGISTRATION_CLOSED = YAML_CONFIG.get(
    'business_registration_closed',
    API_COUNTRY not in Country.open_business_registration(),
)
CUSTOMER_REGISTRATION_CLOSED = YAML_CONFIG.get(
    'customer_registration_closed',
    API_COUNTRY not in Country.open_customer_registration(),
)
CORS__ALLOW_ALL_ORIGINS = YAML_CONFIG.get(
    'cors__allow_all_origins',
    CORS__ALLOW_ALL_ORIGINS,
)
if CORS__ALLOW_ALL_ORIGINS:
    # alternative logic to CORS_ALLOW_ALL_ORIGINS which returns '*' instead of strict origin
    CORS_ALLOWED_ORIGIN_REGEXES.append(r'.*')
BUSINESS_GLOBAL_PAYOUT_METHOD_CHANGE_ALLOWED = YAML_CONFIG.get(
    'business_global_payout_method_change_allowed',
    BUSINESS_GLOBAL_PAYOUT_METHOD_CHANGE_ALLOWED,
)


FORCE_UPDATE_DEFAULTS = {
    'current_business_ios_version': str(CURRENT_BUSINESS_IOS_VERSION),
    'current_business_ios_force_update': CURRENT_BUSINESS_IOS_FORCE_UPDATE,
    'current_business_android_version': str(CURRENT_BUSINESS_ANDROID_VERSION),
    'current_business_android_force_update': CURRENT_BUSINESS_ANDROID_FORCE_UPDATE,
    'current_customer_ios_version': str(CURRENT_CUSTOMER_IOS_VERSION),
    'current_customer_ios_force_update': CURRENT_CUSTOMER_IOS_FORCE_UPDATE,
    'current_customer_android_version': str(CURRENT_CUSTOMER_ANDROID_VERSION),
    'current_customer_android_force_update': CURRENT_CUSTOMER_ANDROID_FORCE_UPDATE,
}


def _get_country_yaml_admin_file(country_code):
    if LOCAL_DEPLOYMENT and country_code == API_COUNTRY:
        return YAML_ADMIN_FILE
    return f'{YAML_ADMIN_FILE[:-7]}{country_code}.yaml'


COUNTRIES_WITH_OPEN_REGISTRATION = [
    country
    for country in Country.values()
    if not (
        YAML_ADMIN_CONFIG.get(
            'business_registration_closed',
            country not in Country.open_business_registration(),
        )
    )
]

# AWS CREDENTIALS FOR BSX FISCALIZATION SETUP FILE
BSX_ACCESS_KEY_ID = SecretsProvider.read_secret('BSX_ACCESS_KEY_ID')
BSX_SECRET_ACCESS_KEY = SecretsProvider.read_secret('BSX_SECRET_ACCESS_KEY')

# AWS CREDENTIALS
COMMON_AWS_ACCESS_KEY = SecretsProvider.read_secret('COMMON_AWS_ACCESS_KEY')
COMMON_AWS_SECRET = SecretsProvider.read_secret('COMMON_AWS_SECRET')
ADMIN_IMPORT_S3_BUCKET = SecretsProvider.read_secret('ADMIN_IMPORT_S3_BUCKET')
ADMIN_IMPORT_S3_ACCESS_KEY = SecretsProvider.read_secret('ADMIN_IMPORT_S3_ACCESS_KEY')
ADMIN_IMPORT_S3_SECRET = SecretsProvider.read_secret('ADMIN_IMPORT_S3_SECRET')

# AUTH TOKENS
AUTH_TOKEN_AGE = 120

# HELPSHIFT APP_ID (ios / android) / PLATFORM_ID / API_KEY / DOMAIN
## Booksy Int. as default
HELPSHIFT_APP_SETTINGS = {
    'ios': SecretsProvider.read_secret('HELPSHIFT_IOS'),
    'android': SecretsProvider.read_secret('HELPSHIFT_ANDROID'),
    'web': SecretsProvider.read_secret('HELPSHIFT_WEB'),
    'api_key': SecretsProvider.read_secret('HELPSHIFT_API_KEY'),
    'domain': 'booksy2020.helpshift.com',
}

if not DEBUG:
    if 'APP_DIRS' in TEMPLATES[0]:
        del TEMPLATES[0]['APP_DIRS']
    TEMPLATES[0]['OPTIONS']['loaders'] = [
        (
            'django.template.loaders.cached.Loader',
            [
                'django.template.loaders.filesystem.Loader',
                'django.template.loaders.app_directories.Loader',
            ],
        ),
    ]

################################
########### MAGIC IF ###########
################################

FACEBOOK_ACCESS_TOKEN = '523698674383043|c7-yk1HR-F6D9ai-OOBXHkGSdJU'
# IN-APP PURCHASES
google_play_api_key_str = SecretsProvider.read_secret('GOOGLE_PLAY_API_KEY_JSON') or '{}'
google_play_api_key_str = google_play_api_key_str.replace("'", '"')
google_play_api_key_dict = json.loads(google_play_api_key_str)

GOOGLE_PLAY = {
    'api-key': google_play_api_key_dict,
    'api-scope': 'https://www.googleapis.com/auth/androidpublisher',
    'api-url': (
        'https://www.googleapis.com/androidpublisher/v3/'
        'applications/{packageName}/purchases/'
        'subscriptions/{productId}/tokens/{purchaseToken}'
    ),
    'public-key': os.path.join(PROJECT_PATH, 'data/google-play/biz.pub'),
    'package-name': 'net.booksy.business',
}
APPLE_RECEIPT_VALIDATION_PASSWORD = SecretsProvider.read_secret('APPLE_RECEIPT_VALIDATION_PASSWORD')
APPLE_RECEIPT_URL = {
    'production': 'https://buy.itunes.apple.com/verifyReceipt',
    'sandbox': 'https://sandbox.itunes.apple.com/verifyReceipt',
}

# <editor-fold desc="Sing In with Apple(SIWA) for Customer app">
APPLE_AUD = 'https://appleid.apple.com'
APPLE_TEAM_ID = 'UG63V48R45'
APPLE_SIWA_CREDENTIALS_SM = {
    AppleService.CUST: {
        'key_id': 'Y8V33P7QH3',
        'jwk_private_key': load_jwk_private_key(
            SecretsProvider.read_secret('SIWA_BOOKSY_CUSTOMER').encode('utf-8')
        ),
        'team_id': APPLE_TEAM_ID,
    },
    AppleService.BIZ: {
        'key_id': '99765TFS4J',
        'jwk_private_key': load_jwk_private_key(
            SecretsProvider.read_secret('SIWA_BOOKSY_BUSINESS').encode('utf-8')
        ),
        'team_id': APPLE_TEAM_ID,
    },
    AppleService.BIZ_SALON: {
        'key_id': 'Z3222K946M',
        'jwk_private_key': load_jwk_private_key(
            SecretsProvider.read_secret('SIWA_BOOKSY_SALON_BUSINESS').encode('utf-8')
        ),
        'team_id': APPLE_TEAM_ID,
    },
    AppleService.BIZ_SALON_WEB: {
        'key_id': 'Z3222K946M',
        'jwk_private_key': load_jwk_private_key(
            SecretsProvider.read_secret('SIWA_BOOKSY_SALON_BUSINESS').encode('utf-8')
        ),
        'team_id': APPLE_TEAM_ID,
    },
}

if LIVE_DEPLOYMENT:
    APPLE_MAPS_KEY_ID = 'XN9YA3Y3W5'
else:
    APPLE_MAPS_KEY_ID = '26ZZ3SM239'

APPLE_MAPS_AUTH_KEY = SecretsProvider.read_secret(f'APPLE_MAPS_AUTH_KEY_{APPLE_MAPS_KEY_ID}')

GOOGLE_API_KEY = SecretsProvider.read_secret('GOOGLE_API_KEY')

# </editor-fold>
##########################################################################
# ##################### ACHTUNG: NUCLEAR OPTION ######################## #
##########################################################################
# use with care only when Apple bans us for opening too many connections #
##########################################################################
APPLE_DISABLED = False
##########################################################################


#################################
# DEVELOPMENT SPECIFIC SETTINGS #
#################################

if not LOCAL_DEPLOYMENT:
    # use IP restricted, but more capable GOOGLE_PLACES_API_KEY_GEOCODER
    GOOGLE_PLACES_API_KEY_DEBUG = SecretsProvider.read_secret(
        'GOOGLE_PLACES_API_KEY_GEOCODER_DEBUG'
    )
    GOOGLE_PLACES_API_KEY = SecretsProvider.read_secret('GOOGLE_PLACES_API_KEY_GEOCODER')

if not LIVE_DEPLOYMENT:
    # make it easy for testers to register a lot of accounts on test servers
    SMS_REGISTRATION_LIMIT = 100
    SMS_REGISTRATION_ABUSE_LIMIT = 100

if LOCAL_DEPLOYMENT:
    ALLOWED_HOSTS = ['*']
    DEVELOPER_EMAIL = YAML_CONFIG.get('developer_email')
    EMAIL_BACKEND = 'lib.email.backend.LogEmailWithAttachmentBackend'
    if DEVELOPER_EMAIL:
        EMAIL_BACKEND = 'lib.email.backend.BooksyDeveloperEmailBackend'
    EMAIL_FILE_PATH = '/home/<USER>/smtp_sink_emails'
    DEBUG_SWAGGER = True
    DEBUG_LOGS = True
    FRAUD_ALERT_EMAIL = None

if LOCAL_DEPLOYMENT:
    CORS_ALLOWED_ORIGINS.append(f'http://api_{API_COUNTRY}:8888')
else:
    CORS_ALLOWED_ORIGINS.append(API_URL.rstrip('/'))
    CORS_ALLOWED_ORIGINS.append(f'https://{BOOKSY_DOMAIN}')
    _domain_regex_part = BOOKSY_DOMAIN.replace('.', r'\.')
    CORS_ALLOWED_ORIGIN_REGEXES.append(fr'^https:\/\/(?:[\w\-\_]+\.)+{_domain_regex_part}$')

if PYTEST:
    EMAIL_BACKEND = 'django.core.mail.backends.locmem.EmailBackend'
    BOOKSY_CELERY_EMAIL_BACKEND = 'django.core.mail.backends.locmem.EmailBackend'

INTEGRATION_TESTS = not LIVE_DEPLOYMENT
PERFORMANCE_TESTS = set_performance_test_flag(LEGACY_DEPLOYMENT_LEVEL)

SEND_NOTIFICATIONS = not (CI_TEST or PERFORMANCE_TESTS) or PYTEST  # sms, email, push notifications

if LOCAL_DEPLOYMENT or PYTEST or not SEND_NOTIFICATIONS:
    SMS_SETTINGS_DEFAULT = {
        'sms_services': {
            NotificationService.TELNYX: {
                'service': NotificationService.TELNYX,
            },
            NotificationService.EVOX: {
                'service': NotificationService.EVOX,
            },
            NotificationService.DEVEL: {
                'service': NotificationService.DEVEL,
            },
        },
        'fallback_sms_service': NotificationService.DEVEL,
        'sms_prefix': 'Booksy: ',
        'marketing_night_adjust': {
            'night_start': '21:30',
            'night_end': '08:30',
        },
        'marketing_days_exclude': False,
        'marketing_public_holidays_adjust': False,
    }
    SMS_SETTINGS_PER_COUNTRY = {}
    ALLOW_SENDING_VIA_API = False

if not SEND_NOTIFICATIONS:
    PUSH_NOTIFICATION_TEST = True


###############################
########### PUSHES ############
###############################

# ANDROID pushes
ANDROID_PUSHES_PROJECT_ID = '785785666615'

# APNS CERTS
APNS_SANDBOX = YAML_CONFIG.get('apns_sandbox_enabled', False)


ANDROID__HASH__ID = 'RWFY3DZS34F'

###############################
##### POST YAML - OTHER #######
###############################

# Language, locale, currency and regions
COUNTRY_CONFIG = CountryConfig(API_COUNTRY)
LANGUAGE_CODE = COUNTRY_CONFIG.language_code
CURRENCY_LOCALE = COUNTRY_CONFIG.currency_locale
CURRENCY_CODE = COUNTRY_CONFIG.currency_code

POS__PLANS = POS__PLANS_PER_COUNTRY.get(API_COUNTRY, [])
# We need to maintain legacy POS__PLANS for 0154_create_posplans migration
POS__PLANS__LEGACY = POS__PLANS_PER_COUNTRY_LEGACY.get(API_COUNTRY, [])
SHOW_TRIAL_INFO = API_COUNTRY in COUNTRY__SHOW_TRIAL_INFO

language, _ = LANGUAGE_CODE.split('-')
if language in (lang for lang, _ in LANGUAGES):
    MARKETPLACE_LANG_COUNTRY = f"{language}-{API_COUNTRY}"
else:
    # fallback to English
    MARKETPLACE_LANG_COUNTRY = f"en-{API_COUNTRY}"

MAX_MESSAGE_BLAST_PARTS = SMS_SETTINGS_PER_COUNTRY.get(API_COUNTRY, {}).get(
    'max_message_blast_parts_country_sms', MAX_MESSAGE_BLAST_PARTS_COUNTRY_SMS
)

# All sms limits can be set both for "trial"/"non trial" merchants
# so we also divide settings for T ("trial", SMSPaidEnum.TRIAL)
# and P ("non-trial", SMSPaidEnum.NON_TRIAL)
# first n free sms counts per merchant per phone number

# ACHTUNG: In terms of first_n_sms_limits and total_m_sms_limits None always
# means "unlimited", so you should always take 0 as default!
# If you put None for both total_m_sms_limits and first_n_sms_limits then
# no limits will be taken into account (also the ones set in subscription!)

FIRST_N_SMS_LIMITS = SMS_SETTINGS_PER_COUNTRY.get(API_COUNTRY, {}).get(
    'first_n_sms_limits', DEFAULT_FIRST_N_SMS
)

NOTIFICATION_SMS_DEDUCTION = SMS_SETTINGS_PER_COUNTRY.get(API_COUNTRY, {}).get(
    'notification_sms_deduction'
)

TOTAL_M_SMS_LIMITS = SMS_SETTINGS_PER_COUNTRY.get(API_COUNTRY, {}).get(
    'total_m_sms_limits', DEFAULT_TOTAL_M_SMS
)

SMS_LIMITS_IN_SUBSCRIPTION = API_COUNTRY in COUNTRY__SMS_LIMITS_IN_SUBSCRIPTION

#####################################
#  SMS limits for test environments #
#####################################

if not LIVE_DEPLOYMENT:
    TEST_CONF_UNLIMITED = {
        SMSTypeEnum.INVITATION: None,
        SMSTypeEnum.MARKETING: None,
        SMSTypeEnum.SYSTEM: None,
    }
    if API_COUNTRY == Country.PL:
        FIRST_N_SMS_LIMITS = {
            SMSPaidEnum.TRIAL: {
                SMSTypeEnum.INVITATION: 1,
                SMSTypeEnum.MARKETING: 0,
                SMSTypeEnum.SYSTEM: 0,
            },
            SMSPaidEnum.NON_TRIAL: {
                SMSTypeEnum.INVITATION: 1,
                SMSTypeEnum.MARKETING: 0,
                SMSTypeEnum.SYSTEM: 0,
            },
        }
        TOTAL_M_SMS_LIMITS = {
            SMSPaidEnum.TRIAL: {
                SMSTypeEnum.INVITATION: 15,
                SMSTypeEnum.MARKETING: None,
                SMSTypeEnum.SYSTEM: None,
            },
            SMSPaidEnum.NON_TRIAL: TEST_CONF_UNLIMITED,
        }
    elif API_COUNTRY == Country.BR:
        FIRST_N_SMS_LIMITS = {
            SMSPaidEnum.TRIAL: {
                SMSTypeEnum.INVITATION: 1,
                SMSTypeEnum.MARKETING: 0,
                SMSTypeEnum.SYSTEM: 0,
            },
            SMSPaidEnum.NON_TRIAL: {
                SMSTypeEnum.INVITATION: 1,
                SMSTypeEnum.MARKETING: 0,
                SMSTypeEnum.SYSTEM: None,
            },
        }
        TOTAL_M_SMS_LIMITS = {
            SMSPaidEnum.TRIAL: {
                SMSTypeEnum.INVITATION: 3,
                SMSTypeEnum.MARKETING: None,
                SMSTypeEnum.SYSTEM: None,
            },
            SMSPaidEnum.NON_TRIAL: TEST_CONF_UNLIMITED,
        }
    elif API_COUNTRY == Country.US:
        FIRST_N_SMS_LIMITS = {
            SMSPaidEnum.TRIAL: {
                SMSTypeEnum.INVITATION: None,
                SMSTypeEnum.MARKETING: 0,
                SMSTypeEnum.SYSTEM: 0,
            },
            SMSPaidEnum.NON_TRIAL: {
                SMSTypeEnum.INVITATION: None,
                SMSTypeEnum.MARKETING: 0,
                SMSTypeEnum.SYSTEM: 0,
            },
        }
        TOTAL_M_SMS_LIMITS = {
            SMSPaidEnum.TRIAL: TEST_CONF_UNLIMITED,
            SMSPaidEnum.NON_TRIAL: TEST_CONF_UNLIMITED,
        }
    elif API_COUNTRY == Country.GB:
        FIRST_N_SMS_LIMITS = {
            SMSPaidEnum.TRIAL: {
                SMSTypeEnum.INVITATION: 6,
                SMSTypeEnum.MARKETING: 0,
                SMSTypeEnum.SYSTEM: 0,
            },
            SMSPaidEnum.NON_TRIAL: {
                SMSTypeEnum.INVITATION: 6,
                SMSTypeEnum.MARKETING: 0,
                SMSTypeEnum.SYSTEM: 0,
            },
        }
        TOTAL_M_SMS_LIMITS = {
            SMSPaidEnum.TRIAL: {
                SMSTypeEnum.INVITATION: 15,
                SMSTypeEnum.MARKETING: None,
                SMSTypeEnum.SYSTEM: None,
            },
            SMSPaidEnum.NON_TRIAL: TEST_CONF_UNLIMITED,
        }
    elif API_COUNTRY == Country.ZA:
        FIRST_N_SMS_LIMITS = {
            SMSPaidEnum.TRIAL: {
                SMSTypeEnum.INVITATION: None,
                SMSTypeEnum.MARKETING: 0,
                SMSTypeEnum.SYSTEM: 0,
            },
            SMSPaidEnum.NON_TRIAL: {
                SMSTypeEnum.INVITATION: None,
                SMSTypeEnum.MARKETING: 0,
                SMSTypeEnum.SYSTEM: 0,
            },
        }
        TOTAL_M_SMS_LIMITS = {
            SMSPaidEnum.TRIAL: {
                SMSTypeEnum.INVITATION: 3,
                SMSTypeEnum.MARKETING: None,
                SMSTypeEnum.SYSTEM: None,
            },
            SMSPaidEnum.NON_TRIAL: TEST_CONF_UNLIMITED,
        }
    elif API_COUNTRY == Country.ES:
        FIRST_N_SMS_LIMITS = {
            SMSPaidEnum.TRIAL: {
                SMSTypeEnum.INVITATION: 0,
                SMSTypeEnum.MARKETING: 0,
                SMSTypeEnum.SYSTEM: 0,
            },
            SMSPaidEnum.NON_TRIAL: {
                SMSTypeEnum.INVITATION: 0,
                SMSTypeEnum.MARKETING: 0,
                SMSTypeEnum.SYSTEM: None,
            },
        }
        TOTAL_M_SMS_LIMITS = {
            SMSPaidEnum.TRIAL: TEST_CONF_UNLIMITED,
            SMSPaidEnum.NON_TRIAL: TEST_CONF_UNLIMITED,
        }

######################## end of test env settings

# TODO: If M limit has to be less important than X+Y then
# change those to filter 0 out too (?)

# Check if any total M sms limit is turned on for never paid businesses
TOTAL_M_SMS_LIMITS_FOR_TRIAL_ON = any(
    TOTAL_M_SMS_LIMITS.get(SMSPaidEnum.TRIAL, {}).get(sms_type) is not None
    for sms_type in SMSTypeEnum
)

# Check if any total M sms limit is turned on for "paid at least once"
# businesses
TOTAL_M_SMS_LIMITS_FOR_NON_TRIAL_ON = any(
    TOTAL_M_SMS_LIMITS.get(SMSPaidEnum.NON_TRIAL, {}).get(sms_type) is not None
    for sms_type in SMSTypeEnum
)

GEOCODER = {
    'google': {
        'language': LANGUAGE_CODE[:2],
        'region': API_COUNTRY if API_COUNTRY != Country.GB else 'uk',
        'components': {
            'us': 'country:us|country:pr|country:vi|country:gu|country:mp',
            'gb': 'country:gb|country:im|country:bm|country:gg|country:je',
        }.get(API_COUNTRY, f'country:{API_COUNTRY}'),
        'dismiss_zipcodes': [],
    },
    'here': {
        'dismiss_zipcodes': ['cl'],
    },
}

REGION_STRUCTURE = REGION_STRUCTURE_PER_COUNTRY.get(
    API_COUNTRY, REGION_STRUCTURE_PER_COUNTRY['default']
)

IMPERIAL_UNIT_COUNTRIES = [
    Country.US,
    Country.CA,
    Country.GB,
    Country.AU,
    Country.ZA,
]
COUNTRY_DISTANCE_UNIT = 'mi' if API_COUNTRY in IMPERIAL_UNIT_COUNTRIES else 'km'
COUNTRY_MASS_UNIT = 'lb' if API_COUNTRY in IMPERIAL_UNIT_COUNTRIES else 'kg'
BIG_COUNTRIES = [
    Country.US,
    Country.BR,
    Country.RU,
]
BIG_COUNTRY = API_COUNTRY in BIG_COUNTRIES

for profile, profile_secrets in VONAGE_COUNTRY_SECRETS.items():
    VONAGE_COUNTRY_SECRETS[profile][  # pylint: disable=unnecessary-dict-index-lookup
        'messaging_profile_name'
    ] = profile_secrets['messaging_profile_name'].replace('Country', API_COUNTRY.upper())

HERE_MAPS_ACCESS_KEY_ID = SecretsProvider.read_secret('HERE_ACCESS_KEY_ID')
HERE_MAPS_ACCESS_KEY_SECRET = SecretsProvider.read_secret('HERE_ACCESS_KEY_SECRET')

if LIVE_DEPLOYMENT:
    GOOGLE_PLACES_API_KEY_V2 = SecretsProvider.read_secret('GOOGLE_PLACES_API_KEY_PROD')
else:
    GOOGLE_PLACES_API_KEY_V2 = SecretsProvider.read_secret('GOOGLE_PLACES_API_KEY_DEV')


###############################
#### API_COUNTRY SPECIFIC #####
###############################


if LIVE_DEPLOYMENT and API_COUNTRY == Country.CA:
    # according to
    # https://redmine.booksy.pm/issues/81118
    # we change limit for US&CA for Booksy 3.0 launch
    SMS_PAID_ACCOUNT_PREPAID_COUNT = 1000
    SMS_PAID_ACCOUNT_PREPAID_HISTORY = {
        '2015-01': 50,
        '2015-11': 500,
        '2021-07': SMS_PAID_ACCOUNT_PREPAID_COUNT,
    }
if LIVE_DEPLOYMENT and API_COUNTRY == Country.US:
    SMS_PAID_ACCOUNT_PREPAID_COUNT = 100
    SMS_PAID_ACCOUNT_PREPAID_HISTORY = {
        '2015-01': 50,
        '2015-11': 500,
        '2021-07': 1000,
        '2022-11': SMS_PAID_ACCOUNT_PREPAID_COUNT,
    }
if LIVE_DEPLOYMENT and API_COUNTRY == Country.PL:
    # from 2017-02 we change free SMS count for paid accounts to 50 SMS
    SMS_DEMO_ACCOUNT_PREPAID_COUNT = 50
    SMS_PAID_ACCOUNT_PREPAID_COUNT = 50
    SMS_PAID_ACCOUNT_PREPAID_HISTORY = {
        '2015-01': 50,
        '2015-11': 500,
        '2017-02': SMS_PAID_ACCOUNT_PREPAID_COUNT,
    }
if LIVE_DEPLOYMENT and API_COUNTRY == Country.BR:
    SMS_PAID_ACCOUNT_PREPAID_COUNT = 0
    SMS_PAID_ACCOUNT_PREPAID_HISTORY = {
        '2015-01': 50,
        '2015-11': 500,
        '2019-04': 50,
        '2019-05': SMS_PAID_ACCOUNT_PREPAID_COUNT,
    }

if LIVE_DEPLOYMENT and (API_COUNTRY == Country.ES or API_COUNTRY in Country.non_premium()):
    SMS_PAID_ACCOUNT_PREPAID_COUNT = 0
    SMS_PAID_ACCOUNT_PREPAID_HISTORY = {
        '2015-01': 50,
        '2015-11': 500,
        '2019-05': SMS_PAID_ACCOUNT_PREPAID_COUNT,
    }

if LIVE_DEPLOYMENT and API_COUNTRY == Country.ES:
    SMS_DEMO_ACCOUNT_PREPAID_COUNT = 200

if LIVE_DEPLOYMENT and API_COUNTRY in (Country.GB, Country.IE):
    SMS_DEMO_ACCOUNT_PREPAID_COUNT = 50
    SMS_PAID_ACCOUNT_PREPAID_COUNT = 50
    SMS_PAID_ACCOUNT_PREPAID_HISTORY = {
        '2015-01': 50,
        '2015-11': 500,
        '2019-06': SMS_PAID_ACCOUNT_PREPAID_COUNT,
    }

if not LIVE_DEPLOYMENT:
    SMS_DEMO_ACCOUNT_PREPAID_COUNT = 5
    SMS_PAID_ACCOUNT_PREPAID_COUNT = 10
    SMS_PAID_ACCOUNT_PREPAID_HISTORY = {
        '2015-01': 5,
        '2015-11': SMS_PAID_ACCOUNT_PREPAID_COUNT,
    }


if not LIVE_DEPLOYMENT and API_COUNTRY == Country.ZA:
    SMS_DEMO_ACCOUNT_PREPAID_COUNT = 5
    SMS_PAID_ACCOUNT_PREPAID_COUNT = 0
    SMS_PAID_ACCOUNT_PREPAID_HISTORY = {
        '2015-01': 5,
        '2015-11': 10,
        '2019-04': SMS_PAID_ACCOUNT_PREPAID_COUNT,
    }


if not LIVE_DEPLOYMENT:
    SMS_BUSINESS_MONTHLY_LIMITS_CHOICES = sorted(set(SMS_BUSINESS_MONTHLY_LIMITS_CHOICES + [10]))

if API_COUNTRY in (Country.GB, Country.IE):
    POS__REGISTERS_COUNTABLE_PAYMENT_TYPES = None  # all

STATUS_FLOW__TRIAL_DURATION, STATUS_FLOW__TRIAL_EXTENSION = {
    Country.AR: (14, STATUS_FLOW__TRIAL_EXTENSION),
    Country.FR: (7, STATUS_FLOW__TRIAL_EXTENSION),
    Country.AU: (14, STATUS_FLOW__TRIAL_EXTENSION),
    Country.US: (14, 7),
    Country.CA: (14, 7),
    Country.CL: (14, STATUS_FLOW__TRIAL_EXTENSION),
    Country.CO: (14, STATUS_FLOW__TRIAL_EXTENSION),
    Country.BR: (7, 7),
    Country.GB: (7, 5),
    Country.IE: (7, 5),
    Country.ES: (7, 5),
    Country.PL: (7, 7),
    Country.PT: (7, STATUS_FLOW__TRIAL_EXTENSION),
    Country.MX: (7, 7),
    Country.ZA: (14, 3),
}.get(API_COUNTRY, (STATUS_FLOW__TRIAL_DURATION, STATUS_FLOW__TRIAL_EXTENSION))

ALLOW_TRIAL_EXTENSION_COUNTRIES = [Country.US]
ALLOW_TRIAL_EXTENSION = API_COUNTRY in ALLOW_TRIAL_EXTENSION_COUNTRIES

if not LIVE_DEPLOYMENT:
    DISCOUNT_AFTER_POB_DAYS_PAST_DUE = 1
    # Number of days/hours etc remaining to next billing cycle when we cancel
    # past due subscriptions
    CHURN__CANCEL_SUBSCRIPTIONS_BEFORE = datetime.timedelta(days=29)

if API_COUNTRY in (
    # Brazil can't into SMS
    Country.BR,
    # we do not send there - it's too expensive
    Country.IN,
    Country.SG,
    Country.RU,
    Country.DE,
    Country.SE,
    Country.MY,
    Country.IT,
):
    SMS_REGISTRATION_CODE_REQUIRED = False


################################
####### DATE/TIME FORMAT #######
################################

LOCALIZED_TIME_FORMAT = {
    'us': '%I:%M %p',
}.get(API_COUNTRY, '%H:%M')

LOCALIZED_DATE_FORMAT = {
    'us': '%Y/%d/%m',
}.get(API_COUNTRY, '%Y/%m/%d')

################################
###### BRAINTREE SETTINGS ######
################################

BRAINTREE_MERCHANT_ID = SecretsProvider.read_secret(
    'BRAINTREE_MERCHANT_ID',
    default_value='BRAINTREE_MERCHANT_ID',
)
BRAINTREE_PRIVATE_KEY = SecretsProvider.read_secret(
    'BRAINTREE_PRIVATE_KEY',
    default_value='BRAINTREE_PRIVATE_KEY',
)
BRAINTREE_PUBLIC_KEY = SecretsProvider.read_secret(
    'BRAINTREE_PUBLIC_KEY',
    default_value='BRAINTREE_PUBLIC_KEY',
)

if LIVE_DEPLOYMENT:
    braintree.Configuration.configure(
        braintree.Environment.Production,
        merchant_id=BRAINTREE_MERCHANT_ID,
        public_key=BRAINTREE_PUBLIC_KEY,
        private_key=BRAINTREE_PRIVATE_KEY,
    )
    BRAINTREE_MERCHANT_ACCOUNTS = {  # by currency-code (for all countries)
        'GBP': 'BooksyInc_GBP',
        'USD': 'BooksyInc_instant',
        'PLN': 'booksyplPLN',
        'ZAR': 'BooksyInc_ZAR',
        'MXN': 'booksyinc_MXN',
    }
    _BRAINTREE_ADDITIONAL_MERCHANT_ACCOUNTS = {  # by country and currency-code
        Country.ES: {
            'EUR': 'booksyES_EUR',
        },
        Country.IE: {
            'EUR': 'booksyPL_EUR',
        },
    }
    BRAINTREE_MERCHANT_ACCOUNTS_BY_COUNTRY = {  # by country-code, for 3ds ver.
        Country.PL: 'booksyplPLN',
        Country.IT: 'booksyplPLN',
        Country.GB: 'BooksyInc_GBP',
    }

else:
    braintree.Configuration.configure(
        braintree.Environment.Sandbox,
        merchant_id=BRAINTREE_MERCHANT_ID,
        public_key=BRAINTREE_PUBLIC_KEY,
        private_key=BRAINTREE_PRIVATE_KEY,
    )
    BRAINTREE_MERCHANT_ACCOUNTS = {  # by currency-code
        'USD': 'booksy',
        'EUR': 'EURO',
        'GBP': 'GBP',
        'PLN': 'PL',
        'MXN': 'MXN',
    }
    _BRAINTREE_ADDITIONAL_MERCHANT_ACCOUNTS = {}  # by country and currency-code
    BRAINTREE_MERCHANT_ACCOUNTS_BY_COUNTRY = {  # by country-code, for 3ds ver.
        Country.PL: 'PL',
        Country.IT: 'PL',
        Country.GB: 'GBP',
    }

if API_COUNTRY in _BRAINTREE_ADDITIONAL_MERCHANT_ACCOUNTS:
    BRAINTREE_MERCHANT_ACCOUNTS.update(_BRAINTREE_ADDITIONAL_MERCHANT_ACCOUNTS[API_COUNTRY])

BRAINTREE_SKIP_COUNTRIES = ['pl', 'br']

BRAINTREE_BOOKSY_PAYMENTS_HASH_KEY = 'aSortOfaSecretKey_'
BRAINTREE_BOOKSY_OVERDUE_PAYMENTS_HASH_KEY = 'jakis#taki_kluczzz;;'

BRAINTREE_DEFAULT_ACCOUNT = BRAINTREE_MERCHANT_ACCOUNTS.get('USD')

STATUS_FLOW__TRIAL_COUNT_FROM_DATE_CREATED = (
    API_COUNTRY in COUNTRY__STATUS_FLOW__TRIAL_COUNT_FROM_DATE_CREATED
)

###############################
# STRIPE ENV MANAGER
STRIPE_TEST_ACCOUNT_EMAIL = SecretsProvider.read_secret('STRIPE_TEST_ACCOUNT_EMAIL')
STRIPE_TEST_ACCOUNT_PASSWORD = SecretsProvider.read_secret('STRIPE_TEST_ACCOUNT_PASSWORD')
STRIPE_TEST_ACCOUNT_AUTHENTICATOR_SECRET = SecretsProvider.read_secret(
    'STRIPE_TEST_ACCOUNT_AUTHENTICATOR_SECRET'
)

###############################
############ ADYEN ############
# Authorisation credentials   #
# are stored in adyen.py      #
###############################
if LIVE_DEPLOYMENT:
    if POS__PAY_BY_APP:
        ADYEN_ACCOUNT = API_COUNTRY
        ADYEN_ENABLED = True
        POS__DEFAULT_PAYMENT_PROVIDER = 'adyen_ee'
    else:
        ADYEN_ACCOUNT = None
        ADYEN_ENABLED = False
        POS__DEFAULT_PAYMENT_PROVIDER = None
else:
    ADYEN_ACCOUNT = 'sandbox'
    ADYEN_ENABLED = True
    POS__DEFAULT_PAYMENT_PROVIDER = 'adyen_ee'

ADYEN_3DS_ENABLED = YAML_CONFIG.get('adyen_3dsecure_enabled', True)


MARKET_PAY_ENABLED = YAML_CONFIG.get('marketpay_enabled', MARKET_PAY_ENABLED)
MARKET_PAY_AVS_ENABLED = YAML_CONFIG.get('marketpay_avs_enabled', None)
# this flag is to separate some b2b referral logic from market_pay
MARKET_PAY_B2B_REFERRAL_ENABLED = (
    YAML_CONFIG.get(
        'marketpay_b2b_referral_enabled',
        MARKET_PAY_B2B_REFERRAL_ENABLED,
    )
    and MARKET_PAY_ENABLED
)

if MARKET_PAY_AVS_ENABLED is None and API_COUNTRY in (Country.US, Country.GB):
    MARKET_PAY_AVS_ENABLED = True
elif MARKET_PAY_AVS_ENABLED is None:
    MARKET_PAY_AVS_ENABLED = False

VOUCHERS_ENABLED = YAML_CONFIG.get('vouchers_enabled', VOUCHERS_ENABLED)
B2B_REFERRAL_ENABLED = YAML_CONFIG.get('b2b_referral_enabled', B2B_REFERRAL_ENABLED)

CONSENT_FORM_SMS_REQUEST = YAML_CONFIG.get('consent_form_sms_request', CONSENT_FORM_SMS_REQUEST)

ADYEN_REQUEST_TIMEOUT = 5

ADYEN_REPORT_USER = '<EMAIL>'
ADYEN_REPORT_PASS = 'Csje6jhyzern'

MARKET_PAY_REPORT_USER = '<EMAIL>'
MARKET_PAY_REPORT_PASS = 'Csje6jhyzern'

# copied from git:route:config.py:ADYEN_NOTIFICATION_AUTH
ADYEN_NOTIFICATION_AUTH = (
    '1e62388bc2e44219975f1753efec03bf',  # username
    '9b86bc730f36471bbd196872beacace4',  # password
)

# Segment API key

SEGMENT_ANALYTICS_KEY = SecretsProvider.read_secret('SEGMENT_ANALYTICS_KEY')
SEGMENT_ANALYTICS_KEY_JS = SecretsProvider.read_secret('SEGMENT_ANALYTICS_KEY_JS')

GOOGLE_RESERVE_LIVE_UPDATES = False

GOOGLE_RESERVE_COUNTRIES_LIST = []
GOOGLE_RESERVE_NOTIFICATION_EMAIL = []
if LIVE_DEPLOYMENT:
    GOOGLE_RESERVE_COUNTRIES_LIST = [
        'ar',
        'au',
        'br',
        'ca',
        'co',
        'es',
        'gb',
        'ie',
        'mx',
        'us',
        'pl',
    ]

# GROUPON SETTINGS
GROUPON_RESERVE_LIVE_UPDATES = False
if LIVE_DEPLOYMENT and API_COUNTRY == Country.US:
    GROUPON_RESERVE_LIVE_UPDATES = True

# GOOGLE SETTINGS
if LIVE_DEPLOYMENT and API_COUNTRY in GOOGLE_RESERVE_COUNTRIES_LIST:
    GOOGLE_RESERVE_LIVE_UPDATES = True
if LIVE_DEPLOYMENT and API_COUNTRY == Country.US:
    GOOGLE_RESERVE_NOTIFICATION_EMAIL = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
    ]

GOOGLE_PARTNER_ID = '10000009'

if LIVE_DEPLOYMENT:
    GOOGLE_CONVERSION_TRACKING_URL = 'https://www.google.com/maps/conversion/collect'
else:
    GOOGLE_CONVERSION_TRACKING_URL = 'https://www.google.com/maps/conversion/debug/collect'

# FACEBOOK SETTINGS
FACEBOOK_SERVICE_LIVE_UPDATES = True

# ANALYTICS
BOOKSY_ANALYTICS_BUCKET_PROJECT_ID = 'booksy-analytics'
BOOKSY_ANALYTICS_BUCKET_KEY_ID = SecretsProvider.read_secret('BOOKSY_ANALYTICS_BUCKET_KEY_ID')
BOOKSY_ANALYTICS_BUCKET_KEY = SecretsProvider.read_secret('BOOKSY_ANALYTICS_BUCKET_KEY')
BOOKSY_ANALYTICS_BUCKET_CLIENT_EMAIL = SecretsProvider.read_secret(
    'BOOKSY_ANALYTICS_BUCKET_CLIENT_EMAIL'
)
BOOKSY_ANALYTICS_BUCKET_CLIENT_ID = SecretsProvider.read_secret('BOOKSY_ANALYTICS_BUCKET_CLIENT_ID')
BOOKSY_ANALYTICS_BUCKET_CLIENT_X509_CERT_URL = SecretsProvider.read_secret(
    'BOOKSY_ANALYTICS_BUCKET_CLIENT_X509_CERT_URL'
)

###############################
########## MARKETING## ########
###############################

BLAST_MONTHLY_EMAIL_LIMIT = 4
AUTO_BLAST_MONTHLY_EMAIL_LIMIT = 3
BLAST_MONTHLY_SMS_LIMIT = 4
AUTO_BLAST_MONTHLY_SMS_LIMIT = 3
BLAST_MONTHLY_PUSH_LIMIT = 4
AUTO_BLAST_MONTHLY_PUSH_LIMIT = 3

UNSUB_TOKEN_SECRET_KEY = SecretsProvider.read_secret('UNSUB_TOKEN_SECRET_KEY')


SMS_INVITE_BEST_HOURS_BY_ISOWEEKDAYS = {
    1: [7, 9, 10, 11, 13, 18, 19, 21],  # monday
    2: [0, 9, 13, 18, 20],  # tuesday
    3: [9, 11, 12, 14, 15, 17, 19, 20, 22],  # wednesday
    4: [7, 9, 10, 15, 18, 19, 20, 21],  # thursday
    5: [7, 12, 14, 17, 23],  # friday
    6: [0, 7, 8, 10, 13],  # saturday
    7: [7, 9, 10, 12, 13, 14, 16, 17, 18, 19, 22, 23],  # sunday
}

SMS_INVITATION_PERIOD_LIMIT_ON = API_COUNTRY in COUNTRY__BIZ__SMS_INVITATION_PERIOD_LIMIT
SMS_LIMIT_CHANGE_BLOCKED = API_COUNTRY in COUNTRY__BIZ__SMS_LIMIT_CHANGE_BLOCKED
SMS_LIMIT_EXTEND_BLOCKED = API_COUNTRY in COUNTRY__BIZ__SMS_LIMIT_EXTEND_BLOCKED

if not LIVE_DEPLOYMENT:
    SMS_INVITATION_LIMIT_PERIOD = datetime.timedelta(minutes=5)

if not LIVE_DEPLOYMENT and API_COUNTRY == Country.PL:
    SMS_INVITATION_PERIOD_LIMIT_ON = True
    SMS_LIMIT_EXTEND_BLOCKED = True


####################################################
######     GOOGLE ANALYTICS TRACKING ID     ########
####################################################

if LIVE_DEPLOYMENT:
    GA_TRACKING_ID = 'UA-86885879-13'
else:
    GA_TRACKING_ID = 'UA-86885879-12'

SQ_BRAINTREE_FREE_MONTH_ID = '1_free_month'
SQ_BRAINTREE_FREE_DAYS_ID = 'free_days'
SQ_BRAINTREE_NUMBER_OF_FREE_DAYS = 7
SQ_FREE_MONTHS_REFERRED = 1
SQ_EARNED_PUSH_TEXT = 'Congrats! You have just earned a free month of Booksy!'

##########################
###### C2B REFERRAL ######
##########################

C2B_REFERRAL = API_COUNTRY in [
    Country.US,
    Country.BR,
    Country.GB,
    Country.PL,
    Country.ZA,
]

C2B_REWARD_TEXT_DICT = {
    'br': {
        'short': '50 R$',
        'long': '50 R$ (Americanas.com Voucher)',
    },
    'pl': {
        'short': '200 zł',
        'long': '200 zł na zakupy',
    },
    'gb': {
        'short': '£35',
        'long': '£35 Amazon Gift Card',
    },
    'us': {
        'short': '$50',
        'long': '$50 Amazon Gift Card',
    },
    'za': {
        'short': '400 ZAR',
        'long': '400 ZAR Superbalist Gift Voucher',
    },
}
C2B_CS_MAIL_LIST = {
    'br': '<EMAIL>',
    'pl': '<EMAIL>',
    'gb': '<EMAIL>',
    'us': (
        '<EMAIL>',
        '<EMAIL>',
    ),
    'za': (
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
    ),
}
C2B_REQUIRED_PAYING_MONTHS_LIST = {
    'br': 1,
    'pl': 2,
    'gb': 2,
    'us': 2,
    'za': 2,
}

C2B_CS_MAIL = C2B_CS_MAIL_LIST.get(API_COUNTRY)
C2B_REQUIRED_PAYING_MONTHS = C2B_REQUIRED_PAYING_MONTHS_LIST.get(API_COUNTRY)
C2B_REWARD_TEXT = C2B_REWARD_TEXT_DICT.get(API_COUNTRY, {})


###############################
############ GDPR #############
###############################

GDPR_FOLDER = os.path.join(PROJECT_PATH, 'data/gdpr')

GDPR_AGREEMENTS_FOLDER = os.path.join(GDPR_FOLDER, 'agreements_descriptions')
# DESCRIPTION_FILES
GDPR_PRIVACY_POLICY_FILE = os.path.join(
    GDPR_AGREEMENTS_FOLDER,
    'privacy_policy.yaml',
)
GDPR_MARKETING_FILE = os.path.join(GDPR_AGREEMENTS_FOLDER, 'marketing.yaml')
GDPR_PARTNERS_MARKETING_FILE = os.path.join(
    GDPR_AGREEMENTS_FOLDER,
    'partners_marketing.yaml',
)
GDPR_WEB_COMMUNICATION_FILE = os.path.join(
    GDPR_AGREEMENTS_FOLDER,
    'web_communication.yaml',
)
GDPR_DISCLOSURE_OBLIGATION_FILE = os.path.join(
    GDPR_AGREEMENTS_FOLDER,
    'disclosure_obligation.yaml',
)
GDPR_PROCESSING_CONSENT_FILE = os.path.join(
    GDPR_AGREEMENTS_FOLDER,
    'processing_consent.yaml',
)
GDPR_RECEIVING_MESSAGES_CONSENT_FILE = os.path.join(
    GDPR_AGREEMENTS_FOLDER,
    'receiving_messages_consent.yaml',
)

GDPR_REQUESTS_FOLDER = os.path.join(GDPR_FOLDER, 'requests_descriptions')
GDPR_REQUESTS_FILE = os.path.join(GDPR_REQUESTS_FOLDER, 'gdpr_requests.yaml')

# 51173. All of this for Pl
GDPR_ANNEX = API_COUNTRY in GDPR_ANNEX_COUNTRIES

GDPR_ANNEX_FOLDER = os.path.join(
    STATICS_DIR,
    'pdf/gdpr/annexes',
)

GDPR_ANNEX_FILE_NAME = GDPR_ANNEX_SETTINGS.get(API_COUNTRY, {}).get('annex_file', '')
if GDPR_ANNEX and not GDPR_ANNEX_FILE_NAME:
    GDPR_ANNEX_FILE_NAME = GDPR_ANNEX_SETTINGS.get('default', {}).get(
        'annex_file',
    )

GDPR_ANNEX_FILE = os.path.join(GDPR_ANNEX_FOLDER, GDPR_ANNEX_FILE_NAME)
GDPR_ANNEX_EMAIL = GDPR_ANNEX_SETTINGS.get(API_COUNTRY, {}).get('email', '')
if GDPR_ANNEX and not GDPR_ANNEX_EMAIL:
    GDPR_ANNEX_EMAIL = GDPR_ANNEX_SETTINGS.get('default', {}).get('email')

GDPR_ANNEX_SIGNED_BY_DEFAULT = GDPR_ANNEX_SETTINGS.get(API_COUNTRY, {}).get(
    'signed_by_default', None
)
if GDPR_ANNEX_SIGNED_BY_DEFAULT is None:
    GDPR_ANNEX_SIGNED_BY_DEFAULT = GDPR_ANNEX_SETTINGS.get('default', {}).get(
        'signed_by_default', False
    )

if LOCAL_DEPLOYMENT:
    BOOKSY_EVENT_URL = '127.0.0.1:8981'
else:
    BOOKSY_EVENT_URL = ''


PATTERNS_DAYS_PAST = 30


###############################
# ####### Experiments ####### #
###############################

# initialize experiments on app startup
INITIALIZE_EXPERIMENTS = True

##################################
######### PHYSIOTHERAPY ##########
##################################
PHYSIOTHERAPY_COUNTRIES = ['pl']

if LIVE_DEPLOYMENT:
    PHYSIOTHERAPY_REDIRECT = 'fizjo.booksy.com'
elif LOCAL_DEPLOYMENT:
    PHYSIOTHERAPY_REDIRECT = 'fizjo-dev.booksy.net'
else:
    PHYSIOTHERAPY_REDIRECT = f'fizjo-{BOOKSY_DOMAIN}'


# <editor-fold desc="elearning">
##################################
########### ELEARNING ############
##################################
ELEARNING_ENABLED = YAML_CONFIG.get('elearning_enabled', ELEARNING_ENABLED)
if ELEARNING_ENABLED:
    if LIVE_DEPLOYMENT:
        ELEARNING_MOBILE_URL = 'https://elearning3.booksy.com/m/courses/'
        ELEARNING_WEB_URL = 'https://elearning3.booksy.com/courses/'
    else:
        ELEARNING_MOBILE_URL = 'https://elearning3-dev.booksy.net/m/courses/'
        ELEARNING_WEB_URL = 'https://elearning3-dev.booksy.net/courses/'
else:
    ELEARNING_MOBILE_URL = ''
    ELEARNING_WEB_URL = ''
# </editor-fold>


##################################
######### MESSAGE BLAST ##########
##################################
MANUAL_BLAST_LIMIT = 4
AUTO_BLAST_LIMIT = 3


######################
#####  INSTAGRAM #####
######################

# MASS CUSTOM PUSH SEND
MASS_CUSTOM_PUSH_BACKUP_MAIL = ''
if LIVE_DEPLOYMENT:
    # MASS CUSTOM PUSH SEND
    MASS_CUSTOM_PUSH_BACKUP_MAIL = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
    ]
if LOCAL_DEPLOYMENT and not PYTEST:
    # MASS CUSTOM PUSH SEND
    MASS_CUSTOM_PUSH_BACKUP_MAIL = 'test_email@not_exisitng_domain.bla'

# INSTAGRAM FEEDS
INSTAGRAM_FEED_FOLDER = os.path.join(MEDIA_ROOT, 'feeds/instagram/')
INSTAGRAM_FEED_FILE_NAME_FORMAT = 'instagram_feeds_part_{number}.json'
INSTAGRAM_FEED_FILE = os.path.join(INSTAGRAM_FEED_FOLDER, 'instagram_feeds.json')
INSTAGRAM_FEEDS_PER_PAGE = 1000
INSTAGRAM_APP_ID = '523698674383043'
INSTAGRAM_FEEDS_OWNER_MAIL = '<EMAIL>'

# API WITH INSTAGRAM LOGIN
API_WITH_INSTAGRAM_LOGIN_APP_ID = SecretsProvider.read_secret('API_WITH_INSTAGRAM_LOGIN_APP_ID')
API_WITH_INSTAGRAM_LOGIN_APP_SECRET = SecretsProvider.read_secret(
    'API_WITH_INSTAGRAM_LOGIN_APP_SECRET'
)


##################################
####### GOOGLE WEBHOOKS ##########
##################################

GOOGLE_WEBHOOK_SIGNATURE = SecretsProvider.read_secret('GOOGLE_WEBHOOK_SIGNATURE')
GOOGLE_WEBHOOK_KEY = SecretsProvider.read_secret('GOOGLE_WEBHOOK_KEY')


##################################
####### APPLE WEBHOOKS ##########
##################################

APPLE_WEBHOOK_SIGNATURE = SecretsProvider.read_secret('APPLE_WEBHOOK_SIGNATURE')
APPLE_WEBHOOK_KEY = SecretsProvider.read_secret('APPLE_WEBHOOK_KEY')


############################
####### SUBSCRIPTIONS ######
############################

# 56156
SUBSCRIPTION_PURCHASE_LIMIT_STAFF_NUM = (
    API_COUNTRY in COUNTRY__SUBSCRIPTION_PURCHASE_LIMIT_STAFF_NUM
)

# BIL-597
SUBSCRIPTION_TEST_BUSINESSES = COUNTRY__SUBSCRIPTION_TEST_BUSINESSES.get(API_COUNTRY, [])
# google sign in
prefix = f'/admin/{API_COUNTRY}/'  # pylint: disable=invalid-name
ACCOUNT_LOGOUT_REDIRECT_URL = f'{prefix}login/'
LOGIN_REDIRECT_URL = prefix

# Google oauth credentials to login in admin panel
GOOGLE_OAUTH_CLIENT_ID = SecretsProvider.read_secret('GOOGLE_OAUTH_CLIENT_ID')
GOOGLE_OAUTH_SECRET_KEY = SecretsProvider.read_secret('GOOGLE_OAUTH_SECRET_KEY')

# flag used to disable google sign_in - currently disabled only on localhost

ENABLED_GOOGLE_SIGN_IN = PYTEST or (not LOCAL_DEPLOYMENT and not AUTOMATION_TESTS_ENV)

if ENABLED_GOOGLE_SIGN_IN:
    AUTHENTICATION_BACKENDS = ('allauth.account.auth_backends.AuthenticationBackend',)
else:
    AUTHENTICATION_BACKENDS = ('webapps.admin_extra.auth_backend.BooksyBackend',)


INVOICING_GRPC_HOST = YAML_CONFIG['invoicing-grpc-host']
DEEPLINK_GRPC_HOST = YAML_CONFIG['deeplinks-grpc-host']


# region INVOICE DETAILS FORMS

DEFAULT_INVOICE_FORM = InvoiceDetailsFormsEnum.PATH_3
INVOICE_DETAILS_FORMS = {
    Country.MX: None,
    Country.BR: None,
    Country.FR: InvoiceDetailsFormsEnum.PATH_2,
    Country.GB: InvoiceDetailsFormsEnum.PATH_2,
    Country.PL: InvoiceDetailsFormsEnum.PATH_2,
    Country.ES: InvoiceDetailsFormsEnum.PATH_1,
    Country.IE: InvoiceDetailsFormsEnum.TWO_TAX_ID_FIELDS,
    Country.US: InvoiceDetailsFormsEnum.CONFIRM_OPTIONAL_TAX_ID,
}
SUPPORTED_INVOICE_FORM = INVOICE_DETAILS_FORMS.get(API_COUNTRY, DEFAULT_INVOICE_FORM)

INVOICE_DETAILS_TAX_ID_PREFIX_PER_COUNTRY = {
    Country.GB: 'GB',
    Country.IE: 'IE',
}
INVOICE_DETAILS_TAX_ID_PREFIX = INVOICE_DETAILS_TAX_ID_PREFIX_PER_COUNTRY.get(API_COUNTRY)

# endregion

HIDE_INVOICE_DETAILS_FROM_OFFLINE_MERCHANTS = API_COUNTRY in {
    Country.BR,
    Country.FR,
    Country.MX,
}

NAVISION_US_COUNTRIES = {
    Country.US,
    Country.CA,
    Country.MX,
    Country.ZA,
    Country.BR,
    Country.AR,
    Country.AU,
    Country.CL,
    Country.IN,
    Country.MY,
    Country.NG,
    Country.RU,
    Country.SG,
}

NAVISION_SAAS_COUNTRIES = {
    Country.ES,
    Country.FR,
    Country.GB,
    Country.IE,
    Country.PL,
    Country.US,
}

NAVISION_INTEGRATION_ENABLED = API_COUNTRY in (
    Country.PL,
    *NAVISION_US_COUNTRIES,
    *NAVISION_SAAS_COUNTRIES,
)

NAVISION_USE_TAX_GROUPS = API_COUNTRY in {
    Country.ES,
    Country.FR,
    Country.GB,
    Country.IE,
}

if API_COUNTRY == Country.US:
    NAVISION_TAX_AREA = RegionType.ZIP
    NAVISION_TAX_CALCULATED_FROM_ZIPCODE = True
else:
    NAVISION_TAX_AREA = RegionType.COUNTRY
    NAVISION_TAX_CALCULATED_FROM_ZIPCODE = False

NAVISION_ENABLE_BOOST_TAX_RATES = API_COUNTRY not in {Country.US}

if not LIVE_DEPLOYMENT:
    NAVISION_INTEGRATION_ENABLED = True

NAVISION_BASE_AUTH_USER = SecretsProvider.read_secret('NAVISION_BASE_AUTH_USER')
NAVISION_BASE_AUTH_PASSWORD = SecretsProvider.read_secret('NAVISION_BASE_AUTH_PASSWORD')

NAVISION_TEST_API_PER_COUNTRY = {
    Country.US: {
        'url': 'https://bc-us-api.booksy.com:7058/Booksy-NA-Test-WebClient/',
        'company_id': '714db899-a152-442d-b1fe-589e881a9cb4',
        'company_name': 'Test Booksy US Inc.',
        'user': NAVISION_BASE_AUTH_USER,
        'password': NAVISION_BASE_AUTH_PASSWORD,
    },
    Country.PL: {
        'url': 'https://bc-api.booksy.com:7058/Booksy-Test-WebClient/',
        'company_id': '82f22376-75e0-4da7-a951-82e227d9a7c6',
        'company_name': 'Test Booksy International',
        'user': NAVISION_BASE_AUTH_USER,
        'password': NAVISION_BASE_AUTH_PASSWORD,
    },
}

if API_COUNTRY in NAVISION_US_COUNTRIES:
    NAVISION_TEST_API = NAVISION_TEST_API_PER_COUNTRY[Country.US]
else:
    NAVISION_TEST_API = NAVISION_TEST_API_PER_COUNTRY[Country.PL]

NAVISION_PRODUCTION_API_PER_COUNTRY = {
    Country.US: {
        'url': 'https://bc-us-api.booksy.com:7058/Booksy-NA-WebClient/',
        'company_id': '1317ad3d-dc39-4307-a031-5a2a614adb85',
        'company_name': 'Booksy US Inc.',
        'user': NAVISION_BASE_AUTH_USER,
        'password': NAVISION_BASE_AUTH_PASSWORD,
    },
    Country.PL: {
        'url': 'https://bc-api.booksy.com:7058/Booksy-WebClient/',
        'company_id': '82f22376-75e0-4da7-a951-82e227d9a7c6',
        'company_name': 'Booksy%20International%20Sp.%20z%20o.o',
        'user': NAVISION_BASE_AUTH_USER,
        'password': NAVISION_BASE_AUTH_PASSWORD,
    },
}

NAVISION_PRODUCTION_API = NAVISION_PRODUCTION_API_PER_COUNTRY.get(API_COUNTRY)

NAVISION_OAUTH2_SCOPE = 'https://api.businesscentral.dynamics.com/.default'

NAVISION_OAUTH2_SANDBOX_CLIENT_ID = SecretsProvider.read_secret('NAVISION_OAUTH2_SANDBOX_CLIENT_ID')
NAVISION_OAUTH2_SANDBOX_CLIENT_SECRET = SecretsProvider.read_secret(
    'NAVISION_OAUTH2_SANDBOX_CLIENT_SECRET'
)

NAVISION_OAUTH2_PRODUCTION_CLIENT_ID = SecretsProvider.read_secret(
    'NAVISION_OAUTH2_PRODUCTION_CLIENT_ID'
)
NAVISION_OAUTH2_PRODUCTION_CLIENT_SECRET = SecretsProvider.read_secret(
    'NAVISION_OAUTH2_PRODUCTION_CLIENT_SECRET'
)
NAVISION_OAUTH2_TOKEN_URL = (
    'https://login.microsoft.com/01b7ef41-edf5-409f-90ed-10e9ca0bb3c5/oauth2/v2.0/token'
)

NAVISION_URL_ROOT = (
    'https://api.businesscentral.dynamics.com/v2.0/01b7ef41-edf5-409f-90ed-10e9ca0bb3c5/'
    '{}_{}/api/Booksy/Admin/v2.0/companies({})/'
)

NAVISION_COMPANY_IDS = {
    Country.GB: '9b30b109-49c5-ec11-8e7f-000d3a0d2c68',
    Country.ES: 'e49beb91-5fe2-ec11-82f8-6045bd91b7ba',
    Country.FR: '00e874d4-25ca-ed11-a7c9-6045bd6d20cc',
    Country.PL: 'a3468cee-1e5e-ef11-bfe2-000d3abde3fd',
    Country.US: 'e64fda36-3b3d-f011-be59-7c1e5217941d',
}


def get_navision_url(env_type: str, country: str):
    if country == Country.IE:  # invoices in IE sent to BC client in ES
        country = Country.ES
    return NAVISION_URL_ROOT.format(
        env_type,
        'UK' if country == Country.GB else country.upper(),
        NAVISION_COMPANY_IDS.get(country, ''),
    )


SANDBOX_ONLY_COUNTRIES = {}

if API_COUNTRY in NAVISION_SAAS_COUNTRIES:
    NAVISION_SANDBOX_URL = get_navision_url('Sandbox', API_COUNTRY)
    if API_COUNTRY in SANDBOX_ONLY_COUNTRIES:
        NAVISION_PRODUCTION_URL = None
    else:
        NAVISION_PRODUCTION_URL = get_navision_url('Production', API_COUNTRY)
else:
    NAVISION_SANDBOX_URL = None
    NAVISION_PRODUCTION_URL = None


# region GCP INVOICE STORAGE
NAVISION_INVOICE_STORAGE_PRIVATE_KEY = SecretsProvider.read_secret(
    'NAVISION_INVOICE_STORAGE_PRIVATE_KEY'
)
NAVISION_INVOICE_STORAGE_PROJECT_ID = SecretsProvider.read_secret(
    'NAVISION_INVOICE_STORAGE_PROJECT_ID'
)
NAVISION_INVOICE_STORAGE_PRIVATE_KEY_ID = SecretsProvider.read_secret(
    'NAVISION_INVOICE_STORAGE_PRIVATE_KEY_ID'
)
NAVISION_INVOICE_STORAGE_CLIENT_EMAIL = SecretsProvider.read_secret(
    'NAVISION_INVOICE_STORAGE_CLIENT_EMAIL'
)
NAVISION_INVOICE_STORAGE_CLIENT_ID = SecretsProvider.read_secret(
    'NAVISION_INVOICE_STORAGE_CLIENT_ID'
)
NAVISION_INVOICE_STORAGE_CLIENT_X509_CERT_URL = SecretsProvider.read_secret(
    'NAVISION_INVOICE_STORAGE_CLIENT_X509_CERT_URL'
)
# endregion


NAVISION_FLAT_MERCHANT_BILLING_ADDRESS = API_COUNTRY in NAVISION_SAAS_COUNTRIES

NAVISION_USE_COUNTRYWIDE_TAX_GROUP_FOR_NON_VAT_MERCHANTS = API_COUNTRY == Country.ES

if LIVE_DEPLOYMENT:
    NAVISION_ENABLE_MERCHANT_INTEGRATION = API_COUNTRY in {
        Country.PL,
        Country.ES,
        Country.FR,
        Country.GB,
        Country.IE,
        Country.US,
    }
    NAVISION_ENABLE_INVOICING_INTEGRATION = API_COUNTRY in {
        Country.PL,
        Country.ES,
        Country.FR,
        Country.GB,
        Country.IE,
        Country.US,
    }
else:
    NAVISION_ENABLE_MERCHANT_INTEGRATION = False
    NAVISION_ENABLE_INVOICING_INTEGRATION = False

NAVISION_ENABLE_TAX_RATE_INTEGRATION = API_COUNTRY in {
    Country.US,  # Fetch real data even in test environments
}

if LIVE_DEPLOYMENT:
    NAVISION_PREFIX = 'CB'
else:
    # We are limited to 8 characters in prefix
    NAVISION_PREFIX = DEPLOYMENT_LEVEL[:8]

CHARGE_GROSS_PRICE = API_COUNTRY in {
    Country.ES,
    Country.FR,
    Country.GB,
    Country.IE,
    Country.US,
}
EXPOSE_GROSS_PRICE = API_COUNTRY not in {
    Country.PL,
}

# todo: use ENUMS Business.PaymentSource and InvoiceService
SUPPORTED_INVOICE_PAYMENT_METHODS_PER_COUNTRY = {
    Country.US: {
        'R': {  # New billing
            'SaaS',
            'Boost',
        },
    },
    Country.PL: {
        'R': {  # New billing
            'SaaS',
            'Boost',
        },
        'O': {  # Offline
            'SaaS',
            'Boost',
        },
    },
    Country.ES: {
        'R': {  # New billing
            'SaaS',
            'Boost',
        },
    },
    Country.CA: {
        'R': {  # New billing
            'SaaS',
            'Boost',
        },
    },
    Country.GB: {
        'R': {  # New billing
            'SaaS',
            'Boost',
        },
    },
    Country.IE: {
        'R': {  # New billing
            'SaaS',
        },
    },
    Country.FR: {
        'R': {  # New billing
            'SaaS',
            'Boost',
        },
    },
    Country.MX: {
        'R': {  # New billing
            'SaaS',
        },
    },
    Country.ZA: {
        'R': {  # New billing
            'SaaS',
            'Boost',
        },
    },
    Country.BR: {
        'R': {  # New billing
            'SaaS',
        },
    },
    Country.AR: {
        'R': {  # New billing
            'SaaS',
        },
    },
    Country.AU: {
        'R': {  # New billing
            'SaaS',
        },
    },
    Country.CL: {
        'R': {  # New billing
            'SaaS',
        },
    },
    Country.IN: {
        'R': {  # New billing
            'SaaS',
        },
    },
    Country.MY: {
        'R': {  # New billing
            'SaaS',
        },
    },
    Country.NG: {
        'R': {  # New billing
            'SaaS',
        },
    },
    Country.RU: {
        'R': {  # New billing
            'SaaS',
        },
    },
    Country.SG: {
        'R': {  # New billing
            'SaaS',
        },
    },
}

SUPPORTED_INVOICE_PAYMENT_METHODS = SUPPORTED_INVOICE_PAYMENT_METHODS_PER_COUNTRY.get(
    API_COUNTRY, {}
)

INVOICE_SERVICES_SEND_TO_PRODUCTION_PER_COUNTRY = {
    Country.ES: {
        InvoicePaymentSource.STRIPE: {
            InvoiceService.BOOST,
            InvoiceService.SAAS,
            InvoiceService.SMS,
        },
        InvoicePaymentSource.HOF: {
            InvoiceService.SAAS,
            InvoiceService.SMS,
        },
    },
    Country.FR: {
        InvoicePaymentSource.STRIPE: {
            InvoiceService.SAAS,
            InvoiceService.SMS,
        },
        InvoicePaymentSource.HOF: {
            InvoiceService.SAAS,
            InvoiceService.SMS,
        },
    },
    Country.GB: {
        InvoicePaymentSource.STRIPE: {
            InvoiceService.BOOST,
        },
        InvoicePaymentSource.HOF: {
            InvoiceService.SAAS,
            InvoiceService.SMS,
        },
    },
    Country.PL: {
        InvoicePaymentSource.OFFLINE: {
            InvoiceService.BOOST,
            InvoiceService.SAAS,
            InvoiceService.SMS,
        },
        InvoicePaymentSource.STRIPE: {
            InvoiceService.BOOST,
            InvoiceService.SAAS,
            InvoiceService.SMS,
        },
        InvoicePaymentSource.BRAINTREE: {
            InvoiceService.BOOST,
            InvoiceService.SAAS,
            InvoiceService.SMS,
        },
    },
    Country.IE: {
        InvoicePaymentSource.STRIPE: {
            InvoiceService.BOOST,
        },
        InvoicePaymentSource.HOF: {
            InvoiceService.SAAS,
            InvoiceService.SMS,
        },
    },
}

INVOICE_SERVICES_SEND_TO_PRODUCTION = INVOICE_SERVICES_SEND_TO_PRODUCTION_PER_COUNTRY.get(
    API_COUNTRY,
    {},
)

# SubscriptionBuyer is not required in countries with offline subscriptions
# where we haven't yet introduced new invoicing process
PURCHASE_SUBSCRIPTION_BUYER_IS_REQUIRED = API_COUNTRY not in (
    Country.BR,
    Country.FR,
    Country.MX,
    Country.US,
)


NAVISION_SALES_DATE_SUPPORTED = API_COUNTRY == Country.PL
NAVISION_BANK_CODE_SUPPORTED = API_COUNTRY == Country.PL

NAVISION_MERCHANT_BUSINESS_ID_SUPPORTED = API_COUNTRY not in NAVISION_US_COUNTRIES

NAVISION_SPLIT_INVOICES_BASED_ON_BILLING_CYCLE = API_COUNTRY in {
    Country.ES,
    Country.FR,
    Country.GB,
    Country.IE,
    Country.PL,
    Country.US,
}

NAVISION_AGGREGATE_INVOICES = API_COUNTRY in {
    Country.PL,
}

NAVISION_SOFT_MIN_SAAS_PRICE_NET_PER_COUNTRY = {
    Country.PL: 1,  # PLN
    Country.US: 1,  # USD
}

NAVISION_HARD_MIN_SAAS_PRICE_NET_PER_COUNTRY = {
    Country.PL: 0.01,  # PLN
    Country.US: 0.01,  # USD
}

NAVISION_DEFAULT_SOFT_MIN_SAAS_PRICE_NET = 1
NAVISION_DEFAULT_HARD_MIN_SAAS_PRICE_NET = 0.01

NAVISION_SOFT_MIN_SAAS_PRICE_NET = NAVISION_SOFT_MIN_SAAS_PRICE_NET_PER_COUNTRY.get(
    API_COUNTRY, NAVISION_DEFAULT_SOFT_MIN_SAAS_PRICE_NET
)
NAVISION_HARD_MIN_SAAS_PRICE_NET = NAVISION_HARD_MIN_SAAS_PRICE_NET_PER_COUNTRY.get(
    API_COUNTRY, NAVISION_DEFAULT_HARD_MIN_SAAS_PRICE_NET
)

NAVISION_SUPPORTED_CURRENCIES_PER_COUNTRY = {
    # ISO 4217 currency codes
    Country.PL: {
        'PLN',
    },
    Country.US: {
        'USD',
    },
    Country.ES: {
        'EUR',
    },
    Country.FR: {
        'EUR',
    },
    Country.CA: {
        'USD',
        'CAD',
    },
    Country.GB: {
        'GBP',
    },
    Country.IE: {
        'EUR',
    },
    Country.MX: {
        'USD',
        'MXN',
    },
    Country.ZA: {
        'USD',
        'ZAR',
    },
    Country.BR: {
        'USD',
        'BRL',
    },
    Country.AR: {
        'USD',
    },
    Country.AU: {
        'USD',
    },
    Country.CL: {
        'USD',
    },
    Country.IN: {
        'USD',
    },
    Country.MY: {
        'USD',
    },
    Country.NG: {
        'USD',
    },
    Country.RU: {
        'USD',
    },
    Country.SG: {
        'USD',
    },
}

NAVISION_SUPPORTED_CURRENCIES = NAVISION_SUPPORTED_CURRENCIES_PER_COUNTRY.get(API_COUNTRY, set())

ACCOUNTING_GROUP_PER_COUNTRY = {
    # Our navision instances persists there, or they are using cloud
    Country.US: 'domestic',
    Country.PL: 'domestic',
    Country.GB: 'domestic',
    Country.IE: 'domestic',
    Country.ES: 'domestic',
    Country.FR: 'domestic',
    # EU productions are going to use navision in Poland
    Country.IT: 'eu',
    Country.NL: 'eu',
    Country.DE: 'eu',
    Country.PT: 'eu',
    Country.SE: 'eu',
    # We don't know anything about other countries, so they are `foreign`
    # if somehow any merchant reach our navision/pl or navision/us
    Country.CA: 'foreign',
    Country.MX: 'foreign',
    Country.ZA: 'foreign',
    Country.BR: 'foreign',
    Country.AR: 'foreign',
    Country.AU: 'foreign',
    Country.CL: 'foreign',
    Country.IN: 'foreign',
    Country.MY: 'foreign',
    Country.NG: 'foreign',
    Country.RU: 'foreign',
    Country.SG: 'foreign',
}

NAVISION_ACCOUNTING_GROUP = ACCOUNTING_GROUP_PER_COUNTRY.get(API_COUNTRY, 'foreign')

if API_COUNTRY in NAVISION_US_COUNTRIES:
    NAVISION_PRICE_WITH_VAT = False
else:
    NAVISION_PRICE_WITH_VAT = True

NAVISION_BRAINTREE_BOOST_CYCLE_END_TO_INVOICE_DAYS = 14 if LIVE_DEPLOYMENT else 1


if LIVE_DEPLOYMENT:
    NAVISION_GCP_INVOICE_BUCKET = 'bks-prd-3-eu-w1-bc-bucket'
else:
    NAVISION_GCP_INVOICE_BUCKET = 'bks-dev-2-eu-w1-287009-bc-test-bucket'

NAVISION_AUTO_VERIFY_MERCHANTS = API_COUNTRY in {
    Country.PL,
    *NAVISION_US_COUNTRIES,
}

# on test env basically every account is marked as "test"
NAVISION_EXCLUDE_TEST_BUSINESSES = LIVE_DEPLOYMENT

NAVISION_CHECK_TAX_RATE_API_ON_NEW_MERCHANT = (API_COUNTRY == Country.US) and (not PYTEST)

NAVISION_DUMMY_BUYER_IDS_BY_COUNTRY = {
    Country.ES: 12483,
    Country.FR: 2809,
    Country.GB: 8097,
    Country.IE: 1034,
    Country.PL: 44792,
}

NAVISION_DUMMY_BUYER_ID = NAVISION_DUMMY_BUYER_IDS_BY_COUNTRY.get(API_COUNTRY)


NAVISION_STRIPE_MIGRATION_DATE = {
    Country.GB: datetime.date(2025, 4, 1),
    Country.IE: datetime.date(2025, 4, 1),
}

NAVISION_STRIPE_MIGRATION_DATE_PER_COUNTRY = NAVISION_STRIPE_MIGRATION_DATE.get(API_COUNTRY)


CHECK_ZIPCODE_IN_REGION_TABLE = API_COUNTRY not in {
    # Do not lookup for zipcode in region table for IE
    # (because this table is empty for that country)
    Country.IE,
    # and in FR, GB because our region table is outdated/incomplete here
    Country.FR,
    Country.GB,
}

NAVISION_BANK_CODES_PER_COUNTRY = {
    Country.PL: {
        'O': 'ALIOR SP',  # Offline
        'B': 'ALIOR BRT',  # Braintree
        'S': 'ALIOR STRIPE',  # Stripe
    }
}
NAVISION_BANK_CODES_PER_SOURCE = NAVISION_BANK_CODES_PER_COUNTRY.get(API_COUNTRY, {})

NAVISION_USE_ENOVA_ID = API_COUNTRY == Country.PL
NAVISION_NEW_MERCHANT_ID_OFFSET = 20_000

NAVISION_API_COUNTRY_CODE_SUPPORTED = API_COUNTRY != Country.FR

NAVISION_COUNTRY_CODE_PER_API_COUNTRY_CODE = {
    Country.GB: 'UK',
    Country.IE: 'IE',
    Country.US: 'US',
    Country.CA: 'US',
    Country.ES: 'ES',
    Country.FR: 'FR',
    Country.BR: 'BR',
    Country.PL: 'PL',
    Country.MX: 'MX',
    Country.ZA: 'ZA',
}

NAVISION_COUNTRY = NAVISION_COUNTRY_CODE_PER_API_COUNTRY_CODE.get(API_COUNTRY, 'OC')

NAVISION_GCP_COUNTRY_CODE_PER_API_COUNTRY_CODE = {
    Country.GB: 'UK',
    Country.IE: 'ES',
    Country.PL: 'PL',
    Country.ES: 'ES',
    Country.FR: 'FR',
    Country.US: 'US',
}

NAVISION_GCP_COUNTRY_CODE = NAVISION_GCP_COUNTRY_CODE_PER_API_COUNTRY_CODE.get(
    API_COUNTRY,
)

NAVISION_STATES_ENABLED_FOR_GROSS_PRICING = {
    'New Jersey',
    'South Carolina',
    'Arkansas',
}

NAVISION_INVOICE_ONLY_SELECTED_ACCOUNTS = (
    API_COUNTRY
    in {
        Country.ES,
    }
    and LIVE_DEPLOYMENT
)

NAVISION_INVOICED_ACCOUNTS_PER_COUNTRY = {
    Country.ES: {
        'acct_1KluTVCidyrxV1Kh',
    },
    Country.FR: {
        'acct_1KP27DHv4pCsQrN0',
    },
}

NAVISION_INVOICED_ACCOUNTS = NAVISION_INVOICED_ACCOUNTS_PER_COUNTRY.get(API_COUNTRY, set())

SELLER_BKSY_INTERNATIONAL = {
    'entity_name': 'BKSY INTERNATIONAL SPOLKA S.L.',
    'tax_id': 'B88598636',
    'address_details1': 'CL VELAZQUEZ 86',
    'address_details2': '1 DCH',
    'zipcode': '28006',
    'city': 'Madrid',
}

SELLER_BOOKSY_IBERIA = {
    'entity_name': 'BOOKSY IBERIA S.L.',
    'tax_id': 'B88598636',
    'address_details1': 'Paseo de la Castellana 216, Planta 9, Despacho 918',
    'zipcode': '28046',
    'city': 'Madrid',
}

SELLER_KIUTE = {
    'entity_name': 'KIUTE SAS',
    'tax_id': 'FR92799889704',
    'address_details1': '37 Rue Bergère',
    'zipcode': '75009',
    'city': 'Paris',
}

SELLER_BOOKSY_GB = {
    'entity_name': 'Booksy UK Ltd',
    'tax_id': '*********',
    'address_details1': 'Ealing Cross, 1st Floor 85 Uxbridge Road',
    'zipcode': 'W5 5BW',
    'city': 'London',
}

NAVISION_SELLER_BY_ACCOUNT_ID = {
    'acct_1KluTVCidyrxV1Kh': SELLER_BKSY_INTERNATIONAL,
    'acct_1KP27DHv4pCsQrN0': SELLER_KIUTE,
    'acct_1Ox9tHKBNDDx2Twh': SELLER_BOOKSY_GB,
}

NAVISION_SELLER_BY_ACCOUNT_ID_BY_TIME = {
    'acct_1KluTVCidyrxV1Kh': {
        datetime.datetime(2023, 3, 21): SELLER_BOOKSY_IBERIA,
        datetime.datetime(2020, 1, 1): SELLER_BKSY_INTERNATIONAL,
    },
}


NAVISION_SYNC_WITH_BOTH_ENVIRONMENTS = LIVE_DEPLOYMENT and NAVISION_ENABLE_MERCHANT_INTEGRATION
NAVISION_SAAS_COUNTRY = API_COUNTRY in NAVISION_SAAS_COUNTRIES

###########################
##### Braintree only ######
###########################

CHARGE_FOR_STAFFERS_ENABLED = API_COUNTRY in COUNTRY__CHARGE_FOR_STAFFERS_ENABLED
STAFFERS_PLAN_SWITCH_ENABLED = (API_COUNTRY in COUNTRY__STAFFERS_PLAN_SWITCH_ENABLED,)

# IDs from Braintree
BUNDLE_DISCOUNT_ID = 'bundle_discount'

COVID_19_MANUAL_DISCOUNT_ID = '4z2r'
# COVID-19 reason, too
POB_DISCOUNT_ID = 'kfg6'

SKIP_STAFF_CHARGES_DISCOUNT_IDS = {
    # Always 100%
    SQ_BRAINTREE_FREE_MONTH_ID,
    COVID_19_MANUAL_DISCOUNT_ID,
    POB_DISCOUNT_ID,
}

# Discount ID prefixes
SKIP_STAFF_CHARGES_PREFIXES = {
    'covid_',
}

DISCOUNT_AFTER_POB_ENABLED = API_COUNTRY in COUNTRY__DISCOUNT_AFTER_POB_ENABLED

BRAINTREE_3D_SECURE_ELIGIBLE_COUNTRIES = (
    Country.PL,
    Country.ES,
    Country.DE,
    Country.IT,
    Country.NL,
    Country.SE,
    Country.IE,
    Country.GB,
)
BRAINTREE_3D_SECURE_ELIGIBLE = API_COUNTRY in BRAINTREE_3D_SECURE_ELIGIBLE_COUNTRIES

BRAINTREE_MERCHANT_ACCOUNT_ID = BRAINTREE_MERCHANT_ACCOUNTS_BY_COUNTRY.get(API_COUNTRY)

########## End of Braintree only

if not LIVE_DEPLOYMENT and API_COUNTRY == Country.PL:
    CHARGE_FOR_STAFFERS_ENABLED = True

DEFAULT_INVOICE_STORAGE_SETTINGS = {
    'invoices_bucket_name': 'boo-enova-invoices-dev',
    'invoices_location': 'invoices',
    'invoices_region_name': 'us-east-1',
    'invoices_signature_version': 's3v4',
}

INVOICE_STORAGE_SETTINGS_PER_COUNTRY = {
    Country.FR: {
        'invoices_bucket_name': 'dev.fr.invoices.booksy.net',
        'invoices_location': 'invoices',
        'invoices_region_name': 'eu-west-3',
        'invoices_signature_version': 's3v4',
    },
}

INVOICES_STORAGE_ENABLED_COUNTRIES = {
    Country.PL,  #  Old Enova invoices for PL
    Country.FR,  #  Chargebee invoices for FR (Kiute)
}
INVOICES_ENABLED = API_COUNTRY in INVOICES_STORAGE_ENABLED_COUNTRIES or INVOICES_ENABLED

INVOICE_STORAGE_SETTINGS = INVOICE_STORAGE_SETTINGS_PER_COUNTRY.get(
    API_COUNTRY, DEFAULT_INVOICE_STORAGE_SETTINGS
)

AWS_INVOICE_STORAGE_BUCKET_NAME = YAML_CONFIG.get(
    'invoices_bucket_name', INVOICE_STORAGE_SETTINGS['invoices_bucket_name']
)
AWS_INVOICE_LOCATION = YAML_CONFIG.get(
    'invoices_location', INVOICE_STORAGE_SETTINGS['invoices_location']
)
AWS_INVOICE_REGION_NAME = YAML_CONFIG.get(
    'invoices_region_name', INVOICE_STORAGE_SETTINGS['invoices_region_name']
)
AWS_INVOICE_SIGNATURE_VERSION = YAML_CONFIG.get(
    'invoices_signature_version', INVOICE_STORAGE_SETTINGS['invoices_signature_version']
)

AWS_INVOICE_ACCESS_KEY = SecretsProvider.read_secret('AWS_INVOICE_ACCESS_KEY')
AWS_INVOICE_SECRET_ACCESS_KEY = SecretsProvider.read_secret('AWS_INVOICE_SECRET_ACCESS_KEY')

AWS_QUERYSTRING_AUTH = True

COLLECTFAST_STRATEGY = 'collectfast.strategies.boto3.Boto3Strategy'
if not LIVE_DEPLOYMENT:
    COLLECTFAST_THREADS = 2
else:
    COLLECTFAST_THREADS = 4

# ONBOARDING
ONBOARDING_ENABLED = YAML_CONFIG.get('onboarding_enabled', ONBOARDING_ENABLED)

ENABLE_FRONTDESK = {
    Country.US: datetime.date(2021, 4, 19),
    Country.PL: datetime.date(2021, 4, 19),
    Country.GB: datetime.date(2021, 5, 10),
    Country.IE: datetime.date(2021, 5, 10),
    Country.ZA: datetime.date(2021, 5, 10),
    Country.BR: datetime.date(2021, 5, 17),
    Country.ES: datetime.date(2021, 5, 17),
    Country.CA: datetime.date(2021, 5, 24),
}
if not LIVE_DEPLOYMENT:
    ENABLE_FRONTDESK = {
        Country.US: datetime.date(2021, 4, 15),
        Country.PL: datetime.date(2021, 4, 15),
        Country.GB: datetime.date(2021, 5, 5),
        Country.IE: datetime.date(2021, 5, 5),
        Country.ZA: datetime.date(2021, 5, 5),
        Country.BR: datetime.date(2021, 5, 17),
        Country.ES: datetime.date(2021, 5, 17),
        Country.CA: datetime.date(2021, 5, 20),
    }


@dataclass
class Phase3Period:
    start_date: datetime.date
    end_date: datetime.date


MIGRATION_PHASE_3_PERIOD: typing.Dict[Country, Phase3Period] = {
    Country.AR: Phase3Period(
        start_date=datetime.date(2021, 8, 26),
        end_date=datetime.date(2021, 9, 14),
    ),
    Country.AU: Phase3Period(
        start_date=datetime.date(2021, 8, 26),
        end_date=datetime.date(2021, 9, 14),
    ),
    Country.BR: Phase3Period(
        start_date=datetime.date(2021, 8, 26),
        end_date=datetime.date(2021, 9, 14),
    ),
    Country.CA: Phase3Period(
        start_date=datetime.date(2021, 8, 26),
        end_date=datetime.date(2021, 9, 14),
    ),
    Country.CL: Phase3Period(
        start_date=datetime.date(2021, 8, 26),
        end_date=datetime.date(2021, 9, 14),
    ),
    Country.CO: Phase3Period(
        start_date=datetime.date(2021, 8, 26),
        end_date=datetime.date(2021, 9, 14),
    ),
    Country.DE: Phase3Period(
        start_date=datetime.date(2021, 8, 26),
        end_date=datetime.date(2021, 9, 14),
    ),
    Country.ES: Phase3Period(
        start_date=datetime.date(2021, 8, 26),
        end_date=datetime.date(2021, 9, 14),
    ),
    Country.GB: Phase3Period(
        start_date=datetime.date(2021, 8, 26),
        end_date=datetime.date(2021, 9, 14),
    ),
    Country.IE: Phase3Period(
        start_date=datetime.date(2021, 8, 26),
        end_date=datetime.date(2021, 9, 14),
    ),
    Country.IN: Phase3Period(
        start_date=datetime.date(2021, 8, 26),
        end_date=datetime.date(2021, 9, 14),
    ),
    Country.IT: Phase3Period(
        start_date=datetime.date(2021, 8, 26),
        end_date=datetime.date(2021, 9, 14),
    ),
    Country.MX: Phase3Period(
        start_date=datetime.date(2021, 8, 26),
        end_date=datetime.date(2021, 9, 14),
    ),
    Country.MY: Phase3Period(
        start_date=datetime.date(2021, 8, 26),
        end_date=datetime.date(2021, 9, 14),
    ),
    Country.NG: Phase3Period(
        start_date=datetime.date(2021, 8, 26),
        end_date=datetime.date(2021, 9, 14),
    ),
    Country.NL: Phase3Period(
        start_date=datetime.date(2021, 8, 26),
        end_date=datetime.date(2021, 9, 14),
    ),
    Country.PL: Phase3Period(
        start_date=datetime.date(2021, 8, 26),
        end_date=datetime.date(2021, 9, 14),
    ),
    Country.PT: Phase3Period(
        start_date=datetime.date(2021, 8, 26),
        end_date=datetime.date(2021, 9, 14),
    ),
    Country.RU: Phase3Period(
        start_date=datetime.date(2021, 8, 26),
        end_date=datetime.date(2021, 9, 14),
    ),
    Country.SE: Phase3Period(
        start_date=datetime.date(2021, 8, 26),
        end_date=datetime.date(2021, 9, 14),
    ),
    Country.SG: Phase3Period(
        start_date=datetime.date(2021, 8, 26),
        end_date=datetime.date(2021, 9, 14),
    ),
    Country.US: Phase3Period(
        start_date=datetime.date(2021, 8, 26),
        end_date=datetime.date(2021, 9, 14),
    ),
    Country.ZA: Phase3Period(
        start_date=datetime.date(2021, 8, 26),
        end_date=datetime.date(2021, 9, 14),
    ),
}

DEFAULT_PHASE_3_MIGRATION_PERIOD = Phase3Period(
    start_date=datetime.date(2021, 8, 19),
    end_date=datetime.date(2021, 8, 23),
)

if not LIVE_DEPLOYMENT:
    MIGRATION_PHASE_3_PERIOD = {
        Country.AR: Phase3Period(
            start_date=datetime.date(2021, 8, 26),
            end_date=datetime.date(2021, 9, 14),
        ),
        Country.AU: Phase3Period(
            start_date=datetime.date(2021, 8, 26),
            end_date=datetime.date(2021, 9, 14),
        ),
        Country.BR: Phase3Period(
            start_date=datetime.date(2021, 8, 26),
            end_date=datetime.date(2021, 9, 14),
        ),
        Country.CA: Phase3Period(
            start_date=datetime.date(2021, 8, 26),
            end_date=datetime.date(2021, 9, 14),
        ),
        Country.CL: Phase3Period(
            start_date=datetime.date(2021, 8, 26),
            end_date=datetime.date(2021, 9, 14),
        ),
        Country.CO: Phase3Period(
            start_date=datetime.date(2021, 8, 26),
            end_date=datetime.date(2021, 9, 14),
        ),
        Country.DE: Phase3Period(
            start_date=datetime.date(2021, 8, 26),
            end_date=datetime.date(2021, 9, 14),
        ),
        Country.ES: Phase3Period(
            start_date=datetime.date(2021, 8, 26),
            end_date=datetime.date(2021, 9, 14),
        ),
        Country.GB: Phase3Period(
            start_date=datetime.date(2021, 8, 26),
            end_date=datetime.date(2021, 9, 14),
        ),
        Country.IE: Phase3Period(
            start_date=datetime.date(2021, 8, 26),
            end_date=datetime.date(2021, 9, 14),
        ),
        Country.IN: Phase3Period(
            start_date=datetime.date(2021, 8, 26),
            end_date=datetime.date(2021, 9, 14),
        ),
        Country.IT: Phase3Period(
            start_date=datetime.date(2021, 8, 26),
            end_date=datetime.date(2021, 9, 14),
        ),
        Country.MX: Phase3Period(
            start_date=datetime.date(2021, 8, 26),
            end_date=datetime.date(2021, 9, 14),
        ),
        Country.MY: Phase3Period(
            start_date=datetime.date(2021, 8, 26),
            end_date=datetime.date(2021, 9, 14),
        ),
        Country.NG: Phase3Period(
            start_date=datetime.date(2021, 8, 26),
            end_date=datetime.date(2021, 9, 14),
        ),
        Country.NL: Phase3Period(
            start_date=datetime.date(2021, 8, 26),
            end_date=datetime.date(2021, 9, 14),
        ),
        Country.PL: Phase3Period(
            start_date=datetime.date(2021, 8, 26),
            end_date=datetime.date(2021, 9, 14),
        ),
        Country.PT: Phase3Period(
            start_date=datetime.date(2021, 8, 26),
            end_date=datetime.date(2021, 9, 14),
        ),
        Country.RU: Phase3Period(
            start_date=datetime.date(2021, 8, 26),
            end_date=datetime.date(2021, 9, 14),
        ),
        Country.SE: Phase3Period(
            start_date=datetime.date(2021, 8, 26),
            end_date=datetime.date(2021, 9, 14),
        ),
        Country.SG: Phase3Period(
            start_date=datetime.date(2021, 8, 26),
            end_date=datetime.date(2021, 9, 14),
        ),
        Country.US: Phase3Period(
            start_date=datetime.date(2021, 8, 26),
            end_date=datetime.date(2021, 9, 14),
        ),
        Country.ZA: Phase3Period(
            start_date=datetime.date(2021, 8, 26),
            end_date=datetime.date(2021, 9, 14),
        ),
    }
    DEFAULT_PHASE_3_MIGRATION_PERIOD = Phase3Period(
        start_date=datetime.date(2021, 8, 19),
        end_date=datetime.date(2021, 8, 23),
    )

# MEDICAL CONSENTS

MEDICAL_CONSENTS_COUNTRIES = {
    Country.PL,
}

SHOW_MEDICAL_CONSENTS = API_COUNTRY in MEDICAL_CONSENTS_COUNTRIES


# BUSINESS FILES STORAGE
AWS_BUSINESS_FILES_STORAGE = YAML_CONFIG.get(
    'business_files_bucket_name',
    'boo-enova-invoices-dev',
)
AWS_BUSINESS_FILES_LOCATION = YAML_CONFIG.get('business_files_location', '')
AWS_BUSINESS_FILES_REGION_NAME = YAML_CONFIG.get(
    'business_files_region_name',
    'us-east-1',
)
AWS_BUSINESS_FILES_SIGNATURE_VERSION = YAML_CONFIG.get(
    'business_files_signature_version',
    's3v4',
)
AWS_BUSINESS_FILES_ACCESS_KEY_ID = SecretsProvider.read_secret('BUSINESS_FILES_AWS_ACCESS_KEY_ID')
AWS_BUSINESS_FILES_SECRET_ACCESS_KEY = SecretsProvider.read_secret(
    'BUSINESS_FILES_AWS_ACCESS_KEY_SECRET'
)

QR_CODE_GCS_BUCKET_NAME = os.environ.get('QR_CODE_GCS_BUCKET_NAME')
QR_CODE_GCS_PROJECT_ID = os.environ.get('QR_CODE_GCS_PROJECT_ID')
QR_CODE_GCS_DOMAIN_NAME = os.environ.get('QR_CODE_GCS_DOMAIN_NAME')

INVOICE_DOWNLOAD_ENABLED = API_COUNTRY in COUNTRY__INVOICE_DOWNLOAD_ENABLED

# RwG
GOOGLE_BOOKING_API_AUTH = (
    SecretsProvider.read_secret('RESERVE_WITH_GOOGLE_USERNAME'),
    SecretsProvider.read_secret('RESERVE_WITH_GOOGLE_PASSWORD'),
)

############################
#         BILLING          #
############################

BILLING_CURRENCIES = COUNTRY__BILLING_CURRENCIES[API_COUNTRY]
BRAINTREE_PAYMENT_METHODS = BRAINTREE_PAYMENT_METHODS_BY_COUNTRY.get(
    API_COUNTRY, BRAINTREE_PAYMENT_METHODS_DEFAULT
)
BILLING_FIRST_N_SMS_LIMITS_NON_TRIAL = BILLING_FIRST_N_SMS_LIMITS_NON_TRIAL_PER_COUNTRY.get(
    API_COUNTRY, BILLING_FIRST_N_SMS_LIMITS_NON_TRIAL_DEFAULT
)

BILLING_SMS_HARD_LIMIT = 16_000

if API_COUNTRY not in COUNTRY__SMS_LIMITS_APPLY:
    BILLING_SMS_HARD_LIMIT = 0
    TOTAL_M_SMS_LIMITS = ALL_ZERO_LIMIT
    FIRST_N_SMS_LIMITS = ALL_ZERO_LIMIT

    SMS_DEMO_ACCOUNT_PREPAID_COUNT = 0
    SMS_PAID_ACCOUNT_PREPAID_COUNT = 0
    SMS_PAID_ACCOUNT_PREPAID_HISTORY = {
        '2015-01': 50,
        '2015-11': 500,
        '2022-11': SMS_PAID_ACCOUNT_PREPAID_COUNT,
    }

BILLING_MISMATCHED_BUSINESS_REPORT_RECIPIENTS = []
if LIVE_DEPLOYMENT:
    BILLING_MISMATCHED_BUSINESS_REPORT_RECIPIENTS = [
        '<EMAIL>',
    ]

SEND_WEEKLY_BILLING_MISMATCHED_BUSINESS_REPORT = API_COUNTRY in (
    Country.US,
    Country.PL,
    Country.ES,
    Country.GB,
    Country.IE,
    Country.ZA,
    Country.CA,
    Country.FR,
)

BILLING_ALLOW_AUTO_SUBSCRIPTION_CANCELLATION = (
    API_COUNTRY in BILLING_COUNTRIES_ALLOW_AUTO_SUBSCRIPTION_CANCELLATION
)


BILLING_ALLOW_CHANGE_CARD_NETWORK = API_COUNTRY in BILLING_COUNTRIES_ALLOW_CHANGE_CARD_NETWORK

DEFAULT_SUBSCRIPTION_DURATION = 1

BILLING_DEFAULT_AUTO_RETRY_CHARGE_DAYS = [3, 7, 11, 13]

BILLING_AUTO_RETRY_CHARGE_DAYS = BILLING_COUNTRIES_AUTO_RETRY_CHARGE_DAYS.get(
    API_COUNTRY, BILLING_DEFAULT_AUTO_RETRY_CHARGE_DAYS
)

BILLING_MIGRATED_SUBSCRIPTION_INITIAL_TASK = (
    API_COUNTRY in BILLING_COUNTRIES_MIGRATED_SUBSCRIPTION_INITIAL_TASK
)

# Stripe


@dataclass(frozen=True)
class StripeAppConfig:
    api_key: str  # private
    publishable_key: str  # public
    account_id: str
    api_version: str


BILLING_STRIPE_APP_CONFIG = StripeAppConfig(
    api_key=SecretsProvider.read_secret(
        'BILLING_STRIPE_APP_API_KEY',
        default_value='invalid-key-must-be-country-specific',
    ),
    publishable_key=SecretsProvider.read_secret(
        'BILLING_STRIPE_APP_PUBLISHABLE_KEY',
        default_value='invalid-publishable-key-must-be-country-specific',
    ),
    account_id=SecretsProvider.read_secret(
        'BILLING_STRIPE_APP_ACCOUNT_ID',
        default_value=None,
    ),
    api_version='2020-08-27',
)
BILLING_STRIPE_PAYMENT_METHODS = COUNTRY__BILLING_STRIPE_PAYMENT_METHODS.get(
    API_COUNTRY,
    BILLING_STRIPE_PAYMENT_METHODS_DEFAULT,
)
# End of BILLING

# STRIPE_APP
if not LIVE_DEPLOYMENT:
    STRIPE_APP_WEBHOOK_SECRET = SecretsProvider.read_secret(
        f'STRIPE_APP_WEBHOOK_SECRET_{API_COUNTRY}_{LEGACY_DEPLOYMENT_LEVEL}',
        default_value='invalid-webhook-secret-must-be-country-specific',
    )
else:
    STRIPE_APP_WEBHOOK_SECRET = SecretsProvider.read_secret(
        'STRIPE_APP_WEBHOOK_SECRET',
        default_value='invalid-webhook-secret-must-be-country-specific',
    )
STRIPE_APP_WEBHOOK_ALLOWED_IPS = (
    '**********',
    '*************',
    '*************',
    '**************',
    '*************',
    '**************',
    '************',
    '*************',
    '*************',
    '**************',
    '**************',
    '*************',
)
# End of STRIPE_APP

POPUP_NOTIFICATIONS_ENABLED = YAML_CONFIG.get(
    'popup_notifications_enabled', POPUP_NOTIFICATIONS_ENABLED
)
POPUP_PHASE2 = YAML_CONFIG.get('popup_phase2', POPUP_PHASE2)
POPUP_PHASE3 = YAML_CONFIG.get('popup_phase3', POPUP_PHASE3)
POPUP_PHASE4 = YAML_CONFIG.get('popup_phase4', POPUP_PHASE4)

if PYTEST:
    POPUP_NOTIFICATIONS_ENABLED = True
    POPUP_PHASE2 = True
    POPUP_PHASE3 = True
    POPUP_PHASE4 = True

BILLING_IN_SANDBOX_MODE = not LIVE_DEPLOYMENT

###########################
######## NAVISION #########
###########################


BOOKSY_SELLER_FR = {
    'entity name': 'KIUTE SAS',
    'tax_id': 'FR92799889704',
    'invoice_address': {
        'address_details1': '37 Rue Bergère',
        'zipcode': '75009',
        'city': 'Paris',
    },
    'country': 'France',
}

BOOKSY_SELLER_PL = {
    'entity_name': 'Booksy International sp. z o.o.',
    'tax_id': 'PL9512381607',
    'invoice_address': {
        'address_details1': 'ul. Prosta 67, piętro 28',
        'zipcode': '00-838',
        'city': 'Warszawa',
    },
    'country': 'Polska',
}
BOOKSY_SELLER_US = {
    'entity_name': 'Booksy Inc.',
    'tax_id': '37-1784710',
    'invoice_address': {
        'address_details1': '515 N State Street, Suite 460',
        'zipcode': '60654',
        'city': 'Chicago, IL',
    },
    'country': Country.US.label,
}
BOOKSY_SELLER_PER_COUNTRY = {
    Country.FR: BOOKSY_SELLER_FR,
    Country.PL: BOOKSY_SELLER_PL,
    Country.US: BOOKSY_SELLER_US,
}
BOOKSY_SELLER_INVOICE_DATA = BOOKSY_SELLER_PER_COUNTRY.get(API_COUNTRY, BOOKSY_SELLER_US)

# GOOGLE RECAPTCHA SITE KEYS
if os.environ.get('GOOGLE_PROJECT_ID') == 'bks-prd-workloads-1':
    # service mesh recaptcha setup
    RECAPTCHA_SITE_KEY_ANDROID = SecretsProvider.read_secret('RECAPTCHA_SITE_KEY_ANDROID_MESH')
    RECAPTCHA_SITE_KEY_IOS = SecretsProvider.read_secret('RECAPTCHA_SITE_KEY_IOS_MESH')
    RECAPTCHA_SITE_KEY_WEB = SecretsProvider.read_secret('RECAPTCHA_SITE_KEY_WEB_MESH')
else:
    RECAPTCHA_SITE_KEY_ANDROID = SecretsProvider.read_secret('RECAPTCHA_SITE_KEY_ANDROID')
    RECAPTCHA_SITE_KEY_IOS = SecretsProvider.read_secret('RECAPTCHA_SITE_KEY_IOS')
    RECAPTCHA_SITE_KEY_WEB = SecretsProvider.read_secret('RECAPTCHA_SITE_KEY_WEB')

# HCAPTCHA secrets
HCAPTCHA_API_URL = 'https://api.hcaptcha.com/siteverify'
HCAPTCHA_SECRET_KEY = SecretsProvider.read_secret('HCAPTCHA_SECRET_KEY')
HCAPTCHA_SITE_KEY_ANDROID = SecretsProvider.read_secret('HCAPTCHA_SITE_KEY_ANDROID')
HCAPTCHA_SITE_KEY_IOS = SecretsProvider.read_secret('HCAPTCHA_SITE_KEY_IOS')
HCAPTCHA_SITE_KEY_WEB = SecretsProvider.read_secret('HCAPTCHA_SITE_KEY_WEB')
HCAPTCHA_SITE_KEY_ANDROID_BUSINESS = SecretsProvider.read_secret(
    'HCAPTCHA_SITE_KEY_ANDROID_BUSINESS'
)
HCAPTCHA_SITE_KEY_IOS_BUSINESS = SecretsProvider.read_secret('HCAPTCHA_SITE_KEY_IOS_BUSINESS')
HCAPTCHA_SITE_KEY_WEB_BUSINESS = SecretsProvider.read_secret('HCAPTCHA_SITE_KEY_WEB_BUSINESS')

RECAPTCHA_ENABLED = False
RECAPTCHA_MIN_SCORE = 0.39

NOTEBOOK_ARGUMENTS = [
    "--no-browser",
    "--ip=0.0.0.0",
    "--port=8887",
    "--NotebookApp.password=''",
    "--NotebookApp.token=''",
]


SEO_BEST_REVIEWS_DAYS_AGO = YAML_CONFIG.get('SEO_BEST_REVIEWS_DAYS_AGO', 30)
SEO_CONTENT_CACHE_TIMEOUT = YAML_CONFIG.get('SEO_CONTENT_CACHE_TIMEOUT', 60 * 60 * 24)  # 1 day
if not LIVE_DEPLOYMENT:
    SEO_CONTENT_CACHE_TIMEOUT = 60
    SEO_BEST_REVIEWS_DAYS_AGO = 300

USER_JWT_DEV_KEY = 'qe26471e2gvrcxlp'

if LOCAL_DEPLOYMENT:
    USER_JWT_ALG = 'HS256'
else:
    USER_JWT_ALG = 'RS256'
    USER_RESET_JWT_PRIVATE_KEY = load_jwk_private_key(
        SecretsProvider.read_secret('USER_RESET_JWT_PRIVATE_KEY'),
    )
    USER_RESET_JWT_PUBLIC_KEY = load_jwk_public_key(
        SecretsProvider.read_secret('USER_RESET_JWT_PUBLIC_KEY'),
    )
    BOOKSY_AUTH_JWT_PUBLIC_KEY = SecretsProvider.read_secret('BOOKSY_AUTH_JWT_PUBLIC_KEY')

BOOKSY_STORAGE_TTL = 300
BOOKSY_STORAGE_DATA_MAX_LENGTH = 5_000_000

QUALAROO_CUS_API_KEY = SecretsProvider.read_secret('QUALAROO_CUS_API_KEY')
QUALAROO_BIZ_API_KEY = SecretsProvider.read_secret('QUALAROO_BIZ_API_KEY')


FRENCH_CERTIFICATION_RSA_PRIVATE_KEY = SecretsProvider.read_secret(
    'FRENCH_CERTIFICATION_RSA_PRIVATE_KEY'
)
FRENCH_CERTIFICATION_RSA_PUBLIC_KEY = SecretsProvider.read_secret(
    'FRENCH_CERTIFICATION_RSA_PUBLIC_KEY'
)

FISCAL_ARCHIVES_GCS_PROJECT_ID = os.environ.get('FISCAL_ARCHIVES_GCS_PROJECT_ID')
FISCAL_ARCHIVES_GCS_BUCKET_NAME = os.environ.get('FISCAL_ARCHIVES_GCS_BUCKET_NAME')
FISCAL_ARCHIVES_STORAGE_PROJECT_ID = SecretsProvider.read_secret(
    'FISCAL_ARCHIVES_STORAGE_PROJECT_ID'
)
FISCAL_ARCHIVES_STORAGE_PRIVATE_KEY_ID = SecretsProvider.read_secret(
    'FISCAL_ARCHIVES_STORAGE_PRIVATE_KEY_ID'
)
FISCAL_ARCHIVES_STORAGE_PRIVATE_KEY = SecretsProvider.read_secret(
    'FISCAL_ARCHIVES_STORAGE_PRIVATE_KEY'
)
FISCAL_ARCHIVES_STORAGE_CLIENT_EMAIL = SecretsProvider.read_secret(
    'FISCAL_ARCHIVES_STORAGE_CLIENT_EMAIL'
)
FISCAL_ARCHIVES_STORAGE_CLIENT_ID = SecretsProvider.read_secret('FISCAL_ARCHIVES_STORAGE_CLIENT_ID')
FISCAL_ARCHIVES_STORAGE_CLIENT_X509_CERT_URL = SecretsProvider.read_secret(
    'FISCAL_ARCHIVES_STORAGE_CLIENT_X509_CERT_URL'
)
API_VERSION = os.environ.get('API_VERSION', API_VERSION)

BOOST_CLAIM_SCREENSHOTS_GCS_BUCKET_NAME = os.environ.get('BOOST_CLAIM_SCREENSHOTS_GCS_BUCKET_NAME')


# LaunchDarkly
if LIVE_DEPLOYMENT or AUTOMATION_TESTS_ENV or UAT_ENV:
    LAUNCH_DARKLY_ENVIRONMENT = 'prod'
    LAUNCH_DARKLY_SDK_KEY_NAME = 'LIVE_LAUNCH_DARKLY_SDK_KEY'
else:
    LAUNCH_DARKLY_ENVIRONMENT = 'test'
    LAUNCH_DARKLY_SDK_KEY_NAME = 'DEV_LAUNCH_DARKLY_SDK_KEY'
LAUNCH_DARKLY_SDK_KEY = SecretsProvider.read_secret(LAUNCH_DARKLY_SDK_KEY_NAME)

_ld_uri = os.environ.get('LAUNCH_DARKLY_PROXY_URL', '')
LAUNCH_DARKLY_RELAY_PROXY_BASE_URI = _ld_uri
LAUNCH_DARKLY_RELAY_PROXY_EVENTS_URI = _ld_uri
LAUNCH_DARKLY_RELAY_PROXY_STREAM_URI = _ld_uri

LAUNCH_DARKLY_CLIENT_CONNECT_TIMEOUT = 1.0
LAUNCH_DARKLY_CLIENT_READ_TIMEOUT = 1.0

# Eppo
if LIVE_DEPLOYMENT or AUTOMATION_TESTS_ENV or UAT_ENV:
    EPPO_ENVIRONMENT = 'prod'
    EPPO_SDK_KEY_NAME = 'EPPO_PROD_KEY'
else:
    EPPO_ENVIRONMENT = 'test'
    EPPO_SDK_KEY_NAME = 'EPPO_TEST_KEY'
EPPO_SDK_KEY = SecretsProvider.read_secret(EPPO_SDK_KEY_NAME)

# Zowie chatbot
US_INSTANCE_ID = '********************************'
PL_INSTANCE_ID = '067b2bee28b34d69b2e0fa7bb82070de'
GB_INSTANCE_ID = '1964c9185e3e4697aa558c16d43f75d4'
ES_INSTANCE_ID = '28310765d94b4b1bad1a859ac1f90da0'
BR_INSTANCE_ID = '700c114183664441a71cfc871fc5ba87'
FR_INSTANCE_ID = 'c316eb28a5d243db942d156ff81e73cc'
COUNTRY_TO_INSTANCE_ID = {
    Country.US: US_INSTANCE_ID,
    Country.CA: US_INSTANCE_ID,
    Country.ZA: US_INSTANCE_ID,
    Country.IT: US_INSTANCE_ID,
    Country.NL: US_INSTANCE_ID,
    Country.AU: US_INSTANCE_ID,
    Country.PL: PL_INSTANCE_ID,
    Country.GB: GB_INSTANCE_ID,
    Country.IE: GB_INSTANCE_ID,
    Country.ES: ES_INSTANCE_ID,
    Country.PT: ES_INSTANCE_ID,
    Country.MX: ES_INSTANCE_ID,
    Country.CL: ES_INSTANCE_ID,
    Country.CO: ES_INSTANCE_ID,
    Country.AR: ES_INSTANCE_ID,
    Country.BR: BR_INSTANCE_ID,
    Country.FR: FR_INSTANCE_ID,
}

US_CHAT_HOST = 'booksy-us.chat.getzowie.com/api/v1/core'
PL_CHAT_HOST = 'booksy-pl.chat.getzowie.com/api/v1/core'
GB_CHAT_HOST = 'booksy-uk.chat.getzowie.com/api/v1/core'
ES_CHAT_HOST = 'booksy-es.chat.getzowie.com/api/v1/core'
BR_CHAT_HOST = 'booksy-br.chat.getzowie.com/api/v1/core'
FR_CHAT_HOST = 'booksy-fr.chat.getzowie.com/api/v1/core'

COUNTRY_TO_CHAT_HOST = {
    Country.US: US_CHAT_HOST,
    Country.CA: US_CHAT_HOST,
    Country.ZA: US_CHAT_HOST,
    Country.IT: US_CHAT_HOST,
    Country.NL: US_CHAT_HOST,
    Country.AU: US_CHAT_HOST,
    Country.PL: PL_CHAT_HOST,
    Country.GB: GB_CHAT_HOST,
    Country.IE: GB_CHAT_HOST,
    Country.ES: ES_CHAT_HOST,
    Country.PT: ES_CHAT_HOST,
    Country.MX: ES_CHAT_HOST,
    Country.CL: ES_CHAT_HOST,
    Country.CO: ES_CHAT_HOST,
    Country.AR: ES_CHAT_HOST,
    Country.BR: BR_CHAT_HOST,
    Country.FR: FR_CHAT_HOST,
}

ZOWIE_INSTANCE_ID = COUNTRY_TO_INSTANCE_ID.get(API_COUNTRY, US_INSTANCE_ID)
ZOWIE_TEST_INSTANCE_ID = '53a516a97f8942a0aef191a4eedd3f9a'

ZOWIE_CHAT_HOST = COUNTRY_TO_CHAT_HOST.get(API_COUNTRY, US_CHAT_HOST)
ZOWIE_TEST_CHAT_HOST = 'booksy-demo.chat.getzowie.com/api/v1/core'

# FACEBOOK FEEDS INTEGRATION
FB_APP_ID = SecretsProvider.read_secret('FB_APP_ID')
FB_VERIFICATION_TOKEN = SecretsProvider.read_secret('FB_VERIFICATION_TOKEN')
FB_APP_SECRET = SecretsProvider.read_secret('FB_APP_SECRET')


# GRUPON FEEDS INTEGRATION
GROUPON_API_KEY = SecretsProvider.read_secret('GROUPON_API_KEY')
PARTNER_NAME = SecretsProvider.read_secret('GROUPON_PARTNER_NAME')
CLIENT_ID = SecretsProvider.read_secret('GROUPON_CLIENT_ID')

# GRPC timeout
GRPC_API_TIMEOUT = 8
GRPC_CELERY_TIMEOUT = 20

if LOCAL_DEPLOYMENT:
    WEB_TOOLS_URL = 'http://localhost:3300'
else:
    if web_tools_url := os.environ.get('WEB_TOOLS_URL'):
        WEB_TOOLS_URL = web_tools_url
    else:
        WEB_TOOLS_URL = f'https://web-tools.{BOOKSY_DOMAIN}'

# FACEBOOK Conversion API
FB_CONVERSION_API_TOKEN = SecretsProvider.read_secret('FB_CONVERSION_API_TOKEN')

if not LIVE_DEPLOYMENT:
    FB_CONVERSION_API_DATASET_ID = '246292925943317'
else:
    FB_CONVERSION_API_DATASET_ID = '1960337004200964'


# region Datadog resource storage variables
DATADOG_RESOURCE_STORAGE_PROJECT_ID = 'bks-prd-tools-1-eu-w1'
DATADOG_RESOURCE_STORAGE_BUCKET_NAME = 'bks********************************components'
# endregion

# Calendar Importer
CALENDAR_IMPORTER_GC_PROJECT_ID = os.environ.get('CALENDAR_IMPORTER_GC_PROJECT_ID')

# Ecommerce
ECOMMERCE_PUBSUB_GC_PROJECT_ID = os.environ.get('ECOMMERCE_PUBSUB_GC_PROJECT_ID')

# Payments Service
PAYMENTS_SERVICE_GC_PROJECT_ID = os.environ.get(
    'PAYMENTS_SERVICE_GC_PROJECT_ID', f'bks-payments-service-{"prd" if LIVE_DEPLOYMENT else "dev"}'
)

# Logs obfuscation/encryption key
LOGS_ENCRYPTION_KEY = SecretsProvider.read_secret('LOGS_ENCRYPTION_KEY')
LOGS_ENCRYPTION_PUBLIC_IV = SecretsProvider.read_secret('LOGS_ENCRYPTION_PUBLIC_IV')

# FACEBOOK
FACEBOOK_CUSTOMER_CLIENT_ID = SecretsProvider.read_secret('FACEBOOK_CUSTOMER_CLIENT_ID')
FACEBOOK_BIZ_CLIENT_ID = SecretsProvider.read_secret('FACEBOOK_BIZ_CLIENT_ID')

# Booking
SCHEDULE_BULK_UPDATE_APPOINTMENTS_TIMEDELTA = 1  # IN MINUTES
SCHEDULE_BULK_UPDATE_APPOINTMENTS_MODULO = 2
TIMESLOT_REST_HOST = YAML_CONFIG.get('timeslot-rest-host') or 'timeslot-service-us-rest-api:3000'

# BOOKING REMINDERS
MAX_REMINDER_HOURS = 24 * 365
DEFAULT_REMINDER_HOURS = 24

PRIVATE_EMAIL_HASH_SALT = SecretsProvider.read_secret('PRIVATE_EMAIL_HASH_SALT')

DD_GIT_COMMIT_SHORT_SHA = os.environ.get('DD_GIT_COMMIT_SHORT_SHA')

BOOKSY_PAY_CASHBACK = BOOKSY_PAY_CASHBACK_PER_COUNTRY.get(API_COUNTRY, {})
BOOKSY_PAY_LATE_CANCELLATION_WINDOW_DEFAULT = {'hours': 8}


# FIREBASE & FIRESTORE CONFIGURATION
PROVIDER_CALENDAR_FIREBASE_PROJECT_NUMBER = os.environ.get(
    'PROVIDER_CALENDAR_FIREBASE_PROJECT_NUMBER'
)
if UAT_ENV:
    PROVIDER_CALENDAR_FIREBASE_JSON_SECRET_BASE64 = SecretsProvider.read_secret(
        'PROVIDER_CALENDAR_FIREBASE_JSON_SECRET-uat'
    )
elif LEGACY_DEPLOYMENT_LEVEL.endswith('t1'):
    PROVIDER_CALENDAR_FIREBASE_JSON_SECRET_BASE64 = SecretsProvider.read_secret(
        'PROVIDER_CALENDAR_FIREBASE_JSON_SECRET-t1'
    )
elif LEGACY_DEPLOYMENT_LEVEL.endswith('env'):
    PROVIDER_CALENDAR_FIREBASE_JSON_SECRET_BASE64 = SecretsProvider.read_secret(
        'PROVIDER_CALENDAR_FIREBASE_JSON_SECRET-env'
    )
else:
    PROVIDER_CALENDAR_FIREBASE_JSON_SECRET_BASE64 = SecretsProvider.read_secret(
        'PROVIDER_CALENDAR_FIREBASE_JSON_SECRET'
    )


# iterable integration
ITERABLE_JWT_API_KEY = SecretsProvider.read_secret('ITERABLE_JWT_API_KEY')
ITERABLE_JWT_SECRET_KEY = SecretsProvider.read_secret('ITERABLE_JWT_SECRET_KEY')

# branch.io keys
BRANCH_IO_KEY_BIZ_DEV = SecretsProvider.read_secret('BRANCH_IO_KEY_BIZ_DEV')
BRANCH_IO_KEY_CUS_DEV = SecretsProvider.read_secret('BRANCH_IO_KEY_CUS_DEV')
BRANCH_IO_KEY_SAL_DEV = SecretsProvider.read_secret('BRANCH_IO_KEY_SAL_DEV')
BRANCH_IO_KEY_BIZ_PRD = SecretsProvider.read_secret('BRANCH_IO_KEY_BIZ_PRD')
BRANCH_IO_KEY_CUS_PRD = SecretsProvider.read_secret('BRANCH_IO_KEY_CUS_PRD')
BRANCH_IO_KEY_SAL_PRD = SecretsProvider.read_secret('BRANCH_IO_KEY_SAL_PRD')
BRANCH_IO_SECRET_BIZ = SecretsProvider.read_secret('BRANCH_IO_SECRET_BIZ')
BRANCH_IO_SECRET_CUS = SecretsProvider.read_secret('BRANCH_IO_SECRET_CUS')
BRANCH_IO_SECRET_SAL = SecretsProvider.read_secret('BRANCH_IO_SECRET_SAL')

# for testing environments
EMBEDDED_MESSAGES_PLACEMENT_IDS = {'PAYMENTS_AND_CHECKOUT': 860, 'CALENDAR': 918}
if LIVE_DEPLOYMENT:
    # for production environments
    EMBEDDED_MESSAGES_PLACEMENT_IDS = {
        'PAYMENTS_AND_CHECKOUT': 924,
        'CALENDAR': 974,
    }

# url shorteners
URL_SHORTENER = 'booksy'
BOOKSY_SHORTENER_API_KEY = SecretsProvider.read_secret('BOOKSY_SHORTENER_API_KEY')
BOOKSY_SHORTENER_DOMAIN = 'http://booksy.info'

GCP_GEN_AI_PROJECT_ID = os.getenv("GCP_GEN_AI_PROJECT_ID")
if LOCAL_DEPLOYMENT:
    GCP_GEN_AI_PROJECT_ID = 'bks-genai-dev'

# Screenshot One
SCREENSHOT_ONE_ACCESS_KEY = SecretsProvider.read_secret('SCREENSHOT_ONE_ACCESS_KEY')

# stats and reports v2
STATS_AND_REPORTS_V2_GCP_FIRESTORE_PROJECT_ID = os.environ.get(
    'STATS_AND_REPORTS_V2_GCP_FIRESTORE_PROJECT_ID'
)
STATS_AND_REPORTS_V2_GCP_FIRESTORE_COLLECTION_NAME = os.environ.get(
    'STATS_AND_REPORTS_V2_GCP_FIRESTORE_COLLECTION_NAME'
)


GOOGLE_BUSINESS_PROFILE_FIREBASE_PROJECT_ID = os.environ.get(
    'GOOGLE_BUSINESS_PROFILE_FIREBASE_PROJECT_ID'
)
if LOCAL_DEPLOYMENT or _deployment_variant == 'test':
    GOOGLE_BUSINESS_PROFILE_FIREBASE_PROJECT_ID = 'bks-business-profile-dev'
GOOGLE_BUSINESS_PROFILE_COUNTRIES = (
    Country.AR,
    Country.AU,
    Country.BR,
    Country.CA,
    Country.CO,
    Country.ES,
    Country.FR,
    Country.GB,
    Country.IE,
    Country.MX,
    Country.PL,
    Country.US,
)

# The default of SOCIALACCOUNT_STORE_TOKENS was changed to `False` in django-allauth 0.48.0.
# We are setting it to `True` to maintain existing functionality.
SOCIALACCOUNT_STORE_TOKENS = True
