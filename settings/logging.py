import copy
import logging
import os

from django.conf import settings

DEBUG_SQL = settings.YAML_CONFIG.get('debug_sql', False)
LOGGING_USE_STDOUT = settings.YAML_CONFIG.get('logging-use-stdout', False)

# LOG TRUNCATION SETTINGS
TRUNCATE_LOGS = settings.YAML_CONFIG.get('truncate_logs', False)
MAX_STR_LOG_LENGTH = 1000
SIDE_STRING_LIMIT = MAX_STR_LOG_LENGTH // 2
MAX_LIST_LOG_LENGTH = 20
MAX_DICT_LOG_LENGTH = 100

STREAM_LOGGER_CLASS = 'logging.StreamHandler'
FILE_HANDLER_CLASS = 'lib.sensi.sensilogging.DailyRotatingFileHandler'

if settings.LOCAL_DEPLOYMENT:
    GENERAL_LOG_LEVEL = logging.INFO
else:
    GENERAL_LOG_LEVEL = logging.WARNING

LOGGER_CLASS = STREAM_LOGGER_CLASS

logging.captureWarnings(True)

LOGGING = {
    'version': 1,
    'disable_existing_loggers': True,
    'formatters': {
        'only_message': {
            'format': '%(message)s',
        },
        'json': {
            '()': 'lib.logging.formatter.BooksyJsonFormatter',
        },
        'json_pretty': {
            '()': 'pythonjsonlogger.jsonlogger.JsonFormatter',
            'json_indent': 4,
        },
    },
    'handlers': {
        'null': {
            'class': 'logging.NullHandler',
        },
        'gcp_logging': {
            'level': logging.DEBUG,
            'class': 'logging.StreamHandler',
            'formatter': 'json',
        },
        'django': {
            'level': logging.ERROR,
            'class': 'lib.sensi.sensilogging.DailyRotatingFileHandler',
            'filename': 'admin_errors.log',
            'formatter': 'json',
        },
        'booksy': {
            'class': 'lib.sensi.sensilogging.BooksyMasterHandler',
            'level': logging.DEBUG,
            'formatter': 'json',
        },
        'elasticsearch': {
            'level': logging.DEBUG,
            'class': 'lib.sensi.sensilogging.DailyRotatingFileHandler',
            'filename': 'elasticesearch.log',
            'formatter': 'json',
        },
        'elasticsearch.helpers': {
            'level': logging.DEBUG,
            'class': 'lib.sensi.sensilogging.DailyRotatingFileHandler',
            'filename': 'elasticesearch.log',
            'formatter': 'json',
        },
        'sqlfile': {
            'level': logging.DEBUG,
            'class': 'lib.sensi.sensilogging.DailyRotatingFileHandler',
            'filename': 'sql_debug.log',
            'formatter': 'json',
        },
        'console': {
            'level': logging.DEBUG,
            'class': 'logging.StreamHandler',
            'stream': 'ext://sys.stdout',
            'formatter': 'json' if settings.DD_AGENT_ENABLED else 'only_message',
        },
    },
    'loggers': {
        'tornado': {
            'handlers': ['console'],
            'level': logging.WARNING,
        },
        'tornado.access': {
            'handlers': ['null'],
            'propagate': False,
        },
        # silence segment main logger
        'segment': {
            'handlers': ['null'],
            'level': logging.WARNING,
            'propagate': False,
        },
        # django admin requests
        'django': {
            'handlers': ['django', 'console'],
            'level': GENERAL_LOG_LEVEL,
            'propagate': True,
        },
        'django.db.backends': {
            'handlers': [
                'django',
            ],
            'level': logging.INFO,
            'propagate': True,
        },
        # celery workers
        'celery': {
            'handlers': ['booksy', 'console'],
            'level': logging.INFO,
        },
        'celery_metric': {
            'handlers': ['gcp_logging'],
            'level': logging.INFO,
        },
        # booksy main logger for application loggers
        'booksy': {
            'handlers': ['booksy', 'console'],
            'level': logging.DEBUG,
            'propagate': True,
        },
        # logger excludedd from DataDog, used to pass data to BQ
        'booksy_experiments': {
            'handlers': ['gcp_logging'],
            'level': logging.INFO,
            'propagate': True,
        },
        'google_integration': {
            'handlers': ['gcp_logging'],
            'level': logging.INFO,
            'propagate': False,
        },
        'invoicing': {
            'handlers': ['gcp_logging'],
            'level': logging.INFO,
            'propagate': False,
        },
        'request': {
            'handlers': ['booksy', 'console'],
            'level': logging.DEBUG,
            'propagate': False,
        },
        'qrf_notification': {
            'handlers': ['null'],
            'level': logging.DEBUG,
            'propagate': False,
        },
        'payload_consumption': {
            'handlers': ['null'],
            'level': logging.DEBUG,
            'propagate': False,
        },
        'elasticsearch': {
            'handlers': ['elasticsearch'],
            'level': logging.DEBUG,
            'propagate': False,
        },
        'datadog': {
            'handlers': ['console'],
            'level': logging.INFO,
        },
        'ddtrace': {
            'handlers': ['console'],
            'level': logging.INFO,
        },
        'grpc_api': {
            'handlers': ['console'],
            'level': logging.INFO,
            'propagate': False,
        },
        'py.warnings': {
            'handlers': ['console'],
            'level': logging.WARNING,
        },
        'multiprocessing': {
            'handlers': ['console'],
            'level': logging.INFO,
            'propagate': False,
        },
        'grpc': {
            'handlers': ['console'],
            'level': logging.INFO,
            'propagate': False,
        },
        'aioapns': {
            'handlers': ['console'],
            'level': logging.INFO,
            'propagate': False,
        },
        'observability': {
            'handlers': ['console'],
            'level': logging.INFO,
            'propagate': False,
        },
        'booksy.feature_flag': {
            'handlers': ['booksy', 'console'],
            'level': logging.INFO,
            'propagate': True,
        },
    },
}

if LOGGING_USE_STDOUT:
    for name, log in LOGGING['loggers'].items():
        if name in (
            'celery_metric',
            'tornado',
            'tornado.access',
            'qrf_notification',
            'payload_consumption',
            'booksy_experiments',
            'google_integration',
            'invoicing',
        ):
            continue
        log['handlers'] = ['gcp_logging']
        log['level'] = logging.WARNING
        log['propagate'] = False
    # make an exception for log level
    LOGGING['loggers']['booksy.eppo'] = copy.deepcopy(LOGGING['loggers']['booksy'])
    LOGGING['loggers']['booksy.eppo']['level'] = logging.INFO
    LOGGING['loggers']['booksy.feature_flag'] = copy.deepcopy(LOGGING['loggers']['booksy'])
    LOGGING['loggers']['booksy.feature_flag']['level'] = logging.INFO
else:
    # update logging configuration with correct paths
    for handler in list(LOGGING['handlers'].values()):
        if 'filename' in handler:
            handler['filename'] = os.path.join(settings.LOG_DIRECTORY, handler['filename'])

###############################
##### POST YAML - LOGGING #####
###############################

if DEBUG_SQL:
    LOGGING['loggers']['django.db.backends'] = {
        'handlers': ['null', 'sqlfile', 'console'],
        'level': logging.DEBUG,
        'propagate': False,
    }
