from logging import getLogger
from django.db.models import F
from webapps.booking.models import Appointment
from webapps.business.enums import BusinessCategoryEnum
from webapps.business.models import BusinessCategory
from lib.cache import lru_booksy_cache
from lib.db import READ_ONLY_DB, using_db_for_reads
from lib.feature_flag.experiment.customer import S4UAfterBookingExperiment
from lib.feature_flag.adapter import UserData
from lib.feature_flag.enums import AppDomains, ClientApp, ExperimentVariants, SubjectType
from drf_api.service.customer.services.dto import SearchDataS4UAfterBookingDTO


logger = getLogger('booksy.s4u_service')


class S4UAfterBookingService:

    @property
    def _target_categories(self) -> list:
        return [
            BusinessCategoryEnum.HAIR_SALONS,
            BusinessCategoryEnum.BROWS_AND_LASHES,
            BusinessCategoryEnum.SKIN_CARE,
            BusinessCategoryEnum.MAKE_UP,
        ]

    @property
    @using_db_for_reads(READ_ONLY_DB)
    @lru_booksy_cache(timeout=60 * 5, skip_in_pytest=True)
    def _target_categories_ids(self) -> set:
        internal_names = [category.value for category in self._target_categories]
        return set(
            BusinessCategory.objects.filter(internal_name__in=internal_names).values_list(
                'id', flat=True
            )
        )

    @property
    @using_db_for_reads(READ_ONLY_DB)
    @lru_booksy_cache(timeout=60 * 5, skip_in_pytest=True)
    def _promoted_categories_ids(self) -> set:
        target_ids = self._target_categories_ids
        if (
            nails_id := BusinessCategory.objects.filter(
                internal_name=BusinessCategoryEnum.NAIL_SALONS.value
            )
            .values_list('id', flat=True)
            .first()
        ):
            return target_ids.union({nails_id})
        return target_ids

    @staticmethod
    @using_db_for_reads(READ_ONLY_DB)
    @lru_booksy_cache(timeout=60 * 5, skip_in_pytest=True)
    def _extract_appointment_data(appointment_id: int) -> dict | None:
        if appointment_data := list(
            Appointment.objects.filter(id=appointment_id)
            .annotate(
                business_latitude=F('business__latitude'),
                business_longitude=F('business__longitude'),
                treatment_parent_id=F('bookings__service_variant__service__treatment__parent_id'),
            )
            .values(
                'business_latitude',
                'business_longitude',
                'treatment_parent_id',
            )
        ):
            first_row = appointment_data[0]
            appointment_categories_ids = {
                row['treatment_parent_id'] for row in appointment_data if row['treatment_parent_id']
            }
            business_location_geo = (
                {'lat': first_row['business_latitude'], 'lon': first_row['business_longitude']}
                if first_row['business_latitude'] and first_row['business_longitude']
                else None
            )
            return {
                'appointment_categories_ids': appointment_categories_ids,
                'business_location_geo': business_location_geo,
            }
        return None

    def _appointment_categories_in_target_categories(self, appointment_categories_ids: set) -> bool:
        return bool(appointment_categories_ids.intersection(self._target_categories_ids))

    def get_search_data_from_appointment(
        self, appointment_id: int | None, user_id: str, app: ClientApp
    ) -> SearchDataS4UAfterBookingDTO | None:
        if not appointment_id:
            logger.warning("No appointment_id provided for user_id: %s", user_id)
            return None
        appointment_data = self._extract_appointment_data(appointment_id=appointment_id)
        if not appointment_data:
            logger.warning(
                "No appointment data found for appointment_id: %s, user_id: %s",
                appointment_id,
                user_id,
            )
            return None
        if (
            appointment_categories_ids := appointment_data.get('appointment_categories_ids')
        ) and self._appointment_categories_in_target_categories(appointment_categories_ids):
            variant = S4UAfterBookingExperiment(
                UserData(
                    subject_key=user_id,
                    subject_type=SubjectType.USER_ID,
                    is_experiment=True,
                    app=app,
                    app_domain=AppDomains.CUSTOMER,
                )
            )
            if variant == ExperimentVariants.VARIANT_A:
                search_categories = list(
                    self._promoted_categories_ids.difference(appointment_categories_ids)
                )
                return SearchDataS4UAfterBookingDTO(
                    search_categories=search_categories,
                    business_location_geo=appointment_data['business_location_geo'],
                )
        return None
