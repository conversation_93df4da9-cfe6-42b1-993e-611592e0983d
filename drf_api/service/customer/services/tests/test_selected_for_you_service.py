# pylint: disable=protected-access
from django.test import (
    TestCase,
)
from drf_api.service.customer.services.selected_for_you_service import S4UAfterBookingService
from webapps.business.baker_recipes import (
    business_recipe,
    category_recipe,
    service_recipe,
    service_variant_recipe,
    treatment_recipe,
)
from webapps.booking.baker_recipes import booking_recipe, appointment_recipe
from webapps.business.enums import BusinessCategoryEnum
from lib.feature_flag.enums import ClientApp, ExperimentVariants
from lib.feature_flag.experiment.customer import S4UAfterBookingExperiment
from lib.tests.utils import override_eppo_feature_flag


class S4UAfterBookingServiceTest(TestCase):

    def setUp(self):
        super().setUp()
        self.service = S4UAfterBookingService()
        self.target_business_categories = self._create_target_business_categories()
        self.hair_salons_category_id = self.target_business_categories[
            BusinessCategoryEnum.HAIR_SALONS
        ].id
        self.haircut_treatment = treatment_recipe.make(
            parent=self.target_business_categories[BusinessCategoryEnum.HAIR_SALONS],
            name='Haircut Treatment',
        )
        self.nails_category = category_recipe.make(
            internal_name=BusinessCategoryEnum.NAIL_SALONS.value
        )
        self.barbers = category_recipe.make(internal_name=BusinessCategoryEnum.BARBERS.value)
        self.manicure_treatment = treatment_recipe.make(
            parent=self.nails_category,
            name='Manicure Treatment',
        )
        self.business_no_location = business_recipe.make(latitude=None, longitude=None)
        self.business_with_location = business_recipe.make(latitude=52.232989, longitude=20.990810)

    @staticmethod
    def _create_target_business_categories():
        categories = [
            BusinessCategoryEnum.HAIR_SALONS,
            BusinessCategoryEnum.MAKE_UP,
            BusinessCategoryEnum.BROWS_AND_LASHES,
            BusinessCategoryEnum.SKIN_CARE,
        ]

        return {
            category: category_recipe.make(full_name=category.value, internal_name=category.value)
            for category in categories
        }

    @staticmethod
    def _make_service_variant(business, treatment):
        service = service_recipe.make(business=business, treatment=treatment)
        variant = service_variant_recipe.make(service=service)
        return variant

    @staticmethod
    def _make_booking(business, service_variants):
        appointment = appointment_recipe.make(business=business)
        for variant in service_variants:
            booking = booking_recipe.prepare(appointment=appointment, service_variant=variant)
            booking.save(override=True)
        return appointment

    def test_extract_appointment_data_business_no_location(self):
        variant = self._make_service_variant(self.business_no_location, self.haircut_treatment)
        appointment = self._make_booking(
            business=self.business_no_location, service_variants=[variant]
        )

        result = self.service._extract_appointment_data(appointment.id)
        self.assertEqual(
            result,
            {
                'appointment_categories_ids': {self.hair_salons_category_id},
                'business_location_geo': None,
            },
        )

    def test_extract_appointment_data_one_booking(self):
        variant = self._make_service_variant(self.business_with_location, self.haircut_treatment)
        appointment = self._make_booking(
            business=self.business_with_location, service_variants=[variant]
        )
        result = self.service._extract_appointment_data(appointment.id)
        self.assertEqual(
            result,
            {
                'appointment_categories_ids': {self.hair_salons_category_id},
                'business_location_geo': {
                    'lat': self.business_with_location.latitude,
                    'lon': self.business_with_location.longitude,
                },
            },
        )

    def test_extract_appointment_data_two_booking(self):
        haircut_variant = self._make_service_variant(
            business=self.business_with_location, treatment=self.haircut_treatment
        )
        nails_variant = self._make_service_variant(
            business=self.business_with_location, treatment=self.manicure_treatment
        )
        appointment = self._make_booking(
            business=self.business_with_location, service_variants=[haircut_variant, nails_variant]
        )
        result = self.service._extract_appointment_data(appointment.id)
        self.assertEqual(
            result,
            {
                'appointment_categories_ids': {
                    self.hair_salons_category_id,
                    self.nails_category.id,
                },
                'business_location_geo': {
                    'lat': self.business_with_location.latitude,
                    'lon': self.business_with_location.longitude,
                },
            },
        )

    def test_target_categories_ids(self):
        expected_categories_ids = {cat.id for cat in self.target_business_categories.values()}
        self.assertEqual(self.service._target_categories_ids, expected_categories_ids)

    def test_promoted_categories_ids(self):
        expected_categories_ids = {
            cat.id for cat in self.target_business_categories.values()
        }.union({self.nails_category.id})
        self.assertEqual(self.service._promoted_categories_ids, expected_categories_ids)

    def test_appointment_categories_in_target(self):
        appointment_categories = {self.hair_salons_category_id, self.barbers.id}
        self.assertEqual(
            self.service._appointment_categories_in_target_categories(appointment_categories), True
        )

        appointment_categories_2 = {self.nails_category.id, self.barbers.id}
        self.assertEqual(
            self.service._appointment_categories_in_target_categories(appointment_categories_2),
            False,
        )

    def test_get_search_data_from_appointment_no_appointment_id_returns_none(self):
        search_data = self.service.get_search_data_from_appointment(
            appointment_id=None, user_id="123", app=ClientApp.WEB_CUSTOMER_2019
        )
        self.assertIsNone(search_data)

    def test_get_search_data_from_appointment_no_such_appointment_returns_none(self):
        search_data = self.service.get_search_data_from_appointment(
            appointment_id=123, user_id="123", app=ClientApp.WEB_CUSTOMER_2019
        )
        self.assertIsNone(search_data)

    def test_get_search_data_from_appointment_appointment_not_in_target_returns_none(self):
        nails_variant = self._make_service_variant(
            business=self.business_with_location, treatment=self.manicure_treatment
        )
        appointment = self._make_booking(
            business=self.business_with_location, service_variants=[nails_variant]
        )
        search_data = self.service.get_search_data_from_appointment(
            appointment_id=appointment.id, user_id="123", app=ClientApp.WEB_CUSTOMER_2019
        )
        self.assertIsNone(search_data)

    @override_eppo_feature_flag({S4UAfterBookingExperiment.flag_name: ExperimentVariants.VARIANT_A})
    def test_get_search_data_from_appointment_appointment_in_target_variant_a_returns_data(self):
        hair_variant = self._make_service_variant(
            business=self.business_with_location, treatment=self.haircut_treatment
        )
        appointment = self._make_booking(
            business=self.business_with_location, service_variants=[hair_variant]
        )
        search_data = self.service.get_search_data_from_appointment(
            appointment_id=appointment.id, user_id="123", app=ClientApp.WEB_CUSTOMER_2019
        )
        expected_search_categories = [
            cat_id
            for cat_id in self.service._promoted_categories_ids
            if cat_id != self.hair_salons_category_id
        ]
        expected_location_geo = {
            'lat': self.business_with_location.latitude,
            'lon': self.business_with_location.longitude,
        }
        self.assertEqual(set(search_data.search_categories), set(expected_search_categories))
        self.assertEqual(search_data.business_location_geo, expected_location_geo)

    @override_eppo_feature_flag({S4UAfterBookingExperiment.flag_name: ExperimentVariants.CONTROL})
    def test_get_search_data_from_appointment_appointment_in_target_variant_control_returns_none(
        self,
    ):
        hair_variant = self._make_service_variant(
            business=self.business_with_location, treatment=self.haircut_treatment
        )
        appointment = self._make_booking(
            business=self.business_with_location, service_variants=[hair_variant]
        )
        search_data = self.service.get_search_data_from_appointment(
            appointment_id=appointment.id, user_id="123", app=ClientApp.WEB_CUSTOMER_2019
        )
        self.assertIsNone(search_data)
