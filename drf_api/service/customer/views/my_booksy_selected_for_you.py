import logging

from functools import lru_cache
from bo_obs.datadog.enums import BooksyTeams
from elasticsearch_dsl import AttrDict
from rest_framework import status
from rest_framework.exceptions import PermissionDenied
from rest_framework.response import Response

from drf_api.base_views import BaseBooksySessionGenericAPIView
from drf_api.permissions.authentication import OptionalLogin
from drf_api.service.customer.enums import SelectedForYouTriggerSource
from drf_api.service.customer.serializers import SelectedForYouRequestSerializer
from drf_api.service.customer.services.dto import SearchDataS4UAfterBookingDTO
from drf_api.service.customer.services.selected_for_you_service import S4UAfterBookingService
from lib.cache import lru_booksy_cache
from lib.db import READ_ONLY_DB, using_db_for_reads
from lib.elasticsearch.consts import ESDocType
from lib.feature_flag.adapter import UserData
from lib.feature_flag.consts import CUSTOMER_BOOKING_SOURCE_TO_CLIENT_APP
from lib.feature_flag.enums import AppDomains, <PERSON><PERSON><PERSON><PERSON>, ExperimentVariants, SubjectType
from lib.feature_flag.utils import get_client_app_from_request
from lib.feature_flag.experiment.customer import SelectedForYouV4RandomizedScoringExperiment
from lib.feature_flag.feature.customer import (
    CustomerRecommendedNewFlag,
    S4UAfterNegativeReviewFlag,
    S4UServiceVariantCountFixFlag,
    SimpleSerchableInRecommendedForYouFlag,
    UseLastBookingLocationInSelectedForYouFlag,
    S4UAfterBookingCreatedFlag,
    S4UDeeplinkNailsCampaignFlag,
)
from lib.searchables.common import IdsSearchable
from webapps.business.searchables.business.galleries.selected_for_you import (
    NegativeReviewSearchable,
    SelectedForYouSearchable,
    SelectedForYouSearchableV4,
    SelectedForYouSimpleSearchable,
)
from webapps.business.searchables.serializers import (
    BusinessDetailsHitSerializer,
    BusinessTreatmentHitSerializer,
)
from webapps.business.enums import BusinessCategoryEnum
from webapps.business.models import BusinessCategory
from webapps.reviews.models import Review
from webapps.structure.models import Region
from webapps.user.const import Gender

logger = logging.getLogger('booksy.recommended_new_px')


class CustomerMyBooksySelectedForYouView(  # nosemgrep: no-is-authenticated-permission-for-drf
    BaseBooksySessionGenericAPIView
):
    permission_classes = (OptionalLogin,)
    serializer_class = SelectedForYouRequestSerializer
    booksy_teams = (BooksyTeams.CUSTOMER_ONBOARDING,)

    @using_db_for_reads(READ_ONLY_DB)
    def get(self, request, *_, **kwargs):
        """
        Get recommended new providers from last 14 days<br>
        Fields *categories_ids* and *gender* are required for not authenticated users
        <br>
        *Example values*:<br>
        *   *gender=M*<br>
        *   *categories_ids=1,2,3*
        *   *location_geo=52.232989,20.990810*<br>
        *   *location_id=30692*<br>
        *   *review_id=3389* - Used for negative review trigger source to exclude
        reviewed business and find alternatives.<br> When combined with
        trigger_source=negative_review, search criteria are based on review data:<br>
        treatment_ids from review services, business location from reviewed business,
        and excludes the reviewed business<br>Temporaliry hidden behind
        Feature_S4UAfterNegativeReviewFlag.
        *   *appointment_id=30692* - used for booking_created trigger source to get data
        of an appointment which triggers s4u<br>
        """
        flag_data = self._get_flag_data()
        is_authenticated = bool(self.user.id)
        validated_data = self._get_validated_data(is_authenticated)
        trigger_source = validated_data.get('trigger_source')
        treatment_limit = flag_data['treatment_limit']
        appointment_search_data = None
        if trigger_source == SelectedForYouTriggerSource.BOOKING_CREATED:
            appointment_search_data = self._handle_booking_created_trigger(
                is_authenticated=is_authenticated,
                validated_data=validated_data,
            )
            if not appointment_search_data:
                return self._get_empty_response_s4u_after_booking()
            treatment_limit = 1  # Override treatment limit for booking created trigger
        nails_campaign_ff_on = False
        if trigger_source == SelectedForYouTriggerSource.NAILS_CAMPAIGN and not (
            nails_campaign_ff_on := S4UDeeplinkNailsCampaignFlag()
        ):
            return self._get_empty_response()
        data = self._prepare_search_data(
            data=validated_data,
            flag_data=flag_data,
            is_authenticated=is_authenticated,
            appointment_search_data=appointment_search_data,
            nails_campaign_ff_on=nails_campaign_ff_on,
        )
        if flag_data['is_required_location'] and not data.get('location_geo'):
            return self._get_empty_response()
        result_limit = flag_data['business_limit']
        min_business_count = flag_data['min_business_count']
        is_variant_randomized_scoring = self._is_experiment_randomized_scoring_variant(
            self.fingerprint,
            trigger_source,
            self.request.booking_source.name,
        )
        businesses = self._get_businesses(
            data,
            max(result_limit, min_business_count),
            treatment_limit,
            is_variant_randomized_scoring,
            trigger_source,
        )
        response = {
            'businesses': self._process_response(businesses, min_business_count, result_limit),
        }
        if appointment_search_data:
            response['is_variant_a'] = True
        return Response(response, status=status.HTTP_200_OK)

    @staticmethod
    def _process_response(businesses, min_business_count, result_limit):
        return [] if len(businesses) < min_business_count else businesses[:result_limit]

    def _prepare_search_data(  # pylint: disable=too-many-arguments, too-many-positional-arguments, too-many-branches
        self,
        data,
        flag_data,
        is_authenticated,
        appointment_search_data=None,
        nails_campaign_ff_on=False,
    ):
        result = {
            'min_treatment_count': flag_data['min_treatment_count'],
            'max_km_distance': flag_data['max_km_distance'],
            'max_active_days': flag_data['max_active_days'],
        }
        if location_id := data.get('location_id'):
            if coordinates := self._get_location_coordinates(location_id):
                result['location_geo'] = coordinates
        elif geo := data.get('location_geo'):
            result['location_geo'] = geo
        elif UseLastBookingLocationInSelectedForYouFlag() and (
            geo := self._get_last_booking_location()
        ):
            result['location_geo'] = geo
        trigger_source = data.get('trigger_source')
        if nails_campaign_ff_on and trigger_source == SelectedForYouTriggerSource.NAILS_CAMPAIGN:
            result['business_categories'] = self._get_search_category_id(
                category=BusinessCategoryEnum.NAIL_SALONS
            )
            result['gender'] = self._get_gender_for_search_data(
                is_authenticated=is_authenticated, data=data
            )
            return result
        if is_authenticated:
            result['gender'] = self.user.gender or Gender.Both
            qset = self.user.customerfavoritecategory_set.values_list('category_id', flat=True)
            result['business_categories'] = list(qset) or data['categories_ids']
        else:
            result['gender'] = data['gender']
            result['business_categories'] = data['categories_ids']
        if (
            trigger_source == SelectedForYouTriggerSource.NEGATIVE_REVIEW
            and S4UAfterNegativeReviewFlag()
        ):
            if review_id := data.get('review_id'):
                if review_data := self._extract_review_data(review_id):
                    result['review_treatment_ids'] = review_data['treatment_ids']
                    result['excluded'] = [review_data['business_id']]
                    if review_data['location_geo'] is not None:
                        result['location_geo'] = review_data['location_geo']
        if appointment_search_data:
            result['business_categories'] = appointment_search_data.search_categories
            if business_geo_location := appointment_search_data.business_location_geo:
                result['location_geo'] = business_geo_location
        return result

    @staticmethod
    def _get_flag_data():
        if not (flag_data := CustomerRecommendedNewFlag()):
            raise PermissionDenied(detail='Disabled view by feature flag')

        defaults = {
            'min_treatment_count': 1,
            'treatment_limit': 3,
            'business_limit': 5,
            'min_business_count': 0,
            'is_required_location': True,
            'max_km_distance': 50,
            'max_active_days': 300,
        }
        return defaults | flag_data

    def _get_businesses(
        self,
        search_data,
        result_limit,
        treatment_limit,
        is_variant_randomized_scoring,
        trigger_source,
    ):  # pylint: disable=too-many-arguments, too-many-positional-arguments
        if SimpleSerchableInRecommendedForYouFlag():
            context_data = {'data': {**search_data, 'treatment_limit': treatment_limit}}
            result = (
                SelectedForYouSimpleSearchable(
                    ESDocType.BUSINESS,
                    serializer=BusinessTreatmentHitSerializer(context=context_data),
                )
                .params(size=result_limit)
                .search(search_data)
                .execute()
            )
            if not result.hits:
                logger.warning(
                    'Empty response for seleted_4_you searchable in dedicated endpoint',
                    extra={'max_size': result_limit} | search_data,
                )
            return result

        if is_variant_randomized_scoring:
            result = (
                SelectedForYouSearchableV4(
                    ESDocType.BUSINESS, serializer=BusinessDetailsHitSerializer
                )
                .params(size=result_limit)
                .search(search_data)
                .execute()
            )
        elif (
            trigger_source == SelectedForYouTriggerSource.NEGATIVE_REVIEW
            and S4UAfterNegativeReviewFlag()
        ):
            result = (
                NegativeReviewSearchable(
                    ESDocType.BUSINESS, serializer=BusinessDetailsHitSerializer
                )
                .params(size=result_limit)
                .search(search_data)
                .execute()
            )
        else:
            result = (
                SelectedForYouSearchable(
                    ESDocType.BUSINESS, serializer=BusinessDetailsHitSerializer
                )
                .params(size=result_limit)
                .search(search_data)
                .execute()
            )

        gender = search_data['gender']
        docs = []
        categories_ids = set(search_data['business_categories'])
        for doc in result.hits:
            doc['top_services'] = doc['top_services'][gender]
            doc.treatment_services = self._get_treatment_services(
                categories_ids, doc.service_categories, treatment_limit
            )
            del doc.service_categories
            docs.append(doc)
        if not result.hits:
            logger.warning(
                'Empty response for seleted_4_you searchable',
                extra={'max_size': result_limit, 'trigger_source': trigger_source} | search_data,
            )
        return docs

    def _get_location_coordinates(self, location_id):
        qset = Region.objects.filter(id=location_id).values_list('latitude', 'longitude')
        if (coordinates := qset.last()) and all(coordinates):
            return {'lat': coordinates[0], 'lon': coordinates[1]}

    def _get_validated_data(self, is_authenticated):
        serializer = self.get_serializer(
            data=self.request.query_params.dict(),
            context={'is_authenticated': is_authenticated},
        )
        serializer.is_valid(raise_exception=True)
        return serializer.validated_data

    @staticmethod
    def _get_empty_response():
        return Response({'businesses': []}, status=status.HTTP_200_OK)

    @staticmethod
    def _get_empty_response_s4u_after_booking():
        return Response({'businesses': [], 'is_variant_a': False}, status=status.HTTP_200_OK)

    @staticmethod
    def _get_treatment_services(treatment_ids, service_categories, treatment_limit):
        service_with_matching_treatment = []
        service_without_matching_treatment = []
        for category in service_categories:
            for s in category.services:
                data = AttrDict({'category_name': category.name, **s.to_dict()})
                if 'treatment_id' in s and {s.treatment_id, s.treatment_parent_id} & treatment_ids:
                    service_with_matching_treatment.append(data)
                else:
                    service_without_matching_treatment.append(data)

        services = service_with_matching_treatment or service_without_matching_treatment
        services = services[:treatment_limit]
        if S4UServiceVariantCountFixFlag():
            left = treatment_limit - len(services)
            for service in services:
                service.variants = service.variants[: left + 1]
                left -= len(service.variants) - 1

        return services

    def _get_last_booking_location(self) -> dict:
        if user_id := self.user.id:
            if result := IdsSearchable(ESDocType.USER).params(size=1).execute({'ids': [user_id]}):
                return result[0].last_booking_location

    @staticmethod
    def _is_experiment_randomized_scoring_variant(fingerprint, trigger, booking_source):
        if trigger != SelectedForYouTriggerSource.ON_DEMAND_MY_BOOKSY:
            return False

        variant = SelectedForYouV4RandomizedScoringExperiment(
            UserData(
                subject_key=fingerprint,
                subject_type=SubjectType.FINGERPRINT,
                is_experiment=True,
                app=CUSTOMER_BOOKING_SOURCE_TO_CLIENT_APP.get(booking_source, ClientApp.DEFAULT),
                app_domain=AppDomains.CUSTOMER,
            )
        )
        return variant == ExperimentVariants.VARIANT_A

    @staticmethod
    @lru_booksy_cache(timeout=60 * 5, skip_in_pytest=True)
    def _extract_review_data(review_id):
        with using_db_for_reads(READ_ONLY_DB):
            if review := Review.objects.select_related('business').filter(id=review_id).first():
                treatment_ids = [
                    service['treatment_id']
                    for service in review.services
                    if service.get('treatment_id')
                ]

                location_geo = (
                    {'lat': review.business.latitude, 'lon': review.business.longitude}
                    if all([review.business.latitude, review.business.longitude])
                    else None
                )

                return {
                    'treatment_ids': treatment_ids,
                    'business_id': review.business_id,
                    'location_geo': location_geo,
                }
        return None

    def _handle_booking_created_trigger(
        self, is_authenticated: bool, validated_data: dict
    ) -> SearchDataS4UAfterBookingDTO | None:
        if not S4UAfterBookingCreatedFlag() or not is_authenticated:
            return None

        app = get_client_app_from_request(self.request)
        s4u_after_booking_service = S4UAfterBookingService()
        appointment_search_data = s4u_after_booking_service.get_search_data_from_appointment(
            appointment_id=validated_data.get('appointment_uid'),
            user_id=self.user.id,
            app=app,
        )

        return appointment_search_data

    def _get_gender_for_search_data(self, is_authenticated: bool, data: dict) -> Gender:
        return (is_authenticated and self.user.gender) or data.get('gender') or Gender.Both

    @staticmethod
    @lru_cache(maxsize=1)
    @using_db_for_reads(READ_ONLY_DB)
    def _get_search_category_id(category: BusinessCategoryEnum) -> list:
        category_id = (
            BusinessCategory.objects.filter(internal_name=category.value)
            .values_list('id', flat=True)
            .first()
        )
        return [category_id] if category_id else []
