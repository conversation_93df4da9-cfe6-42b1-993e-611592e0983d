from unittest.mock import patch

import mock
import pytest
from django.shortcuts import reverse
from django.test import override_settings
from model_bakery import baker
from parameterized import parameterized
from rest_framework import status
from rest_framework.test import APITestCase

from country_config import Country
from drf_api.service.customer.constants import HTML
from drf_api.service.tests.base import (
    AuthenticatedCustomerAPITestCase,
    NoAuthenticatedCustomerAPITestCase,
)
from lib.baker_utils import get_or_create_booking_source
from lib.feature_flag.enums import ExperimentVariants
from lib.feature_flag.experiment.customer import CategoriesOrderExperiment
from lib.feature_flag.feature.customer import (
    CategoriesOrderExperimentOnFlag,
    CategoriesViewUserGenderFlag,
    TranslateCategoriesNamesFlag,
)
from lib.test_utils import get_in_memory_img
from lib.tests.utils import override_eppo_feature_flag
from service.tests import dict_assert
from webapps.booking.models import BookingSources
from webapps.business.baker_recipes import business_recipe, category_recipe
from webapps.business.models import Business
from webapps.business.models.category import CategoryTranslation
from webapps.consts import WEB
from webapps.pos.models import POS
from webapps.user.baker_recipes import user_recipe
from webapps.user.const import Gender
from webapps.user.enums import AuthOriginEnum
from webapps.business.enums import BusinessCategoryEnum
from webapps.images.enums import BusinessCategoryPlaceholderType
from webapps.images.models import BusinessPlaceholderImage


class TestBusinessPaymentOptionsHistory(APITestCase):
    @classmethod
    def setUpTestData(cls):
        cls.booking_source = get_or_create_booking_source(
            app_type=BookingSources.BUSINESS_APP,
            name=WEB,
        )
        cls.customer_web_source = get_or_create_booking_source(
            app_type=BookingSources.CUSTOMER_APP,
            name=WEB,
        )

        cls.headers = {'HTTP_X_API_KEY': cls.customer_web_source.api_key}
        cls.user = user_recipe.make()

    def setUp(self):
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        self.client.credentials(HTTP_X_ACCESS_TOKEN=self.session.session_key)
        self.business = business_recipe.make()
        self.pos = baker.make(POS, business=self.business)

    def build_url(self, business_id=None):
        return reverse(
            'business_payment_options',
            args=[business_id or self.business.id],
        )

    def test_200(self):
        response = self.client.get(self.build_url(), **self.headers)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            response.json(),
            {
                'external_partners': {
                    'google_pay': False,
                    'apple_pay': False,
                    'blik': False,
                    'klarna': False,
                },
                'force_stripe_pba': False,
                'transaction_merchant_account': 'BooksyNET',
            },
        )

    @override_settings(
        POS__APPLE_PAY=True,
        POS__GOOGLE_PAY=True,
    )
    def test_200_google_pay_apple_pay_true_auto(self):
        response = self.client.get(self.build_url(), **self.headers)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        dict_assert(
            response.json(),
            {
                'external_partners': {
                    'google_pay': True,
                    'apple_pay': True,
                    'blik': False,
                },
                'force_stripe_pba': False,
            },
        )

    @override_settings(
        POS__APPLE_PAY=True,
        POS__GOOGLE_PAY=True,
    )
    def test_200_google_pay_apple_pay_true_semi_auto(self):
        self.business.booking_mode = Business.BookingMode.SEMIAUTO
        self.business.save()

        response = self.client.get(self.build_url(), **self.headers)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        dict_assert(
            response.json(),
            {
                'external_partners': {
                    'google_pay': True,
                    'apple_pay': True,
                    'klarna': False,
                },
                'force_stripe_pba': False,
            },
        )

    def test_permissions(self):
        response = self.client.get(
            self.build_url(), {'HTTP_X_API_KEY': self.booking_source.api_key}
        )
        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_404_no_business(self):
        response = self.client.get(
            self.build_url(business_id=self.business.id + 2 * 10), **self.headers
        )
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_404_no_appropriate_pos(self):
        other_business = business_recipe.make()
        assert other_business.pos is None

        response = self.client.get(self.build_url(business_id=other_business.id), **self.headers)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        baker.make(POS, business=other_business, active=False)

        response = self.client.get(self.build_url(business_id=other_business.id), **self.headers)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_retrieve_external_partners_data(self):
        with patch(
            target='webapps.pos.serializers.check_tokenized_payments_v2',
        ) as mocked_check_tokenized_payments:
            mocked_check_tokenized_payments.return_value = {}
            response = self.client.get(self.build_url(), **self.headers)
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            mocked_check_tokenized_payments.assert_called_with(
                appointment_data=None,
                is_cancelation=False,
                stripe_enabled=False,
                allow_blik=False,
                is_booksy_pay_available=None,
            )


@pytest.mark.django_db
class BusinessContactInfoViewTestCase(AuthenticatedCustomerAPITestCase):
    def setUp(self):
        super().setUp()
        self.business = business_recipe.make()
        self.business_with_public_email = business_recipe.make(
            public_email='<EMAIL>'
        )
        self.url = reverse('business_contact_info')

    def post(self, data):
        return self.client.post(self.url, data=data, format='json', **self.headers)

    def test_return_owners_email_when_authenticated(self):
        response = self.post({'business_id': self.business.id})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual({'email': self.business.owner.email}, response.json())

    def test_return_public_email_if_available(self):
        response = self.post({'business_id': self.business_with_public_email.id})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual({'email': self.business_with_public_email.public_email}, response.json())

    def test_return_owner_email_when_no_public_email(self):
        response = self.post({'business_id': self.business.id})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual({'email': self.business.owner.email}, response.json())

    def test_returns_404_on_incorrect_business_id(self):
        response = self.post({'business_id': 69})  # hehe

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(
            {
                'errors': [
                    {
                        'type': 'invalid',
                        'code': 'not_found',
                        'description': 'Requested object not found',
                    }
                ]
            },
            response.json(),
        )

    def test_returns_403_when_not_authenticated(self):
        self.user.delete_all_user_sessions()
        response = self.post({'business_id': self.business.id})

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertEqual(
            {
                'errors': [
                    {
                        'code': 'unauthorized',
                        'field': 'access_token',
                        'description': 'Your session has expired. Please log in once again.',
                    }
                ]
            },
            response.json(),
        )


@pytest.mark.django_db
class TestBooksyOmnibusConsentContentView(AuthenticatedCustomerAPITestCase):
    def setUp(self):
        super().setUp()
        self.url = reverse('omnibus_consent_content')

    def get(self, headers=None):
        return self.client.get(self.url, format='json', **self.headers)

    @override_settings(API_COUNTRY=Country.US)
    def test_returns_200_but_no_content_for_not_eu_logged_in_user(self):
        response = self.get()

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual({'title': None, 'html': None}, response.json())

    @parameterized.expand(
        [
            ('pl', '/pl-pl/p/terms', '<EMAIL>'),
            ('fr', '/fr-fr/p/terms', '<EMAIL>'),
            ('es', '/es-es/p/terms', '<EMAIL>'),
            ('pt', '/pt-pt/p/terms', '<EMAIL>'),
            ('ie', '/en-ie/p/terms', '<EMAIL>'),
            ('nl', '/en-nl/p/terms', '<EMAIL>'),
            ('it', '/en-it/p/terms', '<EMAIL>'),
        ]
    )
    def test_returns_content_for_eu_not_logged_in_user(self, country, country_terms, country_email):
        self.user.delete_all_user_sessions()
        with override_settings(API_COUNTRY=country):
            response = self.get()
        title = "Consumer information"
        html = HTML.format(country_email=country_email, country_terms=country_terms)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            {'title': title, 'html': html},
            response.json(),
        )

    @parameterized.expand(
        [
            ('pl', '/pl-pl/p/terms', '<EMAIL>'),
            ('fr', '/fr-fr/p/terms', '<EMAIL>'),
            ('es', '/es-es/p/terms', '<EMAIL>'),
            ('pt', '/pt-pt/p/terms', '<EMAIL>'),
            ('ie', '/en-ie/p/terms', '<EMAIL>'),
            ('nl', '/en-nl/p/terms', '<EMAIL>'),
            ('it', '/en-it/p/terms', '<EMAIL>'),
        ]
    )
    def test_returns_content_for_eu_logged_in_user(self, country, country_terms, country_email):
        with override_settings(API_COUNTRY=country):
            response = self.get()

        title = "Consumer information"
        html = HTML.format(country_email=country_email, country_terms=country_terms)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(
            {'title': title, 'html': html},
            response.json(),
        )

    @override_settings(API_COUNTRY=Country.US)
    def test_returns_200_but_no_content_for_not_eu_not_logged_in_user(self):
        self.user.delete_all_user_sessions()
        response = self.get()

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual({'title': None, 'html': None}, response.json())


class TestBusinessCategoriesBaseView:
    CATEGORY_NAME_TRANSLATION = {
        BusinessCategoryEnum.BARBERS: {'pl': 'Barber shop', 'es': 'Barberos'},
        BusinessCategoryEnum.HAIR_SALONS: {'pl': 'Fryzjer', 'es': 'Peluquería'},
        BusinessCategoryEnum.NAIL_SALONS: {'pl': 'Paznokcie', 'es': 'Salones de uñas'},
        BusinessCategoryEnum.MASSAGE: {'pl': 'Masaż'},  # no ES translation
        BusinessCategoryEnum.OTHER: {'pl': 'Inni', 'es': 'Otro'},
    }

    def setup_tests_common(self):
        self.icon_v2_name = 'icon_v2.jpg'
        self.url = reverse('customer_business_categories')
        self.categories = [
            category_recipe.make(
                name=BusinessCategoryEnum.BARBERS,
                internal_name=BusinessCategoryEnum.BARBERS,
                id=1,
                m_order=1,
                f_order=13,
                order=2,
            ),
            category_recipe.make(
                name=BusinessCategoryEnum.HAIR_SALONS,
                internal_name=BusinessCategoryEnum.HAIR_SALONS,
                id=2,
                m_order=2,
                f_order=1,
                order=1,
            ),
            category_recipe.make(
                name=BusinessCategoryEnum.NAIL_SALONS,
                internal_name=BusinessCategoryEnum.NAIL_SALONS,
                id=3,
                m_order=12,
                f_order=2,
                order=3,
            ),
            category_recipe.make(
                name=BusinessCategoryEnum.MASSAGE,
                internal_name=BusinessCategoryEnum.MASSAGE,
                id=4,
                m_order=4,
                f_order=4,
                order=4,
            ),
            category_recipe.make(
                name=BusinessCategoryEnum.OTHER,
                internal_name=BusinessCategoryEnum.OTHER,
                id=5,
                m_order=10,
                f_order=10,
                order=5,
            ),
        ]

        for category in self.categories:
            self.make_placeholder_image(
                category, self.icon_v2_name, BusinessCategoryPlaceholderType.ICON_V2
            )
            self.add_translation(category, language='pl')
            self.add_translation(category, language='es')
        self.default_f_order = [
            # sorted by f_order
            BusinessCategoryEnum.HAIR_SALONS,
            BusinessCategoryEnum.NAIL_SALONS,
            BusinessCategoryEnum.MASSAGE,
            BusinessCategoryEnum.BARBERS,
        ]
        self.default_m_order = [
            # sorted by m_order
            BusinessCategoryEnum.BARBERS,
            BusinessCategoryEnum.HAIR_SALONS,
            BusinessCategoryEnum.MASSAGE,
            BusinessCategoryEnum.NAIL_SALONS,
        ]
        self.default_order = [
            # sorted by order
            BusinessCategoryEnum.HAIR_SALONS,
            BusinessCategoryEnum.BARBERS,
            BusinessCategoryEnum.NAIL_SALONS,
            BusinessCategoryEnum.MASSAGE,
        ]

    @staticmethod
    def make_placeholder_image(category, name, type_):
        with mock.patch('django.core.files.storage.Storage.save', return_value=name):
            return baker.make(
                BusinessPlaceholderImage,
                image=get_in_memory_img(),
                category=category,
                type=type_,
            )

    def add_translation(self, category, language):
        if translation_available := self.CATEGORY_NAME_TRANSLATION.get(category.name):
            if translated_name := translation_available.get(language):
                return baker.make(
                    CategoryTranslation,
                    category=category,
                    language=language,
                    translated_name=translated_name,
                )


@override_eppo_feature_flag({CategoriesViewUserGenderFlag.flag_name: True})
class TestBusinessCategoriesViewNoAuth(
    TestBusinessCategoriesBaseView, NoAuthenticatedCustomerAPITestCase
):

    def setUp(self):
        super().setUp()
        self.setup_tests_common()

    def get(self, data=None):
        return self.client.get(self.url, format='json', data=data, **self.headers)

    def test_returns_all_categories_except_for_other(self):
        response = self.get()
        result_categories = response.json()["categories"]

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(4, len(result_categories))
        self.assertFalse(any(category["name"] == "Other" for category in result_categories))

    def test_categories_order_based_on_gender(self):
        response_f = self.get(data={'gender': 'F'})
        response_m = self.get(data={'gender': 'M'})
        response_gender_refused = self.get({'gender': 'R'})

        self.assertEqual(response_f.status_code, status.HTTP_200_OK)
        self.assertListEqual(
            self.default_f_order,
            [cat['name'] for cat in response_f.json()['categories']],
        )

        self.assertEqual(response_m.status_code, status.HTTP_200_OK)
        self.assertListEqual(
            self.default_m_order,
            [cat['name'] for cat in response_m.json()['categories']],
        )
        self.assertEqual(response_gender_refused.status_code, status.HTTP_200_OK)
        self.assertListEqual(
            self.default_order,
            [cat['name'] for cat in response_gender_refused.json()['categories']],
        )

    def test_categories_order_when_no_gender_provided(self):
        response_no_gender = self.get()

        self.assertEqual(response_no_gender.status_code, status.HTTP_200_OK)
        self.assertListEqual(
            self.default_order,
            [cat['name'] for cat in response_no_gender.json()['categories']],
        )

    def test_responds_with_icon_v2(self):
        response_icon_version_2 = self.get()

        self.assertEqual(response_icon_version_2.status_code, status.HTTP_200_OK)
        self.assertTrue(
            response_icon_version_2.json()['categories'][0]['icon'].endswith(self.icon_v2_name)
        )


@override_eppo_feature_flag({CategoriesViewUserGenderFlag.flag_name: True})
class TestBusinessCategoriesViewAuth(
    TestBusinessCategoriesBaseView, AuthenticatedCustomerAPITestCase
):

    def setUp(self):
        super().setUp()
        self.setup_tests_common()

    def get(self, data=None):
        return self.client.get(self.url, format='json', data=data, **self.headers)

    def test_logged_in_user_takes_user_gender(self):
        self.user.gender = Gender.Female
        self.user.save()

        response_no_gender_param = self.get()
        self.assertEqual(response_no_gender_param.status_code, status.HTTP_200_OK)
        self.assertListEqual(
            self.default_f_order,
            [cat['name'] for cat in response_no_gender_param.json()['categories']],
        )

    def test_logged_in_user_no_gender_uses_param(self):
        self.assertEqual(self.user.gender, None)

        response_f = self.get(data={'gender': 'F'})
        self.assertEqual(response_f.status_code, status.HTTP_200_OK)
        self.assertListEqual(
            self.default_f_order,
            [cat['name'] for cat in response_f.json()['categories']],
        )

    def test_categories_order_when_no_gender_provided(self):
        response_no_gender = self.get()

        self.assertEqual(response_no_gender.status_code, status.HTTP_200_OK)
        self.assertListEqual(
            self.default_order,
            [cat['name'] for cat in response_no_gender.json()['categories']],
        )


@override_settings(API_COUNTRY=Country.US)
@override_eppo_feature_flag(
    {
        CategoriesOrderExperimentOnFlag.flag_name: {
            'variant_a_position': 0,
            'variant_b_position': 2,
        }
    }
)
class TestBusinessCategoriesViewExperiment(TestBusinessCategoriesViewNoAuth):

    @override_eppo_feature_flag({CategoriesOrderExperimentOnFlag.flag_name: {}})
    def test_categories_order_experiment_flag_off_default_behaviour(self):
        response_m = self.get(data={'gender': 'M'})

        self.assertEqual(response_m.status_code, status.HTTP_200_OK)
        self.assertListEqual(
            self.default_m_order,
            [cat['name'] for cat in response_m.json()['categories']],
        )

    @override_settings(API_COUNTRY=Country.PL)
    def test_categories_order_experiment_not_us_default_behaviour(self):
        response_m = self.get(data={'gender': 'M'})

        self.assertEqual(response_m.status_code, status.HTTP_200_OK)
        self.assertListEqual(
            self.default_m_order,
            [cat['name'] for cat in response_m.json()['categories']],
        )

    def test_categories_order_experiment_gender_not_m_default_behaviour(self):
        response_f = self.get(data={'gender': 'F'})
        response_r = self.get({'gender': 'R'})

        self.assertEqual(response_f.status_code, status.HTTP_200_OK)
        self.assertListEqual(
            self.default_f_order,
            [cat['name'] for cat in response_f.json()['categories']],
        )

        self.assertEqual(response_r.status_code, status.HTTP_200_OK)
        self.assertListEqual(
            self.default_order,
            [cat['name'] for cat in response_r.json()['categories']],
        )

    @override_eppo_feature_flag({CategoriesOrderExperiment.flag_name: ExperimentVariants.VARIANT_A})
    def test_categories_order_experiment_variant_a(self):
        response_m = self.get(data={'gender': 'M'})

        self.assertEqual(response_m.status_code, status.HTTP_200_OK)
        self.assertListEqual(
            [
                # nails on position 0
                BusinessCategoryEnum.NAIL_SALONS,
                BusinessCategoryEnum.BARBERS,
                BusinessCategoryEnum.HAIR_SALONS,
                BusinessCategoryEnum.MASSAGE,
            ],
            [cat['name'] for cat in response_m.json()['categories']],
        )

    @override_eppo_feature_flag({CategoriesOrderExperiment.flag_name: ExperimentVariants.VARIANT_B})
    def test_categories_order_experiment_variant_b(self):
        response_m = self.get(data={'gender': 'M'})

        self.assertEqual(response_m.status_code, status.HTTP_200_OK)
        self.assertListEqual(
            [
                # nails on position 2
                BusinessCategoryEnum.BARBERS,
                BusinessCategoryEnum.HAIR_SALONS,
                BusinessCategoryEnum.NAIL_SALONS,
                BusinessCategoryEnum.MASSAGE,
            ],
            [cat['name'] for cat in response_m.json()['categories']],
        )

    @override_eppo_feature_flag({CategoriesOrderExperiment.flag_name: ExperimentVariants.CONTROL})
    def test_categories_order_experiment_control_group_default_behaviour(self):
        response_m = self.get(data={'gender': 'M'})

        self.assertEqual(response_m.status_code, status.HTTP_200_OK)
        self.assertListEqual(
            self.default_m_order,
            [cat['name'] for cat in response_m.json()['categories']],
        )

    @override_eppo_feature_flag(
        {
            CategoriesOrderExperiment.flag_name: ExperimentVariants.VARIANT_A,
            CategoriesOrderExperimentOnFlag.flag_name: {
                'variant_a_position': 'not_correct_value',
            },
        }
    )
    def test_categories_order_experiment_variant_incorrect_flag_value_default_position(self):
        response_m = self.get(data={'gender': 'M'})

        self.assertEqual(response_m.status_code, status.HTTP_200_OK)
        self.assertListEqual(
            self.default_m_order,
            [cat['name'] for cat in response_m.json()['categories']],
        )


@override_eppo_feature_flag({TranslateCategoriesNamesFlag.flag_name: True})
class TestBusinessCategoriesTranslatedName(TestBusinessCategoriesViewNoAuth):

    @override_settings(LANGUAGE_CODE='pl')
    def test_translation_available_returns_translated_name(self):
        response = self.get()
        result_categories = response.json()["categories"]

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(4, len(result_categories))
        self.assertEqual(
            result_categories[0]["name"],
            self.CATEGORY_NAME_TRANSLATION[self.default_order[0]]['pl'],
        )
        self.assertEqual(
            result_categories[1]["name"],
            self.CATEGORY_NAME_TRANSLATION[self.default_order[1]]['pl'],
        )
        self.assertEqual(
            result_categories[2]["name"],
            self.CATEGORY_NAME_TRANSLATION[self.default_order[2]]['pl'],
        )
        self.assertEqual(
            result_categories[3]["name"],
            self.CATEGORY_NAME_TRANSLATION[self.default_order[3]]['pl'],
        )

    @override_settings(LANGUAGE_CODE='es')
    def test_translation_if_no_translated_name_fallback_to_name(self):
        response = self.get()
        result_categories = response.json()["categories"]

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(4, len(result_categories))
        self.assertEqual(
            result_categories[0]["name"],
            self.CATEGORY_NAME_TRANSLATION[self.default_order[0]]['es'],
        )
        self.assertEqual(
            result_categories[1]["name"],
            self.CATEGORY_NAME_TRANSLATION[self.default_order[1]]['es'],
        )
        self.assertEqual(
            result_categories[2]["name"],
            self.CATEGORY_NAME_TRANSLATION[self.default_order[2]]['es'],
        )
        # there is no translation for MASSAGE in ES so the value is equal to name
        self.assertEqual(result_categories[3]["name"], self.categories[3].name)

    @override_settings(LANGUAGE_CODE='vi')
    def test_translation_no_translations_in_given_language_uses_name(self):
        response = self.get()
        result_categories = response.json()["categories"]

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(4, len(result_categories))
        self.assertListEqual(
            self.default_order,
            [cat['name'] for cat in result_categories],
        )


@override_eppo_feature_flag(
    {
        TranslateCategoriesNamesFlag.flag_name: True,
        CategoriesOrderExperiment.flag_name: ExperimentVariants.CONTROL,
        CategoriesOrderExperimentOnFlag.flag_name: {
            'variant_a_position': 0,
            'variant_b_position': 2,
        },
    }
)
class TestBusinessCategoriesTranslatedNameInExperiment(TestBusinessCategoriesTranslatedName):
    pass
