# pylint: disable=too-many-statements
from datetime import <PERSON><PERSON><PERSON>

import pytest
from django.shortcuts import reverse
from model_bakery import baker
from rest_framework import status

from conftest_helpers import clean_elastisearch_index_helper
from drf_api.lib.base_drf_test_case import CustomerAPITestCase
from drf_api.service.customer.enums import SelectedForYouTriggerSource
from drf_api.service.customer.views.my_booksy_selected_for_you import (
    CustomerMyBooksySelectedForYouView,
)
from lib.elasticsearch.consts import ESIndex
from lib.feature_flag.enums import ExperimentVariants
from lib.feature_flag.experiment.customer import (
    SelectedForYouV4RandomizedScoringExperiment,
    S4UAfterBookingExperiment,
)
from lib.feature_flag.feature.customer import (
    CustomerRecommendedNewFlag,
    S4UAfterNegativeReviewFlag,
    S4UAfterBookingCreatedFlag,
    SimpleSerchableInRecommendedForYouFlag,
    UseLastBookingLocationInSelectedForYouFlag,
    S4UDeeplinkNailsCampaignFlag,
)
from lib.test_utils import create_subbooking
from lib.tests.utils import override_eppo_feature_flag
from lib.tools import tznow
from webapps.business.baker_recipes import (
    business_recipe,
    category_recipe,
    service_category_recipe,
    service_recipe,
    service_variant_recipe,
    staffer_recipe,
    treatment_recipe,
)
from webapps.booking.baker_recipes import booking_recipe, appointment_recipe
from webapps.business.elasticsearch import BusinessDocument
from webapps.business.enums import BusinessCategoryEnum
from webapps.business.models import Business, Service
from webapps.elasticsearch.elastic import ELASTIC
from webapps.elasticsearch.tests.elasticsearch_test_helpers import ElasticSearchTestCaseMixin
from webapps.images.enums import ImageTypeEnum
from webapps.images.models import Image
from webapps.reviews.models import Review
from webapps.search_engine_tuning.models import UserTuning
from webapps.structure.models import Region
from webapps.user.baker_recipes import customer_user
from webapps.user.const import Gender
from webapps.user.models import CustomerFavoriteCategory


class BaseTestCustomerMyBooksySelectedForYouView(CustomerAPITestCase, ElasticSearchTestCaseMixin):
    def setUp(self):
        self.user = customer_user.make(email='<EMAIL>')
        self.url = reverse('customer_my_booksy_selected_for_you')
        clean_elastisearch_index_helper(ESIndex.BUSINESS)
        self.index = ELASTIC.indices[ESIndex.BUSINESS]
        self.bussiness_categories = self._create_business_categories()
        super().setUp()

    @staticmethod
    def _create_business_categories():
        categories = [
            BusinessCategoryEnum.BARBERS,
            BusinessCategoryEnum.HAIR_SALONS,
            BusinessCategoryEnum.PIERCING_CATEGORY,
            BusinessCategoryEnum.DENTAL,
            BusinessCategoryEnum.NAIL_SALONS,
            BusinessCategoryEnum.MAKE_UP,
            BusinessCategoryEnum.BROWS_AND_LASHES,
            BusinessCategoryEnum.SKIN_CARE,
        ]

        return {
            category: category_recipe.make(full_name=category.value, internal_name=category.value)
            for category in categories
        }

    def get(self, data):
        for key, value in data.items():
            if isinstance(value, list):
                data[key] = ','.join(str(v) for v in value)
        return self.client.get(self.url, data=data)

    def _create_business(
        self,
        active_x_days,
        has_cover_photo,
        categories_names,
        services_count,
        location_geo,
        promoted=False,
        number_of_portfolio_photos=0,
    ):
        latitude, longitude = location_geo or (None, None)
        business = business_recipe.make(
            active_from=active_x_days and tznow() - timedelta(days=active_x_days),
            latitude=latitude,
            longitude=longitude,
            boost_status=(
                Business.BoostStatus.ENABLED if promoted else Business.BoostStatus.DISABLED
            ),
        )
        if location_geo:
            Region.objects.filter(id=business.region_id).update(
                latitude=latitude + 0.05,  # move region 3.5km from business coordinates
                longitude=longitude + 0.05,
            )
        baker.make(
            Image,
            image_url='http://image.url/image.jpg',
            business=business,
            is_cover_photo=has_cover_photo,
        )
        categories = [
            self.bussiness_categories[category_name] for category_name in categories_names
        ]
        business.categories.add(*categories)
        if number_of_portfolio_photos:
            baker.make(
                Image,
                business=business,
                is_cover_photo=False,
                category=ImageTypeEnum.INSPIRATION,
                _quantity=number_of_portfolio_photos,
            )
        business.save()
        assert (
            business.boosted == promoted
        ), "The logic in the save method can overwrite boost_status"
        service_category = service_category_recipe.make(business=business)
        staffer = staffer_recipe.make(business=business)
        for i in range(services_count):
            category = categories[i % len(categories_names)]
            treatment = treatment_recipe.make(parent=category, name=f'T-{category.internal_name}')
            service = service_recipe.make(
                business=business,
                name=f'Service-{category.internal_name}',
                note=f'test note {i}',
                description=f'test description {i}',
                service_category=service_category,
                treatment=treatment,
            )
            service_variant_recipe.make(service=service, _quantity=i * 2 + 1)
            service.add_staffers([staffer])
        business_document = business.get_document()
        business_document.save()
        return business

    def _remove_login_token(self):
        self.client.credentials(HTTP_X_API_KEY=self.customer_booking_src.api_key)


@pytest.mark.django_db
class TestCustomerMyBooksySelectedForYouView(BaseTestCustomerMyBooksySelectedForYouView):

    @override_eppo_feature_flag({CustomerRecommendedNewFlag.flag_name: {}})
    def test_get_disable_ff(self):
        response = self.get({})

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertEqual(response.json(), {'detail': 'Disabled view by feature flag'})

    @override_eppo_feature_flag(
        {
            CustomerRecommendedNewFlag.flag_name: {
                'is_required_location': True,
                'min_business_count': 1,
                'business_limit': 1,
            }
        }
    )
    def test_get_not_logged_in(self):
        self._remove_login_token()
        categories_names = [
            BusinessCategoryEnum.HAIR_SALONS,
            BusinessCategoryEnum.PIERCING_CATEGORY,
            BusinessCategoryEnum.BARBERS,
        ]
        services_names = [f'Service-{c}' for c in categories_names]
        business_0 = self._create_business(
            active_x_days=2,
            has_cover_photo=True,
            categories_names=categories_names,
            services_count=3,
            location_geo=[52.232989, 21.032610],
        )
        _business_1_to_old = self._create_business(
            active_x_days=50,
            has_cover_photo=True,
            categories_names=categories_names,
            services_count=3,
            location_geo=[52.232989, 20.990810],
            promoted=True,
        )
        _business_2_too_low_services = self._create_business(
            active_x_days=50,
            has_cover_photo=True,
            categories_names=categories_names,
            services_count=1,
            location_geo=[52.232989, 20.990810],
        )
        _business_3_wrong_categories = self._create_business(
            active_x_days=1,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.DENTAL],
            services_count=3,
            location_geo=[52.232989, 20.990810],
        )
        _business_4_no_cover = self._create_business(
            active_x_days=1,
            has_cover_photo=False,
            categories_names=categories_names,
            services_count=3,
            location_geo=[52.232989, 20.990810],
        )
        _business_5_too_far_away = business_by_region = self._create_business(
            active_x_days=1,
            has_cover_photo=True,
            categories_names=categories_names,
            services_count=3,
            location_geo=[51.792989, 20.990810],  # 49 km from [51.513989, 20.990810]
            promoted=True,
        )
        _business_6_no_start_day = self._create_business(
            active_x_days=None,
            has_cover_photo=True,
            categories_names=categories_names,
            services_count=3,
            location_geo=[52.232989, 20.990810],
            promoted=True,
        )
        _business_7_no_location = self._create_business(
            active_x_days=1,
            has_cover_photo=True,
            categories_names=categories_names,
            services_count=3,
            location_geo=None,
            promoted=True,
        )
        self.index.refresh()
        categories_ids = [self.bussiness_categories[name].id for name in categories_names]

        data = {
            'location_geo': [52.232989, 20.990810],
            'gender': Gender.Male,
            'categories_ids': categories_ids[:2],
            'location_id': '',
        }

        with override_eppo_feature_flag(
            {
                CustomerRecommendedNewFlag.flag_name: {
                    'is_required_location': True,
                    'min_business_count': 1,
                    'business_limit': 1,
                    'treatment_limit': 3,
                }
            }
        ):
            response = self.get(data=data)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())
        businesses = response.json()['businesses']
        self.assertEqual(len(businesses), 1)
        self.assertEqual(businesses[0]['id'], business_0.id)
        self.assertEqual(
            {s['name'] for s in businesses[0]['treatment_services']}, set(services_names[:2])
        )
        self.assertEqual(sum(len(s['variants']) for s in businesses[0]['treatment_services']), 3)
        self.assertEqual(len(businesses[0]['images']['cover']), 1)
        self.assertEqual(businesses[0]['images']['cover'][0]['image'], 'http://image.url/image.jpg')

        data = {
            'location_id': business_by_region.region_id,
            'gender': Gender.Male,
            'categories_ids': categories_ids[:2],
        }

        with override_eppo_feature_flag(
            {
                CustomerRecommendedNewFlag.flag_name: {
                    'is_required_location': True,
                    'min_business_count': 1,
                    'business_limit': 1,
                }
            }
        ):
            response = self.get(data=data)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())
        businesses = response.json()['businesses']
        self.assertEqual(len(businesses), 1)
        self.assertEqual({b['id'] for b in businesses}, {business_by_region.id})
        self.assertEqual(
            {s['name'] for s in businesses[0]['treatment_services']}, set(services_names[:2])
        )
        self.assertEqual(len(businesses[0]['images']['cover']), 1)
        self.assertEqual(businesses[0]['images']['cover'][0]['image'], 'http://image.url/image.jpg')

        with override_eppo_feature_flag(
            {
                CustomerRecommendedNewFlag.flag_name: {
                    'is_required_location': True,
                    'min_business_count': 2,
                    'business_limit': 2,
                    'treatment_limit': 2,
                }
            }
        ):
            response = self.get(data=data)

        businesses = response.json()['businesses']
        self.assertEqual(len(businesses), 2)
        self.assertEqual(businesses[0]['id'], business_by_region.id)
        self.assertEqual(businesses[1]['id'], business_0.id)
        self.assertEqual(sum(len(s['variants']) for s in businesses[0]['treatment_services']), 2)
        self.assertEqual(sum(len(s['variants']) for s in businesses[1]['treatment_services']), 2)
        # smaller distance
        with override_eppo_feature_flag(
            {
                CustomerRecommendedNewFlag.flag_name: {
                    'is_required_location': True,
                    'min_business_count': 1,
                    'business_limit': 2,
                    'max_km_distance': 10,
                }
            }
        ):
            response = self.get(data=data)

        businesses = response.json()['businesses']
        self.assertEqual(len(businesses), 1)
        self.assertEqual(businesses[0]['id'], business_by_region.id)

        # only for today created business
        with override_eppo_feature_flag(
            {
                CustomerRecommendedNewFlag.flag_name: {
                    'is_required_location': True,
                    'min_business_count': 1,
                    'max_active_days': 0,
                }
            }
        ):
            response = self.get(data=data)

        businesses = response.json()['businesses']
        self.assertEqual(len(businesses), 0)

        with override_eppo_feature_flag(
            {
                CustomerRecommendedNewFlag.flag_name: {
                    'is_required_location': True,
                    'min_business_count': 6,  # more than we have
                    'business_limit': 3,
                }
            }
        ):
            response = self.get(data=data)

        self.assertEqual(len(response.json()['businesses']), 0)

    @override_eppo_feature_flag({CustomerRecommendedNewFlag.flag_name: {'business_limit': 1}})
    def test_get_logged_in(self):
        categories_names = [
            BusinessCategoryEnum.HAIR_SALONS,
            BusinessCategoryEnum.PIERCING_CATEGORY,
            BusinessCategoryEnum.BARBERS,
        ]
        business_0 = self._create_business(
            active_x_days=1,
            has_cover_photo=True,
            categories_names=categories_names,
            services_count=3,
            location_geo=[52.232989, 20.990810],
        )
        CustomerFavoriteCategory.objects.create(
            user=self.authorized_user,
            category=self.bussiness_categories[BusinessCategoryEnum.HAIR_SALONS],
        )
        self.index.refresh()
        categories_ids = [self.bussiness_categories[name].id for name in categories_names]

        data = {
            'location_geo': [52.232989, 20.990810],
            'location_id': '',
            'gender': Gender.Male,
            'categories_ids': categories_ids,
        }

        response = self.get(data=data)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())
        response_data = response.json()
        businesses = response_data['businesses']
        ids = {b['id'] for b in businesses}
        self.assertEqual(len(businesses), 1, ids)
        self.assertEqual(ids, {business_0.id})
        self.assertEqual(len(businesses[0]['treatment_services']), 1)
        self.assertEqual(
            businesses[0]['treatment_services'][0]['name'],
            f'Service-{BusinessCategoryEnum.HAIR_SALONS}',
        )

        self.user.customerfavoritecategory_set.all().delete()

        response = self.get(data=data)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())
        response_data = response.json()
        businesses = response_data['businesses']
        ids = {b['id'] for b in businesses}
        self.assertEqual(len(businesses), 1, ids)

    @staticmethod
    def _create_appointment_with_location(latitude, longitude, user):
        business_with_location = baker.make(
            'business.Business',
            latitude=latitude,
            longitude=longitude,
        )
        create_subbooking(
            business=business_with_location,
            booking_kws=dict(
                booked_for__user=user,
                updated_by=user,
                _save_related=True,
            ),
        )
        UserTuning.update_multiple_tunings([user.id])
        user.get_document().save()
        ELASTIC.indices[ESIndex.USER].refresh()

    @override_eppo_feature_flag({CustomerRecommendedNewFlag.flag_name: {'business_limit': 1}})
    def test_get_logged_in_get_last_booking_location(self):
        categories_names = [
            BusinessCategoryEnum.HAIR_SALONS,
            BusinessCategoryEnum.PIERCING_CATEGORY,
            BusinessCategoryEnum.BARBERS,
        ]
        self._create_business(
            active_x_days=1,
            has_cover_photo=True,
            categories_names=categories_names,
            services_count=3,
            location_geo=[52.232989, 20.990810],
        )
        CustomerFavoriteCategory.objects.create(
            user=self.authorized_user,
            category=self.bussiness_categories[BusinessCategoryEnum.HAIR_SALONS],
        )
        self.index.refresh()
        self._create_appointment_with_location(52, 21, self.user)

        data = {
            'location_geo': '',
            'location_id': '',
            'gender': Gender.Male,
            'categories_ids': [self.bussiness_categories[name].id for name in categories_names],
        }

        response = self.get(data=data)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())
        self.assertEqual(response.json()['businesses'], [])

    @override_eppo_feature_flag(
        {CustomerRecommendedNewFlag.flag_name: {'business_limit': 1, 'min_treatment_count': 1}}
    )
    def test_get_logged_in_no_matching_favorite_service(self):
        categories_names = [
            BusinessCategoryEnum.HAIR_SALONS,
            BusinessCategoryEnum.PIERCING_CATEGORY,
            BusinessCategoryEnum.BARBERS,
        ]
        business_0 = self._create_business(
            active_x_days=1,
            has_cover_photo=True,
            categories_names=categories_names,
            services_count=2,
            location_geo=[52.232989, 20.990810],
        )
        CustomerFavoriteCategory.objects.create(
            user=self.authorized_user,
            category=self.bussiness_categories[BusinessCategoryEnum.BARBERS],
        )
        self.index.refresh()
        categories_ids = [self.bussiness_categories[name].id for name in categories_names]

        data = {
            'location_geo': [52.232989, 20.990810],
            'gender': Gender.Male,
            'categories_ids': categories_ids,
        }

        response = self.get(data=data)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())
        response_data = response.json()
        businesses = response_data['businesses']
        ids = {b['id'] for b in businesses}
        self.assertEqual(len(businesses), 1, ids)
        self.assertEqual(ids, {business_0.id})
        # no matching favorite services so return other services
        self.assertEqual(len(businesses[0]['treatment_services']), 2)
        self.assertEqual(
            {s['name'] for s in businesses[0]['treatment_services']},
            {f'Service-{c}' for c in categories_names[:2]},
        )

    @override_eppo_feature_flag({CustomerRecommendedNewFlag.flag_name: {'business_limit': 1}})
    def test_get_with_user_empty(self):
        data = {
            'location_geo': [52.232989, 20.990810],
            'limit': 10,
        }

        response = self.get(data=data)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())
        self.assertEqual(response.json(), {'businesses': []})

        data['categories_ids'] = []
        response = self.get(data=data)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())
        self.assertEqual(response.json(), {'businesses': []})

        data['categories_ids'] = ''
        response = self.get(data=data)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())
        self.assertEqual(response.json(), {'businesses': []})

    def _assert_response(self, data: dict, is_empty: bool):
        response = self.get(data=data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.json()['businesses']), 0 if is_empty else 1)

    @override_eppo_feature_flag({CustomerRecommendedNewFlag.flag_name: {'business_limit': 1}})
    def test_get_required_location(self):
        self._create_business(
            active_x_days=1,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.BARBERS],
            services_count=2,
            location_geo=[52.232989, 20.990810],
        )
        CustomerFavoriteCategory.objects.create(
            user=self.authorized_user,
            category=self.bussiness_categories[BusinessCategoryEnum.BARBERS],
        )
        self.index.refresh()
        flag_data = {
            'is_required_location': True,
            'min_business_count': 1,
        }
        flags = {CustomerRecommendedNewFlag.flag_name: flag_data}

        with override_eppo_feature_flag(flags):
            self._assert_response(data={}, is_empty=True)

        flag_data['is_required_location'] = False
        with override_eppo_feature_flag(flags):
            self._assert_response(data={}, is_empty=False)

        self._remove_login_token()
        data = {
            'gender': Gender.Female,
            'categories_ids': [self.bussiness_categories[BusinessCategoryEnum.BARBERS].id],
        }
        flag_data['is_required_location'] = True
        with override_eppo_feature_flag(flags):
            self._assert_response(data=data, is_empty=True)

        flag_data['is_required_location'] = False
        with override_eppo_feature_flag(flags):
            self._assert_response(data=data, is_empty=False)

    @override_eppo_feature_flag({CustomerRecommendedNewFlag.flag_name: {'business_limit': 1}})
    def test_get_invalid_without_user(self):
        self._remove_login_token()
        data = {
            'location_geo': [52.232989, 20.990810],
        }

        response = self.get(data=data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.json(),
            {
                'errors': [
                    {
                        'field': 'gender',
                        'description': 'This field is required.',
                        'code': 'required',
                    },
                    {
                        'field': 'categories_ids',
                        'description': 'This field is required.',
                        'code': 'required',
                    },
                ],
            },
        )

        data = {
            'location_geo': [52.232989, 20.990810],
            'gender': 'invalid',
            'categories_ids': ['a'],
            'limit': 20,
        }

        response = self.get(data=data)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            response.json(),
            {
                'errors': [
                    {
                        'field': 'gender',
                        'description': '"invalid" is not a valid choice.',
                        'code': 'invalid_choice',
                    },
                    {
                        'field': 'categories_ids.0',
                        'description': 'A valid integer is required.',
                        'code': 'invalid',
                    },
                ],
            },
        )
        error_min_1_element = {
            'field': 'categories_ids',
            'description': 'Ensure this field has at least 1 elements.',
            'code': 'min_length',
        }

        data = {
            'location_geo': [52.232989, 20.990810],
            'gender': Gender.Female,
            'categories_ids': [],
        }
        response = self.get(data=data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json(), {'errors': [error_min_1_element]})

        data['categories_ids'] = ''
        response = self.get(data=data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json(), {'errors': [error_min_1_element]})

    @override_eppo_feature_flag({CustomerRecommendedNewFlag.flag_name: {'business_limit': 1}})
    def test_no_cover_photo(self):
        categories_names = [
            BusinessCategoryEnum.HAIR_SALONS,
            BusinessCategoryEnum.PIERCING_CATEGORY,
            BusinessCategoryEnum.BARBERS,
        ]
        self._create_business(
            active_x_days=2,
            has_cover_photo=False,
            categories_names=categories_names,
            services_count=3,
            location_geo=[52.232989, 21.032610],
        )
        self.index.refresh()

        categories_ids = [self.bussiness_categories[name].id for name in categories_names]

        data = {
            'location_geo': [52.232989, 20.990810],
            'gender': Gender.Male,
            'categories_ids': categories_ids[:2],
            'location_id': '',
        }

        response = self.get(data=data)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())
        businesses = response.json()['businesses']
        self.assertEqual(len(businesses), 0)

    @override_eppo_feature_flag({CustomerRecommendedNewFlag.flag_name: {'business_limit': 1}})
    def test_request_parameters_validation(self):
        self._remove_login_token()
        data = {
            'location_geo': [52.232989, 20.990810],
            'gender': Gender.Male,
            'categories_ids': [self.bussiness_categories[BusinessCategoryEnum.BARBERS].id],
            'location_id': 123,
            'trigger_source': SelectedForYouTriggerSource.ON_DEMAND_MY_BOOKSY,
        }

        # Test with invalid gender
        invalid_data = data.copy()
        invalid_data['gender'] = 'invalid_gender'
        response = self.get(data=invalid_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json()['errors'][0]['code'], 'invalid_choice')

        # Test with invalid categories_ids (non-integer)
        invalid_data = data.copy()
        invalid_data['categories_ids'] = ['not_an_integer']
        response = self.get(data=invalid_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json()['errors'][0]['code'], 'invalid')

        # Test with invalid location_id (non-integer)
        invalid_data = data.copy()
        invalid_data['location_id'] = 'not_an_integer'
        response = self.get(data=invalid_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json()['errors'][0]['code'], 'invalid')

        # Test with invalid trigger_source
        invalid_data = data.copy()
        invalid_data['trigger_source'] = 'invalid_trigger_source'
        response = self.get(data=invalid_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json()['errors'][0]['code'], 'invalid_choice')


@override_eppo_feature_flag({CustomerRecommendedNewFlag.flag_name: {'business_limit': 3}})
@override_eppo_feature_flag({S4UAfterNegativeReviewFlag.flag_name: True})
class TestMyBooksyS4UViewAfterNegativeReview(TestCustomerMyBooksySelectedForYouView):
    def setUp(self):
        super().setUp()
        hair_salons_category = self.bussiness_categories[BusinessCategoryEnum.HAIR_SALONS]
        self.haircut_treatment = treatment_recipe.make(
            parent=hair_salons_category, name='Haircut Treatment'
        )
        self.styling_treatment = treatment_recipe.make(
            parent=hair_salons_category, name='Styling Treatment'
        )

    def test_extract_review_data(self):
        business = baker.make(Business, latitude=52.232989, longitude=20.990810)
        review_with_treatments = baker.make(
            Review,
            business=business,
            services=[
                {'id': 1, 'name': 'Service 1', 'treatment_id': self.haircut_treatment.id},
                {'id': 2, 'name': 'Service 2', 'treatment_id': self.styling_treatment.id},
                {'id': 3, 'name': 'Service 3', 'treatment_id': None},
            ],
        )
        review_without_treatments = baker.make(
            Review,
            business=business,
            services=[
                {'id': 4, 'name': 'Service 4', 'treatment_id': None},
                {'id': 5, 'name': 'Service 5'},
            ],
        )
        review_empty_services = baker.make(Review, business=business, services=[])

        # Test review with treatments
        review_data = CustomerMyBooksySelectedForYouView._extract_review_data(  # pylint: disable=protected-access
            review_with_treatments.id
        )
        self.assertEqual(
            review_data['treatment_ids'], [self.haircut_treatment.id, self.styling_treatment.id]
        )
        self.assertEqual(review_data['business_id'], business.id)
        self.assertEqual(review_data['location_geo']['lat'], 52.232989)
        self.assertEqual(review_data['location_geo']['lon'], 20.990810)

        # Test review without treatments
        review_data = CustomerMyBooksySelectedForYouView._extract_review_data(  # pylint: disable=protected-access
            review_without_treatments.id
        )
        self.assertEqual(review_data['treatment_ids'], [])
        self.assertEqual(review_data['business_id'], business.id)
        self.assertEqual(review_data['location_geo']['lat'], 52.232989)
        self.assertEqual(review_data['location_geo']['lon'], 20.990810)

        # Test review with empty services
        review_data = CustomerMyBooksySelectedForYouView._extract_review_data(  # pylint: disable=protected-access
            review_empty_services.id
        )
        self.assertEqual(review_data['treatment_ids'], [])
        self.assertEqual(review_data['business_id'], business.id)
        self.assertEqual(review_data['location_geo']['lat'], 52.232989)
        self.assertEqual(review_data['location_geo']['lon'], 20.990810)

        review_data = CustomerMyBooksySelectedForYouView._extract_review_data(  # pylint: disable=protected-access
            99999
        )
        self.assertIsNone(review_data)

    def test_negative_review_trigger_with_matching_treatments(self):
        business_with_review = baker.make(Business)
        review = baker.make(
            Review,
            business=business_with_review,
            services=[
                {'id': 1, 'name': 'Haircut', 'treatment_id': self.haircut_treatment.id},
                {'id': 2, 'name': 'Styling', 'treatment_id': self.styling_treatment.id},
            ],
        )
        business_with_matching_treatment = self._create_business(
            active_x_days=1,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.HAIR_SALONS],
            services_count=2,
            location_geo=[52.232989, 20.990810],
        )

        services = Service.objects.filter(business=business_with_matching_treatment)
        service_1 = services[0]
        service_2 = services[1]
        service_1.treatment = self.haircut_treatment
        service_1.save()
        service_2.treatment = self.styling_treatment
        service_2.save()

        BusinessDocument.reindex(ids=[business_with_matching_treatment.id])

        business_without_matching = self._create_business(
            active_x_days=1,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.HAIR_SALONS],
            services_count=3,
            location_geo=[52.232989, 20.990810],
        )

        self.index.refresh()

        data = {
            'trigger_source': SelectedForYouTriggerSource.NEGATIVE_REVIEW,
            'review_id': str(review.id),
            'gender': Gender.Male,
            'categories_ids': [],
            'location_geo': '52.232989,20.990810',
        }

        response = self.get(data=data)
        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())

        response_data = response.json()
        businesses = response_data['businesses']

        business_ids = {b['id'] for b in businesses}
        self.assertIn(business_with_matching_treatment.id, business_ids)
        self.assertNotIn(business_without_matching.id, business_ids)

        # Verify treatment IDs in the response
        returned_business = businesses[0]
        treatment_services = returned_business.get('treatment_services', [])
        returned_treatment_ids = {service['treatment_id'] for service in treatment_services}
        expected_treatment_ids = {self.haircut_treatment.id, self.styling_treatment.id}
        self.assertEqual(returned_treatment_ids, expected_treatment_ids)

    def test_negative_review_trigger_finds_cross_category_businesses(self):
        business_with_review = baker.make(Business)
        review = baker.make(
            Review,
            business=business_with_review,
            services=[
                {'id': 1, 'name': 'Haircut', 'treatment_id': self.haircut_treatment.id},
                {'id': 2, 'name': 'Styling', 'treatment_id': self.styling_treatment.id},
            ],
        )
        business_with_matching_treatment = self._create_business(
            active_x_days=1,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.HAIR_SALONS],
            services_count=2,
            location_geo=[52.232989, 20.990810],
        )
        services = Service.objects.filter(business=business_with_matching_treatment)
        service_1 = services[0]
        service_2 = services[1]
        service_1.treatment = self.haircut_treatment
        service_1.save()
        service_2.treatment = self.styling_treatment
        service_2.save()
        BusinessDocument.reindex(ids=[business_with_matching_treatment.id])
        self.index.refresh()
        data = {
            'trigger_source': SelectedForYouTriggerSource.NEGATIVE_REVIEW,
            'review_id': str(review.id),
            'gender': Gender.Male,
            'categories_ids': [],
            'location_geo': '52.232989,20.990810',
        }

        response = self.get(data=data)
        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())

        response_data = response.json()
        businesses = response_data['businesses']

        business_ids = {b['id'] for b in businesses}
        self.assertIn(business_with_matching_treatment.id, business_ids)
        returned_business = businesses[0]
        treatment_services = returned_business.get('treatment_services', [])
        returned_treatment_ids = {service['treatment_id'] for service in treatment_services}
        expected_treatment_ids = {self.haircut_treatment.id, self.styling_treatment.id}
        self.assertEqual(returned_treatment_ids, expected_treatment_ids)

    def test_negative_review_trigger_without_review_id(self):
        self._create_business(
            active_x_days=1,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.HAIR_SALONS],
            services_count=3,
            location_geo=[52.232989, 20.990810],
        )
        self.index.refresh()
        data = {
            'trigger_source': SelectedForYouTriggerSource.NEGATIVE_REVIEW,
            'gender': Gender.Male,
            'categories_ids': [],
            'location_geo': '52.232989,20.990810',
        }

        response = self.get(data=data)
        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())

        response_data = response.json()
        businesses = response_data['businesses']
        self.assertEqual(len(businesses), 0)

    def test_negative_review_trigger_with_invalid_review_id(self):
        self._create_business(
            active_x_days=1,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.HAIR_SALONS],
            services_count=3,
            location_geo=[52.232989, 20.990810],
        )
        self.index.refresh()
        data = {
            'trigger_source': SelectedForYouTriggerSource.NEGATIVE_REVIEW,
            'review_id': 99999,
            'gender': Gender.Male,
            'categories_ids': [],
            'location_geo': '52.232989,20.990810',
        }

        response = self.get(data=data)
        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())

        response_data = response.json()
        businesses = response_data['businesses']
        self.assertEqual(len(businesses), 0)

    def test_negative_review_excludes_associated_business(self):
        business_with_review = baker.make(Business)
        review = baker.make(
            Review,
            business=business_with_review,
            services=[
                {'id': 1, 'name': 'Haircut', 'treatment_id': self.haircut_treatment.id},
                {'id': 2, 'name': 'Styling', 'treatment_id': self.styling_treatment.id},
            ],
        )
        business_should_appear = self._create_business(
            active_x_days=1,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.HAIR_SALONS],
            services_count=2,
            location_geo=[52.232989, 20.990810],
        )
        services = Service.objects.filter(business=business_should_appear)
        service_1 = services[0]
        service_2 = services[1]
        service_1.treatment = self.haircut_treatment
        service_1.save()
        service_2.treatment = self.styling_treatment
        service_2.save()
        BusinessDocument.reindex(ids=[business_should_appear.id])
        self.index.refresh()
        data = {
            'trigger_source': SelectedForYouTriggerSource.NEGATIVE_REVIEW,
            'review_id': str(review.id),
            'gender': Gender.Male,
            'categories_ids': [],
            'location_geo': '52.232989,20.990810',
        }

        response = self.get(data=data)
        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())

        response_data = response.json()
        businesses = response_data['businesses']
        business_ids = {b['id'] for b in businesses}
        self.assertNotIn(business_with_review.id, business_ids)
        self.assertIn(business_should_appear.id, business_ids)

    def test_extract_review_data_includes_location_geo(self):
        business = baker.make(Business, latitude=52.232989, longitude=20.990810)
        review = baker.make(
            Review,
            business=business,
            services=[
                {'id': 1, 'name': 'Service 1', 'treatment_id': self.haircut_treatment.id},
            ],
        )

        review_data = CustomerMyBooksySelectedForYouView._extract_review_data(  # pylint: disable=protected-access
            review.id
        )

        self.assertEqual(review_data['business_id'], business.id)
        self.assertEqual(review_data['treatment_ids'], [self.haircut_treatment.id])
        self.assertEqual(review_data['location_geo']['lat'], 52.232989)
        self.assertEqual(review_data['location_geo']['lon'], 20.990810)

    def test_negative_review_uses_review_location_when_no_location_provided(self):
        business_with_review = baker.make(Business, latitude=51.5074, longitude=-0.1278)  # London
        review = baker.make(
            Review,
            business=business_with_review,
            services=[
                {'id': 1, 'name': 'Haircut', 'treatment_id': self.haircut_treatment.id},
            ],
        )
        london_business = self._create_business(
            active_x_days=1,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.HAIR_SALONS],
            services_count=1,
            location_geo=[51.5074, -0.1278],  # London coordinates
        )
        warsaw_business = self._create_business(
            active_x_days=1,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.HAIR_SALONS],
            services_count=1,
            location_geo=[52.232989, 20.990810],  # Warsaw coordinates
        )
        for business in [london_business, warsaw_business]:
            services = Service.objects.filter(business=business)
            if services.exists():
                service = services.first()
                service.treatment = self.haircut_treatment
                service.save()
                BusinessDocument.reindex(ids=[business.id])

        self.index.refresh()
        data = {
            'trigger_source': SelectedForYouTriggerSource.NEGATIVE_REVIEW,
            'review_id': str(review.id),
            'gender': Gender.Male,
            'categories_ids': [self.bussiness_categories[BusinessCategoryEnum.HAIR_SALONS].id],
        }

        response = self.get(data=data)
        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())

        response_data = response.json()
        businesses = response_data['businesses']
        business_ids = {b['id'] for b in businesses}

        # Should include London business
        self.assertIn(london_business.id, business_ids)
        # Should not include Warsaw business
        self.assertNotIn(warsaw_business.id, business_ids)
        # Should exclude the original review business
        self.assertNotIn(business_with_review.id, business_ids)


@override_eppo_feature_flag({SimpleSerchableInRecommendedForYouFlag.flag_name: True})
class TestCustomerMyBooksySelectedForYouViewOptimized(TestCustomerMyBooksySelectedForYouView): ...


@override_eppo_feature_flag({UseLastBookingLocationInSelectedForYouFlag.flag_name: True})
class TestCustomerMyBooksySelectedForYouViewFF(TestCustomerMyBooksySelectedForYouView):

    @override_eppo_feature_flag({CustomerRecommendedNewFlag.flag_name: {'business_limit': 1}})
    def test_get_logged_in_get_last_booking_location(self):
        categories_names = [
            BusinessCategoryEnum.HAIR_SALONS,
            BusinessCategoryEnum.PIERCING_CATEGORY,
            BusinessCategoryEnum.BARBERS,
        ]
        business_0 = self._create_business(
            active_x_days=1,
            has_cover_photo=True,
            categories_names=categories_names,
            services_count=3,
            location_geo=[52.232989, 20.990810],
        )
        CustomerFavoriteCategory.objects.create(
            user=self.authorized_user,
            category=self.bussiness_categories[BusinessCategoryEnum.HAIR_SALONS],
        )
        self.index.refresh()
        self._create_appointment_with_location(52, 21, self.user)

        data = {
            'location_geo': '',
            'location_id': '',
            'gender': Gender.Male,
            'categories_ids': [self.bussiness_categories[name].id for name in categories_names],
        }

        response = self.get(data=data)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())
        response_data = response.json()
        businesses = response_data['businesses']
        ids = {b['id'] for b in businesses}
        self.assertEqual(len(businesses), 1, ids)
        self.assertEqual(ids, {business_0.id})


@override_eppo_feature_flag(
    {
        CustomerRecommendedNewFlag.flag_name: {'business_limit': 3},
        SelectedForYouV4RandomizedScoringExperiment.flag_name: ExperimentVariants.VARIANT_A,
    }
)
class TestCustomerMyBooksySelectedForYouV4(TestCustomerMyBooksySelectedForYouView):

    def _prepare_businesses(self, count, categories_names, **kwargs):
        default_params = {
            'active_x_days': 10,
            'has_cover_photo': True,
            'services_count': 3,
            'location_geo': [52.232989, 20.990810],
            'promoted': False,
        }
        default_params.update(kwargs)

        businesses = []
        for _ in range(count):
            business = self._create_business(categories_names=categories_names, **default_params)
            businesses.append(business)

        self.index.refresh()
        return businesses

    def _setup_customer_favorite_categories(self, categories_names):
        for category_name in categories_names:
            CustomerFavoriteCategory.objects.create(
                user=self.authorized_user,
                category=self.bussiness_categories[category_name],
            )

    def _prepare_request_data(
        self,
        *,
        location_geo=None,
        categories_names=None,
        gender=None,
        trigger_source=None,
    ):
        return {
            'location_geo': location_geo or [52.232989, 20.990810],
            'gender': gender or Gender.Male,
            'categories_ids': [self.bussiness_categories[name].id for name in categories_names],
            'trigger_source': trigger_source or SelectedForYouTriggerSource.ON_DEMAND_MY_BOOKSY,
        }

    def test_randomized_scoring_different_orderings_results(self):
        categories = [BusinessCategoryEnum.HAIR_SALONS]
        self._setup_customer_favorite_categories(categories)

        # more businesses for better variation
        self._prepare_businesses(count=10, categories_names=categories)
        data = self._prepare_request_data(categories_names=categories)

        results = []
        for _ in range(15):
            response = self.get(data=data)
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            business_ids = [b['id'] for b in response.json()['businesses']]
            results.append(business_ids)

        unique_results = set(tuple(result) for result in results)
        # This is conservative but should help to avoid flakiness
        self.assertGreaterEqual(len(unique_results), 3)

    @pytest.mark.random_failure
    def test_promoted_business_penalty(self):
        categories = [BusinessCategoryEnum.HAIR_SALONS]
        self._setup_customer_favorite_categories(categories)

        self._prepare_businesses(count=3, categories_names=categories, promoted=True)
        regular_businesses = self._prepare_businesses(
            count=3, categories_names=categories, promoted=False
        )
        self.index.refresh()

        data = self._prepare_request_data(categories_names=categories)

        regular_first_count = 0
        total_runs = 30  # More runs for better statistics

        for _ in range(total_runs):
            response = self.get(data=data)
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            businesses = response.json()['businesses']
            self.assertEqual(len(businesses), 3)

            # Check if a regular business is in first position
            first_business_id = businesses[0]['id']
            regular_ids = {biz.id for biz in regular_businesses}

            if first_business_id in regular_ids:
                regular_first_count += 1

        # Conservative threshold to avoid flakiness while still testing the penalty
        regular_first_percentage = regular_first_count / total_runs
        self.assertGreater(regular_first_percentage, 0.4)

    def test_reviews_count_scoring_randomization(self):
        categories = [BusinessCategoryEnum.HAIR_SALONS]
        self._setup_customer_favorite_categories(categories)

        businesses = self._prepare_businesses(count=3, categories_names=categories)

        # Set very different review counts to make the effect more visible when applied
        high_reviews_business = businesses[0]
        high_reviews_doc = high_reviews_business.get_document()
        high_reviews_doc.reviews_count = 50  # Many reviews = lower boost when applied
        high_reviews_doc.save()

        low_reviews_business = businesses[1]
        low_reviews_doc = low_reviews_business.get_document()
        low_reviews_doc.reviews_count = 1  # Few reviews = higher boost when applied
        low_reviews_doc.save()

        medium_reviews_business = businesses[2]
        medium_reviews_doc = medium_reviews_business.get_document()
        medium_reviews_doc.reviews_count = 10  # Medium reviews
        medium_reviews_doc.save()

        self.index.refresh()

        data = self._prepare_request_data(categories_names=categories)

        orderings = []
        total_runs = 25  # More runs for better statistical reliability

        for _ in range(total_runs):
            response = self.get(data=data)
            self.assertEqual(response.status_code, status.HTTP_200_OK)
            businesses_response = response.json()['businesses']
            self.assertEqual(len(businesses_response), 3)

            # Record the ordering
            business_ids = [b['id'] for b in businesses_response]
            orderings.append(tuple(business_ids))

        # Also verify that the low-review business appears in different positions
        # due to the randomized application of review scoring
        low_review_positions = []
        for ordering in orderings:
            if low_reviews_business.id in ordering:
                position = list(ordering).index(low_reviews_business.id)
                low_review_positions.append(position)

        # The low-review business should appear in at least 2 different positions
        # due to randomization (when review factor applied vs not applied)
        unique_positions = set(low_review_positions)
        self.assertGreaterEqual(len(unique_positions), 2)


@override_eppo_feature_flag({CustomerRecommendedNewFlag.flag_name: {'business_limit': 3}})
@override_eppo_feature_flag({S4UAfterBookingCreatedFlag.flag_name: True})
@override_eppo_feature_flag({S4UAfterBookingExperiment.flag_name: ExperimentVariants.VARIANT_A})
class TestMyBooksyS4UViewAfterBooking(BaseTestCustomerMyBooksySelectedForYouView):
    def setUp(self):
        super().setUp()
        self.business_no_location = business_recipe.make(latitude=None, longitude=None)
        self.business_with_location = business_recipe.make(latitude=52.232989, longitude=20.990810)
        self.haircut_treatment = treatment_recipe.make(
            parent=self.bussiness_categories[BusinessCategoryEnum.HAIR_SALONS],
            name='Haircut Treatment',
        )
        self.manicure_treatment = treatment_recipe.make(
            parent=self.bussiness_categories[BusinessCategoryEnum.NAIL_SALONS],
            name='Manicure Treatment',
        )
        self.empty_response = {'businesses': [], 'is_variant_a': False}
        self.promoted_categories_names = [
            BusinessCategoryEnum.HAIR_SALONS,
            BusinessCategoryEnum.BROWS_AND_LASHES,
            BusinessCategoryEnum.SKIN_CARE,
            BusinessCategoryEnum.MAKE_UP,
            BusinessCategoryEnum.NAIL_SALONS,
        ]
        self.promoted_categories_ids = [
            cat.id
            for cat in self.bussiness_categories.values()
            if cat.internal_name in self.promoted_categories_names
        ]

    @staticmethod
    def _make_service_variant(business, treatment):
        service = service_recipe.make(business=business, treatment=treatment)
        variant = service_variant_recipe.make(service=service)
        return variant

    @staticmethod
    def _make_booking(business, service_variants):
        appointment = appointment_recipe.make(business=business)
        for variant in service_variants:
            booking = booking_recipe.prepare(appointment=appointment, service_variant=variant)
            booking.save(override=True)
        return appointment

    @override_eppo_feature_flag({S4UAfterBookingCreatedFlag.flag_name: False})
    def test_disabled_by_ff_returns_empty_response(self):
        variant = self._make_service_variant(self.business_with_location, self.haircut_treatment)
        appointment = self._make_booking(
            business=self.business_with_location, service_variants=[variant]
        )
        data = {
            'trigger_source': SelectedForYouTriggerSource.BOOKING_CREATED,
            'appointment_uid': appointment.id,
        }
        response = self.get(data=data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json(), self.empty_response)

    def test_not_logged_in_returns_empty_response(self):
        self._remove_login_token()
        variant = self._make_service_variant(self.business_with_location, self.haircut_treatment)
        appointment = self._make_booking(
            business=self.business_with_location, service_variants=[variant]
        )
        data = {
            'trigger_source': SelectedForYouTriggerSource.BOOKING_CREATED,
            'appointment_uid': appointment.id,
            'gender': Gender.Male,
            'categories_ids': [self.bussiness_categories[BusinessCategoryEnum.HAIR_SALONS].id],
        }
        response = self.get(data=data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json(), self.empty_response)

    def test_no_appointment_id_returns_empty_response(self):
        data = {'trigger_source': SelectedForYouTriggerSource.BOOKING_CREATED}

        response = self.get(data=data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json(), self.empty_response)

    def test_no_such_appointment_returns_empty_response(self):
        data = {
            'trigger_source': SelectedForYouTriggerSource.BOOKING_CREATED,
            'appointment_uid': 1234,
        }

        response = self.get(data=data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json(), self.empty_response)

    def test_appointment_in_non_target_category_returns_empty_response(self):
        variant = self._make_service_variant(self.business_with_location, self.manicure_treatment)
        appointment = self._make_booking(
            business=self.business_with_location, service_variants=[variant]
        )
        data = {
            'trigger_source': SelectedForYouTriggerSource.BOOKING_CREATED,
            'appointment_uid': appointment.id,
        }
        response = self.get(data=data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json(), self.empty_response)

    @override_eppo_feature_flag({S4UAfterBookingExperiment.flag_name: ExperimentVariants.CONTROL})
    def test_control_group_returns_empty_response(self):
        variant = self._make_service_variant(self.business_with_location, self.haircut_treatment)
        appointment = self._make_booking(
            business=self.business_with_location, service_variants=[variant]
        )
        data = {
            'trigger_source': SelectedForYouTriggerSource.BOOKING_CREATED,
            'appointment_uid': appointment.id,
        }
        response = self.get(data=data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json(), self.empty_response)

    def test_appointment_in_target_category_returns_correct_businesses(self):
        variant = self._make_service_variant(self.business_with_location, self.haircut_treatment)
        hair_salon_category = self.bussiness_categories[BusinessCategoryEnum.HAIR_SALONS]
        correct_treatment_categories = [
            cat_id for cat_id in self.promoted_categories_ids if cat_id != hair_salon_category.id
        ]
        appointment = self._make_booking(
            business=self.business_with_location, service_variants=[variant]
        )
        business_nails = self._create_business(
            active_x_days=2,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.NAIL_SALONS],
            services_count=3,
            location_geo=[52.232989, 21.032610],
        )
        business_hair = self._create_business(
            active_x_days=2,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.HAIR_SALONS],
            services_count=3,
            location_geo=[52.232989, 21.032610],
        )
        business_make_up = self._create_business(
            active_x_days=2,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.MAKE_UP],
            services_count=3,
            location_geo=[52.232989, 21.032610],
        )
        self.index.refresh()
        data = {
            'trigger_source': SelectedForYouTriggerSource.BOOKING_CREATED,
            'appointment_uid': appointment.id,
        }
        response = self.get(data=data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()['is_variant_a'], True)
        s4u_businesses = response.json()['businesses']
        s4u_businesses_ids = [b['id'] for b in s4u_businesses]
        self.assertEqual(len(s4u_businesses), 2)
        self.assertIn(business_nails.id, s4u_businesses_ids)
        self.assertIn(business_make_up.id, s4u_businesses_ids)
        # business in category in which there was booking shouldn't be returned
        self.assertNotIn(business_hair.id, s4u_businesses_ids)
        # only one treatment should be returned
        self.assertEqual(len(s4u_businesses[0]['treatment_services']), 1)
        self.assertEqual(len(s4u_businesses[1]['treatment_services']), 1)
        # treatments should be in promoted categories - the one in which appointment was made
        self.assertIn(
            s4u_businesses[0]['treatment_services'][0]['treatment_parent_id'],
            correct_treatment_categories,
        )
        self.assertIn(
            s4u_businesses[1]['treatment_services'][0]['treatment_parent_id'],
            correct_treatment_categories,
        )

    def test_appointment_in_business_no_location_takes_it_from_request(self):
        variant = self._make_service_variant(self.business_no_location, self.haircut_treatment)
        location_geo = [52.232989, 20.990810]
        appointment = self._make_booking(
            business=self.business_with_location, service_variants=[variant]
        )
        business_nails = self._create_business(
            active_x_days=2,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.NAIL_SALONS],
            services_count=3,
            location_geo=location_geo,
        )
        business_hair = self._create_business(
            active_x_days=2,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.HAIR_SALONS],
            services_count=3,
            location_geo=location_geo,
        )
        business_make_up = self._create_business(
            active_x_days=2,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.MAKE_UP],
            services_count=3,
            location_geo=location_geo,
        )
        self.index.refresh()
        data = {
            'trigger_source': SelectedForYouTriggerSource.BOOKING_CREATED,
            'appointment_uid': appointment.id,
            'location_geo': location_geo,
        }
        response = self.get(data=data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()['is_variant_a'], True)
        s4u_businesses = response.json()['businesses']
        s4u_businesses_ids = [b['id'] for b in s4u_businesses]
        self.assertEqual(len(s4u_businesses), 2)
        self.assertIn(business_nails.id, s4u_businesses_ids)
        self.assertIn(business_make_up.id, s4u_businesses_ids)
        # business in category in which there was booking shouldn't be returned
        self.assertNotIn(business_hair.id, s4u_businesses_ids)

    def test_categories_from_request_are_ignored(self):
        variant = self._make_service_variant(self.business_no_location, self.haircut_treatment)
        location_geo = [52.232989, 20.990810]
        appointment = self._make_booking(
            business=self.business_with_location, service_variants=[variant]
        )
        business_barber = self._create_business(
            active_x_days=2,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.BARBERS],
            services_count=3,
            location_geo=location_geo,
        )
        business_nails = self._create_business(
            active_x_days=2,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.NAIL_SALONS],
            services_count=3,
            location_geo=location_geo,
        )
        business_make_up = self._create_business(
            active_x_days=2,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.MAKE_UP],
            services_count=3,
            location_geo=location_geo,
        )
        self.index.refresh()
        data = {
            'trigger_source': SelectedForYouTriggerSource.BOOKING_CREATED,
            'appointment_uid': appointment.id,
            'categories_ids': [self.bussiness_categories[BusinessCategoryEnum.BARBERS].id],
        }
        response = self.get(data=data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()['is_variant_a'], True)
        s4u_businesses = response.json()['businesses']
        s4u_businesses_ids = [b['id'] for b in s4u_businesses]
        self.assertEqual(len(s4u_businesses), 2)
        self.assertIn(business_nails.id, s4u_businesses_ids)
        self.assertIn(business_make_up.id, s4u_businesses_ids)
        # business in category from request not present as it is not target category
        self.assertNotIn(business_barber.id, s4u_businesses_ids)


@override_eppo_feature_flag(
    {
        CustomerRecommendedNewFlag.flag_name: {'business_limit': 3},
        S4UDeeplinkNailsCampaignFlag.flag_name: True,
    }
)
class TestMyBooksyS4UViewNailsCampaign(BaseTestCustomerMyBooksySelectedForYouView):

    def setUp(self):
        super().setUp()
        CustomerMyBooksySelectedForYouView._get_search_category_id.cache_clear()  # pylint: disable=protected-access
        self.non_nails_business = self._create_business(
            active_x_days=1,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.HAIR_SALONS],
            services_count=2,
            location_geo=[52.232989, 20.990810],
        )
        self.index.refresh()
        self.data = {
            'trigger_source': SelectedForYouTriggerSource.NAILS_CAMPAIGN,
            'location_geo': [52.232989, 20.990810],
        }

    @override_eppo_feature_flag({S4UDeeplinkNailsCampaignFlag.flag_name: False})
    def test_ff_off_returns_empty_response(self):
        response = self.get(data=self.data)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())
        self.assertEqual(response.json(), {'businesses': []})

    def test_no_nails_businesses_returns_empty_response(self):
        response = self.get(data=self.data)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())
        self.assertEqual(response.json(), {'businesses': []})

    def test_only_nails_business_is_returned(self):
        nails_business = self._create_business(
            active_x_days=1,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.NAIL_SALONS],
            services_count=2,
            location_geo=[52.232989, 20.990810],
        )
        self.index.refresh()

        response = self.get(data=self.data)
        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())
        businesses = response.json()['businesses']
        returned_ids = {b['id'] for b in businesses}
        self.assertEqual(len(returned_ids), 1)
        self.assertIn(nails_business.id, returned_ids)
        self.assertNotIn(self.non_nails_business.id, returned_ids)

    def test_only_nails_returned_even_if_other_categories_passed(self):
        nails_business = self._create_business(
            active_x_days=1,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.NAIL_SALONS],
            services_count=2,
            location_geo=[52.232989, 20.990810],
        )
        self.index.refresh()
        data = {
            'trigger_source': SelectedForYouTriggerSource.NAILS_CAMPAIGN,
            'categories_ids': [self.bussiness_categories[BusinessCategoryEnum.HAIR_SALONS].id],
            'location_geo': [52.232989, 20.990810],
        }

        response = self.get(data=data)
        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())
        businesses = response.json()['businesses']
        returned_ids = {b['id'] for b in businesses}
        self.assertEqual(len(returned_ids), 1)
        self.assertIn(nails_business.id, returned_ids)
        self.assertNotIn(self.non_nails_business.id, returned_ids)

    def test_logged_out_no_gender_categories_ids_provided_returns_200(self):
        self._remove_login_token()
        nails_business = self._create_business(
            active_x_days=1,
            has_cover_photo=True,
            categories_names=[BusinessCategoryEnum.NAIL_SALONS],
            services_count=2,
            location_geo=[52.232989, 20.990810],
        )
        self.index.refresh()
        data = {
            'trigger_source': SelectedForYouTriggerSource.NAILS_CAMPAIGN,
            'location_geo': [52.232989, 20.990810],
        }

        response = self.get(data=data)
        self.assertEqual(response.status_code, status.HTTP_200_OK, response.json())
        businesses = response.json()['businesses']
        returned_ids = {b['id'] for b in businesses}
        self.assertEqual(len(returned_ids), 1)
        self.assertIn(nails_business.id, returned_ids)
        self.assertNotIn(self.non_nails_business.id, returned_ids)
