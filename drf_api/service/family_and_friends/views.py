from bo_obs.datadog.enums import BooksyTeams
from django.conf import settings
from django.shortcuts import get_object_or_404
from django.utils.translation import gettext_lazy as _
from rest_framework.mixins import CreateModelMixin, RetrieveModelMixin, UpdateModelMixin
from rest_framework.permissions import IsAuthenticated
from rest_framework.serializers import ValidationError

from drf_api.base_views import BaseBooksySessionGenericViewSet
from drf_api.service.images.views import ObjectsPhotoViewSet
from service.customer.hcaptcha import IsHCaptchaValidated
from service.customer.recaptcha import IsRecaptchaValidated
from webapps.family_and_friends.helpers.profiles import get_or_create_current_user_member_profile
from webapps.family_and_friends.models import MemberInvitation, MemberProfile
from webapps.family_and_friends.serializers.member import (
    MatchInvitationKeySerializer,
    MemberActionInvitationSerializer,
    MemberProfileCreateSerializer,
    MemberProfileRelationsSerializer,
    MemberProfileUpdateSerializer,
    UnlinkSerializer,
)
from webapps.images.enums import ImageTypeEnum


# pylint: disable=too-many-ancestors
class FamilyAndFriendsMemberViewSet(
    BaseBooksySessionGenericViewSet, RetrieveModelMixin, CreateModelMixin
):
    booksy_teams = (BooksyTeams.CUSTOMER_BOOKING, BooksyTeams.CUSTOMER_ENGAGEMENT)
    permission_classes = (
        IsAuthenticated,
        IsHCaptchaValidated,
        IsRecaptchaValidated,
    )
    serializer_class = MemberProfileRelationsSerializer

    CREATE = 'create'

    RECAPTCHA_SETTINGS = {
        CREATE: {
            'feature_flag': lambda: settings.RECAPTCHA_ENABLED,
        },
    }

    HCAPTCHA_ENABLED_ACTIONS = [CREATE]

    def get_object(self):
        if self.request and (customer_profile := self.request.user.customer_profile):
            return get_or_create_current_user_member_profile(customer_profile)

    def get_serializer_class(self):
        return (
            MemberProfileCreateSerializer if self.action == self.CREATE else self.serializer_class
        )

    def get_serializer_context(self):
        context = super().get_serializer_context()
        if self.action == self.CREATE:
            context['parent_profile'] = self.get_object()
        return context


class FamilyAndFriendsMemberDetailsViewSet(BaseBooksySessionGenericViewSet, UpdateModelMixin):
    booksy_teams = (BooksyTeams.CUSTOMER_BOOKING, BooksyTeams.CUSTOMER_ENGAGEMENT)
    permission_classes = (
        IsAuthenticated,
        IsRecaptchaValidated,
    )
    queryset = MemberProfile
    serializer_class = MemberProfileUpdateSerializer

    RECAPTCHA_SETTINGS = {
        'update': {
            'feature_flag': lambda: settings.RECAPTCHA_ENABLED,
        },
    }

    def get_serializer_context(self):
        context = super().get_serializer_context()
        if self.request:
            context['parent_profile'] = self.request.user.customer_profile.member_profile
        return context


class MatchUserInvitationViewSet(BaseBooksySessionGenericViewSet, UpdateModelMixin):
    booksy_teams = (BooksyTeams.CUSTOMER_BOOKING, BooksyTeams.CUSTOMER_ENGAGEMENT)
    permission_classes = (IsAuthenticated,)
    serializer_class = MatchInvitationKeySerializer

    def get_object(self):
        return get_object_or_404(MemberInvitation, key=self.request.data.get('key'))


class InvitationViewSet(BaseBooksySessionGenericViewSet, UpdateModelMixin):
    booksy_teams = (BooksyTeams.CUSTOMER_BOOKING, BooksyTeams.CUSTOMER_ENGAGEMENT)
    permission_classes = (IsAuthenticated,)
    serializer_class = MemberActionInvitationSerializer

    def get_object(self):
        self.request.data.update({'action_type': self.kwargs.get('action_type')})
        return get_object_or_404(MemberInvitation, key=self.request.data.get('key'))


class UnlinkViewSet(BaseBooksySessionGenericViewSet, UpdateModelMixin):
    booksy_teams = (BooksyTeams.CUSTOMER_BOOKING, BooksyTeams.CUSTOMER_ENGAGEMENT)
    permission_classes = (IsAuthenticated,)
    serializer_class = UnlinkSerializer

    def get_object(self):
        return get_object_or_404(
            MemberProfile.objects.filter(user_profile_id=self.request.user.customer_profile.id),
        )


class MemberPhotoViewSet(ObjectsPhotoViewSet):
    # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.CUSTOMER_BOOKING, BooksyTeams.CUSTOMER_ENGAGEMENT)
    image_type = ImageTypeEnum.MEMBER_PHOTO

    def get_object(self):
        instance_id = self.kwargs.get('member_profile_pk')

        parent_profile = get_object_or_404(
            MemberProfile.objects.prefetch_related(
                'members',
            ),
            user_profile=self.request.user.customer_profile,
        )
        member_profile = get_object_or_404(parent_profile.members, id=instance_id)

        if member_profile.user_profile:
            raise ValidationError(
                code='bad_request', detail=_('Cannot edit photos for active members')
            )

        return member_profile
