import datetime
from decimal import Decimal

# pylint: disable=line-too-long
from unittest import mock
from unittest.mock import Mock, PropertyMock, patch

from django.conf import settings
from django.test import override_settings
from freezegun import freeze_time
import pytest
from model_bakery import baker
from rest_framework.reverse import reverse
from drf_api.lib.base_drf_test_case import BusinessOwnerAPITestCase
from lib.feature_flag.feature.booksy_pay import (
    BooksyPayActivationScreenFlag,
    BusinessBooksyPayOnboardingFlag,
)
from lib.feature_flag.feature.boost import BoostPreSuspensionWarningBackendFlag
from lib.feature_flag.feature.monetisation import EnablePremiumHoursSplashFlag
from lib.feature_flag.feature.payment import (
    NoShowProtectionSplashFlag,
    NoShowProtectionThresholdsSplashFlag,
    HigherPrepaymentSplashFlag,
    KeepPrepaymentSplashFlag,
)
from lib.tests.utils import override_eppo_feature_flag
from webapps.boost.baker_recipes import boosted_business_recipe
from webapps.boost.consts import FRAUD_PRICE_MANIPULATIONS_TEXTS
from webapps.boost.enums import BoostBanStatus, BoostBanType, BoostFraudWarningType
from webapps.boost.models import BoostBan, BoostFraudSuspicion
from webapps.business.baker_recipes import (
    business_recipe,
    service_recipe,
    service_variant_recipe,
    service_variant_payment_recipe,
    staffer_recipe,
)
from webapps.business.models import Business, Resource, ServiceVariantPayment
from webapps.pos.baker_recipes import pos_recipe
from webapps.pos.enums.splash import (
    SplashType,
)
from webapps.pos.models import (
    BPActivationSplash,
    HigherPrepaymentSplashDecision,
)
from webapps.premium_services.public import DayOfWeek, PeakHour, PeakHourServiceFactory, Service
from webapps.profile_setup.models import ProfileSetupProgress

from country_config import Country
from settings.boost import BoostConfig


@pytest.mark.django_db
class TestInstantTapToPaySplash(BusinessOwnerAPITestCase):
    @classmethod
    @freeze_time(datetime.datetime(2024, 6, 1, 8, tzinfo=datetime.UTC))
    def setUpTestData(cls):
        super().setUpTestData()
        cls.business = business_recipe.make(
            active_from=datetime.datetime(2024, 6, 1, 10, tzinfo=datetime.UTC),
        )
        cls.user = cls.business.owner
        cls.url = reverse(
            'instant_splash',
            kwargs={
                'business_pk': cls.business.id,
            },
        )

    @freeze_time(datetime.datetime(2024, 6, 3, 11, tzinfo=datetime.UTC))
    def test_not_see_splash_if_fees_accepted(self):
        pos_recipe.make(business=self.business, tap_to_pay_enabled=True)

        response = self.client.get(self.url)

        self.assertEqual(response.json().get('splash'), None)

    @freeze_time(datetime.datetime(2024, 6, 3, 9, tzinfo=datetime.UTC))
    def test_business_not_published_true(self):
        pos_recipe.make(business=self.business, tap_to_pay_enabled=True)
        self.business.hidden_in_search = True
        self.business.save()
        baker.make(
            ProfileSetupProgress,
            business=self.business,
            cover_photo=True,
            portfolio=True,
            business_hours=True,
            services=True,
            completed=False,
        )

        response = self.client.get(self.url)
        self.assertIsNone(response.json().get('splash'))

        self.business.hidden_in_search = False
        self.business.status = Business.Status.SETUP
        self.business.save()

        response = self.client.get(self.url)
        self.assertIsNone(response.json().get('splash'))

    @mock.patch('webapps.business.models.Business._validate_status_transfer', return_value=None)
    @mock.patch(
        'drf_api.service.splash.views.calendar_splash.CalendarSplash._create_splash',
        return_value=True,
    )
    def test_will_not_show_splash_if_business_not_paid(self, *args, **kwargs):
        pos_recipe.make(business=self.business, tap_to_pay_enabled=True)
        for status in Business.Status.never_paid_statuses():
            with self.subTest():
                self.business.status = status
                self.business.save()
                response = self.client.get(self.url)
                self.assertIsNone(response.json().get('splash'))

    @mock.patch('webapps.business.models.Business._validate_status_transfer', return_value=None)
    @mock.patch(
        'drf_api.service.splash.views.calendar_splash.CalendarSplash._create_splash',
        return_value=True,
    )
    def test_will_not_show_splash_if_business_in_blocked_overdue_status(self, *args, **kwargs):
        pos_recipe.make(business=self.business, tap_to_pay_enabled=True)
        self.business.status = Business.Status.BLOCKED_OVERDUE
        self.business.save()
        response = self.client.get(self.url)
        self.assertIsNone(response.json().get('splash'))


# TO BE REMOVED WITHIN https://booksy.atlassian.net/browse/BOA-1982
@pytest.mark.django_db
@freeze_time(datetime.datetime(2024, 6, 3, 11, tzinfo=datetime.UTC))
class TestNoShowProtectionSplash(BusinessOwnerAPITestCase):
    @classmethod
    @freeze_time(datetime.datetime(2024, 6, 1, 8, tzinfo=datetime.UTC))
    def setUpTestData(cls):
        super().setUpTestData()
        cls.business = business_recipe.make(
            active_from=datetime.datetime(2024, 6, 1, 10, tzinfo=datetime.UTC),
            public_email='<EMAIL>',
        )
        cls.user = cls.business.owner
        pos_recipe.make(business=cls.business, tap_to_pay_enabled=True)
        cls.url = reverse(
            'instant_splash',
            kwargs={
                'business_pk': cls.business.id,
            },
        )

    def _make_business_visible(self):
        staffer = staffer_recipe.make(
            business=self.business,
            staff_user=self.business.owner,
            staff_access_level=Resource.STAFF,
        )
        self.service = service_recipe.make(business=self.business)
        self.service_variant = service_variant_recipe.make(service=self.service)
        self.service.add_staffers([staffer])

    @override_settings(API_COUNTRY=Country.BR)
    @freeze_time(datetime.datetime(2024, 6, 3, 11, tzinfo=datetime.UTC))
    @mock.patch(
        'drf_api.service.splash.views.calendar_splash.calculate_lost_money',
        Mock(
            return_value={'average_monthly_lost_money': 10000, 'average_monthly_no_show_visits': 40}
        ),
    )
    @override_eppo_feature_flag({NoShowProtectionSplashFlag.flag_name: True})
    @override_eppo_feature_flag({NoShowProtectionThresholdsSplashFlag.flag_name: {'us': 100}})
    def test_no_show_not_available_country(self):
        response = self.client.get(self.url)
        self.assertEqual(response.json().get('splash'), None)

    @freeze_time(datetime.datetime(2024, 6, 3, 11, tzinfo=datetime.UTC))
    @mock.patch(
        'drf_api.service.splash.views.calendar_splash.calculate_lost_money',
        Mock(
            return_value={'average_monthly_lost_money': 340, 'average_monthly_no_show_visits': 11}
        ),
    )
    @override_eppo_feature_flag({NoShowProtectionSplashFlag.flag_name: True})
    @override_eppo_feature_flag({NoShowProtectionThresholdsSplashFlag.flag_name: {'us': 100}})
    def test_no_show_with_additional_data(self):
        response = self.client.get(self.url)
        self.assertEqual(response.json().get('splash'), SplashType.SPLASH_NO_SHOWS)
        self.assertEqual(
            response.json().get('additional_data'),
            {'average_monthly_lost_money': 340, 'average_monthly_no_show_visits': 11},
        )

    @freeze_time(datetime.datetime(2024, 6, 3, 11, tzinfo=datetime.UTC))
    @mock.patch(
        'drf_api.service.splash.views.calendar_splash.calculate_lost_money',
        Mock(return_value={'average_monthly_lost_money': 50, 'average_monthly_no_show_visits': 3}),
    )
    @override_eppo_feature_flag({NoShowProtectionSplashFlag.flag_name: True})
    @override_eppo_feature_flag({NoShowProtectionThresholdsSplashFlag.flag_name: {'us': 100}})
    def test_no_show_value_below_threshold(self):
        response = self.client.get(self.url)
        self.assertEqual(response.json().get('splash'), None)


@pytest.mark.django_db
class TestHigherPrepaymentSplash(BusinessOwnerAPITestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.business = business_recipe.make(
            public_email='<EMAIL>',
        )
        cls.user = cls.business.owner
        pos_recipe.make(business=cls.business)
        cls.url = reverse(
            'instant_splash',
            kwargs={
                'business_pk': cls.business.id,
            },
        )

    def test_dont_show_when_empty_flag(self):
        response = self.client.get(self.url)
        self.assertEqual(response.json().get('splash'), None)

    @override_eppo_feature_flag({HigherPrepaymentSplashFlag.flag_name: {'due_date': '31.12.2024'}})
    def test_dont_show_when_no_prepayments_setup(self):
        response = self.client.get(self.url)
        self.assertEqual(response.json().get('splash'), None)

    @override_eppo_feature_flag({HigherPrepaymentSplashFlag.flag_name: {'due_date': '31.12.2024'}})
    def test_dont_show_when_multiple_prepayment_rates(self):
        service_1 = service_recipe.make(business=self.business, active=True)
        service_variant_1 = service_variant_recipe.make(service=service_1, price=100)
        service_variant_payment_recipe.make(
            service_variant=service_variant_1,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('40'),  # 40%
            saving_type=ServiceVariantPayment.PERCENTAGE,
        )

        service_2 = service_recipe.make(business=self.business, active=True)
        service_variant_2 = service_variant_recipe.make(service=service_2, price=100)
        service_variant_payment_recipe.make(
            service_variant=service_variant_2,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('30'),  # 30%
            saving_type=ServiceVariantPayment.PERCENTAGE,
        )
        response = self.client.get(self.url)
        self.assertEqual(response.json().get('splash'), None)

    @override_eppo_feature_flag({HigherPrepaymentSplashFlag.flag_name: {'due_date': '31.12.2024'}})
    def test_dont_show_when_prepayment_rate_too_high(self):
        service = service_recipe.make(business=self.business, active=True)
        service_variant = service_variant_recipe.make(service=service, price=100)
        service_variant_payment_recipe.make(
            service_variant=service_variant,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('51'),  # 51%
            saving_type=ServiceVariantPayment.PERCENTAGE,
        )
        response = self.client.get(self.url)
        self.assertEqual(response.json().get('splash'), None)

    @override_eppo_feature_flag({HigherPrepaymentSplashFlag.flag_name: {'due_date': '31.12.2024'}})
    def test_show_when_prepayment_rate_ok(self):
        service = service_recipe.make(business=self.business, active=True)
        service_variant = service_variant_recipe.make(service=service, price=100)
        service_variant_payment_recipe.make(
            service_variant=service_variant,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('50'),  # 50%
            saving_type=ServiceVariantPayment.PERCENTAGE,
        )
        response = self.client.get(self.url)
        self.assertEqual(
            response.json(),
            {
                'additional_data': {
                    'current_prepayment_level': 50,
                    'due_date': '31.12.2024',
                    'suggested_prepayment_level': 75,
                },
                'splash': 'SPLASH_HIGHER_PREPAYMENT',
            },
        )


@pytest.mark.django_db
class TestKeepPrepaymentSplash(BusinessOwnerAPITestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.business = business_recipe.make(
            public_email='<EMAIL>',
        )
        cls.user = cls.business.owner
        pos_recipe.make(business=cls.business)
        cls.url = reverse(
            'instant_splash',
            kwargs={
                'business_pk': cls.business.id,
            },
        )

    def test_dont_show_when_empty_flag(self):
        response = self.client.get(self.url)
        self.assertEqual(response.json().get('splash'), None)

    @override_eppo_feature_flag({KeepPrepaymentSplashFlag.flag_name: True})
    def test_dont_show_when_no_prepayments_setup(self):
        response = self.client.get(self.url)
        self.assertEqual(response.json().get('splash'), None)

    @override_eppo_feature_flag({KeepPrepaymentSplashFlag.flag_name: True})
    def test_dont_show_when_multiple_prepayment_rates(self):
        service_1 = service_recipe.make(business=self.business, active=True)
        service_variant_1 = service_variant_recipe.make(service=service_1, price=100)
        service_variant_payment_recipe.make(
            service_variant=service_variant_1,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('40'),  # 40%
            saving_type=ServiceVariantPayment.PERCENTAGE,
        )

        service_2 = service_recipe.make(business=self.business, active=True)
        service_variant_2 = service_variant_recipe.make(service=service_2, price=100)
        service_variant_payment_recipe.make(
            service_variant=service_variant_2,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('30'),  # 30%
            saving_type=ServiceVariantPayment.PERCENTAGE,
        )
        response = self.client.get(self.url)
        self.assertEqual(response.json().get('splash'), None)

    @override_eppo_feature_flag({KeepPrepaymentSplashFlag.flag_name: True})
    def test_show_when_prepayment_rate_ok(self):
        service = service_recipe.make(business=self.business, active=True)
        service_variant = service_variant_recipe.make(service=service, price=100)
        service_variant_payment_recipe.make(
            service_variant=service_variant,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('50'),  # 50%
            saving_type=ServiceVariantPayment.PERCENTAGE,
        )
        HigherPrepaymentSplashDecision.objects.create(
            pos=self.business.pos, decision=HigherPrepaymentSplashDecision.Decision.DATE
        )

        response = self.client.get(self.url)
        self.assertEqual(
            response.json(),
            {
                'additional_data': {
                    'current_prepayment_level': 50,
                    'suggested_prepayment_level': 75,
                },
                'splash': 'SPLASH_KEEP_PREPAYMENT',
            },
        )


@pytest.mark.django_db
class TestChangePrepaymentView(BusinessOwnerAPITestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.business = business_recipe.make(
            public_email='<EMAIL>',
        )
        cls.user = cls.business.owner
        pos_recipe.make(business=cls.business)
        cls.url = reverse(
            'increase_prepayment_splash',
            kwargs={
                'business_pk': cls.business.id,
            },
        )

    def test_post_ok(self):
        service_pp = service_recipe.make(business=self.business, active=True)
        service_variant_pp = service_variant_recipe.make(service=service_pp, price=100)
        svp_pp = service_variant_payment_recipe.make(
            service_variant=service_variant_pp,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('50'),  # 50%
            saving_type=ServiceVariantPayment.PERCENTAGE,
        )
        service_cf = service_recipe.make(business=self.business, active=True)
        service_variant_cf = service_variant_recipe.make(service=service_cf, price=100)
        svp_cf = service_variant_payment_recipe.make(
            service_variant=service_variant_cf,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('50'),  # 50%
            saving_type=ServiceVariantPayment.PERCENTAGE,
        )

        response = self.client.post(self.url, data={'rate': 60})

        self.assertEqual(response.status_code, 200)
        svp_pp.refresh_from_db()
        self.assertEqual(svp_pp.payment_amount, 60)
        svp_cf.refresh_from_db()
        self.assertEqual(svp_cf.payment_amount, 50)  # CF should be left untouched

    def test_post_amount_too_low_error(self):
        service_pp = service_recipe.make(business=self.business, active=True)
        service_variant_pp = service_variant_recipe.make(service=service_pp, price=100)
        svp_pp = service_variant_payment_recipe.make(
            service_variant=service_variant_pp,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('50'),  # 50%
            saving_type=ServiceVariantPayment.PERCENTAGE,
        )
        service_cf = service_recipe.make(business=self.business, active=True)
        service_variant_cf = service_variant_recipe.make(service=service_cf, price=100)
        svp_cf = service_variant_payment_recipe.make(
            service_variant=service_variant_cf,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('50'),  # 50%
            saving_type=ServiceVariantPayment.PERCENTAGE,
        )

        response = self.client.post(self.url, data={'rate': 3})

        self.assertEqual(response.status_code, 400)
        # no changes
        svp_pp.refresh_from_db()
        self.assertEqual(svp_pp.payment_amount, 50)
        svp_cf.refresh_from_db()
        self.assertEqual(svp_pp.payment_amount, 50)


@pytest.mark.django_db
class TestBusinessBooksyPayOnboardingCalendarSplash(BusinessOwnerAPITestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.business = business_recipe.make(
            public_email='<EMAIL>',
        )
        cls.user = cls.business.owner
        cls.pos = pos_recipe.make(business=cls.business)
        cls.splash_url = reverse(
            'instant_splash',
            kwargs={
                'business_pk': cls.business.id,
            },
        )

    @patch(
        'webapps.business.models.Business.booksy_pay_available',
        new_callable=PropertyMock,
        return_value=True,
    )
    @override_eppo_feature_flag({BusinessBooksyPayOnboardingFlag.flag_name: True})
    @patch(
        'drf_api.service.splash.views.calendar_splash.BusinessBooksyPayOnboardingCompatibility',
        return_value=True,
    )
    def test_bp_splash(self, boooksy_pay_available_mock, booksy_pay_compatibility_mock):
        # no splash if flag is set to False
        with override_eppo_feature_flag({BusinessBooksyPayOnboardingFlag.flag_name: False}):
            response = self.client.get(self.splash_url)
            self.assertIsNone(response.json().get('splash'))

        # no splash if BP not available
        boooksy_pay_available_mock.return_value = False
        response = self.client.get(self.splash_url)
        self.assertIsNone(response.json().get('splash'))

        boooksy_pay_available_mock.return_value = True
        response = self.client.get(self.splash_url)
        self.assertEqual(response.json().get('splash'), SplashType.SPLASH_PX_BOOKSY_PAY_ONBOARDING)

        response = self.client.get(self.splash_url)
        # no second splash, ever
        self.assertIsNone(response.json().get('splash'))


@override_eppo_feature_flag({BooksyPayActivationScreenFlag.flag_name: 'variant_a'})
@pytest.mark.django_db
class TestBPActivationCalendarSplash(BusinessOwnerAPITestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.business = business_recipe.make(
            public_email='<EMAIL>',
        )
        cls.user = cls.business.owner
        cls.pos = pos_recipe.make(business=cls.business)
        cls.url = reverse(
            'instant_splash',
            kwargs={
                'business_pk': cls.business.id,
            },
        )

    @override_settings(POS__BOOKSY_PAY=False)
    def test_dont_show_when_booksy_pay_disabled(self):
        response = self.client.get(self.url)
        self.assertEqual(response.json().get('splash'), None)

    @override_settings(POS__BOOKSY_PAY=True)
    @override_eppo_feature_flag({BooksyPayActivationScreenFlag.flag_name: ''})
    def test_dont_show_when_feature_flag_returns_empty_string(self):
        response = self.client.get(self.url)
        self.assertEqual(response.json().get('splash'), None)

    @override_settings(POS__BOOKSY_PAY=True)
    def test_dont_show_when_user_not_owner(self):
        staff_user = baker.make('user.User')
        staffer_recipe.make(
            business=self.business,
            staff_user=staff_user,
            staff_access_level=Resource.STAFF,
        )

        self.client.force_authenticate(user=staff_user)
        response = self.client.get(self.url)
        self.assertEqual(response.json().get('splash'), None)

    @override_settings(POS__BOOKSY_PAY=True)
    @patch('webapps.booksy_pay.services.settings.BooksyPaySettingsService.get_settings')
    def test_dont_show_when_bp_settings_none(self, mock_get_settings):
        mock_get_settings.return_value = None

        response = self.client.get(self.url)
        self.assertEqual(response.json().get('splash'), None)
        mock_get_settings.assert_called_once_with(pos_id=self.pos.id)

    @override_settings(POS__BOOKSY_PAY=True)
    @patch('webapps.booksy_pay.services.settings.BooksyPaySettingsService.get_settings')
    def test_dont_show_when_bp_not_allowed(self, mock_get_settings):
        mock_settings = Mock()
        mock_settings.allowed = False
        mock_settings.enabled = False
        mock_get_settings.return_value = mock_settings

        response = self.client.get(self.url)
        self.assertEqual(response.json().get('splash'), None)
        mock_get_settings.assert_called_once_with(pos_id=self.pos.id)

    @override_settings(POS__BOOKSY_PAY=True)
    @patch('webapps.booksy_pay.services.settings.BooksyPaySettingsService.get_settings')
    def test_dont_show_when_bp_already_enabled(self, mock_get_settings):
        mock_settings = Mock()
        mock_settings.allowed = True
        mock_settings.enabled = True
        mock_get_settings.return_value = mock_settings

        response = self.client.get(self.url)
        self.assertEqual(response.json().get('splash'), None)
        mock_get_settings.assert_called_once_with(pos_id=self.pos.id)

    @override_settings(POS__BOOKSY_PAY=True)
    @patch('webapps.booksy_pay.services.settings.BooksyPaySettingsService.get_settings')
    def test_dont_show_when_already_shown_twice(self, mock_get_settings):
        mock_settings = Mock()
        mock_settings.allowed = True
        mock_settings.enabled = False
        mock_get_settings.return_value = mock_settings

        BPActivationSplash.objects.create(business=self.business, operator=self.user)
        BPActivationSplash.objects.create(business=self.business, operator=self.user)

        response = self.client.get(self.url)
        self.assertEqual(response.json().get('splash'), None)
        mock_get_settings.assert_called_once_with(pos_id=self.pos.id)

    @override_settings(POS__BOOKSY_PAY=True)
    @patch('webapps.booksy_pay.services.settings.BooksyPaySettingsService.get_settings')
    @freeze_time('2024-01-02 12:00:00')
    def test_dont_show_when_shown_within_last_day(self, mock_get_settings):
        mock_settings = Mock()
        mock_settings.allowed = True
        mock_settings.enabled = False
        mock_get_settings.return_value = mock_settings

        with freeze_time('2024-01-01 13:00:00'):
            BPActivationSplash.objects.create(business=self.business, operator=self.user)

        response = self.client.get(self.url)
        self.assertEqual(response.json().get('splash'), None)
        mock_get_settings.assert_called_once_with(pos_id=self.pos.id)

    @override_settings(POS__BOOKSY_PAY=True)
    @patch('webapps.booksy_pay.services.settings.BooksyPaySettingsService.get_settings')
    @freeze_time('2024-01-03 12:00:00')
    def test_show_splash_successfully(self, mock_get_settings):
        mock_settings = Mock()
        mock_settings.allowed = True
        mock_settings.enabled = False
        mock_get_settings.return_value = mock_settings

        with freeze_time('2024-01-01 11:00:00'):
            BPActivationSplash.objects.create(business=self.business, operator=self.user)

        initial_count = BPActivationSplash.objects.filter(business=self.business).count()
        self.assertEqual(initial_count, 1)

        response = self.client.get(self.url)

        self.assertEqual(response.json().get('splash'), SplashType.SPLASH_PX_BP_ACTIVATION.value)

        final_count = BPActivationSplash.objects.filter(business=self.business).count()
        self.assertEqual(final_count, 2)

        new_splash = BPActivationSplash.objects.filter(business=self.business).latest('id')
        self.assertEqual(new_splash.business, self.business)
        self.assertEqual(new_splash.operator, self.user)

        mock_get_settings.assert_called_once_with(pos_id=self.pos.id)

    @override_settings(POS__BOOKSY_PAY=True)
    @patch('webapps.booksy_pay.services.settings.BooksyPaySettingsService.get_settings')
    def test_show_splash_when_no_previous_splashes(self, mock_get_settings):
        mock_settings = Mock()
        mock_settings.allowed = True
        mock_settings.enabled = False
        mock_get_settings.return_value = mock_settings

        initial_count = BPActivationSplash.objects.filter(business=self.business).count()
        self.assertEqual(initial_count, 0)

        response = self.client.get(self.url)

        self.assertEqual(response.json().get('splash'), SplashType.SPLASH_PX_BP_ACTIVATION.value)

        final_count = BPActivationSplash.objects.filter(business=self.business).count()
        self.assertEqual(final_count, 1)

        splash = BPActivationSplash.objects.get(business=self.business)
        self.assertEqual(splash.business, self.business)
        self.assertEqual(splash.operator, self.user)

        mock_get_settings.assert_called_once_with(pos_id=self.pos.id)


@pytest.mark.django_db
class TestBoostPreSuspensionWarningSplash(BusinessOwnerAPITestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.business = boosted_business_recipe.make()
        cls.user = cls.business.owner
        cls.url = reverse(
            'instant_splash',
            kwargs={
                'business_pk': cls.business.id,
            },
        )

    @override_settings(BOOST=BoostConfig(COUNTRY='pl', ENABLED=True, BANS_ENABLED=True))
    @override_eppo_feature_flag({BoostPreSuspensionWarningBackendFlag.flag_name: True})
    def test_dont_show_when_no_warning(self):
        response = self.client.get(self.url)
        self.assertEqual(response.json().get('splash'), None)

    @override_settings(BOOST=BoostConfig(COUNTRY='pl', ENABLED=True, BANS_ENABLED=True))
    @override_eppo_feature_flag({BoostPreSuspensionWarningBackendFlag.flag_name: True})
    def test_dont_show_when_warning_is_not_visible(self):
        baker.make(
            BoostFraudSuspicion,
            business_id=self.business.id,
            warning_visible=False,
        )
        response = self.client.get(self.url)
        self.assertEqual(response.json().get('splash'), None)

    @override_settings(BOOST=BoostConfig(COUNTRY='pl', ENABLED=True, BANS_ENABLED=True))
    @override_eppo_feature_flag({BoostPreSuspensionWarningBackendFlag.flag_name: True})
    def test_dont_show_when_banned(self):
        baker.make(
            BoostFraudSuspicion,
            business_id=self.business.id,
            warning_visible=True,
        )
        baker.make(
            BoostBan,
            business=self.business,
            type=BoostBanType.SUSPENSION,
            status=BoostBanStatus.ACTIVE,
        )
        response = self.client.get(self.url)
        self.assertEqual(response.json().get('splash'), None)

    @override_settings(BOOST=BoostConfig(COUNTRY='pl', ENABLED=True, BANS_ENABLED=True))
    @override_eppo_feature_flag({BoostPreSuspensionWarningBackendFlag.flag_name: False})
    def test_dont_show_when_ff_disabled(self):
        baker.make(
            BoostFraudSuspicion,
            business_id=self.business.id,
            warning_visible=True,
        )
        response = self.client.get(self.url)
        self.assertEqual(response.json().get('splash'), None)

    @override_settings(BOOST=BoostConfig(COUNTRY='pl', ENABLED=True, BANS_ENABLED=False))
    @override_eppo_feature_flag({BoostPreSuspensionWarningBackendFlag.flag_name: True})
    def test_dont_show_when_boost_bans_disabled(self):
        baker.make(
            BoostFraudSuspicion,
            business_id=self.business.id,
            warning_visible=True,
        )
        response = self.client.get(self.url)
        self.assertEqual(response.json().get('splash'), None)

    @override_settings(BOOST=BoostConfig(COUNTRY='pl', ENABLED=True, BANS_ENABLED=True))
    @override_eppo_feature_flag({BoostPreSuspensionWarningBackendFlag.flag_name: True})
    def test_dont_show_when_boost_disabled(self):
        self.business.boost_status = Business.BoostStatus.DISABLED
        self.business.save()

        baker.make(
            BoostFraudSuspicion,
            business_id=self.business.id,
            warning_visible=True,
        )
        response = self.client.get(self.url)
        self.assertEqual(response.json().get('splash'), None)

    @override_settings(BOOST=BoostConfig(COUNTRY='pl', ENABLED=True, BANS_ENABLED=True))
    @override_eppo_feature_flag({BoostPreSuspensionWarningBackendFlag.flag_name: True})
    def test_show(self):
        baker.make(
            BoostFraudSuspicion,
            business_id=self.business.id,
            warning_type=BoostFraudWarningType.PRICE_MANIPULATIONS,
            warning_visible=True,
        )
        response = self.client.get(self.url)
        self.assertEqual(
            response.json(),
            {
                'additional_data': {
                    'boost_terms': f'{settings.FRONTDESK_APP_URL}boost-terms/{settings.API_COUNTRY}.html',
                    'header': FRAUD_PRICE_MANIPULATIONS_TEXTS['splash_header'],
                    'paragraph': FRAUD_PRICE_MANIPULATIONS_TEXTS['splash_paragraph'],
                    'button_label': FRAUD_PRICE_MANIPULATIONS_TEXTS['splash_button_label'],
                    'boost_warning_type': FRAUD_PRICE_MANIPULATIONS_TEXTS['boost_warning_type'],
                },
                'splash': SplashType.SPLASH_BOOST_PRE_SUSPENSION_WARNING.value,
            },
        )


@pytest.mark.django_db
class TestPremiumHoursSplash(BusinessOwnerAPITestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.business = business_recipe.make(
            public_email='<EMAIL>',
        )
        cls.user = cls.business.owner
        cls.url = reverse(
            'instant_splash',
            kwargs={
                'business_pk': cls.business.id,
            },
        )
        cls.variant = service_variant_recipe.make(service__business=cls.business)
        cls.peak_hour_service = PeakHourServiceFactory.get_service()

    @override_eppo_feature_flag({EnablePremiumHoursSplashFlag.flag_name: False})
    def test_dont_show_when_empty_flag(self):
        response = self.client.get(self.url)
        self.assertEqual(response.json().get('splash'), None)

    @override_eppo_feature_flag({EnablePremiumHoursSplashFlag.flag_name: True})
    def test_dont_show_when_using_premium_services(self):
        self.peak_hour_service.enable(
            self.business.id,
            [
                PeakHour(
                    business_id=self.business.id,
                    day_of_week=DayOfWeek.MONDAY,
                    service_variants=[
                        Service(
                            elevation_rate=Decimal(20),
                            hour_from=datetime.time(10, 0),
                            hour_till=datetime.time(14, 0),
                            service_variant_id=self.variant.id,
                        ),
                    ],
                ),
            ],
        )

        response = self.client.get(self.url)
        self.assertEqual(response.json().get('splash'), None)

    @override_eppo_feature_flag({EnablePremiumHoursSplashFlag.flag_name: True})
    def test_show(self):
        response = self.client.get(self.url)
        self.assertEqual(response.json().get('splash'), SplashType.SPLASH_PREMIUM_HOURS.value)

        response = self.client.get(self.url)
        self.assertIsNone(response.json().get('splash'))
