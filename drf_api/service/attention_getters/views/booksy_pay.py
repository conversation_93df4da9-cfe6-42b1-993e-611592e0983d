from bo_obs.datadog.enums import BooksyTeams
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _
from rest_framework import status
from rest_framework.request import Request
from rest_framework.response import Response

from drf_api.service.attention_getters.consts.booksy_pay import (
    AFTER_PAYMENT_ATTENTION_GETTER_NAME_MAPPING,
    NA,
)
from drf_api.service.attention_getters.content.base import AttentionGetter
from drf_api.service.attention_getters.enums.base import AttentionGetterStep
from drf_api.service.attention_getters.enums.booksy_pay import BooksyPayAttentionGetterName
from drf_api.service.attention_getters.serializers.booksy_pay import (
    BooksyPayAttentionGetterQuerySerializer,
    BooksyPayAttentionGetterSerializer,
)
from drf_api.service.attention_getters.utils.booksy_pay import (
    get_booksy_pay_attention_getter,
    get_booksy_pay_attention_getter_name,
)
from drf_api.service.attention_getters.views.base import BaseAttentionGetterView
from lib.db import using_db_for_reads, READ_ONLY_DB
from lib.feature_flag.feature.booksy_pay import (
    BooksyPayCashbackPromoFlag,
)
from service.exceptions import ServiceError
from webapps.booking.appointment_wrapper import AppointmentWrapper
from webapps.booking.enums import AppointmentTypeSM as AT
from webapps.booking.models import Appointment
from webapps.booksy_pay.cashback import (
    BooksyPayCashbackVariant,
    get_booksy_pay_cashback_variant_by_appt,
    get_booksy_pay_cashback_variant_by_type,
)
from webapps.booksy_pay.consts import SECOND_CASHBACK_PROMO_START_DATE
from webapps.booksy_pay.enums import BooksyPayCashbackType
from webapps.booksy_pay.utils import has_finished_booksy_pay_transaction


class BooksyPayAttentionGetterView(BaseAttentionGetterView):
    booksy_teams = (BooksyTeams.PAYMENT_NEXUS,)
    keep_trace = True
    keep_trace_status_codes = {'get': [500]}
    trace_percentage = 100  # Feature in tests - small traffic, hence 100%

    serializer_class = BooksyPayAttentionGetterSerializer
    query_serializer_class = BooksyPayAttentionGetterQuerySerializer

    @using_db_for_reads(READ_ONLY_DB)
    def get(self, request: Request) -> Response:
        return super().get(request)

    @cached_property
    def _appointment(self) -> Appointment:
        appointment_id = self.request.query_params.get('appointment_id')
        if not appointment_id:
            raise ServiceError(
                code=status.HTTP_400_BAD_REQUEST,
                errors=[
                    {
                        'code': 'missing_appointment_id',
                        'description': _('`appointment_id` not provided'),
                    }
                ],
            )

        try:
            appointment_id = int(appointment_id)
        except ValueError as exc:
            raise ServiceError(
                code=status.HTTP_400_BAD_REQUEST,
                errors=[
                    {
                        'code': 'incorrect_appointment_id',
                        'description': _('Incorrect `appointment_id` provided'),
                    }
                ],
            ) from exc

        appointment_wrapper = AppointmentWrapper.get_appointment(
            customer_user_id=self.request.user.id,
            appointment_id=appointment_id,
            appointment_type=AT.SINGLE,
            prefetch_all=False,
        )

        if appointment_wrapper is None:
            appointment_wrapper = AppointmentWrapper.get_appointment(
                customer_user_id=self.request.user.id,
                appointment_id=appointment_id,
                appointment_type=AT.MULTI,
                prefetch_all=False,
            )

        if appointment_wrapper is None:
            raise ServiceError(
                code=status.HTTP_400_BAD_REQUEST,
                errors=[
                    {
                        'code': 'appointment_not_found',
                        'description': _('Appointment not found'),
                    }
                ],
            )

        return appointment_wrapper.appointment

    @cached_property
    def _attention_getter_step(self) -> AttentionGetterStep:
        """
        Return a step (either before_payment, booking_confirmation or after_payment)
        if provided in query params.
        """
        step = self.request.query_params.get('step')

        if not step:
            raise ServiceError(
                code=status.HTTP_400_BAD_REQUEST,
                errors=[
                    {
                        'code': 'missing_step',
                        'description': _('`step` not provided'),
                    }
                ],
            )

        try:
            return AttentionGetterStep(step)
        except ValueError as exc:
            raise ServiceError(
                code=status.HTTP_400_BAD_REQUEST,
                errors=[
                    {
                        'code': 'incorrect_step',
                        'description': _('Incorrect `step` provided'),
                    }
                ],
            ) from exc

    @cached_property
    def _cashback_variant(self) -> BooksyPayCashbackVariant | None:
        """
        NOTE - The final version of cashback is unknown yet. It depends on the results
        of the cashback experiment. Therefore, only the default/initial version
        is supported here for the booking_confirmation step.
        """
        if self._attention_getter_step == AttentionGetterStep.BOOKING_CONFIRMATION:
            # THIS IS JUST A TEMPORARY LOGIC - IT SHOULD BE REMOVED ONCE
            # THE DESIRED VERSION OF CASHBACK IS KNOWN.
            return (
                get_booksy_pay_cashback_variant_by_type(BooksyPayCashbackType.DEFAULT)
                if BooksyPayCashbackPromoFlag()
                else None
            )

        return get_booksy_pay_cashback_variant_by_appt(self._appointment)

    @cached_property
    def _cashback_variant_is_eligible(self) -> bool:
        """
        Moved to a property for caching (to avoid running the same queries multiple times).
        """
        cashback_variant = self._cashback_variant
        appointment = self._appointment

        return bool(cashback_variant and cashback_variant.is_eligible(appointment))

    def _get_attention_getter_name_criteria(self, ignore_appointment_txn: bool = False) -> dict:
        cashback_variant = self._cashback_variant
        appointment = self._appointment

        if (
            cashback_variant
            and cashback_variant.has_received_cashback_already(appointment, ignore_appointment_txn)
            is False
        ):

            cashback_type = cashback_variant.cashback_type
            has_first_booksy_pay_transaction = (
                has_finished_booksy_pay_transaction(
                    appointment=appointment,
                    txn_min_total_value=cashback_variant.min_booking_amount,
                    num_of_txns=1,
                    ignore_appointment_txn=ignore_appointment_txn,
                    created_gte=SECOND_CASHBACK_PROMO_START_DATE,
                )
                if cashback_type == BooksyPayCashbackType.VARIANT_C
                else NA
            )
        else:
            cashback_type = None
            has_first_booksy_pay_transaction = NA

        # `ignore_appointment_txn` indicates it's a query for an attention getter shown before
        # a BP payment, which needs to be determined on the after payment step. And given that it's
        # the after payment step, the window must have been opened before as a transaction couldn't
        # be processed otherwise. Therefore, it's always set to `True` for the after_payment step
        # to be able to retrieve the attention getter shown previously.
        is_booksy_pay_payment_window_open = (
            ignore_appointment_txn or appointment.is_booksy_pay_payment_window_open
        )

        return {
            'cashback_type': cashback_type,
            'is_booksy_pay_payment_window_open': is_booksy_pay_payment_window_open,
            'has_first_booksy_pay_transaction': has_first_booksy_pay_transaction,
        }

    def _get_previous_attention_getter_name(self) -> BooksyPayAttentionGetterName | None:
        """
        Returns an attention getter shown before. It's used to determine the screen that shall
        be shown on the after payment step.
        The same look-up criteria are used - the only difference is that a transaction
        related to a provided appointment is ignored to be able to determine the state
        when the appointment wasn't paid yet.
        """
        attention_getter_name_criteria = self._get_attention_getter_name_criteria(True)
        return get_booksy_pay_attention_getter_name(**attention_getter_name_criteria)

    def _get_attention_getter_name_before_bp_txn(self) -> BooksyPayAttentionGetterName | None:
        """
        Note:
             no BP attention getter should be shown if BP is not available. It's important
             to check BP availability before payment.
        """
        if not self._appointment.is_booksy_pay_available:
            return None

        attention_getter_name_criteria = self._get_attention_getter_name_criteria()
        return get_booksy_pay_attention_getter_name(**attention_getter_name_criteria)

    def _get_attention_getter_name_booking_confirmation(
        self,
    ) -> BooksyPayAttentionGetterName | None:
        return self._get_attention_getter_name_before_bp_txn()

    def _get_attention_getter_name_before_payment(self) -> BooksyPayAttentionGetterName | None:
        return self._get_attention_getter_name_before_bp_txn()

    def _get_attention_getter_name_after_payment(self) -> BooksyPayAttentionGetterName | None:
        """
        Important:
        if a Cx has not received cashback yet, the cashback attention getter shall be shown:
        * on the before_payment step: despite cashback eligibility;
        * on the booking_confirmation step: despite cashback eligibility;
        * on the after_payment step: ONLY if a Cx was cashback-eligible before a BP transaction.
        """
        previous_attention_getter_name = self._get_previous_attention_getter_name()
        after_payment_mapping = AFTER_PAYMENT_ATTENTION_GETTER_NAME_MAPPING

        if after_payment_attention_getter_name := after_payment_mapping.get(
            previous_attention_getter_name
        ):
            cashback_variant = self._cashback_variant
            appointment = self._appointment
            was_cashback_eligible_before = bool(
                cashback_variant
                and cashback_variant.is_eligible(
                    appointment=appointment, ignore_appointment_txn=True
                )
            )

            if was_cashback_eligible_before:
                return after_payment_attention_getter_name

        return None

    def get_attention_getter(self, request: Request) -> AttentionGetter | None:
        """
        Note:
        if a cashback variant is found, but a customer is not cashback eligible,
        then a control group content shall be returned by the endpoint.

        Two important rules:
        1. Cx SHALL be shown a cashback screen if he has NOT received cashback yet (no
           matter if eligible).
        2. Cx SHALL NOT be shown a cashback screen if he has received cashback already.
        """
        attention_getter_name_method = {
            AttentionGetterStep.BOOKING_CONFIRMATION: (
                self._get_attention_getter_name_booking_confirmation
            ),
            AttentionGetterStep.BEFORE_PAYMENT: self._get_attention_getter_name_before_payment,
            AttentionGetterStep.AFTER_PAYMENT: self._get_attention_getter_name_after_payment,
        }[self._attention_getter_step]
        attention_getter_name = attention_getter_name_method()

        if attention_getter_name is None:
            # This means an attention getter for the provided step does not exist
            return None

        return get_booksy_pay_attention_getter(self._attention_getter_step, attention_getter_name)

    def get_serializer_context(self):
        context = super().get_serializer_context()

        if not self.request:
            return context

        metadata = {
            'cashback_variant_is_eligible': self._cashback_variant_is_eligible,
            'is_booksy_pay_available': self._appointment.is_booksy_pay_available,
            'is_booksy_pay_payment_window_open': (
                self._appointment.is_booksy_pay_payment_window_open
            ),
            'is_paid_by_booksy_pay': self._appointment.is_paid_by_booksy_pay,
        }
        context.update(
            {
                'metadata': metadata,
                'cashback_type': (
                    self._cashback_variant.cashback_type if self._cashback_variant else None
                ),
            }
        )

        return context
