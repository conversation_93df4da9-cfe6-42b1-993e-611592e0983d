# pylint: disable=too-many-positional-arguments
from decimal import Decimal

import pytest
from dateutil.relativedelta import relativedelta
from django.test import override_settings
from django.urls import reverse
from mock import MagicMock, patch
from model_bakery import baker
from parameterized import parameterized
from rest_framework import status

from drf_api.service.tests.base import AuthenticatedCustomerAPITestCase
from drf_api.lib.base_drf_test_case import BusinessOwnerAPITestCase
from lib.feature_flag.feature.booksy_pay import (
    BooksyPayAvailabilityV2Flag,
    BooksyPayFlag,
    BooksyPayKlarnaImplementationFlag,
)
from lib.point_of_sale.enums import PaymentMethodType
from lib.tests.utils import override_eppo_feature_flag

from lib.tools import (
    relativedelta_to_json_converter,
)
from webapps.booking.models import Appointment, SubBooking
from webapps.booking.tests.utils import create_appointment
from webapps.booksy_pay.models import BooksyPaySettings
from webapps.business.baker_recipes import service_recipe, service_variant_recipe, business_recipe
from webapps.business.enums import PriceType, StaffAccessLevels
from webapps.business.models import Business, Service
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_providers.models.stripe import StripeAccountHolderSettings
from webapps.pos.baker_recipes import posplan_recipe, receipt_recipe, transaction_recipe
from webapps.pos.enums import PaymentTypeEnum, POSPlanPaymentTypeEnum, receipt_status
from webapps.pos.models import PaymentMethod, PaymentRow, Transaction
from webapps.pos.provider.fake import _CARDS
from webapps.pos.baker_recipes import pos_recipe
from webapps.pos.tests.pos_refactor.helpers_stripe import StripeMixin
from webapps.stripe_integration.enums import StripeAccountStatus
from webapps.stripe_integration.models import StripeAccount, StripeCustomer
from webapps.user.enums import AuthOriginEnum


BOOKSY_PAY_NOT_AVAILABLE = 'Booksy Pay is not available'
BOOKSY_PAY_TIME_WINDOW_IS_CLOSED = 'Booksy Pay is allowed only in the specific time window.'


@override_settings(POS__BLIK=True)
@override_eppo_feature_flag({BooksyPayFlag.flag_name: True})
@override_eppo_feature_flag({BooksyPayKlarnaImplementationFlag.flag_name: True})
@patch(
    'webapps.payment_providers.services.stripe.stripe.StripeProvider.create_payment_intent',
    return_value=MagicMock(id='random_id'),
)
@patch(
    'drf_api.service.booksy_pay.serializers.is_booksy_pay_payment_window_open', return_value=True
)
@pytest.mark.django_db
# pylint: disable=R0902
class TestBooksyPayViewSet(StripeMixin, AuthenticatedCustomerAPITestCase):
    def setUp(self):
        super().setUp()
        self.business = baker.make(
            Business,
            booking_mode=Business.BookingMode.AUTO,
            active=True,
            status=Business.Status.PAID,
        )
        self.business.booking_mode = Business.BookingMode.AUTO
        self.business.save()

        self.pos = baker.make(
            'pos.POS',
            business=self.business,
            _force_stripe_pba=True,
        )
        self.bp_settings = baker.make(
            BooksyPaySettings,
            pos=self.pos,
            enabled=True,
            allowed=True,
            late_cancellation_window=relativedelta(hours=8),
        )
        self.business_wallet, _ = PaymentGatewayPort.get_or_create_business_wallet(
            business_id=self.business.id, statement_name='statement name,'
        )
        PaymentGatewayPort.get_or_create_customer_wallet(
            customer_user_id=self.user.id,
            statement_name='statement name,',
            email='<EMAIL>',
            phone='+**********',
        )
        baker.make(
            StripeAccountHolderSettings,
            account_holder_id=self.business_wallet.account_holder_id,
            pba_fees_accepted=False,
        )
        self.stripe_account = baker.make(
            StripeAccount,
            pos=self.pos,
            external_id='test_external_id',
            status=StripeAccountStatus.VERIFIED,
            kyc_verified_at_least_once=True,
        )
        self.posplan = posplan_recipe.make(
            plan_type=POSPlanPaymentTypeEnum.BOOKSY_PAY,
        )
        self.pos.pos_plans.add(self.posplan)
        baker.make('pos.PaymentType', pos=self.pos, code=PaymentTypeEnum.BOOKSY_PAY, default=True)

        baker.make('pos.TaxRate', pos=self.pos, rate=Decimal(7), default_for_service=True)

        price = Decimal('100.00')
        self.service = baker.make(Service, business=self.business)
        self.service_variant = service_variant_recipe.make(
            service=service_recipe.make(business=self.business, tax_rate=None),
            price=price,
            type=PriceType.FIXED,
        )

        self.bci = baker.make(BusinessCustomerInfo, user=self.user)

        self.appointment = create_appointment(
            [
                {'service_variant': self.service_variant},
            ],
            total_type=PriceType.FIXED,
            total_value=price,
            status=Appointment.STATUS.ACCEPTED,
            business=self.business,
        )
        self.appointment.booked_for = self.bci
        self.appointment.save()
        self.booksy_pay_url = self.generate_booksy_pay_url(self.appointment.id)
        self.card = baker.make(
            PaymentMethod,
            provider='fake',
            card_last_digits=_CARDS[0]['last_digits'],
            user=self.user,
        )
        baker.make(StripeCustomer, user=self.user)
        self.headers = {'HTTP_X_API_KEY': self.booking_source.api_key}

        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

    def _get_body(self, payment_method_type: PaymentMethodType = PaymentMethodType.CARD) -> dict:
        if payment_method_type == PaymentTypeEnum.BLIK:
            return {'external_payment_method': {'partner': 'blik', 'token': '123456'}}

        if payment_method_type == PaymentTypeEnum.KLARNA:
            return {'external_payment_method': {'partner': 'klarna'}}

        return {'payment_method': self.card.id}

    @staticmethod
    def generate_booksy_pay_url(appointment_id, dry_run=False):
        name = 'booksy_pay_dry_run' if dry_run else 'booksy_pay'
        return reverse(name, args=(appointment_id,))

    @parameterized.expand(
        (
            (True,),
            (False,),
        )
    )
    @override_settings(POS__BOOKSY_PAY=True)
    def test_retrieve(self, is_time_window_open_mock, create_payment_intent_mock, v2_flag):
        is_time_window_open_mock.return_value = True

        with override_eppo_feature_flag({BooksyPayAvailabilityV2Flag.flag_name: v2_flag}):
            booksy_pay_url = self.generate_booksy_pay_url(self.appointment.id)
            response = self.client.get(booksy_pay_url, **self.headers)

            self.assertEqual(response.status_code, status.HTTP_200_OK)
            response_data = response.json()
            self.assertEqual(response_data['appointment_id'], self.appointment.id)
            self.assertEqual(response_data['appointment_uid'], self.appointment.id)
            self.assertTrue(response_data['availability_info']['available'])
            self.assertTrue(response_data['availability_info']['payment_window_open'])
            self.assertFalse(response_data['payment_info']['is_paid'])
            self.assertFalse(response_data['payment_info']['refundable'])
            self.assertFalse(response_data['payment_info']['is_auto_refund_possible'])
            self.assertEqual(
                response_data['payment_info']['late_cancellation_window'],
                {'hours': 8},
            )

    @override_settings(POS__BOOKSY_PAY=True)
    def test_booksy_pay_dry_run(self, is_time_window_open_mock, create_payment_intent_mock):
        is_time_window_open_mock.return_value = True
        booksy_pay_url = self.generate_booksy_pay_url(self.appointment.id, dry_run=True)
        response = self.client.post(booksy_pay_url, **self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(PaymentRow.objects.count(), 0)

    @parameterized.expand(
        (
            (True, status.HTTP_201_CREATED, None, 2),
            (False, status.HTTP_400_BAD_REQUEST, BOOKSY_PAY_TIME_WINDOW_IS_CLOSED, 0),
        )
    )
    @override_settings(POS__BOOKSY_PAY=True)
    def test_pay_for_appointment_with_booksy_pay_by_card(
        self,
        is_time_window_open_mock,
        create_payment_intent_mock,
        time_window_open_flag_value,
        expected_status_code,
        expected_error,
        expected_payment_rows_count,
    ):
        is_time_window_open_mock.return_value = time_window_open_flag_value
        response = self.client.post(
            self.booksy_pay_url, data=self._get_body(), **self.headers, format='json'
        )
        self.assertEqual(response.status_code, expected_status_code)
        if expected_error:
            self.assertIn(expected_error, response.json()['errors'][0]['description'])

        self.assertEqual(PaymentRow.objects.count(), expected_payment_rows_count)

    @parameterized.expand(
        (
            (True, status.HTTP_201_CREATED, None, 2),
            (False, status.HTTP_400_BAD_REQUEST, BOOKSY_PAY_TIME_WINDOW_IS_CLOSED, 0),
        )
    )
    @override_settings(POS__BOOKSY_PAY=True)
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    def test_pay_for_appointment_with_booksy_pay_by_blik(
        self,
        time_window_open_flag_value,
        expected_status_code,
        expected_error,
        expected_payment_rows_count,
        confirm_payment_intent_mock,
        is_time_window_open_mock,
        create_payment_intent_mock,
    ):
        external_id = 'random_id'
        self._mock_auth_response(confirm_payment_intent_mock, True, external_id)
        is_time_window_open_mock.return_value = time_window_open_flag_value

        response = self.client.post(
            self.booksy_pay_url,
            data=self._get_body(PaymentMethodType.BLIK),
            **self.headers,
            format='json',
        )
        self.assertEqual(response.status_code, expected_status_code)
        if expected_error:
            self.assertIn(expected_error, response.json()['errors'][0]['description'])
        else:
            self._handle_capture_notification(success=True, external_id=external_id)

        self.assertEqual(PaymentRow.objects.count(), expected_payment_rows_count)

    @parameterized.expand(
        (
            (True, status.HTTP_201_CREATED, None, 2),
            (False, status.HTTP_400_BAD_REQUEST, BOOKSY_PAY_TIME_WINDOW_IS_CLOSED, 0),
        )
    )
    @override_settings(POS__BOOKSY_PAY=True)
    @override_settings(POS__KLARNA=True)
    @override_eppo_feature_flag({BooksyPayKlarnaImplementationFlag.flag_name: True})
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    def test_pay_for_appointment_with_booksy_pay_by_klarna(
        self,
        time_window_open_flag_value,
        expected_status_code,
        expected_error,
        expected_payment_rows_count,
        confirm_payment_intent_mock,
        is_time_window_open_mock,
        create_payment_intent_mock,
    ):
        external_id = 'random_id'
        self._mock_auth_response(confirm_payment_intent_mock, True, external_id)
        is_time_window_open_mock.return_value = time_window_open_flag_value

        response = self.client.post(
            self.booksy_pay_url,
            data=self._get_body(PaymentMethodType.KLARNA),
            **self.headers,
            format='json',
        )
        self.assertEqual(response.status_code, expected_status_code)
        if expected_error:
            self.assertIn(expected_error, response.json()['errors'][0]['description'])
        else:
            self._handle_capture_notification(success=True, external_id=external_id)

        self.assertEqual(PaymentRow.objects.count(), expected_payment_rows_count)

    @override_settings(POS__BOOKSY_PAY=True)
    @override_eppo_feature_flag({BooksyPayFlag.flag_name: False})
    def test_with_flag_set_to_false(self, is_time_window_open, create_payment_intent_mock):
        response = self.client.post(
            self.booksy_pay_url, data=self._get_body(), **self.headers, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertIn('Disabled view by feature flag', response.json()['detail'])

    @override_settings(POS__BOOKSY_PAY=True)
    @override_eppo_feature_flag({BooksyPayKlarnaImplementationFlag.flag_name: False})
    @override_settings(POS__KLARNA=True)
    def test_with_klarna_flag_set_to_false(self, is_time_window_open, create_payment_intent_mock):
        response = self.client.post(
            self.booksy_pay_url,
            data=self._get_body(PaymentMethodType.KLARNA),
            **self.headers,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    @parameterized.expand(
        [
            (
                Appointment.STATUS.ACCEPTED,
                status.HTTP_201_CREATED,
                None,
                2,
                (receipt_status.CALL_FOR_BOOKSY_PAY, receipt_status.BOOKSY_PAY_SUCCESS),
                {'status_code': receipt_status.BOOKSY_PAY_SUCCESS, 'already_paid': 100},
            ),
            (
                Appointment.STATUS.CANCELED,
                status.HTTP_400_BAD_REQUEST,
                BOOKSY_PAY_NOT_AVAILABLE,
                0,
                None,
                None,
            ),
            (
                Appointment.STATUS.DECLINED,
                status.HTTP_400_BAD_REQUEST,
                BOOKSY_PAY_NOT_AVAILABLE,
                0,
                None,
                None,
            ),
            (
                Appointment.STATUS.FINISHED,
                status.HTTP_201_CREATED,
                None,
                2,
                (receipt_status.CALL_FOR_BOOKSY_PAY, receipt_status.BOOKSY_PAY_SUCCESS),
                {'status_code': receipt_status.BOOKSY_PAY_SUCCESS, 'already_paid': 100},
            ),
            (
                Appointment.STATUS.MODIFIED,
                status.HTTP_400_BAD_REQUEST,
                BOOKSY_PAY_NOT_AVAILABLE,
                0,
                None,
                None,
            ),
            (
                Appointment.STATUS.NOSHOW,
                status.HTTP_400_BAD_REQUEST,
                BOOKSY_PAY_NOT_AVAILABLE,
                0,
                None,
                None,
            ),
            (
                Appointment.STATUS.PROPOSED,
                status.HTTP_400_BAD_REQUEST,
                BOOKSY_PAY_NOT_AVAILABLE,
                0,
                None,
                None,
            ),
            (
                Appointment.STATUS.UNCONFIRMED,
                status.HTTP_400_BAD_REQUEST,
                BOOKSY_PAY_NOT_AVAILABLE,
                0,
                None,
                None,
            ),
            (
                Appointment.STATUS.REJECTED,
                status.HTTP_400_BAD_REQUEST,
                BOOKSY_PAY_NOT_AVAILABLE,
                0,
                None,
                None,
            ),
            (
                Appointment.STATUS.UNVERIFIED,
                status.HTTP_400_BAD_REQUEST,
                BOOKSY_PAY_NOT_AVAILABLE,
                0,
                None,
                None,
            ),
            (
                Appointment.STATUS.PENDING_PAYMENT,
                status.HTTP_400_BAD_REQUEST,
                BOOKSY_PAY_NOT_AVAILABLE,
                0,
                None,
                None,
            ),
        ]
    )
    @override_settings(POS__BOOKSY_PAY=True)
    def test_booksy_pay_depends_on_appointment_status(
        self,
        is_time_window_open,
        create_payment_intent_mock,
        appointment_status,
        expected_status_code,
        expected_error,
        expected_payment_rows_count,
        expected_payment_rows_statuses,
        expected_receipt_values,
    ):
        self.appointment.status = appointment_status
        self.appointment.save()
        response = self.client.post(
            self.booksy_pay_url, data=self._get_body(), **self.headers, format='json'
        )
        self.assertEqual(response.status_code, expected_status_code)
        if expected_error:
            self.assertIn(expected_error, response.json()['errors'][0]['description'])

        self.assertEqual(PaymentRow.objects.count(), expected_payment_rows_count)

        if expected_payment_rows_statuses:
            self.assertTupleEqual(
                tuple(PaymentRow.objects.values_list('status', flat=True)),
                expected_payment_rows_statuses,
            )

        if expected_receipt_values:
            receipt = PaymentRow.objects.last().receipt

            for key, value in expected_receipt_values.items():
                assert getattr(receipt, key) == value

    @parameterized.expand(
        [
            (
                PriceType.FIXED,
                status.HTTP_201_CREATED,
                None,
                2,
                (receipt_status.CALL_FOR_BOOKSY_PAY, receipt_status.BOOKSY_PAY_SUCCESS),
                {'status_code': receipt_status.BOOKSY_PAY_SUCCESS, 'already_paid': 100},
            ),
            (
                PriceType.VARIES,
                status.HTTP_400_BAD_REQUEST,
                BOOKSY_PAY_NOT_AVAILABLE,
                0,
                None,
                None,
            ),
            (
                PriceType.DONT_SHOW,
                status.HTTP_400_BAD_REQUEST,
                BOOKSY_PAY_NOT_AVAILABLE,
                0,
                None,
                None,
            ),
            (
                PriceType.FREE,
                status.HTTP_400_BAD_REQUEST,
                BOOKSY_PAY_NOT_AVAILABLE,
                0,
                None,
                None,
            ),
            (
                PriceType.STARTS_AT,
                status.HTTP_400_BAD_REQUEST,
                BOOKSY_PAY_NOT_AVAILABLE,
                0,
                None,
                None,
            ),
        ],
    )
    @override_settings(POS__BOOKSY_PAY=True)
    def test_booksy_pay_depends_on_payment_type(
        self,
        is_time_window_open,
        create_payment_intent_mock,
        total_type,
        expected_status_code,
        expected_error,
        expected_payment_rows_count,
        expected_payment_rows_statuses,
        expected_receipt_values,
    ):
        self.appointment.total_type = total_type
        self.appointment.save()
        response = self.client.post(
            self.booksy_pay_url, data=self._get_body(), **self.headers, format='json'
        )
        self.assertEqual(response.status_code, expected_status_code)
        if expected_error:
            self.assertIn(expected_error, response.json()['errors'][0]['description'])

        self.assertEqual(PaymentRow.objects.count(), expected_payment_rows_count)

        if expected_payment_rows_statuses:
            self.assertTupleEqual(
                tuple(PaymentRow.objects.values_list('status', flat=True)),
                expected_payment_rows_statuses,
            )

        if expected_receipt_values:
            receipt = PaymentRow.objects.last().receipt

            for key, value in expected_receipt_values.items():
                assert getattr(receipt, key) == value

    @override_settings(POS__BOOKSY_PAY=True)
    @patch('webapps.booksy_pay.utils.is_no_show_protection_active', return_value=True)
    def test_pay_booksy_pay_with_no_show_protection(
        self, is_time_window_open, create_payment_intent_mock, is_no_show_protection_active_mock
    ):
        response = self.client.post(
            self.booksy_pay_url, data=self._get_body(), **self.headers, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn(BOOKSY_PAY_NOT_AVAILABLE, response.json()['errors'][0]['description'])

    @override_settings(POS__BOOKSY_PAY=True)
    def test_booksy_pay_with_already_paid_appointment(
        self,
        is_time_window_open,
        create_payment_intent_mock,
    ):
        transaction = transaction_recipe.make(
            appointment=self.appointment,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )
        receipt = receipt_recipe.make(
            transaction=transaction,
            status_code=receipt_status.PAYMENT_SUCCESS,
        )
        transaction.latest_receipt = receipt
        transaction.save()

        response = self.client.post(
            self.booksy_pay_url, data=self._get_body(), **self.headers, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn(BOOKSY_PAY_NOT_AVAILABLE, response.json()['errors'][0]['description'])

    @override_settings(POS__BOOKSY_PAY=True)
    def test_for_appointment_with_tip(
        self,
        is_time_window_open,
        create_payment_intent_mock,
    ):
        body = self._get_body() | {
            "tip": {
                "rate": "5.00",
                "type": "P",
                "label": "No Tip",
                "amount": "$0.00",
                "amount_unformatted": "0.00",
                "selected": False,
                "default": False,
                "disabled": False,
                "main": True,
            }
        }
        response = self.client.post(self.booksy_pay_url, data=body, **self.headers, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTupleEqual(
            tuple(PaymentRow.objects.values_list('status', flat=True)),
            (
                receipt_status.CALL_FOR_BOOKSY_PAY,
                receipt_status.BOOKSY_PAY_SUCCESS,
            ),
        )

    @override_settings(POS__BOOKSY_PAY=True)
    def test_appointment_with_multibooking(self, is_time_window_open, create_payment_intent_mock):
        baker.prepare(
            SubBooking,
            2,
            appointment=self.appointment,
        )

        response = self.client.post(
            self.booksy_pay_url, data=self._get_body(), **self.headers, format='json'
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    @parameterized.expand(
        [
            (
                StripeAccountStatus.VERIFIED,
                status.HTTP_201_CREATED,
                None,
            ),
            (
                StripeAccountStatus.TURNED_OFF,
                status.HTTP_400_BAD_REQUEST,
                BOOKSY_PAY_NOT_AVAILABLE,
            ),
            (
                StripeAccountStatus.VERIFICATION_PENDING,
                status.HTTP_400_BAD_REQUEST,
                BOOKSY_PAY_NOT_AVAILABLE,
            ),
            (
                StripeAccountStatus.NOT_VERIFIED,
                status.HTTP_400_BAD_REQUEST,
                BOOKSY_PAY_NOT_AVAILABLE,
            ),
            (
                StripeAccountStatus.INACTIVE,
                status.HTTP_400_BAD_REQUEST,
                BOOKSY_PAY_NOT_AVAILABLE,
            ),
        ],
    )
    @override_settings(POS__BOOKSY_PAY=True)
    def test_400_if_stripe_account_not_verified(
        self,
        is_time_window_open,
        create_payment_intent_mock,
        tested_status,
        expected_response_code,
        expected_error_message,
    ):
        self.stripe_account.status = tested_status
        self.stripe_account.save(update_fields=['status'])

        response = self.client.post(
            self.booksy_pay_url, data=self._get_body(), **self.headers, format='json'
        )
        self.assertEqual(response.status_code, expected_response_code)
        if expected_error_message:
            self.assertIn(expected_error_message, response.json()['errors'][0]['description'])

    @override_settings(POS__BOOKSY_PAY=True)
    def test_pay_lock(
        self,
        is_time_window_open_mock,
        time_window_open_flag_value,
    ):
        is_time_window_open_mock.return_value = time_window_open_flag_value
        response_1 = self.client.post(
            self.booksy_pay_url, data=self._get_body(), **self.headers, format='json'
        )
        response_2 = self.client.post(
            self.booksy_pay_url, data=self._get_body(), **self.headers, format='json'
        )
        self.assertEqual(response_1.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response_2.status_code, status.HTTP_409_CONFLICT)
        self.assertEqual(response_2.json()['errors'][0]['code'], 'lock_error')

    @override_settings(POS__BOOKSY_PAY=True)
    @override_settings(POS__KLARNA=False)
    def test_klarna_payment_when_klarna_disabled(
        self, is_time_window_open_mock, create_payment_intent_mock
    ):
        is_time_window_open_mock.return_value = True

        response = self.client.post(
            self.booksy_pay_url,
            data=self._get_body(PaymentMethodType.KLARNA),
            **self.headers,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(PaymentRow.objects.count(), 0)

    @override_settings(POS__BOOKSY_PAY=True)
    @override_settings(POS__KLARNA=True)
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    def test_klarna_payment_with_failed_confirmation(
        self,
        confirm_payment_intent_mock,
        is_time_window_open_mock,
        create_payment_intent_mock,
    ):
        external_id = 'random_id'
        self._mock_auth_response(confirm_payment_intent_mock, False, external_id)
        is_time_window_open_mock.return_value = True

        response = self.client.post(
            self.booksy_pay_url,
            data=self._get_body(PaymentMethodType.KLARNA),
            **self.headers,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self._handle_capture_notification(success=False, external_id=external_id)
        self.assertEqual(PaymentRow.objects.count(), 2)

    @override_settings(POS__BOOKSY_PAY=True)
    @override_settings(POS__KLARNA=True)
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    def test_klarna_payment_with_missing_external_payment_method(
        self,
        confirm_payment_intent_mock,
        is_time_window_open_mock,
        create_payment_intent_mock,
    ):
        is_time_window_open_mock.return_value = True

        body = {'external_payment_method': {}}
        response = self.client.post(
            self.booksy_pay_url,
            data=body,
            **self.headers,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    @override_settings(POS__BOOKSY_PAY=True)
    @override_settings(POS__KLARNA=True)
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    def test_klarna_payment_with_wrong_partner(
        self,
        confirm_payment_intent_mock,
        is_time_window_open_mock,
        create_payment_intent_mock,
    ):
        is_time_window_open_mock.return_value = True

        body = {'external_payment_method': {'partner': 'invalid_partner'}}
        response = self.client.post(
            self.booksy_pay_url,
            data=body,
            **self.headers,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    @override_settings(POS__BOOKSY_PAY=True)
    @override_settings(POS__KLARNA=True)
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    def test_klarna_payment_with_tip(
        self,
        confirm_payment_intent_mock,
        is_time_window_open_mock,
        create_payment_intent_mock,
    ):
        external_id = 'random_id'
        self._mock_auth_response(confirm_payment_intent_mock, True, external_id)
        is_time_window_open_mock.return_value = True

        body = self._get_body(PaymentMethodType.KLARNA) | {
            "tip": {
                "rate": "10.00",
                "type": "P",
                "label": "10% Tip",
                "amount": "$10.00",
                "amount_unformatted": "10.00",
                "selected": True,
                "default": False,
                "disabled": False,
                "main": True,
            }
        }
        response = self.client.post(
            self.booksy_pay_url,
            data=body,
            **self.headers,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self._handle_capture_notification(success=True, external_id=external_id)
        self.assertEqual(PaymentRow.objects.count(), 2)

    @override_settings(POS__BOOKSY_PAY=True)
    @override_settings(POS__KLARNA=True)
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    def test_klarna_payment_response_includes_klarna_data(
        self,
        confirm_payment_intent_mock,
        is_time_window_open_mock,
        create_payment_intent_mock,
    ):
        external_id = 'random_id'
        self._mock_auth_response(confirm_payment_intent_mock, True, external_id)
        is_time_window_open_mock.return_value = True

        response = self.client.post(
            self.booksy_pay_url,
            data=self._get_body(PaymentMethodType.KLARNA),
            **self.headers,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        response_data = response.json()

        self.assertIn('appointment', response_data)
        self.assertIn('appointment_payment', response_data)
        self.assertIn('meta', response_data)

    @override_settings(POS__BOOKSY_PAY=True)
    @override_settings(POS__KLARNA=True)
    def test_klarna_payment_dry_run(self, is_time_window_open_mock, create_payment_intent_mock):
        is_time_window_open_mock.return_value = True
        booksy_pay_url = self.generate_booksy_pay_url(self.appointment.id, dry_run=True)

        response = self.client.post(
            booksy_pay_url,
            data=self._get_body(PaymentMethodType.KLARNA),
            **self.headers,
            format='json',
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(PaymentRow.objects.count(), 0)

    @override_settings(POS__BOOKSY_PAY=True)
    @override_settings(POS__KLARNA=True)
    @patch('webapps.payment_providers.services.stripe.stripe.StripeProvider.confirm_payment_intent')
    def test_klarna_payment_with_multibooking(
        self,
        confirm_payment_intent_mock,
        is_time_window_open_mock,
        create_payment_intent_mock,
    ):
        external_id = 'random_id'
        self._mock_auth_response(confirm_payment_intent_mock, True, external_id)
        is_time_window_open_mock.return_value = True

        baker.prepare(SubBooking, 2, appointment=self.appointment)

        response = self.client.post(
            self.booksy_pay_url,
            data=self._get_body(PaymentMethodType.KLARNA),
            **self.headers,
            format='json',
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self._handle_capture_notification(success=True, external_id=external_id)


class TestBooksyPayLateCancellationWindowViewSet(BusinessOwnerAPITestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.business = business_recipe.make(
            public_email='<EMAIL>',
        )
        cls.user = cls.business.owner
        cls.pos = pos_recipe.make(business=cls.business)
        cls.bp_settings = baker.make(
            BooksyPaySettings,
            pos=cls.pos,
            enabled=True,
            allowed=True,
            late_cancellation_window=relativedelta(hours=8),
        )
        cls.url = reverse('booksy_pay_late_cancellation_window', args=(cls.business.id,))
        cls.other_business = business_recipe.make(
            public_email='<EMAIL>',
        )
        cls.second_pos = pos_recipe.make(business=cls.other_business)
        cls.second_business_url = reverse(
            'booksy_pay_late_cancellation_window', args=(cls.other_business.id,)
        )

    def test_endpoint(self):
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        # if particular POS value is None we should return default value
        self.assertEqual(
            response_data['booksy_pay_late_cancellation_window'],
            {'label': "8 hours before the appointment", "value": {'hours': 8}},
        )
        self.assertIn(
            {'label': '8 hours before the appointment', 'value': {'hours': 8}},
            response_data['booksy_pay_late_cancellation_window_options'],
        )

        put_data = {
            'booksy_pay_late_cancellation_window': {
                'value': relativedelta_to_json_converter(relativedelta(days=4)),
                'label': "4 days before the appointment",
            }
        }
        response = self.client.put(self.url, data=put_data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()
        self.assertEqual(response_data['booksy_pay_late_cancellation_window']['value'], {'days': 4})
        self.pos.refresh_from_db()
        self.assertEqual(
            self.pos.booksy_pay_settings.late_cancellation_window, relativedelta(days=4)
        )

        # test if wrong data causes 400
        put_data['booksy_pay_late_cancellation_window']['value'] = relativedelta_to_json_converter(
            relativedelta(days=44)
        )
        response = self.client.put(self.url, data=put_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        put_data['booksy_pay_late_cancellation_window'] = relativedelta_to_json_converter(
            relativedelta(days=44)
        )
        response = self.client.put(self.url, data=put_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # OWNER should not be able to see/change other business data
        response = self.client.get(self.second_business_url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        response = self.client.put(self.second_business_url, data=put_data)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        # STAFF should not be able to see or change this value
        self._create_session_for_user(access_level=StaffAccessLevels.STAFF)
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        response = self.client.put(self.url, data=put_data)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)


class TestBooksyPaySettingsViewSet(BusinessOwnerAPITestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.business = business_recipe.make(
            public_email='<EMAIL>',
        )
        cls.user = cls.business.owner
        cls.pos = pos_recipe.make(business=cls.business)
        cls.bp_settings = baker.make(
            BooksyPaySettings,
            pos=cls.pos,
            enabled=True,
            allowed=True,
            late_cancellation_window=relativedelta(hours=12),
        )
        cls.url = reverse('booksy_pay_settings', args=(cls.business.id,))
        cls.other_business = business_recipe.make(
            public_email='<EMAIL>',
        )
        cls.second_pos = pos_recipe.make(business=cls.other_business)
        cls.second_business_url = reverse('booksy_pay_settings', args=(cls.other_business.id,))

    def test_retrieve_success(self):
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()

        self.assertEqual(response_data['pos_id'], self.pos.id)
        self.assertTrue(response_data['enabled'])
        self.assertTrue(response_data['allowed'])
        self.assertEqual(response_data['late_cancellation_window']['value'], {'hours': 12})
        self.assertIn('late_cancellation_window_options', response_data)
        self.assertIn('booksy_pay_status_badge', response_data)

    def test_retrieve_not_found(self):
        business_without_settings = business_recipe.make()
        pos_recipe.make(business=business_without_settings)
        url_without_settings = reverse('booksy_pay_settings', args=(business_without_settings.id,))

        response = self.client.get(url_without_settings)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_retrieve_permission_staff_allowed(self):
        self._create_session_for_user(access_level=StaffAccessLevels.STAFF)
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_retrieve_permission_owner_allowed(self):
        self._create_session_for_user(access_level=StaffAccessLevels.OWNER)
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_retrieve_other_business_not_found(self):
        response = self.client.get(self.second_business_url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_partial_update_success(self):
        update_data = {
            'enabled': False,
            'late_cancellation_window': {
                'value': relativedelta_to_json_converter(relativedelta(hours=12)),
                'label': "12 hours before the appointment",
            },
        }

        response = self.client.patch(self.url, data=update_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()

        self.assertFalse(response_data['enabled'])
        self.assertEqual(response_data['late_cancellation_window']['value'], {'hours': 12})

        self.bp_settings.refresh_from_db()
        self.assertFalse(self.bp_settings.enabled)
        self.assertEqual(self.bp_settings.late_cancellation_window, relativedelta(hours=12))

    def test_partial_update_enabled_only(self):
        update_data = {
            'enabled': False,
        }

        response = self.client.patch(self.url, data=update_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()

        self.assertFalse(response_data['enabled'])
        self.assertTrue(response_data['allowed'])  # Should remain unchanged
        self.assertEqual(
            response_data['late_cancellation_window']['value'],
            {'hours': 12},  # Should remain unchanged
        )

    def test_partial_update_late_cancellation_window_only(self):
        update_data = {
            'late_cancellation_window': {
                'value': relativedelta_to_json_converter(relativedelta(hours=6)),
                'label': "6 hours before the appointment",
            }
        }

        response = self.client.patch(self.url, data=update_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()

        self.assertTrue(response_data['enabled'])  # Should remain unchanged
        self.assertTrue(response_data['allowed'])  # Should remain unchanged
        self.assertEqual(response_data['late_cancellation_window']['value'], {'hours': 6})

    def test_partial_update_not_found(self):
        business_without_settings = business_recipe.make()
        pos_recipe.make(business=business_without_settings)
        url_without_settings = reverse('booksy_pay_settings', args=(business_without_settings.id,))

        update_data = {'enabled': False}
        response = self.client.patch(url_without_settings, data=update_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_partial_update_permission_owner_required(self):
        self._create_session_for_user(access_level=StaffAccessLevels.STAFF)
        update_data = {'enabled': False}
        response = self.client.patch(self.url, data=update_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

        self._create_session_for_user(access_level=StaffAccessLevels.OWNER)
        response = self.client.patch(self.url, data=update_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_partial_update_other_business_not_found(self):
        update_data = {'enabled': False}
        response = self.client.patch(self.second_business_url, data=update_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_partial_update_empty_data(self):
        response = self.client.patch(self.url, data={}, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        response_data = response.json()
        self.assertTrue(response_data['enabled'])
        self.assertTrue(response_data['allowed'])
        self.assertEqual(response_data['late_cancellation_window']['value'], {'hours': 12})

    @patch('webapps.booksy_pay.services.settings.BooksyPaySettingsService.get_settings')
    def test_retrieve_service_error_handling(self, mock_get_settings):
        mock_get_settings.return_value = None

        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_retrieve_response_structure(self):
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response_data = response.json()

        required_fields = [
            'pos_id',
            'enabled',
            'allowed',
            'late_cancellation_window',
            'late_cancellation_window_options',
            'booksy_pay_status_badge',
        ]
        for field in required_fields:
            self.assertIn(field, response_data)

        self.assertIsInstance(response_data['pos_id'], int)
        self.assertIsInstance(response_data['enabled'], bool)
        self.assertIsInstance(response_data['allowed'], bool)
        self.assertIsInstance(response_data['late_cancellation_window'], dict)
        self.assertIsInstance(response_data['late_cancellation_window_options'], list)
        self.assertIsInstance(response_data['booksy_pay_status_badge'], dict)

        lcw = response_data['late_cancellation_window']
        self.assertIn('value', lcw)
        self.assertIn('label', lcw)

        badge = response_data['booksy_pay_status_badge']
        expected_badge_fields = ['label', 'type', 'leading_icon']
        for field in expected_badge_fields:
            self.assertIn(field, badge)
