import datetime
from datetime import time
from decimal import Decimal

import pytest
from model_bakery import baker
from rest_framework import status

from lib.feature_flag.feature.customer import (
    CancellationFeeCustomerBookingFlowTestFlag,
    CancellationFeeEppoExperimentTest,
)
from lib.tests.utils import override_eppo_feature_flag
from service.booking.tests.create_customer_appointment_tests import (
    AppointmentTestCaseMixin,
)
from service.tests import BaseAsyncHTTPTest
from webapps.booking.tests.test_appointment_base import (
    BaseTestAppointment,
    build_custappt_data,
)
from webapps.business.enums import PriceType
from webapps.business.models import ServiceVariant, ServiceVariantPayment
from webapps.pos.baker_recipes import pos_recipe
from webapps.pos.enums import PaymentTypeEnum
from webapps.pos.models import PaymentType


@pytest.mark.django_db
class TestCancellationFeeEppoExperiment(
    AppointmentTestCaseMixin, BaseAsyncHTTPTest, BaseTestAppointment
):
    def setUp(self):
        super().setUp()
        # Ensure POS exists for the business to compute payment data in dry_run
        self.pos = pos_recipe.make(business=self.business)
        # Enable Pay by App to mirror other appointment tests setup
        baker.make(PaymentType, pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        self.url = self.appointments_url(self.business.id)

    @override_eppo_feature_flag({CancellationFeeEppoExperimentTest.flag_name: True})
    def test_flag_called_with_cancellation_fee_enabled(self):
        # Given a service variant with a cancellation fee
        with_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=datetime.timedelta(minutes=55),
            time_slot_interval=datetime.timedelta(minutes=15),
            type=PriceType.FIXED,
            price=Decimal('17.30'),
        )
        with_deposit.add_staffers([self.staffer])
        baker.make(
            ServiceVariantPayment,
            service_variant=with_deposit,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('10.00'),
            saving_type=ServiceVariantPayment.AMOUNT,
        )

        body = build_custappt_data(
            variant=with_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        body['dry_run'] = True

        # Override the dict flag result; we only assert successful flow under experiment
        with override_eppo_feature_flag({CancellationFeeCustomerBookingFlowTestFlag.flag_name: {}}):
            resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == status.HTTP_201_CREATED

    @override_eppo_feature_flag({CancellationFeeEppoExperimentTest.flag_name: True})
    def test_flag_called_with_cancellation_fee_disabled(self):
        # Given a service variant without any cancellation fee or prepayment
        no_deposit = baker.make(
            ServiceVariant,
            service=self.service,
            duration=datetime.timedelta(minutes=55),
            time_slot_interval=datetime.timedelta(minutes=15),
            type=PriceType.FIXED,
            price=Decimal('17.30'),
        )
        no_deposit.add_staffers([self.staffer])

        body = build_custappt_data(
            variant=no_deposit,
            booked_from=self._dt_from_hour(time(10, 0)),
            staffer=self.staffer,
            recurring=False,
        )
        body['dry_run'] = True

        # Override the dict flag result; we only assert successful flow under experiment
        with override_eppo_feature_flag({CancellationFeeCustomerBookingFlowTestFlag.flag_name: {}}):
            resp = self.fetch(self.url, method='POST', body=body)

        assert resp.code == status.HTTP_201_CREATED


