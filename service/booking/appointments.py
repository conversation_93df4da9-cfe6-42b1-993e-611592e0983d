import datetime
import json
import logging
from decimal import Decimal
from typing import Any, cast

import tornado.web
from bo_obs.datadog.enums import BooksyTeams, DatadogOperationNames, DatadogCustomServices
from bo_obs.datadog.mixins import set_apm_tag_in_current_span, MANUAL_KEEP_KEY


from ddtrace import tracer
from django.conf import settings
from django.db.models import Q
from django.db.transaction import atomic
from django.utils.translation import gettext as _
from rest_framework import status

import lib.tools
from lib.db import READ_ONLY_DB, using_db_for_reads
from lib.feature_flag.adapter import UserData
from lib.feature_flag.feature.customer import (
    CancellationFeeCustomerBookingFlowTestFlag,
    CancellationFeeEppoExperimentTest,
)
from lib.feature_flag.enums import AppDomains, SubjectType, CustomUserAttributes
from lib.feature_flag.feature.analytics import (
    BranchIOCustomerBookingTrackingFlag,
    FacebookAnalyticsCustomerBookingTrackingFlag,
    FifthCustomerBookingInFourteenDaysSegment,
)
from lib.feature_flag.feature.booking import DecoupledDryRunFromAPI
from lib.feature_flag.feature.payment import (  # pylint: disable=no-name-in-module
    PrepaymentsForBusinessAppointmentEnabled,
    ThreeDSecureInAppointmentEditionFlag,
)
from lib.invite import CustomerContactDetails, invite_customer
from lib.payment_providers.entities import DeviceDataEntity
from lib.pos.utils import txn_refactor_stage2_enabled
from lib.tools import get_meta_data_from_handler, sget, tznow
from service.booking.doers.create_customer_appointment import (
    BussinessDoesNotExistError,
    CreateCustomerAppointment,
    CreateCustomerAppointmentCommand,
    CreateCustomerAppointmentResult,
)
from service.booking.exceptions import (
    AppointmentProcessingFinishedException,
    AppointmentProcessingFinishedExceptionWithResponse,
)
from service.booking.experiment_cx_incentives_task import (
    send_experiment_event_if_applicable,
)
from service.booking.partner_payment import PartnerPaymentStatus
from service.booking.save_appointment_data import SaveAppointmentData
from service.booking.save_appointment_forward import SaveAppointmentForward
from service.booking.serializers import CustomerAppointmentClaimSerializer
from service.booking.tools import (
    AppointmentData,
    get_vital_summary,
    get_vital_summary_appointment,
    should_run_reschedule_scenario,
)
from service.other.redirects import VisitRedirectHandler
from service.partner_forward import partner_forward
from service.tools import (
    AnalyticsTokensMixin,
    DryRunMixin,
    HTTPErrorWithCode,
    RequestHandler,
    json_request,
    session,
)
from webapps.adyen.exceptions import ThreeDSecureAuthenticationRequired
from webapps.booking.appointment_wrapper import AppointmentWrapper
from webapps.booking.enums import AppointmentStatus, AppointmentTypeSM as AT
from webapps.booking.events import (
    appointment_changed_by_business_event,
    appointment_changed_by_customer_event,
    business_appointment_created_event,
    customer_appointment_created_event,
)
from webapps.booking.models import Appointment, BookingChange, SubBooking

from webapps.booking.no_show_protection.business_appointment.validation_rules.validation_rule import (  # pylint: disable=line-too-long
    ValidationError as NSPBusinessAppointmentValidationError,
)
from webapps.booking.serializers.appointment import (
    AnalyticsCustomerAppointmentSerializer,
    AppointmentSerializer,
    CreateAnalyticsCustomerAppointmentSerializer,
    CustomerAppointmentSerializer,
    get_appointment_serializer,
    ExternalPaymentMethodSerializer,
    ExternalPaymentMethod,
)
from webapps.booking.serializers.booking import CalendarBookingSerializer
from webapps.booking.serializers.business_book_again import (
    BookAgainAppointmentSerializer,
)
from webapps.booking.serializers.drag import DragBookingSerializer
from webapps.booking.tasks import (
    appointment_analytics_task,
    post_business_update_appointment_task,
    update_any_mobile_customer_appointments_task,
    update_bci_service_questions_task,
)
from webapps.booksy_med.utils import is_medical_consent_required
from webapps.business.context import business_context
from webapps.business.models import Business, Resource
from webapps.business.searchables.serializers import BCIWithLastAppointmentHitSerializer
from webapps.business_customer_info.bci_merge import ClaimMixin
from webapps.consents.events import maybe_send_sms_with_deeplink
from webapps.consents.utils import check_send_consent_form_sms_request
from webapps.df_creator.marketing_actions import first_cb
from webapps.family_and_friends.factory import (
    create_member_transaction_for_parent_for_family_and_friends_appointment,
)
from webapps.family_and_friends.helpers.appointment import get_user_id_creator
from webapps.feeds.google.tasks import notify_conversion_task
from webapps.kill_switch.models import KillSwitch
from webapps.notification.scenarios import start_scenario
from webapps.notification.scenarios.scenarios_booking import (
    BookingChangedScenario as BCS,
)
from webapps.pos.adapters import (
    pay_deposit,
    ExternalPaymentMethod as POSExternalPaymentMethod,
    AppointmentId as POSAppointmentId,
)
from webapps.pos.enums import compatibilities, receipt_status
from webapps.pos.exceptions import BlikActionRequired
from webapps.pos.models import PaymentRowChange, Transaction
from webapps.pos.provider.proxy import ProxyProvider
from webapps.pos.serializers import (
    CustomerAppointmentCheckoutSerializer,
    CustomerAppointmentTransactionSerializer,
    TransactionSerializer,
)
from webapps.pos.tasks import (
    ReleaseDepositOnCancel,
)
from webapps.segment.feature_flags import EnergyCBCreatedForCustomerBranchEventFlag
from webapps.segment.tasks import (
    analytics_cb_created_count_for_business_branchio_task,
    analytics_cb_created_count_in_days_for_business_segment_task,
    analytics_cb_created_for_business_task,
    analytics_cb_created_for_customer_task,
    analytics_energy_cb_created_for_customer_branchio_task,
    segment_api_appointment_booked_task,
    send_analytics_1st_cb_created_for_business_to_facebook,
)
from webapps.segment.utils import spawn_booking_finished_analytics_events
from webapps.user.tasks.business_customer_info import publish_basic_customer_data_changed
from webapps.zoom.models import ZoomMeeting

log_action = logging.getLogger('booksy.actions')
_sms_log = logging.getLogger('booksy.sms_counter')
rollback_logger = logging.getLogger('booksy.adyen_rollback_logger')
feature_flag_logger = logging.getLogger('booksy.feature_flag')


class CreateCustomerAppointmentHandlerError(Exception): ...


class BaseAppointmentHandler(RequestHandler):
    """Base class with unified methods for Customer & Business Appointments."""

    booksy_teams = (BooksyTeams.CUSTOMER_BOOKING,)

    @staticmethod
    def get_appointment(
        appointment_type,
        appointment_id,
        business_id=None,
        customer_user_id=None,
        prefetch_all=True,
    ):
        appointment = AppointmentWrapper.get_appointment(
            appointment_type,
            appointment_id,
            business_id,
            customer_user_id,
            prefetch_all,
        )
        if appointment is None and appointment_type == AT.SINGLE:
            appointment = AppointmentWrapper.get_appointment(
                AT.MULTI,
                appointment_id,
                business_id,
                customer_user_id,
                prefetch_all,
            )
        if appointment is None:
            raise tornado.web.HTTPError(404)
        return appointment

    @staticmethod
    def cancel_transaction_for_cancelled_pending_prepayment_appointment(
        original_status: Appointment.STATUS,
        new_status: Appointment.STATUS,
        log_note: str,
        appointment: AppointmentWrapper,
    ) -> None:
        if not (
            original_status == Appointment.STATUS.PENDING_PAYMENT
            and new_status == Appointment.STATUS.CANCELED
        ):
            return
        transaction = Transaction.objects.by_appointment_id(
            appointment.appointment.id,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        ).last()
        if transaction.latest_receipt.status_code in [
            receipt_status.PREPAYMENT_FAILED,
            receipt_status.PREPAYMENT_AUTHORISATION_FAILED,
        ]:
            return
        if txn_refactor_stage2_enabled(transaction):
            ProxyProvider.cancel_payment(
                payment_row=transaction.latest_receipt.payment_rows.last(),
                log_note='cancel_transaction_for_cancelled_pending_prepayment_appointment',
            )
        # Update transaction status in old POS structure (Adyen + data consistency for Stripe)
        transaction.update_payment_rows(
            receipt_status.PAYMENT_CANCELED,
            log_action=PaymentRowChange.MULTI_ROW_UPDATE,
            log_note=log_note,
        )


class CreateBusinessAppointmentHandler(
    DryRunMixin, BaseAppointmentHandler
):  # pylint: disable=too-many-ancestors

    booksy_teams = (BooksyTeams.CUSTOMER_BOOKING, BooksyTeams.FX_SOLUTIONS)

    @tracer.wrap(DatadogOperationNames.KIP_BA, service=DatadogCustomServices.KIP)
    def _pay_deposit(self, appointment: Appointment) -> None:
        set_apm_tag_in_current_span(MANUAL_KEEP_KEY)
        external_payment_method_serializer = ExternalPaymentMethodSerializer(
            data=self.data.get('external_payment_method', None)
        )
        self.validate_serializer(external_payment_method_serializer)
        external_payment_method = cast(
            ExternalPaymentMethod, external_payment_method_serializer.save()
        )

        pos_external_payment_method = POSExternalPaymentMethod(
            partner=external_payment_method.partner, token=external_payment_method.token
        )
        pos_appointment_id = cast(POSAppointmentId, appointment.id)
        pay_deposit(appointment_id=pos_appointment_id, payment_method=pos_external_payment_method)

    @session(login_required=True)
    @json_request
    def post(self, business_id):
        """New Appointment.

        swagger:
            summary: New Appointment
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business ID
                - name: body
                  type: AppointmentDetails
                  paramType: body
                  description: Appointment details
            type: AppointmentResponse
        """
        self.set_dry_run_from_params()
        business = self.business_with_staffer(business_id)

        serializer = AppointmentSerializer(
            instance=None,
            data=self.data,
            context={
                'business': business,
                'user_staffer': self.user_staffer,
                'single_category': business.is_single_category,
                'user': self.user,
                # values for some attributes
                'type': Appointment.TYPE.BUSINESS,
                'source': self.booking_source,
                'device_data': DeviceDataEntity(
                    device_fingerprint=self.fingerprint,
                    phone_number=self.user.cell_phone,
                    user_agent=self.user_agent,
                    ip=self.forwarded_ip,
                ),
            },
        )
        with business_context(business):
            self.validate_serializer(serializer)
            dry_run = serializer.validated_data['dry_run']
            try:
                appointment = serializer.save()
            except NSPBusinessAppointmentValidationError as e:
                self.finish_with_json(400, {"errors": [{"description": str(e)}]})
                return

        if not dry_run and self.data.get('deposit', None) is not None:
            self._pay_deposit(appointment.appointment)

        ret = {
            'appointment': serializer.data,
            'customer': appointment.booked_for_data(access_level=self.access_level),
        }

        self.finish_with_json(201, ret)
        # POST FINISH STUFF
        # <editor-fold desc="early_finish section">
        # TODO move to celery task
        log_action.info(
            # pylint: disable=consider-using-f-string
            'Create BusinessBooking action.'
            'User {}. Type: {}.Dry run {}'.format(
                self.user.id,
                Appointment.TYPE.BUSINESS,
                serializer.validated_data['dry_run'],
            )
        )
        if serializer.validated_data['dry_run']:
            return

        BookingChange.add(
            appointment,
            changed_by=BookingChange.BY_BUSINESS,
            changed_user=self.user,
            handler=self,
            metadata={
                'reason': 'business_create_appointment',
            },
        )

        _notification_enabled = serializer.validated_data.get('_notification_enabled', True)

        business_appointment_created_event.send(
            appointment.appointment,
            operator_id=self.user.id,
            notification_enabled=_notification_enabled,
        )
        action = None  # only plan a reminder
        if _notification_enabled:
            action = BCS.BUSINESS_CREATED
        if not (
            PrepaymentsForBusinessAppointmentEnabled(
                UserData(custom={CustomUserAttributes.BUSINESS_ID: business.id})
            )
            and appointment.appointment.status == AppointmentStatus.PENDING_PAYMENT
        ):
            start_scenario(
                BCS,
                appointment=appointment,
                action=action,
                skip_email=self.user.email,
            )

        update_bci_service_questions_task.delay(
            appointment.appointment_uid,
            business_id=business.id,
        )

        invite = serializer.validated_data.get('invite', False)
        segment_api_appointment_booked_task.delay(appointment.subbookings[0].id)

        if check_send_consent_form_sms_request(appointment.appointment, business):
            maybe_send_sms_with_deeplink.send(appointment)
        else:
            # handle invite customer
            email = serializer.validated_data.get('customer_email')
            phone = serializer.validated_data.get('customer_phone')
            if invite and _notification_enabled and business.visible and (phone or email):
                if phone:
                    # pylint: disable=consider-using-f-string
                    _sms_log.info('CreateBusinessAppointmentHandler\n {}'.format(json.dumps(phone)))
                invite_customer(
                    CustomerContactDetails(
                        business=business,
                        user=appointment.booked_for and appointment.booked_for.user,
                        email=email,
                        phone=phone,
                        bci_id=ret['customer'].get('_id'),
                        staffer_invite=(
                            self.access_level in Resource.STAFF_ACCESS_LEVELS_NOT_OWNER
                        ),
                    )
                )
        # </editor-fold>


class OldBusinessAppointmentHandler(
    DryRunMixin, BaseAppointmentHandler
):  # pylint: disable=too-many-ancestors

    booksy_teams = (BooksyTeams.CUSTOMER_BOOKING,)

    @session(login_required=True)
    @using_db_for_reads(
        database_name=READ_ONLY_DB,
        condition=lambda: KillSwitch.alive(KillSwitch.System.REPLICA_APPOINTMENT_DETAILS),
    )
    def get(self, business_id, appointment_type, appointment_id):
        """DEPRECATED Get Appointment details.

        swagger:
            summary: DEPRECATED Get Appointment details
                     [will be deprecated soon use appointment_uid
                     instead appointment_id and appointment_type]
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business ID
                - name: appointment_type
                  type: integer
                  paramType: path
                  description: Appointment Type (single or multi)
                  enum:
                    - single
                    - multi
                - name: appointment_id
                  type: integer
                  paramType: path
                  description: (Multi-)Booking ID
                - name: stardust
                  description: Compatibility changing behavior of
                      TransactionSerializer
                  type: boolean
                  paramType: query
            type: AppointmentResponse

        """
        business = self.business_with_staffer(business_id)
        appointment = self.get_appointment(
            business_id=business_id,
            appointment_type=appointment_type,
            appointment_id=appointment_id,
        )

        data = self._prepare_get_arguments()

        serializer = get_appointment_serializer(
            instance=appointment,
            context={
                'business': business,
                'access_level': self.access_level,
                'single_category': business.is_single_category,
                compatibilities.COMPATIBILITIES: {
                    compatibilities.STARDUST: data.get(compatibilities.STARDUST, False)
                },
            },
        )

        customer = appointment.booked_for
        parameters = {
            'x-access-token': self.request.headers['X-Access-Token'],
            'business_id': business_id,
        }
        if customer:
            parameters['customer_id'] = customer.id
        if customer and not customer.visible_in_business:
            # deleted_customer
            customer = None
        ret = {
            'appointment': serializer.data,
            'customer': (
                BCIWithLastAppointmentHitSerializer(
                    instance=customer,
                    context={'access_level': self.access_level},
                ).data
                if customer
                else None
            ),
            'physio_visit_redirect': self.__create_physio_visit_redirect(
                appointment=appointment,
                parameters=parameters,
            ),
        }
        self.finish_with_json(200, ret)

    @staticmethod
    def __create_physio_visit_redirect(appointment, parameters):
        if (
            appointment and appointment.business.patient_file_enabled
        ):  # pylint: disable=protected-access
            return VisitRedirectHandler.get_redirect_url(
                redirect_id=appointment._first.id, **parameters  # pylint: disable=protected-access
            )
        return ''

    @session(login_required=True)
    @json_request
    def put(self, business_id, appointment_type, appointment_id):
        """DEPRECATED Edit Appointment.

        swagger:
            summary: DEPRECATED Edit Appointment
                     [will be deprecated soon use appointment_uid
                     instead appointment_id and appointment_type]
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business ID
                - name: appointment_type
                  type: integer
                  paramType: path
                  description: Appointment Type (single or multi)
                  enum:
                    - single
                    - multi
                - name: appointment_id
                  type: integer
                  paramType: path
                  description: (Multi-)Booking ID
                - name: body
                  type: AppointmentDetails
                  paramType: body
                  description: Appointment details
            type: AppointmentResponse
        :swagger
        """
        self.set_dry_run_from_params()
        business = self.business_with_staffer(business_id)
        appointment = self.get_appointment(
            business_id=business_id,
            appointment_type=appointment_type,
            appointment_id=appointment_id,
        )
        if not appointment.check_access(self.user_staffer):
            raise HTTPErrorWithCode(404, reason=_('Access Denied'))

        serializer = AppointmentSerializer(
            instance=appointment,
            data=self.data,
            context={
                'business': business,
                'user_staffer': self.user_staffer,
                'single_category': business.is_single_category,
                'user': self.user,
                # copied from instance:
                # 'type': Appointment.TYPE.BUSINESS,
                # 'source': self.booking_source,
            },
        )
        with business_context(business):
            self.validate_serializer(serializer)

            # remember some data before update
            dry_run = serializer.validated_data['dry_run']
            before = get_vital_summary_appointment(appointment) if not dry_run else None

            appointment = serializer.save()

        # because appointment is not appointment but appointment wrapper...
        spawn_booking_finished_analytics_events(appointment.appointment, self.booking_source.id)

        ret = {
            'appointment': serializer.data,
            'customer': appointment.booked_for_data(access_level=self.access_level),
        }
        log_action.info(
            # pylint: disable=consider-using-f-string
            'Edit BusinessBooking action. '
            'User {}. Type: {}.Dry run {}'.format(
                self.user.id,
                appointment_type,
                serializer.validated_data['dry_run'],
            )
        )
        if not dry_run:
            post_business_update_appointment_task.delay(
                notify=serializer.validated_data['_notify_about_reschedule'],
                appointment_id=appointment.appointment.id,
                before=before,
                metadata=get_meta_data_from_handler(self),
                user_id=self.user.id,
            )

        self.finish_with_json(status.HTTP_200_OK, ret)


class BusinessAppointmentHandler(OldBusinessAppointmentHandler):
    # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.PROVIDER_CALENDAR,)
    force_dry_run_from_path = True

    @session(optional_login=True)
    def get(self, business_id, appointment_uid):
        """Get Appointment details.

        swagger:
            summary: Get Appointment details
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business ID
                - name: appointment_uid
                  type: integer
                  paramType: path
                  description: unique Appointment ID
                - name: stardust
                  description: Compatibility changing behavior of
                      TransactionSerializer
                  type: boolean
                  paramType: query
            type: AppointmentResponse

        """
        return super().get(business_id, AT.MULTI, appointment_uid)

    @session(optional_login=True)
    def put(self, business_id, appointment_uid):
        """Edit Appointment.

        swagger:
            summary: Edit Appointment
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business ID
                - name: appointment_uid
                  type: integer
                  paramType: path
                  description: unique Appointment ID
                - name: body
                  type: AppointmentDetails
                  paramType: body
                  description: Appointment details
            type: AppointmentResponse
        :swagger
        """
        return super().put(business_id, AT.MULTI, appointment_uid)


# pylint: disable=too-many-ancestors
class BookAgainBusinessAppointmentHandler(BaseAppointmentHandler):
    booksy_teams = (BooksyTeams.CUSTOMER_BOOKING,)

    @session(login_required=True)
    def get(self, business_id, appointment_uid):
        """Get Appointment and subbookings details.
        swagger:
            summary: Get appointment and subbookings details.
            type: BookAgainBusinessResponse
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business ID
                - name: appointment_uid
                  type: integer
                  paramType: path
                  description: unique Appointment ID
        :swagger
        """
        appointment = self.get_appointment(
            appointment_type=AT.MULTI,
            appointment_id=appointment_uid,
            business_id=business_id,
        )
        business = self.business_with_staffer(business_id)

        serializer = BookAgainAppointmentSerializer(appointment, context={'business': business})

        self.finish_with_json(200, {'appointment': serializer.data})


# pylint: disable=too-many-ancestors
class CalendarDragSubbookingHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_CALENDAR,)

    @session(login_required=True)
    @json_request
    def put(self, business_id, booking_id):
        """Drag subbooking on calendar.

        swagger:
            summary: Drag subbooking on calendar.
            notes: >
                On calendar you can change the following fields
                of a booking: booked_from, booked_till, staffer, appliance
                <br/>
                Response contains all appointment subbookings.
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business ID
                - name: booking_id
                  type: integer
                  paramType: path
                  description: ID of dragged SubBooking
                - name: body
                  paramType: body
                  type: DragBooking
            type: CalendarBookingsResponse

        """

        business = self.business_with_staffer(business_id)
        qset = SubBooking.objects.filter(
            appointment__business_id=business_id,
        )
        booking = self.get_object_or_404(
            qset,
            id=booking_id,
            __select_related=['appointment'],
        )
        self._validate_endpoint(booking)

        if not booking.check_access(self.user_staffer):
            raise HTTPErrorWithCode(404, reason=_('Access Denied'))

        # remember some data before update
        before = get_vital_summary(booking)

        # enable business_context for matcher
        with business_context(business):
            serializer = DragBookingSerializer(
                instance=booking,
                data=self.data,
                context={
                    'business': business,
                    'access_level': self.access_level,
                    'single_category': business.is_single_category,
                    'user': self.user,
                    'device_fingerprint': self.fingerprint,
                    'cell_phone': self.user.cell_phone if self.user else '',
                },
            )
            self.validate_serializer(serializer)

        # as of now, history operations cannot be executed within business_context:
        # they suppose UTC timezone. Shouldn't they use business timezone insteed?

        with atomic(), business_context(business):
            serializer.save()
            booking = serializer.instance  # refresh booking
            TransactionSerializer.update_transaction_with_booking(
                booking.appointment,
                ignore_analytics=True,
            )

            # SubBooking has been moved, if necessary it should get disjoint
            # from the previous group meeting and joint to new group meeting
            zoom_meeting = booking.appointment.get_zoom_meeting()
            if zoom_meeting:
                zoom_meeting.delete()
                booking.appointment.refresh_zoom_meeting()
            ZoomMeeting.create_meeting_for_booking_if_necessary(booking)

        # BOOKING_PROPOSED logic
        after = get_vital_summary(booking)
        notify_about_reschedule = serializer.validated_data['_notify_about_reschedule']

        # inform separately about notify and reschedule
        rescheduled = should_run_reschedule_scenario(before, after, True)

        # FINISH
        ret = self.get_response(business, booking)

        BookingChange.add(
            booking,
            changed_by=BookingChange.BY_BUSINESS,
            changed_user=self.user,
            handler=self,
            metadata={
                'reason': 'business_update',
                'notify_about_reschedule': notify_about_reschedule,
            },
        )

        # handle customer or staff change scenario
        customer_changed = any(
            before[key] != after[key] for key in ['booked_for_id', 'customer_email']
        )
        staffer_changed = before['staff'] != after['staff']

        appointment_changed_by_business_event.send(
            booking.appointment,
            rescheduled=rescheduled,
            notify=notify_about_reschedule,
            customer_changed=customer_changed,
            staffer_changed=staffer_changed,
            operator_id=self.user.id,
        )
        # this updates reminder for customer 24h before
        start_scenario(
            BCS,
            appointment=booking.appointment,
            action=BCS.BUSINESS_CHANGE,
        )

        # handle reschedule scenario
        if rescheduled and notify_about_reschedule:
            start_scenario(
                BCS,
                appointment=booking.appointment,
                action=BCS.BUSINESS_BOOKING_RESCHEDULE_REQUEST,
                notify_about_reschedule=notify_about_reschedule,
            )

        # handle customer or staff change scenario
        for action, change_keys in [
            (
                BCS.BUSINESS_CHANGED_CUSTOMER,
                ['booked_for_id', 'customer_email'],
            ),
            (BCS.BUSINESS_CHANGED_STAFFER, ['staff']),
        ]:
            if any(before[key] != after[key] for key in change_keys):
                start_scenario(BCS, appointment=booking.appointment, action=action)

        self.finish_with_json(200, ret)

    def _validate_endpoint(self, booking: SubBooking):
        self._validate_booking_status(booking)

    @staticmethod
    def _validate_booking_status(booking: SubBooking):
        if booking.appointment.status == Appointment.STATUS.PENDING_PAYMENT:
            raise HTTPErrorWithCode(
                status_code=status.HTTP_400_BAD_REQUEST,
                reason=_('Bookings with pending deposit cannot be modified'),
            )

    def get_response(self, business: Business, booking: SubBooking):
        bookings = booking.combo_parent.combo_children if booking.combo_parent else [booking]
        return {
            'bookings': CalendarBookingSerializer(
                instance=bookings,
                context={
                    'business': business,
                    'staffer': self.user_staffer,
                    'single_category': business.is_single_category,
                },
                many=True,
            ).data
        }


class CreateCustomerAppointmentHandler(
    DryRunMixin,
    BaseAppointmentHandler,
    AnalyticsTokensMixin,
):  # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.CUSTOMER_BOOKING,)
    _create_customer_appointment = CreateCustomerAppointment()

    @session(optional_login=True)
    @json_request
    def post(self, business_id):
        """New Appointment.

        swagger:
            summary: New Appointment
            notes: |
                In dry_run this method prepares and appointment draft. Without dry_run it creates
                the appointment in database. <br>
                Login is optional for dry_run mode and required for appointment creation (403,
                code: unauthorized is called otherwise). <br>
                Dry_run is added as a parameter based on the endpoints.<br>
                Some appointment parameters returned from dry run are used to form input
                for another dry run (after reconfiguration on frontend) or for the final booking:
                appointment_uid, subbookings (simplified), traveling (simplified),
                service_questions, recurring, payment_method, compatibilities,
                book_for_family_member, tip (added to payload as a copy of
                tip_choices from appointment_payment + user choices), rwg_token<br>
                When appointment is 'created from scratch' in the first dry_run then the field
                that are being used are: subbookings (with service_variant (variant & id),
                staffer_id and booked_from (based on timeslot endpoints query)),
                force_incomplete = true. Other fields are either not used / empty or set to default.
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business ID
                - name: body
                  type: CustomerAppointmentDetails
                  paramType: body
                  description: Appointment details
            type: CustomerAppointmentResponse
        """
        self.set_dry_run_from_params()

        with using_db_for_reads(READ_ONLY_DB, condition=lambda: self.dry_run):
            if DecoupledDryRunFromAPI():
                return self._decoupled_post(business_id)

            return self._old_post(business_id)

    def _old_post(self, business_id) -> None:
        self.set_dry_run_from_params()
        business = self.get_object_or_404(
            Business.objects.select_related(
                'traveling',
                'region',
                'primary_category',
            ).prefetch_related(
                'pos__tips',
                'images',
            ),
            id=business_id,
        )

        # customer can do dry_run without login
        dry_run = self.data['dry_run']

        if not dry_run:
            lib.tools.sasrt(
                self.user,
                403,
                [
                    {
                        'code': 'unauthorized',
                        'field': 'access_token',
                        'description': _('Unauthorized access attempt.'),
                    }
                ],
            )
        no_thumbs = self.data.pop('no_thumbs', False)

        serializer = CreateAnalyticsCustomerAppointmentSerializer(
            instance=None,
            data=self.data,
            context={
                'is_booksy_gift_card_appointment': bool(
                    self.data.get('gift_card_code') or self.data.get('gift_cards_ids')
                ),
                'business': business,
                'pos': business.pos,
                'single_category': business.is_single_category,
                'user': self.user,
                # values for some attributes
                'type': Appointment.TYPE.CUSTOMER,
                'source': self.booking_source,
                compatibilities.COMPATIBILITIES: self.data.get(compatibilities.COMPATIBILITIES, {}),
                'no_thumbs': no_thumbs,
                'device_fingerprint': self.fingerprint,
                'cell_phone': self.user.cell_phone if self.user else '',
                'is_medical_consent_required': is_medical_consent_required(
                    business=business, user=self.user
                ),
            },
        )

        with business_context(business):
            try:
                save_data = self.save_appointment(serializer)
            except AppointmentProcessingFinishedException:
                return

        appointment = save_data.appointment
        first_booking = appointment.subbookings[0]
        appt = appointment.appointment

        if CancellationFeeEppoExperimentTest():
            # Check if appointment has cancellation_fee
            is_cancellation_fee_enabled = False
            if save_data.payment_data:
                cancellation_fee_total = save_data.payment_data.get('cancellation_fee_total', 0)
                # Convert to Decimal if it's a string, or use as is if it's already a Decimal
                if isinstance(cancellation_fee_total, str):
                    cancellation_fee_total = Decimal(cancellation_fee_total)
                is_cancellation_fee_enabled = cancellation_fee_total > 0
            
            # Call feature flag
            user_data = UserData(
                subject_key=business_id,
                subject_type=SubjectType.BUSINESS_ID.value,
                app_domain=AppDomains.CUSTOMER.value,
                custom={
                    'biz_id': business_id,
                    'cust_id': self.user.id if self.user else None,
                    'is_cancellation_fee_enabled': is_cancellation_fee_enabled,
                }
            )
            feature_flag_result = CancellationFeeCustomerBookingFlowTestFlag(user_data)
            feature_flag_logger.info(
                'Feature flag result: %s, timestamp: %s, business_id: %s, customer_id: %s, '
                'is_cancellation_fee_enabled: %s',
                feature_flag_result,
                datetime.datetime.now().isoformat(),
                business_id,
                self.user.id if self.user else None,
                is_cancellation_fee_enabled,
            )

        ret = {
            'appointment': save_data.appointment_data,
            'appointment_payment': save_data.payment_data,
            PartnerPaymentStatus.APPOINTMENT_RESPONSE_KEY: save_data.partner_payment_data,
            'three_d_data': save_data.payment_3d_error,
            # we are keeping this for backwards-compatibility for now
            'meta': (
                {
                    'first': appt.is_first(),
                    'cross': appt.is_crossing(),
                    'first_cross': appt.is_first_crossing(),
                }
                if not dry_run
                else None
            ),
        }
        if save_data.blik_data:
            ret['blik_data'] = save_data.blik_data

        self.finish_with_json(201, ret)

        if dry_run:
            return

        self._martech_analytics(appointment, analytics_tokens=self.analytics_auth_dict)

        if appt.booked_for:
            update_any_mobile_customer_appointments_task.delay(
                customer_id=appt.booked_for_id,
                newest_appointment_id=appt.id,
            )
        log_action.info(
            # pylint: disable=consider-using-f-string
            'New CustomerAppointment action.'
            'user={} type={} id={} status: {}.'.format(
                self.user.id,
                appointment.appointment_type,
                appointment.appointment_id,
                appointment.status,
            )
        )

        # HANDLE BOOKING CHANGES AND SCENARIOS
        BookingChange.add(
            appointment,
            changed_by=BookingChange.BY_CUSTOMER,
            changed_user=self.user,
            handler=self,
            metadata={
                'reason': 'customer_create_appointment',
            },
        )

        # all task below can be moved to this event receivers
        if not save_data.payment_3d_error and not save_data.blik_data:
            customer_appointment_created_event.send(appointment.appointment)

            # replaced by customer_booking_created_event
            start_scenario(
                BCS,
                appointment=appointment.appointment,
                action=BCS.CUSTOMER_CREATED,
            )

            segment_api_appointment_booked_task.delay(first_booking.id)

            update_bci_service_questions_task.delay(
                appointment.appointment_uid,
                customer_user_id=self.user.id,
            )

            # df marketing
            if appt.is_first_cb_for_business():
                first_cb(first_booking)
                publish_basic_customer_data_changed.delay(['user_id'], self.user.id)

            if tracking_token := serializer.validated_data.get('rwg_token'):
                notify_conversion_task.delay(
                    appointment.id,
                    tracking_token,
                    serializer.validated_data.get('merchant_changed'),
                )

        if analytics := self.data.get('analytics'):
            analytics['appointment'] = appt.id
            appointment_analytics_task.delay(analytics)

        if self.user and self.user.email and self.user.created and self.fingerprint:
            send_experiment_event_if_applicable.delay(
                fingerprint=self.fingerprint,
                user_id=self.user.id,
                user_email=self.user.email,
                user_created=self.user.created,
            )

    @partner_forward(SaveAppointmentForward)
    def save_appointment(
        self, serializer: AnalyticsCustomerAppointmentSerializer
    ) -> SaveAppointmentData:
        # TODO: add tests
        self.validate_serializer(serializer)

        business = serializer.context['business']
        dry_run = serializer.validated_data['dry_run']

        # SAVE BOOKING AND CREATE DEPOSIT IF NEEDED
        three_d_secure_error = None
        user_action_required_error = None
        with atomic():
            # create booking
            appointment = serializer.save()

            # validate payment
            payment_serializer = CustomerAppointmentTransactionSerializer(
                data=self.data,
                context={
                    'currency_symbol': settings.CURRENCY_CODE,
                    'valid_currency': True,
                    'user': self.user,
                    'business': business,
                    'pos': business.pos,
                    'appointment_checkout': appointment.checkout,
                    'appointment_data': AppointmentData.build(appointment),
                    'already_prepaid': serializer.is_prepaid_appointment,
                    'extra_data': self.payment_extra_data(),
                    'compatibilities': self.data.get('compatibilities', {}),
                    'device_fingerprint': self.fingerprint,
                    'cell_phone': self.user.cell_phone if self.user else '',
                    'user_agent': self.user_agent,
                    'show_payment_summary': True,
                },
            )
            self.validate_serializer(payment_serializer)

            if not dry_run:
                try:
                    payment_serializer.save()
                except ThreeDSecureAuthenticationRequired as e:
                    three_d_secure_error = e
                except BlikActionRequired as e:
                    user_action_required_error = e
                except Exception as e:
                    rollback_logger.warning(
                        'business_id: %s, user_id: %s datetime: %s, exception: %s',
                        business.id,
                        self.user.id,
                        lib.tools.tznow(),
                        e,
                    )
                    raise e

                create_member_transaction_for_parent_for_family_and_friends_appointment(appointment)

        save_data = SaveAppointmentData(
            appointment=appointment,
            appointment_data=serializer.data,
            payment_data=payment_serializer.data,
        )
        if user_action_required_error:
            balance_transaction_id = str(user_action_required_error.balance_transaction_id)
            save_data.payment_data['balance_transaction_id'] = balance_transaction_id
            save_data.blik_data = {
                'balance_transaction_id': balance_transaction_id,
            }
        if three_d_secure_error:
            save_data.payment_3d_error = three_d_secure_error.three_d_data
        return save_data

    def _decoupled_post(self, business_id) -> None:
        self.set_dry_run_from_params()

        try:
            create_customer_appointment_result = self._create_customer_appointment.do(
                CreateCustomerAppointmentCommand(
                    user=self.user,
                    fingerprint=self.fingerprint,
                    user_agent=self.user_agent,
                    booking_source=self.booking_source,
                    business_id=business_id,
                    data=self.data,
                    payment_extra=self.payment_extra_data(),
                ),
            )
        except BussinessDoesNotExistError as e:
            raise tornado.web.HTTPError(404) from e
        except AppointmentProcessingFinishedExceptionWithResponse as e:
            self.finish_with_json(e.status_code, e.response_payload)
        else:
            self.finish_with_json(
                201,
                self._construct_response_payload(
                    create_customer_appointment_result.save_appointment_data
                ),
            )
            if not self.data['dry_run']:
                self._analytics_and_notifications(create_customer_appointment_result)

    def _construct_response_payload(
        self, save_appointment_data: SaveAppointmentData
    ) -> dict[str, Any]:
        appointment_data = save_appointment_data.appointment_data
        payment_data = save_appointment_data.payment_data
        partner_payment_data = save_appointment_data.partner_payment_data
        payment_3d_error = save_appointment_data.payment_3d_error
        appointment = save_appointment_data.appointment.appointment

        response_payload = {
            'appointment': appointment_data,
            'appointment_payment': payment_data,
            PartnerPaymentStatus.APPOINTMENT_RESPONSE_KEY: partner_payment_data,
            'three_d_data': payment_3d_error,
            # we are keeping this for backwards-compatibility for now
            'meta': (
                {
                    'first': appointment.is_first(),
                    'cross': appointment.is_crossing(),
                    'first_cross': appointment.is_first_crossing(),
                }
                if not self.data['dry_run']
                else None
            ),
        }

        if save_appointment_data.blik_data:
            response_payload['blik_data'] = save_appointment_data.blik_data

        return response_payload

    def _analytics_and_notifications(
        self,
        create_customer_appointment_result: CreateCustomerAppointmentResult,
    ) -> None:
        save_appointment_data = create_customer_appointment_result.save_appointment_data
        merchant_tracking = create_customer_appointment_result.merchant_tracking

        self._martech_analytics(
            save_appointment_data.appointment,
            analytics_tokens=self.analytics_auth_dict,
        )

        if save_appointment_data.appointment.appointment.booked_for:
            update_any_mobile_customer_appointments_task.delay(
                customer_id=save_appointment_data.appointment.appointment.booked_for_id,
                newest_appointment_id=save_appointment_data.appointment.appointment.id,
            )
        log_action.info(
            # pylint: disable=consider-using-f-string
            'New CustomerAppointment action.'
            'user={} type={} id={} status: {}.'.format(
                self.user.id,
                save_appointment_data.appointment.appointment_type,
                save_appointment_data.appointment.appointment_id,
                save_appointment_data.appointment.status,
            )
        )

        # HANDLE BOOKING CHANGES AND SCENARIOS
        BookingChange.add(
            save_appointment_data.appointment,
            changed_by=BookingChange.BY_CUSTOMER,
            changed_user=self.user,
            handler=self,
            metadata={
                'reason': 'customer_create_appointment',
            },
        )

        segment_api_appointment_booked_task.delay(
            save_appointment_data.appointment.subbookings[0].id
        )

        # all task below can be moved to this event receivers
        if not save_appointment_data.payment_3d_error and not save_appointment_data.blik_data:
            first_booking = save_appointment_data.appointment.subbookings[0]
            customer_appointment_created_event.send(save_appointment_data.appointment.appointment)

            # replaced by customer_booking_created_event
            start_scenario(
                BCS,
                appointment=save_appointment_data.appointment.appointment,
                action=BCS.CUSTOMER_CREATED,
            )

            segment_api_appointment_booked_task.delay(first_booking.id)

            update_bci_service_questions_task.delay(
                save_appointment_data.appointment.appointment_uid,
                customer_user_id=self.user.id,
            )

            # df marketing
            if save_appointment_data.appointment.appointment.is_first_cb_for_business():
                first_cb(first_booking)
                publish_basic_customer_data_changed.delay(['user_id'], self.user.id)

            if merchant_tracking.rwg_token:
                notify_conversion_task.delay(
                    save_appointment_data.appointment.id,
                    merchant_tracking.rwg_token,
                    merchant_tracking.merchant_changed,
                )

        if analytics := self.data.get('analytics'):
            analytics['appointment'] = save_appointment_data.appointment.appointment.id
            appointment_analytics_task.delay(analytics)

        if self.user and self.user.email and self.user.created and self.fingerprint:
            send_experiment_event_if_applicable.delay(
                fingerprint=self.fingerprint,
                user_id=self.user.id,
                user_email=self.user.email,
                user_created=self.user.created,
            )

    @staticmethod
    def _martech_analytics(appointment, analytics_tokens=None):
        if user_id_family_and_friends_creator := get_user_id_creator(appointment.id):
            session_user_id = user_id_family_and_friends_creator
        else:
            session_user_id = sget(appointment, ['booked_for', 'user_id'])
        analytics_cb_created_for_customer_task.delay(
            appointment_id=appointment.id,
            context={
                'business_id': appointment.business_id,
                'session_user_id': session_user_id,
                'source_id': appointment.source_id,
            },
        )
        analytics_cb_created_for_business_task.delay(
            appointment_id=appointment.id,
            context={
                'business_id': appointment.business_id,
            },
        )
        if BranchIOCustomerBookingTrackingFlag():
            analytics_cb_created_count_for_business_branchio_task.delay(
                appointment_id=appointment.id,
                context={
                    'business_id': appointment.business_id,
                },
            )
        if FifthCustomerBookingInFourteenDaysSegment():
            analytics_cb_created_count_in_days_for_business_segment_task.delay(
                appointment_id=appointment.id,
                context={
                    'business_id': appointment.business_id,
                },
            )

        if FacebookAnalyticsCustomerBookingTrackingFlag():
            send_analytics_1st_cb_created_for_business_to_facebook.delay(
                appointment_id=appointment.id
            )

        if (
            appointment.appointment.get_chargeable_for_analytics()
            and analytics_tokens
            and EnergyCBCreatedForCustomerBranchEventFlag()
        ):
            analytics_energy_cb_created_for_customer_branchio_task.delay(
                appointment_id=appointment.id,
                context={
                    'business_id': appointment.business_id,
                    'session_user_id': session_user_id,
                    'source_id': appointment.source_id,
                    'tokens': analytics_tokens,
                },
            )


class OldCustomerAppointmentHandler(
    DryRunMixin, BaseAppointmentHandler
):  # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.CUSTOMER_BOOKING,)

    @session(login_required=True)
    def get(self, appointment_type, appointment_id):
        """DEPRECATED Get Appointment details.

        swagger:
            summary: DEPRECATED Get Appointment details
                     [will be deprecated soon use appointment_uid
                     instead appointment_id and appointment_type]
            parameters:
                - name: appointment_type
                  type: integer
                  paramType: path
                  description: Appointment Type (single or multi)
                  enum:
                    - single
                    - multi
                - name: appointment_id
                  type: integer
                  paramType: path
                  description: (Multi-)Booking ID
                - name: no_thumbs
                  type: boolean
                  paramType: query
                - name: with_combos
                  type: integer
                  paramType: path
                  required: False
                  enum:
                    - 0
                    - 1
            type: CustomerAppointmentResponse

        """

        appointment = self.get_appointment(
            customer_user_id=self.user.id,
            appointment_type=appointment_type,
            appointment_id=appointment_id,
        )

        with_combos = int(self.get_argument('with_combos', default=0))
        if not with_combos and any(
            subbooking.combo_children for subbooking in appointment.subbookings
        ):
            self.finish_with_json(
                400, {'errors': [_('Please download a new version of the application')]}
            )
            return

        serializer = CustomerAppointmentSerializer(
            instance=appointment,
            context={
                'business': appointment.business,
                'user': self.user,
                'single_category': appointment.business.is_single_category,
                'customer_api': True,
                'no_thumbs': self.no_thumbs,
                'fingerprint': self.fingerprint,
                'booking_source': self.booking_source,
                'request': self.request,
            },
        )
        ret = {
            'appointment': serializer.data,
        }
        appointment_payment_serializer = CustomerAppointmentCheckoutSerializer(
            instance=appointment,
            context={
                'currency_symbol': settings.CURRENCY_CODE,
                'valid_currency': True,
                'user': self.user,
                'business': appointment.business,
                'pos': appointment.business.pos,
                'appointment_checkout': appointment.checkout,
                'appointment_data': AppointmentData.build(appointment.appointment),
                'booksy_pay_flow_enabled': appointment.booksy_pay_flow_enabled,
                'is_paid_by_booksy_pay': appointment.is_paid_by_booksy_pay,
                'extra_data': self.payment_extra_data(),
                'already_prepaid': serializer.is_prepaid_appointment,
                'compatibilities': {
                    'prepayment': True,
                },
                'device_fingerprint': self.fingerprint,
                'cell_phone': self.user.cell_phone,
                'user_agent': self.user_agent,
            },
        )
        ret['appointment_payment'] = appointment_payment_serializer.data
        self.finish_with_json(200, ret)

    @session(login_required=True)
    @json_request
    def put(self, appointment_type, appointment_id):
        """DEPRECATED Edit Appointment.

        swagger:
            summary: DEPRECATED Edit Appointment
                     [will be deprecated soon use appointment_uid
                     instead appointment_id and appointment_type]
            parameters:
                - name: appointment_type
                  type: integer
                  paramType: path
                  description: Appointment Type (single or multi)
                  enum:
                    - single
                    - multi
                - name: appointment_id
                  type: integer
                  paramType: path
                  description: (Multi-)Booking ID
                - name: body
                  type: AppointmentDetails
                  paramType: body
                  description: Appointment details
            type: CustomerAppointmentResponse
        :swagger
        """
        self.set_dry_run_from_params()

        appointment: AppointmentWrapper = self.get_appointment(
            customer_user_id=self.user.id,
            appointment_type=appointment_type,
            appointment_id=appointment_id,
        )
        business = self.get_object_or_404(
            Business.objects.select_related(
                'traveling',
                'region',
                'primary_category',
            ).prefetch_related(
                'pos__tips',
                'images',
            ),
            id=appointment.business.id,
        )

        if not appointment.can_customer_change():
            self.quick_error(
                ('invalid', 'validation', None),
                _("Appointment change is not allowed"),
            )

        gift_cards_ids = self.data.get("gift_cards_ids", [])
        if gift_cards_ids and any((appointment.is_prepaid, appointment.is_having_deposit)):
            self.quick_error(
                ('invalid', 'validation', None),
                _('Only time and note can be edited for prepaid appointments'),
            )
            return

        no_thumbs = self.data.pop('no_thumbs', False)
        # TODO: add tests
        serializer = CustomerAppointmentSerializer(
            instance=appointment,
            data=self.data,
            context={
                'is_booksy_gift_card_appointment': bool(
                    self.data.get('gift_card_code') or self.data.get('gift_cards_ids')
                ),
                'business': business,
                'single_category': business.is_single_category,
                'user': self.user,
                'no_thumbs': no_thumbs,
                'device_fingerprint': self.fingerprint,
                'cell_phone': self.user.cell_phone,
                'booking_source': self.booking_source,
            },
        )
        dry_run = self.data['dry_run']

        # remember some data before update
        before = get_vital_summary_appointment(appointment) if not dry_run else None

        with business_context(business):
            try:
                save_data = self.save_appointment(serializer)
            except AppointmentProcessingFinishedException:
                return

        ret = {
            'appointment': save_data.appointment_data,
            'appointment_payment': save_data.payment_data,
        }
        if save_data.payment_3d_error:
            ret['three_d_data'] = save_data.payment_3d_error
        if save_data.blik_data:
            ret['blik_data'] = save_data.blik_data

        self.finish_with_json(200, ret)

        if dry_run:
            return

        appointment = save_data.appointment

        log_action.info(
            # pylint: disable=consider-using-f-string
            'Edit CustomerAppointment action.'
            'user={} type={} id={} status: {}.'.format(
                self.user.id,
                appointment.appointment_type,
                appointment.appointment_id,
                appointment.status,
            )
        )

        # HANDLE BOOKING CHANGES AND SCENARIOS
        BookingChange.add(
            appointment,
            changed_by=BookingChange.BY_CUSTOMER,
            changed_user=self.user,
            handler=self,
            metadata={
                'reason': 'CustomerAppointmentHandler',
            },
        )

        after = get_vital_summary_appointment(appointment)
        changed = should_run_reschedule_scenario(before, after, True)
        if changed:
            appointment_changed_by_customer_event.send(appointment.appointment)

        if changed:
            start_scenario(
                BCS,
                appointment=appointment,
                action=BCS.CUSTOMER_BOOKING_RESCHEDULED,
            )
        # THIS IS NEVER CALLED?
        elif (
            before['status'] == Appointment.STATUS.PROPOSED
            and after['status'] == Appointment.STATUS.ACCEPTED
        ):
            start_scenario(
                BCS,
                appointment=appointment,
                action=BCS.CUSTOMER_BOOKING_RESCHEDULE_RESPONSE,
            )

        update_bci_service_questions_task.delay(
            appointment.appointment_uid,
            customer_user_id=self.user.id,
        )

    @partner_forward(SaveAppointmentForward)
    def save_appointment(self, serializer) -> SaveAppointmentData:
        # pylint: disable=too-many-branches
        self.validate_serializer(serializer)

        appointment = serializer.instance
        business = serializer.context['business']

        autocancel_deposit = self.validate_booking_change_and_deposit(
            appointment.appointment, self.data
        )
        dry_run = serializer.validated_data['dry_run']

        # SAVE BOOKING AND CREATE DEPOSIT IF NEEDED
        transaction_ex = False  # True if transaction was created
        three_d_secure_error = None
        user_action_required_error = None
        with atomic():
            appointment = serializer.save()
            payment_serializer = CustomerAppointmentTransactionSerializer(
                data=self.data,
                context={
                    'currency_symbol': settings.CURRENCY_CODE,
                    'valid_currency': True,
                    'user': self.user,
                    'business': business,
                    'pos': business.pos,
                    'appointment_checkout': appointment.checkout,
                    'appointment_data': AppointmentData.build(appointment.appointment),
                    'extra_data': self.payment_extra_data(),
                    'already_prepaid': serializer.is_prepaid_appointment,
                    'compatibilities': self.data.get('compatibilities', {}),
                    'device_fingerprint': self.fingerprint,
                    'cell_phone': self.user.cell_phone,
                    'user_agent': self.user_agent,
                    'show_payment_summary': True,
                },
            )
            self.validate_serializer(payment_serializer)

            if autocancel_deposit and not dry_run:
                ReleaseDepositOnCancel.run(appointment_id=appointment.appointment_uid)

            save_transaction = not dry_run and not serializer.is_prepaid_appointment
            if save_transaction:
                try:
                    payment_serializer.save()
                except ThreeDSecureAuthenticationRequired as e:
                    if ThreeDSecureInAppointmentEditionFlag():
                        three_d_secure_error = e
                    else:
                        raise
                except BlikActionRequired as e:
                    user_action_required_error = e

                transaction_ex = True

        if transaction_ex:
            # reload appointment - transaction was added after serializer.save()
            appointment = self.get_appointment(
                customer_user_id=self.user.id,
                appointment_type=AT.MULTI,
                appointment_id=appointment.appointment_uid,
            )
            serializer.instance = appointment

        save_data = SaveAppointmentData(
            appointment=appointment,
            appointment_data=serializer.data,
            payment_data=payment_serializer.data,
        )
        if three_d_secure_error:
            save_data.payment_3d_error = three_d_secure_error.three_d_data

        if user_action_required_error:
            balance_transaction_id = str(user_action_required_error.balance_transaction_id)
            save_data.payment_data['balance_transaction_id'] = balance_transaction_id
            save_data.blik_data = {
                'balance_transaction_id': balance_transaction_id,
            }
        return save_data


class CustomerAppointmentHandler(OldCustomerAppointmentHandler):
    # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.CUSTOMER_BOOKING,)
    force_dry_run_from_path = True

    @session(optional_login=True)
    def get(self, appointment_uid):
        """Get Appointment details.

        swagger:
            summary: Get Appointment details
            parameters:
                - name: appointment_uid
                  type: integer
                  paramType: path
                  description: unique Appointment ID
                - name: no_thumbs
                  type: boolean
                  paramType: query
                - name: with_combos
                  type: integer
                  paramType: path
                  required: False
                  enum:
                    - 0
                    - 1
            type: CustomerAppointmentResponse

        """
        return super().get(AT.MULTI, appointment_uid)

    @session(optional_login=True)
    def put(self, appointment_uid):
        """Edit Appointment.

        swagger:
            summary: Edit Appointment
            parameters:
                - name: appointment_uid
                  type: integer
                  paramType: path
                  description: unique Appointment ID
                - name: body
                  type: AppointmentDetails
                  paramType: body
                  description: Appointment details
            type: CustomerAppointmentResponse
        :swagger
        """
        return super().put(AT.MULTI, appointment_uid)


class CustomerAppointmentClaimHandler(
    ClaimMixin,
    BaseAppointmentHandler,
):  # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @session(login_required=True)
    @json_request
    def post(self, appointment_uid):
        """Claim ownership of the appointment.

        It only works for future appointments where a client card
        doesn't have associated user.

        swagger:
            summary: Claim Appointment
            parameters:
                - name: appointment_uid
                  type: integer
                  paramType: path
                  description: Appointment ID
                - name: body
                  type: AppointmentClaim
                  paramType: body
        :swagger
        swaggerModels:
            AppointmentClaim:
                id: AppointmentClaim
                required:
                    - secret
                properties:
                    secret:
                        type: string
                        description: UUID token
                    reason:
                        type: string
                        description: UUID token
        :swaggerModels
        """
        appointment = self._get_appointment_or_404(AT.MULTI, appointment_uid)
        request_serializer = CustomerAppointmentClaimSerializer(
            data=self.data,
            context={
                'appointment': appointment,
            },
        )
        self.validate_serializer(request_serializer)
        validated_data = request_serializer.validated_data
        secret = validated_data.get('secret') or None

        # Don't allow claiming past bookings
        min_booked_from = tznow() - datetime.timedelta(days=30)
        if (
            not appointment.is_family_and_friends
            and appointment.secret
            and str(appointment.secret) == secret
            and appointment.booked_from > min_booked_from
        ):
            self.claim_appointment_or_merge_bci(
                logged_in_user=self.user,
                appointment=appointment,
                merge_reason=validated_data['reason'],
            )
            self.finish_with_json(status.HTTP_200_OK, {})

    def _get_appointment_or_404(self, appointment_type, appointment_id):
        user_query = Q(booked_for__user_id__isnull=True)
        if self.user:
            user_query |= Q(booked_for__user_id=self.user.id)

        if appointment_type == AT.SINGLE:
            appointment = Appointment.objects.filter(
                user_query,
                bookings__id=appointment_id,
            ).first()
        elif appointment_type == AT.MULTI:
            appointment = Appointment.objects.filter(
                user_query,
                id=appointment_id,
            ).first()
        else:
            appointment = None

        if appointment is None:
            raise tornado.web.HTTPError(404)

        return appointment
