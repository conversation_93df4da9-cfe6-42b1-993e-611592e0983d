from bo_obs.datadog.enums import BooksyTeams
from django.conf import settings
from rest_framework import status

from lib.feature_flag.adapter import UserData
from lib.feature_flag.feature.marketing import SkipMarketingDataFlag
from lib.feature_flag.feature.visibility_promotion import VisibilityPromotionActiveOnBackend
from service.business.feature_status.invite_customers import (
    invite_customers_status,
)
from service.business.feature_status.message_blast import (
    get_message_blast_status,
)
from service.business.feature_status.service_promotions import (
    detailed_service_promotions_status,
    promotion_service_variants,
    service_promotions_status,
)
from service.business.feature_status.social_media import social_media_status
from service.tools import RequestHandler, json_request, session
from webapps.business.enums import FeatureStatus, FeatureStatusColor
from webapps.business.models import Resource
from webapps.marketplace.boost_details import boost_status, BoostStatusProvider
from webapps.marketplace.models import MarketplaceCommission
from webapps.visibility_promotion.services import (
    PromotionEligibilityService,
    GetPromotionStatusUseCase,
)


def _get_mock_invite_customers_status():
    return {
        'status': FeatureStatus.ACTIVE,
        'status_color': FeatureStatusColor.GREEN,
        'label': '',
    }


def _get_mock_message_blast_status():
    return {
        'status': FeatureStatus.ACTIVE,
        'status_color': FeatureStatusColor.GREEN,
        'label': '',
    }


def _get_mock_service_promotions_status():
    return {
        'status': FeatureStatus.ACTIVE,
        'status_color': FeatureStatusColor.GREEN,
        'label': '',
    }


def _get_mock_social_media_status():
    return {
        'status': FeatureStatus.ACTIVE,
        'status_color': FeatureStatusColor.GREEN,
        'label': '',
    }


class BusinessFeatureStatusHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @session(login_required=True)
    def get(self, business_id=None):  # pylint: disable=too-many-branches
        """
        swagger:
            summary:
                Return business statuses
            type: BusinessFeatureStatusResponse
            parameters:
                - name: business_id
                  description: Business id
                  type: integer
                  paramType: path
                  required: true
        :swagger
        """
        business = self.business_with_staffer(business_id, __check_region=False)
        access_level = self.get_access_level(self.user)

        if SkipMarketingDataFlag(UserData(subject_key=business_id)):
            if access_level == Resource.STAFF_ACCESS_LEVEL_ADVANCED:
                ret = {
                    'invite_customers': _get_mock_invite_customers_status(),
                }
            elif access_level == Resource.STAFF_ACCESS_LEVEL_RECEPTION:
                ret = {
                    'invite_customers': _get_mock_invite_customers_status(),
                    'message_blast': _get_mock_message_blast_status(),
                    'service_promotions': _get_mock_service_promotions_status(),
                    'social_media': _get_mock_social_media_status(),
                }
            elif access_level in (
                Resource.STAFF_ACCESS_LEVEL_OWNER,
                Resource.STAFF_ACCESS_LEVEL_MANAGER,
            ):
                ret = {
                    'invite_customers': _get_mock_invite_customers_status(),
                    'message_blast': _get_mock_message_blast_status(),
                    'service_promotions': _get_mock_service_promotions_status(),
                    'social_media': _get_mock_social_media_status(),
                }
                boost_status_ = None
                if settings.BOOST.ENABLED:
                    boost_status_ = boost_status(business)
                    ret['boost'] = boost_status_

                if VisibilityPromotionActiveOnBackend():
                    boost_status_ = boost_status_ if not boost_status_ else boost_status(business)

                    if PromotionEligibilityService.can_access_visibility_promotion(
                        business_id, BoostStatusProvider.get_from_boost_status_dict(boost_status_)
                    ):
                        ret['visibility_promotion'] = (
                            GetPromotionStatusUseCase.get_promotion_status(business_id)
                        )
            else:
                ret = {}
        else:
            if access_level == Resource.STAFF_ACCESS_LEVEL_ADVANCED:
                ret = {
                    'invite_customers': invite_customers_status(business, access_level),
                }
            elif access_level == Resource.STAFF_ACCESS_LEVEL_RECEPTION:
                ret = {
                    'invite_customers': invite_customers_status(business, access_level),
                    'message_blast': get_message_blast_status(business.id),
                    'service_promotions': service_promotions_status(business),
                    'social_media': social_media_status(business),
                }
            elif access_level in (
                Resource.STAFF_ACCESS_LEVEL_OWNER,
                Resource.STAFF_ACCESS_LEVEL_MANAGER,
            ):
                ret = {
                    'invite_customers': invite_customers_status(business, access_level),
                    'message_blast': get_message_blast_status(business.id),
                    'service_promotions': service_promotions_status(business),
                    'social_media': social_media_status(business),
                }
                boost_status_ = None
                if settings.BOOST.ENABLED:
                    boost_status_ = boost_status(business)
                    ret['boost'] = boost_status_

                if VisibilityPromotionActiveOnBackend():
                    boost_status_ = boost_status_ if not boost_status_ else boost_status(business)

                    if PromotionEligibilityService.can_access_visibility_promotion(
                        business_id, BoostStatusProvider.get_from_boost_status_dict(boost_status_)
                    ):
                        ret['visibility_promotion'] = (
                            GetPromotionStatusUseCase.get_promotion_status(business_id)
                        )
            else:
                ret = {}

        self.finish_with_json(status.HTTP_200_OK, ret)


class ServicePromotionsDetailsHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @session(login_required=True)
    @json_request
    def get(self, business_id):
        """
        swagger:
            summary: Detailed information about service promotions.
            parameters:
                - name: business_id
                  description: id of business
                  type: integer
                  paramType: path
                  required: true
            type: DetailedServicePromotionsStatus
        """
        business = self.business_with_reception(business_id, __check_region=False)

        return self.finish_with_json(
            status.HTTP_200_OK,
            {
                **detailed_service_promotions_status(business),
                **promotion_service_variants(business.id),
            },
        )


class BusinessBoostStatusHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @session(login_required=True)
    def get(self, business_id):
        """
        swagger:
            summary:
                Return business statuses
            type: DetailedBoostStatus
            parameters:
                - name: business_id
                  description: Business id
                  type: integer
                  paramType: path
                  required: true
        :swagger
        """
        business = self.business_with_manager(
            business_id,
            __check_region=False,
        )

        if not settings.BOOST.ENABLED:
            return self.finish_with_json(
                status.HTTP_400_BAD_REQUEST,
                {
                    'description': 'Boost is not available',
                },
            )

        commission = MarketplaceCommission.get_commission(business)
        commission_val = commission.commission if commission else None

        ret = {
            'boost_status': boost_status(business),
            'commission_val': commission_val,
        }

        return self.finish_with_json(status.HTTP_200_OK, ret)
