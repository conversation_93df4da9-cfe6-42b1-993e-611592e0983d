from datetime import (
    datetime,
    time,
)
from unittest.mock import patch

import pytest
from django.conf import settings
from django.test import override_settings
from model_bakery import baker
from rest_framework import status

from country_config import Country
from lib.baker_utils import get_or_create_booking_source
from lib.feature_flag.feature import BooksyGiftcardsEnabledFlag
from lib.feature_flag.feature.security import (
    HCaptchaBusinessFlag,
    HCaptchaBusinessForceFlag,
    HCaptchaBusinessFrontdeskFlag,
)
from lib.tests.utils import override_feature_flag, override_eppo_feature_flag
from lib.tools import (
    firstof,
    id_to_external_api,
)
from service.tests import (
    BaseAsyncHTTPTest,
    dict_assert,
)
from webapps.admin_extra.bank_importer_new import (
    ENTERPRISE_IMPORTER_ID,
    IMPORTER_KEY,
)
from webapps.booking.enums import BookingMode
from webapps.booking.models import BookingSources
from webapps.business.models.models import SubdomainsCache
from webapps.business.baker_recipes import category_recipe
from webapps.business.enums import BusinessCategoryEnum, BOOKSY_GIFT_CARDS_ACCEPT_CATEGORIES
from webapps.business.models import (
    Business,
    BusinessCategory,
    ServiceAddOn,
)
from webapps.consts import FRONTDESK

from webapps.pos.enums import bank_account_type
from webapps.pos.models import POS
from webapps.schedule.enums import DayOfWeek
from webapps.schedule.models import (
    BusinessHours,
    ResourceHours,
)
from webapps.structure.enums import RegionType
from webapps.structure.models import (
    Region,
    RegionGraph,
)
from webapps.subdomain_grpc.client import SubdomainGRPC, SubdomainGRPCValidationError
from webapps.subdomain_grpc.tests.conftest import (
    get_subdomain_from_cache,
    get_subdomains_from_cache,
)

BUSINESS = {'name': 'Test Business Name'}

BANK_ACCOUNT = {
    'routing_number': '*********',
    'account_number': '************',
    'type': bank_account_type.SAVINGS,
}

REGION_ZIP = {'name': '00000', 'type': RegionType.ZIP}

REGION_CITY = {'name': 'Test City Name', 'type': RegionType.CITY}

REGION_STATE = {
    'name': 'Test State Name',
    'abbrev': 'Abbreviated State Name',
    'type': RegionType.STATE,
}


@pytest.mark.django_db
class BaseBusinessUpdateSetUp(BaseAsyncHTTPTest):
    @property
    def url(self):
        return f'/business_api/me/businesses/{self.business.id}/'

    def setUp(self):
        super().setUp()

        self.business.delete()
        self.region.delete()

        delattr(self, 'business')
        delattr(self, 'region')

        self.user.email = '<EMAIL>'
        self.user.save()

        self.region_city = baker.make(Region, **REGION_CITY)
        self.region_zip = baker.make(Region, **REGION_ZIP)
        self.region_state = baker.make(Region, **REGION_STATE)
        baker.make(RegionGraph, region=self.region_city, related_region=self.region_zip)
        baker.make(RegionGraph, region=self.region_state, related_region=self.region_city)

        self.business = baker.make(Business, owner=self.user, **BUSINESS)

        self.categories = list(category_recipe.make(_quantity=5))

    @override_settings(ES_ADM_1_LVL=[RegionType.STATE.value])
    def put(self, body):
        return self.fetch(self.url, method='PUT', body=body)


class TestBusinessUpdateWithNone(BaseBusinessUpdateSetUp):
    def setUp(self):
        super().setUp()

        subdomain = SubdomainGRPC.claim(
            data=dict(
                business_id=self.business.id,
                subdomain='barber',
            )
        )
        self.body = {
            'name': 'Test Business Name',
            'name_short': None,
            'official_name': None,
            'phone': '',
            'alert_phone': '',
            'description': '',
            'website': None,
            'facebook_link': None,
            'instagram_link': None,
            'public_email': None,
            'ecommerce_link': None,
            'credit_cards': None,
            'wheelchair_access': None,
            'parking': None,
            'booking_mode': '',
            'open_hours': [],
            'blocked_hours': [],
            'categories': [],
            'primary_category': None,
            'booking_min_lead_time': {'hours': 1},
            'booking_max_lead_time': {'months': 3},
            'booking_max_modification_time': {'hours': 12},
            'pricing_level': None,
            'registration_code': None,
            'sms_priority': 'P',
            'sms_limit': 0,
            'locked_limit_hourly': None,
            'opening_hours_note': None,
            'location': {
                'coordinate': {'latitude': None, 'longitude': None},
                'city': None,
                'address2': None,
                'zipcode': None,
                'address': None,
            },
            'invoice_address': None,
            'invoice_email': None,
            'bank_account_number': '',
            'bank_account_type': '',
            'visible': False,
            'segment_business_id': 'dev-us-2',
            'owner_email': None,
            'owner_full_name': None,
            'reviews_rank': None,
            'reviews_count': None,
            'reviews_stars': None,
            'payment_source': None,
            'trial_till': None,
            'status': None,
            'pos_registers_enabled': None,
            'pos_registers_reopen': None,
            'pos_commissions_enabled': None,
            'pos_prepayment_enabled': None,
            'pos_pay_by_app_enabled': None,
            'pos_enabled': None,
            'subdomain': None,
            'active_from': None,
            'active_till': None,
            'boost_remind_later': False,
            'boost_contact_now': False,
            'traveling': None,
        }

        self.expected = {
            'name': 'Test Business Name',
            'name_short': '',
            'official_name': '',
            'phone': '',
            'phone_with_prefix': '',
            'alert_phone': '',
            'description': '',
            'website': '',
            'facebook_link': '',
            'instagram_link': '',
            'public_email': None,
            'ecommerce_link': '',
            'has_meeting_provider': False,
            'credit_cards': '',
            'wheelchair_access': '',
            'parking': '',
            'booking_mode': BookingMode.AUTO,
            'open_hours': [],
            'business_opening_hours': [
                {'day_of_week': 0, 'hours': []},
                {'day_of_week': 1, 'hours': []},
                {'day_of_week': 2, 'hours': []},
                {'day_of_week': 3, 'hours': []},
                {'day_of_week': 4, 'hours': []},
                {'day_of_week': 5, 'hours': []},
                {'day_of_week': 6, 'hours': []},
            ],
            'hours_apply_from': None,
            'blocked_hours': [],
            'categories': [],
            'primary_category': None,
            'primary_category_internal_name': '-',
            'booking_min_lead_time': {'hours': 1},
            'booking_max_lead_time': {'months': 3},
            'booking_max_modification_time': {'hours': 12},
            'pricing_level': None,
            'registration_code': None,
            'sms_priority': 'P',
            'sms_limit': 0,
            'locked_limit_hourly': None,
            'opening_hours_note': '',
            'location': {
                'coordinate': {'latitude': None, 'longitude': None},
                'city': None,
                'address2': '',
                'zipcode': None,
                'state': None,
                'address': '',
                'address3': '',
                'time_zone_name': None,
            },
            'invoice_address': '',
            'invoice_email': None,
            'bank_account_number': '',
            'bank_account_type': '',
            'visible': False,
            'visible_delay_till': None,
            'segment_business_id': 'dev-us-2',
            'referred_by': None,
            'referrer_reward': None,
            'invited_reward': None,
            'owner_id': self.business.owner.id,
            'owner_email': self.business.owner.email,
            'owner_full_name': self.business.owner.full_name,
            'reviews_rank': None,
            'reviews_count': None,
            'reviews_stars': 0,
            'payment_source': 'U',
            'trial_till': None,
            'status': 'S',
            'package': Business.Package.UNKNOWN,
            'suggested_package': Business.Package.LITE,
            'pos_commissions_enabled': False,
            'pos_prepayment_enabled': False,
            'pos_pay_by_app_enabled': None,
            'pos_enabled': False,
            'subdomain': subdomain['subdomain'],
            'active_from': None,
            'active_till': None,
            'active': False,
            'active_appliances_count': 0,
            'active_staffers_count': 0,
            'promotion_trial_status': 'trial_not_active',
            'cover_photo': None,
            'booking_policy': 'Changes allowed up to 12 hours before visit',
            'promoted_before': False,
            'sms_notification_status': 'D',
            'is_gdpr_first_run': True,
            'promotion_status': None,
            'mp_deeplink': f'http://localhost:8600/en-us/dl/show-business/{self.business.id}',
            'timezone': datetime.now(tz=self.business.get_timezone()).strftime('%z'),
            'pos_minimal_pay_by_app_payment': {'amount': 5.0, 'formatted_amount': '$5.00'},
            'id': self.business.id,
            'physiotherapy_enabled': False,
            'has_addons': False,
            'has_braintree': False,
            'time_slots_optimization': False,
            'gdpr_annex_signed': None,
            'salon_network': None,
            'boost_contact_now': False,
            'boost_remind_later': False,
            'boost_payment_source': 'B',
            'boost_status': Business.BoostStatus.DISABLED,
            'traveling': None,
            'can_use_frontdesk': self.business.can_use_frontdesk,
            'new_terms_flow': True,
            'is_3d_secure_verified': None,
            'renting_venue': None,
            'has_new_billing': False,
            'printer_config': False,
            'hidden_in_search': True,
            'partner_apps': [],
            'is_visible_in_marketplace': False,
            'turntracker_enabled': False,
            'show_adyen_to_stripe_consent': False,
            'show_onboarding_walkthrough_banner': False,
            'show_activate_booksy_med_banner': False,
            'is_migrated_demo_account': False,
            'migrated_from': None,
            'is_migration_completed': None,
            'can_extend_trial': False,
            'not_published': False,
            'was_published_after_profile_setup': False,
        }

        self.response = self.put(self.body)

    def test_business_required_keys_presence(self):
        expected = set(self.response.json['business'].keys())
        tested = set(self.expected.keys())

        diff = expected - tested
        self.assertSetEqual(diff, set())

    def test_200_update_business_with_null(self):
        self.assertEqual(self.response.code, 200)
        assert SubdomainGRPC.search(
            data=dict(
                business_id=self.business.id,
            )
        )[0]
        dict_assert(self.response.json['business'], self.expected)


class TestBusinessUpdateWithEmptyString(BaseBusinessUpdateSetUp):
    def setUp(self):
        super().setUp()

        SubdomainGRPC.claim(
            data=dict(
                business_id=self.business.id,
                subdomain='barber',
            )
        )
        self.body = {
            'name': 'Test Business Name',
            'name_short': '',
            'official_name': '',
            'phone': '',
            'alert_phone': '',
            'description': '',
            'website': '',
            'facebook_link': '',
            'instagram_link': '',
            'public_email': '',
            'ecommerce_link': '',
            'credit_cards': '',
            'wheelchair_access': '',
            'parking': '',
            'booking_mode': '',
            'open_hours': [],
            'blocked_hours': [],
            'categories': [],
            'primary_category': '',
            'booking_min_lead_time': {'hours': 1},
            'booking_max_lead_time': {'months': 3},
            'booking_max_modification_time': {'hours': 12},
            'pricing_level': None,
            'registration_code': '',
            'sms_priority': 'P',
            'sms_limit': 0,
            'locked_limit_hourly': None,
            'opening_hours_note': '',
            'location': {
                'coordinate': {'latitude': '', 'longitude': ''},
                'city': '',
                'address2': '',
                'zipcode': '',
                'address': '',
                'address3': '',
            },
            'invoice_address': '',
            'invoice_email': '',
            'bank_account_number': '',
            'bank_account_type': '',
            'visible': False,
            'segment_business_id': 'dev-us-2',
            'owner_email': '',
            'owner_full_name': '',
            'reviews_rank': '',
            'reviews_count': '',
            'reviews_stars': '',
            'payment_source': 'U',
            'trial_till': '',
            'status': '',
            'pos_commissions_enabled': '',
            'pos_prepayment_enabled': '',
            'pos_pay_by_app_enabled': '',
            'pos_enabled': '',
            'subdomain': '',
            'active_from': '',
            'active_till': '',
            'boost_remind_later': False,
            'boost_contact_now': False,
            'boost_remind_sended': False,
        }

        self.expected = {
            'name': 'Test Business Name',
            'name_short': '',
            'official_name': '',
            'phone': '',
            'phone_with_prefix': '',
            'alert_phone': '',
            'description': '',
            'website': '',
            'facebook_link': '',
            'instagram_link': '',
            'public_email': '',
            'ecommerce_link': '',
            'has_meeting_provider': False,
            'credit_cards': '',
            'wheelchair_access': '',
            'parking': '',
            'booking_mode': BookingMode.AUTO,
            'open_hours': [],
            'business_opening_hours': [
                {'day_of_week': 0, 'hours': []},
                {'day_of_week': 1, 'hours': []},
                {'day_of_week': 2, 'hours': []},
                {'day_of_week': 3, 'hours': []},
                {'day_of_week': 4, 'hours': []},
                {'day_of_week': 5, 'hours': []},
                {'day_of_week': 6, 'hours': []},
            ],
            'hours_apply_from': None,
            'blocked_hours': [],
            'categories': [],
            'primary_category': None,
            'primary_category_internal_name': '-',
            'booking_min_lead_time': {'hours': 1},
            'booking_max_lead_time': {'months': 3},
            'booking_max_modification_time': {'hours': 12},
            'pricing_level': None,
            'registration_code': None,
            'sms_priority': 'P',
            'sms_limit': 0,
            'locked_limit_hourly': None,
            'opening_hours_note': '',
            'location': {
                'coordinate': {'latitude': None, 'longitude': None},
                'city': None,
                'address2': '',
                'zipcode': None,
                'state': None,
                'address': '',
                'address3': '',
                'time_zone_name': None,
            },
            'invoice_address': '',
            'invoice_email': '',
            'bank_account_number': '',
            'bank_account_type': '',
            'visible': False,
            'visible_delay_till': None,
            'segment_business_id': 'dev-us-2',
            'referred_by': None,
            'referrer_reward': None,
            'invited_reward': None,
            'owner_id': self.business.owner.id,
            'owner_email': self.business.owner.email,
            'owner_full_name': self.business.owner.full_name,
            'reviews_rank': None,
            'reviews_count': None,
            'reviews_stars': 0,
            'payment_source': 'U',
            'trial_till': None,
            'status': 'S',
            'package': Business.Package.UNKNOWN,
            'suggested_package': Business.Package.LITE,
            'pos_commissions_enabled': False,
            'pos_prepayment_enabled': False,
            'pos_pay_by_app_enabled': None,
            'pos_enabled': False,
            'subdomain': None,
            'active_from': None,
            'active_till': None,
            'active': False,
            'active_appliances_count': 0,
            'active_staffers_count': 0,
            'promotion_trial_status': 'trial_not_active',
            'cover_photo': None,
            'booking_policy': 'Changes allowed up to 12 hours before visit',
            'promoted_before': False,
            'sms_notification_status': 'D',
            'is_gdpr_first_run': True,
            'promotion_status': None,
            'mp_deeplink': f'http://localhost:8600/en-us/dl/show-business/{self.business.id}',
            'timezone': datetime.now(tz=self.business.get_timezone()).strftime('%z'),
            'pos_minimal_pay_by_app_payment': {'amount': 5.0, 'formatted_amount': '$5.00'},
            'id': self.business.id,
            'physiotherapy_enabled': False,
            'has_addons': False,
            'has_braintree': False,
            'time_slots_optimization': False,
            'gdpr_annex_signed': None,
            'salon_network': None,
            'boost_contact_now': False,
            'boost_status': Business.BoostStatus.DISABLED,
            'boost_payment_source': 'B',
            'boost_remind_later': False,
            'traveling': None,
            'can_use_frontdesk': self.business.can_use_frontdesk,
            'new_terms_flow': True,
            'is_3d_secure_verified': None,
            'renting_venue': None,
            'has_new_billing': False,
            'printer_config': False,
            'hidden_in_search': True,
            'partner_apps': [],
            'is_visible_in_marketplace': False,
            'turntracker_enabled': False,
            'show_adyen_to_stripe_consent': False,
            'show_onboarding_walkthrough_banner': False,
            'show_activate_booksy_med_banner': False,
            'is_migrated_demo_account': False,
            'migrated_from': None,
            'is_migration_completed': None,
            'can_extend_trial': False,
            'not_published': False,
            'was_published_after_profile_setup': False,
        }

        with (
            patch.object(SubdomainsCache, 'get_from_cache', get_subdomain_from_cache),
            patch.object(SubdomainsCache, 'push_to_cache', lambda *args, **kwargs: None),
        ):
            self.response = self.put(self.body)

    def test_business_required_keys_presence(self):
        expected = set(self.response.json['business'].keys())
        tested = set(self.expected.keys())
        diff = expected - tested
        self.assertSetEqual(diff, set())

    def test_200_update_business_with_null(self):
        self.assertEqual(self.response.code, 200)
        assert len(get_subdomains_from_cache(business_id=self.business.id)) == 0
        dict_assert(self.response.json['business'], self.expected)


class TestBusinessUpdateWithFull(BaseBusinessUpdateSetUp):
    def setUp(self):
        super().setUp()

        self.subdomain = SubdomainGRPC.claim(
            data=dict(
                business_id=self.business.id,
                subdomain='barber',
            )
        )['subdomain']
        self.pos = baker.make(POS, business=self.business, active=True)

        categories = sorted([c.id for c in self.categories])  # pylint: disable=not-an-iterable
        categories_dict = {c.id: c for c in self.categories}  # pylint: disable=not-an-iterable

        self.body = {
            'name': 'New Test Business Name',
            'name_short': 'Test Short Name',
            'official_name': 'Test Official Business Name',
            'phone': '**************',
            'alert_phone': '**************',
            'description': 'Test Business Description',
            'website': 'https://example.com',
            'facebook_link': 'https://facebook.com/TestBusiness/',
            'instagram_link': '',
            'public_email': '',
            'ecommerce_link': '',
            'credit_cards': '1234 1234 1234 1234',
            'wheelchair_access': 'Test WH Access',
            'parking': 'Around the corner',
            'booking_mode': BookingMode.MANUAL,
            'open_hours': [
                {'hour_from': '10:00', 'hour_till': '19:00', 'day_of_week': 1},
                {'hour_from': '10:00', 'hour_till': '19:00', 'day_of_week': 2},
                {'hour_from': '10:00', 'hour_till': '19:00', 'day_of_week': 3},
                {'hour_from': '10:00', 'hour_till': '19:00', 'day_of_week': 4},
                {'hour_from': '10:00', 'hour_till': '19:00', 'day_of_week': 5},
            ],
            'blocked_hours': [
                {'hour_from': '13:00', 'hour_till': '15:00', 'day_of_week': 1},
                {'hour_from': '13:00', 'hour_till': '15:00', 'day_of_week': 2},
                {'hour_from': '13:00', 'hour_till': '15:00', 'day_of_week': 3},
                {'hour_from': '13:00', 'hour_till': '15:00', 'day_of_week': 4},
                {'hour_from': '13:00', 'hour_till': '15:00', 'day_of_week': 5},
            ],
            'categories': categories,
            'primary_category': categories[0],
            'booking_min_lead_time': {'hours': 2},
            'booking_max_lead_time': {'months': 1},
            'booking_max_modification_time': {'hours': 24},
            'pricing_level': None,
            'registration_code': None,
            'sms_priority': 'O',
            'sms_limit': 10,
            'locked_limit_hourly': 76,
            'opening_hours_note': 'Test Opening Hours Note',
            'location': {
                'coordinate': {'latitude': -300.1, 'longitude': -300.1},
                'city': REGION_CITY.get('name'),
                'address2': '',
                'zipcode': REGION_ZIP.get('name'),
                'address': '',
            },
            'invoice_address': 'Test invoice address',
            'invoice_email': '<EMAIL>',
            'bank_account_number': '{routing_number}-{account_number}'.format(**BANK_ACCOUNT),
            'bank_account_type': BANK_ACCOUNT.get('type'),
            'visible': False,
            'segment_business_id': 'dev-us-2',
            'owner_email': '<EMAIL>',
            'owner_full_name': 'New Owner Name',
            'reviews_rank': 4,
            'reviews_count': 3,
            'reviews_stars': 3,
            'payment_source': 'B',
            'trial_till': '2018-10-10 10:10:10',
            'status': Business.Status.TRIAL,
            # # 'pos_registers_enabled': True,
            # # 'pos_registers_reopen': None,
            'pos_commissions_enabled': True,
            'pos_prepayment_enabled': True,
            'pos_pay_by_app_enabled': True,
            'pos_enabled': True,
            'subdomain': 'testerosky',
            'active_from': '2018-10-01 01:01:01',
            'active_till': '2018-10-10 10:10:10',
            'boost_remind_later': False,
            'boost_contact_now': False,
            'traveling': {
                'price': '12.50',
                'price_type': 'X',
                'distance': 20,
                'hide_address': True,
                'traveling_only': True,
                'policy': 'some policy',
            },
        }

        self.expected = {
            'name': 'New Test Business Name',
            'name_short': 'Test Short Name',
            'official_name': 'Test Official Business Name',
            'phone': '(*************',
            'phone_with_prefix': '+1 **********',
            'alert_phone': '(*************',
            'description': 'Test Business Description',
            'website': 'https://example.com',
            'facebook_link': 'https://facebook.com/TestBusiness/',
            'instagram_link': '',
            'public_email': '',
            'ecommerce_link': '',
            'has_meeting_provider': False,
            'credit_cards': '',
            'wheelchair_access': 'Test WH Access',
            'parking': 'Around the corner',
            # 16177: Manual -> Semi-Automatic hack
            'booking_mode': BookingMode.SEMIAUTO,
            'open_hours': [
                {'hour_from': '10:00', 'hour_till': '19:00', 'day_of_week': 1},
                {'hour_from': '10:00', 'hour_till': '19:00', 'day_of_week': 2},
                {'hour_from': '10:00', 'hour_till': '19:00', 'day_of_week': 3},
                {'hour_from': '10:00', 'hour_till': '19:00', 'day_of_week': 4},
                {'hour_from': '10:00', 'hour_till': '19:00', 'day_of_week': 5},
            ],
            'business_opening_hours': [
                {'hours': [], 'day_of_week': 0},
                {'hours': [{'hour_from': '10:00', 'hour_till': '19:00'}], 'day_of_week': 1},
                {'hours': [{'hour_from': '10:00', 'hour_till': '19:00'}], 'day_of_week': 2},
                {'hours': [{'hour_from': '10:00', 'hour_till': '19:00'}], 'day_of_week': 3},
                {'hours': [{'hour_from': '10:00', 'hour_till': '19:00'}], 'day_of_week': 4},
                {'hours': [{'hour_from': '10:00', 'hour_till': '19:00'}], 'day_of_week': 5},
                {'hours': [], 'day_of_week': 6},
            ],
            'hours_apply_from': None,
            'blocked_hours': [],
            'categories': categories,
            'primary_category': categories[0],
            'primary_category_internal_name': categories_dict[categories[0]].internal_name,
            'booking_min_lead_time': {'hours': 2},
            'booking_max_lead_time': {'months': 1},
            'booking_max_modification_time': {'days': 1},
            'pricing_level': None,
            'registration_code': None,
            'sms_priority': 'O',
            'sms_limit': 10,
            'locked_limit_hourly': 76,
            'opening_hours_note': 'Test Opening Hours Note',
            'location': {
                'coordinate': {'latitude': -300.1, 'longitude': -300.1},
                'city': REGION_CITY.get('name'),
                'address2': '',
                'zipcode': REGION_ZIP.get('name'),
                'state': REGION_STATE.get('abbrev'),
                'address': '',
                'address3': '',
                'time_zone_name': '',
            },
            'invoice_address': 'Test invoice address',
            'invoice_email': '<EMAIL>',
            'bank_account_number': '',  # removed
            'bank_account_type': '',  # removed
            'visible': False,
            'visible_delay_till': None,
            'segment_business_id': 'dev-us-2',
            'referred_by': None,
            'referrer_reward': None,
            'invited_reward': None,
            'owner_id': self.business.owner.id,
            'owner_email': self.business.owner.email,
            'owner_full_name': self.business.owner.full_name,
            'reviews_rank': None,
            'reviews_count': None,
            'reviews_stars': 0,
            'payment_source': 'U',
            'trial_till': None,
            'status': 'S',
            'package': Business.Package.UNKNOWN,
            'suggested_package': Business.Package.LITE,
            'pos_commissions_enabled': True,
            'pos_prepayment_enabled': False,
            'pos_pay_by_app_enabled': False,
            'pos_enabled': True,
            'subdomain': 'testerosky',
            'active_from': None,
            'active_till': None,
            'active': False,
            'active_appliances_count': 0,
            'active_staffers_count': 0,
            'promotion_trial_status': 'trial_not_active',
            'cover_photo': None,
            'booking_policy': 'Changes allowed up to 1 days before visit',
            'promoted_before': False,
            'sms_notification_status': 'D',
            'is_gdpr_first_run': True,
            'promotion_status': None,
            'mp_deeplink': f'http://localhost:8600/en-us/dl/show-business/{self.business.id}',
            'timezone': datetime.now(tz=self.business.get_timezone()).strftime('%z'),
            'pos_minimal_pay_by_app_payment': {'amount': 5.0, 'formatted_amount': '$5.00'},
            'id': self.business.id,
            'physiotherapy_enabled': False,
            'has_addons': False,
            'has_braintree': False,
            'time_slots_optimization': False,
            'gdpr_annex_signed': None,
            'salon_network': None,
            'boost_contact_now': False,
            'boost_status': Business.BoostStatus.DISABLED,
            'boost_payment_source': 'B',
            'boost_remind_later': False,
            'traveling': {
                'price': '12.50',
                'price_type': 'X',
                'distance': 20,
                'distance_unit': 'mi',
                'hide_address': True,
                'traveling_only': True,
                'policy': 'some policy',
            },
            'can_use_frontdesk': self.business.can_use_frontdesk,
            'new_terms_flow': True,
            'is_3d_secure_verified': None,
            'renting_venue': None,
            'has_new_billing': False,
            'printer_config': False,
            'hidden_in_search': True,
            'partner_apps': [],
            'is_visible_in_marketplace': False,
            'turntracker_enabled': False,
            'show_adyen_to_stripe_consent': False,
            'show_onboarding_walkthrough_banner': False,
            'show_activate_booksy_med_banner': False,
            'is_migrated_demo_account': False,
            'migrated_from': None,
            'is_migration_completed': None,
            'can_extend_trial': False,
            'not_published': False,
            'was_published_after_profile_setup': False,
        }

        self.response = self.put(self.body)

    def test_business_required_keys_presence(self):
        expected = set(self.response.json['business'].keys())
        tested = set(self.expected.keys())

        diff = expected - tested
        self.assertSetEqual(diff, set())

    def test_200_update_business_full(self):
        self.assertEqual(self.response.code, 200)
        names = {s['subdomain'] for s in get_subdomains_from_cache(business_id=self.business.id)}
        assert names == {self.subdomain, self.expected['subdomain']}
        dict_assert(self.response.json['business'], self.expected)

    def test_200_subdomain_valid_value(self):
        subdomain_name = 'hairdresser'
        body = expected = {
            'subdomain': subdomain_name,
        }
        response = self.put(body)

        self.assertEqual(response.code, status.HTTP_200_OK)
        dict_assert(response.json['business'], expected)
        subdomain = SubdomainGRPC.search(
            data=dict(
                business_id=self.business.id,
            )
        )[0]
        assert subdomain['subdomain'] == subdomain_name
        assert subdomain['deeplinks']['general'] == ''
        assert subdomain['deeplinks']['sms'] == ''
        new_business = Business.objects.filter(
            id=response.json['business']['id'],
        ).first()
        assert new_business.subdomain == subdomain_name
        sms_invite_mp_deeplink = new_business.get_sms_invite_mp_deeplink()
        assert subdomain_name in sms_invite_mp_deeplink
        assert sms_invite_mp_deeplink.endswith('/s/')

    @override_settings(API_COUNTRY=Country.BR)
    def test_200_subdomain_valid_value_brazil(self):
        Business.objects.filter(id=self.business.id).update(status=Business.Status.TRIAL)
        sms_invite_deeplink_0 = self.business.get_sms_invite_mp_deeplink()

        baker.make(
            BookingSources,
            name='Internal',
            app_type=BookingSources.INTERNAL_APP,
        )

        expected = body = {
            'subdomain': 'blabla-bla',
        }

        response = self.put(body)

        self.assertEqual(response.code, status.HTTP_200_OK)
        dict_assert(response.json['business'], expected)

        # subdomain created in setUp
        subdomain = SubdomainGRPC.search(
            data=dict(
                business_id=self.business.id,
                country_code=Country.US,
            )
        )[0]
        assert subdomain['subdomain'] == 'testerosky'
        deeplinks = subdomain['deeplinks']
        assert deeplinks['general'] == ''
        assert deeplinks['sms'] == ''
        assert deeplinks['blast'] == ''
        assert subdomain['country_code'] == 'us'

        subdomain = SubdomainGRPC.search(
            data=dict(
                business_id=self.business.id,
                country_code=Country.BR,
            )
        )[0]

        assert subdomain['subdomain'] == 'blabla-bla'
        deeplinks = subdomain['deeplinks']
        assert deeplinks['general'] == 'https://tdl.booksy.com/000000000-merchant_customer_invite'
        assert deeplinks['sms'] == 'https://tdl.booksy.com/000000000-sms-merchant_customer_invite'
        assert deeplinks['blast'] == 'https://tdl.booksy.com/000000000-sms-blast'
        assert subdomain['country_code'] == 'br'
        self.business.refresh_from_db()
        refreshed_business = Business.objects.filter(
            id=self.business.id,
        ).first()
        sms_invite_deeplink_1 = refreshed_business.get_sms_invite_mp_deeplink()
        assert sms_invite_deeplink_0 != sms_invite_deeplink_1

    def test_200_subdomain_valid_value_no_experiment(self):
        self.business = baker.make(
            Business,
            owner=self.user,
            active=True,
            status=Business.Status.TRIAL,
            **BUSINESS,
        )
        subdomain_name = 'hairdresser'

        response = self.put(dict(subdomain=subdomain_name))

        self.assertEqual(response.code, status.HTTP_200_OK)
        dict_assert(response.json['business'], dict(subdomain=subdomain_name))

        subdomain = SubdomainGRPC.search(
            data=dict(
                business_id=self.business.id,
            )
        )[0]
        assert subdomain['subdomain'] == subdomain_name
        deeplinks = subdomain['deeplinks']
        assert deeplinks['general'] == 'https://tdl.booksy.com/000000000-merchant_customer_invite'
        assert deeplinks['sms'] == 'https://tdl.booksy.com/000000000-sms-merchant_customer_invite'
        assert deeplinks['blast'] == 'https://tdl.booksy.com/000000000-sms-blast'
        new_business = Business.objects.filter(
            id=response.json['business']['id'],
        ).first()
        assert new_business.subdomain == subdomain_name
        assert new_business.get_sms_invite_mp_deeplink().endswith('/s/')

    def test_200_on_properties_in_body(self):
        body = {'not_published': True, 'was_published_after_profile_setup': True, **self.body}

        response = self.put(body)

        assert response.code == status.HTTP_200_OK
        assert self.response.json['business'] == self.expected


class TestBusinessUpdateWithOpeningHours(BaseBusinessUpdateSetUp):
    def setUp(self):
        super().setUp()

        self.body = {
            'name': 'New Test Business Name',
            'business_opening_hours': [
                {'hours': [{'hour_from': '10:00', 'hour_till': '19:00'}], 'day_of_week': 1},
            ],
        }
        self.response = self.put(self.body)

    def test_200_update_business_opening_hours(self):
        self.assertEqual(self.response.code, status.HTTP_200_OK)
        monday = firstof(
            filter(
                lambda x: x['day_of_week'] == 1,
                self.response.json['business']['business_opening_hours'],
            )
        )
        other = firstof(
            filter(
                lambda x: x['day_of_week'] != 1,
                self.response.json['business']['business_opening_hours'],
            )
        )

        assert monday == {'hours': [{'hour_from': '10:00', 'hour_till': '19:00'}], 'day_of_week': 1}
        assert other['hours'] == []


class TestBusinessUpdateSubdomainNotAvailable(BaseBusinessUpdateSetUp):
    def setUp(self, session_access_level=None):
        super().setUp()
        subdomain = 'barber'
        SubdomainGRPC.claim(data={'business_id': 9999999, 'subdomain': subdomain})
        self.body = {'name': 'Test Subdomain', 'subdomain': subdomain}

    def test_400_update_subdomain_not_available_on_setup(self):
        self.response = self.put(self.body)

        assert self.response.code == status.HTTP_400_BAD_REQUEST
        assert self.response.json == {
            'errors': [
                {
                    'field': 'name',
                    'description': 'Business name invalid. Please choose another.',
                    'code': 'invalid_name',
                },
            ]
        }

    def test_400_update_subdomain_not_available_after_setup(self):
        self.business.status = Business.Status.TRIAL
        self.business.save()

        self.response = self.put(self.body)

        assert self.response.code == status.HTTP_400_BAD_REQUEST
        assert self.response.json == {
            'errors': [
                {
                    'field': 'subdomain',
                    'description': 'Subdomain is not available.',
                    'code': 'invalid',
                },
            ]
        }

    @override_settings(LANGUAGE_CODE='es-es')
    def test_400_update_subdomain_not_available_on_setup_translated(self):
        self.response = self.put(self.body)

        assert self.response.code == status.HTTP_400_BAD_REQUEST
        assert 'válido' in self.response.json['errors'][0]['description']

    @override_settings(LANGUAGE_CODE='es-es')
    def test_400_update_subdomain_not_available_after_setup_translated(self):
        self.business.status = Business.Status.TRIAL
        self.business.save()

        self.response = self.put(self.body)

        assert self.response.code == status.HTTP_400_BAD_REQUEST
        assert 'Subdominio' in self.response.json['errors'][0]['description']


@pytest.mark.django_db
class TestOnboardingBusinessOpeningHours(BaseAsyncHTTPTest):

    url = '/business_api/me/businesses/{business_id}/'

    def setUp(self):
        super().setUp()

        self.business.status = Business.Status.SETUP
        self.business.save(update_fields=['status'])

        tz = self.business.get_timezone()
        hours = {
            DayOfWeek.monday: [(time(9, 0), time(17, 0))],
            DayOfWeek.tuesday: [(time(9, 0), time(17, 0))],
            DayOfWeek.wednesday: [(time(9, 0), time(17, 0))],
        }

        BusinessHours.set_hours(
            business_id=self.business.id,
            hours=hours,
            tz=tz,
        )
        ResourceHours.set_many_resources_hours(
            business_id=self.business.id,
            resource_ids=[self.owner.id],
            hours=hours,
            tz=tz,
        )

    def test_addons_in_business(self):
        baker.make(
            ServiceAddOn,
            business=self.business,
        )
        url = self.url.format(business_id=self.business.id)
        response = self.fetch(url, method='GET')
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertTrue(response.json['business']['has_addons'])

    def test_put_business_opening_hours(self):
        SubdomainGRPC.claim(
            data=dict(
                business_id=self.business.id,
                subdomain='barber99',
                country_code=settings.API_COUNTRY,
                deeplinks=dict(
                    general='https://general.com',
                    sms='https://sms.com',
                    blast='https://blast.com',
                ),
            )
        )
        url = self.url.format(business_id=self.business.id)
        response = self.fetch(
            url,
            method='PUT',
            body={
                "business_opening_hours": [
                    {"day_of_week": 1, "hours": [{"hour_from": "07:00", "hour_till": "19:00"}]},
                    {"day_of_week": 2, "hours": [{"hour_from": "07:00", "hour_till": "15:00"}]},
                    {"day_of_week": 3, "hours": [{"hour_from": "13:00", "hour_till": "20:00"}]},
                ]
            },
        )
        self.assertEqual(response.code, status.HTTP_200_OK)

        business_hours = BusinessHours.get_hours(self.business.id)
        self.assertDictEqual(
            business_hours,
            {
                0: [],
                1: [(time(7, 0), time(19, 0))],
                2: [(time(7, 0), time(15, 0))],
                3: [(time(13, 0), time(20, 0))],
                4: [],
                5: [],
                6: [],
            },
        )

        owner_hours = ResourceHours.get_hours(self.business.id, self.owner.id)
        self.assertDictEqual(
            owner_hours,
            {
                0: [],
                1: [(time(7, 0), time(19, 0))],
                2: [(time(7, 0), time(15, 0))],
                3: [(time(13, 0), time(20, 0))],
                4: [],
                5: [],
                6: [],
            },
        )

        self.business.refresh_from_db()
        assert 'branchio_business' not in self.business.integrations
        subdomain = SubdomainGRPC.search(
            data=dict(
                business_id=self.business.id,
            )
        )[0]
        assert subdomain['deeplinks']['general'] == 'https://general.com'
        assert subdomain['deeplinks']['blast'] == 'https://blast.com'
        assert subdomain['deeplinks']['sms'] == 'https://sms.com'


# pylint: disable=too-many-public-methods
class TestBusinessUpdateWithErrors(BaseBusinessUpdateSetUp):
    def setUp(self):
        super().setUp()

        self.pos = baker.make(POS, business=self.business, active=True)

        categories = sorted([c.id for c in self.categories])  # pylint: disable=not-an-iterable

        self.body = {
            'name': 'New Test Business Name' * 15,  # 330 characters
            'name_short': 'Test Short Name' * 15,  # xx characters
            'official_name': 'Test Official Business Name',
            'phone': '**************',
            'alert_phone': '**************',
            'description': 'Test Business Description',
            'website': 'https://example.com',
            'facebook_link': 'https://example.com/facebook-link/',
            'instagram_link': 'https://example.com/instagram-link/',
            'ecommerce_link': 'https://example.com/ecommerce-link/',
            'credit_cards': '1234 1234 1234 1234',
            'wheelchair_access': 'Test WH Access',
            'parking': 'Around the corner',
            'booking_mode': BookingMode.AUTO,
            'open_hours': [
                {'hour_from': '10:00', 'hour_till': '19:00', 'day_of_week': 1},
                {'hour_from': '10:00', 'hour_till': '19:00', 'day_of_week': 2},
                {'hour_from': '10:00', 'hour_till': '19:00', 'day_of_week': 3},
                {'hour_from': '10:00', 'hour_till': '19:00', 'day_of_week': 4},
                {'hour_from': '10:00', 'hour_till': '19:00', 'day_of_week': 5},
            ],
            'blocked_hours': [
                {'hour_from': '13:00', 'hour_till': '15:00', 'day_of_week': 1},
                {'hour_from': '13:00', 'hour_till': '15:00', 'day_of_week': 2},
                {'hour_from': '13:00', 'hour_till': '15:00', 'day_of_week': 3},
                {'hour_from': '13:00', 'hour_till': '15:00', 'day_of_week': 4},
                {'hour_from': '13:00', 'hour_till': '15:00', 'day_of_week': 5},
            ],
            'categories': categories,
            'primary_category': categories[0],
            'booking_min_lead_time': {'hours': 2},
            'booking_max_lead_time': {'months': 1},
            'booking_max_modification_time': {'hours': 24},
            'pricing_level': None,
            'registration_code': None,
            'sms_priority': 'O',
            'sms_limit': 10,
            'locked_limit_hourly': None,
            'opening_hours_note': 'Test Opening Hours Note',
            'location': {
                'coordinate': {'latitude': -300.1, 'longitude': -300.1},
                'city': REGION_CITY.get('name'),
                'address2': '',
                'zipcode': REGION_ZIP.get('name'),
                'address': '',
            },
            'invoice_address': 'Test invoice address',
            'invoice_email': '<EMAIL>',
            'bank_account_number': '{routing_number}-{account_number}'.format(**BANK_ACCOUNT),
            'bank_account_type': BANK_ACCOUNT.get('type'),
            'visible': False,
            'segment_business_id': 'dev-us-2',
        }

        # self.response = self.put(self.body)

    def test_400_name_too_long(self):
        body = {
            'name': 's' * (Business.NAME_MAX_LENGTH + 1),
        }
        expected = {
            "field": "name",
            "code": "max_length",
            "description": (
                'Business name is too long. '
                f'Max. number of characters: {Business.NAME_MAX_LENGTH}.'
            ),
        }

        response = self.put(body)

        self.assertEqual(response.code, 400)
        self.assertIn(expected, response.json['errors'])

    def test_400_name_invalid(self):
        body = {'name': 'Barber <EMAIL>'}
        expected = {
            "field": "name",
            "code": "invalid_name",
            "description": ("Business name cannot contain email, phone, or website link."),
        }

        response = self.put(body)

        self.assertEqual(response.code, 400)
        self.assertIn(expected, response.json['errors'])

    def test_400_short_name_too_long(self):
        body = {'name_short': 's' * (Business.NAME_MAX_LENGTH + 1)}

        expected = {
            "field": "name_short",
            "code": "max_length",
            "description": (
                f"Ensure this field has no more than {Business.NAME_MAX_LENGTH} characters."
            ),
        }

        response = self.put(body)

        self.assertEqual(response.code, 400)
        self.assertIn(expected, response.json['errors'])

    def test_400_official_name_too_long(self):
        body = {
            'official_name': 'Test Official Business Name' * 20,
        }

        expected = {
            "field": "official_name",
            "code": "max_length",
            "description": ("Ensure this field has no more than 250 characters."),
        }

        response = self.put(body)

        self.assertEqual(response.code, 400)
        self.assertIn(expected, response.json['errors'])

    def test_400_invalid_phone(self):
        body = {'phone': '123 123 123 123 123'}

        expected = {'field': 'phone', 'code': 'invalid', 'description': 'Invalid phone number'}

        response = self.put(body)

        self.assertEqual(response.code, 400)
        self.assertIn(expected, response.json['errors'])

    def test_400_invalid_alert_phone(self):
        body = {'alert_phone': '123 123 123 123 123'}

        expected = {
            'field': 'alert_phone',
            'code': 'invalid',
            'description': 'Invalid phone number',
        }

        response = self.put(body)

        self.assertEqual(response.code, 400)
        self.assertIn(expected, response.json['errors'])

    def test_200_invalid_description(self):
        body = {'description': '<html>&nbsp<body><script></script></body></html>'}

        expected = {}

        response = self.put(body)

        self.assertEqual(response.code, 200)
        dict_assert(response.json['business'], expected)

    def test_400_invalid_xss_ecommerce_link(self):
        body = {'ecommerce_link': 'javascript:alert(doc.domain);//xhttps://w.com/?Test<S>$${{7*7}}'}

        expected = {
            "errors": [
                {"field": "ecommerce_link", "description": "Enter a valid URL.", "code": "invalid"}
            ]
        }

        response = self.put(body)

        self.assertEqual(400, response.code)
        self.assertDictEqual(expected, response.json)

    def test_200_website_empty_url(self):
        body = {'website': ''}
        expected = body

        response = self.put(body)

        self.assertEqual(response.code, 200)
        dict_assert(response.json['business'], expected)

    def test_200_website_null_value(self):
        body = {'website': None}
        expected = {'website': ''}

        response = self.put(body)

        self.assertEqual(response.code, 200)
        dict_assert(response.json['business'], expected)

    def test_400_invalid_facebook_link(self):
        body = {'facebook_link': 'some invalid string'}

        response = self.put(body)

        self.assertEqual(response.code, 400)

    def test_400_wheelchair_too_long(self):
        body = {
            'wheelchair_access': 'A' * 55,  # xx characters
        }

        expected = {
            "field": "wheelchair_access",
            "code": "max_length",
            "description": ("Ensure this field has no more than 50 characters."),
        }

        response = self.put(body)

        self.assertEqual(response.code, 400)
        self.assertIn(expected, response.json['errors'])

    def test_400_parking_too_long(self):
        body = {
            'parking': 'A' * 55,  # xx characters
        }

        expected = {
            "field": "parking",
            "code": "max_length",
            "description": ("Ensure this field has no more than 50 characters."),
        }

        response = self.put(body)

        self.assertEqual(response.code, 400)
        self.assertIn(expected, response.json['errors'])

    def test_400_booking_mode_invalid_value(self):
        body = {'booking_mode': 'X'}

        expected = {
            "field": "booking_mode",
            "code": "invalid_choice",
            "description": '"X" is not a valid choice.',
        }

        response = self.put(body)

        self.assertEqual(response.code, 400)
        self.assertIn(expected, response.json['errors'])

    # sms_priority
    def test_400_sms_priority_invalid_value_type(self):
        """Legacy - NOW: /business/sms/management"""
        body = {'sms_priority': 42}

        expected = {
            "field": "sms_priority",
            "code": "invalid_choice",
            "description": ('"42" is not a valid choice.'),
        }

        response = self.put(body)

        self.assertEqual(response.code, 400)
        self.assertIn(expected, response.json['errors'])

    # sms_limit
    def test_400_sms_limit_invalid_value_type(self):
        body = {'sms_limit': 'V'}

        expected = {
            "field": "sms_limit",
            "code": "invalid",
            "description": "A valid integer is required.",
        }

        response = self.put(body)

        self.assertEqual(response.code, 400)
        self.assertIn(expected, response.json['errors'])

    def test_400_locked_limit_hourly_invalid_value_type(self):
        body = {'locked_limit_hourly': 'Ala'}

        expected = {
            "field": "locked_limit_hourly",
            "code": "invalid",
            "description": "A valid integer is required.",
        }

        response = self.put(body)

        self.assertEqual(response.code, 400)
        self.assertIn(expected, response.json['errors'])

    def test_400_locked_limit_hourly_value_less_than_0(self):
        body = {'locked_limit_hourly': -1}

        expected = {
            "field": "locked_limit_hourly",
            "code": "min_value",
            "description": "Ensure this value is greater than or equal to 0.",
        }

        response = self.put(body)

        self.assertEqual(response.code, 400)
        self.assertIn(expected, response.json['errors'])

    def test_400_locked_limit_hourly_value_greater_than_180(self):
        body = {'locked_limit_hourly': 181}

        expected = {
            "field": "locked_limit_hourly",
            "code": "max_value",
            "description": "Ensure this value is less than or equal to 180.",
        }

        response = self.put(body)

        self.assertEqual(response.code, 400)
        self.assertIn(expected, response.json['errors'])

    def test_400_opening_hours_note_over_500_chars(self):
        body = {'opening_hours_note': 'X' * 501}

        expected = {
            'field': 'opening_hours_note',
            'code': 'max_length',
            'description': ('Ensure this field has no more than 500 characters.'),
        }

        response = self.put(body)

        self.assertEqual(response.code, 400)
        self.assertIn(expected, response.json['errors'])

    def test_400_invoice_address_too_long(self):
        body = {'invoice_address': 'X' * 1025}

        expected = {
            'field': 'invoice_address',
            'code': 'max_length',
            'description': ('Ensure this field has no more than 1024 characters.'),
        }

        response = self.put(body)

        self.assertEqual(response.code, 400)
        self.assertIn(expected, response.json['errors'])

    def test_400_invoice_email_invalid_value(self):
        body = {'invoice_email': 'fake'}

        expected = {
            "field": "invoice_email",
            "code": "invalid",
            "description": "Enter a valid email address.",
        }

        response = self.put(body)

        self.assertEqual(response.code, 400)
        self.assertIn(expected, response.json['errors'])

    def test_200_segment_business_id_update(self):
        self.business.integrations['segment_business_id'] = 'al al al al'
        self.business.save()

        body = {'segment_business_id': 'la la la la'}

        expected = body

        response = self.put(body)

        self.business = Business.objects.get(pk=self.business.id)

        self.assertEqual(response.code, 200)
        dict_assert(response.json['business'], expected)

    def test_200_coordinate_lat_long_invalid_values(self):
        body = {
            'location': {
                'coordinate': {
                    'latitude': -301.7,
                    'longitude': 417.8,
                }
            }
        }

        expected = body

        response = self.put(body)

        self.assertEqual(response.code, 200)
        dict_assert(response.json['business']['location'], expected['location'])

    def test_200_city_non_existing(self):
        body = {'location': {'city': 'Non Existing Fake Town'}}

        expected = body

        response = self.put(body)

        self.assertEqual(response.code, 200)
        dict_assert(response.json['business']['location'], expected['location'])

    def test_400_city_over_100_chars(self):
        body = {'location': {'city': 'C' * 101}}

        expected = {
            'field': 'location.city',
            'code': 'max_length',
            'description': ('Ensure this field has no more than 100 characters.'),
        }

        response = self.put(body)

        self.assertEqual(response.code, 400)
        self.assertIn(expected, response.json['errors'])

    def test_200_address_invalid(self):
        body = {'location': {'address': '21674 fhsdj sdaf'}}

        expected = body['location']

        response = self.put(body)

        self.assertEqual(response.code, 200)
        dict_assert(response.json['business']['location'], expected)

    def test_400_address_over_100_chars(self):
        body = {'location': {'address': 'X' * 101}}

        expected = {
            'field': 'location.address',
            'code': 'max_length',
            'description': ('Ensure this field has no more than 100 characters.'),
        }

        response = self.put(body)

        self.assertEqual(response.code, 400)
        self.assertIn(expected, response.json['errors'])

    def test_200_invalid_address2(self):
        body = {'location': {'address2': '21674 fhsdj sdaf'}}

        expected = body['location']

        response = self.put(body)

        self.assertEqual(response.code, 200)
        dict_assert(response.json['business']['location'], expected)

    def test_400_address2_over_100_chars(self):
        body = {'location': {'address2': 'X' * 101}}

        expected = {
            'field': 'location.address2',
            'code': 'max_length',
            'description': ('Ensure this field has no more than 100 characters.'),
        }

        response = self.put(body)

        self.assertEqual(response.code, 400)
        self.assertIn(expected, response.json['errors'])

    def test_400_zipcode_invalid_value(self):
        body = {'location': {'zipcode': ' 4583672 bla bla'}}

        expected = {
            "field": "location.zipcode",
            "code": "invalid_zipcode",
            "description": "Zip code is not valid.",
        }

        response = self.put(body)

        self.assertEqual(response.code, 400)
        self.assertIn(expected, response.json['errors'])

    def test_200_valid_zipcode_but_no_country_zipcode_regexp(self):
        with patch.object(settings.COUNTRY_CONFIG, 'zipcode_regexp', None):
            body = {'location': {'zipcode': REGION_ZIP.get('name')}}
            response = self.put(body)
            self.assertEqual(response.code, status.HTTP_200_OK)

    def test_200_owner_email_invalid_value(self):
        body = {'owner_email': -64372}

        expected = {'owner_email': self.business.owner.email}

        response = self.put(body)

        self.assertEqual(response.code, 200)
        dict_assert(response.json['business'], expected)

    def test_200_owner_full_name_invalid_value(self):
        body = {'owner_full_name': -64372}

        expected = {'owner_full_name': self.business.owner.full_name}

        response = self.put(body)

        self.assertEqual(response.code, 200)
        dict_assert(response.json['business'], expected)

    def test_200_payment_source_invalid_value(self):
        body = {'payment_source': -64372}

        expected = {'payment_source': 'U'}

        response = self.put(body)

        self.assertEqual(response.code, 200)
        dict_assert(response.json['business'], expected)

    def test_200_trial_till_invalid_value(self):
        body = {'trial_till': -64372}

        expected = {'trial_till': None}

        response = self.put(body)

        self.assertEqual(response.code, 200)
        dict_assert(response.json['business'], expected)

    def test_200_status_invalid_value(self):
        body = {'status': -64372}

        expected = {'status': 'S'}

        response = self.put(body)

        self.assertEqual(response.code, 200)
        dict_assert(response.json['business'], expected)

    def test_200_pos_commissions_enabled_invalid_value(self):
        body = {
            # 'pos_commissions_enabled': -64372
            'pos_commissions_enabled': 'fhsd'
        }

        expected = {'pos_commissions_enabled': True}

        response = self.put(body)

        self.assertEqual(response.code, 200)
        dict_assert(response.json['business'], expected)

    def test_200_pos_prepayment_enabled_invalid_value(self):
        body = {'pos_prepayment_enabled': -64372}

        expected = {'pos_prepayment_enabled': False}

        response = self.put(body)

        self.assertEqual(response.code, 200)
        dict_assert(response.json['business'], expected)

    def test_200_pos_pay_by_app_enabled_invalid_value(self):
        body = {'pos_pay_by_app_enabled': -64372}

        expected = {'pos_pay_by_app_enabled': False}

        response = self.put(body)

        self.assertEqual(response.code, 200)
        dict_assert(response.json['business'], expected)

    def test_200_pos_enabled_invalid_value(self):
        body = {'pos_enabled': -64372}

        expected = {'pos_enabled': True}

        response = self.put(body)

        self.assertEqual(response.code, 200)
        dict_assert(response.json['business'], expected)

    def test_200_subdomain_invalid_value(self):
        body = {'subdomain': 'test-subdomain-slug'}

        expected = body

        response = self.put(body)

        self.assertEqual(response.code, 200)
        dict_assert(response.json['business'], expected)

    def test_200_active_from_invalid_value(self):
        body = {'active_from': -64372}

        expected = {'active_from': None}

        response = self.put(body)

        self.assertEqual(response.code, 200)
        dict_assert(response.json['business'], expected)

    def test_200_active_till_invalid_value(self):
        body = {'active_till': -64372}

        expected = {'active_till': None}

        response = self.put(body)

        self.assertEqual(response.code, 200)
        dict_assert(response.json['business'], expected)

    def test_200_reviews_rank_invalid_value(self):
        body = {'reviews_rank': -64372}

        expected = {'reviews_rank': None}

        response = self.put(body)

        self.assertEqual(response.code, 200)
        dict_assert(response.json['business'], expected)

    def test_200_reviews_count_invalid_value(self):
        body = {'reviews_count': -64372}

        expected = {'reviews_count': None}

        response = self.put(body)

        self.assertEqual(response.code, 200)
        dict_assert(response.json['business'], expected)

    def test_200_reviews_stars_invalid_value(self):
        body = {'reviews_stars': -64372}

        expected = {'reviews_stars': 0}

        response = self.put(body)

        self.assertEqual(response.code, 200)
        dict_assert(response.json['business'], expected)

    def test_400_financial_category_invalid_subcategory(self):
        financial_category = category_recipe.make(
            internal_name=BusinessCategoryEnum.FINANCIAL_INSTITUTIONS,
        )
        barber_category = category_recipe.make(
            internal_name=BusinessCategoryEnum.BARBERS,
        )

        body = {
            'primary_category': financial_category.id,
            'categories': [financial_category.id, barber_category.id],
        }
        expected = {
            'field': 'primary_category',
            'description': 'With financial institutions as primary category '
            'all categories must be subcategories of financial '
            'institutions.',
            'code': 'invalid',
        }

        response = self.put(body)

        self.assertEqual(response.code, 400)
        self.assertIn(expected, response.json['errors'])

    def test_400_financial_category_invalid_primary_category(self):
        financial_category = category_recipe.make(
            internal_name=BusinessCategoryEnum.FINANCIAL_INSTITUTIONS,
        )
        self.business.primary_category = financial_category
        self.business.save(update_fields=['primary_category'])
        barber_category = category_recipe.make(
            internal_name=BusinessCategoryEnum.BARBERS,
        )

        body = {
            'primary_category': barber_category.id,
            'categories': [financial_category.id, barber_category.id],
        }
        expected = {
            'field': 'primary_category',
            'description': 'To change business category, ' 'contact the support department',
            'code': 'invalid',
        }

        response = self.put(body)

        self.assertEqual(response.code, 400)
        self.assertIn(expected, response.json['errors'])


class TestBusinessUpdateCleanData(BaseBusinessUpdateSetUp):
    def setUp(self):
        super().setUp()

        self.pos = baker.make(POS, business=self.business, active=True)

        self.category_ids = sorted(
            [c.id for c in self.categories]
        )  # pylint: disable=not-an-iterable

        self.body = {
            'name': 'New Test Business Name',
            'name_short': 'Test Short Name',
            'official_name': 'Test Official Business Name',
            'phone': '**************',
            'alert_phone': '**************',
            'description': 'Test Business Description',
            'website': 'https://example.com',
            'facebook_link': 'https://example.com/facebook-link/',
            'instagram_link': 'https://example.com/instagram-link/',
            'ecommerce_link': 'https://example.com/ecommerce-link/',
            'credit_cards': '1234 1234 1234 1234',
            'wheelchair_access': 'Test WH Access',
            'parking': 'Around the corner',
            'booking_mode': BookingMode.AUTO,
            'open_hours': [
                {'hour_from': '10:00', 'hour_till': '19:00', 'day_of_week': 1},
                {'hour_from': '10:00', 'hour_till': '19:00', 'day_of_week': 2},
                {'hour_from': '10:00', 'hour_till': '19:00', 'day_of_week': 3},
                {'hour_from': '10:00', 'hour_till': '19:00', 'day_of_week': 4},
                {'hour_from': '10:00', 'hour_till': '19:00', 'day_of_week': 5},
            ],
            'blocked_hours': [],
            'categories': self.category_ids,
            'primary_category': self.category_ids[0],
            'booking_min_lead_time': {'months': 1, 'days': 1, 'hours': 1},
            'booking_max_lead_time': {'months': 2, 'days': 22, 'hours': 22},
            'booking_max_modification_time': {'months': 0, 'days': 7, 'hours': 2},
            'pricing_level': None,
            'registration_code': None,
            'sms_priority': 'O',
            'sms_limit': 10,
            'locked_limit_hourly': 76,
            'opening_hours_note': 'Test Opening Hours Note',
            'location': {
                'coordinate': {'latitude': -300.1, 'longitude': -300.1},
                'city': REGION_CITY.get('name'),
                'address2': '',
                'zipcode': REGION_ZIP.get('name'),
                'address': '',
            },
            'invoice_address': 'Test invoice address',
            'invoice_email': '<EMAIL>',
            'visible': True,
        }

        self.response = self.put(self.body)

    def test_400_name_empty(self):
        body = {'name': ''}

        expected = {'field': 'name', 'code': 'blank', 'description': 'This field may not be blank.'}

        response = self.put(body)
        self.assertEqual(response.code, 400)
        self.assertIn(expected, response.json['errors'])

    def test_200_booking_mode_set_empty(self):
        body = {'booking_mode': ''}

        expected = {'booking_mode': BookingMode.AUTO}

        response = self.put(body)

        self.assertEqual(response.code, 200)
        dict_assert(response.json['business'], expected)

    def test_200_open_hours_set_empty(self):
        body = {'open_hours': []}

        expected = body

        response = self.put(body)

        self.assertEqual(response.code, 200)
        dict_assert(response.json['business'], expected)

    def test_200_categories_set_empty(self):
        body = {'categories': []}

        expected = {'categories': []}

        response = self.put(body)

        self.assertEqual(response.code, 200)
        dict_assert(response.json['business'], expected)

    def test_200_booking_min_lead_time_set_empty(self):
        body = {'booking_min_lead_time': {}}

        expected = {'booking_min_lead_time': {'minutes': 30}}

        response = self.put(body)

        self.assertEqual(response.code, 200)
        dict_assert(response.json['business'], expected)

    def test_200_booking_max_lead_time_set_empty(self):
        body = {'booking_max_lead_time': {}}

        expected = {'booking_max_lead_time': {'months': 3}}

        response = self.put(body)

        self.assertEqual(response.code, 200)
        dict_assert(response.json['business'], expected)

    def test_200_booking_max_modification_time_set_empty(self):
        body = {'booking_max_modification_time': {}}

        expected = {'booking_max_modification_time': {'hours': 1}}

        response = self.put(body)

        self.assertEqual(response.code, 200)
        dict_assert(response.json['business'], expected)

    def test_200_segment_business_id_set_empty(self):
        body = {'segment_business_id': ''}

        biz = Business.objects.get(id=self.business.id)
        expected = {'segment_business_id': id_to_external_api(biz.id)}

        response = self.put(body)

        self.assertEqual(response.code, 200)
        dict_assert(response.json['business'], expected)

    def test_200_location_coordinate_lat_log_set_empty(self):
        body = {
            'location': {
                'coordinate': {
                    'latitude': '',
                    'longitude': '',
                }
            }
        }

        expected = {
            'location': {
                'coordinate': {
                    'latitude': None,
                    'longitude': None,
                }
            }
        }

        response = self.put(body)

        self.assertEqual(response.code, 200)
        dict_assert(response.json['business']['location'], expected['location'])

    def test_200_financial_category_valid_subcategory(self):
        financial_category = category_recipe.make(
            internal_name=BusinessCategoryEnum.FINANCIAL_INSTITUTIONS,
        )
        some_bank_category = category_recipe.make(
            parent=financial_category,
        )

        body = {
            'primary_category': financial_category.id,
            'categories': [financial_category.id, some_bank_category.id],
        }

        response = self.put(body)

        self.assertEqual(response.code, 200)
        self.assertIn(financial_category.id, response.json['business']['categories'])
        self.assertIn(some_bank_category.id, response.json['business']['categories'])


class TestBusinessUpdateEnterpriseBusiness(BaseBusinessUpdateSetUp):
    def setUp(self):
        super().setUp()
        self.categories_ids = sorted(
            c.id for c in self.categories
        )  # pylint: disable=not-an-iterable
        self.business.categories.add(*self.categories_ids)
        self.business.save()

    def test_200_should_return_ok_for_no_enterprise_business(self):
        body = {
            'categories': self.categories_ids[:1],
        }

        expected = self.categories_ids[:1]

        response = self.put(body)

        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertEqual(response.json['business']['categories'], expected)

    def test_200_should_return_ok_when_categories_are_not_changed(self):
        self.business.integrations[IMPORTER_KEY] = ENTERPRISE_IMPORTER_ID
        self.business.save()
        body = {
            'categories': self.categories_ids,
        }

        expected = self.categories_ids

        response = self.put(body)

        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertEqual(response.json['business']['categories'], expected)

    def test_403_should_return_error_when_categories_are_changed(self):
        self.business.integrations[IMPORTER_KEY] = ENTERPRISE_IMPORTER_ID
        self.business.save()
        body = {
            'categories': self.categories_ids[1:],
        }

        response = self.put(body)

        self.assertEqual(response.code, status.HTTP_403_FORBIDDEN)
        self.assertIsNotNone(response.json['errors'])

    def test_403_should_return_error_when_primary_category_is_changed(self):
        self.business.integrations[IMPORTER_KEY] = ENTERPRISE_IMPORTER_ID
        self.business.primary_category = self.categories[
            -1
        ]  # pylint: disable=unsubscriptable-object
        self.business.save()
        body = {
            'primary_category': self.categories[-2].id,  # pylint: disable=unsubscriptable-object
            'categories': self.categories_ids,
        }

        response = self.put(body)

        self.assertEqual(response.code, status.HTTP_403_FORBIDDEN)
        self.assertIsNotNone(response.json['errors'])


@override_feature_flag({BooksyGiftcardsEnabledFlag: True})
class TestAcceptBooksyGiftCardOnCategoryChange(BaseBusinessUpdateSetUp):
    def setUp(self):
        super().setUp()
        self.bgc_accepted_category = baker.make(
            BusinessCategory,
            type=BusinessCategory.CATEGORY,
            internal_name=BOOKSY_GIFT_CARDS_ACCEPT_CATEGORIES[0],
            enable_booksy_gift_cards=True,
        )
        self.categories_ids = sorted(
            c.id for c in self.categories
        )  # pylint: disable=not-an-iterable
        self.business.primary_category = self.categories[
            0
        ]  # pylint: disable=unsubscriptable-object
        self.business.categories.add(*self.categories_ids)

    @patch('webapps.business_related.utils._is_kyced_and_active', return_value=True)
    def test_change_category_to_accepted(self, _is_kyced_and_active_mock):
        self.assertFalse(self.business.booksy_gift_cards_settings.accept_booksy_gift_cards)
        self.categories_ids.append(self.bgc_accepted_category.id)

        body = {
            'primary_category': self.bgc_accepted_category.id,
            'categories': self.categories_ids,
        }
        response = self.put(body)

        self.assertEqual(response.code, status.HTTP_200_OK)
        self.business.refresh_from_db()
        self.assertEqual(self.business.primary_category, self.bgc_accepted_category)
        self.assertTrue(self.business.booksy_gift_cards_settings.accept_booksy_gift_cards)

    @patch('webapps.business_related.utils._is_kyced_and_active', return_value=True)
    def test_change_category_to_not_accepted(self, _is_kyced_and_active_mock):
        self.business.primary_category = self.bgc_accepted_category
        self.business.categories.add(self.bgc_accepted_category.id)
        self.business.save()
        self.business.refresh_from_db()
        self.assertTrue(self.business.booksy_gift_cards_settings.accept_booksy_gift_cards)

        body = {
            'primary_category': self.categories_ids[0],
            'categories': self.categories_ids,
        }
        response = self.put(body)
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.business.refresh_from_db()
        self.assertEqual(
            self.business.primary_category, self.categories[0]
        )  # pylint: disable=unsubscriptable-object
        self.assertFalse(self.business.booksy_gift_cards_settings.accept_booksy_gift_cards)

    @patch('webapps.business_related.utils._is_kyced_and_active', return_value=False)
    def test_change_category_to_accepted_not_kyced(self, _is_kyced_and_active_mock):
        self.assertFalse(self.business.booksy_gift_cards_settings.accept_booksy_gift_cards)
        self.categories_ids.append(self.bgc_accepted_category.id)

        body = {
            'primary_category': self.bgc_accepted_category.id,
            'categories': self.categories_ids,
        }
        response = self.put(body)
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.business.refresh_from_db()
        self.assertEqual(self.business.primary_category, self.bgc_accepted_category)
        self.assertFalse(self.business.booksy_gift_cards_settings.accept_booksy_gift_cards)


@pytest.mark.django_db
@pytest.mark.usefixtures('default_pos_fixture')
class MyBusinessHandlerCreateTests(BaseAsyncHTTPTest):

    url = '/business_api/me/businesses/'

    def setUp(self):
        super().setUp()

        self.business.delete()
        self.region.delete()

        delattr(self, 'business')
        delattr(self, 'region')

        self.user.email = '<EMAIL>'
        self.user.save()

        self.region_city = baker.make(Region, **REGION_CITY)
        self.region_zip = baker.make(Region, **REGION_ZIP)
        self.region_state = baker.make(Region, **REGION_STATE)
        baker.make(RegionGraph, region=self.region_city, related_region=self.region_zip)
        baker.make(RegionGraph, region=self.region_state, related_region=self.region_city)

    def test_creating_business_without_provided_subdomain(self):
        data = {
            'name': 'Tom M1',
            'phone': '+***********',
            'package': Business.Package.PRO,
        }

        response = self.fetch(self.url, method='POST', body=data)
        assert response.code == status.HTTP_201_CREATED
        assert response.json['business']['subdomain'] == 'tomm1'

    @patch(
        'webapps.subdomain_grpc.serializers.SubdomainSerializerMixin._create_subdomain_draft',
        side_effect=SubdomainGRPCValidationError(errors=[{'error': 'mock'}]),
    )
    def test_creating_business_subdomain_creation_error_handling(self, _):
        data = {
            'name': 'booksy',
            'phone': '+***********',
            'package': Business.Package.PRO,
        }

        response = self.fetch(self.url, method='POST', body=data)
        assert response.code == status.HTTP_400_BAD_REQUEST
        assert response.json == {
            'errors': [
                {
                    'field': 'name',
                    'description': 'Business name invalid. Please choose another.',
                    'code': 'invalid_name',
                },
            ]
        }

    def test_creating_business_with_provided_subdomain(self):
        data = {
            'name': 'Tom M1',
            'phone': '+***********',
            'package': Business.Package.PRO,
            'subdomain': 'loremipsum',
        }

        response = self.fetch(self.url, method='POST', body=data)
        assert response.code == status.HTTP_201_CREATED
        assert response.json['business']['subdomain'] == 'loremipsum'

    def test_creating_business_with_too_short_subdomain(self):
        data = {
            'name': 'Tom',
            'phone': '+***********',
            'package': Business.Package.PRO,
        }

        response = self.fetch(self.url, method='POST', body=data)
        assert response.code == status.HTTP_201_CREATED
        assert response.json['business']['subdomain'] is not None

    def test_creating_business_with_too_long_subdomain(self):
        data = {
            'name': '8Y8E6TB8KPBR UR4JYAT5WCY5TXIU5S1G08ZCY 238A9P2Z9ASFR1B7P5baded',
            'phone': '+***********',
            'package': 'P',
        }
        response = self.fetch(self.url, method='POST', body=data)
        assert response.code == status.HTTP_201_CREATED
        assert response.json['business']['subdomain'] is not None

    def test_if_name_too_long_then_cannot_create_business(self):
        data = {
            'name': ('s' * Business.NAME_MAX_LENGTH) + 's',
            'phone': '+***********',
            'package': 'P',
        }
        response = self.fetch(self.url, method='POST', body=data)
        assert response.code == status.HTTP_400_BAD_REQUEST

    def test_if_short_name_too_long_then_cannot_create_business(self):
        data = {
            'name': ('test_name'),
            'name_short': ('s' * Business.NAME_MAX_LENGTH) + 's',
            'phone': '+***********',
            'package': 'P',
        }
        response = self.fetch(self.url, method='POST', body=data)
        assert response.code == status.HTTP_400_BAD_REQUEST

    def test_creating_business_with_public_email(self):
        data = {
            'name': 'Test Business',
            'phone': '+***********',
            'package': Business.Package.PRO,
            'public_email': '<EMAIL>',
        }

        response = self.fetch(self.url, method='POST', body=data)

        assert response.code == status.HTTP_201_CREATED
        assert response.json['business']['public_email'] == data['public_email']

    def test_creating_business_without_public_email(self):
        data = {
            'name': 'Test Business',
            'phone': '+***********',
            'package': Business.Package.PRO,
        }

        response = self.fetch(self.url, method='POST', body=data)

        assert response.code == status.HTTP_201_CREATED
        assert response.json['business']['public_email'] == self.user.email

    def test_creating_business_partner_apps_enabled_if_not_pl(self):
        data = {
            'name': 'Test Business',
            'phone': '+***********',
            'package': Business.Package.PRO,
        }

        response = self.fetch(self.url, method='POST', body=data)
        self.assertEqual(status.HTTP_201_CREATED, response.code)
        self.assertFalse(response.json['business']['partner_apps_enabled'])

    @override_settings(API_COUNTRY=Country.PL)
    def test_creating_business_partner_apps_enabled_if_pl(self):
        data = {
            'name': 'Test Business',
            'phone': '+***********',
            'package': Business.Package.PRO,
        }

        response = self.fetch(self.url, method='POST', body=data)
        self.assertEqual(status.HTTP_201_CREATED, response.code)
        self.assertTrue(response.json['business']['partner_apps_enabled'])


@pytest.mark.django_db
@pytest.mark.usefixtures('default_pos_fixture')
class TestMyBusinessesHandlerHCaptcha(BaseAsyncHTTPTest):
    def setUp(self):
        self.url = '/business_api/me/businesses/'

        super().setUp()

        self.business_api_key = get_or_create_booking_source(
            app_type=BookingSources.BUSINESS_APP,
            name=FRONTDESK,
        ).api_key

        self.business.delete()
        self.region.delete()

        delattr(self, 'business')
        delattr(self, 'region')

        self.user.email = '<EMAIL>'
        self.user.save()

        self.region_city = baker.make(Region, **REGION_CITY)
        self.region_zip = baker.make(Region, **REGION_ZIP)
        self.region_state = baker.make(Region, **REGION_STATE)
        baker.make(RegionGraph, region=self.region_city, related_region=self.region_zip)
        baker.make(RegionGraph, region=self.region_state, related_region=self.region_city)

        self.data = {
            'name': 'Tom M1',
            'phone': '+***********',
            'package': Business.Package.PRO,
        }

    @patch('service.account.HCaptchaBusinessRequestValidator.verify_hcaptcha', return_value=False)
    def test_hcaptcha_inactive(self, mocked_verify):
        response = self.fetch(
            self.url,
            method='POST',
            body=self.data,
            extra_headers={'x-hcaptcha-token': 'test_token'},
        )

        mocked_verify.assert_not_called()
        self.assertEqual(status.HTTP_201_CREATED, response.code)

    @override_eppo_feature_flag(
        {HCaptchaBusinessFlag.flag_name: True, HCaptchaBusinessFrontdeskFlag.flag_name: True}
    )
    @patch('service.account.HCaptchaBusinessRequestValidator.verify_hcaptcha', return_value=True)
    def test_hcaptcha_active_request_passed(self, mocked_verify):
        response = self.fetch(
            self.url,
            method='POST',
            body=self.data,
            extra_headers={'x-hcaptcha-token': 'test_token'},
        )

        mocked_verify.assert_called()
        self.assertEqual(status.HTTP_201_CREATED, response.code)

    @override_eppo_feature_flag(
        {HCaptchaBusinessFlag.flag_name: True, HCaptchaBusinessFrontdeskFlag.flag_name: True}
    )
    @patch('service.account.HCaptchaBusinessRequestValidator.verify_hcaptcha', return_value=False)
    def test_hcaptcha_active_request_blocked(self, _):
        response = self.fetch(
            self.url,
            method='POST',
            body=self.data,
            extra_headers={'x-hcaptcha-token': 'test_token'},
        )

        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.code)
        error = response.json['errors'][0]
        self.assertIn('Request blocked', error['description'])

    @override_eppo_feature_flag(
        {HCaptchaBusinessFlag.flag_name: True, HCaptchaBusinessFrontdeskFlag.flag_name: True}
    )
    @patch('service.account.HCaptchaBusinessRequestValidator.verify_hcaptcha', return_value=False)
    def test_hcaptcha_active_no_token(self, mocked_verify):
        response = self.fetch(self.url, method='POST', body=self.data)

        mocked_verify.assert_not_called()
        self.assertEqual(status.HTTP_201_CREATED, response.code)

    @override_eppo_feature_flag(
        {
            HCaptchaBusinessFlag.flag_name: True,
            HCaptchaBusinessForceFlag.flag_name: True,
            HCaptchaBusinessFrontdeskFlag.flag_name: True,
        }
    )
    @patch('service.account.HCaptchaBusinessRequestValidator.verify_hcaptcha', return_value=False)
    def test_hcaptcha_force_no_token(self, _):
        response = self.fetch(self.url, method='POST', body=self.data)

        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.code)
        error = response.json['errors'][0]
        self.assertIn('Request blocked', error['description'])

    @override_eppo_feature_flag(
        {
            HCaptchaBusinessFlag.flag_name: True,
            HCaptchaBusinessForceFlag.flag_name: True,
            HCaptchaBusinessFrontdeskFlag.flag_name: True,
        }
    )
    @patch('service.account.HCaptchaBusinessRequestValidator.verify_hcaptcha', return_value=True)
    def test_hcaptcha_force_request_passed(self, mocked_verify):
        response = self.fetch(
            self.url,
            method='POST',
            body=self.data,
            extra_headers={'x-hcaptcha-token': 'test_token'},
        )

        mocked_verify.assert_called()
        self.assertEqual(status.HTTP_201_CREATED, response.code)

    @override_eppo_feature_flag(
        {
            HCaptchaBusinessFlag.flag_name: True,
            HCaptchaBusinessForceFlag.flag_name: True,
            HCaptchaBusinessFrontdeskFlag.flag_name: True,
        }
    )
    @patch('service.account.HCaptchaBusinessRequestValidator.verify_hcaptcha', return_value=False)
    def test_hcaptcha_force_request_blocked(self, _):
        response = self.fetch(
            self.url,
            method='POST',
            body=self.data,
            extra_headers={'x-hcaptcha-token': 'test_token'},
        )

        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.code)
        error = response.json['errors'][0]
        self.assertIn('Request blocked', error['description'])
