import json
from decimal import Decimal

import pytest
from dateutil.relativedelta import relativedelta
from django.conf import settings
from mock import (
    MagicMock,
    patch,
)
from model_bakery import baker
from parameterized import parameterized
from rest_framework import status
from segment.analytics import Client

from lib.feature_flag.feature.payment import NSPFixedPrize
from lib.locks import ServiceVariantLock
from lib.tests.utils import override_eppo_feature_flag
from lib.tools import l_b, id_to_external_api
from service.tests import BaseAsyncHTTPTest, dict_assert
from webapps.booking.models import BookingSources
from webapps.business.baker_recipes import (
    business_recipe,
    service_recipe,
    service_variant_recipe,
    staffer_recipe,
    treatment_recipe,
)
from webapps.business.consts import NOT_ASSIGNED

from webapps.business.enums import (
    ComboPricing,
    ComboType,
    PriceType,
)
from webapps.business.models import (
    Business,
    ComboMembership,
    Resource,
    Service,
    ServiceVariant,
    ServiceVariantPayment,
)
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business.models.category import BusinessCategory
from webapps.consts import FRONTDESK
from webapps.notification.elasticsearch import NotificationHistoryDocument
from webapps.onboarding_space.public import OnboardingSpaceMetricsEnum
from webapps.pos.enums import PaymentTypeEnum
from webapps.pos.models import PaymentType, POS
from webapps.segment.consts import UserRoleEnum
from webapps.warehouse.models import (
    Commodity,
    Warehouse,
    WarehouseFormula,
    WarehouseFormulaRow,
)


@pytest.mark.django_db
class ServiceHandlerTestCase(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{}/services/{}'

    _base_request_body = {
        'gap_time': '00',
        'name': 'Neutral Service Name',
        'padding_time': 0,
        'note_to_customer': 'oh hi',
        'description': 'ciach ciach',
        'tax_rate': 10,
    }

    def setUp(self):
        super().setUp()

        self.business.owner.cell_phone = '*********'
        self.business.owner.save()

        primary_category = baker.make(BusinessCategory)
        self.business.primary_category = primary_category
        self.business.save()
        pos = baker.make(
            POS,
            business=self.business,
            pay_by_app_status=POS.PAY_BY_APP_ENABLED,
        )
        baker.make(PaymentType, pos=pos, code=PaymentTypeEnum.PAY_BY_APP)
        BookingSources.objects.filter(api_key=self.business_api_key).update(name=FRONTDESK)
        self.resource = baker.make(
            Resource,
            type=Resource.STAFF,
            business=self.business,
            staff_user=self.user,
        )

    @patch('service.business.services.segment_analytics.get_segment_api')
    def test_put_remove_gap_time(self, get_segment_api_mock):
        segment_api = MagicMock()
        get_segment_api_mock.return_value = segment_api

        service = baker.make(Service, business=self.business, gap_time='10')
        variant = baker.make(
            ServiceVariant,
            duration='0100',
            service=service,
            gap_hole_duration='0130',
            gap_hole_start_after='0005',
        )

        url = self.url.format(self.business.id, service.id)

        body = self._get_request_body(
            **{
                'resources': [self.resource.id],
                'variants': [
                    {
                        'id': variant.id,
                        'gap_hole_duration': '0000',
                        'time_slot_interval': 15,
                        'price': 10,
                        'duration': 60,
                        'type': PriceType.FIXED,
                    }
                ],
            }
        )

        response = self.fetch(url, method='PUT', body=body)

        assert response.code == 200

        resp_json = json.loads(l_b(response.body))['service']

        assert resp_json['gap_time'] == 0
        assert resp_json['variants'][0]['gap_hole_duration'] == 0
        assert resp_json['variants'][0]['gap_hole_start_after'] == 0

        assert resp_json['variants'][0]['formula'] == []

    def test_put_add_gap_time(self):
        service = baker.make(Service, business=self.business)
        variant = baker.make(ServiceVariant, duration='0100', service=service)
        warehouse = baker.make(Warehouse, business=self.business)
        commodity = baker.make(
            Commodity,
            business=self.business,
        )

        url = self.url.format(self.business.id, service.id)

        body = self._get_request_body(
            **{
                'gap_time': '10',
                'resources': [self.resource.id],
                'variants': [
                    {
                        'id': variant.id,
                        'time_slot_interval': 15,
                        'price': 10,
                        'duration': 140,
                        'type': PriceType.FIXED,
                        'gap_hole_duration': 40,
                        'gap_hole_start_after': 10,
                        'formula': [
                            {
                                'commodity': commodity.id,
                                'count': 1259,
                                'warehouse': warehouse.id,
                            },
                        ],
                    }
                ],
            }
        )

        response = self.fetch(url, method='PUT', body=body)

        assert response.code == 200

        resp_json = json.loads(l_b(response.body))['service']

        assert resp_json['gap_time'] == 10
        assert resp_json['variants'][0]['gap_hole_duration'] == 40
        assert resp_json['variants'][0]['gap_hole_start_after'] == 10

        formula = resp_json['variants'][0]['formula']
        assert formula[0]['commodity']['id'] == commodity.id
        assert 'name' in formula[0]['commodity']
        assert 'volume_unit' in formula[0]['commodity']
        assert formula[0]['count'] == 1259

    @patch('service.business.services.segment_analytics.get_segment_api')
    def test_put_archived_commodity(self, get_segment_api_mock):
        segment_api = MagicMock()
        get_segment_api_mock.return_value = segment_api
        warehouse = baker.make(Warehouse)
        commodity_1 = baker.make(
            Commodity,
            business=self.business,
        )
        commodity_2 = baker.make(
            Commodity,
            business=self.business,
            archived=True,
        )
        commodity_3 = baker.make(
            Commodity,
            business=self.business,
            archived=True,
        )

        service = baker.make(
            Service, business=self.business, gap_time='10', resources=[self.resource]
        )
        service_variant = baker.make(
            ServiceVariant,
            service=service,
            duration=relativedelta(minutes=15),
            type=PriceType.FIXED,
        )
        formula_row_1 = baker.make(
            WarehouseFormulaRow,
            commodity=commodity_1,
            count=2,
            warehouse=warehouse,
        )
        formula_row_2 = baker.make(
            WarehouseFormulaRow,
            commodity=commodity_2,
            count=5,
            warehouse=warehouse,
        )
        baker.make(
            WarehouseFormula,
            service_variants=[service_variant],
            rows=[formula_row_1, formula_row_2],
        )

        url = self.url.format(self.business.id, service.id)

        body_temp = self._get_request_body(
            **{
                'resources': [self.resource.id],
                'variants': [
                    {
                        'id': service_variant.id,
                        'gap_hole_duration': '0000',
                        'time_slot_interval': 15,
                        'price': 10,
                        'duration': 60,
                        'type': PriceType.FIXED,
                        'formula': [
                            {
                                'id': formula_row_1.id,
                                'commodity': commodity_1.id,
                                'count': 2,
                                'warehouse': warehouse.id,
                            },
                            {
                                'id': formula_row_2.id,
                                'commodity': commodity_2.id,
                                'count': 5,
                                'warehouse': warehouse.id,
                            },
                        ],
                    }
                ],
            }
        )

        # change in the formula row with an unarchived commodity - it's OK
        # (row with archived commodity remains unchanged)
        body = body_temp.copy()
        body['variants'][0]['formula'][0]['count'] = 7
        with patch('lib.locks.AbstractLock.try_to_lock', return_value=True):
            response = self.fetch(url, method='PUT', body=body)
        assert response.code == 200

        # change from unarchived commodity to archived - it's wrong
        body = body_temp.copy()
        body['variants'][0]['formula'][0]['commodity'] = commodity_3.id
        with patch('lib.locks.AbstractLock.try_to_lock', return_value=True):
            response = self.fetch(url, method='PUT', body=body)
        assert response.code == 400
        assert response.json['errors'][0]['field'] == 'variants'

        # change in the formula row with an archived commodity - it's wrong
        body = body_temp.copy()
        body['variants'][0]['formula'][1]['count'] = 7
        with patch('lib.locks.AbstractLock.try_to_lock', return_value=True):
            response = self.fetch(url, method='PUT', body=body)
        assert response.code == 400
        assert response.json['errors'][0]['field'] == 'variants'

        # add formula row with an archived commodity - it's wrong
        body = body_temp.copy()
        body['variants'][0]['formula'].append(
            {'commodity': commodity_3.id, 'count': 2, 'warehouse': warehouse.id}
        )
        with patch('lib.locks.AbstractLock.try_to_lock', return_value=True):
            response = self.fetch(url, method='PUT', body=body)
        assert response.code == 400
        assert response.json['errors'][0]['field'] == 'variants'

    def test_put_new_no_show_protection_few_variants(self):
        service = baker.make(Service, business=self.business, gap_time='10')
        variant = baker.make(
            ServiceVariant,
            duration='0100',
            service=service,
            gap_hole_duration='0130',
            gap_hole_start_after='0005',
        )

        url = self.url.format(self.business.id, service.id)
        payment_type = ServiceVariantPayment.PRE_PAYMENT_TYPE

        body = self._get_request_body(
            **{
                'resources': [self.resource.id],
                'variants': [
                    {
                        'id': variant.id,
                        'gap_hole_duration': '0000',
                        'time_slot_interval': 15,
                        'price': 20,
                        'duration': 60,
                        'type': PriceType.FIXED,
                    },
                    {
                        'gap_hole_duration': '0000',
                        'time_slot_interval': 15,
                        'price': 30,
                        'duration': 120,
                        'type': PriceType.FIXED,
                    },
                    {
                        'gap_hole_duration': '0000',
                        'time_slot_interval': 15,
                        'price': 10,
                        'duration': 30,
                        'type': PriceType.FIXED,
                    },
                ],
                'no_show_protection': {
                    "type": payment_type,
                    "percentage": 50,
                },
            }
        )
        with patch('lib.locks.AbstractLock.try_to_lock', return_value=True):
            response = self.fetch(url, method='PUT', body=body)

        assert response.code == 200

        service_json = response.json['service']
        expected = {'type': 'PP', 'percentage': 50}
        dict_assert(
            service_json['no_show_protection'],
            expected,
        )
        dict_assert(
            service_json['variants'][0]['no_show_protection'],
            expected,
        )
        dict_assert(
            service_json['variants'][1]['no_show_protection'],
            expected,
        )
        dict_assert(
            service_json['variants'][2]['no_show_protection'],
            expected,
        )

        body['variants'][0]['no_show_protection'] = None
        body['variants'][1]['no_show_protection'] = {
            "type": payment_type,
            "percentage": 50,
        }
        body['variants'][2]['no_show_protection'] = {
            "type": payment_type,
            "percentage": 50,
        }
        body.pop('no_show_protection')
        response = self.fetch(url, method='PUT', body=body)
        response_json = response.json
        assert response_json['service']['no_show_protection'] is None
        for variant in response_json['service']['variants']:
            assert variant['no_show_protection'] is None

        service_id = response_json['service']['id']
        service_obj = Service.objects.filter(id=service_id).first()
        assert not ServiceVariantPayment.objects.filter(
            service_variant__in=service_obj.service_variants.filter(
                deleted__isnull=True,
            ),
        ).exists()

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_put_new_no_show_protection_from_single(
        self,
        analytics_track_mock,
        analytics_identify_mock,
    ):
        service = baker.make(Service, business=self.business, gap_time='10')
        variant = baker.make(
            ServiceVariant,
            duration='0100',
            service=service,
            gap_hole_duration='0130',
            gap_hole_start_after='0005',
        )

        url = self.url.format(self.business.id, service.id)
        payment_type = ServiceVariantPayment.PRE_PAYMENT_TYPE

        body = self._get_request_body(
            **{
                'gap_time': '00',
                'resources': [self.resource.id],
                'variants': [
                    {
                        'id': variant.id,
                        'gap_hole_duration': '0000',
                        'time_slot_interval': 15,
                        'price': 20,
                        'duration': 60,
                        'type': PriceType.FIXED,
                        'no_show_protection': {
                            "type": payment_type,
                            "percentage": 25,
                        },
                    },
                    {
                        'gap_hole_duration': '0000',
                        'time_slot_interval': 15,
                        'price': 30,
                        'duration': 120,
                        'type': PriceType.FIXED,
                        'no_show_protection': {
                            "type": payment_type,
                            "percentage": 30,
                        },
                    },
                    {
                        'gap_hole_duration': '0000',
                        'time_slot_interval': 15,
                        'price': 10,
                        'duration': 30,
                        'type': PriceType.FIXED,
                        'no_show_protection': {
                            "type": payment_type,
                            "percentage": 50,
                        },
                    },
                ],
            }
        )

        response = self.fetch(url, method='PUT', body=body)

        assert response.code == 200

        service_json = response.json['service']
        expected = {'type': 'PP', 'percentage': 50}
        dict_assert(
            service_json['no_show_protection'],
            expected,
        )
        dict_assert(
            service_json['variants'][0]['no_show_protection'],
            expected,
        )
        dict_assert(
            service_json['variants'][1]['no_show_protection'],
            expected,
        )
        dict_assert(
            service_json['variants'][2]['no_show_protection'],
            expected,
        )

        dict_assert(
            analytics_track_mock.call_args_list[0][1],
            {
                'event': 'Protection_Service_Enabled',
                'properties': {
                    'country': settings.API_COUNTRY,
                    'email': self.business.owner.email,
                    'business_id': id_to_external_api(self.business.id),
                    'booking_protection_enabled': True,
                },
            },
        )
        dict_assert(
            analytics_identify_mock.call_args_list[0][1],
            {
                'traits': {
                    'country': settings.API_COUNTRY,
                    'email': self.business.owner.email,
                    'user_role': UserRoleEnum.OWNER.value,
                    'phone': self.business.owner.cell_phone,
                    'offer_type': Business.Package(
                        self.business.package
                    ).label,  # pylint: disable=no-value-for-parameter
                    'business_id': id_to_external_api(self.business.id),
                    'booking_protection_enabled': True,
                    'services_with_protection': 3,
                },
            },
        )

    def test_put_new_no_show_protection_too_low_error(self):
        service = baker.make(Service, business=self.business, gap_time='10')
        variant = baker.make(
            ServiceVariant,
            duration='0100',
            service=service,
            gap_hole_duration='0130',
            gap_hole_start_after='0005',
        )

        url = self.url.format(self.business.id, service.id)
        payment_type = ServiceVariantPayment.PRE_PAYMENT_TYPE

        body = self._get_request_body(
            **{
                'resources': [self.resource.id],
                'variants': [
                    {
                        'id': variant.id,
                        'gap_hole_duration': '0000',
                        'time_slot_interval': 15,
                        'price': 20,
                        'duration': 60,
                        'type': PriceType.FIXED,
                    },
                    {
                        'gap_hole_duration': '0000',
                        'time_slot_interval': 15,
                        'price': 30,
                        'duration': 120,
                        'type': PriceType.FIXED,
                    },
                    {
                        'gap_hole_duration': '0000',
                        'time_slot_interval': 15,
                        'price': 10,
                        'duration': 30,
                        'type': PriceType.FIXED,
                    },
                ],
                'no_show_protection': {
                    "type": payment_type,
                    "percentage": 10,
                },
            }
        )

        response = self.fetch(url, method='PUT', body=body)

        assert response.code == 400
        dict_assert(
            response.json,
            {
                'errors': [
                    {
                        'field': 'no_show_protection',
                        'description': "Minimal payment amount can't" " be less than $5.00",
                        'code': 'minimal_payment_amount',
                    }
                ],
            },
        )

    def test_put_new_combo_no_show_protection_too_low_error(self):
        combo_service = self._get_combo_service()
        combo_variant = combo_service.service_variants.first()
        through_1, through_2 = combo_variant.combo_children_through.all()

        baker.make(
            ServiceVariantPayment,
            service_variant=through_1.child,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('0.4') * through_1.child.price,
            saving_type=ServiceVariantPayment.PERCENTAGE,
        )

        url = self.url.format(self.business.id, combo_service.id)

        body = self._get_request_body(
            **{
                'resources': [combo_service.active_resources[0].id],
                'combo_type': ComboType.SEQUENCE,
                'variants': [
                    {
                        'id': combo_variant.id,
                        'combo_pricing': ComboPricing.CUSTOM,
                        'combo_children': [
                            {
                                'service_variant': {
                                    'id': through_1.child_id,
                                },
                                'price': 10,
                                'type': PriceType.FIXED,
                            },
                            {
                                'service_variant': {
                                    'id': through_2.child_id,
                                },
                                'price': 20,
                                'type': PriceType.FIXED,
                            },
                        ],
                        'price': 30,
                        'type': PriceType.FIXED,
                        'duration': 30,
                    },
                ],
            }
        )
        response = self.fetch(url, method='PUT', body=body)
        assert response.code == 400
        assert response.json['errors'] == [
            {
                'field': 'variants.0.non_field_errors',
                'description': "Minimal payment amount can't be less than $5.00",
                'code': 'minimal_payment_amount',
            }
        ]

    @override_eppo_feature_flag({NSPFixedPrize.flag_name: True})
    def test_put_new_no_show_protection_too_high_error(self):
        service = baker.make(Service, business=self.business, gap_time='10')
        variant = baker.make(
            ServiceVariant,
            duration='0100',
            service=service,
            gap_hole_duration='0130',
            gap_hole_start_after='0005',
        )

        url = self.url.format(self.business.id, service.id)
        payment_type = ServiceVariantPayment.PRE_PAYMENT_TYPE

        body = self._get_request_body(
            **{
                'resources': [self.resource.id],
                'variants': [
                    {
                        'id': variant.id,
                        'gap_hole_duration': '0000',
                        'time_slot_interval': 15,
                        'price': 20,
                        'duration': 60,
                        'type': PriceType.FIXED,
                    },
                    {
                        'gap_hole_duration': '0000',
                        'time_slot_interval': 15,
                        'price': 30,
                        'duration': 120,
                        'type': PriceType.FIXED,
                    },
                    {
                        'gap_hole_duration': '0000',
                        'time_slot_interval': 15,
                        'price': 10,
                        'duration': 30,
                        'type': PriceType.FIXED,
                    },
                ],
                'no_show_protection': {
                    "type": payment_type,
                    "payment_amount": 401,
                },
            }
        )

        response = self.fetch(url, method='PUT', body=body)

        assert response.code == 400
        dict_assert(
            response.json,
            {
                'errors': [
                    {
                        'field': 'no_show_protection',
                        'description': "No-Show Protection fee can't be more than $400.00.",
                        'code': 'max_payment_amount',
                    }
                ],
            },
        )

    @override_eppo_feature_flag({NSPFixedPrize.flag_name: True})
    def test_put_fix_amount_new_no_show_protection_single_variant_no_price(self):
        service = baker.make(Service, business=self.business, gap_time='10')
        variant = baker.make(
            ServiceVariant,
            duration='0100',
            service=service,
            gap_hole_duration='0130',
            gap_hole_start_after='0005',
        )
        variant_no_price = baker.make(
            ServiceVariant,
            duration='0100',
            service=service,
            gap_hole_duration='0130',
            gap_hole_start_after='0005',
            price=None,
            type=PriceType.FREE,
        )

        url = self.url.format(self.business.id, service.id)
        payment_type = ServiceVariantPayment.PRE_PAYMENT_TYPE

        body = self._get_request_body(
            **{
                'gap_time': '00',
                'resources': [self.resource.id],
                'no_show_protection': {
                    "type": payment_type,
                    "payment_amount": 50,
                },
                'variants': [
                    {
                        'id': variant.id,
                        'gap_hole_duration': '0000',
                        'time_slot_interval': 15,
                        'price': 20,
                        'duration': 60,
                        'type': PriceType.FIXED,
                    },
                    {
                        'id': variant_no_price.id,
                        'gap_hole_duration': '0000',
                        'time_slot_interval': 15,
                        'duration': 60,
                        'type': PriceType.FREE,
                        'label': 'Variant without price',
                    },
                ],
            }
        )

        response = self.fetch(url, method='PUT', body=body)

        assert response.code == 200
        assert response.json['service']['variants'][1]['no_show_protection'] == {
            "type": "PP",
            "payment_amount": 50.0,
        }
        assert response.code == 200
        assert response.json['service']['variants'][0]['no_show_protection'] == {
            "type": "PP",
            "payment_amount": 50.0,
        }

    @override_eppo_feature_flag({NSPFixedPrize.flag_name: True})
    def test_put_percentage_new_no_show_protection_single_variant_no_price(self):
        service = baker.make(Service, business=self.business, gap_time='10')
        variant = baker.make(
            ServiceVariant,
            duration='0100',
            service=service,
            gap_hole_duration='0130',
            gap_hole_start_after='0005',
        )
        variant_no_price = baker.make(
            ServiceVariant,
            duration='0100',
            service=service,
            gap_hole_duration='0130',
            gap_hole_start_after='0005',
            price=None,
            type=PriceType.FREE,
        )

        url = self.url.format(self.business.id, service.id)
        payment_type = ServiceVariantPayment.PRE_PAYMENT_TYPE

        body = self._get_request_body(
            **{
                'gap_time': '00',
                'resources': [self.resource.id],
                'no_show_protection': {
                    "type": payment_type,
                    "percentage": 50,
                },
                'variants': [
                    {
                        'id': variant.id,
                        'gap_hole_duration': '0000',
                        'time_slot_interval': 15,
                        'price': 20,
                        'duration': 60,
                        'type': PriceType.FIXED,
                    },
                    {
                        'id': variant_no_price.id,
                        'gap_hole_duration': '0000',
                        'time_slot_interval': 15,
                        'duration': 60,
                        'type': PriceType.FREE,
                        'label': 'Variant without price',
                    },
                    {
                        'gap_hole_duration': '0000',
                        'time_slot_interval': 15,
                        'price': 30,
                        'duration': 120,
                        'type': PriceType.FIXED,
                    },
                    {
                        'gap_hole_duration': '0000',
                        'time_slot_interval': 15,
                        'price': 10,
                        'duration': 30,
                        'type': PriceType.FIXED,
                    },
                ],
            }
        )

        response = self.fetch(url, method='PUT', body=body)

        assert response.code == 400
        dict_assert(
            response.json,
            {
                'errors': [
                    {
                        'field': 'non_field_errors',
                        'description': "Can't set no show protection for variants without price",
                        'code': 'user_decision',
                    }
                ],
            },
        )

        body['no_show_protection']['skip_variant_if_no_price'] = True
        response = self.fetch(url, method='PUT', body=body)

        assert response.code == 200

        variants = response.json['service']['variants']
        for variant in variants:
            if variant['label'] == 'Variant without price':
                assert variant['no_show_protection'] is None
            else:
                assert variant['no_show_protection'] is not None

    def test_put_new_no_show_protection_real_test_issue(self):
        service = baker.make(Service, business=self.business)
        sv_0 = baker.make(
            ServiceVariant,
            service=service,
            type=PriceType.FIXED,
            price="12.00",
            duration="2000",
            payment=None,
        )
        sv_1 = baker.make(
            ServiceVariant,
            duration='3000',
            service=service,
            type=PriceType.FREE,
            payment=None,
        )

        service = baker.make(Service, business=self.business, gap_time='10')
        baker.make(
            ServiceVariant,
            duration='0100',
            service=service,
            gap_hole_duration='0130',
            gap_hole_start_after='0005',
        )
        baker.make(
            ServiceVariant,
            duration='0100',
            service=service,
            gap_hole_duration='0130',
            gap_hole_start_after='0005',
            price=None,
            type=PriceType.FREE,
        )

        url = self.url.format(self.business.id, service.id)

        body = {
            "id": service.id,
            "name": service.name,
            "order": 2,
            "description": "",
            "padding_type": "",
            "padding_time": 0,
            "gap_time": 0,
            "note_to_customer": None,
            "resources": [self.resource.id],
            "tax_rate": "20.00",
            "parallel_clients": 1,
            "wordcloud": None,
            "color": 17,
            "variants": [
                {
                    "id": sv_0.id,
                    "type": "X",
                    "price": "12.00",
                    "duration": 20,
                    "time_slot_interval": 15,
                    "gap_hole_start_after": 0,
                    "gap_hole_duration": 0,
                    "formula": [],
                    "label": None,
                },
                {
                    "id": sv_1.id,
                    "type": "F",
                    "price": None,
                    "duration": 30,
                    "time_slot_interval": 15,
                    "gap_hole_start_after": 0,
                    "gap_hole_duration": 0,
                    "formula": [],
                    "label": None,
                },
            ],
            "questions": [],
            "default_questions": [],
            "is_available_for_customer_booking": True,
            "is_online_service": False,
            "is_traveling_service": False,
            "photos": [],
            "no_show_protection": {
                "type": "PP",
                "percentage": 50,
            },
        }

        response = self.fetch(url, method='PUT', body=body)

        assert response.code == 400
        dict_assert(
            response.json,
            {
                'errors': [
                    {
                        'field': 'non_field_errors',
                        'description': "Can't set no show protection for variants without price",
                        'code': 'user_decision',
                    }
                ],
            },
        )

    def test_put_last_available_service(self):
        service = baker.make(Service, business=self.business)
        service_variant = baker.make(
            ServiceVariant,
            service=service,
            type=PriceType.FIXED,
            price="12.00",
            duration="2000",
            payment=None,
        )

        url = self.url.format(self.business.id, service.id)
        url += '?skip_extra_validation=false'

        body = {
            "id": service.id,
            "name": service.name,
            "order": 2,
            "description": "",
            "padding_type": "",
            "padding_time": 0,
            "gap_time": 0,
            "note_to_customer": None,
            "resources": [self.resource.id],
            "tax_rate": "20.00",
            "parallel_clients": 1,
            "wordcloud": None,
            "color": 17,
            "variants": [
                {
                    "id": service_variant.id,
                    "type": "X",
                    "price": "12.00",
                    "duration": 20,
                    "time_slot_interval": 15,
                    "gap_hole_start_after": 0,
                    "gap_hole_duration": 0,
                    "formula": [],
                    "label": None,
                },
            ],
            "questions": [],
            "default_questions": [],
            "is_available_for_customer_booking": False,
            "is_online_service": False,
            "is_traveling_service": False,
            "photos": [],
            "no_show_protection": {
                "type": "PP",
                "percentage": 50,
            },
        }

        response = self.fetch(url, method='PUT', body=body)

        assert response.code == 400
        dict_assert(
            response.json,
            {
                'errors': [
                    {
                        'description': 'This is last available service.',
                        'code': 'last_available_service_error',
                    }
                ],
            },
        )

    def test_put_last_available_service_with_invites(self):
        bci = baker.make(
            BusinessCustomerInfo,
            business=self.business,
            first_name='A',
            last_name='B',
            email='<EMAIL>',
        )
        bci.invite()
        NotificationHistoryDocument.tasks_refresh()

        service = baker.make(Service, business=self.business)
        service_variant = baker.make(
            ServiceVariant,
            service=service,
            type=PriceType.FIXED,
            price="12.00",
            duration="2000",
            payment=None,
        )

        url = self.url.format(self.business.id, service.id)
        url += '?skip_extra_validation=false'

        body = {
            "id": service.id,
            "name": service.name,
            "order": 2,
            "description": "",
            "padding_type": "",
            "padding_time": 0,
            "gap_time": 0,
            "note_to_customer": None,
            "resources": [self.resource.id],
            "tax_rate": "20.00",
            "parallel_clients": 1,
            "wordcloud": None,
            "color": 17,
            "variants": [
                {
                    "id": service_variant.id,
                    "type": "X",
                    "price": "12.00",
                    "duration": 20,
                    "time_slot_interval": 15,
                    "gap_hole_start_after": 0,
                    "gap_hole_duration": 0,
                    "formula": [],
                    "label": None,
                },
            ],
            "questions": [],
            "default_questions": [],
            "is_available_for_customer_booking": False,
            "is_online_service": False,
            "is_traveling_service": False,
            "photos": [],
            "no_show_protection": {
                "type": "PP",
                "percentage": 50,
            },
        }

        response = self.fetch(url, method='PUT', body=body)

        assert response.code == 400
        dict_assert(
            response.json,
            {
                'errors': [
                    {
                        'description': 'Invites were sent during last 3 days.',
                        'code': 'invites_error',
                    },
                    {
                        'description': 'This is last available service.',
                        'code': 'last_available_service_error',
                    },
                ],
            },
        )

    def test_put_for_race_condition__lock_works(self):
        service = baker.make(Service, business=self.business, gap_time='10')
        variant = baker.make(
            ServiceVariant,
            duration='0100',
            service=service,
            gap_hole_duration='0130',
            gap_hole_start_after='0005',
        )

        url = f'/business_api/me/businesses/{self.business.id}/services/{service.id}'
        payment_type = ServiceVariantPayment.PRE_PAYMENT_TYPE

        body = self._get_request_body(
            **{
                'resources': [self.resource.id],
                'variants': [
                    {
                        'id': variant.id,
                        'gap_hole_duration': '0000',
                        'time_slot_interval': 15,
                        'price': 20,
                        'duration': 60,
                        'type': PriceType.FIXED,
                    },
                    {
                        'gap_hole_duration': '0000',
                        'time_slot_interval': 15,
                        'price': 30,
                        'duration': 120,
                        'type': PriceType.FIXED,
                    },
                    {
                        'gap_hole_duration': '0000',
                        'time_slot_interval': 15,
                        'price': 10,
                        'duration': 30,
                        'type': PriceType.FIXED,
                    },
                ],
                'no_show_protection': {
                    "type": payment_type,
                    "percentage": 50,
                },
            }
        )
        ServiceVariantLock.lock(self.business.id)
        response = self.fetch(url, method='PUT', body=body)
        self.assertEqual(response.code, status.HTTP_409_CONFLICT)

    @patch(
        'webapps.onboarding_space.application.services.onboarding_space.'
        'OnboardingSpaceService.decrement_metric'
    )
    def test_delete_decrement_onboarding_space_metric(self, decrement_metric_mock):
        service = baker.make(Service, business=self.business, gap_time='10')
        url = self.url.format(self.business.id, service.id)
        response = self.fetch(url, method='DELETE')
        assert response.code == status.HTTP_200_OK
        assert decrement_metric_mock.call_count == 1
        assert decrement_metric_mock.call_args[1]['business_id'] == self.business.id
        assert decrement_metric_mock.call_args[1]['metric'] == OnboardingSpaceMetricsEnum.SERVICES
        assert decrement_metric_mock.call_args[1]['count'] == 1

    def test_delete_combo_children_with_parents(self):
        combo_service = self._get_combo_service()
        combo_service_variant = combo_service.active_variants.first()
        child_service_1 = combo_service_variant.combo_children_through.first().child.service

        url = self.url.format(self.business.id, child_service_1.id)

        ids_to_delete = [child_service_1.id, combo_service.id]
        response = self.fetch(url, method='DELETE')
        assert response.code == status.HTTP_400_BAD_REQUEST
        assert Service.objects.filter(id__in=ids_to_delete).count() == len(ids_to_delete)

        url += '?skip_combo_validation=true'
        response = self.fetch(url, method='DELETE')
        assert response.code == status.HTTP_200_OK
        assert Service.objects.filter(id__in=ids_to_delete).count() == 0

    def _get_combo_service(self):
        child_service_variant_1 = service_variant_recipe.make(
            service=service_recipe.make(
                business=self.business,
                name='Combo Child 1',
            ),
            duration=relativedelta(minutes=30),
            time_slot_interval=relativedelta(minutes=15),
            price=30,
        )

        child_service_variant_2 = service_variant_recipe.make(
            service=service_recipe.make(
                business=self.business,
                name='Combo Child 2',
            ),
            duration=relativedelta(minutes=30),
            time_slot_interval=relativedelta(minutes=15),
            price=20,
        )

        combo_service_variant = service_variant_recipe.make(
            service=service_recipe.make(
                business=self.business,
                name='Combo Service',
                combo_type=ComboType.SEQUENCE,
            ),
            time_slot_interval=relativedelta(minutes=15),
            price=None,
            type=None,
            duration=None,
            combo_pricing=ComboPricing.SERVICES,
        )
        ComboMembership.objects.bulk_create(
            [
                ComboMembership(
                    combo=combo_service_variant,
                    child=child_service_variant_1,
                    order=1,
                ),
                ComboMembership(
                    combo=combo_service_variant,
                    child=child_service_variant_2,
                    order=2,
                ),
            ]
        )

        staffers = staffer_recipe.make(business=self.business, _quantity=3)
        for service in (
            combo_service_variant.service,
            child_service_variant_1.service,
            child_service_variant_2.service,
        ):
            service.add_staffers(staffers)

        return combo_service_variant.service

    def _get_request_body(self, **kwargs):
        body = dict(self._base_request_body)
        body.update(**kwargs)
        return body

    def test_put_service_with_treatment_selected_by_user(self):
        treatment = treatment_recipe.make()
        new_treatment = treatment_recipe.make()
        service = baker.make(Service, business=self.business, treatment=treatment)
        variant = baker.make(ServiceVariant, service=service)

        url = self.url.format(self.business.id, service.id)

        body = self._get_request_body(
            **{
                'resources': [self.resource.id],
                'variants': [
                    {
                        'id': variant.id,
                        'gap_hole_duration': '0000',
                        'time_slot_interval': 15,
                        'price': 10,
                        'duration': 60,
                        'type': PriceType.FIXED,
                    }
                ],
                'treatment': new_treatment.id,
                'is_treatment_selected_by_user': True,
            }
        )

        response = self.fetch(url, args={'use_service_type': 1}, method='PUT', body=body)
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertTrue(response.json['service']['is_treatment_selected_by_user'])
        self.assertEqual(response.json['service']['treatment'], new_treatment.id)

        service.refresh_from_db()
        self.assertTrue(service.is_treatment_selected_by_user)
        self.assertEqual(service.treatment.id, new_treatment.id)

    def test_put_service_with_treatment_not_selected_by_user(self):
        treatment = treatment_recipe.make()
        new_treatment = treatment_recipe.make()
        service = baker.make(Service, business=self.business, treatment=treatment)
        variant = baker.make(ServiceVariant, service=service)

        url = self.url.format(self.business.id, service.id)

        body = self._get_request_body(
            **{
                'resources': [self.resource.id],
                'variants': [
                    {
                        'id': variant.id,
                        'gap_hole_duration': '0000',
                        'time_slot_interval': 15,
                        'price': 10,
                        'duration': 60,
                        'type': PriceType.FIXED,
                    }
                ],
                'treatment': new_treatment.id,
                'is_treatment_selected_by_user': False,
            }
        )

        response = self.fetch(url, args={'use_service_type': 1}, method='PUT', body=body)
        self.assertEqual(response.code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(response.json['errors'][0]['description'], 'Service type is required')

    def test_get_service_with_treatment_selected_by_user(self):
        treatment = treatment_recipe.make()
        service = baker.make(
            Service,
            business=self.business,
            treatment=treatment,
            is_treatment_selected_by_user=True,
        )

        url = self.url.format(self.business.id, service.id)
        response = self.fetch(url, method='GET')

        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertTrue(response.json['service']['is_treatment_selected_by_user'])
        self.assertEqual(response.json['service']['treatment'], treatment.id)
        self.assertEqual(response.json['service']['treatment_name'], treatment.name)

    def test_get_service_with_treatment_not_selected_by_user(self):
        treatment = treatment_recipe.make()
        service = baker.make(
            Service,
            business=self.business,
            treatment=treatment,
            is_treatment_selected_by_user=False,
        )

        url = self.url.format(self.business.id, service.id)
        response = self.fetch(url, method='GET')

        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertFalse(response.json['service']['is_treatment_selected_by_user'])
        self.assertIsNone(response.json['service']['treatment'])
        self.assertIsNone(response.json['service']['treatment_name'])

    def test_get_service_with_no_matching_treatment(self):
        service = baker.make(
            Service,
            business=self.business,
            treatment=None,
            is_treatment_selected_by_user=True,
        )

        url = self.url.format(self.business.id, service.id)
        response = self.fetch(url, method='GET')

        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertTrue(response.json['service']['is_treatment_selected_by_user'])
        self.assertIsNone(response.json['service']['treatment'])
        self.assertEqual(response.json['service']['treatment_name'], NOT_ASSIGNED)

    def test_get_service_with_service_variant_and_combo(self):
        service = baker.make(Service, business=self.business, order=0)
        combo_service_variant = service_variant_recipe.make(
            service=service, label='ServiceVariantCombo'
        )
        child_service_variant = service_variant_recipe.make(
            service=service, label='ServiceVariantChild'
        )
        ComboMembership.objects.create(
            combo=combo_service_variant,
            child=child_service_variant,
            order=1,
        )

        url = self.url.format(self.business.id, service.id)
        response = self.fetch(url, method='GET')

        self.assertEqual(response.code, status.HTTP_200_OK)
        variant_ids = []
        for variant in response.json['service']['variants']:
            variant_id = variant['id']
            if variant_id == child_service_variant.id:
                self.assertEqual(variant['label'], 'ServiceVariantChild')
            elif variant_id == combo_service_variant.id:
                self.assertEqual(variant['label'], 'ServiceVariantCombo')
                self.assertEqual(
                    variant['combo_children'][0]['service_variant']['label'], 'ServiceVariantChild'
                )
            variant_ids.append(variant_id)
        self.assertEqual(
            set(variant_ids), set([combo_service_variant.id, child_service_variant.id])
        )


class ServiceChangesTestCase(ServiceHandlerTestCase):
    price = 150
    duration = 15
    name = 'service 1'

    @parameterized.expand(
        [
            (
                duration,
                duration,
                price,
                price + 100,
                name,
                name,
                Business.Status.SETUP,
                False,
            ),  # will unflag service as suggested if price changed during business setup
            (
                duration,
                duration,
                price,
                price + 100,
                name,
                name,
                Business.Status.PAID,
                True,
            ),  # will not unflag service as suggested if price changed outside of business setup
            (
                duration,
                duration + 15,
                price,
                price,
                name,
                name,
                Business.Status.SETUP,
                False,
            ),  # will unflag service as suggested if duration changed during business setup
            (
                duration,
                duration + 15,
                price,
                price,
                name,
                name,
                Business.Status.PAID,
                True,
            ),  # will not unflag service as suggested if duration changed outside of business setup
            (
                duration,
                duration,
                price,
                price,
                name,
                'new' + name,
                Business.Status.SETUP,
                False,
            ),  # will unflag service as suggested if name changed during business setup
            (
                duration,
                duration,
                price,
                price,
                name,
                'new' + name,
                Business.Status.PAID,
                True,
            ),  # will not unflag service as suggested if name changed outside of business setup
        ]
    )
    def test_modifying_service_price_or_duration_or_name_can_toggle_service_is_suggested_flag(
        self,
        duration,
        new_duration,
        price,
        new_price,
        name,
        new_name,
        business_status,
        expected_flag_value,
    ):
        business = business_recipe.make(
            owner=self.user,
            name='test business',
            active=True,
            status=business_status,
        )

        resource = baker.make(
            Resource,
            type=Resource.STAFF,
            business=business,
            staff_user=self.user,
        )

        service = baker.make(Service, name=name, business=business, is_suggested=True)

        variant = baker.make(
            ServiceVariant,
            price=Decimal(price).quantize(Decimal('0.00')),
            duration=relativedelta(minutes=duration),
            service=service,
        )

        url = self.url.format(business.id, service.id)

        body = self._get_request_body(
            resources=[resource.id],
            name=new_name,
            variants=[
                {
                    'id': variant.id,
                    'gap_hole_duration': '0000',
                    'time_slot_interval': 15,
                    'price': new_price,
                    'duration': new_duration,
                    'type': PriceType.FIXED,
                }
            ],
        )

        response = self.fetch(url, method='PUT', body=body)

        assert response.code == 200
        assert Service.objects.get(id=service.id).is_suggested == expected_flag_value
