from datetime import timed<PERSON><PERSON>
from urllib.parse import quote

import pytest
from django.conf import settings
from django.test.utils import override_settings
from mock import (
    MagicMock,
    patch,
)
from model_bakery import baker
from rest_framework import status
from segment.analytics import Client

from country_config.enums import Country
from lib.feature_flag.feature import (
    ServiceTypeAlertGenerationNumberFlag,
    ServiceTypeCampaignFlag,
)
from lib.test_utils import spy_mock
from lib.tests.utils import override_feature_flag
from lib.tools import (
    id_to_external_api,
    tznow,
)
from service.tests import BaseAsyncHTTPTest, dict_assert
from webapps.billing.enums import TransactionStatus, TransactionSource
from webapps.billing.models import BillingTransaction
from webapps.business.baker_recipes import business_recipe, category_recipe
from webapps.business.elasticsearch.account import BusinessAccountDocument
from webapps.business.enums import CustomData, BusinessCategoryEnum
from webapps.business.models import (
    Business,
    BusinessUserInternalData,
    Resource,
    Service,
    ReTrialAttempt,
)
from webapps.business.models.category import BusinessCategory
from webapps.business.models.external import AppsFlyer
from webapps.business.tasks import business_visible_delay_task
from webapps.consts import ANDROID, WEB
from webapps.kill_switch.models import KillSwitch
from webapps.segment.consts import UserRoleEnum
from webapps.segment.enums import BooksyAppVersions, DeviceTypeName
from webapps.segment.models import AppUsedByUserByDate
from webapps.segment.tasks import save_used_app_version_task
from webapps.structure.models import Region
from webapps.subdomain_grpc.client import SubdomainGRPC
from webapps.user.baker_recipes import user_recipe
from webapps.user.enums import AuthOriginEnum
from webapps.user.models import User, UserInternalData
from webapps.warehouse.models import Warehouse


@pytest.mark.django_db
@pytest.mark.usefixtures('default_pos_fixture')
class MyBusinessesTestCase(BaseAsyncHTTPTest):

    url = '/business_api/me/businesses/'
    body = {'name': 'Biznes pierwszy'}

    def test_response_business_fields(self):
        BusinessAccountDocument.reindex(refresh_index=True)
        expected_business_keys = {
            'active',
            'id',
            'location',
            'name',
            'official_name',
            'phone',
            'pos_enabled',
            'status',
        }
        expected_location_keys = {'address', 'city', 'zipcode'}

        response = self.fetch(self.url)
        self.assertEqual(response.code, status.HTTP_200_OK)
        self.assertTrue(response.json)

        self.assertIn('businesses', response.json)
        self.assertEqual(len(response.json['businesses']), 1)

        business_keys = set(response.json['businesses'][0])
        location_keys = set(response.json['businesses'][0]['location'])

        self.assertTrue(expected_business_keys.issubset(business_keys))
        self.assertTrue(expected_location_keys.issubset(location_keys))

    def test_existing_business_owner(self):
        resp = self.fetch(self.url, method='POST', body=self.body)
        self.assertEqual(resp.code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(resp.json['errors'][0]['code'], 'unique')

    def test_existing_staffer(self):
        self.bad_user = baker.make(User, first_name='Zly', last_name='Juzer')
        self.staffer = baker.make(Resource, staff_user=self.bad_user)
        # access_level='owner'
        self.session = self.bad_user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

        resp = self.fetch(self.url, method='POST', body=self.body)
        self.assertEqual(resp.code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(resp.json['errors'][0]['code'], 'unique')

    @override_settings(POS=False)
    def test_business_owner(self):
        self.user = user_recipe.make(first_name='Dobry', last_name='Juzer')
        # access_level='owner'
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        resp = self.fetch(self.url, method='POST', body=self.body)
        self.assertEqual(resp.code, status.HTTP_201_CREATED)
        self.assertEqual(len(Business.objects.all()), 2)

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_business_create_with_analytics_201(
        self, analytics_track_mock, analytics_identify_mock
    ):
        self.business.delete()
        body = {
            'auto_correct': True,
            'name': 'jsahdbashdasd asjdbasd',
            'segment_business_id': '0000000000000000',
            'subdomain': 'jsahdbashdasdasjdbasd',
        }
        response = self.fetch(self.url, method='POST', body=body)
        self.assertEqual(response.code, status.HTTP_201_CREATED)
        business_id = response.json['business']['id']
        business = Business.objects.filter(id=business_id).first()
        self.assertTrue(business)
        self.assertEqual(business.status, Business.Status.SETUP)

        event_params_dict = {
            'business_admin_status': Business.Status.SETUP.name,
            'app_version': '3.0.0',
            'business_id': id_to_external_api(business_id),
            'business_name': business.name,
            'country': settings.API_COUNTRY,
            'created_at': business.created.date().isoformat(),
            'device_type': DeviceTypeName.UNKNOWN,
            'email': business.owner.email,
            'offer_type': Business.Package.UNKNOWN.label,
            'owner_email': business.owner.email,
            'owner_name': business.owner.full_name,
            'phone': business.phone,
            'user_id': id_to_external_api(business.owner.id),
            'user_role': UserRoleEnum.OWNER,
        }
        dict_assert(
            analytics_track_mock.call_args_list[0][1],
            {
                'event': 'Business_Registration_Started_Backend',
                'properties': event_params_dict,
            },
        )
        dict_assert(
            analytics_identify_mock.call_args_list[0][1],
            {
                'traits': {
                    'event_name': 'python.analytics_business_registration_started_task',
                    **event_params_dict,
                },
            },
        )

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_creating_business_with_no_categories_will_not_trigger_categories_changed_analytics(
        self, analytics_track_mock, analytics_identify_mock
    ):
        self.business.delete()
        body = {
            'auto_correct': True,
            'name': 'jsahdbashdasd asjdbasd',
            'segment_business_id': '0000000000000000',
            'subdomain': 'jsahdbashdasdasjdbasd',
        }
        response = self.fetch(self.url, method='POST', body=body)
        self.assertEqual(response.code, status.HTTP_201_CREATED)

        assert analytics_track_mock.call_count == 1
        assert analytics_identify_mock.call_count == 1

        assert analytics_track_mock.call_args_list[0][1]['event'] != 'Business_Categories_Updated'
        assert (
            analytics_identify_mock.call_args_list[0][1]['traits']['event_name']
            != 'python.analytics_business_categories_updated_task'
        )

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_creating_business_with_categories_will_trigger_categories_changed_analytics(
        self, analytics_track_mock, analytics_identify_mock
    ):
        category = baker.make(
            BusinessCategory,
            internal_name=BusinessCategoryEnum.OTHER,
            type=BusinessCategory.CATEGORY,
        )
        self.business.delete()
        body = {
            "name": "test",
            "package": "P",
            "phone": "+**************",
            "categories": [category.id],
            "primary_category": category.id,
        }

        response = self.fetch(self.url, method='POST', body=body)
        self.assertEqual(response.code, status.HTTP_201_CREATED)

        business = Business.objects.first()

        assert analytics_track_mock.call_count == 2
        dict_assert(
            analytics_track_mock.call_args_list[1][1],
            {
                'event': 'Business_Categories_Updated',
                'properties': {
                    'email': business.owner.email,
                    'business_id': id_to_external_api(business.id),
                    'business_name': business.name,
                    'business_primary_category': (category.internal_name),
                    'business_categories': ['Other'],
                },
            },
        )

        assert analytics_identify_mock.call_count == 2
        dict_assert(
            analytics_identify_mock.call_args_list[1][1],
            {
                'traits': {
                    'event_name': 'python.analytics_business_categories_updated_task',
                    'country': settings.API_COUNTRY,
                    'email': business.owner.email,
                    'user_role': UserRoleEnum.OWNER,
                    'business_id': id_to_external_api(business.id),
                    'business_name': business.name,
                    'business_primary_category': category.internal_name,
                    'business_categories': ['Other'],
                },
            },
        )

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_business_create_with_analytics_201_and_killswitch(
        self, analytics_track_mock, analytics_identify_mock
    ):
        baker.make(
            KillSwitch,
            name=KillSwitch.MarTech.BUSINESS_REGISTRATION_STARTED,
            is_killed=True,
        )
        self.business.delete()
        body = {
            'auto_correct': True,
            'name': 'jsahdbashdasd asjdbasd',
            'segment_business_id': '0000000000000000',
            'subdomain': 'jsahdbashdasdasjdbasd',
        }
        response = self.fetch(self.url, method='POST', body=body)
        self.assertEqual(response.code, status.HTTP_201_CREATED)
        business_id = response.json['business']['id']
        business = Business.objects.filter(id=business_id).first()
        self.assertTrue(business)
        self.assertEqual(business.status, Business.Status.SETUP)

        self.assertEqual(analytics_track_mock.call_count, 0)
        self.assertEqual(analytics_identify_mock.call_count, 0)

    def test_business_create_with_analytics_201_subdomain(self):
        baker.make(
            KillSwitch,
            name=KillSwitch.MarTech.BUSINESS_REGISTRATION_STARTED,
            is_killed=True,
        )
        self.business.delete()
        body = {
            'auto_correct': True,
            'name': 'jsahdbashdasd asjdbasd',
            'segment_business_id': '0000000000000000',
            'subdomain': 'jsahdbashdasdasjdbasd',
        }
        response = self.fetch(self.url, method='POST', body=body)
        self.assertEqual(response.code, status.HTTP_201_CREATED)
        business_id = response.json['business']['id']
        business = Business.objects.filter(id=business_id).first()
        self.assertTrue(business)
        self.assertEqual(business.status, Business.Status.SETUP)

        subdomain = SubdomainGRPC.search(
            data=dict(
                business_id=business.id,
            )
        )[0]
        self.assertDictEqual(
            subdomain['deeplinks'], dict(blast='', general='', sms='', marketplace_url='')
        )

    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_business_create_with_analytics_200(
        self, analytics_track_mock, analytics_identify_mock
    ):
        self.business.delete()
        self.business = baker.make(Business, owner=self.user)
        body = {
            'auto_correct': True,
            'name': 'jsahdbashdasd asjdbasd',
            'segment_business_id': '0000000000000000',
            'subdomain': 'jsahdbashdasdasjdbasd',
        }
        response = self.fetch(self.url, method='POST', body=body)
        self.assertEqual(response.code, status.HTTP_200_OK)
        business_id = response.json['business']['id']
        business = Business.objects.filter(id=business_id).first()
        self.assertTrue(business)
        self.assertEqual(business.status, Business.Status.SETUP)

        event_params_dict = {
            'business_admin_status': Business.Status.SETUP.name,
            'app_version': '3.0.0',
            'business_id': id_to_external_api(business_id),
            'business_name': business.name,
            'country': settings.API_COUNTRY,
            'created_at': business.created.date().isoformat(),
            'device_type': DeviceTypeName.UNKNOWN,
            'email': business.owner.email,
            'offer_type': Business.Package.UNKNOWN.label,
            'owner_email': business.owner.email,
            'owner_name': business.owner.full_name,
            'phone': business.phone,
            'user_id': id_to_external_api(business.owner.id),
            'user_role': UserRoleEnum.OWNER,
        }

        dict_assert(
            analytics_track_mock.call_args_list[0][1],
            {
                'event': 'Business_Registration_Started_Backend',
                'properties': event_params_dict,
            },
        )

        dict_assert(
            analytics_identify_mock.call_args_list[0][1],
            {
                'traits': {
                    'event_name': 'python.analytics_business_registration_started_task',
                    **event_params_dict,
                },
            },
        )

    def test_show_active_businesses_firstly(self):
        business_2 = business_recipe.make(owner=self.user, active=False)
        business_2.status = Business.Status.SUSPENDED
        business_2.save(update_fields=['status'])
        business_2.reindex()
        business_3 = business_recipe.make(owner=self.user)
        BusinessAccountDocument.reindex(refresh_index=True)

        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        resp = self.fetch(self.url, method='GET')
        self.assertListEqual(
            [biz['id'] for biz in resp.json['businesses']],
            [self.business.id, business_3.id, business_2.id],
        )

    def test_show_active_businesses_firstly_with_only_one_biz_per_page(self):
        self.business.status = Business.Status.CHURNED
        self.business.save()
        self.assertFalse(self.business.active)
        self.business.reindex()

        business_2 = business_recipe.make(owner=self.user)
        self.assertTrue(business_2.active)
        business_2.reindex()

        BusinessAccountDocument.reindex(refresh_index=True)
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        resp = self.fetch(self.url, method='GET', args={'businesses_per_page': 1})
        self.assertListEqual([biz['id'] for biz in resp.json['businesses']], [business_2.id])

    def test_filter_owner_businesses_by_name(self):
        BusinessAccountDocument.reindex(refresh_index=True)
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

        name = self.business.name[:2].lower()
        resp = self.fetch(self.url, method='GET', args={'name': quote(name)})
        self.assertListEqual(
            [biz['id'] for biz in resp.json['businesses']],
            [self.business.id],
        )

        name = self.business.name.upper()
        resp = self.fetch(self.url, method='GET', args={'name': quote(name)})
        self.assertListEqual(
            [biz['id'] for biz in resp.json['businesses']],
            [self.business.id],
        )

        resp = self.fetch(self.url, method='GET', args={'name': '404'})
        self.assertListEqual(
            [biz['id'] for biz in resp.json['businesses']],
            [],
        )

    def test_filter_staffer_businesses_by_name(self):
        owner = baker.make(User, first_name='Pan', last_name='Prezes')
        staffer = baker.make(User, first_name='Stanisław', last_name='Wita')

        business_1 = baker.make(Business, owner=owner, name='Siedziba 1')
        business_2 = baker.make(Business, owner=owner, name='Siedziba 2')
        business_3 = baker.make(Business, owner=owner, name='Oddział 1')
        baker.make(Resource, staff_user=staffer, business=business_1)
        baker.make(Resource, staff_user=staffer, business=business_2)
        baker.make(Resource, staff_user=staffer, business=business_3)

        BusinessAccountDocument.reindex(refresh_index=True)
        self.session = staffer.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

        resp = self.fetch(self.url, method='GET', args={'name': 'Siedziba'})
        self.assertListEqual(
            [biz['id'] for biz in resp.json['businesses']],
            [business_1.id, business_2.id],
        )

        resp = self.fetch(self.url, method='GET', args={'name': 'siedzi'})
        self.assertListEqual(
            [biz['id'] for biz in resp.json['businesses']],
            [business_1.id, business_2.id],
        )

        resp = self.fetch(self.url, method='GET', args={'name': 'odd'})
        self.assertListEqual(
            [biz['id'] for biz in resp.json['businesses']],
            [business_3.id],
        )

    def test_business_create_with_subdomain_without_category(self):
        self.business.delete()
        body = {  # payload from test instance from browser network log
            'name': 'jsahdbashdasd asjdbasd',
            'package': 'P',
            'phone': '****** 613 2293',
        }

        # pylint: disable=protected-access
        spy__branchio_generate_data = spy_mock(Business._branchio_generate_data)

        with patch(
            'webapps.business.models.Business._branchio_generate_data',
            spy__branchio_generate_data,
        ):
            response = self.fetch(self.url, method='POST', body=body)
        self.assertEqual(response.code, status.HTTP_201_CREATED)
        business_id = response.json['business']['id']
        business = Business.objects.filter(id=business_id).first()
        self.assertTrue(business)
        self.assertEqual(business.status, Business.Status.SETUP)
        self.assertTrue(
            SubdomainGRPC.search(
                data=dict(business_id=business_id, country_code=settings.API_COUNTRY)
            )
        )
        for single_call_result in spy__branchio_generate_data.mock.result:
            self.assertIn('None', single_call_result['$desktop_url'])
            self.assertNotIn('__', single_call_result['$desktop_url'])


@pytest.mark.django_db
class MyBusinessHandlerTestCase(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{}'

    def setUp(self):
        super().setUp()
        self.primary_category = baker.make(
            BusinessCategory,
            internal_name=BusinessCategoryEnum.THERAPY,
            type=BusinessCategory.CATEGORY,
        )
        self.business.primary_category = self.primary_category
        self.business.save()
        self.region_zip1 = baker.make(
            Region,
            name='01000',
            time_zone_name='Europe/Warsaw',
            type=Region.Type.ZIP,
        )
        self.business.region = self.region_zip1
        self.business.save(update_fields=['region'])
        self.region_zip2 = baker.make(
            Region,
            name='00010',
            time_zone_name='Europe/Amsterdam',
            type=Region.Type.ZIP,
        )

    def tearDown(self):
        super().tearDown()
        AppUsedByUserByDate.objects.all().delete()
        AppUsedByUserByDate.pop_river_values()

    @override_settings(POS=False)
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_business_region_and_timezone(
        self,
        analytics_track_mock,
        analytics_identify_mock,
    ):
        # set business time zone to empty
        self.business.time_zone_name = None
        self.business.region = None
        self.business.save()

        # access_level='owner'
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        resp = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={
                "location": {
                    "zipcode": "01000",
                }
            },
        )
        assert resp.code == status.HTTP_200_OK

        self.business.refresh_from_db()
        assert self.business.time_zone_name == 'Europe/Warsaw'

        assert analytics_track_mock.call_count == 1
        dict_assert(
            analytics_track_mock.call_args_list[0][1],
            {
                # AnalyticsContext
                # 'user_id': id_to_external_api(self.business.id),
                'event': 'Business_Info_Updated',
                'properties': {
                    'country': settings.API_COUNTRY,
                    # 'device': None,
                    'business_id': id_to_external_api(self.business.id),
                    'business_name': self.business.name,
                    'user_id': id_to_external_api(self.user.id),
                    'email': self.business.owner.email,
                    'owner_email': self.business.owner.email,
                    'owner_name': self.business.owner.full_name,
                    'phone': self.business.owner.cell_phone,
                    'business_phone': self.business.phone,
                    'address': self.business.get_formatted_address()['address'],
                    'city': self.business.city_or_region_city,
                    'postalCode': self.business.zip,
                    'state': None,
                    'urban_area': None,
                    'urban_subarea': None,
                    'focus_area': None,
                },
            },
        )

        assert analytics_identify_mock.call_count == 1
        dict_assert(
            analytics_identify_mock.call_args_list[0][1],
            {
                # AnalyticsContext
                # 'user_id': id_to_external_api(self.business.id),
                'traits': {
                    'event_name': 'python.analytics_business_info_updated_task',
                    'user_role': UserRoleEnum.OWNER,
                    'country': settings.API_COUNTRY,
                    'user_id': id_to_external_api(self.business.owner.id),
                    'business_id': id_to_external_api(self.business.id),
                    'business_name': self.business.name,
                    'business_primary_category': (self.business.primary_category.internal_name),
                    'business_categories': [],
                    'business_owner_email': self.business.owner.email,
                    'business_owner_name': self.business.owner.full_name,
                    'business_owner_phone': self.business.owner.cell_phone,
                    'business_address': self.business.get_formatted_address()['address'],
                    'business_city': self.business.city_or_region_city,
                    'business_postalCode': self.business.zip,
                    'state': None,
                    'urban_area': None,
                    'urban_subarea': None,
                    'focus_area': None,
                    'timeZone': self.business.time_zone_name,
                },
            },
        )

        resp = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={
                "location": {
                    "zipcode": "00010",
                }
            },
        )
        assert resp.code == status.HTTP_400_BAD_REQUEST
        assert (
            resp.json['errors'][0]['description'] == 'You can not change your time zone. '
            'Please contact customer support.'
        )

        assert analytics_track_mock.call_count == 1
        assert analytics_identify_mock.call_count == 1

    @override_settings(POS=False)
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_business_region_and_timezone_with_analytics_killswitch(
        self,
        analytics_track_mock,
        analytics_identify_mock,
    ):
        baker.make(
            KillSwitch,
            name=KillSwitch.MarTech.BUSINESS_INFO_UPDATED,
            is_killed=True,
        )
        # set business time zone to empty
        self.business.time_zone_name = None
        self.business.region = None
        self.business.save()
        # access_level='owner'
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        resp = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={
                "location": {
                    "zipcode": "01000",
                }
            },
        )
        assert resp.code == status.HTTP_200_OK

        self.business.refresh_from_db()
        assert self.business.time_zone_name == 'Europe/Warsaw'

        assert analytics_track_mock.call_count == 0
        assert analytics_identify_mock.call_count == 0

        resp = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={
                "location": {
                    "zipcode": "00010",
                }
            },
        )
        assert resp.code == status.HTTP_400_BAD_REQUEST
        assert (
            resp.json['errors'][0]['description'] == 'You can not change your time zone. '
            'Please contact customer support.'
        )

        assert analytics_track_mock.call_count == 0
        assert analytics_identify_mock.call_count == 0

    @override_settings(POS=False)
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_business_categories(
        self,
        analytics_track_mock,
        analytics_identify_mock,
    ):
        other_category = baker.make(
            BusinessCategory,
            internal_name=BusinessCategoryEnum.OTHER,
            type=BusinessCategory.CATEGORY,
        )

        # set business time zone to empty
        self.business.time_zone_name = None
        self.business.region = None
        self.business.categories.add(self.primary_category)
        self.business.save()
        # access_level='owner'
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        resp = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={
                'categories': [self.primary_category.id, other_category.id],
            },
        )
        assert resp.code == status.HTTP_200_OK

        self.business.refresh_from_db()

        assert resp.json['categories_changed']

        assert analytics_track_mock.call_count == 2
        dict_assert(
            analytics_track_mock.call_args_list[0][1],
            {
                'event': 'Business_Categories_Updated',
                'properties': {
                    'email': self.business.owner.email,
                    'business_id': id_to_external_api(self.business.id),
                    'business_name': self.business.name,
                    'business_primary_category': (self.primary_category.internal_name),
                    'business_categories': ['Other', 'Therapy'],
                },
            },
        )

        assert analytics_identify_mock.call_count == 2
        dict_assert(
            analytics_identify_mock.call_args_list[0][1],
            {
                'traits': {
                    'event_name': 'python.analytics_business_categories_updated_task',
                    'country': settings.API_COUNTRY,
                    'email': self.business.owner.email,
                    'user_role': UserRoleEnum.OWNER,
                    'business_id': id_to_external_api(self.business.id),
                    'business_name': self.business.name,
                    'business_primary_category': self.primary_category.internal_name,
                    'business_categories': ['Other', 'Therapy'],
                },
            },
        )

    @override_settings(POS=False)
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_business_categories_analytics_killswitch(
        self,
        analytics_track_mock,
        analytics_identify_mock,
    ):
        baker.make(
            KillSwitch,
            name=KillSwitch.MarTech.BUSINESS_INFO_UPDATED,
            is_killed=True,
        )
        baker.make(
            KillSwitch,
            name=KillSwitch.MarTech.BUSINESS_CATEGORIES_UPDATED,
            is_killed=True,
        )

        other_category = baker.make(
            BusinessCategory,
            internal_name=BusinessCategoryEnum.OTHER,
            type=BusinessCategory.CATEGORY,
        )

        # set business time zone to empty
        self.business.time_zone_name = None
        self.business.region = None
        self.business.categories.add(self.primary_category)
        self.business.save()
        # access_level='owner'
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        resp = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={
                'categories': [self.primary_category.id, other_category.id],
            },
        )
        assert resp.code == status.HTTP_200_OK

        self.business.refresh_from_db()

        assert analytics_track_mock.call_count == 0
        assert analytics_identify_mock.call_count == 0

    @override_settings(POS=False)
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_business_categories_primary_category_change(
        self,
        analytics_track_mock,
        analytics_identify_mock,
    ):
        other_category = baker.make(
            BusinessCategory,
            internal_name=BusinessCategoryEnum.OTHER,
            type=BusinessCategory.CATEGORY,
        )

        # set business time zone to empty
        self.business.time_zone_name = None
        self.business.region = None

        self.business.save()
        # access_level='owner'
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        resp = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={
                'categories': [self.primary_category.id, other_category.id],
                'primary_category': other_category.id,
            },
        )
        assert resp.code == status.HTTP_200_OK

        self.business.refresh_from_db()

        assert analytics_track_mock.call_count == 2
        dict_assert(
            analytics_track_mock.call_args_list[0][1],
            {
                'event': 'Business_Categories_Updated',
                'properties': {
                    'email': self.business.owner.email,
                    'business_id': id_to_external_api(self.business.id),
                    'business_name': self.business.name,
                    'business_primary_category': other_category.internal_name,
                    'business_categories': ['Other', 'Therapy'],
                },
            },
        )

        assert analytics_identify_mock.call_count == 2
        dict_assert(
            analytics_identify_mock.call_args_list[0][1],
            {
                'traits': {
                    'country': settings.API_COUNTRY,
                    'email': self.business.owner.email,
                    'user_role': UserRoleEnum.OWNER,
                    'business_id': id_to_external_api(self.business.id),
                    'business_name': self.business.name,
                    'business_primary_category': other_category.internal_name,
                    'business_categories': ['Other', 'Therapy'],
                },
            },
        )

    @patch.object(Business, '_validate_status_transfer', MagicMock())
    def test_business_venue_404(self):
        self.business.status = Business.Status.VENUE
        self.business.save()

        response = self.fetch(self.url.format(self.business.id))
        assert response.code == status.HTTP_404_NOT_FOUND

    @override_settings(POS=False)
    def test_business_is_visible_in_marketplace(self):
        resp = self.fetch(self.url.format(self.business.id), method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json['business']['is_visible_in_marketplace'] is False

    @override_settings(POS=False)
    @override_settings(API_COUNTRY='xx')
    def test_business_physiotherapy_enabled_false_bad_country(self):
        self.business.custom_data[CustomData.PHYSIOTHERAPY_ENABLED] = True
        self.business.save()
        resp = self.fetch(self.url.format(self.business.id), method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json['business']['physiotherapy_enabled'] is False

    @override_settings(POS=False)
    @override_settings(API_COUNTRY=Country.PL)
    def test_business_physiotherapy_enabled_true(self):
        self.business.custom_data[CustomData.PHYSIOTHERAPY_ENABLED] = True
        self.business.save()
        resp = self.fetch(self.url.format(self.business.id), method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json['business']['physiotherapy_enabled'] is True

    @override_settings(POS=False)
    def test_business_facebook_link_validation_and_formatting(self):
        self.business.time_zone_name = None
        self.business.region = None
        self.business.facebook_link = '2PacShakur'
        self.business.save()
        resp = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={
                'facebook_link': 'https://www.facebook.com/facebook.Guru24/',
                "location": {
                    "zipcode": "01000",
                },
            },
        )
        self.assertEqual(status.HTTP_200_OK, resp.code)
        self.business = Business.objects.get(id=self.business.id)
        self.assertEqual('https://www.facebook.com/facebook.Guru24/', self.business.facebook_link)

        # wrong domain
        resp = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={
                'facebook_link': 'https://www.scambook.com/zuckcuk/',
                "location": {
                    "zipcode": "01000",
                },
            },
        )
        self.assertEqual(status.HTTP_400_BAD_REQUEST, resp.code)
        self.assertDictEqual(
            resp.json['errors'][0],
            {
                'code': 'invalid',
                'description': 'Facebook link is incorrect',
                'field': 'facebook_link',
            },
        )
        self.business = Business.objects.get(id=self.business.id)
        self.assertEqual('https://www.facebook.com/facebook.Guru24/', self.business.facebook_link)

        resp = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={
                'facebook_link': '@szefito',
                "location": {
                    "zipcode": "01000",
                },
            },
        )
        self.assertEqual(status.HTTP_200_OK, resp.code)
        self.business = Business.objects.get(id=self.business.id)
        self.assertEqual('https://www.facebook.com/szefito/', self.business.facebook_link)

        resp = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={
                'facebook_link': 'dzik',
                "location": {
                    "zipcode": "01000",
                },
            },
        )
        self.assertEqual(status.HTTP_200_OK, resp.code)
        self.business = Business.objects.get(id=self.business.id)
        self.assertEqual('https://www.facebook.com/dzik/', self.business.facebook_link)

    @override_settings(POS=False)
    def test_business_instagram_link_formatting(self):
        self.business.time_zone_name = None
        self.business.region = None
        self.business.instagram_link = 'instainflu2024'
        self.business.save()
        resp = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={
                'instagram_link': 'https://www.instagram.com/instainflu2024/',
                "location": {
                    "zipcode": "01000",
                },
            },
        )
        self.assertEqual(status.HTTP_200_OK, resp.code)
        self.business = Business.objects.get(id=self.business.id)
        self.assertEqual('https://www.instagram.com/instainflu2024/', self.business.instagram_link)

        # ig handle
        resp = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={
                'instagram_link': '@realKDot',
                "location": {
                    "zipcode": "01000",
                },
            },
        )
        self.assertEqual(status.HTTP_200_OK, resp.code)
        self.business = Business.objects.get(id=self.business.id)
        self.assertEqual('https://www.instagram.com/realKDot/', self.business.instagram_link)

        # ig username
        resp = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={
                'instagram_link': 'drake_A_minor',
                "location": {
                    "zipcode": "01000",
                },
            },
        )
        self.assertEqual(status.HTTP_200_OK, resp.code)
        self.business = Business.objects.get(id=self.business.id)
        self.assertEqual('https://www.instagram.com/drake_A_minor/', self.business.instagram_link)

        # not full https instagram url
        resp = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={
                'instagram_link': 'instagram.com/not_like_us/',
                "location": {
                    "zipcode": "01000",
                },
            },
        )
        self.assertEqual(status.HTTP_200_OK, resp.code)
        self.business = Business.objects.get(id=self.business.id)
        self.assertEqual('https://www.instagram.com/not_like_us/', self.business.instagram_link)

        # malformed link
        self.business.instagram_link = 'https://www.instagram.com/base_username/'
        self.business.save()
        resp = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={
                'instagram_link': 'scamstagram.com/not_like_us/',
                "location": {
                    "zipcode": "01000",
                },
            },
        )
        self.assertEqual(status.HTTP_400_BAD_REQUEST, resp.code)
        self.business = Business.objects.get(id=self.business.id)
        self.assertEqual('https://www.instagram.com/base_username/', self.business.instagram_link)

    @override_settings(POS=False)
    def test_business_instagram_link_change(self):
        # set business time zone to empty
        self.business.time_zone_name = None
        self.business.region = None
        self.business.instagram_link = 'oldProfile'
        self.business.save()
        resp = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={  # old value is always valid
                'instagram_link': 'newProfile',
                "location": {
                    "zipcode": "01000",
                },
            },
        )
        assert resp.code == status.HTTP_200_OK
        business = Business.objects.get(id=self.business.id)
        assert business.instagram_link == 'https://www.instagram.com/newProfile/'
        resp = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={
                'instagram_link': 'AAAAAAAAAAAAAAAAAAAAAAA',
                "location": {
                    "zipcode": "01000",
                },
            },
        )
        assert resp.code == status.HTTP_200_OK
        business = Business.objects.get(id=self.business.id)
        assert business.instagram_link == 'https://www.instagram.com/AAAAAAAAAAAAAAAAAAAAAAA/'

        resp = self.fetch(  # valid url
            self.url.format(self.business.id),
            method='PUT',
            body={
                'instagram_link': 'https://www.instagram.com/ighandleronlyastext/',
                "location": {
                    "zipcode": "01000",
                },
            },
        )
        assert resp.code == status.HTTP_200_OK
        business = Business.objects.get(id=self.business.id)
        assert business.instagram_link == 'https://www.instagram.com/ighandleronlyastext/'

        resp = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={
                'instagram_link': 'poprawna.nazwa_123',
                "location": {
                    "zipcode": "01000",
                },
            },
        )
        assert resp.code == status.HTTP_200_OK
        business = Business.objects.get(id=self.business.id)
        assert business.instagram_link == 'https://www.instagram.com/poprawna.nazwa_123/'

    @override_settings(POS=False)
    def test_business_instagram_link_change_invalid_username(self):
        self.business.time_zone_name = None
        self.business.region = None
        self.business.save()
        resp = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={
                'instagram_link': '<EMAIL>',
                "location": {
                    "zipcode": "01000",
                },
            },
        )
        assert resp.code == status.HTTP_400_BAD_REQUEST
        assert resp.json['errors'] == [
            {
                'code': 'invalid',
                'description': 'Instagram link is incorrect',
                'field': 'instagram_link',
            }
        ]

        resp = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={
                'instagram_link': 'znaki^zbyt$specjalne:(',
                "location": {
                    "zipcode": "01000",
                },
            },
        )
        assert resp.code == status.HTTP_400_BAD_REQUEST
        assert resp.json['errors'] == [
            {
                'code': 'invalid',
                'description': 'Instagram link is incorrect',
                'field': 'instagram_link',
            }
        ]

        resp = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={
                'instagram_link': 'wiecej_niz_30_znakow_123456789012345678901234567890',
                "location": {
                    "zipcode": "01000",
                },
            },
        )
        assert resp.code == status.HTTP_400_BAD_REQUEST
        assert resp.json['errors'] == [
            {
                'code': 'invalid',
                'description': 'Instagram link is incorrect',
                'field': 'instagram_link',
            }
        ]

    @override_settings(POS=False)
    def test_update_appsflyer(self):
        assert AppsFlyer.objects.all().count() == 0
        self.biz_booking_src.name = WEB
        self.biz_booking_src.save()

        self.fetch(
            self.url.format(self.business.id),
            method='GET',
            extra_headers={'X-Appsflyer-User-ID': 'test'},
        )
        self.fetch(
            self.url.format(self.business.id),
            method='GET',
            extra_headers={'X-Appsflyer-User-ID': 'test2'},
        )
        assert AppsFlyer.objects.all().count() == 1
        assert AppsFlyer.objects.all().first().appsflyer_user_id == 'test2'

    @override_settings(POS=False)
    def test_update_appsflyer_andro(self):
        self.fetch(
            self.url.format(self.business.id),
            method='GET',
            extra_headers={'X-Appsflyer-User-ID': 'test'},
        )
        assert AppsFlyer.objects.all().count() == 0

    @override_settings(POS=False)
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_business_app_opened(
        self,
        analytics_track_mock,
        analytics_identify_mock,
    ):
        AppUsedByUserByDate.pop_river_values()
        self.biz_booking_src.name = ANDROID
        self.biz_booking_src.save()
        resp = self.fetch(self.url.format(self.business.id), method='GET')
        assert resp.code == 200
        save_used_app_version_task.delay()  # task spawned by celery beat
        dict_assert(
            analytics_track_mock.call_args_list[0][1],
            {
                'event': 'Business_App_Opened',
                'properties': {
                    'email': self.business.owner.email,
                    'app_version': BooksyAppVersions.B30,
                    'offer_type': Business.Package.UNKNOWN.label,
                    'device_type': DeviceTypeName.ANDROID,
                },
            },
        )
        dict_assert(
            analytics_identify_mock.call_args_list[0][1]['traits'],
            {
                'event_name': 'python.analytics_business_app_opened_task',
                'email': self.business.owner.email,
                'app_version': BooksyAppVersions.B30,
                'offer_type': Business.Package.UNKNOWN.label,
                'device_type': DeviceTypeName.ANDROID,
            },
        )

    @override_settings(POS=False)
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_business_app_opened_analytics_killswitch(
        self,
        analytics_track_mock,
        analytics_identify_mock,
    ):
        baker.make(
            KillSwitch,
            name=KillSwitch.MarTech.BUSINESS_APP_OPENED,
            is_killed=True,
        )
        self.biz_booking_src.name = ANDROID
        self.biz_booking_src.save()
        resp = self.fetch(self.url.format(self.business.id), method='GET')
        assert resp.code == 200
        save_used_app_version_task.delay()  # task spawned by celery beat
        assert analytics_track_mock.call_count == 0
        assert analytics_identify_mock.call_count == 0

    @override_settings(POS=False)
    def test_single_request(self):
        resp = self.fetch(self.url.format(self.business.id), method='GET')
        assert resp.code == 200
        river_values = AppUsedByUserByDate.pop_river_values()
        assert river_values == [
            AppUsedByUserByDate.get_river_value(
                self.user.id,
                self.biz_booking_src.id,
                self.business,
            )
        ]

    @override_settings(POS=False)
    def test_two_requests(self):
        resp = self.fetch(self.url.format(self.business.id), method='GET')
        assert resp.code == 200
        resp = self.fetch(self.url.format(self.business.id), method='GET')
        assert resp.code == 200
        river_values = AppUsedByUserByDate.pop_river_values()
        assert river_values == [
            AppUsedByUserByDate.get_river_value(
                self.user.id,
                self.biz_booking_src.id,
                self.business,
            )
        ]

    @override_settings(POS=False)
    def test_business_with_visible_delay_till(self):
        self.business.visible_delay_till = tznow()
        self.business.visible = False
        self.business.save()

        resp = self.fetch(self.url.format(self.business.id), method='GET')
        assert resp.code == 200
        assert resp.json['business']['visible_delay_till'] is not None
        assert resp.json['business']['visible'] is False

        business_visible_delay_task.run()

        resp = self.fetch(self.url.format(self.business.id), method='GET')
        assert resp.code == 200
        assert resp.json['business']['visible_delay_till'] is None
        assert resp.json['business']['visible']

    @override_settings(POS=False)
    @override_feature_flag(
        {
            ServiceTypeCampaignFlag.flag_name: True,
            ServiceTypeAlertGenerationNumberFlag.flag_name: 1,
        }
    )
    def test_service_type_marketing_data(self):
        baker.make(Service, business=self.business, is_treatment_selected_by_user=False)
        baker.make(
            UserInternalData,
            user=self.user,
            show_service_type_intro_screen=False,
        )
        baker.make(
            BusinessUserInternalData,
            user=self.user,
            business=self.business,
        )
        resp = self.fetch(self.url.format(self.business.id), method='GET')
        self.assertEqual(resp.code, status.HTTP_200_OK)
        self.assertDictEqual(
            resp.json['service_type_marketing'],
            {
                'show_alert': True,
                'show_banner': True,
                'show_intro': False,
            },
        )

    @override_settings(POS=False)
    @override_feature_flag(
        {
            ServiceTypeCampaignFlag.flag_name: True,
            ServiceTypeAlertGenerationNumberFlag.flag_name: 1,
        }
    )
    def test_service_type_marketing_data_owner_is_also_a_staffer_in_another_business(self):
        another_business = business_recipe.make()

        baker.make(Service, business=self.business, is_treatment_selected_by_user=False)
        baker.make(Service, business=another_business, is_treatment_selected_by_user=False)

        baker.make(
            Resource,
            type=Resource.STAFF,
            business=another_business,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_STAFF,
            staff_user=self.user,
        )

        baker.make(
            UserInternalData,
            user=self.user,
            show_service_type_intro_screen=True,
        )

        resp = self.fetch(self.url.format(another_business.id), method='GET')
        self.assertEqual(resp.code, status.HTTP_200_OK)
        self.assertDictEqual(
            resp.json['service_type_marketing'],
            {
                'show_alert': False,
                'show_banner': False,
                'show_intro': False,
            },
        )

    @override_settings(POS=False)
    @override_feature_flag(
        {
            ServiceTypeCampaignFlag.flag_name: True,
            ServiceTypeAlertGenerationNumberFlag.flag_name: 1,
        }
    )
    def test_service_type_marketing_data_user_owns_business_and_has_no_assigned_staffer(self):
        baker.make(Service, business=self.business, is_treatment_selected_by_user=False)

        self.user.staffers.first().delete()
        self.assertIsNone(self.user.staffers.first())

        resp = self.fetch(self.url.format(self.business.id), method='GET')
        self.assertEqual(resp.code, status.HTTP_200_OK)
        self.assertDictEqual(
            resp.json['service_type_marketing'],
            {
                'show_alert': True,
                'show_banner': True,
                'show_intro': True,
            },
        )

    def test_business_migrated_from_versum(self):
        self.business.integrations = {'importer': 'Versum Automatic'}
        self.business.save()

        resp = self.fetch(self.url.format(self.business.id), method='GET')

        assert resp.code == status.HTTP_200_OK
        assert resp.json['business']['migrated_from'] == 'Versum'

    def test_business_migrated_from_elsewhere(self):
        self.business.integrations = {'importer': 'Other'}
        self.business.save()

        resp = self.fetch(self.url.format(self.business.id), method='GET')

        assert resp.code == status.HTTP_200_OK
        assert resp.json['business']['migrated_from'] == 'Other'

    def test_business_no_migration_info(self):
        self.business.integrations = {}
        self.business.save()

        resp = self.fetch(self.url.format(self.business.id), method='GET')

        assert resp.code == status.HTTP_200_OK
        assert resp.json['business']['migrated_from'] is None

    def test_business_is_migration_uncompleted(self):
        self.business.integrations = {'importer': 'Versum'}
        self.business.save()

        resp = self.fetch(self.url.format(self.business.id), method='GET')

        assert resp.code == status.HTTP_200_OK
        assert resp.json['business']['is_migration_completed'] is False

    def test_business_is_migration_completed(self):
        self.business.integrations = {'importer': 'Versum'}
        self.business.save()
        baker.make(
            BillingTransaction,
            business=self.business,
            status=TransactionStatus.CHARGED,
            transaction_source=TransactionSource.BILLING_SUBSCRIPTION,
        )

        resp = self.fetch(self.url.format(self.business.id), method='GET')

        assert resp.code == status.HTTP_200_OK
        assert resp.json['business']['is_migration_completed']

    def test_business_is_migration_completed_no_migration_info(self):
        self.business.integrations = {}
        self.business.save()

        resp = self.fetch(self.url.format(self.business.id), method='GET')

        assert resp.code == status.HTTP_200_OK
        assert resp.json['business']['is_migration_completed'] is None

    def test_business_can_extend_trial_ok(self):
        self.business.active_from = tznow() - timedelta(days=29)
        self.business.status = Business.Status.TRIAL_BLOCKED
        barber_category = category_recipe.make(
            internal_name=BusinessCategoryEnum.BARBERS,
        )
        self.business.primary_category = barber_category
        self.business.save()
        resp = self.fetch(self.url.format(self.business.id), method='GET')

        assert status.HTTP_200_OK == resp.code
        assert resp.json['business']['can_extend_trial']

    def test_business_can_extend_trial_too_soon_after_business_activation(self):
        self.business.active_from = tznow() - timedelta(days=27)
        self.business.status = Business.Status.TRIAL_BLOCKED
        barber_category = category_recipe.make(
            internal_name=BusinessCategoryEnum.BARBERS,
        )
        self.business.primary_category = barber_category
        self.business.save()
        resp = self.fetch(self.url.format(self.business.id), method='GET')

        assert status.HTTP_200_OK == resp.code
        assert not resp.json['business']['can_extend_trial']

    def test_business_can_extend_trial_too_late_after_business_activation(self):
        self.business.active_from = tznow() - timedelta(days=91)
        self.business.status = Business.Status.TRIAL_BLOCKED
        barber_category = category_recipe.make(
            internal_name=BusinessCategoryEnum.BARBERS,
        )
        self.business.primary_category = barber_category
        self.business.save()
        resp = self.fetch(self.url.format(self.business.id), method='GET')

        assert resp.code == status.HTTP_200_OK
        assert not resp.json['business']['can_extend_trial']

    def test_business_can_extend_trial_invalid_category(self):
        self.business.status = Business.Status.TRIAL_BLOCKED
        self.business.save()
        resp = self.fetch(self.url.format(self.business.id), method='GET')

        assert resp.code == status.HTTP_200_OK
        assert not resp.json['business']['can_extend_trial']

    def test_business_can_extend_trial_already_used_trial_extension(self):
        self.business.status = Business.Status.TRIAL_BLOCKED
        self.business.save()
        baker.make(ReTrialAttempt, business=self.business)
        resp = self.fetch(self.url.format(self.business.id), method='GET')

        assert resp.code == status.HTTP_200_OK
        assert not resp.json['business']['can_extend_trial']

    def test_business_can_extend_trial_invalid_status(self):
        self.business.status = Business.Status.PAID
        self.business.save()
        baker.make(ReTrialAttempt, business=self.business)
        resp = self.fetch(self.url.format(self.business.id), method='GET')

        assert resp.code == status.HTTP_200_OK
        assert not resp.json['business']['can_extend_trial']

    @override_settings(ALLOW_TRIAL_EXTENSION=False)
    def test_business_can_extend_trial_allow_trial_extension_false(self):
        self.business.status = Business.Status.TRIAL_BLOCKED
        self.business.primary_category = category_recipe.make(
            internal_name=BusinessCategoryEnum.BARBERS,
        )
        self.business.active_from = tznow() - timedelta(days=29)
        self.business.save()
        resp = self.fetch(self.url.format(self.business.id), method='GET')

        assert resp.code == status.HTTP_200_OK
        assert not resp.json['business']['can_extend_trial']

    def test_business_get_phone_with_prefix(self):
        self.business.phone = '(*************'
        self.business.save()

        resp = self.fetch(self.url.format(self.business.id), method='GET')

        assert resp.code == status.HTTP_200_OK
        assert resp.json['business']['phone_with_prefix'] == '*************'

    def test_get_business_with_public_email(self):
        self.business.public_email = '<EMAIL>'
        self.business.save()

        resp = self.fetch(self.url.format(self.business.id), method='GET')

        assert resp.code == status.HTTP_200_OK
        assert resp.json['business']['public_email'] == '<EMAIL>'

    def test_add_public_email(self):
        assert self.business.public_email is None
        resp = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={'public_email': '<EMAIL>'},
        )
        assert resp.code == status.HTTP_200_OK
        self.business.refresh_from_db()
        assert self.business.public_email == '<EMAIL>'

    def test_update_public_email(self):
        self.business.public_email = '<EMAIL>'
        self.business.save()
        resp = self.fetch(
            self.url.format(self.business.id),
            method='PUT',
            body={'public_email': '<EMAIL>'},
        )
        assert resp.code == status.HTTP_200_OK
        self.business.refresh_from_db()
        assert self.business.public_email == '<EMAIL>'


@pytest.mark.django_db
class MyBusinessHandlerForPersonalTrainersTestCase(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{}'

    def setUp(self):
        super().setUp()
        primary_category = baker.make(
            BusinessCategory,
            internal_name=BusinessCategoryEnum.PERSONAL_TRAINERS,
        )
        self.business.primary_category = primary_category
        self.business.save()
        self.region_zip1 = baker.make(
            Region,
            name='01000',
            time_zone_name='Europe/Warsaw',
            type=Region.Type.ZIP,
        )
        self.business.region = self.region_zip1
        self.business.save(update_fields=['region'])
        self.region_zip2 = baker.make(
            Region,
            name='00010',
            time_zone_name='Europe/Amsterdam',
            type=Region.Type.ZIP,
        )

    @override_settings(POS=False)
    @override_settings(API_COUNTRY='xx')
    def test_business_physiotherapy_enabled_false_bad_country(self):
        self.business.custom_data[CustomData.PHYSIOTHERAPY_ENABLED] = True
        self.business.save()
        resp = self.fetch(self.url.format(self.business.id), method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json['business']['physiotherapy_enabled'] is False

    @override_settings(POS=False)
    @override_settings(API_COUNTRY=Country.PL)
    def test_business_physiotherapy_enabled_true(self):
        self.business.custom_data[CustomData.PHYSIOTHERAPY_ENABLED] = True
        self.business.save()
        resp = self.fetch(self.url.format(self.business.id), method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json['business']['physiotherapy_enabled'] is True


@pytest.mark.django_db
class MyBusinessHandlerForMassageTestCase(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{}'

    def setUp(self):
        super().setUp()
        primary_category = baker.make(
            BusinessCategory,
            internal_name=BusinessCategoryEnum.MASSAGE,
        )
        self.business.primary_category = primary_category
        self.business.save()
        self.region_zip1 = baker.make(
            Region,
            name='0100',
            time_zone_name='Europe/Warsaw',
            type=Region.Type.ZIP,
        )
        self.business.region = self.region_zip1
        self.business.save(update_fields=['region'])
        self.region_zip2 = baker.make(
            Region,
            name='00010',
            time_zone_name='Europe/Amsterdam',
            type=Region.Type.ZIP,
        )

    @override_settings(POS=False, API_COUNTRY=Country.US)
    def test_business_physiotherapy_enabled_false(self):
        resp = self.fetch(self.url.format(self.business.id), method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json['business']['physiotherapy_enabled'] is False

    @override_settings(POS=False)
    @override_settings(API_COUNTRY=Country.PL)
    def test_business_physiotherapy_enabled_false_no_business_switch(self):
        # no business custom_data physiotherapy_enabled flag
        resp = self.fetch(self.url.format(self.business.id), method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json['business']['physiotherapy_enabled'] is False

    @override_settings(POS=False)
    @override_settings(API_COUNTRY='xx')
    def test_business_physiotherapy_enabled_false_bad_country(self):
        self.business.custom_data[CustomData.PHYSIOTHERAPY_ENABLED] = True
        self.business.save()
        resp = self.fetch(self.url.format(self.business.id), method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json['business']['physiotherapy_enabled'] is False

    @override_settings(POS=False)
    @override_settings(API_COUNTRY=Country.PL)
    def test_business_physiotherapy_enabled_true(self):
        self.business.custom_data[CustomData.PHYSIOTHERAPY_ENABLED] = True
        self.business.save()
        resp = self.fetch(self.url.format(self.business.id), method='GET')
        assert resp.code == status.HTTP_200_OK
        assert resp.json['business']['physiotherapy_enabled'] is False


@pytest.mark.django_db
@patch.object(
    Business,
    'get_mp_deeplink',
    MagicMock(return_value='www.test-mocked.com'),
)
class MyBusinessHandlerWithWarehouse(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/'

    @override_settings(POS=False)
    @patch.object(Client, 'identify')
    @patch.object(Client, 'track')
    def test_new_business_with_warehouse(
        self,
        analytics_track_mock,
        analytics_identify_mock,
    ):
        self.user = user_recipe.make(first_name='New', last_name='Owner')
        # access_level='owner'
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        resp = self.fetch(self.url, method='POST', body={'name': 'Biznes pierwszy'})
        assert resp.code == status.HTTP_201_CREATED
        warehouse = Warehouse.objects.filter(
            business__id=resp.json['business']['id'],
        ).first()
        assert warehouse
        assert warehouse.is_default is True

        # because business is in setup
        assert analytics_track_mock.call_count == 1
        dict_assert(
            analytics_track_mock.call_args_list[0][1],
            {
                'event': 'Business_Registration_Started_Backend',
                'properties': {
                    'business_admin_status': Business.Status.SETUP.name,
                },
            },
        )


@pytest.mark.django_db
class MyBusinessHandlerCurrentStafferTestCase(BaseAsyncHTTPTest):
    url = '/business_api/me/businesses/{}'

    @override_settings(POS=False)
    def test_current_staffer_owner(self):
        # GET case
        resp = self.fetch(self.url.format(self.business.id))
        assert resp.code == status.HTTP_200_OK
        assert resp.json['business']['id'] == self.business.id
        assert resp.json['current_staffer']['id'] == self.owner.id
        assert resp.json['current_staffer']['staff_access_level'] == self.owner.staff_access_level
        assert resp.json['current_staffer']['staff_user_id'] == self.owner.staff_user_id

        # PUT case
        resp = self.fetch(
            self.url.format(self.business.id), method='PUT', body={'name': 'Update something'}
        )
        assert resp.code == status.HTTP_200_OK
        assert resp.json['business']['id'] == self.business.id
        assert resp.json['current_staffer']['id'] == self.owner.id
        assert resp.json['current_staffer']['staff_access_level'] == self.owner.staff_access_level
        assert resp.json['current_staffer']['staff_user_id'] == self.owner.staff_user_id

    @override_settings(POS=False)
    def test_current_staffer_employee(self):
        # create staffer
        self.user = baker.make(User, first_name='Random', last_name='Dude')
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')

        staffer = baker.make(
            Resource,
            visible=True,
            type=Resource.STAFF,
            business=self.business,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_MANAGER,
            staff_user=self.user,
            staff_email=self.user.email,
        )

        # GET case
        resp = self.fetch(self.url.format(self.business.id))
        assert resp.code == status.HTTP_200_OK
        assert resp.json['business']['id'] == self.business.id
        assert resp.json['current_staffer']['id'] == staffer.id
        assert resp.json['current_staffer']['staff_access_level'] == staffer.staff_access_level
        assert resp.json['current_staffer']['staff_user_id'] == staffer.staff_user_id

        # PUT case
        resp = self.fetch(
            self.url.format(self.business.id), method='PUT', body={'name': 'Update something'}
        )
        assert resp.code == status.HTTP_200_OK
        assert resp.json['business']['id'] == self.business.id
        assert resp.json['current_staffer']['id'] == staffer.id
        assert resp.json['current_staffer']['staff_access_level'] == staffer.staff_access_level
        assert resp.json['current_staffer']['staff_user_id'] == staffer.staff_user_id
