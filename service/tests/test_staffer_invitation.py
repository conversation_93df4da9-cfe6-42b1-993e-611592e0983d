import pytest
from django.utils import timezone
from model_bakery import baker

from service.staffer_invitation import StafferInviteService
from service.tests import BaseAsyncHTTPTest
from webapps.business.baker_recipes import basic_staffer_recipe, business_recipe
from webapps.business.models import Resource


@pytest.mark.django_db
class TestStafferInvite:
    @pytest.fixture(name='business')
    def business_fixture(self):
        return business_recipe.make()

    def test_accept_invitation(self, business):
        staffer = basic_staffer_recipe.make(
            business=business,
            invited=timezone.now(),
        )
        initial_updated = staffer.updated
        assert staffer.invited is not None

        StafferInviteService.accept_invitation(user_id=staffer.staff_user_id)

        staffer.refresh_from_db()
        assert staffer.invited is None
        assert staffer.updated != initial_updated

    def test_service_does_not_accept_appliance_invitation(self, business):
        appliance = baker.make(
            Resource,
            business=business,
            type=Resource.APPLIANCE,
            active=True,
            invited=timezone.now(),
        )
        assert appliance.invited is not None

        StafferInviteService.accept_invitation(user_id=appliance.staff_user_id)
        appliance.refresh_from_db()
        assert appliance.invited is not None


@pytest.mark.django_db
class StafferInviteAPITestCase(BaseAsyncHTTPTest):
    def test_accept_invitation(self):
        staffer = basic_staffer_recipe.make(
            business=self.business,
            invited=timezone.now(),
        )

        # Arrange - login as the newly invited staff user
        user = staffer.staff_user
        user.set_password('password')
        user.save()
        assert user.last_login is None

        response = self.fetch(
            '/business_api/account/login/',
            method='POST',
            body={
                'email': user.email,
                'password': 'password',
            },
        )

        assert response.code == 200
        assert response.json['access_token'] is not None

        # Assert - the staffer's invitation should be accepted
        staffer.refresh_from_db()
        assert staffer.invited is None
