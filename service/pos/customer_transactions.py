import logging
import re

from bo_obs.datadog.enums import BooksyTeams
from django.conf import settings
from django.utils.translation import gettext as _
from rest_framework import (
    serializers,
    status,
)

from lib.db import (
    READ_ONLY_DB,
    using_db_for_reads,
)
from lib.locks import CustomerTransactionActionLock
from lib.point_of_sale.enums import BasketPaymentAnalyticsTrigger
from lib.serializers import PaginatorSerializer, SendEmailRequest
from lib.tools import get_object_or_404, sget_v2
from service.booking.tools import AppointmentData, check_tokenized_payments_v2
from service.tools import (
    RequestHandler,
    json_request,
    session,
)
from webapps.adyen.typing import DeviceDataDict
from webapps.kill_switch.models import KillSwitch
from webapps.notification.models import NotificationHistory
from webapps.pos import enums
from webapps.pos.enums import (
    compatibilities,
    receipt_status,
)
from webapps.pos.models import (
    PaymentRow,
    Transaction,
)
from webapps.pos.provider import get_payment_provider
from webapps.pos.serializers import (
    CustomerTransactionActionRequest,
    PaymentTransactionPartSerializer,
    TransactionSerializer,
    get_receipt_details_serializer,
    get_transaction_serializer,
)
from webapps.pos.services import TransactionService
from webapps.pos.tasks import SendReceiptToCustomer
from webapps.pos.tools import (
    RECEIPT_STATUS_FILTERS,
    TRANSACTION_FILTERS,
    get_transaction_counts_by_id,
)

log = logging.getLogger('booksy.customer_transactions')

version_regex = re.compile(r'(\d+)\.(\d+).(\d+) \((\d+)\)')


class CustomerTransactionsHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    class GetCustomerTransactionsRequest(PaginatorSerializer):
        booking_id = serializers.IntegerField(required=False)
        transaction_type = serializers.ChoiceField(
            choices=sorted(TRANSACTION_FILTERS),
            default='all',
            required=False,
        )
        status_type = serializers.ChoiceField(
            choices=RECEIPT_STATUS_FILTERS,
            default='all',
            required=False,
        )
        stardust = serializers.BooleanField(
            write_only=True,
            required=False,
            default=False,
        )

    @session(login_required=True, api_key_required=True)
    @using_db_for_reads(READ_ONLY_DB)
    def get(self):
        """
        swagger:
            summary: Get list of POS Transactions
            notes: >
                .
            parameters:
                - name: booking_id
                  type: integer
                  paramType: query
                  required: False
                  description: optionally filter by Booking ID
                - name: transaction_type
                  description: filter charges
                  type: string
                  paramType: query
                  defaultValue: all
                  enum_from_const: webapps.pos.tools.TRANSACTION_FILTERS
                - name: status_type
                  description: filter transactions by latest_receipt status
                  type: string
                  paramType: query
                  defaultValue: all
                  enum_from_const: webapps.pos.tools.RECEIPT_STATUS_FILTERS
                - name: query
                  description: filter transactions by text matching
                  type: string
                  paramType: query
                - name: stardust
                  description: Compatibility changing behavior of
                      TransactionSerializer
                  type: boolean
                  paramType: query
                - name: page
                  description: Results page
                  paramType: query
                  type: integer
                  defaultValue: 1
                  minimum: 1
                - name: per_page
                  description: how many transactions per page to return
                  paramType: query
                  type: integer
                  minimum: 0
                  defaultValue: 20
                  maximum: 1000
            type: SearchTransactionsResponse
        """
        data = self._prepare_get_arguments()
        serializer = self.GetCustomerTransactionsRequest(data=data)
        data = self.validate_serializer(serializer)
        transactions_qs = Transaction.objects.filter(
            customer=self.user,
        ).prefetch_for_get()

        if data.get('booking_id'):
            transactions_qs = transactions_qs.filter(
                appointment__bookings__id=data['booking_id'],
            )
        if data.get('status_type', 'all') != 'all':
            transactions_qs = transactions_qs.filter(
                latest_receipt__status_code__in=receipt_status.STATUS_TYPES[data['status_type']],
            )

        # Filter child transactions
        transaction_ids = list(
            transactions_qs.filter(
                children__isnull=True,
            ).values_list('id', flat=True)
        )

        transaction_counts = get_transaction_counts_by_id(transaction_ids)
        transactions_qs = Transaction.objects.filter(
            id__in=transaction_ids,
        ).prefetch_for_get()
        filtered_transactions = transactions_qs.filter(
            TRANSACTION_FILTERS[data['transaction_type']]
        ).order_by('-latest_receipt__created')

        transactions_data = TransactionSerializer(
            instance=filtered_transactions[data['offset'] : data['limit']],
            many=True,
            context={
                # global 'pos' not available
                # __pos_per_instance will be used
                'customer_api': True,
                compatibilities.COMPATIBILITIES: {
                    compatibilities.STARDUST: data.get(compatibilities.STARDUST, False)
                },
            },
        ).data

        response = {
            'count': transaction_counts[data['transaction_type']],
            'page': data['page'],
            'per_page': data['per_page'],
            'transactions': transactions_data,
            'transaction_counts': transaction_counts,
        }
        self.finish(response)


class CustomerTransactionDetailsHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True, api_key_required=True)
    @using_db_for_reads(
        database_name=READ_ONLY_DB,
        condition=lambda: KillSwitch.alive(KillSwitch.System.REPLICA_POS_TRANSACTION_DETAILS),
    )
    def get(self, transaction_id):
        """
        swagger:
            summary: Get Transaction (Call for Payment) Details
            notes: >
                ->POS-CFP-01.
                This can return transaction tip rate choices is set_tip_rate
                is available.
            parameters:
                - name: transaction_id
                  type: integer
                  paramType: path
                  description: Transaction id
                - name: stardust
                  description: Compatibility changing behavior of
                      TransactionSerializer
                  type: boolean
                  paramType: query
            type: TransactionDetailsResponse
        """

        transaction = self.get_object_or_404(
            Transaction.objects.prefetch_for_get().for_user(user_id=self.user.id),
            id=transaction_id,
            pos__active=True,
        )

        data = self._prepare_get_arguments()

        appointment_data = (
            AppointmentData.build(transaction.appointment) if transaction.appointment else None
        )
        is_booksy_pay_available = (
            transaction.appointment.is_booksy_pay_available if transaction.appointment else False
        )
        pos = transaction.pos
        response = {
            'transaction': get_transaction_serializer(
                instance=transaction,
                context={
                    'pos': pos,
                    'customer_api': True,
                    compatibilities.COMPATIBILITIES: {
                        compatibilities.STARDUST: data.get(compatibilities.STARDUST, False)
                    },
                },
            ).data,
            'external_partners': check_tokenized_payments_v2(
                appointment_data=appointment_data,
                is_cancelation=sget_v2(transaction, ['transaction_type'])
                == (Transaction.TRANSACTION_TYPE__CANCELLATION_FEE),
                is_booksy_pay_available=is_booksy_pay_available,
            ),
            'transaction_merchant_account': (
                settings.ADYEN_MARKET_PAY_MERCHANT_ACCOUNT
                if transaction.pos.marketpay_enabled
                else settings.ADYEN_MERCHANT_ACCOUNT
            ),
        }
        self.finish(response)


class CustomerTransactionActionHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True, api_key_required=True)
    @json_request
    def post(self, transaction_id):
        """
        swagger:
            summary: Execute Transaction Action
            notes: >
                POS-CFP-01->
            parameters:
                - name: transaction_id
                  type: integer
                  paramType: path
                  description: Transaction ID
                - name: body
                  description: Customer Transaction Action Data
                  type: CustomerTransactionActionRequest
                  paramType: body
            type: TransactionDetailsResponse
        """
        lock_acquired = CustomerTransactionActionLock.try_to_lock(transaction_id)
        if not lock_acquired:
            return self.return_error(
                code=409,
                errors=[
                    {
                        'code': 'lock_error',
                        'description': _('Could not perform action'),
                    }
                ],
            )

        self.transaction = self.get_object_or_404(
            Transaction.objects.prefetch_related('pos__tips').for_user(self.user.id),
            id=transaction_id,
            pos__active=True,
        )
        request = CustomerTransactionActionRequest(
            data=self.data,
            instance=self.transaction,
            context={
                'user': self.user,
            },
        )
        self.validated_data = self.validate_serializer(request)

        if self.validated_data['action'] == enums.CUSTOMER_ACTION__SET_TIP_RATE:
            TransactionService.change_pba_tip(txn=self.transaction, data=self.validated_data)

        elif self.validated_data['action'] == enums.CUSTOMER_ACTION__MAKE_PAYMENT:
            self._action__make_payment()

        elif self.validated_data['action'] == enums.CUSTOMER_ACTION__CANCEL_PAYMENT:
            TransactionService.action__cancel_payment(
                txn=self.transaction,
                pr_id=self.validated_data['row_id'],
                log_note='CustomerTransactionActionHandler Cancel Payment',
            )
        elif self.validated_data['action'] == enums.CUSTOMER_ACTION__RETRY_PAYMENT:
            # Create new transaction for repayment (retry)
            self._renew_transaction()
            # Make payment
            self._action__make_payment()

        # reload transaction from database
        transaction = Transaction.objects.filter(id=self.transaction.id).prefetch_all().last()
        response = {
            'transaction': TransactionSerializer(
                instance=transaction,
                context={
                    'pos': transaction.pos,
                    'customer_api': True,
                },
            ).data
        }
        self.finish(response)

    def _action__make_payment(self):
        payment_method = self.validated_data.get('payment_method') or self.validated_data.get(
            'external_payment_method'
        )

        provider = get_payment_provider(
            codename=payment_method.provider,
            txn=self.transaction,
        )
        pba_row = get_object_or_404(PaymentRow, id=self.validated_data['row_id'])
        provider.make_payment(
            self.transaction,
            payment_method=payment_method,
            payment_row=pba_row,
            device_data=DeviceDataDict(
                fingerprint=self.fingerprint,
                phone_number=self.user.cell_phone,
                user_agent=self.user_agent,
            ),
            extra_data=self.payment_extra_data(),
            trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__MOBILE_PAYMENT_CONFIRM,
        )

    def _renew_transaction(self):
        txn_data = TransactionSerializer.get_data_for_renewal(self.transaction, force=True)
        txn_data['dry_run'] = False
        txn_data['parent_txn'] = self.transaction.id
        txn_data['appointment'] = self.transaction.appointment.id
        serializer = TransactionSerializer(
            data=txn_data,
            context={
                'pos': self.transaction.pos,
                'prepayment': True,
                'old_txn': self.transaction,
                'operator': self.transaction.operator,
                compatibilities.COMPATIBILITIES: {compatibilities.PREPAYMENT: True},
            },
        )
        self.validate_serializer(serializer)
        self.transaction = serializer.save()
        self.validated_data['row_id'] = self.transaction.payment_rows.last().id


class CustomerTransactionSendReceiptHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True, api_key_required=True)
    @json_request
    def post(self, transaction_id):
        """
        swagger:
            summary: Send POS Transaction Receipt
            notes: >
                .
            parameters:
                - name: transaction_id
                  type: integer
                  paramType: path
                  description: Transaction id
                - name: body
                  description: specify recipient email for this receipt
                  type: SendEmailRequest
                  paramType: body
            type: SendEmailResponse
        :swagger
        """

        transaction = self.get_object_or_404(
            Transaction.objects.for_user(user_id=self.user.id), id=transaction_id, pos__active=True
        )

        serializer = SendEmailRequest(
            data=self.data or {},
            context={
                'email': transaction.customer and transaction.customer.email,
            },
        )
        data = self.validate_serializer(serializer)

        # <editor-fold desc="early_finish section">
        SendReceiptToCustomer.delay(
            pos_id=transaction.pos.id,
            transaction_id=transaction.id,
            language=self.language,
            email=data['email'],
            sender=NotificationHistory.SENDER_CUSTOMER,
        )
        # </editor-fold>
        self.finish_with_json(200, {'email': data['email']})


class CustomerTransactionLastReceiptHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @staticmethod
    def customer_user_ids(transaction: Transaction) -> list:
        """Get customer user id from transaction and appointment bci."""
        user_ids = [transaction.customer_id]
        if transaction.appointment and transaction.appointment.booked_for:
            user_ids.append(transaction.appointment.booked_for.user_id)
        return user_ids

    @session(login_required=True, api_key_required=True)
    def get(self, transaction_id):
        """
        swagger:
            summary: Get most recent Transaction Receipt
            notes: >
                Endpoint to do polling for payment.
                Use `finalized` to determine if do next query
            parameters:
                - name: transaction_id
                  type: integer
                  paramType: path
                  description: Transaction id
            type: CustomerTransactionLastReceiptResponse
        :swagger

        swaggerModels:
            CustomerTransactionLastReceiptResponse:
                id: CustomerTransactionLastReceiptResponse
                description:
                required:
                  - receipt
                  - finalized
                properties:
                    receipt:
                        type: ReceiptDetails
                    finalized:
                        type: boolean
                        description: >
                            If `true` then `last_receipt` will not change.
                            So there is no need to ask this endpoint any more.
        :swaggerModels
        """
        transaction = self.get_object_or_404(
            Transaction.objects.select_related('appointment__booked_for'),
            id=transaction_id,
        )

        self.quick_assert_404(
            self.user.id in self.customer_user_ids(transaction),
        )

        last_receipt = transaction.latest_receipt
        response = get_receipt_details_serializer(
            instance=last_receipt,
            context={
                'pos': transaction.pos,
                'currency_symbol': settings.CURRENCY_CODE,
                'valid_currency': True,
                'customer_api': True,
            },
        )
        finalized = last_receipt.status_code in receipt_status.FINALIZED_STATUSES
        actions = PaymentTransactionPartSerializer(context={'customer_api': True}).get_actions(
            transaction
        )

        self.finish_with_json(
            status.HTTP_200_OK,
            {
                'receipt': response.data,
                'finalized': finalized,
                'actions': actions,
            },
        )
