import logging
from collections import defaultdict
from decimal import Decimal
from typing import Dict, List, Sequence, Tuple

import tornado.web
from bo_obs.datadog.enums import BooksyTeams
from django.conf import settings
from django.db.models import Q
from django.utils.translation import gettext as _
from rest_framework import status

from country_config import Country
from lib.db import using_db_for_reads, READ_ONLY_DB
from lib.elasticsearch.consts import ESDocType
from lib.serializers import PolymorphicModelListSerializer
from lib.tools import format_currency_in_locale
from service.mixins.paginator import PaginatorMixin
from service.tools import HTTPErrorWithCode, RequestHandler, json_request, session
from webapps.business.models import Business, Service, ServiceAddOn, ServiceVariantPayment
from webapps.business.serializers import (
    ServiceSimpleSerializer,
    ServiceVariantSimpleSerializer,
)
from webapps.business.serializers.addon import ServiceAddOnSerializer
from webapps.french_certification.services import SellerService
from webapps.pos.enums import (
    POSPlanPaymentTypeEnum,
    POS_PLAN_FEE_DESCRIPTION_EXCLUDING_TAX_MAP,
    POS_PLAN_FEE_DESCRIPTION_MAP,
)
from webapps.pos.enums.tax_rates import COUNTRIES_PRESENTING_NET_FEES
from webapps.pos.models import POS, ServiceVariant
from webapps.pos.searchables.business_item import (
    BusinessItemSearchable,
)
from webapps.pos.serializers import (
    BusinessItemsSearchRequestSerializer,
    BusinessPosPlanInfoRequestSerializer,
    NoShowFeeSerializer,
    NoShowFeeWizardServiceSerializer,
    POSSerializer,
    UpdateNoShowFeeSerializer,
    UpdateNoShowFeeWizardServicesSerializer,
    get_pos_serializer_class,
)
from webapps.pos.services import TransactionService
from webapps.pos.tools import (
    apply_default_tax_rate_products,
    apply_default_tax_rate_services,
    create_pos,
    display_business_pba_pos_plan,
    get_pos_plan_info,
    get_service_variants_prices,
)
from webapps.search_engine_tuning.models import BusinessCustomerTuning
from webapps.warehouse.models import Commodity
from webapps.warehouse.serializers.other import (
    CommoditySimpleSerializer,
)

log = logging.getLogger('booksy.business_pos')


class BusinessPOSHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_CALENDAR,)

    @session(login_required=True, api_key_required=True)
    @json_request
    def put(self, business_id):
        """
        swagger:
            summary: Update Business POS Settings
            notes: >
                POS-B-01, POS-B-02, POS-B-03, POS-B-04,
                POS-TX-01, POS-TX-02, POS-TX-03,
                POS-TP-01, POS-TP-02, POS-TP-03.
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: body
                  description: Business POS settings
                  type: BusinessPOSSettings
                  paramType: body
            type: BusinessPOSSettingsResponse
        """
        business = self.business_with_manager(business_id, __check_region=False)
        pos = create_pos(business)
        if not self.data.get('active', True):
            self.data.pop('active')

        data_pay_by_app_status = self.data.get('pay_by_app_status')
        pay_by_app_status_changed = False
        if data_pay_by_app_status:
            pay_by_app_status_changed = pos.pay_by_app_status != data_pay_by_app_status

        serializer_class = get_pos_serializer_class(business_id)
        serializer = serializer_class(data=self.data, instance=pos, context={'pos_view': True})
        self.validate_serializer(serializer)

        pos = serializer.save(operator_id=self.user.id)

        self.finish_with_json(
            200,
            {
                # reinitialize serializer instance to force related data fetch
                'pos': POSSerializer(instance=pos).data,
            },
        )

    @session(login_required=True, api_key_required=True)
    def get(self, business_id):
        """
        swagger:
            summary: Get Business POS Settings
            notes: >
                POS-B-01, POS-B-02, POS-B-03, POS-B-04,
                POS-TX-01, POS-TX-02, POS-TX-03,
                POS-TP-01, POS-TP-02, POS-TP-03.
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
            type: BusinessPOSSettingsResponse
        """

        business = self.business_with_staffer(
            business_id,
            __check_region=False,
        )
        pos = create_pos(business)
        serializer = POSSerializer(instance=pos, context={'pos_view': True})
        # sort tax rates No tax on first on top
        serializer.data.get('tax_rates', []).sort(
            key=lambda el: Decimal(el['rate'] if el['rate'] else u'0.0')
        )
        self.finish_with_json(200, {'pos': serializer.data})


class ApplyDefaultTaxRateHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True, api_key_required=True)
    def post(self, business_id, item_type):
        """Apply default tax rate to all products/services.

        swagger:
            summary: Apply default tax rate to all items
            notes: This updates only active services or products.
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: item_type
                  type: string
                  paramType: path
                  description: Apply to 'services' (+ AddOns) or 'products'
                  enum:
                    - services
                    - products

        """
        pos = self.get_object_or_404(
            POS.objects.select_related('business'),
            business_id=business_id,
            active=True,
        )
        self.business_with_manager(
            pos.business,
            __check_region=False,
        )
        item_type_map = {
            'services': apply_default_tax_rate_services,
            'products': apply_default_tax_rate_products,
        }
        if apply_default_func := item_type_map.get(item_type):
            apply_default_func(pos)
        else:
            raise tornado.web.HTTPError(404)

        self.set_status(200)
        self.finish({})


class BusinessPayByAppInfoPlanHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    # todo this should be deprecated, clients should use BusinessPosPlanInfoHandler instead
    @session(login_required=True, api_key_required=True)
    def get(self, business_id):
        """
        swagger:
            summary: Get info about pay_by_app functions in POS.
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
            type: POSPayByAppInfo
        :swagger
        """
        business = self.business_with_manager(business_id, __check_region=False)
        show_net = settings.API_COUNTRY in COUNTRIES_PRESENTING_NET_FEES
        if business.pos.marketpay_enabled:
            if business.pos.force_stripe_pba:
                plan = business.get_pos_plan(POSPlanPaymentTypeEnum.STRIPE_MOBILE_PAYMENT)
            else:
                plan = business.get_pos_plan(POSPlanPaymentTypeEnum.ADYEN_MOBILE_PAYMENT)

            if show_net:
                cost_text = POS_PLAN_FEE_DESCRIPTION_EXCLUDING_TAX_MAP[plan.plan_type]
            else:
                cost_text = POS_PLAN_FEE_DESCRIPTION_MAP[plan.plan_type]

            fee = (
                format_currency_in_locale(
                    plan.chargeback_txn_fee,
                    settings.CURRENCY_LOCALE,
                ),
            )
            fee_line = (
                _(
                    'In the unlikely event, one of your customers opens a dispute '
                    'with their bank, you’ll be charged a %s '
                    'non-refundable chargeback fee'
                )
                % fee
                if settings.API_COUNTRY != Country.PL
                else ' '
            )
        else:
            cost_text = _(u'Fee per mobile payment transaction')
            fee_line = None
        rates = display_business_pba_pos_plan(business, show_net=show_net)
        # cost_text = cost_text % business.country_code.upper()
        ret = {
            'line': cost_text,
            'terms_conditions': _(u'By enabling you agree to'),
            'rates': rates,
        }
        if fee_line:
            ret['fee_line'] = fee_line

        self.finish_with_json(200, ret)


class BusinessPosPlanInfoHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @session(login_required=True)
    @using_db_for_reads(READ_ONLY_DB)
    def get(self, business_id):
        """
        swagger:
            summary: Get info about given pos plan type
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business id
                - name: pos_plan_type
                  type: string
                  paramType: query
                  enum_from_const: webapps.pos.enums.POSPlanPaymentTypeEnum
                  description: POS plan type
            type: POSPlanInfo
        :swagger
        """
        business = self.business_with_staffer(business_id, __check_region=False)

        SellerService.verify_seller_data(business_id=business_id)

        data = self._prepare_get_arguments()
        serializer = BusinessPosPlanInfoRequestSerializer(data=data)
        data = self.validate_serializer(serializer)
        pos_plan_type = data['pos_plan_type']
        try:
            self.finish_with_json(
                status.HTTP_200_OK,
                get_pos_plan_info(
                    business,
                    pos_plan_type,
                    show_net=settings.API_COUNTRY in COUNTRIES_PRESENTING_NET_FEES,
                ),
            )
        except ValueError:
            raise HTTPErrorWithCode(status.HTTP_404_NOT_FOUND, reason='Pos Plan doesn\'t exist')


class NoShowProtectionFeeServiceHandler(RequestHandler):
    """
    Endpoint for setting new show protection on selected services.
    In Booksy 3.0 users are no longer allowed to set these protections on
    individual service variants - protection settings are set on whole services
    and propagated to all belonging variants instead.
    """

    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @staticmethod
    def get_valid_pos(business: Business) -> POS:
        try:
            pos = POS.objects.get(business=business)
        except POS.DoesNotExist:
            raise HTTPErrorWithCode(404, reason=_('POS object doesn\'t exists'))

        if pos.pay_by_app_status == POS.PAY_BY_APP_DISABLED:
            raise HTTPErrorWithCode(404, reason=_('Pay By App payments disabled'))
        return pos

    @session(login_required=True, api_key_required=True)
    def get(
        self,
        business_id: int,
    ) -> None:
        """
        swagger:
            summary: Get no show protection settings for the business' services
            notes: >
                Return ServicesVariants grouped by ServiceCategory
            parameters:
                - name: business_id
                  type: integer
                  required: True
                  paramType: path
                  description: Business id
            type: NoShowProtectionFeeResponse
        """
        business: Business = self.business_with_advanced_staffer(business_id)
        pos = NoShowProtectionFeeServiceHandler.get_valid_pos(business)
        self._finish_request(business, pos)

    @session(login_required=True, api_key_required=True)
    @json_request
    def post(self, business_id: int) -> None:
        """
        Sets provided no show protection for the list of services.
        In Booksy 3.0 users are no longer able to set NSP (no show protection)
        for individual service variants - protection is tied to whole services
        instead.

        swagger:
            summary: Sets provided no show protection for the list of services.
            notes: >
                In Booksy 3.0 users are no longer able to set NSP
                (no show protection) for individual service variants -
                protection is tied to whole services instead.
            parameters:
                - name: business_id
                  type: integer
                  required: True
                  paramType: path
                  description: Business id
                - name: body
                  description: No Show Protection settings
                  type: NoShowProtectionFeeServiceRequest
                  paramType: body
            type: NoShowProtectionFeeResponse
        """
        business: Business = self.business_with_manager(business_id)
        pos = NoShowProtectionFeeServiceHandler.get_valid_pos(business)

        serializer = UpdateNoShowFeeWizardServicesSerializer(
            data=self.data,
            instance=pos,
            context={
                'business': business,
                'user': self.user,
            },
        )
        self.validate_serializer(serializer)
        serializer.save(operator_id=self.user.id)

        self._finish_request(business, pos)

    def _finish_request(
        self,
        business: Business,
        pos: POS,
    ) -> None:
        serializer = NoShowFeeWizardServiceSerializer(
            instance=pos,
            context={
                'business': business,
            },
        )
        self.finish_with_json(200, serializer.data)


class NoShowProtectionFeeAllServicesHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    @staticmethod
    def prepare_services(data, payment_percentage):
        """
        Converts output of NoShowFeeSerializer to UpdateNoShowFeeSerializer
        format.

        Set payment_percentage for all valid service variants.
        :arg dict data: NoShowFeeSerializer data
        :arg int payment_percentage: 0-100 number
        """

        # Prepare body for UpdateNoShowFeeSerializer
        minimal_payment = TransactionService.get_minimal_pay_by_app_payment()
        updated_services = []

        for category in data['service_categories']:
            for service_data in category['services']:
                lowest_price = min(
                    [Decimal(variant_data['price']) for variant_data in service_data['variants']],
                    default=Decimal(0),
                )
                if payment_percentage * lowest_price / 100 > minimal_payment:
                    service_data['no_show_protection'] = {
                        'type': ServiceVariantPayment.PRE_PAYMENT_TYPE,
                        'percentage': payment_percentage,
                    }
                else:
                    # Not possible to set the percentage. Keep initial setting.
                    pass
                updated_services.append(service_data)

        return updated_services

    @session(login_required=True, api_key_required=True)
    @json_request
    def post(self, business_id):
        """
        swagger:
            summary: Update No show protection payments on all services
            notes: >
                Made for #54010 Mobile Payment Pilot.
                Business added to experiment can use it without body.
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: True
                  description: Business id
                - name: body
                  type: NoShowProtectionFeeAllServicesRequest
                  paramType: body
                  required: False
            type: NoShowProtectionFeeResponse
        :swagger
        swaggerModels:
            NoShowProtectionFeeAllServicesRequest:
                id: NoShowProtectionFeeAllServicesRequest
                properties:
                    percentage:
                        type: integer
        :swaggerModels
        """
        business = self.business_with_advanced_staffer(business_id, __check_region=False)

        payment_percentage = int(self.data['percentage'])
        pos = NoShowProtectionFeeServiceHandler.get_valid_pos(business)
        service_variant_prices = get_service_variants_prices(business)

        data = NoShowFeeSerializer(
            instance=pos,
            context={
                'business': business,
            },
        ).data

        updated_services = self.prepare_services(data, payment_percentage)

        serializer = UpdateNoShowFeeSerializer(
            data={
                'services': updated_services,
            },
            instance=pos,
            context={
                'business': business,
                # until we validate we don't know which service_variant
                # will be updated, that why need get all prices here
                # and validate payment_amount of each
                'service_prices': service_variant_prices,
            },
        )

        self.validate_serializer(serializer)
        serializer.save(operator_id=self.user.id)

        # return response
        serializer = NoShowFeeSerializer(
            instance=pos,
            context={
                'business': business,
            },
        )
        self.finish_with_json(200, serializer.data)


class BusinessItemsSearchHandler(PaginatorMixin, RequestHandler):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)

    OBJECT_TYPES = {
        'product': Commodity,
        'service': Service,
        'service-variant': ServiceVariant,
        'addon': ServiceAddOn,
    }
    DOC_TYPES = {
        Commodity: ESDocType.COMMODITY,
        Service: ESDocType.SERVICE,
        ServiceVariant: ESDocType.SERVICE_VARIANT,
        ServiceAddOn: ESDocType.ADDON,
    }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # ids of products not available for sale
        self.excluded_products_ids: List[int] = []

    def _get_queryset(self, model, business=None):
        if model is Commodity:
            return Commodity.objects.filter(~Q(id__in=self.excluded_products_ids))

        if model is Service:
            return Service.objects.filter(
                business=business,
            )

        if model is ServiceVariant:
            return ServiceVariant.objects.filter(
                service__business=business,
            ).select_related('service')

        if model is ServiceAddOn:
            return ServiceAddOn.objects.filter(
                business=business,
            )

        raise RuntimeError('Unsupported model {}'.format(model))

    def _get_customer_items(self, business, customer_id: int, object_types: Sequence[str]):
        tuning = (
            BusinessCustomerTuning.objects.filter(customer_id=customer_id)
            .only('popular_items')
            .first()
        )

        object_ids = defaultdict(set)  # by model
        object_ids_list = []
        objects = defaultdict(dict)  # by model
        object_list = []

        if tuning:
            for item in tuning.popular_items:
                object_id = item.get('object_id')
                object_type = item.get('object_type')

                if object_type not in object_types or not object_id:
                    continue

                model = self.OBJECT_TYPES[object_type]

                # skip excluded products
                if model is Commodity and object_id in self.excluded_products_ids:
                    continue

                object_ids[model].add(object_id)
                object_ids_list.append((model, object_id))

        for model, object_ids in object_ids.items():
            queryset = self._get_queryset(model, business=business)
            for object_id, obj in queryset.in_bulk(object_ids).items():
                objects[model][object_id] = obj

        # collect objects of multiple querysets in same order as in
        # tuning.popular_items
        for model, object_id in object_ids_list:
            obj = objects[model].get(object_id)
            if obj is not None:
                object_list.append(obj)

        # there is only order by popularity,
        # and no filtering by query

        return object_list[self.offset : self.limit], len(object_list)

    def _get_items(self, business, data):
        search_params = {
            'business_id': business.id,
            'query': data['query'],
            'sort_order': data.get('sort_order'),
            'archived': False,
        }

        if Commodity in data['models']:
            excluded_ids = [f'commodity:{cid}' for cid in self.excluded_products_ids]
            search_params['excluded'] = excluded_ids

        res = (
            BusinessItemSearchable(*[self.DOC_TYPES[model] for model in data['models']])
            .params(
                _source_includes=['id'],
                from_=data['per_page'] * (data['page'] - 1),
                size=data['per_page'],
                routing=business.id,
            )
            .search(search_params)
            .execute()
        )

        object_ids = defaultdict(set)  # by model
        object_ids_list = []
        objects = defaultdict(dict)  # by model
        object_list = []

        for document in res.hits:
            model = document.get_model()
            object_id = int(document.meta.id.split(':')[-1])

            object_ids[model].add(object_id)
            object_ids_list.append((model, object_id))

        for model, object_ids in object_ids.items():
            queryset = self._get_queryset(model, business=business)
            for object_id, obj in queryset.in_bulk(object_ids).items():
                objects[model][object_id] = obj

        # collect objects of multiple querysets in same order as in
        # elasticsearch response
        for model, object_id in object_ids_list:
            obj = objects[model].get(object_id)
            if obj is not None:
                object_list.append(obj)

        return object_list, res.hits.total.value

    @staticmethod
    def _get_excluded_products_ids(business: Business) -> List[int]:
        """Return ids of products not available for sale."""
        return []  # todo BP-1799
        # queryset = Commodity.objects
        # filters = {
        #     'business': business,
        #     'product_type': Commodity.TYPE_RETAIL,
        #     'archived': False
        # }
        # if business and business.pos.products_stock_enabled:
        #     queryset = queryset.annotate_is_in_stock()
        #     filters['is_in_stock'] = True
        #
        # return list(queryset.filter(~Q(**filters)).values_list('id', flat=True))

    def get_items(self, business, validated_data: Dict) -> Tuple[List, int]:
        if validated_data.get('customer_id'):
            items, total_count = self._get_customer_items(
                business,
                validated_data.get('customer_id'),
                validated_data.get('object_type'),
            )
        else:
            items, total_count = self._get_items(business, validated_data)

        # annotate instances for the PolymorphicListSerializer
        object_types_inv = {model: name for name, model in self.OBJECT_TYPES.items()}
        for obj in items:
            object_type = object_types_inv.get(type(obj))
            if object_type:
                setattr(obj, '_object_id', obj.pk)
                setattr(obj, '_object_type', object_type)

        return items, total_count

    @session(login_required=True)
    def get(self, business_id):
        """
        swagger:
            summary: List POS items
            notes: .
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  description: Business ID
                - name: customer_id
                  type: integer
                  paramType: query
                  description: BCI ID
                - name: page
                  type: integer
                  paramType: query
                  description: page to load
                  defaultValue: 1
                - name: per_page
                  type: integer
                  paramType: query
                  description: number of items per page
                  defaultValue: 20
                - name: query
                  type: string
                  paramType: query
                  description: text query to search for
                  required: false
                - name: object_type
                  type: Array[string]
                  enum: [product, service, service-variant, addon]
                  paramType: query
                  description: query item name
                  required: false
                - name: sort_order
                  type: string
                  enum: [score, name, popularity]
                  paramType: query
                  description: sorting order
                  required: false
            type: BusinessItemsSearchResponse
        """
        business = self.business_with_staffer(business_id, __check_region=False)

        request_serializer = BusinessItemsSearchRequestSerializer(
            data=self._prepare_get_arguments(list_values=['object_type']),
            context={
                'business': business,
            },
        )
        data = self.validate_serializer(request_serializer)

        self.excluded_products_ids = self._get_excluded_products_ids(business)
        self.page, self.per_page = data['page'], data['per_page']
        object_list, total_count = self.get_items(business, data)

        serializer = PolymorphicModelListSerializer(
            {
                Commodity: CommoditySimpleSerializer(read_only=True),
                Service: ServiceSimpleSerializer(read_only=True),
                ServiceVariant: ServiceVariantSimpleSerializer(read_only=True),
                ServiceAddOn: ServiceAddOnSerializer(read_only=True),
            },
            instance=object_list,
            annotate=True,
            context={
                'pos': business.pos,
                'currency_symbol': settings.CURRENCY_CODE,
                'valid_currency': True,
                'single_category': business.is_single_category,
            },
        )

        self.finish_with_json(
            200,
            {
                'items': serializer.data,
                'count': total_count,
                'page': data['page'],
                'per_page': data['per_page'],
            },
        )
