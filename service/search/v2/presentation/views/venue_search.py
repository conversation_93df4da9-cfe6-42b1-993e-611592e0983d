from bo_obs.datadog.enums import BooksyTeams
from lagom import magic_bind_to_container

from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request

from rest_framework.response import Response

from drf_api.base_views import BaseBooksySessionGenericAPIView
from drf_api.mixins import BusinessViewValidatorMixin, QuerySerializerMixin
from service.search.v2.containers import container
from service.search.v2.infrastructure.dtos import VenueSearchParams
from service.search.v2.application.venue_search_service import AbstractVenueSearchService
from service.search.v2.presentation.serializers.venue_search import (
    VenueSearchResponseSerializer,
    VenueSearchSerializer,
)
from webapps.business.models import Resource


class VenueSearchView(
    BusinessViewValidatorMixin, QuerySerializerMixin, BaseBooksySessionGenericAPIView
):
    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING, BooksyTeams.PROVIDER_CONVERSION)
    permission_classes = (IsAuthenticated,)
    required_minimum_access_level = Resource.STAFF_ACCESS_LEVEL_MANAGER
    serializer_class = VenueSearchResponseSerializer
    query_serializer_class = VenueSearchSerializer

    @magic_bind_to_container(container)
    def get(
        self,
        request: Request,
        business_pk: int,
        venue_search_service: AbstractVenueSearchService,
        *args,
        **kwargs,
    ):
        self.get_business(business_pk, check_region=False)
        request_serializer = self.get_query_serializer(data=request.query_params)
        if not request_serializer.is_valid():
            return Response(
                {"errors": request_serializer.errors}, status=status.HTTP_400_BAD_REQUEST
            )
        venue = venue_search_service.execute(
            VenueSearchParams(
                zipcode=request_serializer.validated_data.get("zipcode"),
                street=request_serializer.validated_data.get("street"),
                city=request_serializer.validated_data.get("city"),
            )
        )
        response_serializer = self.get_serializer(venue)
        return Response(status=status.HTTP_200_OK, data=response_serializer.data)
