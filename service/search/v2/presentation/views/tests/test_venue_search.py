from unittest.mock import patch

import pytest
from django.urls import reverse
from rest_framework import status

from drf_api.lib.base_drf_test_case import BaseBusinessApiTestCase
from webapps.business.baker_recipes import business_recipe, resource_recipe
from webapps.business.models import Resource
from webapps.user.baker_recipes import user_recipe


@pytest.mark.django_db
class TestVenueSearchView(BaseBusinessApiTestCase):
    def setUp(self):
        self.user = user_recipe.make()
        self.business = business_recipe.make()
        self.resource = resource_recipe.make(
            business=self.business,
            staff_user=self.user,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_MANAGER,
        )
        super().setUp()
        self.query_params = {
            "city": "Warszawa",
            "zipcode": "02-497",
            "street": "Starodęby 10",
        }
        self.mock_service_result = {
            "id": 123,
            "name": "APX GP Barbershop",
            "cover_photo": "",
            "thumbnail_photo": "",
            "address": "Starodęby 10",
            "address2": "",
            "city": "Warszawa",
            "zipcode": "02-497",
            "latitude": 52.1863054,
            "longitude": 20.8947561,
            "meta_score": 1.0,
            "meta_id": "doc-1",
            "meta_item_no": 1,
            "last_updated_contractor_id": 0,
            "last_updated_contractor_name": "",
            "last_updated_updated": "",
        }

    @patch("service.search.v2.application.venue_search_service.VenueSearchService.execute")
    def test_get_success(self, mock_execute):
        mock_execute.return_value = self.mock_service_result
        url = reverse(
            "venue_search",
            kwargs={"business_pk": self.business.id},
        )
        response = self.client.get(url, data=self.query_params)
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        mock_execute.assert_called_once()

    @patch("service.search.v2.application.venue_search_service.VenueSearchService.execute")
    def test_get_response(self, mock_execute):
        mock_execute.return_value = self.mock_service_result
        url = reverse(
            "venue_search",
            kwargs={"business_pk": self.business.id},
        )
        response = self.client.get(url, data=self.query_params)
        expected_response = self.mock_service_result
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        body = response.json()
        self.assertIsInstance(body, dict)
        self.assertEqual(expected_response, body)
        mock_execute.assert_called_once()

    def test_validation_error_when_missing_required_fields(self):
        # Omit required 'street' to trigger 400 from query serializer
        invalid_params = {"city": "Warszawa", "zipcode": "02-497"}
        url = reverse(
            "venue_search",
            kwargs={"business_pk": self.business.id},
        )
        response = self.client.get(url, data=invalid_params)
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
        body = response.json()
        self.assertIn("errors", body)
        self.assertIn("street", body["errors"])
