from rest_framework import serializers


class VenueSearchSerializer(serializers.Serializer):
    city = serializers.CharField(required=True)
    zipcode = serializers.CharField(required=True)
    street = serializers.CharField(required=True)


class VenueSearchResponseSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    name = serializers.CharField()
    cover_photo = serializers.CharField(required=False)
    thumbnail_photo = serializers.CharField(required=False)
    address = serializers.CharField()
    address2 = serializers.CharField(required=False)
    city = serializers.CharField()
    zipcode = serializers.CharField(required=False)
    latitude = serializers.FloatField()
    longitude = serializers.FloatField()
    meta_score = serializers.FloatField(required=False)
    meta_id = serializers.CharField(required=False)
    meta_item_no = serializers.IntegerField(required=False)
    last_updated_contractor_id = serializers.IntegerField(required=False)
    last_updated_contractor_name = serializers.CharField(required=False)
    last_updated_updated = serializers.CharField(required=False)
