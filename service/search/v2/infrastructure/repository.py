from typing import Iterable
from lib.elasticsearch.consts import ESDocType
from service.search.v2.infrastructure.dtos import VenueSearchParams
from service.search.v2.domain.ports import VenueRepository
from service.search.v2.infrastructure.searchable import VenueMatchAddressSearchable
from webapps.business.searchables.serializers import VenueSearchHitSerializer


class ElasticsearchVenueRepository(VenueRepository):
    @staticmethod
    def _translate_to_es_params(params: VenueSearchParams) -> dict[str, str]:
        return {
            'postalCode': params.zipcode,
            'city': params.city,
            'street': params.street,
        }

    def find_by_address(self, params: VenueSearchParams) -> Iterable:
        return (
            VenueMatchAddressSearchable(ESDocType.BUSINESS, serializer=VenueSearchHitSerializer)
            .search(self._translate_to_es_params(params))
            .execute()
        )
