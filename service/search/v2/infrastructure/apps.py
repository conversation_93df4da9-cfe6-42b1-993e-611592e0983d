from django.apps import AppConfig


class VenueSearchConfig(AppConfig):
    name = 'service.search.v2.infrastructure'
    label = 'venue_search'
    verbose_name = 'Venue Search'

    def ready(self):
        from service.search.v2.application.venue_search_service import (
            VenueSearchService,
            AbstractVenueSearchService,
        )
        from service.search.v2.infrastructure.repository import ElasticsearchVenueRepository
        from service.search.v2.domain.ports import VenueRepository
        from service.search.v2.containers import container

        # Register repository
        container[VenueRepository] = ElasticsearchVenueRepository

        # Register service with repository dependency
        container[AbstractVenueSearchService] = lambda c: VenueSearchService(
            repository=c[VenueRepository]
        )
