import elasticsearch_dsl as dsl

from lib.searchables.searchables import Filter, Searchable, V


class VenueMatchAddressSearchable(Searchable):
    class Meta:
        bool_param = 'must'

    venue = Filter(dsl.query.Term(is_renting_venue=True))
    zipcode = Filter(dsl.query.Term(venue_location__zipcode=V('postalCode', default='__skip__')))
    city = dsl.query.Match(venue_location__city=V('city'))
    address = dsl.query.Match(venue_location__address=V('street'))
