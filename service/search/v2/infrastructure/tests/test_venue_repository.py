# pylint: disable=protected-access
from unittest.mock import patch
from django.test import TestCase
from service.search.v2.infrastructure.repository import ElasticsearchVenueRepository
from service.search.v2.infrastructure.dtos import VenueSearchParams


class TestElasticsearchVenueRepository(TestCase):
    def test_translate_to_es_params(self):
        params = VenueSearchParams(street="123 Main Rd", city="Austin", zipcode="73301")
        expected = {"postalCode": "73301", "city": "Austin", "street": "123 Main Rd"}
        repo = ElasticsearchVenueRepository()
        self.assertEqual(expected, repo._translate_to_es_params(params))  # noqa: SLF001

    def test_find_by_address_invokes_searchable_with_payload(self):
        repo = ElasticsearchVenueRepository()
        params = VenueSearchParams(street="1455 Semoran Blvd", city="Casselberry", zipcode="32707")
        expected_payload = repo._translate_to_es_params(params)

        with patch(
            "service.search.v2.infrastructure.repository.VenueMatchAddressSearchable"
        ) as cls:
            instance = cls.return_value
            search = instance.search.return_value
            search.execute.return_value = ["hit-1", "hit-2"]

            result = list(repo.find_by_address(params))

            cls.assert_called_once()
            instance.search.assert_called_once()
            called_payload = instance.search.call_args.args[0]
            self.assertEqual(expected_payload, called_payload)
            self.assertListEqual(["hit-1", "hit-2"], result)

    def test_find_by_address_empty_results(self):
        repo = ElasticsearchVenueRepository()
        params = VenueSearchParams(street="Nope", city="Nowhere", zipcode="00000")

        with patch(
            "service.search.v2.infrastructure.repository.VenueMatchAddressSearchable"
        ) as cls:
            instance = cls.return_value
            instance.search.return_value.execute.return_value = []

            self.assertListEqual([], list(repo.find_by_address(params)))
