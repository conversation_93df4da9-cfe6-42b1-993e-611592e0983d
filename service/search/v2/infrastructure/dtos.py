from dataclasses import dataclass
from typing import Optional


@dataclass
class VenueSearchParams:
    street: str
    city: str
    zipcode: str


# pylint: disable=too-many-instance-attributes
@dataclass(frozen=True)
class VenueSearchResult:
    id: int
    name: str
    cover_photo: Optional[str]
    thumbnail_photo: Optional[str]
    address: str
    address2: Optional[str]
    city: str
    zipcode: Optional[str]
    latitude: float
    longitude: float
    meta_score: Optional[float]
    meta_id: Optional[str]
    meta_item_no: Optional[int]
    last_updated_contractor_id: Optional[int]
    last_updated_contractor_name: Optional[str]
    last_updated_updated: Optional[str]
