import abc
import re
from typing import Optional

from service.search.v2.infrastructure.dtos import VenueSearchParams, VenueSearchResult
from service.search.v2.domain.ports import VenueRepository

from webapps.business.searchables.serializers.typing import VenueLikeHit


class AbstractVenueSearchService(abc.ABC):
    @abc.abstractmethod
    def execute(self, params: VenueSearchParams) -> Optional[VenueSearchResult]: ...


class VenueSearchService(AbstractVenueSearchService):
    """Service for searching venues and returning a domain-level DTO (VenueSearchResult)."""

    def __init__(self, repository: VenueRepository):
        self._repo = repository

    @staticmethod
    def _tokens(s: str) -> list[str]:
        return re.findall(r'[a-z0-9]+', (s or "").lower())

    @staticmethod
    def _convert_to_result(venue: VenueLikeHit) -> VenueSearchResult:
        venue_location = venue.venue_location
        lu = getattr(venue, 'last_updated', None)
        return VenueSearchResult(
            id=venue.id,
            name=venue.name,
            cover_photo=venue.cover_photo,
            thumbnail_photo=venue.thumbnail_photo,
            latitude=venue_location.coordinate.lat,
            longitude=venue_location.coordinate.lon,
            address=venue_location.address,
            address2=venue_location.address2,
            city=venue_location.city,
            meta_score=venue.meta.score,
            meta_id=venue.meta.id,
            meta_item_no=venue.meta.item_no,
            zipcode=venue_location.zipcode,
            last_updated_contractor_id=lu.contractor_id if lu else None,
            last_updated_contractor_name=lu.contractor_name if lu else None,
            last_updated_updated=lu.updated if lu else None,
        )

    def _passes_final_filter(self, result: VenueSearchResult, params: VenueSearchParams) -> bool:

        if result and params:
            q_tokens = self._tokens(params.street)
            a_tokens = set(self._tokens(result.address))

            num_q = [t for t in q_tokens if t.isdigit()]
            word_q = [t for t in q_tokens if not t.isdigit()]

            has_num_match = any(t in a_tokens for t in num_q)
            has_word_match = any(t in a_tokens for t in word_q)

            overlap = len(set(q_tokens) & a_tokens)

            if not ((has_num_match and has_word_match) or overlap >= 2):
                return False
            return True
        return False

    def execute(self, params: VenueSearchParams) -> Optional[VenueSearchResult]:
        """
        Find venue by address components and return a VenueSearchResult or None.
        """
        hits = list(self._repo.find_by_address(params))
        if not hits:
            return None
        converted_result = self._convert_to_result(hits[0])
        if self._passes_final_filter(converted_result, params):
            return converted_result
