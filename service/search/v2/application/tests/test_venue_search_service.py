import pytest

from service.search.v2.infrastructure.repository import ElasticsearchVenueRepository
from service.search.v2.application.venue_search_service import (
    VenueSearchService,
    VenueSearchParams,
    VenueSearchResult,
)
from webapps.business.baker_recipes import business_recipe
from webapps.business.models import Business
from webapps.elasticsearch.tests.elasticsearch_test_helpers import ElasticSearchClientDjangoTestCase


@pytest.mark.django_db
class TestVenueSearchService(ElasticSearchClientDjangoTestCase):
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.service = VenueSearchService(repository=ElasticsearchVenueRepository())
        self.business = business_recipe.make(
            status=Business.Status.VENUE,
            name='Phenix Salon Suites- Casselberry',
            city='Casselberry',
            zipcode='32707',
            address='1455 S. Semoran Boulevard Unit 299 Casselberry   Florida  32707',
            latitude='28.625764',
            longitude='-81.319674',
        )
        self.business.reindex(refresh_index=True)
        self.business2 = business_recipe.make(
            status=Business.Status.VENUE,
            name='Some other Business',
            city='Krakow',
            zipcode='32-120',
            address='ul. Pawia 123',
            latitude='38.8083801',
            longitude='-11.3400245',
        )
        self.business2.reindex(refresh_index=True)
        self.business3 = business_recipe.make(
            status=Business.Status.VENUE,
            name='Some other Business',
            city='Cádiz',
            zipcode='11005',
            address='Plocia 2',
            latitude='38.8083801',
            longitude='-11.3400245',
        )
        self.business3.reindex(refresh_index=True)

    def test_exact_match(self):
        """Test exact address match returns correct venue."""
        params = VenueSearchParams(
            street="1455 Semoran Blvd unit 299", city="Casselberry", zipcode="32707"
        )
        result = self.service.execute(params)
        self.assertIsNotNone(result)
        self.assertIsInstance(result, VenueSearchResult)

        self.assertEqual(result.name, self.business.name)
        self.assertEqual(result.address, self.business.address)
        self.assertEqual(result.city, self.business.city)

        self.assertEqual(float(result.latitude), float(self.business.latitude))
        self.assertEqual(float(result.longitude), float(self.business.longitude))

    def test_partly_match(self):
        params = VenueSearchParams(street="ulica Pawia 123", city="Krakow", zipcode="32-120")
        result = self.service.execute(params)
        self.assertIsNotNone(result)
        self.assertIsInstance(result, VenueSearchResult)

        self.assertEqual(result.name, self.business2.name)
        self.assertEqual(result.address, self.business2.address)
        self.assertEqual(result.city, self.business2.city)

        self.assertEqual(float(result.latitude), float(self.business2.latitude))
        self.assertEqual(float(result.longitude), float(self.business2.longitude))

    def test_match_with_extended_street(self):
        params = VenueSearchParams(street="Calle Plocia 2", city="Cádiz", zipcode="11005")
        result = self.service.execute(params)
        self.assertIsNotNone(result)
        self.assertIsInstance(result, VenueSearchResult)

        self.assertEqual(result.name, self.business3.name)
        self.assertEqual(result.address, self.business3.address)
        self.assertEqual(result.city, self.business3.city)

        self.assertEqual(float(result.latitude), float(self.business3.latitude))
        self.assertEqual(float(result.longitude), float(self.business3.longitude))

    def test_no_match(self):
        """Test no match returns None."""
        params = VenueSearchParams(street="Wrong Street 123", city="Krakow", zipcode="32-120")
        result = self.service.execute(params)
        self.assertIsNone(result)

    def test_result_optional_fields_present(self):
        """Ensure optional fields exist on result and can be None without breaking."""
        params = VenueSearchParams(
            street="1455 Semoran Blvd unit 299", city="Casselberry", zipcode="32707"
        )
        result = self.service.execute(params)
        self.assertIsInstance(result, VenueSearchResult)
        # Optional fields should exist and be of expected types or None
        _ = result.cover_photo
        _ = result.thumbnail_photo
        _ = result.address2
        _ = result.meta_score
        _ = result.meta_id
        _ = result.meta_item_no
        _ = result.last_updated_contractor_id
        _ = result.last_updated_contractor_name
        _ = result.last_updated_updated

    def test_multiple_matches_deterministic_choice(self):
        target = business_recipe.make(
            status=Business.Status.VENUE,
            name='Target Venue',
            city='Austin',
            zipcode='73301',
            address='123 Main Road Apt 2',
            latitude='30.2672',
            longitude='-97.7431',
        )
        competitor = business_recipe.make(
            status=Business.Status.VENUE,
            name='Competitor Venue',
            city='Austin',
            zipcode='73301',
            address='123 Main Rd',
            latitude='30.2000',
            longitude='-97.7000',
        )
        target.reindex(refresh_index=True)
        competitor.reindex(refresh_index=True)
        params = VenueSearchParams(street='123 Main Rd Apt 2', city='Austin', zipcode='73301')

        result = self.service.execute(params)
        self.assertIsNotNone(result)
        self.assertEqual(result.name, target.name)
        self.assertEqual(result.address, target.address)
        self.assertEqual(result.city, target.city)
