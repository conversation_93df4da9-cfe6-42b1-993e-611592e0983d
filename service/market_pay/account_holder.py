from typing import Dict, Type

import tornado.web
from bo_obs.datadog.enums import BooksyTeams
from django.conf import settings
from django.core.paginator import EmptyPage, Paginator
from django.db import transaction
from django.utils.translation import gettext as _
from rest_framework import status

from lib.serializers import PaginatorSerializer
from lib.tools import quick_error
from service.exceptions import ServiceError
from service.pos.tools import BasePOSHandler
from service.tools import RequestHandler, json_request, session
from webapps.adyen.helpers import cents_to_float_amount
from webapps.market_pay import requests
from webapps.market_pay.models import AccountHolder
from webapps.market_pay.ports import get_balances
from webapps.market_pay.serializers import (
    AccountHolderSerializer,
    KYC2AccountHolderSerializer,
    KYC2BusinessEntitySerializer,
    KYC2IndividualEntitySerializer,
    NextPayoutSerializer,
    PayoutDetailsPaymentSerializer,
    PayoutSerializer,
)
from webapps.pos.models import POS, PaymentRow
from webapps.pos.payout import get_payout_report
from webapps.pos.serializers import BusinessCashFlowSerializer, POSChangeLogSerializer
from webapps.pos.tools import (
    annotate_payout_transactions_count,
    get_business_cash_flow_objects,
)


# pylint: disable=use-dict-literal


class AccountHolderHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)

    @session(login_required=True)
    def get(self, business_id):
        """
        swagger:
            summary: MarketPay Account Holder Details
            notes: >
                Get existing MarketPay Account Holder Details<br>
                Use it if business.pos.has_marketpay_account<br><br>
                With query mode=Individual|Business prefill data from business<br>
                Use it if not business.pos.has_marketpay_account or
                you want to change mode.<br>
            type: MarketPayAccountHolderResponse
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                - name: mode
                  description: Individual or Business
                  type: string
                  paramType: query
                  required: false
        :swagger
        """
        if not settings.POS:
            raise tornado.web.HTTPError(404)

        pos = self.get_object_or_404(
            POS.objects.select_related('business'),
            business_id=business_id,
            active=True,
        )
        business = self.business_with_manager(pos.business)
        self.validate_business_subscription(business)

        data = self._prepare_get_arguments()
        try:
            kyc_version = int(data.pop('kyc_version', 1))
        except ValueError:
            kyc_version = 1
        # return prefilled with data from business
        if 'mode' in data:
            serializer_cls = self._get_serializer_for_request(
                kyc_version,
            )
            serializer = serializer_cls(
                data=data,
                context=dict(
                    business=pos.business,
                    first_run=True,
                    operator=self.user,
                ),
                partial=True,
            )
            self.validate_serializer(serializer)
            return self.finish_with_json(
                201,
                dict(
                    account_holder=serializer.data,
                    labels=serializer.labels(),
                ),
            )

        if not pos.account_holder:
            return self.finish_with_json(
                200,
                self._get_all_kyc_labels(kyc_version),
            )

        try:
            account_holder = requests.get_account_holder(pos.account_holder.account_holder_code)
        except ServiceError as e:
            if e.code == 422 and kyc_version == 1:
                # KYC 1 should not support 422 error code - unrecognized
                # account holder should present "Connection error" message.
                quick_error(
                    ('error', 'connection', None),
                    _('Connection Error. Please try again later.'),
                )
            else:
                raise

        serializer_cls = self._get_serializer_for_request(
            kyc_version,
        )
        serializer = serializer_cls(account_holder)
        labels = (
            dict(labels=serializer.labels())
            if kyc_version != 2
            else self._get_all_kyc_labels(kyc_version)
        )
        return self.finish_with_json(
            200,
            dict(
                account_holder=serializer.data,
                **labels,
            ),
        )

    @json_request
    @session(login_required=True)
    def put(self, business_id):
        """
        swagger:
            summary: Save MarketPay Account Holder Details
            notes: >
                Save MarketPay Account<br><br>
            type: MarketPayAccountHolderResponse
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                  required: true
                - name: body
                  description: Account Holder request
                  type: MarketPayAccountHolder
                  paramType: body
                  required: true
        :swagger
        """
        if not settings.POS:
            raise tornado.web.HTTPError(404)

        pos = self.get_object_or_404(
            POS.objects.select_related('business'),
            business_id=business_id,
            active=True,
        )

        business = self.business_with_owner(pos.business)
        self.validate_business_subscription(business)

        try:
            kyc_version = int(self.data.pop('kyc_version', 1))
        except ValueError:
            kyc_version = 1
        serializer_cls = self._get_serializer_for_request(kyc_version)
        serializer = serializer_cls(
            data=self.data,
            context=dict(
                business=pos.business,
                first_run=False,
                operator=self.user,
                update_signatories=True,
            ),
        )
        self.validate_serializer(serializer, flatten=False)
        serializer.save()

        self.finish_with_json(
            200,
            dict(
                account_holder=serializer.data,
                labels=serializer.labels(),
            ),
        )

    @staticmethod
    def _get_serializer_for_request(
        kyc_version: int,
    ) -> Type[AccountHolderSerializer]:
        """
        Returns serializer that should be used to validate request data.

        This endpoint uses different serializers because of the changes to the
        KYC process on the Booksy side, changing required and/or pre-filled
        fields.

        :raises KeyError: if the provided `kyc_version` value does not have
                          any associated serializer class
        """
        kyc_version_to_serializer_class = {
            1: AccountHolderSerializer,
            2: KYC2AccountHolderSerializer,
        }
        return kyc_version_to_serializer_class[kyc_version]

    @staticmethod
    def _get_all_kyc_labels(
        kyc_version: int,
    ) -> Dict[str, str]:
        """
        Returns dict object containing list of labels for the KYC process.
        In case of unrecognized `kyc_version` value, the empty dict is returned.
        """
        labels = {}
        if kyc_version == 2:
            labels = KYC2IndividualEntitySerializer().labels()
            labels['labels'] = {
                'general': AccountHolderSerializer.get_general_kyc_labels(),
                'individual': labels.pop('individual'),
                'business': KYC2BusinessEntitySerializer().labels()['business'],
            }
        return labels


class PBAHandler(RequestHandler):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)

    @session(login_required=True)
    def post(self, business_id):
        """
        swagger:
            summary: Turns on PBA
            type: TurnOnPBAResponse
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
        :swagger
        swaggerModels:
            TurnOnPBAResponse:
                description: 200 if everything is ok. 400/404 if not
                required:
                    - ok
                properties:
                    ok: boolean
        :swaggerModels
        """
        if not settings.POS:
            raise tornado.web.HTTPError(404)

        pos = self.get_object_or_404(
            POS.objects.select_related('business'),
            business_id=business_id,
            active=True,
        )
        self.business_with_owner(pos.business)
        if not (pos.force_stripe_pba and pos.stripe_kyc_completed):
            return self.finish_with_json(400, {})

        with transaction.atomic():
            pos.enable_pay_by_app_default()
            pos.refresh_from_db()
            serializer = POSChangeLogSerializer(instance=pos)
            pos.log_changes(self.user.id, serializer.data)

        self.finish_with_json(200, dict(ok=True))


class NextPayoutHandler(BasePOSHandler):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)

    @session(login_required=True)
    def get(self, business_id):
        """
        swagger:
            summary: Get predicted info about next payout
            type: NextPayoutResponse
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
        :swagger
        swaggerModels:
            NextPayoutResponse:
                id: NextPayoutResponse
                description: 200 if everything is ok. 400/404 if not
                properties:
                    next_payout:
                        type: string
                        description: available balance
                    pending_balance:
                        type: string
                        description: pending balance
                    currency:
                        type: string
                        description: values currency
        :swaggerModels
        """
        self._get_business_with_pos(business_id)
        balances = get_balances(business_id)
        if not balances:
            return self.finish_with_json(status.HTTP_400_BAD_REQUEST, {})

        currency_code = settings.CURRENCY_CODE
        serializer = NextPayoutSerializer(
            dict(
                next_payout=cents_to_float_amount(
                    dict(value=balances['balance'] or 0, currency=currency_code),
                )['value'],
                pending_balance=cents_to_float_amount(
                    dict(
                        value=balances['pendingBalance'] or 0,
                        currency=currency_code,
                    ),
                )['value'],
            ),
        )
        self.finish_with_json(status.HTTP_200_OK, serializer.data)


class BusinessPayoutsHandler(BasePOSHandler):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)

    @session(login_required=True)
    def get(self, business_id):
        """
        swagger:
            summary: Get list of Payouts
            type: array
            items:
                type: BusinessPayoutsResponse
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                - name: page
                  description: Results page
                  paramType: query
                  type: integer
                  defaultValue: 1
                  minimum: 1
                - name: per_page
                  description: how many transactions per page to return
                  paramType: query
                  type: integer
                  minimum: 0
                  defaultValue: 20
                  maximum: 1000
        :swagger
        swaggerModels:
            BusinessPayoutsResponse:
                id: BusinessPayoutsResponse
                description: 200 if everything is ok. 400/404 if not
                properties:
                    page:
                        type: integer
                        description: current results page number
                    per_page:
                        type: integer
                        description: number of items per page
                    count:
                        type: integer
                        description: count of all objects across all pages
                    payouts:
                        type: array
                        items:
                            type: PayoutResponse
            PayoutResponse:
                id: PayoutResponse
                properties:
                    id:
                        type: int
                        description: payout id
                    created:
                        type: string
                        description : payout datetime
                    amount:
                        type: number
                        description: payout amount
                    transactions_count:
                        type: integer
                        description: number of payout transactions
        :swaggerModels
        """
        business, pos = self._get_business_with_pos(business_id)
        account_holder = AccountHolder.objects.filter(pos__business_id=business_id).first()
        if not account_holder:
            return self.finish_with_json(status.HTTP_400_BAD_REQUEST, {})

        data = self._prepare_get_arguments()
        serializer = PaginatorSerializer(data=data)
        data = self.validate_serializer(serializer)
        page = data['page']
        per_page = data['per_page']
        qs = account_holder.payouts.all()
        qs = annotate_payout_transactions_count(qs, pos)
        pager = Paginator(
            qs.order_by('-created'),
            per_page,
        )
        try:
            object_list = pager.page(page).object_list
        except EmptyPage:
            object_list = []

        serializer = PayoutSerializer(
            instance=object_list,
            many=True,
        )
        self.finish_with_json(
            status.HTTP_200_OK,
            {
                'page': page,
                'per_page': per_page,
                'count': pager.count,
                'payouts': serializer.data,
            },
        )


class BusinessPayoutDetailsHandler(BasePOSHandler):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)

    @session(login_required=True)
    def get(self, business_id, payout_id):
        """
        swagger:
            summary: Get payout payments details
            type: array
            items:
                type: BusinessPayoutDetailsResponse
            parameters:
                - name: business_id
                  type: integer
                  paramType: path
                - name: payout_id
                  type: integer
                  paramType: path
        :swagger
        swaggerModels:
            BusinessPayoutDetailsResponse:
                id: BusinessPayoutDetailsResponse
                description: 200 if everything is ok. 404 if not
                properties:
                    payments:
                        type: array
                        items:
                            type: PayoutDetailsPayment
                    cash_flows:
                        type: array
                        items:
                            type: CashFlow
                    transactions_count:
                        type: integer
                        description: number of payout transactions
            PayoutDetailsPayment:
                id: PayoutDetailsPayment
                properties:
                    payment_type:
                        type: string
                    payment_type_code:
                        type: string
                    total:
                        type: string
        :swaggerModels
        """
        business, pos = self._get_business_with_pos(business_id)

        payout = self.get_object_or_404(
            ('market_pay', 'Payout'),
            id=payout_id,
        )

        payout_report = get_payout_report(business, payout.psp_reference)
        payments_serializer = PayoutDetailsPaymentSerializer(
            payout_report.payout_summary_data_rows,
            context={
                'valid_currency': True,
            },
            many=True,
        )

        cash_flow_qs, count = get_business_cash_flow_objects(
            pos,
            payout_reference=payout.psp_reference,
        )
        objects = []
        for obj in cash_flow_qs:
            if isinstance(obj, PaymentRow):
                objects.append(
                    {
                        'payment_row': obj,
                        'fund_transfer': None,
                    }
                )
            else:
                objects.append(
                    {
                        'payment_row': None,
                        'fund_transfer': obj,
                    }
                )
        cash_flow_serializer = BusinessCashFlowSerializer(
            objects,
            context={
                'business': business,
            },
            many=True,
        )
        self.finish_with_json(
            status.HTTP_200_OK,
            {
                'transactions_count': count,
                'payments': payments_serializer.data,
                'cash_flows': cash_flow_serializer.data,
            },
        )
