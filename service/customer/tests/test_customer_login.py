import json
from datetime import datetime
from datetime import timezone
from functools import partial

import mock
import pytest
from mock import patch
from rest_framework import status
from segment.analytics import Client

from lib.email_internal import generate_private_email
from lib.feature_flag.feature.booksy_med import CustomerLoginRestrictionForImport
from lib.test_utils import standard_login_logger_kwargs_check
from lib.tests.utils import override_eppo_feature_flag
from service.tests import BaseAsyncHTTPTest
from webapps.booksy_med.utils import generate_booksy_med_import_email
from webapps.session.booksy_auth import BooksyAuthValidationError
from webapps.session.booksy_auth import BooksyAuthServiceException
from webapps.session.booksy_auth.pb2.auth_pb2 import (  # pylint: disable=no-name-in-module
    LoginUserResponse,
)
from webapps.user.models import User, UserProfile


def _patched_login_response(
    fnc_name,
    message_type,
    data,
    password_change=False,
    timeout=8,
):  # pylint: disable=unused-argument
    data = LoginUserResponse(
        country_user_id=User.objects.first().id,
        session_key='some_secret_key',
        password_change_required=password_change,
        account_exists=True,
        expired=datetime(2021, 12, 1, 11, 12, tzinfo=timezone.utc).isoformat(),
    )
    return data


@pytest.mark.django_db
@pytest.mark.usefixtures('switch_on_new_login_fixture')
# ENSURE that session is not expired in _patched_login_response
@pytest.mark.freeze_time('2021-11-01')
class CustomerLoginHandlerTestCase(BaseAsyncHTTPTest):
    url = '/customer_api/account/login/'

    def setUp(self):
        super().setUp()
        # guess cipher:)
        self.password = 'лфсягышф'
        self.user.set_password(self.password)
        self.user.save()

    def get_body(self):
        return {
            'username': self.user.username,
            'email': self.user.email,
            'password': self.password,
        }

    @patch('service.account.logger')
    @mock.patch(
        'webapps.session.booksy_auth.grpc_client.BooksyAuthClient._make_request',
        side_effect=_patched_login_response,
    )
    @patch.object(Client, 'track')
    @mock.patch('service.account.business_user_logged_in')
    def test_login_success(
        self,
        patched_logged_in,
        analytics_track,
        patched_login,
        mock_logger,
    ):
        assert not UserProfile.objects.filter(
            profile_type=UserProfile.Type.CUSTOMER,
            user=self.user,
        ).exists()
        resp = self.fetch(
            self.url,
            body=self.get_body(),
            method='POST',
        )
        self.assertEqual(resp.code, status.HTTP_200_OK)
        self.assertFalse(resp.json['superuser'])
        # see patch
        self.assertEqual(resp.json['access_token'], 'some_secret_key')
        patched_login.assert_called_once()

        self.assertEqual(analytics_track.call_count, 1)
        event_args = analytics_track.call_args[1]
        self.assertEqual(event_args['event'], 'Customer_Registration_Completed')
        properties = event_args['properties']

        self.assertTrue(
            set(properties.keys()).issuperset(
                {
                    'first_name',
                    'last_name',
                    'phone',
                    'created_at',
                    'web_communication_agreement',
                    'app_language',
                    'gender_code',
                    'user_role',
                    'control_group',
                    'country',
                    'user_id',
                    'email',
                }
            )
        )
        patched_logged_in.send.assert_not_called()
        standard_login_logger_kwargs_check(
            mock_logger=mock_logger,
            call_number=0,
            message='[LOGIN - ATTEMPT] ',
            recaptcha_valid=None,
            recaptcha_score=None,
        )
        standard_login_logger_kwargs_check(
            mock_logger=mock_logger,
            call_number=1,
            message=f'[LOGIN - LOGIN SUCCESS] User {self.user.id} logged in',
            recaptcha_valid=None,
            recaptcha_score=None,
        )

    @patch('service.account.logger')
    @mock.patch(
        'webapps.session.booksy_auth.grpc_client.BooksyAuthClient._make_request',
        side_effect=partial(_patched_login_response, password_change=True),
    )
    def test_login_password_change_required(self, patched_login, mock_logger):
        resp = self.fetch(self.url, body=self.get_body(), method='POST')
        self.assertEqual(resp.code, status.HTTP_400_BAD_REQUEST)
        errors = json.loads(resp.body)['errors'][0]
        self.assertEqual(errors['field'], 'password')
        patched_login.assert_called_once()
        standard_login_logger_kwargs_check(
            mock_logger=mock_logger,
            call_number=0,
            message='[LOGIN - ATTEMPT] ',
            recaptcha_valid=None,
            recaptcha_score=None,
        )
        standard_login_logger_kwargs_check(
            mock_logger=mock_logger,
            call_number=1,
            message='[LOGIN - PASSWORD CHANGE ATTEMPT] ',
            recaptcha_valid=None,
            recaptcha_score=None,
        )

    @patch('service.account.logger')
    @mock.patch(
        'webapps.session.booksy_auth.grpc_client.BooksyAuthClient._make_request',
        side_effect=BooksyAuthValidationError(
            errors=dict(invalid_email_or_password='Invalid email or password')
        ),
    )
    def test_login_non_existing_user(self, patched_login, mock_logger):
        resp = self.fetch(self.url, body=self.get_body(), method='POST')
        self.assertEqual(resp.code, status.HTTP_400_BAD_REQUEST)
        patched_login.assert_called_once()
        standard_login_logger_kwargs_check(
            mock_logger=mock_logger,
            call_number=0,
            message='[LOGIN - ATTEMPT] ',
            recaptcha_valid=None,
            recaptcha_score=None,
        )
        standard_login_logger_kwargs_check(
            mock_logger=mock_logger,
            call_number=1,
            message='[LOGIN - BAD LOGIN DATA] ',
            recaptcha_valid=None,
            recaptcha_score=None,
        )

    @pytest.mark.xfail  # TODO: remove after manual tests
    @mock.patch(
        'webapps.session.booksy_auth.grpc_client.BooksyAuthClient._make_request',
        side_effect=BooksyAuthServiceException('foo', 'bar'),
    )
    @mock.patch('webapps.session.booksy_auth.grpc_client.booksy_auth_logger.error')
    def test_login_raises_error(self, patched_error_logger, patched_login):
        # booksy_auth service is unavailable
        resp = self.fetch(self.url, body=self.get_body(), method='POST')
        self.assertEqual(resp.code, status.HTTP_200_OK)
        self.assertIsNone(resp.json['superuser'])
        patched_login.assert_called_once()
        # ensure user email was added to log
        # to sync latter user
        patched_error_logger.assert_called_once_with(
            '[LOGIN USER] USER_EMAIl: %s',
            self.user.email,
            exc_info=True,
        )

    def test_login_private_email(self):
        self.user.email = generate_private_email('500600700')
        self.user.save()

        resp = self.fetch(self.url, body=self.get_body(), method='POST')

        self.assertEqual(resp.code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(
            resp.json,
            {
                'errors': [
                    {
                        'code': 'invalid',
                        'type': 'validation',
                        'field': 'email',
                        'description': 'Provided email is not a valid email address.',
                    },
                ]
            },
        )

    @override_eppo_feature_flag({CustomerLoginRestrictionForImport.flag_name: True})
    def test_not_allow_booksy_med_import(self):
        self.user.email = generate_booksy_med_import_email()
        self.user.save()

        resp = self.fetch(self.url, body=self.get_body(), method='POST')

        self.assertEqual(status.HTTP_400_BAD_REQUEST, resp.code)
        self.assertEqual(
            resp.json,
            {
                'errors': [
                    {
                        'code': 'invalid',
                        'type': 'validation',
                        'field': 'email',
                        'description': 'Login is restricted to this email address.',
                    },
                ]
            },
        )

    @override_eppo_feature_flag({CustomerLoginRestrictionForImport.flag_name: False})
    def test_allow_booksy_med_import(self):
        self.user.email = generate_booksy_med_import_email()
        self.user.save()

        resp = self.fetch(self.url, body=self.get_body(), method='POST')

        self.assertEqual(status.HTTP_200_OK, resp.code)
