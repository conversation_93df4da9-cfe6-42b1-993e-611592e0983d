from unittest.mock import ANY, MagicMock

import pytest
from django.test import override_settings
from mock import patch
from rest_framework import status
from tornado.httpclient import HTTPResponse

from lib.feature_flag.feature.security import (
    HCaptchaFlag,
    HCaptchaForceFlag,
)
from lib.tests.utils import override_feature_flag
from service.tests import BaseAsyncHTTPTest
from webapps.notification.enums import NotificationCategory
from webapps.notification.models import NotificationHistory, UserNotification


@pytest.mark.django_db
class TestCustomerSmsInvite(BaseAsyncHTTPTest):
    url = '/customer_api/sms_invite/?'
    _PHONE_NUMBER = '+48555555555'

    def post(self, data: dict, **kwargs) -> HTTPResponse:
        return self.fetch(self.url, body=data, method='POST', **kwargs)

    def test_post_formats_cell_phone(self):
        response = self.post({'cell_phone': self._PHONE_NUMBER, 'language': 'pl'})

        assert response.code == status.HTTP_200_OK
        assert response.json == {'formatted_cell_phone': '+48 55 555 55 55'}

    @patch('service.customer.customers.commence_send_sms')
    def test_commence_send_sms_called_with_correct_data(self, mock_commence_send_sms: MagicMock):
        self.post({'cell_phone': self._PHONE_NUMBER, 'language': 'pl'})

        mock_commence_send_sms.assert_called_once_with(
            ANY,
            [self._PHONE_NUMBER],
            history_data={
                'sender': NotificationHistory.SENDER_SYSTEM,
                'type': UserNotification.SMS_NOTIFICATION,
                'task_id': f'{NotificationCategory.CUSTOMER_INVITATION},{self._PHONE_NUMBER}',
                'task_type': NotificationHistory.TASK_TYPE__SMS_CUSTOMER_INVITATION,
            },
        )

    def test_post_raises_when_phone_number_blacklisted(self):
        response = self.post({'cell_phone': '+1555555555', 'language': 'pl'})

        assert response.code == status.HTTP_400_BAD_REQUEST
        assert response.json == {
            'errors': [
                {
                    'code': 'not_valid',
                    'description': (
                        'Your phone number country is not supported. '
                        'Please use a local phone number.'
                    ),
                    'field': 'cell_phone',
                    'type': 'validation',
                },
            ],
        }

    def test_post_raises_when_phone_number_invalid(self):
        response = self.post({'cell_phone': '+4855555', 'language': 'pl'})

        assert response.code == status.HTTP_400_BAD_REQUEST
        assert response.json == {
            'errors': [
                {
                    'code': 'not_valid',
                    'description': 'Invalid phone number',
                    'field': 'cell_phone',
                    'type': 'validation',
                },
            ],
        }

    @override_settings(SMS_REGISTRATION_ABUSE_LIMIT=10)
    @patch('service.customer.customers.NotificationSMSCodes.get_usage_count')
    def test_post_raises_on_to_many_requests(self, mock_get_usage_count: MagicMock):
        mock_get_usage_count.return_value = 969
        response = self.post({'cell_phone': self._PHONE_NUMBER, 'language': 'pl'})

        assert response.code == status.HTTP_400_BAD_REQUEST
        assert response.json == {
            'errors': [
                {
                    'code': 'abuse',
                    'description': 'Too many requests for invite SMS',
                    'field': '__all__',
                    'type': 'validation',
                },
            ],
        }

    @override_settings(SMS_REGISTRATION_WAIT=10)
    @patch('service.customer.customers.NotificationSMSCodes.get_recent')
    @patch('service.customer.customers.min')
    def test_post_raises_on_sms_registration_wait(
        self, mock_min: MagicMock, mock_get_recent: MagicMock
    ):
        mock_min.return_value = 1
        mock_get_recent.return_value = [MagicMock()]
        response = self.post({'cell_phone': self._PHONE_NUMBER, 'language': 'pl'})

        assert response.code == status.HTTP_400_BAD_REQUEST
        assert response.json == {
            'errors': [
                {
                    'code': 'entity_exists',
                    'description': 'Please wait. Text is on its way.',
                    'field': 'sms_code',
                    'type': 'database',
                },
            ],
        }

    @override_settings(SMS_REGISTRATION_LIMIT=1)
    @patch('service.customer.customers.NotificationSMSCodes.get_recent')
    @patch('service.customer.customers.min')
    def test_post_raises_on_sms_registration_limit(
        self, mock_min: MagicMock, mock_get_recent: MagicMock
    ):
        mock_min.return_value = 9999
        mock_get_recent.return_value = [MagicMock()]
        response = self.post({'cell_phone': self._PHONE_NUMBER, 'language': 'pl'})

        assert response.code == status.HTTP_400_BAD_REQUEST
        assert response.json == {
            'errors': [
                {
                    'code': 'entity_exists',
                    'description': 'Too many attempts for this phone number',
                    'field': 'cell_phone',
                    'type': 'database',
                },
            ],
        }

    @override_feature_flag({HCaptchaFlag.flag_name: True})
    @patch('service.customer.customers.HCaptchaRequestValidator.validate')
    def test_hcaptcha_header(self, mock_hcaptcha_validate: MagicMock):
        self.post(
            data={'cell_phone': self._PHONE_NUMBER, 'language': 'pl'},
            extra_headers={'x-hcaptcha-token': 'test_token'},
        )

        mock_hcaptcha_validate.assert_called_once()

    @override_feature_flag({HCaptchaFlag.flag_name: True})
    @override_feature_flag({HCaptchaForceFlag.flag_name: True})
    @patch('service.customer.customers.HCaptchaRequestValidator.validate')
    def test_hcaptcha_forced(self, mock_hcaptcha_validate: MagicMock):
        self.post(
            data={'cell_phone': self._PHONE_NUMBER, 'language': 'pl'},
        )

        mock_hcaptcha_validate.assert_called_once()

    @override_feature_flag({HCaptchaFlag.flag_name: True})
    @override_feature_flag({HCaptchaForceFlag.flag_name: True})
    @patch('service.customer.customers.HCaptchaRequestValidator.validate')
    def test_hcaptcha_forced_with_recaptcha_enabled(self, mock_hcaptcha_validate: MagicMock):
        self.post(
            data={'cell_phone': self._PHONE_NUMBER, 'language': 'pl'},
        )

        mock_hcaptcha_validate.assert_called_once()
