import unittest
from unittest.mock import MagicMock, patch

from django.conf import settings

from lib.datadog.tools import is_datadog_synthetics_test
from lib.feature_flag.feature.security import (
    DisableCaptchaForAccountExistEndpointFlag,
    HCaptchaFlag,
)
from lib.tests.utils import override_feature_flag
from lib.tools import ServiceError
from service.customer.hcaptcha import (
    HCaptchaRequestValidator,
    consts,
    get_hcaptcha_protected_endpoints,
)


class TestHCaptchaRequestValidator(unittest.TestCase):
    def setUp(self):
        self.request = MagicMock()
        self.request.path = '/some_path'
        self.request.method = 'POST'
        self.request.headers = {
            'X-Real-IP': '************',
            'x-hcaptcha-token': 'test_token',
            'Sec-Datadog': 'test',
        }

    def test_request_ip(self):
        self.request.remote_ip = '***********'
        self.request.headers['X-Real-IP'] = '***********'

        validator = HCaptchaRequestValidator(consts.WEB, self.request)
        self.assertEqual(validator.request_ip(), '***********')

        del self.request.headers['X-Real-IP']
        validator = HCaptchaRequestValidator(consts.WEB, self.request)
        self.assertEqual(validator.request_ip(), None)

    def test_endpoint_chosen_for_validation(self):
        self.request.path = '/some_unprotected_path'
        validator = HCaptchaRequestValidator(consts.WEB, self.request)
        self.assertFalse(validator.endpoint_chosen_for_validation())

        self.request.path = get_hcaptcha_protected_endpoints()[0]['path']
        validator = HCaptchaRequestValidator(consts.WEB, self.request)
        self.assertTrue(validator.endpoint_chosen_for_validation())

    def test_get_hcaptcha_protected_endpoints(self):
        endpoints = get_hcaptcha_protected_endpoints()
        self.assertIn('customer_account_exists', [row['key'] for row in endpoints])
        self.assertIn('customer_sms_invite', [row['key'] for row in endpoints])

        with override_feature_flag({DisableCaptchaForAccountExistEndpointFlag.flag_name: True}):
            endpoints = get_hcaptcha_protected_endpoints()
            self.assertNotIn('customer_account_exists', [row['key'] for row in endpoints])

    def test_is_datadog_synthetics_test(self):
        with patch('lib.datadog.tools.get_datadog_synthetics_ip_ranges') as mocked_ip_ranges:
            mocked_ip_ranges.return_value = []
            self.assertFalse(is_datadog_synthetics_test(self.request.headers))

            mocked_ip_ranges.return_value = ['************/32']
            self.assertTrue(is_datadog_synthetics_test(self.request.headers))

    def test_is_enabled_for_mobile(self):
        self.request.headers = {'x-hcaptcha-token': 'test_token'}

        validator = HCaptchaRequestValidator(consts.ANDROID, self.request)
        with patch('service.customer.hcaptcha.HCaptchaAndroidFlag') as mocked_android_flag:
            mocked_android_flag.return_value = True
            self.assertTrue(validator.is_enabled_for_mobile())

            mocked_android_flag.return_value = False
            self.assertFalse(validator.is_enabled_for_mobile())

        validator = HCaptchaRequestValidator(consts.IPHONE, self.request)
        with patch('service.customer.hcaptcha.HCaptchaiOSFlag') as mocked_ios_flag:
            mocked_ios_flag.return_value = True
            self.assertTrue(validator.is_enabled_for_mobile())

            mocked_ios_flag.return_value = False
            self.assertFalse(validator.is_enabled_for_mobile())

    def test_is_web_booking_source(self):
        self.request.headers = {'x-hcaptcha-token': 'test_token'}

        for booking_source in (
            consts.WEB,
            consts.WIDGET,
            consts.INSTAGRAM,
            consts.FACEBOOK,
            consts.INSTAGRAM_STAFFER,
        ):
            validator = HCaptchaRequestValidator(booking_source, self.request)
            self.assertTrue(validator.is_web_booking_source())

        validator = HCaptchaRequestValidator("not_web", self.request)
        self.assertFalse(validator.is_web_booking_source())

    def test_test_env_bypass(self):
        headers = {
            'User-Agent': 'test user agent',
            'x-hcaptcha-token': '20000000-aaaa-bbbb-cccc-000000000002',
        }
        self.request.headers = headers

        with patch('service.customer.hcaptcha.settings') as mocked_settings:
            mocked_settings.LIVE_DEPLOYMENT = False
            validator = HCaptchaRequestValidator(consts.WEB, self.request)
            self.assertTrue(validator.test_env_bypass())

            mocked_settings.LIVE_DEPLOYMENT = True
            validator = HCaptchaRequestValidator(consts.WEB, self.request)
            self.assertFalse(validator.test_env_bypass())

    def test_validate(self):
        headers = {'User-Agent': 'test user agent', 'x-hcaptcha-token': 'test_token'}
        self.request.headers = headers
        self.request.remote_ip = '***********'

        with (
            patch('service.customer.hcaptcha.HCaptchaFlag') as mocked_flag,
            patch('requests.post') as mocked_post,
            patch('service.customer.hcaptcha.logger.warning') as mocked_logger,
        ):
            mocked_flag.return_value = False
            validator = HCaptchaRequestValidator(consts.WEB, self.request)
            validator.validate()  # No exception should be raised

            mocked_flag.return_value = True
            response_data = {'success': True, 'score': 0.3}
            mocked_post.return_value.json.return_value = response_data
            validator.validate()  # No exception should be raised

            response_data['success'] = False
            with self.assertRaises(Exception) as context:
                validator.validate()
                self.assertIn('Request malformed - Hcaptcha', str(context.exception))

            response_data['success'] = True
            response_data['score'] = 0.5
            with self.assertRaises(Exception) as context:
                validator.validate()
                self.assertIn('Request malformed - Hcaptcha', str(context.exception))

            mocked_logger.assert_not_called()

    from parameterized import parameterized

    @parameterized.expand(
        [
            ('data', {}, None),
            ('data', {'k': 'v'}, None),
            ('data', {'email': '<EMAIL>'}, '<EMAIL>'),
            ('query_params', {}, None),
            ('query_params', {'k': 'v'}, None),
            ('query_params', {'email': ['<EMAIL>']}, '<EMAIL>'),
            ('body', '{}', None),
            ('body', '{"k": "v"}', None),
            ('body', '{"email": "<EMAIL>"}', '<EMAIL>'),
            ('body', '"}', None),  # invalid json
            ('arguments', {}, None),
            ('arguments', {'k': 'v'}, None),
            ('arguments', {'email': ['<EMAIL>']}, '<EMAIL>'),
        ]
    )
    @override_feature_flag({HCaptchaFlag.flag_name: True})
    @patch('requests.post')
    @patch('service.customer.hcaptcha.logger.warning')
    def test_validate_failed_logger(self, field, value, email, mocked_logger, mocked_post):
        self.request = MagicMock(
            headers={'user-agent': 'test user agent', 'x-hcaptcha-token': 'test_token'},
            path='/customer_api/account/login',
            method='POST',
            user=MagicMock(id=123),
            spec=True,
            **{field: value},
        )
        mocked_post.return_value.json.return_value = {'success': False, 'score': 0}
        source = consts.WEB

        validator = HCaptchaRequestValidator(source, self.request)
        with self.assertRaises(ServiceError):
            validator.validate()

        mocked_logger.assert_called_once()
        call_log_kwargs = mocked_logger.call_args.kwargs
        self.assertEqual('hCaptcha - validation failed', call_log_kwargs['msg'])
        self.assertEqual(self.request.path, call_log_kwargs['extra']['path'])
        self.assertEqual(settings.API_COUNTRY, call_log_kwargs['extra']['country_code'])
        self.assertEqual(source, call_log_kwargs['extra']['source'])
        self.assertEqual(self.request.user.id, call_log_kwargs['extra']['user_id'])
        self.assertEqual('test user agent', call_log_kwargs['extra']['user_agent'])
        self.assertEqual('test_token', call_log_kwargs['extra']['hcaptcha_token'])
        self.assertEqual(email, call_log_kwargs['extra']['email'])
