import logging
import random
import time
from collections import defaultdict
from dataclasses import asdict

from bo_obs.datadog.enums import BooksyTeams
from django.conf import settings
from django.db import transaction
from django.utils.translation import gettext as _
from rest_framework import serializers

import lib.tools
from lib import jinja_renderer
from lib.booksy_sms import (
    PhoneNumber,
    generate_sms_registration_code,
    parse_phone_number,
    phone_number_is_whitelisted,
)
from lib.db import retry_on_sync_error
from lib.elasticsearch.consts import ESDocType
from lib.elasticsearch.tools import index_document
from lib.feature_flag.adapter import UserData
from lib.feature_flag.enums import CustomUserAttributes
from lib.feature_flag.feature.bci import ReduceBCIDuplicatesFromInvitesFlag
from lib.feature_flag.feature.customer import PhoneLimitterFlag
from lib.feature_flag.feature.security import (
    ExcludeFromOTPSMSFlag,
    HCaptchaFlag,
    HCaptchaForceFlag,
)
from lib.rivers import River, bump_document
from lib.tools import mp_deeplink, tznow, validated_email
from service.account import AccountRequestHandler
from service.customer.hcaptcha import HCaptchaRequestValidator
from service.customer.serializers import BooksyCustomerRegistrationPhoneSerializer
from service.customer.throttling import (
    CustomerPhoneRateTrottle,
    OTPPhoneRateThrottle,
    OTPSimpleRateThrottle,
)
from service.exceptions import ServiceError
from service.images.helpers import BasePhotoUploadHandler, get_file_from_request_files
from service.mixins.paginator import PaginatorMixin
from service.mixins.throttling import BooksyScopedRateThrottle, ThrottleScopeEnum
from service.tools import RequestHandler, json_request, session
from webapps import consts
from webapps.business.events import business_bookmarked_event
from webapps.business.models import Business, Resource
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business.searchables.business import BusinessByIdSearchable
from webapps.business.searchables.serializers import (
    BusinessSearchDetailsHitSerializer,
    BusinessSearchHitSerializer,
)
from webapps.business_customer_info.serializers.serializers import ShortCustomerInfoSerializer
from webapps.business_customer_info.v2.application.future_domain_services.bci_duplicates import (
    BCIDuplicatesServiceConfig,
    BCIDuplicatesServiceImpl,
)
from webapps.business_customer_info.v2.application.future_domain_services.bookmark import (
    BookmarkServiceConfig,
    BookmarkServiceImpl,
)
from webapps.business_customer_info.v2.application.services.bookmark import (
    BookmarkApplicationService,
    BookmarkApplicationServiceConfig,
)
from webapps.business_customer_info.v2.infrastructure.analytics.service import (
    AnalyticServiceSegment,
)
from webapps.business_customer_info.v2.infrastructure.search_engine.service import (
    ElasticSearchService,
)
from webapps.family_and_friends.helpers.profiles import delete_member_profile_after_user_deletion
from webapps.images.enums import ImageTypeEnum
from webapps.images.models import ImageLike
from webapps.images.searchables import ImageWithLikeSearchable
from webapps.notification.enums import NotificationCategory
from webapps.notification.models import NotificationHistory, NotificationSMSCodes, UserNotification
from webapps.notification.scenarios import AccountAddedScenario, start_scenario
from webapps.notification.tasks import commence_send_sms
from webapps.notification.tasks.sms_codes import (
    SMSRegistrationRequest,
    send_sms_registration_code_task,
)
from webapps.photo.models import Photo
from webapps.segment.tasks import analytics_customer_registration_completed_task
from webapps.user.elasticsearch.tools import append_to_user_search_data_fast_river
from webapps.user.enums import AuthOriginEnum
from webapps.user.models import User, UserProfile
from webapps.versum_migration.user_connection.customer_registration import (
    versum_migration_handle_customer_registration_completed,
)

_logger_sms = logging.getLogger('booksy.sms')


class CustomerSMSCodeHandler(AccountRequestHandler):  # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.CUSTOMER_ONBOARDING,)
    throttle_scope = ThrottleScopeEnum.CUSTOMER_SMS_CODE
    throttle_classes = [BooksyScopedRateThrottle, OTPSimpleRateThrottle, OTPPhoneRateThrottle]

    @session()
    @json_request
    def post(self):
        """
        swagger:
            summary: Send verification SMS Code
            type: SMSCodeResponse
            parameters:
                - name: body
                  description: JSON with account data
                  type: SMSCodeRequest
                  paramType: body

        """
        abuse_ip_address = self.request.headers.get('X-Real-IP')
        abuse_fingerprint = self.request.headers.get('X-FINGERPRINT')

        if (HCaptchaFlag() and HCaptchaForceFlag()) or (
            self.request.headers.get('x-hcaptcha-token') and HCaptchaFlag()
        ):
            HCaptchaRequestValidator(
                booking_source_name=self.booking_source.name,
                request=self.request,
            ).validate()

        serializer = BooksyCustomerRegistrationPhoneSerializer(
            data=self.data,
            context={'abuse_fingerprint': abuse_fingerprint},
        )

        data = self.validate_serializer(serializer)
        cell_phone: PhoneNumber = data['cell_phone']

        if ExcludeFromOTPSMSFlag(
            UserData(
                custom={
                    CustomUserAttributes.PHONE_NO: cell_phone.global_short,
                    CustomUserAttributes.PHONE_NO_COUNTRY: cell_phone.country.lower(),
                }
            )
        ) and (  # don't block local numbers by mistake!
            cell_phone.country.lower() != settings.API_COUNTRY
        ):
            # ACHTUNG SECURITY HACK
            # temporarily disable selected numbers from OTP SMS
            # but do it silently, so hackers wouldn't notice :)
            _logger_sms.warning(
                'Request excluded from OTP SMS [%s -> %s]',
                settings.API_COUNTRY,
                cell_phone.country.lower(),
                extra={
                    'hct': self.request.headers.get('x-hcaptcha-token'),
                    'abuse_ip_address': abuse_ip_address,
                    'abuse_fingerprint': abuse_fingerprint,
                    'cell_phone': cell_phone.db_format,
                    'cell_phone_prefix': cell_phone.country.lower(),
                    'country_code': settings.API_COUNTRY,
                    'metadata': lib.tools.get_meta_data_from_handler(self),
                    'request_data': self.data,
                },
            )
            time.sleep(random.random())
            self.finish(
                {
                    'sms_code_hash': '',  # deprecated
                    'sms_code_length': 4,
                    'formatted_cell_phone': cell_phone.global_nice,
                }
            )
            return

        sms_code = generate_sms_registration_code()

        registration_request = SMSRegistrationRequest(
            phone_db_format=cell_phone.db_format,
            phone_global_short=cell_phone.global_short,
            sms_code=sms_code,
            android_receiver=self.booking_source.name == consts.ANDROID,
            abuse_ip_address=abuse_ip_address,
            abuse_fingerprint=abuse_fingerprint,
        )
        send_sms_registration_code_task.delay(
            # serialize explicit
            request=asdict(registration_request),
            # if empty string should be None
            language=self.data.get('language') or None,
        )

        self.finish(
            {
                'sms_code_hash': '',  # deprecated
                'sms_code_length': len(sms_code),
                'formatted_cell_phone': cell_phone.global_nice,
            }
        )


class CustomerSMSInviteHandler(RequestHandler):
    booksy_teams = (BooksyTeams.CUSTOMER_ONBOARDING,)
    throttle_scope = ThrottleScopeEnum.CUSTOMER_SMS_INVITE
    throttle_classes = [BooksyScopedRateThrottle]

    @session()
    @json_request
    def post(self):
        """
        swagger:
            summary: Send Booksy invite SMS Code
            notes: |
                Recipient gets an SMS with deeplink
                to app store with customer app
            type: SMSInvitationResponse
            parameters:
                - name: body
                  description: JSON with account data
                  type: SMSInvitationRequest
                  paramType: body

        """
        cell_phone = parse_phone_number(self.data.get('cell_phone'))

        if (HCaptchaFlag() and HCaptchaForceFlag()) or (
            self.request.headers.get('x-hcaptcha-token') and HCaptchaFlag()
        ):
            HCaptchaRequestValidator(
                booking_source_name=self.booking_source.name, request=self.request
            ).validate()

        self.quick_assert(
            phone_number_is_whitelisted(cell_phone),
            ('not_valid', 'validation', 'cell_phone'),
            _("Your phone number country is not supported. Please use a local phone number."),
        )

        self.quick_assert(
            cell_phone.is_valid,
            ('not_valid', 'validation', 'cell_phone'),
            _("Invalid phone number"),
        )

        abuse_ip_address = self.forwarded_ip
        abuse_fingerprint = self.request.headers.get('X-FINGERPRINT')
        usage_count = NotificationSMSCodes.get_usage_count(
            abuse_fingerprint=abuse_fingerprint,
            seconds=settings.SMS_REGISTRATION_ABUSE_PERIOD,
        )

        self.quick_assert(
            usage_count < settings.SMS_REGISTRATION_ABUSE_LIMIT,
            ('abuse', 'validation', '__all__'),
            _("Too many requests for invite SMS"),
        )

        recent_tries = NotificationSMSCodes.get_recent(
            cell_phone.db_format,
            settings.SMS_REGISTRATION_PERIOD,
        )

        recent_tries_time_ago = [(tznow() - rt.created).total_seconds() for rt in recent_tries]

        self.quick_assert(
            (not recent_tries or min(recent_tries_time_ago) > settings.SMS_REGISTRATION_WAIT),
            ('entity_exists', 'database', 'sms_code'),
            _("Please wait. Text is on its way."),
        )

        self.quick_assert(
            len(recent_tries) < settings.SMS_REGISTRATION_LIMIT,
            ('entity_exists', 'database', 'cell_phone'),
            _("Too many attempts for this phone number"),
        )

        sjr = jinja_renderer.ScenariosJinjaRenderer()
        body = sjr.render(
            'invitation',
            'sms_invitation',
            language=self.data.get('language') or None,
            extension='sms',
            template_args={
                'download_link': mp_deeplink('download'),
            },
        )

        NotificationSMSCodes.add(
            phone=cell_phone.db_format,
            sms_code='',
            abuse_ip_address=abuse_ip_address,
            abuse_fingerprint=abuse_fingerprint,
            metadata=self.data,
        )

        history_data = {
            'sender': NotificationHistory.SENDER_SYSTEM,
            'type': UserNotification.SMS_NOTIFICATION,
            'task_id': f'{NotificationCategory.CUSTOMER_INVITATION},{cell_phone.global_short}',
            'task_type': NotificationHistory.TASK_TYPE__SMS_CUSTOMER_INVITATION,
        }

        commence_send_sms(
            body,
            [cell_phone.global_short],
            history_data=history_data,
        )

        self.finish(
            {
                'formatted_cell_phone': cell_phone.global_nice,
            }
        )


class CustomerCreateHandler(
    AccountRequestHandler,
    RequestHandler,
):  # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.CUSTOMER_ONBOARDING,)
    throttle_scope = ThrottleScopeEnum.ACCOUNT_CREATE
    throttle_classes = [BooksyScopedRateThrottle, CustomerPhoneRateTrottle]

    def get_throttles(self):
        if not PhoneLimitterFlag():
            self.throttle_classes = [BooksyScopedRateThrottle]
        return super().get_throttles()

    @json_request
    @session()
    def post(self):
        """Create Customer User.

        swagger:
            summary: Create Customer User
            notes: Create new customer user
            type: LoginResponse
            parameters:
                - name: body
                  description: JSON with account data
                  type: CreateUserData
                  paramType: body

        """
        image_obj = get_file_from_request_files(self.request)
        user = self.create_user_from_data(
            self.data,
            profile_type=UserProfile.Type.CUSTOMER,
            # <editor-fold desc="TODO photo base64 rm when frontend migrated">
            # process photo only if no file attached
            process_photo=image_obj is None,
            # </editor-fold>
        )
        self.session = user.create_session(
            origin=AuthOriginEnum.BOOKSY,
            fingerprint=self.fingerprint,
        )
        profile = user.profiles.get(profile_type=UserProfile.Type.CUSTOMER)
        analytics_customer_registration_completed_task.delay(
            user_id=user.id,
            invited_by_business_id=self.data.get('invited_by_business_id'),
            context={
                'session_user_id': user.id,
            },
        )
        versum_migration_handle_customer_registration_completed(user_id=user.id)

        # <editor-fold desc="early_finish section">
        start_scenario(AccountAddedScenario, user_profile=profile)
        # <editor-fold desc="save image on S3">
        if photo := Photo.save_from_request(image_obj=image_obj):
            profile.photo = photo
            profile.save()
        # </editor-fold>
        # </editor-fold>

        self.with_access_token_response(profile, status=201)


class CustomerAccountHandler(
    AccountRequestHandler,
    RequestHandler,
):  # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.CUSTOMER_ENGAGEMENT, BooksyTeams.CUSTOMER_ONBOARDING)

    @session(login_required=True)
    def get(self):
        """Return Customer User's Profile details.

        swagger:
            summary: Return Customer User's Profile details
            type: ProfileDetailsResponse

        """
        self.no_access_token_response(profile=self.user.profile, user=self.user)

    @session(login_required=True)
    @json_request
    def put(self):
        """
        New apps. Update Customer User Profile details. With sms
        registration code

        swagger:
            summary: Update Customer User Profile details
            type: LoginResponse
            parameters:
                - name: body
                  description: JSON with account data
                  type: Profile
                  paramType: body

        """
        self.update_user(self.user, self.data, profile_type=UserProfile.Type.CUSTOMER)

    @session(login_required=True)
    def delete(self):
        """Delete Customer Account

        swagger:
            summary: Delete Customer Account
        """
        self.user.email = f'deleted {self.user.email}'
        self.user.is_active = False
        self.user.save()

        delete_member_profile_after_user_deletion(self.user)
        # pylint: disable=unreachable
        self.session.delete()
        self.finish_with_json(200, {})


class CustomerLoginHandler(
    AccountRequestHandler,
    RequestHandler,
):  # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.CUSTOMER_ONBOARDING,)
    throttle_scope = ThrottleScopeEnum.CHECK_ACCOUNT_EXISTS
    throttle_classes = [BooksyScopedRateThrottle]

    @json_request
    @session()
    def post(self):
        """Log-in Customer User.

        swagger:
            summary: Log-in Customer User
            type: LoginResponse
            parameters:
                - name: body
                  paramType: body
                  type: EmailAndPassword
                  description: a JSON with email and password

        """
        if (HCaptchaFlag() and HCaptchaForceFlag()) or (
            self.request.headers.get('x-hcaptcha-token') and HCaptchaFlag()
        ):
            self.hcaptcha_request_validator = HCaptchaRequestValidator(
                booking_source_name=self.booking_source.name,
                request=self.request,
            )

            try:
                self.hcaptcha_request_validator.validate()
            except ServiceError as exc:
                self._log_captcha_exception(captcha_provider=HCaptchaRequestValidator.SERVICE_NAME)
                raise exc

        self.log_user_in()


class CustomerChangeEmailHandler(RequestHandler):
    booksy_teams = (BooksyTeams.CUSTOMER_ENGAGEMENT, BooksyTeams.CUSTOMER_ONBOARDING)

    @session(login_required=True)
    @json_request
    def post(self):
        """Initiate email change.
        Input: the new email

        swagger:
            summary:
                Change Customer email
            notes: Empty response
            parameters:
              - name: body
                paramType: body
                type: ChangeEmailRequest
        :swagger

        swaggerModels:
            ChangeEmailRequest:
                id: ChangeEmailRequest
                required:
                  - new_email
                properties:
                    new_email:
                        type: string
                        description: New email address
        """
        if (HCaptchaFlag() and HCaptchaForceFlag()) or (
            self.request.headers.get('x-hcaptcha-token') and HCaptchaFlag()
        ):
            HCaptchaRequestValidator(
                booking_source_name=self.booking_source.name,
                request=self.request,
            ).validate()

        self.quick_assert(
            not self.user.is_staff,
            ('invalid', 'validation', 'new_email'),
            _("Booksy Staff cannot change email"),
        )
        new_email = validated_email(
            self.data.get('new_email'),
            field='new_email',
            description_required=_('New email is required'),
        )
        self.quick_assert(
            not User.objects.filter(email=new_email).exists(),
            ('entity_exists', 'database', 'email'),
            _("Email already registered"),
        )

        # This is necessary for generating a unique token per
        # provided new_email.
        old_email = self.user.email
        self.user.email = new_email
        self.user.initiate_email_change(
            renderer=self.render_string,
            receiver=old_email,
        )

        self.set_status(200)
        self.finish({})


class CustomerHistoryHandler(RequestHandler):
    booksy_teams = (BooksyTeams.CUSTOMER_ENGAGEMENT,)

    @session(login_required=True)
    def delete(self):
        # UserRecentSearch model was removed, but we don't want to break API
        self.finish_with_json(200, {})


# pylint: disable=too-many-ancestors
class CustomerPhotoHandler(BasePhotoUploadHandler):
    # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.CUSTOMER_ENGAGEMENT, BooksyTeams.CUSTOMER_ONBOARDING)

    @session(login_required=True)
    def delete(self):
        if self.user.profile.photo is not None:
            self.user.profile.photo = None
            self.user.profile.save()
        self.finish_with_json(200, {})

    @session(login_required=True)
    def put(self):
        """
        swagger:
            summary: Upload avatar photo (multipart/form-data).
            notes: |
                If user has already photo then delete it and create new.
                If user has no photo then create new.
            consumes: multipart/form-data
            parameters:
                - name: photo
                  description: Photo content
                  type: File
                  paramType: form
                  required: true

        """
        context = {
            'owner_model': self.user.profile,
            'image_type': ImageTypeEnum.USER_AVATAR,
        }

        serializer = self.save_photo(context)
        self.finish_with_json(201, serializer.data)


# pylint: disable=too-many-ancestors
class CustomerBookmarksListHandler(RequestHandler, PaginatorMixin):
    # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.CUSTOMER_ENGAGEMENT,)
    page_name = 'businesses_page'
    per_page_name = 'businesses_per_page'
    default_per_page_setting = 'CUSTOMER_BOOKMARKS_PER_PAGE'

    @session(login_required=True)
    def get(self):
        """
        swagger:
            parameters:
                - name: businesses_page
                  description: Results page
                  paramType: query
                  required: False
                  type: integer
                  defaultValue: 1
                - name: businesses_per_page
                  description: How many results per page to return
                  paramType: query
                  required: False
                  type: integer
                  default_from_const: settings.CUSTOMER_BOOKMARKS_PER_PAGE
                - name: include_details
                  description: full Business details as in search handler
                  paramType: query
                  required: False
                  type: integer
            type: CustomerBookmarksList
        :swagger
        swaggerModels:
            CustomerBookmarksList:
                id: CustomerBookmarksList
                required:
                    - businesses
                    - businesses_count
                    - businesses_per_page
                properties:
                    businesses:
                      type: array
                      items:
                        type: BookmarkedBusiness
                    businesses_count:
                      type: integer
                    businesses_per_page:
                      type: integer
            BookmarkedBusiness:
                id: BookmarkedBusiness
                required:
                    - item_no
                    - id
                    - name
                    - name_short
                    - reviews_count
                    - reviews_rank
                    - reviews_stars
                    - booking_policy
                    - pricing_level
                    - promoted
                    - cover_photo
                    - thumbnail_photo
                    - regions
                    - business_categories
                    - location
                    - is_recommended
                    - max_discount_rate
                properties:
                    item_no:
                        type: integer
                    id:
                        type: integer
                    name:
                        type: string
                    name_short:
                        type: string
                    reviews_count:
                        type: integer
                    reviews_rank:
                        type: number
                        maximum: 5
                        minimum: 1
                    reviews_stars:
                        type: integer
                        maximum: 5
                        minimum: 0
                    booking_policy:
                        type: string
                    pricing_level:
                        type: integer
                    promoted:
                        type: boolean
                    cover_photo:
                        type: string
                    thumbnail_photo:
                        type: string
                    business_categories:
                        type: IdAndNameAndSlug
                    regions:
                        type: array
                        items:
                            type: RegionSimple
                    location:
                        type: BusinessDetailsLocation
                    is_recommended:
                        type: boolean
                    max_discount_rate:
                        type: integer

        """
        self.set_status(200)

        self.parse_page_values_from_get()
        args = self._prepare_get_arguments()

        all_biz = (
            Business.objects.filter(
                active=True,
                visible=True,
                business_customer_infos__user=self.user,
                business_customer_infos__bookmarked=True,
            )
            .extra(
                select={'bookmarked_date_null': "bookmarked_date IS null"},
            )
            .order_by(
                'bookmarked_date_null',  # nulls to the back
                '-business_customer_infos__bookmarked_date',
            )
        )
        all_biz_count = all_biz.count()
        biz_on_page = all_biz.values_list('id', flat=True)[self.offset : self.limit]

        businesses = []
        if biz_on_page:
            businesses = BusinessByIdSearchable(
                ESDocType.BUSINESS,
                serializer=(
                    BusinessSearchDetailsHitSerializer
                    if args.get('include_details')
                    else BusinessSearchHitSerializer
                ),
            ).execute({'ids': biz_on_page})

        # inject item_no
        for i, business in enumerate(businesses, 1):
            business['item_no'] = i

        self.finish_with_json(
            200,
            {
                "businesses": list(businesses),
                'businesses_count': all_biz_count,
                # pylint: disable=no-member
                'businesses_per_page': self.businesses_per_page,
            },
        )


class CustomerBookmarksHandler(RequestHandler):
    """
    swagger:
        parameters:
            - name: business_id
              description: Business id
              type: integer
              paramType: path
            - name: body
              paramType: body
              required: true
              type: Source
    :swagger
    swaggerModels:
        Source:
            id: Source
            properties:
                source:
                    type: string
    """

    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @json_request
    @session(login_required=True)
    def put(self, business_id):
        business = self.get_object_or_404(
            ('business', 'Business'),
            __only='id',
            id=business_id,
        )
        source = self.data.get('source')
        if ReduceBCIDuplicatesFromInvitesFlag():
            bookmark_application_service = BookmarkApplicationService(
                analytic_service=AnalyticServiceSegment(),
                bookmark_service=BookmarkServiceImpl(
                    analytic_service=AnalyticServiceSegment(),
                    config=BookmarkServiceConfig(user=self.user),
                ),
                duplicate_service=BCIDuplicatesServiceImpl(
                    config=BCIDuplicatesServiceConfig(user=self.user)
                ),
                search_service=ElasticSearchService(),
                config=BookmarkApplicationServiceConfig(user=self.user),
            )
            bookmark_application_service.handle_business_bookmark(business, source)
        else:
            self.handle_business_bookmark(business, source)

        self.set_status(201)
        self.finish({})

    @session(login_required=True)
    def delete(self, business_id):
        bci = self.get_object_or_404(
            ('business', 'BusinessCustomerInfo'),
            business_id=business_id,
            user=self.user,
            bookmarked=True,
            deleted__isnull=True,
        )

        bci.bookmarked = False
        bci.bookmarked_date = None
        bci.save()
        self.finish_with_json(200, {})

    def handle_business_bookmark(self, business, source):
        with transaction.atomic():
            bci, created = self.user.business_customer_infos.get_or_create(
                business=business,
                defaults={
                    "bookmarked": True,
                    "bookmarked_date": business.tznow,
                    "visible_in_business": False,
                },
                deleted__isnull=True,
            )
            if source:
                bci.set_client_type(BusinessCustomerInfo.CLIENT_TYPE__BUSINESS_SUB_DEP)

            if not created and bci.bookmarked is not True:
                bci.bookmarked = True
                bci.bookmarked_date = business.tznow

            bci.save()
        business_bookmarked_event.send(bci)


class CustomerLikesRequest(serializers.Serializer):
    """Serializer responsible for CustomerLikesRequest swagger model."""

    businesses = serializers.ListField(child=serializers.IntegerField(), required=False)
    images = serializers.ListField(child=serializers.IntegerField(), required=False)
    businesses_subdomains = serializers.ListField(child=serializers.IntegerField(), required=False)
    resources = serializers.ListField(child=serializers.IntegerField(), required=False)


class CustomerLikesHandler(RequestHandler):
    """Check Customer's Image Likes and Business Bookmarks (and review status).

    swagger:
        summary: Check Customer's Image Likes and Business Bookmarks.
        parameters:
          - name: body
            paramType: body
            type: CustomerLikesRequest
        type: CustomerLikesResponse
    :swagger
    swaggerModels:
        CustomerLikesRequest:
            id: CustomerLikesRequest
            description: a request to check if images and businesses are liked
            properties:
                businesses:
                    type: array
                    items:
                        type: integer
                    description: a list of Business IDs
                images:
                    type: array
                    items:
                        type: integer
                    description: a list of Images IDs
                businesses_subdomains:
                    type: array
                    items:
                        type: integer
                    description:
                        a list of business IDs which was visited by
                        subdomain/deeplink. It must be a subset of businesses
                        array
                resources:
                    type: array
                    items:
                        type: integer
                    description: a list of Resource IDs
        CustomerLikesResponse:
            id: CustomerLikesResponse
            description: a response listing liked images and businesses
            required:
                - businesses
                - images
            properties:
                businesses:
                    type: BusinessIDtoShortCustomerInfo
                    description: a mapping of Business IDs to ShortCustomerInfos
                image_likes:
                    type: ImageIDtoLikesCount
                    description: a mapping of Images IDs to LikesCount
                resource_likes:
                    type: ResourceIDtoResourceLikes
                    description: a mapping of Resource IDs to ResourceLike
        BusinessIDtoShortCustomerInfo:
            id: BusinessIDtoShortCustomerInfo
            description: a mapping of Business IDs to ShortCustomerInfos
            properties:
                '[business_id]':
                    type: ShortCustomerInfo
        ImageIDtoLikesCount:
            id: ImageIDtoLikesCount
            description: a mapping of Images IDs to LikesCount
            properties:
                '[image_id]':
                    type: LikesCount
        ResourceIDtoResourceLikes:
            id: ResourceIDtoResourceLikes
            description: a mapping of Resource IDs to ResourceLike
            properties:
                '[resource_id]':
                    type: ResourceLike
        LikesCount:
            id: LikesCount
            description: a container for likes count and user like
            required:
                - liked
                - likes_count
            properties:
                liked:
                    description: current User likes this photo
                    type: boolean
                likes_count:
                    description: number of likes for this photo
                    type: integer
        ResourceLike:
            id: ResourceLike
            description: a container for User Resource like
            required:
                - liked
            properties:
                liked:
                    description: current User likes this Resource
                    type: boolean

    """

    booksy_teams = (BooksyTeams.CUSTOMER_ENGAGEMENT,)

    @session(login_required=True)
    @json_request
    def post(self):
        serializer = CustomerLikesRequest(data=self.data)
        data = self.validate_serializer(serializer)

        biz_qs = BusinessCustomerInfo.objects.filter(
            user=self.user,
            business__in=data.get('businesses', []),
        )
        image_ids = data.get('images', [])
        image_likes = self._get_image_likes(image_ids, self.user.id)

        ret = {
            'businesses': {
                info['business']: info
                for info in ShortCustomerInfoSerializer(biz_qs, many=True).data
            },
            'image_likes': {
                image_id: image_likes.get(image_id, {'liked': False, 'likes_count': 0})
                for image_id in image_ids
            },
            'resource_likes': self._get_resource_likes(
                data.get('resources', []),
            ),
        }
        self.finish(ret)

    @staticmethod
    def _get_image_likes(image_ids, user_id):
        if not image_ids:
            return set()
        result = (
            ImageWithLikeSearchable(
                ESDocType.IMAGE,
            )
            .search(
                {
                    'image_ids': image_ids,
                    'user_id': user_id,
                }
            )
            .params(_source=False)
            .execute()
        )

        return {
            int(doc.get_model_id()): {
                'liked': bool(doc.meta.inner_hits.user_like.hits.total.value),
                'likes_count': doc.meta.inner_hits.image_like.hits.total.value,
            }
            for doc in result
        }

    def _get_resource_likes(self, resource_ids):
        if not resource_ids:
            return {}
        bookmarked_resources = BusinessCustomerInfo.objects.filter(
            user=self.user,
            bookmarked_resources__in=resource_ids,
        ).values_list('bookmarked_resources', flat=True)
        return {
            resource_id: {'liked': resource_id in bookmarked_resources}
            for resource_id in resource_ids
        }


class LikesBulkCreateHandler(RequestHandler):
    """Bulk Create Customer's Image Likes and Business Bookmarks.

    swagger:
        summary: Bulk Create Customer's Image Likes and Business Bookmarks.
        notes: Image Likes are not implemented yet.
        parameters:
          - name: body
            paramType: body
            type: CustomerLikesRequest
        type: LikesBulkCreateResponse
    :swagger
    swaggerModels:
        LikesBulkCreateResponse:
            id: LikesBulkCreateResponse
            description: a response listing liked images and businesses
            required:
                - businesses
                - images
            properties:
                businesses:
                    type: BusinessLikeBulkCreate
                    description: lists status of like creation
                images:
                    type: ImagesLikeBulkCreate
                    description: ImagesLikeBulkCreate
                resources:
                    type: ResourcesLikeBulkCreate
                    description: ResourcesLikeBulkCreate
        BusinessLikeBulkCreate:
            id: BusinessLikeBulkCreate
            description: a mapping of Business IDs to ShortCustomerInfos
            required:
                - skipped
                - updated
                - created
                - no_such_business
            properties:
                skipped:
                    type: array
                    description: skipped, because user already liked this
                    items:
                        type: integer
                updated:
                    type: array
                    description: success, customer card was updated
                    items:
                        type: integer
                created:
                    type: array
                    description: success, customer card was created
                    items:
                        type: integer
                no_such_business:
                    type: array
                    description: error, business does not exist
                    items:
                        type: integer
        ImagesLikeBulkCreate:
            id: ImagesLikeBulkCreate
            description:
                Count of bookmarked Images,
                no mater if was bookmarked before
            required:
                - updated
            properties:
                bookmarked:
                    type: array
                    description:
                        Count of bookmarked Resoruces,
                        no mater if was bookmarked before
                    items:
                        type: integer
        ResourcesLikeBulkCreate:
            id: ResourcesLikeBulkCreate
            description: Count of bookmarked Resources
            required:
                - updated
            properties:
                bookmarked:
                    type: array
                    description: success, Resource was bookmarked
                    items:
                        type: integer
    """

    booksy_teams = (BooksyTeams.CUSTOMER_ENGAGEMENT, BooksyTeams.PROVIDER_MARKETING)

    @session(login_required=True)
    @json_request
    def post(self):
        serializer = CustomerLikesRequest(data=self.data)
        data = self.validate_serializer(serializer)
        business_ids = set(data.get('businesses', []))
        business_subdomains_ids = set(data.get('businesses_subdomains', []))
        if ReduceBCIDuplicatesFromInvitesFlag():
            self.validate_business_subdomains(business_ids, business_subdomains_ids)
            bookmark_application_service = BookmarkApplicationService(
                analytic_service=AnalyticServiceSegment(),
                bookmark_service=BookmarkServiceImpl(
                    analytic_service=AnalyticServiceSegment(),
                    config=BookmarkServiceConfig(user=self.user),
                ),
                duplicate_service=BCIDuplicatesServiceImpl(
                    config=BCIDuplicatesServiceConfig(user=self.user)
                ),
                search_service=ElasticSearchService(),
                config=BookmarkApplicationServiceConfig(user=self.user),
            )
            business_result = bookmark_application_service.handle_bulk_business_bookmark(
                business_ids, business_subdomains_ids
            )
        else:
            business_result = self.handle_business_likes(business_ids, business_subdomains_ids)
        resource_result = self.handle_resource_likes(data.get('resources'))
        image_result = self.handle_image_likes(data.get('images'))
        self.finish(
            {
                'businesses': business_result,
                'resources': resource_result,
                'images': image_result,
            }
        )

    def validate_business_subdomains(self, business_ids, business_subdomains_ids):
        if business_subdomains_ids - business_ids:
            self.quick_error(
                ('not_valid', 'validation', 'businesses_subdomains'),
                'businesses_subdomains must be a subset of businesses',
            )

    @retry_on_sync_error
    @transaction.atomic
    def handle_business_likes(self, business_ids, business_subdomains_ids):
        now = lib.tools.tznow()
        business_ids = set(business_ids)
        business_subdomains_ids = set(business_subdomains_ids)
        if business_subdomains_ids - business_ids:
            self.quick_error(
                ('not_valid', 'validation', 'businesses_subdomains'),
                'businesses_subdomains must be a subset of businesses',
            )
        result = {
            'skipped': [],
            'updated': [],
            'created': [],
            'no_such_business': [],
        }
        existing_bookmarks = BusinessCustomerInfo.objects.filter(
            user=self.user,
            business__in=business_ids,
        ).values_list('id', 'business_id', 'bookmarked')
        updated_bci_ids = []
        for bci_id, biz_id, bookmarked in existing_bookmarks:
            if bookmarked:
                result['skipped'].append(biz_id)
            else:
                result['updated'].append(biz_id)
                updated_bci_ids.append(bci_id)

        # update existing customer cards
        BusinessCustomerInfo.objects.filter(
            id__in=updated_bci_ids,
        ).update(
            bookmarked=True,
            bookmarked_date=now,
        )

        # get ids of existing businesses
        no_existing_bci = business_ids - set(result['skipped']) - set(result['updated'])
        result['created'] = list(
            Business.objects.filter(id__in=no_existing_bci).values_list('id', flat=True)
        )
        result['no_such_business'] = list(no_existing_bci - set(result['created']))
        # create new customer cards
        BusinessCustomerInfo.objects.bulk_create(
            [
                BusinessCustomerInfo(
                    user=self.user,
                    business_id=biz_id,
                    bookmarked=True,
                    bookmarked_date=now,
                    visible_in_business=False,
                )
                for biz_id in result['created']
            ]
        )
        # .bulk_create does not return ids, so we refetch
        created_bci_ids = list(
            BusinessCustomerInfo.objects.filter(
                user=self.user,
                business_id__in=result['created'],
            ).values_list('id', flat=True)
        )

        bump_document(
            River.BUSINESS_CUSTOMER,
            updated_bci_ids + created_bci_ids,
            _origin='LikesBulkCreateHandler',
        )
        BusinessCustomerInfo.objects.filter(
            business_id__in=business_subdomains_ids,
            client_type=BusinessCustomerInfo.CLIENT_TYPE__UNKNOWN,
        ).update(client_type=BusinessCustomerInfo.CLIENT_TYPE__BUSINESS_SUB_DEP)
        append_to_user_search_data_fast_river(self.user)
        return result

    def handle_resource_likes(self, resource_ids):
        if not resource_ids:
            return

        resources = Resource.objects.filter(pk__in=resource_ids).only('id', 'business_id')
        businesses = defaultdict(list)
        for resource in resources:
            businesses[resource.business_id].append(resource)
        for business_id, biz_resources in list(businesses.items()):
            bci, __ = BusinessCustomerInfo.objects.get_or_create(
                user=self.user,
                business_id=business_id,
            )
            bci.bookmarked_resources.add(*biz_resources)
        return len(resources)

    def handle_image_likes(self, image_ids):
        if not image_ids:
            return

        existing_image_likes = ImageLike.objects.filter(
            user_id=self.user.id,
            image_id__in=image_ids,
        ).values_list('image_id', flat=True)
        new_ids = set(image_ids) - set(existing_image_likes)
        likes = ImageLike.objects.bulk_create(
            ImageLike(
                user_id=self.user.id,
                image_id=new_id,
            )
            for new_id in new_ids
        )
        ids_to_reindex = [like.id for like in likes]
        index_document(ESDocType.IMAGE_LIKE, ids_to_reindex)
        return len(new_ids)


class CustomerResourceBookmarksHandler(RequestHandler):
    """
    swagger:
        parameters:
            - name: resource_id
              description: Resource id
              type: integer
              paramType: path
    :swagger
    """

    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)

    @session(login_required=True)
    def put(self, resource_id):
        resource = self.get_object_or_404(
            ('business', 'Resource'),
            __only=['id', 'business'],
            id=resource_id,
        )

        with transaction.atomic():
            bci, __ = self.user.business_customer_infos.get_or_create(
                business_id=resource.business_id,
                defaults={
                    "visible_in_business": False,
                },
            )
            bci.bookmarked_resources.add(resource)
        self.set_status(201)
        self.finish({})

    @session(login_required=True)
    def delete(self, resource_id):
        resource = self.get_object_or_404(
            ('business', 'Resource'),
            __only=['id', 'business'],
            id=resource_id,
        )
        bci = self.get_object_or_404(
            ('business', 'BusinessCustomerInfo'),
            business_id=resource.business_id,
            user=self.user,
        )
        bci.bookmarked_resources.remove(resource)
        self.finish_with_json(200, {})
