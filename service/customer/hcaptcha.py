import logging

import json
from contextlib import suppress

import requests
from django.conf import settings
from rest_framework.permissions import BasePermission

from lib.datadog.tools import is_datadog_synthetics_test
from lib.feature_flag.feature.customer import HCaptchaFailedLogFlag
from lib.feature_flag.feature.security import (
    DisableCaptchaForAccountExistEndpointFlag,
    HCaptchaAndroidFlag,
    HCaptchaCustomerEmailChangeFlag,
    HCaptchaFlag,
    HCaptchaiOSFlag,
    HCaptchaMaxScore,
)
from lib.tools import quick_assert, l_b
from service.mixins.throttling import THROTTLE_WHITELIST
from webapps import consts

logger = logging.getLogger('booksy.hcaptcha_customer')


def get_hcaptcha_protected_endpoints():
    endpoints = [
        {'path': '/customer_api/account/login', 'methods': ['POST'], 'key': 'customer_login'},
        {
            'path': '/customer_api/account/password_reset',
            'methods': ['POST'],
            'key': 'customer_password_reset',
        },
        {
            'path': '/customer_api/me/change_email',
            'methods': ['POST'],
            'key': 'customer_change_email',
        },
        {'path': '/customer_api/account/sms_code', 'methods': ['POST'], 'key': 'customer_sms_code'},
        {
            'path': '/customer_api/me/family_and_friends/members',
            'methods': ['POST'],
            'key': 'customer_family_and_friends_member_invitation',
        },
        {'path': '/customer_api/contact_us', 'methods': ['POST'], 'key': 'customer_contact'},
        {
            'path': '/customer_api/businesses/contact_info/',
            'methods': ['POST'],
            'key': 'business_contact_info',
        },
        {'path': '/customer_api/sms_invite', 'methods': ['POST'], 'key': 'customer_sms_invite'},
    ]
    if not DisableCaptchaForAccountExistEndpointFlag():
        endpoints.append(
            {
                'path': '/customer_api/account/exists',
                'methods': ['GET'],
                'key': 'customer_account_exists',
            }
        )

    if HCaptchaCustomerEmailChangeFlag():
        endpoints.append(
            {
                'path': '/customer_api/me/email_change/',
                'methods': ['POST', 'PUT'],
                'key': 'customer_email_change',
            },
        )

    return endpoints


class HCaptchaRequestValidator:
    SERVICE_NAME = 'HCaptcha'

    def __init__(self, booking_source_name, request):
        self.request = request
        self.headers = request.headers
        self.booking_source_name = booking_source_name
        self.hcaptcha_token = self.headers.get('x-hcaptcha-token')

    def request_ip(self):
        return self.headers.get('X-Real-IP')

    def endpoint_chosen_for_validation(self):
        return any(
            endpoint['path'] in self.request.path and self.request.method in endpoint['methods']
            for endpoint in get_hcaptcha_protected_endpoints()
        )

    def test_env_bypass(self):
        return (
            not settings.LIVE_DEPLOYMENT
            and self.hcaptcha_token == '20000000-aaaa-bbbb-cccc-000000000002'
        )

    def is_enabled_for_mobile(self):
        return (self.booking_source_name == consts.ANDROID and HCaptchaAndroidFlag()) or (
            self.booking_source_name == consts.IPHONE and HCaptchaiOSFlag()
        )

    def is_web_booking_source(self):
        return self.booking_source_name in (
            consts.WEB,
            consts.WIDGET,
            consts.INSTAGRAM,
            consts.INSTAGRAM_STAFFER,
            consts.FACEBOOK,
        )

    def _log_captcha_validation_failed(self) -> None:
        data = self._get_request_data()
        logger.warning(
            msg='hCaptcha - validation failed',
            extra={
                'path': self.request.path.split('?', 1)[0].rstrip('/'),
                'country_code': settings.API_COUNTRY,
                'source': self.booking_source_name,
                'user_id': self.request.user and self.request.user.id,
                'email': data.get('email'),
                'phone': data.get('cell_phone'),
                'user_agent': self.headers.get('user-agent'),
                'hcaptcha_token': self.hcaptcha_token,
            },
        )

    def _get_request_data(self) -> dict:
        request = self.request
        if data := getattr(request, 'data', {}):  # DRF POST
            return data

        if body := getattr(request, 'body', None):  # Tornado POST
            with suppress(Exception):
                return json.loads(l_b(body))

        # DRF GET or Tornado GET
        if args := (getattr(request, 'query_params', None) or getattr(request, 'arguments', None)):
            return {k: l_b(v[0]) for k, v in args.items()}

        return {}

    def validate(self):
        if (
            not HCaptchaFlag()
            or self.test_env_bypass()
            or is_datadog_synthetics_test(self.headers)
            or self.request_ip() in THROTTLE_WHITELIST
        ):
            return

        if (
            self.is_web_booking_source() or self.is_enabled_for_mobile()
        ) and self.endpoint_chosen_for_validation():
            form_data = {
                'secret': settings.HCAPTCHA_SECRET_KEY,
                'response': self.hcaptcha_token,
                'remoteip': self.request_ip(),
            }
            response = requests.post(
                'https://api.hcaptcha.com/siteverify', data=form_data, timeout=5
            ).json()

            captcha_verified = (
                response.get('success', False) and response.get('score', 0) < HCaptchaMaxScore()
            )
            if not captcha_verified and HCaptchaFailedLogFlag():
                self._log_captcha_validation_failed()

            quick_assert(
                captcha_verified,
                ('invalid', 'validation', None),
                'Request malformed - Hcaptcha',
            )


class IsHCaptchaValidated(BasePermission):
    def has_permission(self, request, view):
        action = view.action
        booking_source_name = view.booking_source.name

        if (
            request.headers.get('x-hcaptcha-token')
            and HCaptchaFlag()
            and (
                hcaptcha_enabled_actions := view.HCAPTCHA_ENABLED_ACTIONS  # pylint: disable=line-too-long
            )
            and action in hcaptcha_enabled_actions
        ):
            HCaptchaRequestValidator(
                booking_source_name=booking_source_name,
                request=request,
            ).validate()

        return True
