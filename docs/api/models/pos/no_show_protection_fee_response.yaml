id: NoShowProtectionFeeResponse
description: 
    Get Service No show protection payments
properties:
    now_show_cancel_time_option:
        description: Current setting of cancelation
        type: CancelTimeOption
        enum_from_const: pos.POS.POS_CANCEL_TIME_OPTIONS
    now_show_protection_policy:
        description: Protection policy text
        type: string
    auto_charge_cancellation_fee:
        description: If enabled opened transactions with CF will be closed automatically
        type: boolean
    now_show_cancel_time_options:
        description: possible options Cancellation Fee time
        type: array
        items:
            type: CancelTimeOption
        enum_from_const: pos.POS.POS_CANCEL_TIME_OPTIONS
    is_first_run:
        description: No services with no_shwo_protection payment exists ( instead of two fields is_first_run_pp, is_first_run_cf)
        type: boolean
    minimal_pay_by_app_payment:
        description: Field to validate minimal payment that can be done trough adyen system
        type: FormattedPrice
    max_pay_by_app_payment:
        description: Field to validate maximum payment that can be done trough mobile payments system
        type: FormattedPrice
    service_categories:
        description: Name of Category and assigned services_variants to this category
        type: array
        items:
            type: ServiceCategoryWithPayment
