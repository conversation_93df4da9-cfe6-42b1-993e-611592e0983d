"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
import google.protobuf.descriptor
import google.protobuf.message
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class PaymentProcessEvent(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    @typing.final
    class PaymentMethod(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        TYPE_FIELD_NUMBER: builtins.int
        TOKEN_FIELD_NUMBER: builtins.int
        TOKENIZED_PM_ID_FIELD_NUMBER: builtins.int
        type: builtins.str
        token: builtins.str
        tokenized_pm_id: builtins.str
        def __init__(
            self,
            *,
            type: builtins.str = ...,
            token: builtins.str = ...,
            tokenized_pm_id: builtins.str = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["payment_details", b"payment_details", "token", b"token", "tokenized_pm_id", b"tokenized_pm_id"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["payment_details", b"payment_details", "token", b"token", "tokenized_pm_id", b"tokenized_pm_id", "type", b"type"]) -> None: ...
        def WhichOneof(self, oneof_group: typing.Literal["payment_details", b"payment_details"]) -> typing.Literal["token", "tokenized_pm_id"] | None: ...

    @typing.final
    class Tip(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        TYPE_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        type: builtins.str
        value: builtins.int
        def __init__(
            self,
            *,
            type: builtins.str = ...,
            value: builtins.int = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing.Literal["type", b"type", "value", b"value"]) -> None: ...

    @typing.final
    class PaymentProcessEventData(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        BASKET_ID_FIELD_NUMBER: builtins.int
        PAYMENT_METHOD_FIELD_NUMBER: builtins.int
        TIP_FIELD_NUMBER: builtins.int
        basket_id: builtins.str
        @property
        def payment_method(self) -> global___PaymentProcessEvent.PaymentMethod: ...
        @property
        def tip(self) -> global___PaymentProcessEvent.Tip: ...
        def __init__(
            self,
            *,
            basket_id: builtins.str = ...,
            payment_method: global___PaymentProcessEvent.PaymentMethod | None = ...,
            tip: global___PaymentProcessEvent.Tip | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["payment_method", b"payment_method", "tip", b"tip"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["basket_id", b"basket_id", "payment_method", b"payment_method", "tip", b"tip"]) -> None: ...

    EVENT_ID_FIELD_NUMBER: builtins.int
    EVENT_TYPE_FIELD_NUMBER: builtins.int
    EVENT_VERSION_FIELD_NUMBER: builtins.int
    CREATED_FIELD_NUMBER: builtins.int
    DATA_FIELD_NUMBER: builtins.int
    event_id: builtins.str
    event_type: builtins.str
    event_version: builtins.str
    created: builtins.int
    @property
    def data(self) -> global___PaymentProcessEvent.PaymentProcessEventData: ...
    def __init__(
        self,
        *,
        event_id: builtins.str = ...,
        event_type: builtins.str = ...,
        event_version: builtins.str = ...,
        created: builtins.int = ...,
        data: global___PaymentProcessEvent.PaymentProcessEventData | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["data", b"data"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["created", b"created", "data", b"data", "event_id", b"event_id", "event_type", b"event_type", "event_version", b"event_version"]) -> None: ...

global___PaymentProcessEvent = PaymentProcessEvent
