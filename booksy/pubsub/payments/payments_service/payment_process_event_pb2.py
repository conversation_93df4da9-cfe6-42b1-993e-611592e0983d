# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: booksy/pubsub/payments/payments_service/payment_process_event.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nCbooksy/pubsub/payments/payments_service/payment_process_event.proto\x12\x1apayments_service.pubsub.v1\"\x84\x04\n\x13PaymentProcessEvent\x12\x10\n\x08\x65vent_id\x18\x01 \x01(\t\x12\x12\n\nevent_type\x18\x02 \x01(\t\x12\x15\n\revent_version\x18\x03 \x01(\t\x12\x0f\n\x07\x63reated\x18\x04 \x01(\x03\x12U\n\x04\x64\x61ta\x18\x05 \x01(\x0b\x32G.payments_service.pubsub.v1.PaymentProcessEvent.PaymentProcessEventData\x1a\\\n\rPaymentMethod\x12\x0c\n\x04type\x18\x01 \x01(\t\x12\x0f\n\x05token\x18\x02 \x01(\tH\x00\x12\x19\n\x0ftokenized_pm_id\x18\x03 \x01(\tH\x00\x42\x11\n\x0fpayment_details\x1a\"\n\x03Tip\x12\x0c\n\x04type\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x03\x1a\xc5\x01\n\x17PaymentProcessEventData\x12\x11\n\tbasket_id\x18\x01 \x01(\t\x12U\n\x0epayment_method\x18\x02 \x01(\x0b\x32=.payments_service.pubsub.v1.PaymentProcessEvent.PaymentMethod\x12@\n\x03tip\x18\x03 \x01(\x0b\x32\x33.payments_service.pubsub.v1.PaymentProcessEvent.Tipb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'booksy.pubsub.payments.payments_service.payment_process_event_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_PAYMENTPROCESSEVENT']._serialized_start=100
  _globals['_PAYMENTPROCESSEVENT']._serialized_end=616
  _globals['_PAYMENTPROCESSEVENT_PAYMENTMETHOD']._serialized_start=288
  _globals['_PAYMENTPROCESSEVENT_PAYMENTMETHOD']._serialized_end=380
  _globals['_PAYMENTPROCESSEVENT_TIP']._serialized_start=382
  _globals['_PAYMENTPROCESSEVENT_TIP']._serialized_end=416
  _globals['_PAYMENTPROCESSEVENT_PAYMENTPROCESSEVENTDATA']._serialized_start=419
  _globals['_PAYMENTPROCESSEVENT_PAYMENTPROCESSEVENTDATA']._serialized_end=616
# @@protoc_insertion_point(module_scope)
