"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
import google.protobuf.descriptor
import google.protobuf.message
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class PaymentCanceledEvent(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    @typing.final
    class PaymentCanceledEventData(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        BASKET_ID_FIELD_NUMBER: builtins.int
        BASKET_PAYMENT_ID_FIELD_NUMBER: builtins.int
        BALANCE_TRANSACTION_ID_FIELD_NUMBER: builtins.int
        BUSINESS_ID_FIELD_NUMBER: builtins.int
        USER_ID_FIELD_NUMBER: builtins.int
        PAYMENT_METHOD_FIELD_NUMBER: builtins.int
        SOURCE_FIELD_NUMBER: builtins.int
        STATUS_FIELD_NUMBER: builtins.int
        CREATED_FIELD_NUMBER: builtins.int
        basket_id: builtins.str
        basket_payment_id: builtins.str
        balance_transaction_id: builtins.str
        business_id: builtins.int
        user_id: builtins.int
        payment_method: builtins.str
        source: builtins.str
        status: builtins.str
        created: builtins.int
        def __init__(
            self,
            *,
            basket_id: builtins.str = ...,
            basket_payment_id: builtins.str = ...,
            balance_transaction_id: builtins.str = ...,
            business_id: builtins.int = ...,
            user_id: builtins.int = ...,
            payment_method: builtins.str = ...,
            source: builtins.str = ...,
            status: builtins.str = ...,
            created: builtins.int = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing.Literal["balance_transaction_id", b"balance_transaction_id", "basket_id", b"basket_id", "basket_payment_id", b"basket_payment_id", "business_id", b"business_id", "created", b"created", "payment_method", b"payment_method", "source", b"source", "status", b"status", "user_id", b"user_id"]) -> None: ...

    EVENT_ID_FIELD_NUMBER: builtins.int
    EVENT_TYPE_FIELD_NUMBER: builtins.int
    EVENT_VERSION_FIELD_NUMBER: builtins.int
    CREATED_FIELD_NUMBER: builtins.int
    DATA_FIELD_NUMBER: builtins.int
    event_id: builtins.str
    event_type: builtins.str
    event_version: builtins.str
    created: builtins.int
    @property
    def data(self) -> global___PaymentCanceledEvent.PaymentCanceledEventData: ...
    def __init__(
        self,
        *,
        event_id: builtins.str = ...,
        event_type: builtins.str = ...,
        event_version: builtins.str = ...,
        created: builtins.int = ...,
        data: global___PaymentCanceledEvent.PaymentCanceledEventData | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["data", b"data"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["created", b"created", "data", b"data", "event_id", b"event_id", "event_type", b"event_type", "event_version", b"event_version"]) -> None: ...

global___PaymentCanceledEvent = PaymentCanceledEvent
