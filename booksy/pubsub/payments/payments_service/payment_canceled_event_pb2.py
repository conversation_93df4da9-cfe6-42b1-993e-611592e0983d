# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: booksy/pubsub/payments/payments_service/payment_canceled_event.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\nDbooksy/pubsub/payments/payments_service/payment_canceled_event.proto\x12\x1apayments_service.pubsub.v1\"\x97\x03\n\x14PaymentCanceledEvent\x12\x10\n\x08\x65vent_id\x18\x01 \x01(\t\x12\x12\n\nevent_type\x18\x02 \x01(\t\x12\x15\n\revent_version\x18\x03 \x01(\t\x12\x0f\n\x07\x63reated\x18\x04 \x01(\x03\x12W\n\x04\x64\x61ta\x18\x05 \x01(\x0b\x32I.payments_service.pubsub.v1.PaymentCanceledEvent.PaymentCanceledEventData\x1a\xd7\x01\n\x18PaymentCanceledEventData\x12\x11\n\tbasket_id\x18\x01 \x01(\t\x12\x19\n\x11\x62\x61sket_payment_id\x18\x02 \x01(\t\x12\x1e\n\x16\x62\x61lance_transaction_id\x18\x03 \x01(\t\x12\x13\n\x0b\x62usiness_id\x18\x04 \x01(\x03\x12\x0f\n\x07user_id\x18\x05 \x01(\x03\x12\x16\n\x0epayment_method\x18\x06 \x01(\t\x12\x0e\n\x06source\x18\x07 \x01(\t\x12\x0e\n\x06status\x18\x08 \x01(\t\x12\x0f\n\x07\x63reated\x18\t \x01(\x03\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'booksy.pubsub.payments.payments_service.payment_canceled_event_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_PAYMENTCANCELEDEVENT']._serialized_start=101
  _globals['_PAYMENTCANCELEDEVENT']._serialized_end=508
  _globals['_PAYMENTCANCELEDEVENT_PAYMENTCANCELEDEVENTDATA']._serialized_start=293
  _globals['_PAYMENTCANCELEDEVENT_PAYMENTCANCELEDEVENTDATA']._serialized_end=508
# @@protoc_insertion_point(module_scope)
