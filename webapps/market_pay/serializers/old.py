import re
from collections import defaultdict
from typing import Dict

from django.conf import settings
from django.db import transaction
from django.utils.encoding import force_str
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from django.utils.translation import (
    gettext,
    gettext_lazy as _,
    gettext_noop,
)
from rest_framework import serializers
from rest_framework.fields import get_attribute, set_value

from country_config.enums import Country
from lib.booksy_sms import parse_phone_number
from lib.enums import BaseEnum
from lib.fields.phone_number import BooksyPhoneSerializerField
from lib.serializers import (
    MergeSourceSerializer,
    MultiModeSerializer,
    RepresentationField,
    SingleItemListSerializer,
    account_number_validator,
    any_zip_code_validator,
    employer_identification_number_validator,
    es_employer_identification_number_validator,
    gb_employer_identification_number_validator,
    gb_iban_validator,
    name_validator,
    pesel_number_validator,
    pl_employer_identification_number_validator,
    pl_iban_validator,
    routing_number_validator,
    safe_get,
    social_security_number_validator,
    sort_code_validator,
    zip_code_validator,
)
from service.tools import ServiceError
from webapps.business.models import Business
from webapps.market_pay import enums, requests
from webapps.market_pay.helpers import (
    generate_account_holder_code,
    indexed_dict_to_list,
    source_map_gen,
    get_error_description_by_code,
)
from webapps.market_pay.models import AccountHolder, Payout
from webapps.market_pay.requests import get_account_holder
from webapps.market_pay.signatory_provider import SignatureProvider
from webapps.pos.serializer_fields import PriceField


def get_validators(validators_map):
    validator = validators_map.get(settings.API_COUNTRY)
    return [validator] if validator else None


BRANCH_CODE_VALIDATORS = {
    'us': routing_number_validator,
    'gb': sort_code_validator,
}


ID_NUMBER_VALIDATORS = {
    'us': social_security_number_validator,
    'pl': pesel_number_validator,
}


REGISTRATION_NUMBER_VALIDATORS = {
    'us': employer_identification_number_validator,
    'gb': gb_employer_identification_number_validator,
    'pl': pl_employer_identification_number_validator,
    'es': es_employer_identification_number_validator,
}

IBAN_VALIDATORS = {
    'gb': gb_iban_validator,
    'pl': pl_iban_validator,
}

US_ADDRESS_SPLIT_RE = re.compile(r'^(\d+\w*)? ?(.*)$')
PL_ADDRESS_SPLIT_RE = re.compile(r'^(.*) (\d+[\w\/]*)$')


def any_zip_code(value: str) -> str:
    """
    Validates zip code using AVS zip code regex.

    :raises serializers.ValidationError:
    """
    if not re.match(settings.AVS_ZIPCODE_REGEXP, value):
        raise serializers.ValidationError(_('Invalid zip code'))
    return value


def us_address_splitter(address):
    return US_ADDRESS_SPLIT_RE.match(address).groups()


def pl_address_splitter(address):
    match = PL_ADDRESS_SPLIT_RE.match(address)
    return match.groups()[::-1] if match else (None, address)


def noop_address_splitter(address):
    return (None, address)


ADDRESS_SPLITTERS = {
    'us': us_address_splitter,
    'pl': pl_address_splitter,
    'default': noop_address_splitter,
}


def get_address_splitter():
    return ADDRESS_SPLITTERS.get(
        settings.API_COUNTRY,
        ADDRESS_SPLITTERS['default'],
    )


STATUS_COLOR_CHOICES = {
    enums.AccountHolderStatus.ACTIVE.value: 'green',
    enums.AccountHolderStatus.INACTIVE.value: 'red',
    enums.AccountHolderStatus.SUSPENDED.value: 'red',
    enums.AccountHolderStatus.CLOSED.value: 'black',
}


class BusinessDataMixin:
    def data_from_business(self, business):
        """Call data_from_business from all serializer fields"""
        return {
            fieldname: field.data_from_business(business)
            for fieldname, field in self.fields.items()
            if hasattr(field, 'data_from_business')
        }


class FilterFieldsMixin:
    def get_fields(self):
        fields = super().get_fields()
        exclude = getattr(self.Meta, 'exclude_by_country', {})
        for name, countries in exclude.items():
            if settings.API_COUNTRY in countries:
                del fields[name]

        include = getattr(self.Meta, 'include_by_country', {})
        for name, countries in include.items():
            if settings.API_COUNTRY not in countries:
                del fields[name]

        required = getattr(self.Meta, 'required_by_country', {})
        for name, countries in required.items():
            if settings.API_COUNTRY in countries:
                fields[name].required = True

        return fields


class ZipCodeField(serializers.CharField):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        if settings.API_COUNTRY in [Country.US, Country.CA]:
            self.max_length = 5
        else:
            self.max_length = 10


class StateOrProvinceField(serializers.CharField):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        if settings.API_COUNTRY in [Country.US, Country.CA]:
            self.max_length = 2
            self.min_length = 2
        else:
            self.max_length = 3


class AddressSerializer(FilterFieldsMixin, serializers.Serializer):
    class Meta:
        include_by_country = {'state': ['us', 'ca']}
        required_by_country = {'state': ['us', 'ca']}

    street = serializers.CharField(
        max_length=100,
        label=_('Street'),
    )
    house_number = serializers.CharField(
        source='houseNumberOrName',
        max_length=100,
        label=_('House Number / Name'),
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    city = serializers.CharField(
        max_length=100,
        label=_('City'),
        validators=[name_validator],
    )
    zipcode = ZipCodeField(
        source='postalCode',
        label=defaultdict(
            us=_('Zip Code'),
            gb='Postal Code',
            pl=_('Zip Code'),
            es=_('Zip Code'),
        ),
        validators=[zip_code_validator],
    )
    state = StateOrProvinceField(
        source='stateOrProvince',
        label=_('State'),
    )

    def data_from_business(self, business):
        splitter = get_address_splitter()
        house, street = splitter(business.address or '')
        house = ' '.join(filter(None, ['', house, business.address2]))

        return {
            'street': street,
            'house_number': house,
            'city': business.city_or_region_city,
            'zipcode': business.zip,
            'state': business.region.get_parent_name_by_type(
                settings.ES_ADM_1_LVL,
                'abbrev',
            ),
            'country': settings.API_COUNTRY,
        }


class BankAccountOwnerSerializer(FilterFieldsMixin, serializers.Serializer):
    class Meta:
        include_by_country = {'state': ['us', 'ca']}
        required_by_country = {'state': ['us', 'ca']}

    name = serializers.CharField(
        source='ownerName',
        label=_('Legal Business Name'),
    )
    street = serializers.CharField(
        source='ownerStreet',
        max_length=100,
        label=_('Street'),
    )
    house_number = serializers.CharField(
        source='ownerHouseNumberOrName',
        max_length=100,
        label=_('House Number / Name'),
        allow_blank=True,
        allow_null=True,
        required=False,
    )
    city = serializers.CharField(
        source='ownerCity',
        max_length=100,
        label=_('City'),
    )
    zipcode = serializers.CharField(
        source='ownerPostalCode',
        max_length=10,
        label=_('Zip Code'),
        validators=[any_zip_code_validator],
    )
    state = serializers.CharField(
        source='ownerState',
        max_length=3,
        label=_('State'),
    )
    country = serializers.SerializerMethodField()

    def get_country(self, _instance):
        return settings.API_COUNTRY.upper()

    def data_from_request(self, data):
        individual = data.get('individual')
        business = data.get('business')
        if not (individual or business):
            return {}

        address = (
            safe_get(
                (individual or business),
                ['contact_details', 'address'],
            )
            or {}
        )
        if individual:
            name = ' '.join(
                [
                    safe_get(individual, ['personal_details', 'first_name']),
                    safe_get(individual, ['personal_details', 'last_name']),
                ]
            )
        else:
            name = safe_get(business, ['business_details', 'legal_name'])

        return {
            'ownerName': name,
            'ownerStreet': address.get('street'),
            'ownerHouseNumberOrName': address.get('house_number'),
            'ownerCity': address.get('city'),
            'ownerPostalCode': address.get('zipcode'),
            'ownerState': address.get('state'),
            'ownerCountryCode': self.get_country(data),
        }

    def validate(self, attrs):
        attrs['ownerCountryCode'] = self.get_country(None)
        return attrs


class BankAccountSerializer(
    FilterFieldsMixin,
    SingleItemListSerializer,
    MergeSourceSerializer,
):
    class Meta:
        include_by_country = {
            'account_type': ['us'],
            'account_number': ['us'],
            'branch_code': ['us'],
            'iban': ['gb', 'pl', 'es'],
        }

    account_number = serializers.CharField(
        source='accountNumber',
        label=_('Account Number'),
        validators=(account_number_validator,),
    )
    account_type = serializers.CharField(
        source='accountType',
        label=_('Account Type'),
        # write_only=True, #  adyen doesnt return this field...
    )
    branch_code = serializers.CharField(
        source='branchCode',
        label={
            'us': 'Routing Number',
            'gb': 'Sort code',
            'de': 'Bankleitzahl',
        },
        validators=get_validators(BRANCH_CODE_VALIDATORS),
    )
    iban = serializers.CharField(
        label='IBAN',
        validators=get_validators(IBAN_VALIDATORS),
    )

    im_owner = serializers.BooleanField(write_only=True)
    owner = BankAccountOwnerSerializer(source='*')

    def data_from_business(self, business):
        if not business.pos.has_bank_account:
            return None

        bank_account = business.pos.bank_account
        return {
            'account_number': bank_account.account_number,
            'account_type': bank_account.type,
            'branch_dode': bank_account.routing_number,
        }

    def run_validation(self, data=serializers.empty):
        if data and data is not serializers.empty:
            im_owner = data.get('im_owner')
            if im_owner:
                self.fields['owner'].read_only = True
                self.fields['owner'].required = False
        return super().run_validation(data)

    def validate(self, attrs):
        # ensure always updating existing bank account
        bank_account_uuid = safe_get(
            self.context['business'].pos.account_holder,
            ['data', 'bank_account_uuid'],
        )
        if bank_account_uuid:
            attrs['bankAccountUUID'] = bank_account_uuid

        if attrs.pop('im_owner', None):
            attrs.update(self.fields['owner'].data_from_request(self.root.initial_data))

        attrs['countryCode'] = settings.API_COUNTRY.upper()
        attrs['currencyCode'] = settings.CURRENCY_CODE
        return SingleItemListSerializer.validate(self, attrs)

    def to_representation(self, instance):
        if instance:
            # adyen sometimes doesnt return this field...
            if 'accountType' not in instance[0]:
                instance[0]['accountType'] = ''
            # ...or return in uppercase...
            instance[0]['accountType'] = instance[0]['accountType'].lower()
        if instance and len(instance) != 1:
            instance = [instance[0]]
        return super().to_representation(instance)


class MarketPayBooksyPhoneSerializerField(BooksyPhoneSerializerField):
    """Phone Field for Account Holder

    Adyen returns phone in {phoneNumber: {phoneCountryCode, phoneNumber}}
    format instead of fullPhoneNumber
    """

    def get_attribute(self, instance: dict) -> str:
        """
        Parses and formats phone number sent via Adyen response.
        """
        result = safe_get(instance, self.source_attrs)
        if result is not None:
            return result

        phone = safe_get(instance, ['phoneNumber', 'phoneNumber'])
        country = safe_get(instance, ['phoneNumber', 'phoneCountryCode'])
        parsed = parse_phone_number(phone, country)
        return getattr(parsed, self.read_format, phone)


class ContactSerializer(serializers.Serializer):
    email = serializers.EmailField(
        required=False,  # can be set True in init
        label=_('Email address'),
    )
    phone = MarketPayBooksyPhoneSerializerField(
        source='fullPhoneNumber',
        write_format='global_nice',
        read_format='global_nice',
        required=False,
        label=_('Phone Number'),
    )
    # allow omit address field; but require all fields in address
    address = AddressSerializer(
        required=False,
        allow_null=True,
    )
    web_site = serializers.CharField(
        source='webAddress',
        label=_('Web site'),
        required=False,
    )

    def __init__(self, instance=None, data=serializers.empty, email_required=False, **kwargs):
        super().__init__(instance, data, **kwargs)
        self.email_required = email_required

    def get_fields(self):
        fields = super().get_fields()
        if self.email_required:
            fields['email'].required = True
        return fields

    def validate(self, attrs):
        # always return country
        if 'address' not in attrs:
            attrs['address'] = {}

        if (
            settings.API_COUNTRY == Country.US
            and attrs['address']
            .get(
                'stateOrProvince',
                '',
            )
            .upper()
            == 'PR'
        ):
            attrs['address']['country'] = 'PR'
        else:
            attrs['address']['country'] = settings.API_COUNTRY

        return attrs

    def to_representation(self, instance):
        # if no specific address data, remove address field
        # to escape address serializer error
        if 'address' in instance and 'street' not in instance['address']:
            del instance['address']
        return super().to_representation(instance)

    def data_from_business(self, business):
        return {
            'email': business.owner.email,
            'phone': business.owner.cell_phone or business.phone,
            'address': self.fields['address'].data_from_business(business),
            'web_site': business.get_seo_url(),
        }


class PersonSerializer(FilterFieldsMixin, MergeSourceSerializer):
    class Meta:
        include_by_country = {
            'id_number': ['us', 'it', 'pl'],
        }

    first_name = serializers.CharField(
        source='name.firstName',
        label=_('First Name'),
        validators=[name_validator],
        required=False,
    )
    last_name = serializers.CharField(
        source='name.lastName',
        label=_('Last Name'),
        validators=[name_validator],
        required=False,
    )
    gender = serializers.ChoiceField(
        source='name.gender',
        choices=enums.Gender.choices(),
        default=enums.Gender.UNKNOWN.value,
        label=_('Gender'),
        required=False,
    )
    birthday = serializers.CharField(
        source='personalData.dateOfBirth',
        label=_('Date of birth'),
        required=False,
    )
    id_number = serializers.CharField(
        source='personalData.idNumber',
        label={
            'us': 'Social Security number',
            'ca': 'SIN',
            'it': 'Codice fiscale',
            'pl': 'PESEL',
        },
        validators=get_validators(ID_NUMBER_VALIDATORS),
        required=False,
    )

    def get_fields(self):
        fields = super().get_fields()

        tier = self.context.get('tier', 0)
        if tier == 0:
            for field_name, field in fields.items():
                if field_name in ['birthday', 'id_number']:
                    field.required = False

        return fields

    def data_from_business(self, business):
        return {
            'first_name': business.owner.first_name,
            'last_name': business.owner.last_name,
            'gender': enums.USER_GENDER_MAP[business.owner.gender].value,
            'birthday': (
                business.owner.birthday.strftime(
                    settings.DATE_FORMAT,
                )
                if business.owner.birthday
                else None
            ),
            'id_number': '',
        }

    def validate_birthday(self, value):
        # validate date; return string
        serializers.DateField().to_internal_value(value)
        return value

    def validate(self, attrs):
        if 'personalData' in attrs:
            id_number = attrs['personalData'].pop('idNumber', None)
            self._set_id_number(id_number, attrs)
        return attrs

    def to_representation(self, instance):
        result = super().to_representation(instance)
        result['id_number'] = self._get_id_number(instance)
        return result

    def _set_id_number(self, id_number, attrs):
        id_doc = {
            'number': id_number,
            'type': 'ID',
        }
        if 'documentData' not in attrs['personalData']:
            attrs['personalData']['documentData'] = []
        if id_number is None:
            return
        if not attrs['personalData']['documentData']:
            attrs['personalData']['documentData'] = [id_doc]
        else:
            for doc in attrs['personalData']['documentData']:
                if doc['type'] == 'ID':
                    doc['number'] = id_number
                return
            attrs['personalData']['documentData'].append(id_doc)

    def _get_id_number(self, instance):
        document_data = safe_get(instance, ['personalData', 'documentData'])
        if not document_data:
            return None
        for doc in document_data:
            if doc['type'] == 'ID':
                return doc['number']
        return None


class ShareholderSortListSerializer(serializers.ListSerializer):
    def to_representation(self, data):
        items = super().to_representation(data)
        sorted(items, key=lambda x: x['shareholder_code'])
        return items


class ShareholderSerializer(MergeSourceSerializer, BusinessDataMixin):
    class Meta:
        list_serializer_class = ShareholderSortListSerializer

    shareholder_code = serializers.CharField(
        source='shareholderCode',
        required=False,
        allow_null=True,
    )
    personal_details = PersonSerializer(
        source='*',
        required=False,
        allow_null=True,
    )
    contact_details = ContactSerializer(
        source='*',
        required=False,
        allow_null=True,
        email_required=False,
    )
    job_title = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    shareholder_type = serializers.CharField(required=False, allow_null=True, allow_blank=True)

    def validate(self, attrs):
        # always return country
        if 'address' not in attrs:
            attrs['address'] = {}
        attrs['address']['country'] = settings.API_COUNTRY
        return attrs


class BusinessDetailsSerializer(FilterFieldsMixin, serializers.Serializer):
    class Meta:
        include_by_country = {
            'tax_id': ['us'],
        }
        labels = {
            'registration_number': {
                'at': 'Firmenbuchnummer',
                'be': 'Company Number/Numéro d\'entreprise',
                'cz': 'IČO',
                'fi': 'Y-TUNNUS',
                'fr': 'SIRET number',
                'de': 'Handelsregisternummer',
                'ie': 'Company Number',
                'it': 'Codice fiscale',
                'nl': 'KvK',
                'pl': 'KRS',
                'pt': 'NIPC',
                'es': 'NIF',
                'ch': 'UID/IDE/IDI',
                'gb': 'Company Number',
                'us': 'Tax ID (Employer Identification Number)',
            },
        }

    name = serializers.CharField(
        source='doingBusinessAs',
        label=_('Doing Business As'),
        required=False,
    )
    legal_name = serializers.CharField(
        source='legalBusinessName',
        label=_('Legal Business Name'),
        required=False,
    )
    tax_id = serializers.CharField(
        source='taxId',
        required=False,
        label=_('Tax ID'),
    )
    registration_number = serializers.CharField(
        source='registrationNumber',
        label={
            'at': 'Firmenbuchnummer',
            'be': 'Company Number/Numéro d\'entreprise',
            'cz': 'IČO',
            'fi': 'Y-TUNNUS',
            'fr': 'SIRET number',
            'de': 'Handelsregisternummer',
            'ie': 'Company Number',
            'it': 'Codice fiscale',
            'nl': 'KvK',
            'pl': 'KRS lub REGON',
            'pt': 'NIPC',
            'es': 'Número de Identificación Fiscal (NIF)',
            'ch': 'UID/IDE/IDI',
            'gb': 'Company Number',
            'us': 'Tax ID (EIN)',
        },
        validators=get_validators(REGISTRATION_NUMBER_VALIDATORS),
        required=False,
    )

    signatories = serializers.SerializerMethodField(
        required=False,
        allow_null=True,
    )

    def get_signatories(self, _instance):
        return []

    def data_from_business(self, business):
        return {
            'name': business.name,
            'legal_name': business.name,
            'tax_id': '',
            'registration_number': '',
        }


class VerificationItemSerializer(serializers.Serializer):
    def to_representation(self, instance):
        shareholder_code = instance.get('shareholderCode')

        res = list(
            filter(
                None,
                [
                    self.get_verification_error(check, shareholder_code)
                    for check in instance['checks']
                ],
            )
        )
        return res

    def get_verification_error(self, check, shareholder_code=None):
        fields = check.get('requiredFields')
        code = safe_get(check, ['summary', 'kycCheckCode'])

        if fields:
            fields = list(
                filter(
                    None, [self.map_verification_field(field, shareholder_code) for field in fields]
                )
            )
        else:
            mapping = self.map_verification_field(
                f'verification_type.{check["type"]}', shareholder_code
            )
            if (
                check['type'] == enums.VerificationType.BANK_ACCOUNT_VERIFICATION.value
                and code in enums.BANK_ACCOUNT_DOCUMENT_CODES
            ):
                fields = [mapping, mapping + ['document']]
            else:
                fields = [mapping]

        description = get_error_description_by_code(code)

        if description:
            description = force_str(description)
        else:
            description = safe_get(check, ['summary', 'kycCheckDescription'])

        # special case for document status
        if check['status'] not in enums.VERIFICATION_ERROR_STATUSES:
            if fields[0][-1] == 'document':
                fields[0][-1] = 'document_status'
                description = description or force_str(
                    enums.VERIFICATION_STATUS_MESSAGES.get(check['status'])
                )
                return fields, description

        # all other fields: limit to error statuses
        if check['status'] not in enums.VERIFICATION_ERROR_STATUSES:
            return None

        description = description or gettext('This field is required.')

        for field in fields:
            # list index
            if field[-1].isnumeric():
                field[-1] = int(field[-1])
                field.append('non_field_error')
            # name
            else:
                field[-1] = f'{field[-1]}_error'

        return fields, description

    def map_verification_field(self, field, shareholder_code):
        mapping = self.root.source_map.get(field.lower()) or []
        mapping = mapping[:]
        if shareholder_code and '[shareholders_id]' in mapping:
            shareholders = safe_get(
                self.root.instance,
                ['accountHolderDetails', 'businessDetails', 'shareholders'],
            )
            for i, holder in enumerate(shareholders):
                if holder['shareholderCode'] == shareholder_code:
                    shareholder_id = str(i)
                    break
            else:
                shareholder_id = None

            mapping[mapping.index('[shareholders_id]')] = shareholder_id

        if not mapping:
            mapping = [field]

        return mapping


class VerificationSerializer(serializers.Serializer):
    accountHolder = VerificationItemSerializer(
        read_only=True,
    )
    bankAccounts = VerificationItemSerializer(
        read_only=True,
        many=True,
    )
    shareholders = VerificationItemSerializer(
        read_only=True,
        many=True,
    )

    def to_representation(self, instance):
        data = super().to_representation(instance)

        # make all fields to list fields
        items = [
            data[name] if isinstance(field, serializers.ListSerializer) else [data[name]]
            for name, field in self.fields.items()
            if name in data
        ]
        # flatten to single list
        flatten_fields = [field for item in items for verifs in item for field in verifs]
        ret = {}
        for fields, description in flatten_fields:
            for field in fields:
                set_value(ret, field, [description])

        shareholders = safe_get(ret, ['business', 'shareholders'])
        if shareholders:
            ret['business']['shareholders'] = indexed_dict_to_list(
                shareholders, length=len(instance.get('shareholders', []))
            )

        return ret


class StatusField(serializers.ChoiceField):
    def __init__(self, **kwargs):
        self.status_enum = kwargs.pop('status_enum', None)
        super().__init__(**kwargs)

    def to_representation(self, value):
        status = value
        if not isinstance(value, BaseEnum):
            status = self.status_enum(value)
        return {
            'value': gettext(status.value),
            'color': self.choices.get(status),
        }


class SerializerPayoutStatus(BaseEnum):
    ALLOWED = gettext_noop('Allowed')
    NOT_ALLOWED = gettext_noop('Not Allowed')
    DISABLED = gettext_noop('Disabled')
    UNKNOWN = gettext_noop('Unknown')
    VERIFICATION_PENDING = gettext_noop('Verification Pending')


PAYOUT_COLOR_CHOICES = list(
    {
        SerializerPayoutStatus.ALLOWED: 'green',
        SerializerPayoutStatus.NOT_ALLOWED: 'red',
        SerializerPayoutStatus.DISABLED: 'orange',
        SerializerPayoutStatus.UNKNOWN: 'black',
        SerializerPayoutStatus.VERIFICATION_PENDING: 'orange',
    }.items()
)


class _VerificationPendingSerializerBase(serializers.Serializer):
    pending = serializers.SerializerMethodField()

    @staticmethod
    def get_pending(instance):
        for __, verifications in instance['verification'].items():
            if not isinstance(verifications, list):
                verifications = [verifications]
            for verif in verifications:
                for check in verif['checks']:
                    if check['status'] == enums.VerificationStatus.PENDING.value:
                        return True
        return False


class PayoutStatusSerializer(_VerificationPendingSerializerBase):
    """Select status displayed as payout status"""

    # fields used to calculate status
    payout_allowed = serializers.BooleanField(
        source='accountHolderStatus.payoutState.allowPayout',
        read_only=True,
    )
    payout_disabled = serializers.BooleanField(
        source='accountHolderStatus.payoutState.disabled',
        read_only=True,
    )

    # representation field
    status = StatusField(
        choices=PAYOUT_COLOR_CHOICES,
        status_enum=SerializerPayoutStatus,
        read_only=True,
    )

    def to_representation(self, instance):
        data = super().to_representation(instance)
        status = None

        if data['payout_disabled']:
            status = SerializerPayoutStatus.DISABLED
        elif 'payout_allowed' not in data:
            status = SerializerPayoutStatus.UNKNOWN
        elif data['payout_allowed']:
            status = SerializerPayoutStatus.ALLOWED
        else:
            if data['pending']:
                status = SerializerPayoutStatus.VERIFICATION_PENDING
            else:
                status = SerializerPayoutStatus.NOT_ALLOWED

        return self.fields['status'].to_representation(status)


class AccountStatusSerializer(_VerificationPendingSerializerBase):
    class AccountStatus(BaseEnum):
        VERIFICATION_PENDING = gettext_noop('Verification pending')
        ACTIVE = gettext_noop('Active')
        BLOCKED = gettext_noop('Blocked')

    payout_allowed = serializers.BooleanField(
        source='accountHolderStatus.payoutState.allowPayout',
        read_only=True,
    )
    account_status = serializers.CharField(
        source='accountHolderStatus.status',
        read_only=True,
    )

    # representation field
    status = StatusField(
        choices=(
            (AccountStatus.VERIFICATION_PENDING, 'yellow'),
            (AccountStatus.ACTIVE, 'green'),
            (AccountStatus.BLOCKED, 'red'),
        ),
        status_enum=AccountStatus,
        read_only=True,
    )

    def to_representation(self, instance):
        data = super().to_representation(instance)
        if data['pending']:
            status = self.AccountStatus.VERIFICATION_PENDING
        elif data['account_status'] == 'Active' and data['payout_allowed']:
            status = self.AccountStatus.ACTIVE
        else:
            status = self.AccountStatus.BLOCKED
        return self.fields['status'].to_representation(status)


class BaseEntitySerializer(serializers.Serializer, BusinessDataMixin):
    mode = serializers.ChoiceField(
        source='legalEntity',
        choices=enums.LegalEntity.choices(),
    )
    status = StatusField(
        choices=list(STATUS_COLOR_CHOICES.items()),
        status_enum=enums.AccountHolderStatus,
        source='accountHolderStatus.status',
        read_only=True,
    )
    payout_status = PayoutStatusSerializer(
        source='*',
        read_only=True,
    )
    verification = VerificationSerializer(
        read_only=True,
    )
    dry_run = serializers.BooleanField(write_only=True)
    account_status = AccountStatusSerializer(
        source='*',
        read_only=True,
    )

    def validate(self, attrs):
        if 'accountHolderDetails' in attrs:
            if 'address' not in attrs['accountHolderDetails']:
                attrs['accountHolderDetails']['address'] = {'country': settings.API_COUNTRY.upper()}

        business = self.context.get('business')
        if not business:
            return attrs

        account_holder = self.context['business'].pos.account_holder
        if account_holder:
            attrs['accountHolderCode'] = account_holder.account_holder_code
        else:
            attrs['accountHolderCode'] = generate_account_holder_code(self.context['business'].id)

        if attrs.get('dry_run'):
            # HACK to prevent adyen from creating account
            attrs['primaryCurrency'] = 'USDR'
        else:
            attrs['primaryCurrency'] = settings.CURRENCY_CODE
        return attrs

    def to_representation(self, instance):
        try:
            data = super().to_representation(instance)
        # really first_run have no data
        except KeyError:
            data = {}

        if self.context.get('first_run'):
            business_data = self.data_from_business(self.context['business'])
            business_data.update(data)
            return business_data

        return data

    def labels(self, serializer=None):
        if serializer is None:
            fields = self.fields
        else:
            # get child if ListSerializer
            child = getattr(serializer, 'child', serializer)
            fields = child.fields

        def get_label(label):
            if isinstance(label, dict):
                return label.get(settings.API_COUNTRY)
            return label

        return {
            f.field_name: (
                self.labels(f)
                if isinstance(f, serializers.BaseSerializer)
                else force_str(get_label(f.label))
            )
            for f in fields.values()
            if not f.read_only
        }


class IndividualHolderSerializer(serializers.Serializer, BusinessDataMixin):
    contact_details = ContactSerializer(
        source='*',
        required=False,
        allow_null=True,
        email_required=True,
    )
    personal_details = PersonSerializer(
        source='individualDetails',
    )
    bank_account = BankAccountSerializer(
        source='bankAccountDetails',
        required=False,
        allow_null=True,
    )

    # def get_fields(self):
    #     fields = super(IndividualHolderSerializer, self).get_fields()
    #     tier = self.context.get('tier', 3)
    #     if tier == 0:
    #         fields['personal_details'].required = False
    #     return fields


class BusinessHolderSerializer(MergeSourceSerializer, BusinessDataMixin):
    contact_details = ContactSerializer(
        source='*',
        required=False,
        allow_null=True,
        email_required=True,
    )
    business_details = BusinessDetailsSerializer(
        source='businessDetails',
    )
    shareholders = ShareholderSerializer(
        source='businessDetails.shareholders',
        many=True,
        required=False,
    )
    bank_account = BankAccountSerializer(
        source='bankAccountDetails',
        required=False,
        allow_null=True,
    )

    def data_from_business(self, business):
        data = BusinessDataMixin.data_from_business(self, business)
        data['shareholders'] = [self.fields['shareholders'].child.data_from_business(business)]
        return data

    def validate(self, attrs):
        data = super().validate(attrs)

        business = self.context.get('business')
        if (
            not data['businessDetails'].get('shareholders')
            or not self.context.get('update_signatories')
            or not business
        ):
            return data

        account_holder = business.pos.account_holder
        if account_holder:
            account_holder_code = account_holder.account_holder_code
            account_holder_data = get_account_holder(account_holder_code)
            if not account_holder_data['accountHolderDetails'].get('businessDetails'):
                return data

            job_title = SignatureProvider.get_job_title_for_update(
                account_holder_data['accountHolderDetails']['businessDetails'].get(
                    'shareholders', []
                ),
                data['businessDetails']['shareholders'],
            )

            if SignatureProvider.is_eea_country(settings.API_COUNTRY):
                for shareholder in data['businessDetails']['shareholders']:
                    shareholder['shareholderType'] = 'Owner'
            else:
                for shareholder in data['businessDetails']['shareholders']:
                    shareholder['jobTitle'] = job_title
                    shareholder['shareholderType'] = 'Controller'

            update_signatories = (
                SignatureProvider.map_account_holder_and_shareholders_data_to_update_signatories(
                    account_holder_data,
                    data['businessDetails']['shareholders'],
                    job_title,
                )
            )

            if update_signatories:
                data['businessDetails']['signatories'] = update_signatories
        else:
            if SignatureProvider.is_eea_country(settings.API_COUNTRY):
                for shareholder in data['businessDetails']['shareholders']:
                    shareholder['shareholderType'] = 'Owner'
            else:
                for shareholder in data['businessDetails']['shareholders']:
                    shareholder['jobTitle'] = SignatureProvider.get_job_title_for_create(
                        data['businessDetails']['shareholders']
                    )
                    shareholder['shareholderType'] = 'Controller'
        return data


class IndividualEntitySerializer(BaseEntitySerializer, BusinessDataMixin):
    individual = IndividualHolderSerializer(
        source='accountHolderDetails',
    )


class BusinessEntitySerializer(BaseEntitySerializer, BusinessDataMixin):
    business = BusinessHolderSerializer(
        source='accountHolderDetails',
    )


class AccountHolderSerializer(MultiModeSerializer):
    """MarketPay AccountHolder Serializer

    Mostly, maps our data structure to adyen API  structure
    """

    MODES = {
        enums.LegalEntity.INDIVIDUAL.value: IndividualEntitySerializer,
        enums.LegalEntity.BUSINESS.value: BusinessEntitySerializer,
    }

    turn_on_pba = serializers.BooleanField(write_only=True, required=False, default=True)

    def infer_mode(self, instance):
        return get_attribute(instance, ['legalEntity'])

    def labels(self):
        mode = self.infer_mode(self.instance or self.validated_data)
        serializer_class = self.MODES[mode]
        return {'general': self.get_general_kyc_labels(), **serializer_class().labels()}

    def save(self, **kwargs):
        dry_run = self.initial_data.get('dry_run', False)
        create = not self.context['business'].pos.account_holder
        turn_on_pba = self.initial_data.get('turn_on_pba', True)
        first_save = not dry_run and create
        request_method = (
            requests.create_account_holder if create else requests.update_account_holder
        )

        try:
            with transaction.atomic():
                # Ensure local AccountHolder object and Adyen account holder
                # reference are created at the same time, and any error results
                # in their removal (thus usage of database transaction)
                if first_save:
                    AccountHolder.objects.create(
                        account_holder_code=self.validated_data['accountHolderCode'],
                        pos=self.context['business'].pos,
                    )
                if dry_run:
                    # Setting `primaryCurrency` field to `USDR` prevents Adyen
                    # from creating the account
                    self.validated_data['primaryCurrency'] = 'USDR'
                self.instance = request_method(self.validated_data)
        except requests.InvalidFieldsException as e:
            errors = self.map_market_pay_error_list(e.args[0])
            if errors or not dry_run:
                raise ServiceError(400, errors) from e

        if dry_run:
            return None

        serializer = ModelAccountHolderSerializer(
            instance=self.instance,
            context={
                'pos': self.context['business'].pos,
                'operator': self.context['operator'],
            },
        )
        model = serializer.save()

        if self.enable_pay_by_app_after_save() and create and turn_on_pba:
            self.context['business'].pos.enable_pay_by_app_default()

        return model

    @classmethod
    def enable_pay_by_app_after_save(cls) -> bool:
        """
        Should the business that finished KYC process have the pay by app option
        enabled?
        """
        return True

    def map_market_pay_error_list(self, errors):
        result = {}
        for attrs, description in filter(None, map(self.map_market_pay_error, errors)):
            set_value(result, attrs, [description])

        shareholders = safe_get(result, ['business', 'shareholders'])
        if shareholders:
            # return as list field with None for valid shareholder
            result['business']['shareholders'] = indexed_dict_to_list(
                shareholders,
            )

        return result

    def map_market_pay_error(self, error):
        """Map Market Pay invalidFields response to
        serializers.ValidationError format
        """
        field_type = error.get('fieldType')
        if not field_type:
            return None

        field_name = field_type['field'].lower()
        code = error.get('errorCode')
        if self._should_skip_error(code, field_type):
            return None

        # get translated description based on code, fallback to
        # provided english description
        description = force_str(enums.ERROR_CODES.get(code, error.get('errorDescription')))
        shareholder_code = field_type.get('shareholderCode')

        attrs = self.source_map.get(field_name)
        if attrs and shareholder_code and '[shareholders_id]' in attrs:
            attrs[attrs.index('[shareholders_id]')] = self.get_shareholder_id(
                shareholder_code,
            )

        if not attrs:
            if self.initial_data.get('dry_run'):
                if field_type['field'] == 'primaryCurrency':
                    return None

            attrs = ['non_field_error']
            description = f'{field_type["field"]}: {description}'

        return attrs, description

    @staticmethod
    def _should_skip_error(error_code: int, field_type: dict) -> bool:
        if error_code == 1 and field_type.get('fieldName') == 'shareholderType':
            return True

        return False

    def get_shareholder_id(self, shareholder_code):
        shareholders = safe_get(
            self.validated_data,
            ['accountHolderDetails', 'businessDetails', 'shareholders'],
        )
        if shareholder_code == 'New':
            for i, holder in enumerate(shareholders):
                if not holder.get('shareholderCode'):
                    return str(i)
        else:
            for i, holder in enumerate(shareholders):
                if holder.get('shareholderCode') == shareholder_code:
                    return str(i)

        return None

    @property
    def source_map(self):
        mode = self.infer_mode(self.instance or self.validated_data)
        serializer_class = self.MODES[mode]
        return serializer_class.source_map

    @classmethod
    def get_general_kyc_labels(cls) -> Dict[str, str]:
        return {
            'account_verification_info': cls._get_account_verification_info_label(),
            'bank_account_info': cls._get_bank_account_info_label(),
            'business_account_question': cls._get_business_account_question_label(),
            'business_account_explanation': cls._get_business_account_explanation_info(),
            'iban_question': cls._get_iban_question_label(),
            'iban_help': cls._get_iban_help_label(),
        }

    @staticmethod
    def _get_account_verification_info_label() -> str:
        return {
            Country.US: _(
                'To verify your identity, have your current address, '
                'social security number, and ID ready. Identity '
                'details must match the name of the bank account '
                'holder.'
            ),
            Country.PL: '',
            Country.GB: _(
                'To verify your identity, have your current address, ID'
                ' and company registation documents ready. Identity '
                'details must match the name of the bank account '
                'holder.'
            ),
            Country.ES: _(
                'To verify your identity, have your current address, ID'
                ' and company registation documents ready. Identity '
                'details must match the name of the bank account '
                'holder.'
            ),
        }.get(settings.API_COUNTRY, '')

    @staticmethod
    def _get_bank_account_info_label() -> str:
        return {
            Country.US: _(
                'Please include your bank account details so that '
                'Booksy can appropriately route your payouts. '
                'This account must be associated with a licensed '
                'institution - Venmo, Cashapp, and PayPal '
                'are not accepted.'
            ),
            Country.PL: _(
                'Please provide your bank account details to which '
                'the processed money will be transferred.'
            ),
            Country.GB: _(
                'Please provide your bank account details to which '
                'the processed money will be transferred. '
                'Your account must be from a licensed institution. '
                'Banks such as Monzo, Revolut and CashPlus are '
                'not accepted.'
            ),
            Country.ES: _(
                'Please provide your bank account details to which '
                'the processed money will be transferred. Your '
                'account must be from a licensed institution.'
            ),
        }.get(settings.API_COUNTRY, '')

    @staticmethod
    def _get_business_account_question_label() -> str:
        return {
            Country.US: _('Is this account held by a business?'),
            Country.PL: _('Do you have a KRS or REGON number?'),
            Country.GB: _('Do you have a Company Number?'),
            Country.ES: _('Do you have a NIF number'),
        }.get(settings.API_COUNTRY, '')

    @staticmethod
    def _get_business_account_explanation_info() -> str:
        return {
            Country.US: _(
                'Please provide the business\' Tax ID number (EIN) as found on IRS documentation'
            ),
            Country.PL: _(
                'A Company Number is a business identification '
                'number. You can find it in KRS or CEIDG.'
            ),
            Country.GB: _(
                'A Company Number is a business identification '
                'number. You can find it on the business '
                'registration documentation.'
            ),
            Country.ES: _(
                'A Company Number is a business identification '
                'number. You can find it on the business '
                'registration documentation.'
            ),
        }.get(settings.API_COUNTRY, '')

    @staticmethod
    def _get_iban_question_label() -> str:
        return {
            Country.US: _('What is an account number?'),
            Country.PL: _('What is a IBAN number?'),
            Country.GB: _('What is a IBAN number?'),
            Country.ES: _('What is a IBAN number?'),
        }.get(settings.API_COUNTRY, '')

    @staticmethod
    def _get_iban_help_label() -> str:
        return {
            Country.US: _('You can find an account number on your personal check.'),
            Country.PL: _(
                'You can find an account number on bank statement. '
                'The IBAN number consists of 22 digits. '
                'Example format is ****************************'
            ),
            Country.GB: _(
                'Sort Code and Account Number are not supported. '
                'You can find an IBAN number on bank statement. '
                'The IBAN number consists of 22 digits. '
                'Example format is **********************'
            ),
            Country.ES: _(
                'You can find an account number on bank statement. '
                'The IBAN number consists of 22 digits. '
                'Example format is ES79 2100 0813 6101 2345 6789'
            ),
        }.get(settings.API_COUNTRY, '')


class KYC2BankAccountSerializer(BankAccountSerializer):
    owner = serializers.CharField(
        source='ownerName',
        label=_("Account Holder's Name"),
    )

    @staticmethod
    def check_or_add_iban_prefix(
        iban: str,
        country: str,
        length_without_prefix: int,
    ) -> str:
        iban = iban.replace(' ', '')
        if len(iban) == length_without_prefix and not iban[:2].upper() == country:
            iban = country + iban
        return iban.upper()

    def to_internal_value(self, data):
        if 'iban' in data:
            match settings.API_COUNTRY:
                case Country.PL:
                    data['iban'] = self.check_or_add_iban_prefix(data['iban'], 'PL', 26)
                case Country.GB:
                    data['iban'] = self.check_or_add_iban_prefix(data['iban'], 'GB', 20)
        return super().to_internal_value(data)


class KYC2IndividualHolderSerializer(IndividualHolderSerializer):
    contact_details = ContactSerializer(
        source='*',
        required=False,
        allow_null=True,
        email_required=False,
    )
    personal_details = PersonSerializer(
        source='individualDetails',
        required=False,
        allow_null=True,
    )
    bank_account = KYC2BankAccountSerializer(
        source='bankAccountDetails',
        required=False,
        allow_null=True,
    )

    def run_validation(self, *args, **kwargs):
        validated_data = super().run_validation(*args, **kwargs)
        business: Business = self.context['business']
        validated_data['email'] = validated_data.get('email') or business.owner.email
        validated_data['fullPhoneNumber'] = (
            validated_data.get('fullPhoneNumber')
            or parse_phone_number(
                business.phone,
                business.country_code.upper(),
            ).global_nice
        )
        return validated_data


class KYC2BusinessHolderSerializer(BusinessHolderSerializer):
    contact_details = ContactSerializer(
        source='*',
        required=False,
        allow_null=True,
        email_required=False,
    )
    business_details = BusinessDetailsSerializer(
        source='businessDetails',
        required=False,
        allow_null=True,
    )
    bank_account = KYC2BankAccountSerializer(
        source='bankAccountDetails',
        required=False,
        allow_null=True,
    )

    def run_validation(self, *args, **kwargs):
        validated_data = super().run_validation(*args, **kwargs)
        business: Business = self.context['business']
        validated_data['email'] = validated_data.get('email') or business.owner.email
        validated_data['fullPhoneNumber'] = (
            validated_data.get('fullPhoneNumber')
            or parse_phone_number(
                business.phone,
                business.country_code.upper(),
            ).global_nice
        )
        return validated_data


class KYC2IndividualEntitySerializer(IndividualEntitySerializer):
    individual = KYC2IndividualHolderSerializer(
        source='accountHolderDetails',
    )


class KYC2BusinessEntitySerializer(BusinessEntitySerializer):
    business = KYC2BusinessHolderSerializer(
        source='accountHolderDetails',
    )


class KYC2AccountHolderSerializer(AccountHolderSerializer):
    """
    The KYC 2.0 process streamlined most of the views presented to the user.
    This resulted in less data being gathered from the user, and more of it
    being deducted from the existing Booksy account.  Please take a look at
    `run_validation()` method in which data is populated.

    Additionally, the KYC 2.0 changed the default account holder tier, that the
    user is associated with. By default, after finishing kyc process, the
    account is assigned to tier 1 meaning that it can process money freely.
    Payments in the POS settings are not enabled by default though, and require
    account holder creation confirmation from the Adyen.
    """

    MODES = {
        enums.LegalEntity.INDIVIDUAL.value: KYC2IndividualEntitySerializer,
        enums.LegalEntity.BUSINESS.value: KYC2BusinessEntitySerializer,
    }

    @classmethod
    def enable_pay_by_app_after_save(cls) -> bool:
        """
        The KYC 2.0 does not enable payments unless confirmation from the Adyen
        arrives.
        """
        return False

    def save(self, **kwargs):
        super().save(**kwargs)
        dry_run = self.initial_data.get('dry_run', False)
        turn_on_pba = self.initial_data.get('turn_on_pba', True)
        if not dry_run and turn_on_pba and not self.context['business'].pos.force_stripe_pba:
            # Mark POS `pay_by_app` status as awaiting Adyen's confirmation.
            # This blocks pos payments until proper notification is received.
            # Works only for Adyen businesses. For Stripe businesses do nothing.
            self.context['business'].pos.await_pba_approval()

    def run_validation(self, *args, **kwargs) -> dict:
        validated_data = super().run_validation(*args, **kwargs)
        address = validated_data['accountHolderDetails'].get('address', {})
        bank_account_details = validated_data['accountHolderDetails']['bankAccountDetails']
        for bank_account in bank_account_details:
            bank_account.update(
                {
                    'ownerStreet': address.get('street', ''),
                    'ownerHouseNumberOrName': address.get('houseNumberOrName', ''),
                    'ownerCity': address.get('city', ''),
                    'ownerPostalCode': address.get('postalCode', ''),
                    'ownerState': address.get('stateOrProvince', ''),
                    'ownerCountryCode': address.get(
                        'country',
                        settings.API_COUNTRY,
                    ).upper(),
                }
            )
        validated_data['processingTier'] = 1
        return validated_data


def extend_source_map(source_map, typ):
    """Extend source map to include adyen peculiarities

    :param (gen) source_map: source map
    :param typ: 'business' or 'individual'
    """
    extra = {}
    source_map = dict(source_map)
    for source, mapping in source_map.items():
        if source.endswith('fullphonenumber'):
            src = source.replace('fullphonenumber', 'phonenumber.fullphonenumber')
            extra[src] = mapping

        if source.endswith('email'):
            src = source.replace('email', 'email.email')
            extra[src] = mapping

    doc_types = [
        'document',
        'drivinglicence',
        'drivinglicenceback',
        'passport',
    ]

    source, mapping = {
        "business": (
            'accountholderdetails.businessdetails.shareholders.document',
            ['business', 'shareholders', '[shareholders_id]', 'document'],
        ),
        "individual": (
            'accountholderdetails.document',
            ['individual', 'personal_details', 'document'],
        ),
    }[typ]
    for doc_type in doc_types:
        extra[f'{source}.{doc_type}'] = mapping

    # verification error
    bank_mapping = None
    for mapping in source_map.values():
        if 'bank_account' in mapping:
            bank_mapping = mapping[: mapping.index('bank_account') + 1]
            break

    if bank_mapping:
        extra['accountholderdetails.bankaccountdetails.bankaccount'] = bank_mapping

    extra[f'verification_type.{enums.VerificationType.BANK_ACCOUNT_VERIFICATION.value.lower()}'] = (
        bank_mapping
    )
    extra[f'verification_type.{enums.VerificationType.COMPANY_VERIFICATION.value.lower()}'] = [
        'business'
    ]

    _identity_verification = enums.VerificationType.IDENTITY_VERIFICATION.value.lower()
    _passport_verification = enums.VerificationType.PASSPORT_VERIFICATION.value.lower()

    verification_types = {
        'individual': {
            _identity_verification: ['individual', 'personal_details'],
            _passport_verification: ['individual', 'personal_details', 'document'],
        },
        'business': {
            _identity_verification: ['business', 'shareholders', '[shareholders_id]'],
            _passport_verification: ['business', 'shareholders', '[shareholders_id]', 'document'],
        },
    }[typ]

    for key, value in verification_types.items():
        extra[f'verification_type.{key}'] = value

    extra.update(source_map)
    return extra


IndividualEntitySerializer.source_map = extend_source_map(
    source_map_gen(IndividualEntitySerializer().fields),
    'individual',
)
BusinessEntitySerializer.source_map = extend_source_map(
    source_map_gen(BusinessEntitySerializer().fields),
    'business',
)


##
## MODEL
##


class ModelAccountHolderDataSerializer(serializers.Serializer):
    mode = serializers.CharField(
        source='legalEntity',
        read_only=True,
    )
    payout_schedule = RepresentationField(
        source='accounts.0.payoutSchedule.schedule',
    )
    bank_account_uuid = RepresentationField(
        source='accountHolderDetails.bankAccountDetails.0.bankAccountUUID'
    )
    accountHolderStatus = serializers.DictField(read_only=True)
    verification = serializers.DictField(read_only=True)
    # accountHolderDetails = serializers.DictField(read_only=True)


class ModelAccountHolderSerializer(serializers.Serializer):
    """Data stored in AccountHolder Model

    read-only serializer
    """

    account_holder_code = serializers.CharField(
        source='accountHolderCode',
        read_only=True,
    )
    account_code = serializers.SerializerMethodField()
    account_code = serializers.CharField(
        source='accountCode',
        read_only=True,
    )
    status = serializers.CharField(
        source='accountHolderStatus.status',
        read_only=True,
    )
    payout_allowed = serializers.BooleanField(
        source='accountHolderStatus.payoutState.allowPayout',
        read_only=True,
    )
    data = ModelAccountHolderDataSerializer(source='*')

    def get_account_code(self, instance):
        # response from createAccountHolder
        if 'accountCode' in instance:
            return instance['accountCode']
        # response from getAccountHolder
        if 'accounts' in instance:
            return safe_get(
                instance,
                ['accounts', 0, 'Account', 'accountCode'],
            )
        return None

    def save(self, **kwargs):
        if 'account_holder' in self.context:
            model, created = self.context['account_holder'], False
        else:
            model, created = AccountHolder.objects.get_or_create(
                pos=self.context['pos'],
            )

        if not created:
            assert model.account_holder_code == self.data['account_holder_code']

        for attr, value in self.data.items():
            # update response doesnt contains accounts info
            # dont clear those fields
            if attr in ['account_code', 'payout_schedule'] and value is None:
                continue
            setattr(model, attr, value)
        model.save()
        operator = self.context.get('operator')
        model.log_changes(operator_id=operator.id if operator else None)

        return model


#
# ADMIN
#


def format_status(status):
    return format_html(  # nosemgrep: formathtml-fstring-parameter
        '<b style="color: {}">{}</b>', STATUS_COLOR_CHOICES.get(status, 'black'), status
    )


def format_payout_status(allowed):
    text = (
        '<b style="color: green">Allowed</b>' if allowed else '<b style="color: black">Blocked</b>'
    )
    return format_html(text)


class StateSerializer(serializers.Serializer):
    disabled = serializers.BooleanField(
        read_only=True,
    )
    disableReason = serializers.CharField(
        read_only=True,
    )
    tierNumber = serializers.IntegerField(
        read_only=True,
    )

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data['disabled'] = (
            '<b>DISABLED</b><span style="margin-left: 2em">{disableReason}</span>'.format(  # pylint: disable=consider-using-f-string
                **data
            )
            if data.get('disabled')
            else ''
        )
        if 'tierNumber' in data:
            data['tier'] = (
                '<span style="margin-left: 2em">(tier {tierNumber:d})</span>'.format(  # pylint: disable=consider-using-f-string
                    **data
                )
            )
        return data


class PayoutStateSerializer(serializers.Serializer):
    allowPayout = serializers.BooleanField(
        source='accountHolderStatus.payoutState.allowPayout',
        read_only=True,
    )
    limit_value = serializers.IntegerField(
        source='accountHolderStatus.payoutState.payoutLimit.value',
        read_only=True,
    )
    limit_currency = serializers.CharField(
        source='accountHolderStatus.payoutState.payoutLimit.currency',
        read_only=True,
    )
    state = StateSerializer(
        source='accountHolderStatus.payoutState',
        read_only=True,
    )

    def render(self):
        disabled = self.data.get('state', {}).get('disabled', '')
        tier = self.data.get('state', {}).get('tier', '')

        status = format_payout_status(self.data.get('allowPayout', False))
        limit = ''
        if 'limit_value' in self.data:
            limit = '<span style="margin-left: 2em">&lt; {value:.2f} {currency}</span>'.format(  # pylint: disable=consider-using-f-string
                value=(self.data['limit_value'] + 1) / 100.0,
                currency=self.data['limit_currency'],
            )
        return mark_safe(''.join([status, disabled, limit, tier]))  # nosemgrep: avoid-mark-safe


class AmountSerializer(serializers.Serializer):
    currency = serializers.CharField(read_only=True)
    value = serializers.IntegerField(read_only=True)

    def to_representation(self, instance):
        return f'{instance["value"] / 100.0:.2f} {instance["currency"]}'


class NextPayoutSerializer(serializers.Serializer):
    next_payout = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
    )
    pending_balance = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
    )
    currency = serializers.SerializerMethodField()

    def get_currency(self, _instance):
        return settings.CURRENCY_CODE


class PayoutSerializer(serializers.ModelSerializer):
    transactions_count = serializers.SerializerMethodField()
    amount = serializers.SerializerMethodField()

    class Meta:
        model = Payout
        fields = (
            # model fields
            'id',
            'created',
            # custom fields
            'transactions_count',
            'amount',
        )

    def get_transactions_count(self, instance):
        transactions_count = getattr(instance, "transactions_count", None)

        # if this serializer is used with many=True, please use annotate_payout_transactions_count
        assert transactions_count is not None

        return transactions_count

    def get_amount(self, instance):
        return serializers.DecimalField(
            max_digits=10,
            decimal_places=2,
        ).to_representation(-instance.amount)


class PayoutDetailsPaymentSerializer(serializers.Serializer):
    payment_type = serializers.CharField()
    payment_type_code = serializers.CharField()
    total = PriceField(read_only=True)
