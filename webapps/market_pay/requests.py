import logging

import requests
from django.conf import settings
from django.utils.translation import gettext as _
from rest_framework import status

import lib.tools
from lib.enums import StrEnum
from service.exceptions import ServiceError
from webapps.adyen.helpers import make_request


# pylint:disable=use-dict-literal

log = logging.getLogger('booksy.market_pay')


class InvalidFieldsException(Exception):
    pass


class AccountHolderStateType(StrEnum):
    PAYOUT = 'Payout'
    PROCESSING = 'Processing'


def create_account_holder(data):
    return _make_request(data, '/createAccountHolder')


def update_account_holder(data):
    return _make_request(data, '/updateAccountHolder')


def get_account_holder(account_holder_code):
    data = dict(accountHolderCode=account_holder_code)
    return _make_request(data, '/getAccountHolder')


def account_holder_balance(account_holder_code):
    return _make_request(
        dict(accountHolderCode=account_holder_code),
        '/accountHolderBalance',
        api_url=settings.MARKET_PAY_FUND_URL,
    )


def _make_request(data, url, api_url=settings.MARKET_PAY_ACCOUNT_URL, timeout=None):
    full_url = f'{api_url}{url}'
    try:
        response = make_request(data, full_url, market_pay=True, timeout=timeout)
    except requests.exceptions.RequestException:
        # this is already logged in adyen_requests.log
        _connection_error()

    return _validate_response(response)


def _validate_response(response):
    """Raises on invalid fields"""
    if response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY:
        raise ServiceError(
            code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            errors=['Account holder does not exist'],
        )
    if response.status_code not in [
        status.HTTP_200_OK,
        status.HTTP_202_ACCEPTED,
        status.HTTP_400_BAD_REQUEST,
    ]:
        _connection_error()

    response_data = response.json()

    if response_data.get('invalidFields'):
        raise InvalidFieldsException(response_data['invalidFields'])

    return response_data


def _connection_error():
    lib.tools.quick_error(
        ('error', 'connection', None),
        _('Connection Error. Please try again later.'),
    )
