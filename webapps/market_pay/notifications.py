import logging
import typing as t
from django.utils.translation import get_language, gettext_lazy as _

import lib.tools
from lib.time_24_hour import format_datetime
from webapps.business.notifications.planner import WorkingHoursSchedulePlanner
from webapps.notification.base import (
    BaseNotification,
    Context,
    PopupTemplate,
    PushTarget,
)
from webapps.notification.channels import (
    EmailChannel,
    PopupChannel,
    PushChannel,
    push_from_popup,
)
from webapps.notification.enums import (
    NotificationCategory,
    NotificationGroup,
    NotificationIcon,
    NotificationSize,
    NotificationTarget,
)
from webapps.notification.models import NotificationHistory
from webapps.notification.recipients import (
    Managers,
    NotificationCustomer,
    NotificationCustomerUser,
    Reception,
    SystemSender,
)
from webapps.pos.enums import PaymentTypeEnum, receipt_status
from webapps.pos.models import TransactionRow
from webapps.pos.tools import get_receipt_data

if t.TYPE_CHECKING:
    # pylint: disable=ungrouped-imports, unused-import
    from webapps.pos.models import PaymentRow


logger = logging.getLogger('market_pay.notifications')


class KYCReceiptBaseNotification(BaseNotification):
    recipients = (NotificationCustomer,)
    sender = SystemSender
    channels = (EmailChannel,)

    def __init__(self, transaction, **parameters):
        super().__init__(transaction, **parameters)

        self.transaction = transaction
        self.business = transaction.pos.business
        self.customer = transaction.customer_card
        self.customer_user = transaction.customer

    @property
    def identity(self) -> str:
        return f'{self.notif_type},{self.transaction.id}'


class KYCBaseNotification(BaseNotification):
    recipients = (Managers,)
    sender = SystemSender

    def __init__(self, marketpay_notification, **parameters):
        super().__init__(marketpay_notification, **parameters)
        self.marketpay_notification = marketpay_notification
        self.business = marketpay_notification.account_holder.pos.business

    @property
    def identity(self) -> str:
        return f'{self.notif_type},{self.marketpay_notification.id}'


class _BasePaymentBusinessNotification(BaseNotification):
    sender = SystemSender
    recipients = (Managers, Reception)

    def __init__(self, payment_row: 'PaymentRow', **parameters):
        super().__init__(payment_row, **parameters)
        self.payment_row = payment_row
        self.business = payment_row.receipt.transaction.pos.business
        self.transaction = self.payment_row.receipt.transaction

    @property
    def identity(self) -> str:
        return f'{self.notif_type},{self.payment_row.id}'

    def get_target(self):
        return PushTarget(
            type=NotificationTarget.TRANSACTION,
            id=self.payment_row.receipt.transaction_id,
        )


class PaymentContext(Context):
    def get_context(self):
        payment_row = self.notification.payment_row
        client = self.notification.transaction.customer
        created = payment_row.created.astimezone(self.notification.business.get_timezone())
        transaction_date = format_datetime(created, 'date_ymd', get_language())
        transaction_time = format_datetime(created, 'time_hm', get_language())
        service_name = self._get_service_name(payment_row.receipt.transaction_id)

        appointment = self.notification.transaction.appointment
        booked_from = appointment.booked_from
        booking_date = format_datetime(booked_from, 'date_ymd', get_language())
        booking_time = format_datetime(booked_from, 'time_hm', get_language())
        return {
            "amount": lib.tools.format_currency(payment_row.amount),
            "client_name": client.full_name,
            "transaction_type": payment_row.payment_type.label,
            "transaction_date": transaction_date,
            "transaction_time": transaction_time,
            "service_name": service_name,
            "booking_date": booking_date,
            "booking_time": booking_time,
            "booking_reference": appointment.id,
            "customer_email": client.email,
            "customer_phone": appointment.customer_phone,
        }

    @staticmethod
    def _get_service_name(transaction_id):
        names = (
            TransactionRow.objects.filter(
                transaction_id=transaction_id,
            )
            .values_list(
                'subbooking__service_variant__service__name',
                'subbooking__service_name',
                'service_variant__service__name',
                'product__name',
            )
            .last()
            or []
        )
        first_name = next(filter(None, names), None)
        return first_name


class BusinessRefundContext(Context):
    def get_context(self):
        def get_name(transaction_row):
            name = None
            if transaction_row.subbooking:
                name = (
                    transaction_row.subbooking.service_variant.service.name
                    if transaction_row.subbooking.service_variant
                    else transaction_row.subbooking.service_name
                )
            elif transaction_row.service_variant:
                name = transaction_row.service_variant.service.name
            elif transaction_row.product:
                name = transaction_row.product.name

            return name

        services = list(
            filter(
                None, map(get_name, self.notification.payment_row.receipt.transaction.rows.all())
            )
        )

        amount = self.notification.payment_row.amount
        operation_fee = self.notification.payment_row.operation_fees.first()
        fee_amount = lib.tools.major_unit(operation_fee.amount)
        total_amount = amount + fee_amount

        return {
            "business_owner_name": self.notification.business.owner.full_name,
            "services": services,
            "refund_fee": lib.tools.format_currency(fee_amount),
            "total_amount": lib.tools.format_currency(total_amount),
        }


class RefundBusinessEmailNotification(_BasePaymentBusinessNotification):
    """
    DEPRECATED https://booksy.atlassian.net/browse/PN-453
    Currently, this notification is not being triggered.
    """

    category = NotificationCategory.MARKETPAY_BUSINESS_REFUND
    contexts = (BusinessRefundContext, PaymentContext)
    channels = (EmailChannel,)
    email_template_name = 'KYC/KYC_business_refund'
    recipients = (Managers,)
    sender = SystemSender


class RefundBusinessMobileNotification(_BasePaymentBusinessNotification):
    """
    DEPRECATED https://booksy.atlassian.net/browse/PN-453
    Currently, this notification is not being triggered.
    """

    channels = (PopupChannel, PushChannel)
    contexts = (BusinessRefundContext, PaymentContext)
    schedule_planner = WorkingHoursSchedulePlanner
    category = NotificationCategory.MARKETPAY_BUSINESS_REFUND

    popup_template = PopupTemplate(
        group=NotificationGroup.PAYMENT,
        icon=NotificationIcon.MOBILE_PAYMENT_WARNING,
        size=NotificationSize.NORMAL,
        messages=[
            _("You've refunded {total_amount} charge"),
            '{transaction_type} • {transaction_date} • {transaction_time}',
            '{client_name} • {service_name}',
        ],
        crucial=False,
        relevance=1,
    )
    push_template = push_from_popup(popup_template)


class PaymentCompletedNotification(KYCReceiptBaseNotification):
    category = NotificationCategory.MARKETPAY_PAYMENT_COMPLETED
    email_template_name = 'KYC/KYC_payment_completed'

    def get_context(self) -> dict:
        template_args, _ = get_receipt_data(
            self.transaction.pos,
            self.transaction,
            NotificationHistory.SENDER_CUSTOMER,
            get_language(),
        )

        return template_args


class CancellationFeeChargeNotification(PaymentCompletedNotification):
    category = NotificationCategory.MARKETPAY_CANCELLATION_FEE_CHARGE
    email_template_name = 'KYC/KYC_cancellation_fee_charge'


class RefundCustomerNotification(BaseNotification):
    category = NotificationCategory.MARKETPAY_CUSTOMER_REFUND
    recipients = (NotificationCustomerUser,)
    sender = SystemSender
    channels = (EmailChannel, PushChannel)
    email_template_name = 'KYC/KYC_customer_refund'
    push_template = _('Your refund of {amount} from {business_name} is on its way.')

    @property
    def identity(self) -> str:
        return f'{self.notif_type},{self.payment_row.id}'

    def get_context(self) -> dict:
        pos = self.payment_row.receipt.transaction.pos
        transaction = self.payment_row.receipt.transaction
        user = transaction.customer_card.user
        language = user.customer_profile.language
        sender = NotificationHistory.SENDER_CUSTOMER
        data, _ = get_receipt_data(pos, transaction, sender, language)
        business = pos.business
        data.update(
            {
                "business_id": business.id,
                "business_name": business.name,
                "user_id": user.id,
                "user_email": user.email,
                "user_language": language,
                "card_last_digits": self.payment_row.card_last_digits,
                "amount": lib.tools.format_currency(self.payment_row.amount),
            }
        )

        return data

    def __init__(self, payment_row, **parameters):
        self.payment_row = payment_row
        self.business = payment_row.receipt.transaction.pos.business
        self.customer = payment_row.receipt.transaction.customer
        self.customer_card = payment_row.receipt.transaction.customer_card
        self.appointment = self.payment_row.receipt.transaction.appointment
        super().__init__(payment_row, **parameters)

    def get_target(self):
        return PushTarget(
            type='booking', id=self.payment_row.receipt.transaction.appointment.subbookings[0].id
        )


class CustomerNotificationHandler:
    @classmethod
    def handle_notification(cls, payment_row: 'PaymentRow'):
        # (status, payment_type)
        handlers = {
            (
                receipt_status.PAYMENT_SUCCESS,
                PaymentTypeEnum.PAY_BY_APP,
            ): PaymentCompletedNotification,
            (
                receipt_status.PREPAYMENT_SUCCESS,
                PaymentTypeEnum.PREPAYMENT,
            ): PaymentCompletedNotification,
            (
                receipt_status.DEPOSIT_CHARGE_SUCCESS,
                PaymentTypeEnum.PAY_BY_APP,
            ): CancellationFeeChargeNotification,
            (
                receipt_status.BOOKSY_PAY_SUCCESS,
                PaymentTypeEnum.BOOKSY_PAY,
            ): PaymentCompletedNotification,
        }
        notification = handlers.get(
            (payment_row.status, payment_row.payment_type.code),
        )
        if notification:
            notification(payment_row.receipt.transaction).send()
