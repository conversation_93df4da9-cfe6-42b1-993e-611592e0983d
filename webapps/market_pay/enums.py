from django.utils.translation import gettext_noop, gettext_lazy as _

from lib.enums import BaseEnum, StrEnum, StrChoicesEnum
from webapps.pos.enums import bank_account_type


class LegalEntity(BaseEnum):
    INDIVIDUAL = 'Individual'
    BUSINESS = 'Business'


class Gender(BaseEnum):
    MALE = 'MALE'
    FEMALE = 'FEMALE'
    UNKNOWN = 'UNKNOWN'


# TODO: change to direct reference when user.models.GENDERS moved to enums
USER_GENDER_MAP = {
    'M': Gender.MALE,
    'F': Gender.FEMALE,
    'R': Gender.UNKNOWN,
    None: Gender.UNKNOWN,
}


class AccountHolderStatus(BaseEnum):
    ACTIVE = gettext_noop('Active')
    INACTIVE = gettext_noop('Inactive')
    SUSPENDED = gettext_noop('Suspended')
    CLOSED = gettext_noop('Closed')


class BankAccountType(BaseEnum):
    CHECKING = 'checking'
    SAVING = 'savings'


# map type from pos enums to market_pay enums
ACCOUNT_TYPE_MAP = {
    bank_account_type.CHECKING: BankAccountType.CHECKING,
    bank_account_type.SAVINGS: BankAccountType.SAVING,
}


class PhotoDocumentType(BaseEnum):
    PASSPORT = 'PASSPORT'
    ID_CARD_FRONT = 'ID_CARD_FRONT'
    ID_CARD_BACK = 'ID_CARD_BACK'
    DRIVING_LICENCE_FRONT = 'DRIVING_LICENCE_FRONT'
    DRIVING_LICENCE_BACK = 'DRIVING_LICENCE_BACK'
    BANK_STATEMENT = 'BANK_STATEMENT'
    COMPANY_REGISTRATION_SCREENING = 'COMPANY_REGISTRATION_SCREENING'


class VerificationStatus(StrChoicesEnum):
    """
    https://docs.adyen.com/developers/marketpay/onboarding-and-verification/verification-checks/identity-check#verificationstatuses
    """

    AWAITING_DATA = 'AWAITING_DATA', _('Awaiting data')
    DATA_PROVIDED = 'DATA_PROVIDED', _('Data provided')
    PENDING = 'PENDING', _('Pending')
    INVALID_DATA = 'INVALID_DATA', _('Invalida data')
    RETRY_LIMIT_REACHED = 'RETRY_LIMIT_REACHED', _('Retry limit reached')
    PASSED = 'PASSED', _('Passed')
    FAILED = 'FAILED', _('Failed')


VERIFICATION_ERROR_STATUSES = [
    VerificationStatus.AWAITING_DATA.value,
    VerificationStatus.INVALID_DATA.value,
    VerificationStatus.FAILED.value,
    VerificationStatus.RETRY_LIMIT_REACHED.value,
]


VERIFICATION_STATUS_MESSAGES = {
    VerificationStatus.AWAITING_DATA.value: _('Awaiting data'),
    VerificationStatus.DATA_PROVIDED.value: _('Data provided'),
    VerificationStatus.PENDING.value: _('Pending'),
    VerificationStatus.INVALID_DATA.value: _('Invalida data'),
    VerificationStatus.RETRY_LIMIT_REACHED.value: _('Retry limit reached'),
    VerificationStatus.PASSED.value: _('Passed'),
    VerificationStatus.FAILED.value: _('Failed'),
}


class VerificationType(StrChoicesEnum):
    COMPANY_VERIFICATION = 'COMPANY_VERIFICATION', _('Company verification')
    IDENTITY_VERIFICATION = 'IDENTITY_VERIFICATION', _('Identity verification')
    PASSPORT_VERIFICATION = 'PASSPORT_VERIFICATION', _('Passport verification')
    BANK_ACCOUNT_VERIFICATION = 'BANK_ACCOUNT_VERIFICATION', _('Bank account verification')
    NONPROFIT_VERIFICATION = 'NONPROFIT_VERIFICATION', _('Nonprofit verification')


class PayoutSchedule(BaseEnum):
    DEFAULT = 'DEFAULT'
    HOLD = 'HOLD'
    DAILY = 'DAILY'
    WEEKLY = 'WEEKLY'
    MONTHLY = 'MONTHLY'


class NotificationEvent(StrEnum):
    ACCOUNT_HOLDER_CREATED = 'ACCOUNT_HOLDER_CREATED'
    ACCOUNT_CREATED = 'ACCOUNT_CREATED'
    ACCOUNT_UPDATED = 'ACCOUNT_UPDATED'
    ACCOUNT_HOLDER_UPDATED = 'ACCOUNT_HOLDER_UPDATED'
    ACCOUNT_HOLDER_STATUS_CHANGE = 'ACCOUNT_HOLDER_STATUS_CHANGE'
    ACCOUNT_HOLDER_VERIFICATION = 'ACCOUNT_HOLDER_VERIFICATION'
    ACCOUNT_HOLDER_LIMIT_REACHED = 'ACCOUNT_HOLDER_LIMIT_REACHED'
    ACCOUNT_HOLDER_PAYOUT = 'ACCOUNT_HOLDER_PAYOUT'
    PAYMENT_FAILURE = 'PAYMENT_FAILURE'
    SCHEDULED_REFUNDS = 'SCHEDULED_REFUNDS'
    REPORT_AVAILABLE = 'REPORT_AVAILABLE'
    TRANSFER_FUNDS = 'TRANSFER_FUNDS'
    BENEFICIARY_SETUP = 'BENEFICIARY_SETUP'
    COMPENSATE_NEGATIVE_BALANCE = 'COMPENSATE_NEGATIVE_BALANCE'


class PayoutStatus(StrEnum):
    INITIATED = 'Initiated'
    FAILED = 'Failed'


PAYOUT_STATUS_CHOICES = (
    ('I', PayoutStatus.INITIATED.value),
    ('F', PayoutStatus.FAILED.value),
)


# Verification codes
BANK_ACCOUNT_DOCUMENT_CODES = [
    1001,
    1003,
    1059,
    3109,
]


# pylint: disable=line-too-long
ERROR_CODES = {
    1: _('Field is missing'),
    2: _('Email address is invalid'),
    3: _('Country code is invalid'),
    4: _('Value contains illegal characters (numbers)'),
    5: _('URL is invalid'),
    6: _('Date is not in a format yyyy-MM-dd'),
    7: _('Date is not in a valid range (1900 - now)'),
    8: _('Bank details are invalid'),
    9: _('Postal code is invalid for the country'),
    10: _('State code is invalid, should be 1-3 characters'),
    11: _('State code is unknown for the country'),
    12: _(
        'AccountHolderDetails.fullPhoneNumber and AccountHolderDetails.phoneNumber provided, please provide one or another'
    ),
    13: _('fullPhoneNumber is not a valid phone number'),
    14: _('phoneNumber is too short'),
    15: _('Country is not supported'),
    16: _('Unknown currency'),
    17: _(
        'IBAN and accountNumber/branchCode/bankCode are specified, please provide one or another.'
    ),
    18: _(
        'Can not determine bank code from branch code and account number, please supply bank code.'
    ),
    19: _(
        'Provided tier number is not valid, value needs to equal or higher then 0 and lower or equal than the maximum available tier.'
    ),
    20: _('Field should not be filled for country.'),
    21: _('Account description is not valid.'),
    22: _('This currency and/or country is not supported for payouts.'),
    23: _('Account holder code does not exists or invalid.'),
    24: _('Business details field not allowed on individual account.'),
    25: _('Individual details field not allowed on business or non-profit account.'),
    26: _('No tier configuration found for account.'),
    27: _('Account update not allowed.'),
    28: _('Account holder already exists with the accountHolderCode.'),
    29: _('Account code does not exist or invalid.'),
    30: _('Field should not be filled.'),
    31: _('Account holder does not exists with the accountHolderCode.'),
    32: _('Account holder update not allowed.'),
    33: _('Account has an invalid status for requested operation.'),
    34: _('An invalid code is provided.'),
    40: _('No document content was provided.'),
    41: _('The maximum accepted file size : {  0} bytes. The size of the file {1} is {  2} bytes.'),
    42: _('The image uploaded is too small.'),
    43: _('File can possibly contain malware or a virus. Not accepted.'),
    44: _('The document has already been uploaded.'),
    45: _('File is of invalid format. Accepted formats: pdf, jp(e)g and png.'),
    46: _('The document exceeds the maximum allowed number of pages.'),
    50: _('Bank account does not exist for bankAccountCode.'),
    51: _('Currency code is invalid.'),
    52: _('Amount is negative or zero.'),
    53: _('Source and destination must be different accounts.'),
    54: _('Not enough balance.'),
    55: _('Account holder has an invalid status for requested operation.'),
    56: _('Account holder has beneficiary setup. Fund transfer is not allowed.'),
    57: _('Fund transfer/payout is not allowed for account.'),
    58: _('Outgoing transfer/payout limit reached for account.'),
    70: _('Shareholder does not exist for shareHolderCode.'),
    71: _(
        'Shareholder codes cannot be deleted, as business account must have at least one shareholder.'
    ),
    72: _('Payout instrument token does not exist for payoutInstrumentTokenCode.'),
    73: _('Bank account and payout instrument token cannot both be present in the request.'),
    74: _('P.O. Boxes are not allowed.'),
    75: _('Bank statement is not allowed for shareholders.'),
    76: _('ID document is not allowed for bank accounts.'),
}


VERIFICATION_CODES = {
    1001: _(
        "The name of the bank account owner is missing from the bank statement photo. Reupload a bank statement photo that includes the name of the account owner."
    ),
    1002: _(
        "The bank account number is missing from the bank statement photo. Reupload a bank statement photo that includes the bank account number."
    ),
    1003: _(
        "The name on the bank statement does not match the name of the account holder. Reupload a bank statement that is associated with the account holder."
    ),
    1004: _(
        "DEPRECATED The bank account number on the bank statement does not match the configured bank account. Please upload a bank statement belonging to the configured account and account holder or change the configured bank account."
    ),
    1005: _(
        "DEPRECATED Both the account holder name and bank account number do not match the configured bank account. Please upload a bank statement corresponding to the configured bank details or change the account holder configuration."
    ),
    1006: _(
        "DEPRECATED The bank statement provided is more that 3 months old. Please provide a bank statement that is less than 3 months old."
    ),
    1052: _("DEPRECATED Manual review required."),
    1055: _(
        "DEPRECATED The maximum amount of retries has been surpassed. Please provide the bank statement."
    ),
    1056: _("Whitelisted bank account."),
    1057: _("Bank account details are currently being verified."),
    1058: _("DEPRECATED There is no bank data provided."),
    1059: _(
        "Unable to verify bank account with the information provided. Double-check the information provided is correct."
    ),
    1060: _(
        "A technical issue occured while processing your information. Another verification attempt has been scheduled."
    ),
    1101: _(
        "The ID you provided does not match the account holder. Upload a different ID that matches the account holder."
    ),
    1102: _("Please upload a new version of the ID document and ensure all details are visible."),
    1103: _("DEPRECATED The provided document has expired. Please upload a valid ID document."),
    1104: _(
        "DEPRECATED The required details are not fully visible. Please make sure that the entire front of the ID is fully visible. Preferably the image should be flat, without any glare, and not edited in any way."
    ),
    1202: _(
        "The account holder is pending review. This may add a delay to the verification process."
    ),
    1301: _(
        "The account holder is being manually reviewed. This may add a delay to the verification process."
    ),
    1302: _(
        "User's identity could not be verified. Ensure personal details are correct or upload a passport."
    ),
    1303: _("Passing identity verification, since the passport verification has PASSED."),
    1304: _("The maximum amount of retries has been surpassed, please provide a passport."),
    1306: _("We had an unexpected error. Please provide a new upload of your ID document."),
    1401: _(
        "We were unable to verify your identity. Please double-check all information provided is correct."
    ),
    1402: _("We are currently processing your information."),
    1501: _("Please contact our customer support by using the 'HELP' button."),
    1602: _("Congratulations! You've been approved to start using mobile payments."),
    1603: _("Your information is currently being reviewed."),
    1604: _(
        "Your Date of Birth does not match. Please double-check this information and re-submit."
    ),
    1605: _(
        "Unfortunately we are not able to set up mobile payments for you at this time. For more information, contact Customer Success by using the 'HELP' button."
    ),
    1606: _(
        "Your personal data has been updated too many times. You will need to provide additional Photo ID for verification. Please contact Customer Success by using the 'HELP' button"
    ),
    1607: _("Your information is currently being reviewed. "),
    2101: _(
        "Business registration and/or company name and/or the business partners appear to be incorrect."
    ),
    9001: _(
        "Unfortunately we are not able to set up mobile payments for you at this time. For more information, contact Customer Success by using the 'HELP' button."
    ),
    9002: _(
        "Unfortunately we are not able to set up mobile payments for you at this time. For more information, contact Customer Success by using the 'HELP' button."
    ),
}


BANK_VERIFICATION_CODES = {
    3101: _('Owner and/or account number do not match configured details'),
    3102: _('Owner and/or account number missing from document'),
    3103: _('Not a valid high quality bank document'),
    3104: _(
        'Unable to verify bank account with information provided. Please verify that the information provided is correct. If the information is correct, provide a bank statement clearly showing the bank logo, bank account number, and account holder name. This statement must verify that the bank account details are tied to the individual who is being verified.'
    ),
    3105: _('Bank statement older than 3 months'),
    3106: _('Not a valid bank statement'),
    3109: _('The maximum amount of retries has been surpassed, please provide a document.'),
    3110: _(
        'The maximum amount of retries has been surpassed. Please don\'t retry. Document verification is pending.'
    ),
    3111: _(
        'The routing number provided is incorrect. Please provide the correct ABA routing number associated with this account'
    ),
    3112: _('The account number provided is incorrect. Please provide the correct account number.'),
    3113: _(
        'This institution is not programmatically covered. Please use a different financial institution or upload a copy of your bank statement.'
    ),
    3114: _(
        'This institution is not eligible for payouts on MarketPay. Please provide a different bank account.'
    ),
    3120: _('The user is being manually reviewed by Adyen\'s onboarding team'),
    8000: _(
        'There was a technical issue when processing verification checks. A retry has been scheduled.'
    ),
    3130: _('Invalid data'),
    3151: _('Bank logo not on statement.'),
    3152: _('Account number not on statement.'),
    3153: _('Account owner name not on statement.'),
    3154: _('Account number on statement does not match account holder entered information.'),
    3155: _('Bank account does not belong to account holder.'),
    3156: _('Image quality of statement not sufficient.'),
    3157: _('Document type not accepted.'),
    3158: _('Bank logo and account number not on statement.'),
    3159: _('Bank logo, account number, and account owner name not on statement.'),
    3160: _('Account number, and account owner name not on statement.'),
    3161: _('Bank logo and account owner name not on statement.'),
    3162: _('Bank account country does not match account holder location.'),
    3163: _('Document contains sensitive information.'),
}


COMPANY_VERIFICATION_CODES = {
    2120: _(
        'The account is under manual risk evaluation, this evaluation may take longer than the usual approval times.'
    ),
    2130: _(
        'Business registration and/or company name and/or the shareholders appear to be incorrect or cannot be verified.'
    ),
    2151: _(
        'An individual appears to have on-boarded as a business. Please complete the individual on-boarding flow.'
    ),
    2152: _('Business entity name could not be verified.'),
    2153: _('Business registration number could not be verified.'),
    2154: _('Business VAT number could not be verified.'),
    2155: _(
        'Could not verify connection between individual listed as shareholder and the legal entity.'
    ),
    2156: _('The submitted address does not match the company extract.'),
    2157: _('The submitted business registration number does not match the company extract.'),
    2158: _('The submitted legal name does not match the company extract.'),
    2159: _('The doing business as name is provided as legal name.'),
    2160: _('The submitted country does not match the company extract.'),
    2161: _('The address is a PO box.'),
    2162: _('The company extract does not contain the legal name.'),
    2163: _('The company extract does not contain the business registration number.'),
    2164: _('Unrecognized document; please provide a supported document type.'),
    2165: _('Unable to process image; the image quality is too poor.'),
}


class TransferFundsStatus(BaseEnum):
    PREPARED = 'P'
    RECEIVED = 'R'
    SUCCESS = 'S'
    FAILED = 'F'
    EXPIRED = 'E'

    @classmethod
    def from_result_code(cls, code):
        return _TRANSFER_RESULT_CODE_MAP.get(
            code,
            TransferFundsStatus.FAILED,
        ).value

    @classmethod
    def from_status_code(cls, code):
        return _TRANSFER_STATUS_CODE_MAP.get(
            code,
            TransferFundsStatus.FAILED,
        ).value


class TransferFundsType(BaseEnum):
    REFERRAL_REWARD = 'R'
    MANUAL_TRANSFER = 'M'


class TransferFundsResultCode(BaseEnum):
    RECEIVED = 'Received'
    INVALID = 'Invalid'


_TRANSFER_RESULT_CODE_MAP = {
    TransferFundsResultCode.RECEIVED.value: TransferFundsStatus.RECEIVED,
    TransferFundsResultCode.INVALID.value: TransferFundsStatus.FAILED,
}


class TransferFundsStatusCode(BaseEnum):
    SUCCESS = 'Success'
    FAILED = 'Failed'


_TRANSFER_STATUS_CODE_MAP = {
    TransferFundsStatusCode.SUCCESS.value: TransferFundsStatus.SUCCESS,
    TransferFundsStatusCode.FAILED.value: TransferFundsStatus.FAILED,
}


class VerificationColor(BaseEnum):
    RED = 'red'
    ORANGE = 'orange'
    GREEN = 'green'


class AdyenToStripeMigrationConsentStage(StrEnum):
    STAGE_I = 'STAGE_I'
    STAGE_II = 'STAGE_II'
