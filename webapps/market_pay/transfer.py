from logging import getLogger

from django.conf import settings

from service.exceptions import ServiceError
from webapps.market_pay import requests
from webapps.market_pay.requests import InvalidFieldsException

log = getLogger('booksy.marketpay_flow')

"""
https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html
czysta architektura 317
https://www.youtube.com/watch?v=5OjqD-ow8GE 42
https://phalt.github.io/django-api-domains/examples/

TransferFunds lifecycle

1. A pending TransferFunds created for pending fees (status PREPARED)
    - old pending TransferFunds get EXPIRED
2. Adyen request is sent, TransferFunds status updated (status RECEIVED)
3. Adyen notification received, TransferFunds status updated
    (status FAILED or SUCCESS)
    if SUCCESS:
        fees marked as settled
4. FAILED TransferFunds is still pending
    Adyen request can be repeated
    it will be expired during creating new TransferFunds
"""


def get_balances(account_holder_code, account_code):
    try:
        result = requests.account_holder_balance(account_holder_code)
    except ServiceError:
        return None
    except InvalidFieldsException:
        # occurs temporarily after creation of account holder because adyen returns errors
        # for short period of time (until send ACCOUNT_HOLDER_CREATED notification)
        return None
    balances = {
        'balance': None,
        'pendingBalance': None,
        'onHoldBalance': None,
    }
    for balance in result['balancePerAccount']:
        if balance['accountCode'] == account_code:
            for balance_key in balances.copy():
                for currency_balance in balance['detailBalance'].get(balance_key, []):
                    if currency_balance['currency'] == settings.CURRENCY_CODE:
                        balances[balance_key] = currency_balance['value']
    return balances
