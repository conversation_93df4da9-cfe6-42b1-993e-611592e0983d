from bo_obs.datadog.enums import BooksyTeams
from django.conf import settings

from service.tools import RequestHandler, json_request, require_basic_auth


@require_basic_auth(credentials=settings.ADYEN_NOTIFICATION_AUTH)
class NotificationsHandler(RequestHandler):
    """
    Endpoint exposed for incoming notifications.
    """

    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)

    @json_request
    def post(self, *args, **kwargs):
        self.finish_with_json(200, {'notificationResponse': '[accepted]'})
