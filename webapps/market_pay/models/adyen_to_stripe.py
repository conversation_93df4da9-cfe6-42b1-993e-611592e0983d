from django.db import models

from lib.models import UUIDArchiveModel
from webapps.market_pay.enums import AdyenToStripeMigrationConsentStage


class AdyenToStripeMigrationConsent(UUIDArchiveModel):
    business = models.ForeignKey(
        'business.Business',
        on_delete=models.DO_NOTHING,
    )
    ip_address = models.CharField(max_length=46)
    agreed = models.BooleanField(default=False)
    approver = models.ForeignKey(
        'user.User',
        on_delete=models.DO_NOTHING,
    )
    stage = models.CharField(
        choices=AdyenToStripeMigrationConsentStage.choices(),
        default=AdyenToStripeMigrationConsentStage.STAGE_I,
        max_length=12,
    )
