# -*- coding: utf-8 -*-
from __future__ import annotations

import datetime
from decimal import Decimal
from difflib import Differ
from typing import Optional

import simplejson as json
from dateutil.utils import today
from django.conf import settings
from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON><PERSON>
from django.db import models
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from django.utils.functional import cached_property
from rest_framework import status

from lib.models import (
    ArchiveManager,
    ArchiveModel,
    AutoUpdateQuerySet,
    BaseArchiveManager,
    SoftDeleteManager,
    UndeletableMixin,
)
from lib.tools import (
    create_uuid,
    format_currency,
    id_to_external_api,
    minor_unit,
    tznow,
)
from webapps.adyen.helpers import make_request
from webapps.b2b_referral.models import B2BReferralReward
from webapps.business.models import Business
from webapps.market_pay import consts, enums
from webapps.pos.models import POS

# pylint: disable=abstract-method, too-many-positional-arguments

CURRENCY_FACTOR = 10 ** settings.COUNTRY_CONFIG.currency_data['frac_digits']


class AccountHolder(UndeletableMixin, ArchiveModel):
    class Meta:
        # for admin page
        verbose_name_plural = 'KYC Status'

    id = models.AutoField(primary_key=True, db_column='accountholder_id')
    pos = models.ForeignKey(
        POS,
        null=True,
        blank=True,
        on_delete=models.DO_NOTHING,
        db_constraint=False,
        related_name='account_holders',
    )
    account_holder_code = models.CharField(max_length=64, unique=True)
    account_code = models.CharField(max_length=64)
    status = models.CharField(max_length=12, blank=True)
    payout_allowed = models.BooleanField(default=False)
    manual_payout_allowed = models.BooleanField(default=False)
    ever_passed_kyc = models.BooleanField(default=False)
    data = JSONField(default=dict)

    all_objects = SoftDeleteManager()

    def __str__(self):
        return f'{self.id}, {self.account_holder_code}'

    @property
    def payout_schedule(self) -> Optional[enums.PayoutSchedule]:
        payout_schedule = self.data.get('payout_schedule')
        return enums.PayoutSchedule(payout_schedule) if payout_schedule else None

    def log_changes(self, operator_id=None):
        """
        Creates AccountHolderChangeLog object to track changes in AccountHolder.
        Data field contains all needed information.
        """
        AccountHolderChangeLog.objects.create(
            account_holder=self,
            operator_id=operator_id,
            data=json.dumps(self.data, sort_keys=True),
        )


class AccountHolderChangeLog(ArchiveModel):
    account_holder = models.ForeignKey(
        AccountHolder,
        related_name='change_logs',
        on_delete=models.CASCADE,
    )

    operator = models.ForeignKey(
        'user.User',
        null=True,
        blank=True,
        on_delete=models.DO_NOTHING,
        db_constraint=False,
    )
    data = models.TextField()

    @cached_property
    def previous(self):
        return AccountHolderChangeLog.objects.filter(
            account_holder_id=self.account_holder_id,
            created__lt=self.created,
        ).last()

    @property
    def diff(self):
        """Compares current data with previous; used in admin."""
        previous = self.previous

        if not previous or not previous.data:
            return 'No previous settings'

        def format_data(data):
            decoded_json = json.loads(data)

            return json.dumps(
                {k: json.dumps(v) for k, v in decoded_json.items()},
                indent=True,
                sort_keys=True,
            )

        try:
            prev_data = format_data(previous.data)
            curr_data = format_data(self.data)
        except ValueError:
            return 'data error'

        differ = Differ()
        _diff = differ.compare(prev_data.split('\n'), curr_data.split('\n'))

        result = '\n'.join((line for line in _diff if line[0] in '+-'))
        result = result.replace('{', '{{').replace('}', '}}')
        return result


class MarketpayNotification(ArchiveModel):
    id = models.AutoField(primary_key=True, db_column='notification_id')
    event_type = models.CharField(max_length=64)
    event_date = models.DateTimeField()
    psp_reference = models.CharField(max_length=80, db_index=True)
    account_holder = models.ForeignKey(
        AccountHolder,
        related_name='notifications',
        null=True,
        on_delete=models.DO_NOTHING,
    )
    payout = models.ForeignKey(
        'Payout',
        related_name='notifications',
        null=True,
        on_delete=models.DO_NOTHING,
    )
    live = models.BooleanField(default=False)
    content = JSONField(default=dict)
    handled_successfully = models.BooleanField(default=None, null=True)
    prev_oper_result = models.IntegerField(blank=True, null=True)

    objects = ArchiveManager()
    all_objects = models.Manager()

    class Meta:
        ordering = ['-id']
        get_latest_by = 'event_date'

    def __str__(self):
        return f'{self.id}, {self.event_type}'


class TransferFundsQuerySet(AutoUpdateQuerySet):
    def pending(self):
        return self.filter(
            status__in=[
                enums.TransferFundsStatus.PREPARED.value,
                enums.TransferFundsStatus.FAILED.value,
            ]
        )


class FundTransfer(UndeletableMixin, ArchiveModel):
    class Meta:
        verbose_name_plural = 'Fund Transfers'

    source_account_code = models.CharField(max_length=64)
    destination_account_code = models.CharField(max_length=64)
    account_holder = models.ForeignKey(
        AccountHolder,
        related_name='fund_transfers',
        null=True,
        on_delete=models.DO_NOTHING,
    )
    currency = models.CharField(max_length=3)
    amount = models.IntegerField()
    merchant_reference = models.CharField(
        max_length=80,
        db_index=True,
        default=create_uuid,
    )
    transfer_code = models.CharField(
        max_length=80,
        choices=consts.FUND_TRANSFER_CHOICES,
    )
    psp_reference = models.CharField(
        max_length=80,
        blank=True,
        null=True,
        db_index=True,
    )
    payout_reference = models.CharField(
        max_length=64,
        blank=True,
    )
    status = models.CharField(
        max_length=1,
        db_index=True,
        choices=enums.TransferFundsStatus.choices(),
    )
    status_changes = JSONField(
        default=list,
    )
    # list(OperationFee.reference)
    # More: https://phalt.github.io/django-api-domains/
    fee_refs = ArrayField(
        models.CharField(max_length=64),
        default=list,
    )

    # list(Prize.reference)
    prize_refs = ArrayField(
        models.CharField(max_length=64),
        default=list,
    )
    source_account_description = models.CharField(
        max_length=256,
        null=False,
        blank=True,
        default='',
    )
    destination_account_description = models.CharField(
        max_length=256,
        null=False,
        blank=True,
        default='',
    )

    objects = BaseArchiveManager.from_queryset(TransferFundsQuerySet)()
    all_objects = SoftDeleteManager()

    def __str__(self):
        return f'{self.id}, {self.formatted_amount()}'

    @property
    def balance(self):
        """
        If destination account code is Booksy, balance is negative - we take money from merchant.
        """
        if self.destination_account_code == settings.MARKET_PAY_MERCHANT_ACCOUNT_CODE:
            return -self.amount  # pylint: disable=invalid-unary-operand-type

        return self.amount

    def formatted_amount(self):
        return format_currency(self.amount / CURRENCY_FACTOR)

    def update_status(self, status_change):
        """Update status and record old values to status_changes field

        :param status_change: TransferFundsStatusChange
        """
        record = {
            'old_status': self.status,
            'old_psp_reference': self.psp_reference,
            'new_data': status_change.data,
        }
        self.status = status_change.status
        self.psp_reference = status_change.psp_reference
        self.status_changes.append(record)

        update_fields = ['status', 'status_changes']
        if status_change.psp_reference is not None:
            update_fields.append('psp_reference')

        self.save(update_fields=update_fields)


class PayoutQuerySet(AutoUpdateQuerySet):
    def notification_pending(self):
        return self.filter(
            notification_sent__isnull=True,
        )


class Payout(UndeletableMixin, ArchiveModel):
    # Kind of ID for Payout. Generated by Adyen
    psp_reference = models.CharField(
        max_length=80,
        blank=True,
        null=True,
        db_index=True,
        unique=True,
    )
    report_date = models.DateField(
        default=datetime.date(1990, 1, 1),
    )
    booking_date = models.DateField(default=today)
    currency = models.CharField(max_length=3, default=settings.CURRENCY_CODE)
    amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, blank=True)

    account_holder = models.ForeignKey(
        AccountHolder,
        related_name='payouts',
        on_delete=models.DO_NOTHING,
        null=True,
    )
    # list(pos.PaymentRow.pnref). PR with standard payments
    payment_refs = ArrayField(
        models.CharField(max_length=64),
        default=list,
    )
    # list(pos.FundTransfer.merchant_reference)
    fee_refs = ArrayField(
        models.CharField(max_length=500),
        default=list,
    )
    # list(pos.PaymentRow.pnref). PR with refunds
    refund_refs = ArrayField(
        models.CharField(max_length=64),
        default=list,
    )
    # list(pos.PaymentRow.pnref). PR with chargebacks
    chargeback_refs = ArrayField(
        models.CharField(max_length=64),
        default=list,
    )
    # list(pos.FundTransfer.merchant_reference)
    transfer_fund_refs = ArrayField(
        models.CharField(max_length=64),
        default=list,
    )
    notification_sent = models.DateTimeField(null=True)

    objects = BaseArchiveManager.from_queryset(PayoutQuerySet)()
    all_objects = SoftDeleteManager()

    def __str__(self):
        return f'id:{self.id}, Account Holder {self.account_holder_id}: {self.amount} {self.currency}'  # pylint: disable=line-too-long

    def formatted_amount(self):
        return format_currency(self.amount)


class ManualPendingFundsPayout(UndeletableMixin, ArchiveModel):
    account_holder = models.ForeignKey(
        AccountHolder,
        related_name='manual_payouts',
        on_delete=models.DO_NOTHING,
        null=False,
    )
    currency = models.CharField(max_length=3, default=settings.CURRENCY_CODE)
    amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, blank=True)
    psp_reference = models.CharField(max_length=80, db_index=True, null=True, blank=True)
    payment_refs = ArrayField(
        models.CharField(max_length=64),
        default=list,
    )
    refund_refs = ArrayField(
        models.CharField(max_length=64),
        default=list,
    )
    chargeback_refs = ArrayField(
        models.CharField(max_length=64),
        default=list,
    )
    paid_out = models.DateField(null=True, blank=True)
    successful = models.BooleanField(null=True, blank=True, default=None)

    all_objects = SoftDeleteManager()


class MarketplaceAccountingQuerySet(AutoUpdateQuerySet):
    def payouts(self):
        return self.filter(
            record_type=consts.RECORD_TYPE_PAYOUT,
        )


class MarketplaceAccounting(ArchiveModel):
    class Meta:
        unique_together = ('psp_reference', 'record_type')
        index_together = ('account_code', 'booking_date')

    id = models.AutoField(primary_key=True)
    psp_reference = models.CharField(max_length=64)
    report_date = models.DateField(default=today, db_index=True)
    merchant = models.CharField(max_length=64)
    account_holder_code = models.CharField(max_length=64)
    account_code = models.CharField(max_length=64)
    payment_merchant_reference = models.CharField(max_length=64, blank=True)
    capture_merchant_reference = models.CharField(max_length=64, blank=True)
    chargeback_psp_reference = models.CharField(max_length=64, blank=True)
    booking_date = models.DateTimeField()
    record_type = models.CharField(max_length=64)
    amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, blank=True)
    currency = models.CharField(max_length=3)

    objects = models.Manager.from_queryset(MarketplaceAccountingQuerySet)()

    def __str__(self):
        return f'{self.id}, {self.report_date}, {self.record_type}, {self.account_holder_code}: {self.amount} {self.currency}'  # pylint: disable=line-too-long


class ManualPayout(UndeletableMixin, ArchiveModel):
    referral_reward = models.ForeignKey(
        B2BReferralReward,
        on_delete=models.DO_NOTHING,
        null=True,
    )
    business = models.ForeignKey(
        'business.Business',
        on_delete=models.DO_NOTHING,
        null=True,
    )
    request = JSONField()
    response = JSONField(blank=True, null=True)

    all_objects = SoftDeleteManager()

    @staticmethod
    def _get_prepared_business_name(business):
        """Returns business name without special characters and spaces."""
        return ''.join(c for c in business.name if c.isalnum())

    @classmethod
    def create_request(  # pylint: disable=too-many-arguments
        cls,
        business: Business,
        amount: Decimal,
        bic: str,
        iban: str,
        owner_name: str,
        shopper_statement: str,
    ) -> dict:
        """Creates body request.

        :param business: Business related to payout
        :param amount: amount of payment. Major unit
        :param bic: BIC, nothing special
        :param iban: Iban, nothing special
        :param owner_name: Full name of Bank Account owner
        :param shopper_statement: Kind of transfer title
        :return: request body
        """
        business_name = cls._get_prepared_business_name(business)

        return {
            "bank": {
                "bic": bic,
                "countryCode": settings.API_COUNTRY.upper(),
                "iban": iban,
                "ownerName": owner_name,
            },
            "amount": {
                "value": minor_unit(amount),
                "currency": settings.CURRENCY_CODE,
            },
            "recurring": {
                "contract": "PAYOUT",
            },
            "reference": f"{tznow().strftime('%y%m%d')} - {id_to_external_api(business.id)}",
            "merchantAccount": settings.ADYEN_MERCHANT_ACCOUNT,
            "shopperReference": business_name,
            "shopperEmail": business.owner.email,
            "shopperStatement": shopper_statement,
        }

    @classmethod
    def create_referral_reward_payout(
        cls,
        referral_reward: B2BReferralReward,
        bic: str,
        iban: str,
        owner_name: str,
    ):
        """Creates manual payout for reward.

        :param referral_reward: B2BReferralReward related to payout
        :param bic: BIC, nothing special
        :param iban: Iban, nothing special
        :param owner_name: Full name of Bank Account owner
        """

        request = cls.create_request(
            business=referral_reward.business,
            amount=referral_reward.amount,
            bic=bic,
            iban=iban,
            owner_name=owner_name,
            shopper_statement='Booksy reward',
        )

        return cls.objects.create(referral_reward=referral_reward, request=request)

    def send(self):
        """Sends manual payout.

        Result will be saved in response field.
        If there is related referral_reward and everything was ok, mark it as
        paid.
        """
        response = make_request(self.request, settings.ADYEN_MANUAL_PAY_OUT_URL, manual_payout=True)

        if response.json():
            self.response = {'body': response.json(), 'status_code': response.status_code}
            self.save(update_fields=['response'])

            if response.status_code == status.HTTP_200_OK and self.referral_reward:
                self.referral_reward.mark_as_paid_manually()
