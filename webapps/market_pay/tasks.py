from lib.celery_tools import celery_task


@celery_task
def send_manual_reward_payout_task(referral_reward_id: int, **kwargs):
    """Sends manual payout for selected referral reward."""
    from webapps.b2b_referral.models import B2BReferralReward, Prize
    from webapps.market_pay.models import ManualPayout

    referral_reward = B2BReferralReward.objects.get(id=referral_reward_id)

    Prize.objects.filter(
        referral_reward=referral_reward,
        settled=False,
    ).update(settled=True)

    manual_payout = ManualPayout.create_referral_reward_payout(referral_reward, **kwargs)
    manual_payout.send()
