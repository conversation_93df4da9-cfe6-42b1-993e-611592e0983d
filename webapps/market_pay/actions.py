import uuid

from lib.events import lazy_event_receiver
from lib.point_of_sale.events import basket_payment_refunded_event
from webapps.market_pay.notifications import (
    RefundCustomerNotification,
)
from webapps.pos.events import (
    refund_received_event,
)
from webapps.pos.models import PaymentRow


__all__ = [
    'send_refund_customer_notification',
    'send_refund_customer_notification_deprecated',
]


@lazy_event_receiver(refund_received_event)
def send_refund_customer_notification_deprecated(payment_row: PaymentRow, **_kwargs):
    RefundCustomerNotification(payment_row).send()


@lazy_event_receiver(basket_payment_refunded_event)
def send_refund_customer_notification(basket_payment_id: uuid.UUID, **_kwargs):
    payment_row = PaymentRow.objects.filter(basket_payment_id=basket_payment_id).last()
    RefundCustomerNotification(payment_row).send()
