import json
import logging

from django.conf import settings
from django.contrib import admin
from django.db import models
from django.db.models import Q
from django.template import loader
from django.urls.base import reverse as url_reverse
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from django.utils.translation import gettext_lazy as _
from elasticsearch_dsl import AttrList

import webapps.pop_up_notification.models as pop_up_models
from lib import safe_json
from lib.admin_helpers import (
    BaseModelAdmin,
    BusinessSearchQueryFieldsMixin,
    NoAddDelMixin,
    NoAddMixin,
    NoRowsInListViewMixin,
    ReadOnlyTabular,
    admin_link,
    format_as_json,
    manual_admin_link,
)
from lib.elasticsearch.admin import DocumentAdmin, DocumentAsModel
from lib.rangefilter import CompactDateTimeRangeFilter
from lib.tools import CachingPaginator, get_available_language_from_language_code, sget_v2
from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin
from webapps.booking.models import Appointment
from webapps.business.models import Business
from webapps.notification.consts import NO_INFORMATION
from webapps.notification.elasticsearch import NotificationDocument, NotificationHistoryDocument
from webapps.notification.enums import (
    NotificationIcon,
    NotificationSendStatus,
    NotificationService,
    NotificationStatus,
)
from webapps.notification.models import (
    FreeSMSBunch,
    LimitedSMSBunch,
    NotificationEmailCodes,
    NotificationHistory,
    NotificationSchedule,
    NotificationSMSCodes,
    NotificationSMSStatistics,
    Reciever,
    SpecialDay,
    UserNotification,
)
from webapps.user.groups import GroupNameV2

admin_logger = logging.getLogger('booksy.admin')


# not efficient filter by type of notification
class TypeNotificationFilter(admin.SimpleListFilter):
    title = _('Auto notification types')
    # Parameter for the filter that will be used in the URL query.
    parameter_name = 'type'

    # CONST; APP -stands for appointment
    # all actions taken from scenarios_booking.BookingChangedScenario.plan
    # REMINDER
    TO_CUSTOMER_APP_REMINDER = 'customer_booking_reminder'
    TO_CUSTOMER_BOOKSY_PAY_BEFORE_APPOINTMENT_STARTED_REMINDER = (
        'customer_booksy_pay_before_appointment_started_reminder'
    )
    TO_CUSTOMER_BOOKSY_PAY_BEFORE_APPOINTMENT_FINISHED_REMINDER = (
        'customer_booksy_pay_before_appointment_finished_reminder'
    )
    TO_CUSTOMER_TAP_TO_PAY_BEFORE_APPOINTMENT_STARTED_REMINDER = (
        'customer_tap_to_pay_before_appointment_started_reminder'
    )
    # CONFIRMATION
    TO_CUSTOMER_APP_CONFIRMATION = 'customer_booking_confirmation'
    TO_BUSINESS_APP_CONFIRMATION = 'business_booking_confirmation'
    TO_CUSTOMER_APP_CONFIRMATION_MANUAL = 'customer_booking_confirmation_manual'
    # CANCEL
    TO_BUSINESS_APP_CANCEL = 'business_booking_cancel'
    TO_CUSTOMER_APP_CANCEL = 'customer_booking_cancel'
    TO_BUSINESS_APP_CANCEL_BY_BUSINESS = 'business_booking_cancel_by_business'
    # DECLINE
    TO_CUSTOMER_APP_DECLINE = 'customer_booking_decline_manual'
    # RESCHEDULED
    TO_BUSINESS_APP_RESCHEDULED = 'business_booking_rescheduled'
    TO_CUSTOMER_APP_RESCHEDULED_REQUEST = 'customer_booking_reschedule_request'
    TO_BUSINESS_APP_RESCHEDULED_REQUEST = 'business_booking_reschedule_request'
    TO_BUSINESS_APP_RESCHEDULED_RESPONSE = 'business_booking_reschedule_response'
    # TIPPING FAKE DOOR
    TO_CUSTOMER_TIPPING_EXPERIMENT = 'tipping_experiment_push'
    # List of actions with readable names
    LIST_ACTIONS = (
        (TO_CUSTOMER_APP_REMINDER, _('To customer. Reminder appointment')),
        (
            TO_CUSTOMER_BOOKSY_PAY_BEFORE_APPOINTMENT_STARTED_REMINDER,
            _('To customer. Reminder booksy pay before appointment started'),
        ),
        (
            TO_CUSTOMER_BOOKSY_PAY_BEFORE_APPOINTMENT_FINISHED_REMINDER,
            _('To customer. Reminder booksy pay before appointment finished'),
        ),
        (
            TO_CUSTOMER_TAP_TO_PAY_BEFORE_APPOINTMENT_STARTED_REMINDER,
            _('To customer. Reminder about availability of Tap To Pay before appointment started'),
        ),
        (TO_CUSTOMER_APP_CONFIRMATION, _('To customer. Confirmation appointment')),
        (TO_BUSINESS_APP_CONFIRMATION, _('To business. Confirmation appointment')),
        (TO_CUSTOMER_APP_CONFIRMATION_MANUAL, _('To customer. Manual confirmation appointment')),
        (TO_BUSINESS_APP_CANCEL, _('To business. Cancel appointment')),
        (TO_CUSTOMER_APP_CANCEL, _('To customer. Cancel appointment')),
        (TO_BUSINESS_APP_CANCEL_BY_BUSINESS, _('To business. Cancel appointment by business')),
        (TO_CUSTOMER_APP_DECLINE, _('To business. Cancel appointment by business')),
        (TO_BUSINESS_APP_RESCHEDULED, _('To business. Rescheduled appointment')),
        (TO_CUSTOMER_APP_RESCHEDULED_REQUEST, _('To customer. Rescheduled appointment request')),
        (TO_BUSINESS_APP_RESCHEDULED_REQUEST, _('To business. Rescheduled appointment request')),
        (TO_BUSINESS_APP_RESCHEDULED_RESPONSE, _('To business. Rescheduled appointment response')),
        (TO_CUSTOMER_TIPPING_EXPERIMENT, _('To customer. Tipping experiment push')),
    )

    def lookups(self, request, model_admin):
        """
        Returns a list of tuples. The first element in each
        tuple is the coded value for the option that will
        appear in the URL query. The second element is the
        human-readable name for the option that will appear
        in the right sidebar.
        """
        return TypeNotificationFilter.LIST_ACTIONS

    def queryset(self, request, queryset):
        """
        Returns the filtered queryset based on the value
        provided in the query string and retrievable via
        `self.value()`.
        """
        if self.value():
            queryset = queryset.filter(task_id__icontains=self.value())
        return queryset


class RecieverAdmin(NoRowsInListViewMixin, NoAddMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    list_per_page = 15
    list_display = [
        'id',
        'user_link',
        'business_link',
        'resources_link',
        'user_profile',
        'identifier',
        'language',
    ]
    readonly_fields = [
        'user_link',
        'business_link',
        'resources_link',
        'user_profile',
        'notification_type',
        'identifier',
        'device',
        'is_tablet',
        'is_superuser',
        'created',
        'updated',
        'deleted',
        'last_refresh',
    ]
    fieldsets = (
        (
            None,
            {
                'fields': (
                    'user_link',
                    'business_link',
                    'resources_link',
                    'user_profile',
                    'notification_type',
                    'identifier',
                    'language',
                    'device',
                    'is_tablet',
                    'is_superuser',
                )
            },
        ),
        (
            'metadata',
            {
                'fields': (
                    'created',
                    'updated',
                    'deleted',
                    'last_refresh',
                ),
            },
        ),
    )
    search_fields = [
        '=customer_notifications__profile__user__id',
        '=customer_notifications__profile__user__email',
        '=identifier',
        '=business__id',
    ]
    list_filter = [
        'customer_notifications__type',
        'customer_notifications__profile__profile_type',
        'created',
        'updated',
        'deleted',
        'last_refresh',
        'is_tablet',
    ]

    def get_queryset(self, request):  # pylint: disable=duplicate-code
        return (
            self.model.all_objects.get_queryset()
            .select_related(
                'business',
                'business__owner',
                'customer_notifications',
                'customer_notifications__profile',
                'customer_notifications__profile__user',
            )
            .prefetch_related(
                'resources',
            )
        )

    @staticmethod
    def user_link(obj):
        return obj.customer_notifications.profile.user.admin_id_link

    user_link.short_description = 'User'
    user_link.admin_order_field = 'customer_notifications__profile__user_id'

    @staticmethod
    def business_link(obj):
        biz = obj.business
        if not biz:
            return '-'
        return obj.business.admin_id_link

    business_link.short_description = 'Business'
    business_link.admin_order_field = 'business_id'

    @staticmethod
    def resources_link(obj):
        return format_html(', '.join(res.admin_id_link for res in obj.resources.all()))

    resources_link.short_description = 'Resources'
    resources_link.admin_order_field = 'resources__id'

    @staticmethod
    def user_profile(obj):
        return obj.customer_notifications.profile.get_profile_type_display()

    user_profile.short_description = 'Profile'
    user_profile.admin_order_field = 'customer_notifications__profile__profile_type'

    @staticmethod
    def notification_type(obj):
        return obj.customer_notifications.get_type_display()

    notification_type.short_description = 'Notification Type'
    notification_type.admin_order_field = 'customer_notifications__type'

    def delete_model(self, request, obj):
        obj.soft_delete()


class TaskTypeListFilter(admin.SimpleListFilter):
    title = 'Task type'

    # Parameter for the filter that will be used in the URL query.
    parameter_name = 'task_type'

    def lookups(self, request, model_admin):
        """
        Returns a list of tuples. The first element in each
        tuple is the coded value for the option that will
        appear in the URL query. The second element is the
        human-readable name for the option that will appear
        in the right sidebar.
        """
        return (
            ('waitlist', 'Wait list'),
            ('booking_changed', 'Booking changed'),
            ('booking_other', 'Booking other'),
            ('sms_blast', 'SMS blast'),
            ('sms_gate', 'SMS gate'),
            ('template_test', 'Template test'),
            ('no_show_proposition', 'No show proposition'),
        )

    def queryset(self, request, queryset):
        """
        Returns the filtered queryset based on the value
        provided in the query string and retrievable via
        `self.value()`.
        """
        mapping_value_task_type = {
            'waitlist': NotificationHistory.TASK_TYPE__SMS_WAIT_LIST,
            'booking_changed': NotificationHistory.TASK_TYPE__BOOKING_CHANGED,
            'booking_other': NotificationHistory.TASK_TYPE__BOOKING_OTHER,
            'sms_blast': NotificationHistory.TASK_TYPE__SMS_BLAST,
            'sms_gate': NotificationHistory.TASK_TYPE__SMS_GATE,
            'template_test': NotificationHistory.TASK_TYPE__TEMPLATE_TEST,
            'no_show_proposition': NotificationHistory.TASK_TYPE__NO_SHOW_PROPOSITION,
        }
        if self.value():
            task_type = mapping_value_task_type[self.value()]
            return queryset.filter(task_type=task_type)
        return queryset


class NotificationHistoryAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    paginator = CachingPaginator
    list_display = [
        'id',
        'title',
        'type',
        'sender',
        'business_id',
        'customer_id',
        'appointment_id',
        'booking_id',
        'customer_card_id',
        'task_type',
        'task_id',
        'created',
        'recipient',
        'is_blast',
        'status',
    ]
    list_per_page = 10
    # date_hierarchy = 'created'  # too damn slow
    search_fields = [
        '=id',
        '=appointment_id',
        '=business_id',
        '=customer_id',
        '=booking_id',
        '=customer_card_id',
        '=task_id',
        '=recipient_phone',
        '=recipient_email',
    ]
    show_full_result_count = False
    list_filter = ['sender', TaskTypeListFilter, 'status']
    exclude = ('content',)  # use content_preview
    readonly_fields = [
        'id',
        'created',
        'created_business_tz',
        'type',
        'sender',
        'task_id',
        'appointment_id',
        'booking_id',
        'business_id',
        'customer_id',
        'customer_card_id',
        'parameters',
        'sms_count',
        'sms_cost',
        'title',
        'task_link',
        'content_preview',
        'metadata',
        'recipient_phone',
        'recipient_email',
        'task_type',
        'free_sms_bunch_id',
        'status',
    ]

    query_fields = [
        'id',
        'task_id',
        'business_id',
        'customer_id',
        'appointment_id',
        'booking_id',
        'customer_card_id',
        'recipient_email',
        'recipient_phone',
    ]
    hide_keyword_field = True

    @staticmethod
    def recipient(obj):
        if obj.type == UserNotification.PUSH_NOTIFICATION:
            return 'PUSH'
        if obj.type == UserNotification.SMS_NOTIFICATION:
            return obj.recipient_phone
        if obj.type == UserNotification.EMAIL_NOTIFICATION:
            return obj.recipient_email
        return '--'

    @staticmethod
    def task_link(obj):
        try:
            if obj.task_id is None:
                return None
            schedule = NotificationSchedule.objects.only('id').get(task_id=obj.task_id)
            return format_html(
                '<a href="{}">{}</a>',
                url_reverse('admin:notification_notificationschedule_change', args=[schedule.id]),
                obj.task_id,
            )
        except Exception as e:  # pylint: disable=broad-except
            return str(e)

    @staticmethod
    def content_preview(obj):
        if "BGC_Purchase" in (sget_v2(obj, ['task_id']) or ''):
            return "-"
        return format_html(obj.content)

    @staticmethod
    def created_business_tz(obj):
        business = Business.objects.filter(id=obj.business_id).first()
        if not business:
            return '(None)'

        tz = business.get_timezone()
        created = obj.created.astimezone(tz)
        return created.strftime('%Y-%m-%d %H:%M:%S')

    created_business_tz.short_description = 'Created (business TZ)'

    @staticmethod
    def is_blast(obj):
        return bool(obj.blast_id)


class BusinessIdInputFilter(admin.SimpleListFilter):
    template = 'admin/input_filter.html'
    title = parameter_name = placeholder = 'business_id'

    def lookups(self, request, model_admin):
        # Dummy, required to show the filter, but not used.
        return (('', ''),)

    def queryset(self, request, queryset):
        business_id = self.value()
        if business_id:
            try:
                business_id = int(business_id)
            except ValueError:
                from django.contrib import messages

                messages.error(request, '"business_id" filter value need to be integer!')
                return queryset
            appointments_ids = list(
                Appointment.objects.filter(
                    business_id=business_id,
                ).values_list('id', flat=True)
            )
            queryset = queryset.filter(
                Q(
                    parameters__has_key='business_id',
                    parameters__business_id=int(business_id),
                )
                | Q(
                    parameters__has_key='appointment_id',
                    parameters__appointment_id__in=appointments_ids,
                )
            )
        return queryset


class AppointmentIdInputFilter(admin.SimpleListFilter):
    template = 'admin/input_filter.html'
    title = parameter_name = placeholder = 'appointment_id'

    def lookups(self, request, model_admin):
        # Dummy, required to show the filter, but not used.
        return (('', ''),)

    def queryset(self, request, queryset):
        appointment_id = request.GET.get(self.parameter_name)
        if appointment_id:
            queryset = queryset.filter(
                parameters__has_key=self.parameter_name,
                parameters__appointment_id=int(appointment_id),
            )
        return queryset


class NotificationScheduleAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    paginator = CachingPaginator
    list_display = [
        'id',
        'state',
        'task_id',
        'scheduled',
        'finished',
        'scheduled_tz',
        'finished_tz',
        'parameters',
    ]
    list_per_page = 10
    # date_hierarchy = 'scheduled'  # too damn slow
    search_fields = ['=id', 'task_id']
    show_full_result_count = False
    list_filter = [
        BusinessIdInputFilter,
        AppointmentIdInputFilter,
        'state',
    ]
    readonly_fields = [
        'created',
        'updated',
        'show_history',
        'json_template_variables',
    ]

    def scheduled_tz(self, obj):
        return self.get_datetime_in_user_timezone(obj.scheduled)

    scheduled_tz.short_description = 'Scheduled (user tz)'
    scheduled_tz.admin_order_field = 'scheduled'

    def finished_tz(self, obj):
        return self.get_datetime_in_user_timezone(obj.finished)

    finished_tz.short_description = 'Finished (user tz)'
    finished_tz.admin_order_field = 'finished'

    @staticmethod
    def show_history(obj):
        template = loader.get_template('admin/fields/field__show_history.html')
        ctx = {
            'history': NotificationHistory.objects.filter(
                task_id=obj.task_id,
            ).order_by('created')
        }
        return template.render(ctx)

    def json_template_variables(self, obj):
        field_resize_hack = mark_safe(  # nosemgrep: avoid-mark-safe
            """<script type="text/javascript">"""
            """$(function() {"""
            """$('#id_result').css({width: '100%', height: '500px'});"""
            """})"""
            """</script>"""
        )
        try:
            return field_resize_hack + self.json_template_variables_do(obj)
        except Exception:  # pylint: disable=broad-except
            return field_resize_hack

    json_template_variables.short_description = (
        'Template Variables JSON (note: it\'s calculated now)'
    )

    @staticmethod
    def json_template_variables_do(obj):
        # pylint: disable=unbalanced-tuple-unpacking
        from webapps.notification.scenarios.base import Scenario
        from webapps.notification.scenarios.scenarios_booking_mixin import BookingMixin

        _params, booking = Scenario.get_params_objects(json.loads(obj.parameters), 'booking')
        language = get_available_language_from_language_code(settings.LANGUAGE_CODE)
        if obj.task_id.startswith('booking_changed:'):
            _calculate_booking_changes = 'reschedule' in obj.task_id
            variables = BookingMixin.get_booking_template_variables(
                booking,
                language,
                _calculate_booking_changes=_calculate_booking_changes,
            )
            text = safe_json.dumps(variables, pretty=True)
        else:
            text = ''
        return format_html('<pre>{}</pre>', text)


class NotificationSMSCodesAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    list_display = [
        'id',
        'created',
        'phone',
        'sms_code',
        'abuse_ip_address',
        'abuse_fingerprint',
    ]
    list_per_page = 100
    date_hierarchy = 'created'
    search_fields = ['=id', 'phone', 'sms_code', 'metadata']


class NotificationEmailCodesAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    list_display = [
        'id',
        'user',
        'created',
        'email_address',
        'confirmation_code',
    ]
    list_per_page = 100
    date_hierarchy = 'created'
    search_fields = ['=id', 'email_address', 'confirmation_code', 'user']


class NotificationDocumentAdmin(GroupPermissionMixin, DocumentAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    list_display = ['notif_type', 'business_id', 'icon', 'status', 'date']
    list_filter = ['status', 'icon']
    search_fields = ['=business_id', '=notif_type']
    query_fields = ['notif_type', 'business_id']
    ordering = ['-created']
    hide_keyword_field = True


class NotificationDocumentAsModel(DocumentAsModel):
    document = NotificationDocument
    fields = {
        "created": models.DateTimeField(),
        "updated": models.DateTimeField(),
        "business_id": models.IntegerField(),
        "user_id": models.IntegerField(),
        "content": models.TextField(),
        "crucial": models.BooleanField(),
        "relevance": models.IntegerField(),
        "icon": models.CharField(choices=NotificationIcon.choices()),
        "status": models.CharField(choices=NotificationStatus.choices()),
        "date": models.DateField(),
        "notif_type": models.CharField(),
    }

    class Meta:
        verbose_name = _('Business Notification')
        verbose_name_plural = _('Business Notifications')


class NotificationHistoryDocumentAdmin(GroupPermissionMixin, NoRowsInListViewMixin, DocumentAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    list_display = [
        '_id',
        'type',
        'title',
        'sender',
        'business_id',
        'customer_id',
        'booking_id',
        'customer_card_id',
        'task_type',
        'task_id',
        'created',
        'recipient',
        'is_blast',
        'status',
        'service',
        'external_entity_admin_link',
    ]
    sortable_by = [
        '_id',
        'type',  #'title',
        'sender',
        'business_id',
        'customer_id',
        'booking_id',
        'customer_card_id',
        'task_type',
        'task_id',
        'created',
        'recipient',
        'is_blast',
        'status',
    ]
    list_filter = [
        'type',
        'sender',
        'task_type',
        'status',
        'service',
        ('created', CompactDateTimeRangeFilter),
    ]
    # search_fields = ['task_id', '=business_id']
    search_fields = [
        '=_id',
        '=business_id',
        '=customer_id',
        '=booking_id',
        '=customer_card_id',
        '=task_id',
        '=recipient_phone',
        '=recipient_email',
    ]
    ordering = ['-created']

    readonly_fields = [
        '_id',
        'created',
        'created_business_tz',
        'type',
        'sender',
        'task_id',
        'booking_id',
        'business_id',
        'customer_id',
        'customer_card_id',
        'parameters_json',
        'sms_count',
        'sms_cost',
        'title',
        'task_link',
        'content_preview',
        'metadata_json',
        'recipient_phone',
        'recipient_email',
        'task_type',
        'free_sms_bunch_id',
        'status',
        'service',
        'task_type',
        'external_id',
        'errors_json',
        'blast_send_type',
    ]

    list_per_page = 10
    show_full_result_count = False
    hide_keyword_field = True

    def _get_empty_queryset_if_no_filters(self, request):
        return NotificationHistory.objects.none()

    @staticmethod
    def recipient(obj):  # pylint: disable=too-many-return-statements
        if obj.type == UserNotification.PUSH_NOTIFICATION:
            return 'PUSH'
        if obj.type == UserNotification.SMS_NOTIFICATION:
            return obj.recipient_phone
        if obj.type == UserNotification.EMAIL_NOTIFICATION:
            return obj.recipient_email
        if obj.type == UserNotification.CUSTOMER_POPUP_NOTIFICATION:
            return f'Customer:{obj.customer_id}'
        if obj.type == UserNotification.POPUP_NOTIFICATION:
            return f'Business:{obj.business_id}'
        return '--'

    @staticmethod
    def task_link(obj):
        try:
            if obj.task_id is None:
                return None
            schedule = NotificationSchedule.objects.only('id').get(task_id=obj.task_id)
            return format_html(
                '<a href="{}">{}</a>',
                url_reverse('admin:notification_notificationschedule_change', args=[schedule.id]),
                obj.task_id,
            )
        except Exception as e:  # pylint: disable=broad-except
            return str(e)

    @staticmethod
    def content_preview(obj):
        if "BGC_Purchase" in (sget_v2(obj, ['task_id']) or ''):
            return "-"
        return format_html(obj.content)

    @staticmethod
    def created_business_tz(obj):
        business = Business.objects.filter(id=obj.business_id).first()
        if not business:
            return '(None)'

        tz = business.get_timezone()
        created = obj.created.astimezone(tz)
        return created.strftime('%Y-%m-%d %H:%M:%S')

    created_business_tz.short_description = 'Created (business TZ)'

    @staticmethod
    def is_blast(obj):
        return bool(obj.blast_id)

    @staticmethod
    def metadata_json(obj):
        return format_as_json(obj.metadata.to_dict())

    metadata_json.short_description = 'metadata'

    @staticmethod
    def parameters_json(obj):
        return format_as_json(obj.parameters.to_dict())

    parameters_json.short_description = 'parameters'

    @staticmethod
    def errors_json(obj):
        if isinstance(obj.errors, AttrList):
            errors = [
                error.to_dict() for error in obj.errors._l_  # pylint: disable=protected-access
            ]
        else:
            errors = obj.errors.to_dict()
        return format_as_json(errors)

    def external_entity_admin_link(self, obj):
        try:
            return self._external_entity_admin_link(obj)
        except AttributeError:
            return NO_INFORMATION

    @staticmethod
    def _external_entity_admin_link(obj):
        other_entity = NotificationHistoryDocumentAdmin._get_other_entity_details(obj)
        if original_document := getattr(other_entity, 'original_document', None):
            if original_document == 'NotificationDocument':
                return NotificationHistoryDocumentAdmin._get_notification_document_link(obj)
            if getattr(pop_up_models, original_document, None):
                return NotificationHistoryDocumentAdmin._get_customer_pop_up_entity_admin_link(obj)
        return NO_INFORMATION

    @staticmethod
    def _get_notification_document_link(obj):
        other_entity = NotificationHistoryDocumentAdmin._get_other_entity_details(obj)
        if not other_entity:
            return NO_INFORMATION
        document_id = other_entity.original_document_id
        if not NotificationDocument._get_by_search(document_id):  # pylint: disable=protected-access
            return NO_INFORMATION
        document_type = getattr(other_entity, 'type', NO_INFORMATION)
        return format_html(
            '<a href="{}">{}</a>',
            manual_admin_link(
                app_name='notification',
                obj_class=NotificationDocument.__name__,
                obj_id=document_id,
            ),
            f'{document_type}:{document_id}',
        )

    @classmethod
    def _get_customer_pop_up_entity_admin_link(cls, obj):
        other_entity = cls._get_other_entity_details(obj)
        original_document = getattr(other_entity, 'original_document', None)
        document_id = getattr(other_entity, 'original_document_id', None)
        if not original_document or not document_id:
            return NO_INFORMATION
        if popup_db_model := cls._get_popup_model(original_document):
            obj = popup_db_model.objects.filter(id=document_id).first()
            if obj:
                return format_html(
                    '<a href="{}">{}:{}</a>',
                    admin_link(obj),
                    popup_db_model.__name__,
                    document_id,
                )
        return NO_INFORMATION

    @staticmethod
    def _get_other_entity_details(obj):
        if not obj.metadata:
            return None
        return getattr(obj.metadata, 'other_entity', None)

    @staticmethod
    def _get_popup_model(original_document):
        if not original_document:
            return None
        return getattr(pop_up_models, original_document, None)

    errors_json.short_description = 'errors'
    external_entity_admin_link.short_description = 'External admin link'


class NotificationHistoryAsModel(DocumentAsModel):
    class Meta:
        verbose_name = _('Notification History Entry')
        verbose_name_plural = _('Notification History Entries')

    document = NotificationHistoryDocument
    fields = {
        'type': models.CharField(choices=UserNotification.NOTIFICATION_TYPES),
        'sender': models.CharField(choices=NotificationHistory.SENDER_TYPES),
        'task_type': models.CharField(choices=NotificationHistory.TASK_TYPE_CHOICES),
        'status': models.CharField(
            choices=NotificationSendStatus.choices(),
        ),
        'service': models.CharField(
            choices=NotificationService.choices(),
        ),
    }
    list_display = ['date', 'name']


class SpecialDayAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    list_display = ['date', 'name']


class NotificationSMSStatisticsAdmin(
    BusinessSearchQueryFieldsMixin, ReadOnlyTabular, GroupPermissionMixin, BaseModelAdmin
):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    list_display = ['id', 'business', 'date', 'parts_count', 'sms_count', 'created', 'updated']

    list_select_related = True
    list_per_page = 20


class FreeSMSBunchAdmin(
    BusinessSearchQueryFieldsMixin, ReadOnlyTabular, GroupPermissionMixin, BaseModelAdmin
):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    list_display = ['id', 'business', 'cell_phone', 'sms_count', 'sms_type', 'updated']
    list_select_related = True
    list_per_page = 20


class LimitedSMSBunchAdmin(
    BusinessSearchQueryFieldsMixin, ReadOnlyTabular, GroupPermissionMixin, BaseModelAdmin
):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    list_display = ['id', 'business', 'sms_count', 'sms_type', 'updated']
    list_select_related = True
    list_per_page = 20


admin.site.register(NotificationHistory, NotificationHistoryAdmin)
admin.site.register(NotificationSchedule, NotificationScheduleAdmin)
admin.site.register(NotificationSMSCodes, NotificationSMSCodesAdmin)
admin.site.register(NotificationEmailCodes, NotificationEmailCodesAdmin)
admin.site.register(Reciever, RecieverAdmin)

admin.site.register(NotificationDocumentAsModel, NotificationDocumentAdmin)
admin.site.register(NotificationHistoryAsModel, NotificationHistoryDocumentAdmin)
admin.site.register(SpecialDay, SpecialDayAdmin)
admin.site.register(LimitedSMSBunch, LimitedSMSBunchAdmin)
admin.site.register(FreeSMSBunch, FreeSMSBunchAdmin)
admin.site.register(NotificationSMSStatistics, NotificationSMSStatisticsAdmin)
