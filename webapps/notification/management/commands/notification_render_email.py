from pathlib import Path
from unittest.mock import Mock

from django.core.management.base import BaseCommand

from webapps.notification.channels import EmailChannel


class Command(BaseCommand):
    def add_arguments(self, parser) -> None:
        parser.add_argument(
            "template",
            help=(
                "Name of the template.\n"
                "Templates are stored in /templates/notifications/"
                "and the name is a path relative to that directory stripped of an extension."
            ),
        )
        parser.add_argument(
            "-o",
            "--out",
            help="Outputs rendered template to FILE, defaults to stdout.",
            default=None,
            metavar="FILE",
        )

    def handle(self, *args, **kwargs) -> None:
        channel = EmailChannel(Mock())
        template = kwargs.pop("template")
        context = {"STATIC_FULL_URL": "statics/"}
        recipient = ""
        rendered = channel.render_template(template, context, recipient)

        if out := kwargs.pop("out"):
            Path(out).write_text(rendered)
        else:
            print(rendered)
