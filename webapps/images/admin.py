#!/usr/bin/env python
from django import forms
from django.contrib import admin
from django.db.models import ManyToMany<PERSON>ield
from django.utils.html import format_html
from lib.elasticsearch.consts import ESDocType

from lib.elasticsearch.tools import get_by_id

from lib.admin_helpers import BaseModelAdmin
from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin
from webapps.images.models import (
    BusinessPlaceholderImage,
    Image,
    ImageComment,
)
from webapps.user.groups import GroupNameV2


class ChangeListImageForm(forms.ModelForm):
    class Meta:
        model = Image
        fields = ['inspiration_categories']

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if kwargs.get('instance') is not None and kwargs['instance'].business:
            # on edit limit categories to business categories
            # business categories are prefetched
            # so we must build choices here instad on iterating on every loop
            icf = self.fields['inspiration_categories']
            icf.queryset = kwargs['instance'].business.categories.all()
            icf._set_choices(
                [
                    (
                        icf.prepare_value(cat),
                        icf.label_from_instance(cat),
                    )
                    for cat in icf.queryset
                ]
            )

    def has_changed(self):
        # workaround for https://code.djangoproject.com/ticket/11313
        if self.instance.id is None:
            return False
        return bool(self.changed_data)


class ImageAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    list_display = [
        'id',
        'business',
        'created',
        'category',
        'image_thumbnail',
    ]
    readonly_fields = [
        'active',
        'created',
        'updated',
        'link',
        'image_thumbnail',
        'image_display',
        'height',
        'width',
        'image_url',
    ]
    search_fields = [
        'business__name',
        '=business__id',
        'business__owner__email',
    ]
    list_filter = ['category']
    list_per_page = 100
    raw_id_fields = [
        'business',
        'cms_content',
    ]

    formfield_overrides = {
        ManyToManyField: {'widget': forms.CheckboxSelectMultiple},
    }

    def has_add_permission(self, request):
        return False

    def save_model(self, request, obj, form, change):
        if obj.id is None:
            # workaround for https://code.djangoproject.com/ticket/11313
            return
        return super().save_model(request, obj, form, change)

    def save_related(self, request, form, formsets, change):
        if form.instance.id is None:
            # workaround for https://code.djangoproject.com/ticket/11313
            return
        return super().save_related(request, form, formsets, change)

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.prefetch_related(
            'business',
            'business__categories',
            'inspiration_categories',
        )

    def delete_model(self, request, obj):
        es_doc = get_by_id(obj.id, ESDocType.IMAGE, routing=obj.business_id)
        if es_doc is not None:
            es_doc.delete(refresh=True)
        super().delete_model(request, obj)

    def get_form(self, request, obj=None, change=False, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        form.base_fields['inspiration_categories'].widget.can_add_related = False
        if obj is not None and obj.business is not None:
            # on edit limit categories to business categories
            form.base_fields['inspiration_categories'].queryset = obj.business.categories.all()
        return form

    def get_changelist_form(self, request, **kwargs):
        return ChangeListImageForm

    @staticmethod
    def inspiration_categories(obj):
        """Workaround for: (admin.E109)
            The value of 'list_display[5]' must not be a ManyToManyField.

        We must explicitly return prefetched inspiration_categories,
        to show that we know what we are doing.

        """
        return obj.inspiration_categories.all()

    @staticmethod
    def link(obj):
        return format_html('<a href="{url}">{url}</a>', url=obj.full_url)

    @staticmethod
    def image_display(obj):
        return format_html('<img src="{url}"/>', url=obj.full_url)

    @staticmethod
    def image_thumbnail(obj):
        return format_html('<img src="{url}" style="width: 150px"/>', url=obj.thumbnail(150, 150))


class ImageCommentAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    list_display = [
        'id',
        'image_thumbnail',
        'user',
        'profile_type',
        'content',
        'created',
        'updated',
    ]
    search_fields = [
        '=image__id',
        '=user__id',
        'user__email',
    ]
    list_filter = ['profile_type']
    raw_id_fields = ['image', 'user']
    readonly_fields = ['link', 'image_display']
    date_hierarchy = 'created'
    list_per_page = 100

    def has_add_permission(self, request):
        return False

    @staticmethod
    def link(obj):
        return format_html('<a href="{url}">{url}</a>', url=obj.image.full_url)

    @staticmethod
    def image_display(obj):
        return format_html('<img src="{}"/>', obj.image.full_url)

    @staticmethod
    def image_thumbnail(obj):
        return format_html('<img src="{}"/>', obj.image.thumbnail(150, 150))

    def delete_model(self, request, obj):
        es_doc = get_by_id(obj.id, ESDocType.IMAGE_COMMENT)
        if es_doc is not None:
            es_doc.delete(refresh=True)
        super().delete_model(request, obj)


class BusinessPlaceholderImageAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    list_display = ['id', 'category', 'type', 'image_thumb']
    readonly_fields = ['category', 'type', 'image_thumb']

    @staticmethod
    def image_thumb(obj):
        url = f'{obj.image.url}?size=150x150'
        return format_html('<img src="{url}" style="width: 150px"/>', url=url)


admin.site.register(BusinessPlaceholderImage, BusinessPlaceholderImageAdmin)
admin.site.register(Image, ImageAdmin)
admin.site.register(ImageComment, ImageCommentAdmin)
