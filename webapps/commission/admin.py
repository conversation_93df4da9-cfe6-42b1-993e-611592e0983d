from django.contrib import admin

from lib.admin_helpers import (
    BaseModelAdmin,
    ReadOnlyFieldsMixin,
)
from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin
from webapps.commission.models import Commission
from webapps.user.groups import GroupNameV2


class CommissionAdmin(ReadOnlyFieldsMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR, GroupNameV2.BUSINESS_EDITOR)
    read_only_access_groups = ()
    list_display = (
        'id',
        'item_id',
        'business_id',
        'resource_id',
        'amount',
        'type',
        'rate',
        'created',
        'deleted',
    )
    search_fields = [
        '=id',
        '=item_id',
        '=business_id',
        '=resource_id',
    ]

    def get_queryset(self, request):
        """
        Return a QuerySet of all model instances that can be edited by the
        admin site. This is used by changelist_view.
        """
        return Commission.all_objects.all().order_by('-created')


admin.site.register(Commission, CommissionAdmin)
