import json

from django.contrib import admin
from django.forms import Select
from django.utils.html import format_html
from django.urls import re_path as url
from django.urls import reverse
from django.template.defaultfilters import truncatechars

from lib.admin_helpers import (
    BaseModelAdmin,
    NoDelMixin,
    NoAddDelMixin,
    ReadOnlyFieldsMixin,
)
from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin
from webapps.admin_extra.views.wholesaler import WholesalerCommodityImportView
from webapps.pos.models import POS
from webapps.user.groups import GroupNameV2
from webapps.warehouse.models import (
    Barcode,
    Brand,
    Commodity,
    CommodityCategory,
    CommodityStockLevel,
    StocktakingDocument,
    StocktakingDocumentRow,
    Supplier,
    Supply,
    SupplyRow,
    VolumeMeasure,
    Warehouse,
    WarehouseDocument,
    WarehouseDocumentRow,
    WarehouseFormula,
    WarehouseFormulaRow,
    Wholesaler,
    WholesalerCommodity,
)


class AttributeAdminLinkMixin:
    @staticmethod
    def common_attribute_admin_link(obj, attr_name):
        attribute_object = getattr(obj, attr_name, None)
        if attribute_object is None:
            return '-'
        return attribute_object.admin_id_link


class WarehouseBaseAdmin(
    AttributeAdminLinkMixin,
    NoAddDelMixin,
    GroupPermissionMixin,
    BaseModelAdmin,
):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    exclude = ('history_change',)

    @staticmethod
    def history_change_summary(obj):
        return format_html('<pre><code>{}</code></pre>', json.dumps(obj.history_change, indent=4))

    def save_model(self, request, obj, form, change):
        obj.operator_id = request.user.id
        return super().save_model(request, obj, form, change)

    @staticmethod
    def short_business(obj):
        business = getattr(obj, 'business', None)
        if not business:
            return ''
        business_name = truncatechars(business.name, 100)
        return f'Business [{business.external_api_id}] "{business_name}" ({business.owner.email})'


class CommodityAdmin(WarehouseBaseAdmin):
    list_filter = ('product_type',)

    search_fields = (
        '=id',
        '=business__id',
        'name',
        'category__name',
        'business__name',
    )

    list_display = [
        'id',
        'short_name',
        'business_link',
        'category_link',
        'product_type',
        'archived',
    ]

    raw_id_fields = ('category', 'business', 'tax_rate', 'photo')
    readonly_fields = [
        'barcodes',
        'subbookings',
        'suppliers',
        'created',
        'updated',
        'deleted',
        'business',
        'history_change_summary',
    ]

    fieldsets = (
        (
            None,
            {
                'fields': (
                    ('created', 'updated', 'deleted'),
                    'business',
                    'category',
                    'suppliers',
                    'barcodes',
                    'volume_unit',
                    'name',
                    'description',
                    'enable_stock_control',
                )
            },
        ),
        (
            'Price and Tax',
            {
                'classes': ('collapse',),
                'fields': (
                    'net_price',
                    'gross_price',
                    'tax',
                    'tax_rate',
                ),
            },
        ),
        (
            'Details',
            {
                'classes': ('collapse',),
                'fields': (
                    'product_code',
                    'product_type',
                    'archived',
                    'current_net_purchase_price',
                    'total_pack_capacity',
                    'subbookings',
                    'photo',
                ),
            },
        ),
        (
            'History',
            {
                'classes': ('collapse',),
                'fields': ('history_change_summary',),
            },
        ),
    )

    @staticmethod
    def short_name(obj):
        return truncatechars(obj.name, 100)

    def business_link(self, obj):
        return self.common_attribute_admin_link(obj, 'business')

    def category_link(self, obj):
        return self.common_attribute_admin_link(obj, 'category')

    business_link.short_description = 'Business'
    category_link.short_description = 'Category'


class CommodityCategoryAdmin(ReadOnlyFieldsMixin, WarehouseBaseAdmin):

    search_fields = (
        '=id',
        '=business__id',
        'name',
        'business__name',
    )

    list_display = [
        'id',
        'business_link',
        'short_name',
        'order',
        'is_active',
    ]

    exclude = (
        'business',
        'history_change',
    )

    editable_fields = ['name']

    readonly_fields = [
        'business_link',
        'history_change_summary',
    ]

    def get_queryset(self, request):
        return CommodityCategory.all_objects.all().order_by('id')

    def business_link(self, obj):
        return self.common_attribute_admin_link(obj, 'business')

    business_link.short_description = 'Business'

    @staticmethod
    def short_name(obj):
        return truncatechars(obj.name, 100)

    short_name.short_description = 'Name'

    @staticmethod
    def is_active(obj) -> bool:
        return not bool(obj.deleted)

    is_active.boolean = True


class SupplierAdmin(ReadOnlyFieldsMixin, WarehouseBaseAdmin):

    search_fields = (
        '=id',
        '=business__id',
        'name',
        'business__name',
    )

    list_display = [
        'id',
        'short_name',
        'business_link',
        'tax_id_number',
        'email',
        'phone',
        'city',
        'zip_code',
        'address',
        'country',
    ]

    readonly_fields = [
        'business',
        'history_change_summary',
    ]

    @staticmethod
    def short_name(obj):
        return truncatechars(obj.name, 100)

    def business_link(self, obj):
        return self.common_attribute_admin_link(obj, 'business')

    short_name.short_description = 'Name'
    business_link.short_description = 'Business'


class CommodityBarcodeAdmin(ReadOnlyFieldsMixin, WarehouseBaseAdmin):

    search_fields = [
        '=id',
        '=business__id',
        'code',
    ]

    list_display = [
        'id',
        'short_business',
        'code',
    ]

    readonly_fields = [
        'business',
        'history_change_summary',
    ]


class VolumeMeasureAdmin(ReadOnlyFieldsMixin, WarehouseBaseAdmin):
    search_fields = ('=id', 'label')
    list_filter = ('standard',)

    list_display = [
        'id',
        'label',
        'standard',
    ]

    exclude = ('businesses',)

    readonly_fields = [
        'history_change_summary',
    ]


class WarehouseAdmin(WarehouseBaseAdmin):
    ordering = ('business', 'name')
    search_fields = ('=id', '=business__id', 'name')
    list_filter = ('type',)
    list_display = [
        'id',
        'business_link',
        'name',
        'address',
        'zip_code',
        'city',
        'type',
        'deleted',
    ]
    list_per_page = 100
    exclude = ('business', 'history_change')
    readonly_fields = (
        'id',
        'business_link',
        'name',
        'address',
        'zip_code',
        'city',
        'type',
        'history_change_summary',
    )

    def business_link(self, obj):
        return self.common_attribute_admin_link(obj, 'business')

    business_link.short_description = 'Business'


class BrandAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    search_fields = ('=id', '=business__id', 'name')
    list_display = (
        'id',
        'short_business',
        'name',
        'deleted',
    )
    list_per_page = 100
    raw_id_fields = ['business', 'photo']
    readonly_fields = (
        'id',
        'business',
        'name',
        'photo',
    )

    @staticmethod
    def short_business(obj):
        business = obj.business
        if not business:
            return ''
        business_name = truncatechars(business.name, 100)
        return f'Business [{business.external_api_id}] "{business_name}" ({business.owner.email})'


class FormulaAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    search_fields = (
        '=id',
        '=service_variants__id',
        '=subbookings__id',
        '=rows__id',
    )
    list_display = [
        'id',
    ]


class FormulaRowAdmin(AttributeAdminLinkMixin, NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    search_fields = (
        '=id',
        '=commodity__id',
        '=count',
        '=warehouse__id',
    )
    list_display = [
        'id',
        'commodity_link',
        'count',
        'warehouse_link',
    ]

    def commodity_link(self, obj):
        return self.common_attribute_admin_link(obj, 'commodity')

    def warehouse_link(self, obj):
        return self.common_attribute_admin_link(obj, 'warehouse')

    commodity_link.short_description = 'Commodity'
    warehouse_link.short_description = 'Warehouse'


class WholesalerCommoditiesInline(NoAddDelMixin, admin.TabularInline):
    model = WholesalerCommodity
    classes = ['collapse']  # Django-1.10 :)
    extra = 0
    can_delete = False

    fields = (
        'name',
        'product_code',
        'category_name',
        'net_price',
        'tax_rate',
        'tax',
        'gross_price',
        'barcode',
        'barcode_type',
        'total_pack_capacity',
        'volume_unit',
        'description',
        'archived',
        'created',
        'updated',
        'deleted',
    )
    readonly_fields = [
        'name',
        'product_code',
        'category_name',
        'net_price',
        'tax_rate',
        'tax',
        'gross_price',
        'barcode',
        'barcode_type',
        'total_pack_capacity',
        'volume_unit',
        'description',
        'archived',
        'created',
        'updated',
        'deleted',
    ]
    verbose_name = "Wholesaler commodity"
    verbose_name_plural = "Wholesaler commodities"

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.filter(archived=0).order_by('name', 'product_code')

    @classmethod
    def category_name(cls, resource):
        return resource.category.name if resource.category else '-'


class WholesalerAdmin(NoDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    search_fields = ('=id', 'name')
    list_display = [
        'id',
        'name',
        'email',
        'phone',
        'commodities_count',
    ]
    list_per_page = 100
    readonly_fields = [
        'id',
        'commodities_count',
    ]

    change_form_template = 'admin/change_forms/change_form__wholesaler.html'

    @classmethod
    def commodities_count(cls, obj):
        return obj.wholesaler_commodities.filter(archived=0).count()

    commodities_count.admin_order_field = 'commodities_count'

    def get_form(self, request, obj=None, change=False, **kwargs):
        tax_rates = [(t.rate, t.rate) for t in POS.get_default_pos().tax_rates.all()]
        kwargs['widgets'] = {
            'default_tax_rate': Select(choices=tax_rates),
        }
        self.wholesaler_commodities_import = self.get_commodities_import(obj)

        form = super().get_form(request, obj, change, **kwargs)

        return form

    @classmethod
    def get_commodities_import(cls, obj=None):
        if obj is None:
            return ''
        link = reverse("admin:commodities_import", kwargs={'wholesaler_id': obj.id})
        return format_html(
            """
            <a href="{}" class="btn ">
                Commodities Import
            </a>
            """,
            link,
        )

    def get_urls(self):
        urls = super().get_urls()
        wholesaler_import_view = WholesalerCommodityImportView
        additional_urls = [
            url(
                r'^(?P<wholesaler_id>\d+)/commodities_import/$',
                self.admin_site.admin_view(wholesaler_import_view.as_view()),
                name='commodities_import',
            ),
        ]
        return additional_urls + urls


class WholesalerCommodityAdmin(
    ReadOnlyFieldsMixin, NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin
):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    search_fields = ('=id', 'name', 'category__name', 'product_code', 'barcode')
    list_filter = ('wholesaler',)
    list_display = [
        'id',
        'name',
        'wholesaler_name',
        'category_name',
        'product_code',
        'net_price',
        'tax_rate',
        'tax',
        'gross_price',
        'barcode_type',
        'barcode',
        'volume_unit',
        'total_pack_capacity',
        'description',
        'archived',
        'created',
        'updated',
    ]
    list_per_page = 100
    verbose_name = "Wholesaler commodity"
    verbose_name_plural = "Wholesaler commodities"

    @classmethod
    def category_name(cls, resource):
        return resource.category.name if resource.category else '-'

    @staticmethod
    def wholesaler_name(resource):
        return f'{resource.wholesaler.name} (id: {resource.wholesaler.id})'


class CommodityStockLevelAdmin(ReadOnlyFieldsMixin, WarehouseBaseAdmin):
    search_fields = ('=id', 'warehouse__id', 'commodity__id')
    list_per_page = 100
    list_display = [
        'id',
        'warehouse_link',
        'commodity_link',
        'remaining_volume',
        'minimum_packages',
        'maximum_packages',
        'updated',
        'created',
    ]

    @classmethod
    def commodity_name(cls, stock_level):
        return truncatechars(stock_level.commodity.name, 100)

    def warehouse_link(self, obj):
        return self.common_attribute_admin_link(obj, 'warehouse')

    def commodity_link(self, obj):
        return self.common_attribute_admin_link(obj, 'commodity')

    warehouse_link.short_description = 'Warehouse'
    commodity_link.short_description = 'Commodity'


class WarehouseDocumentAdmin(ReadOnlyFieldsMixin, WarehouseBaseAdmin):
    search_fields = ('=id', 'transaction__id', 'warehouse_to__id')
    list_filter = ('type',)
    list_per_page = 100
    list_display = [
        'id',
        'type',
        'transaction',
        'warehouse_to_link',
        'number',
    ]

    def warehouse_to_link(self, obj):
        return self.common_attribute_admin_link(obj, 'warehouse_to')

    warehouse_to_link.short_description = 'Warehouse to'


class WarehouseDocumentRowAdmin(ReadOnlyFieldsMixin, WarehouseBaseAdmin):
    search_fields = ('=id', 'document__id', 'commodity__id')
    list_filter = ('is_full_package_expenditure',)
    list_per_page = 100
    list_display = [
        'id',
        'document_link',
        'commodity_link',
        'quantity',
        'net_price',
        'gross_price',
        'tax',
        'is_full_package_expenditure',
    ]

    def commodity_link(self, obj):
        return self.common_attribute_admin_link(obj, 'commodity')

    def document_link(self, obj):
        return self.common_attribute_admin_link(obj, 'document')

    commodity_link.short_description = 'Commodity'
    document_link.short_description = 'Document'


class StocktakingDocumentAdmin(ReadOnlyFieldsMixin, WarehouseBaseAdmin):
    search_fields = (
        '=id',
        'warehouse__id',
        'issuing_staffer__id',
        'confirming_staffer__id',
    )
    list_per_page = 100
    list_display = [
        'id',
        'warehouse_link',
        'acceptance_date',
        'issue_date',
        'issuing_staffer_link',
        'confirming_staffer_link',
        'number',
    ]

    def warehouse_link(self, obj):
        return self.common_attribute_admin_link(obj, 'warehouse')

    def confirming_staffer_link(self, obj):
        return self.common_attribute_admin_link(obj, 'confirming_staffer')

    def issuing_staffer_link(self, obj):
        return self.common_attribute_admin_link(obj, 'issuing_staffer')

    confirming_staffer_link.short_description = 'Confirming staffer'
    issuing_staffer_link.short_description = 'Issuing staffer'
    warehouse_link.short_description = 'Warehouse'


class StocktakingDocumentRowAdmin(ReadOnlyFieldsMixin, WarehouseBaseAdmin):
    search_fields = ('=id', 'stocktaking__id', 'commodity__id')
    list_filter = ('is_full_package_inventoried',)
    list_per_page = 100
    list_display = [
        'id',
        'stocktaking_link',
        'commodity_object',
        'initial_volume',
        'quantity_inventoried',
        'is_full_package_inventoried',
    ]

    def stocktaking_link(self, obj):
        return self.common_attribute_admin_link(obj, 'stocktaking')

    stocktaking_link.short_description = 'Stocktaking'

    def commodity_object(self, obj):
        return self.common_attribute_admin_link(obj, 'commodity')

    commodity_object.short_description = 'Commodity'


class SupplyAdmin(ReadOnlyFieldsMixin, WarehouseBaseAdmin):
    search_fields = ('=id', 'supplier__id', '=order__id', 'business__id')
    list_filter = ('type',)
    list_per_page = 100
    list_display = [
        'id',
        'type',
        'supplier_link',
        'numbers_from_suppliers',
        'order_link',
        'business_link',
    ]

    def supplier_link(self, obj):
        return self.common_attribute_admin_link(obj, 'supplier')

    def business_link(self, obj):
        return self.common_attribute_admin_link(obj, 'business')

    def order_link(self, obj):
        return self.common_attribute_admin_link(obj, 'order')

    supplier_link.short_description = 'Supplier'
    business_link.short_description = 'Business'
    order_link.short_description = 'Order'


class SupplyRowAdmin(ReadOnlyFieldsMixin, WarehouseBaseAdmin):
    search_fields = ('=id', 'supply__id', 'commodity__id', 'warehouse__id')
    list_per_page = 100
    list_display = [
        'id',
        'supply_link',
        'commodity_link',
        'quantity',
        'net_price',
        'gross_price',
        'tax',
        'warehouse_link',
    ]

    def supply_link(self, obj):
        return self.common_attribute_admin_link(obj, 'supply')

    def commodity_link(self, obj):
        return self.common_attribute_admin_link(obj, 'commodity')

    def warehouse_link(self, obj):
        return self.common_attribute_admin_link(obj, 'warehouse')

    supply_link.short_description = 'Supply'
    commodity_link.short_description = 'Commodity'
    warehouse_link.short_description = 'Warehouse'


admin.site.register(Barcode, CommodityBarcodeAdmin)
admin.site.register(Brand, BrandAdmin)
admin.site.register(Commodity, CommodityAdmin)
admin.site.register(CommodityCategory, CommodityCategoryAdmin)
admin.site.register(CommodityStockLevel, CommodityStockLevelAdmin)
admin.site.register(Supplier, SupplierAdmin)
admin.site.register(VolumeMeasure, VolumeMeasureAdmin)
admin.site.register(Warehouse, WarehouseAdmin)
admin.site.register(WarehouseDocument, WarehouseDocumentAdmin)
admin.site.register(WarehouseDocumentRow, WarehouseDocumentRowAdmin)
admin.site.register(StocktakingDocument, StocktakingDocumentAdmin)
admin.site.register(StocktakingDocumentRow, StocktakingDocumentRowAdmin)
admin.site.register(Supply, SupplyAdmin)
admin.site.register(SupplyRow, SupplyRowAdmin)
admin.site.register(WarehouseFormula, FormulaAdmin)
admin.site.register(WarehouseFormulaRow, FormulaRowAdmin)
admin.site.register(Wholesaler, WholesalerAdmin)
admin.site.register(WholesalerCommodity, WholesalerCommodityAdmin)
