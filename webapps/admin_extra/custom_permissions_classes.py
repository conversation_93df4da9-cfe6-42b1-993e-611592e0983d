"""
overrides default django classes to corresponding ones inheriting from
PermissionRequiredMixin
"""

from dataclasses import dataclass
from typing import ClassVar, Iterable

from django.conf import settings
from django.contrib.auth.mixins import AccessMixin, PermissionRequiredMixin
from django.core.exceptions import ImproperlyConfigured
from django.views import generic

from country_config import Country
from lib.feature_flag.adapter import UserData
from lib.feature_flag.feature.admin import UseNewAdminGroupPermissions
from webapps.user.groups import GroupName, GroupNameV2


def use_new_permissions(user):
    """
    Checks if the new group permission model is enabled via Eppo flag.

    When removing the feature flag, please check all usages of this method.
    """
    user_key = str(user.email) if user and user.is_authenticated else 'anonymous'
    return UseNewAdminGroupPermissions(user=UserData(subject_key=user_key))


class View(PermissionRequiredMixin, generic.View):
    def visible(self):
        return self.has_permission()


class FormView(PermissionRequiredMixin, generic.edit.FormView):
    def visible(self):
        return self.has_permission()


class TemplateView(PermissionRequiredMixin, generic.TemplateView):
    def visible(self):
        return self.has_permission()


@dataclass
class PermissionGroups:
    full_access: list[GroupNameV2]
    read_only: list[GroupNameV2]
    legacy_full_access: list[GroupName]


class BaseIsInGroupMixin:
    # if there is more than one group, checks if you belong to at least one
    full_access_groups: ClassVar[Iterable[GroupName | GroupNameV2]]
    read_only_access_groups: ClassVar[Iterable[GroupNameV2]]

    @classmethod
    def get_required_groups(cls) -> PermissionGroups:
        full_access_groups = getattr(cls, 'full_access_groups', None)
        read_only_access_groups = getattr(cls, 'read_only_access_groups', None)

        if full_access_groups is None and read_only_access_groups is None:
            raise ImproperlyConfigured(
                f'{cls.__name__} is missing the groups attribute.'
                f' Define {cls.__name__}.full_access_groups or '
                f'{cls.__name__}.read_only_access_groups or override '
                f'{cls.__name__}.get_required_groups().'
            )

        full_access = [
            group for group in full_access_groups or [] if isinstance(group, GroupNameV2)
        ]
        legacy_full_access = [
            group for group in full_access_groups or [] if isinstance(group, GroupName)
        ]
        read_only_access = [
            group for group in read_only_access_groups or [] if isinstance(group, GroupNameV2)
        ]
        return PermissionGroups(
            full_access=full_access,
            read_only=read_only_access,
            legacy_full_access=legacy_full_access,
        )

    @classmethod
    def is_user_in_full_access_groups(cls, user):
        if user.is_superuser:
            return True

        groups = cls.get_required_groups()
        if use_new_permissions(user=user):
            has_permission = user.groups.filter(
                name__in=[group.value for group in groups.full_access]
            ).exists()
        else:
            access_groups = groups.legacy_full_access
            has_permission = (
                not access_groups
                or user.groups.filter(name__in=[group.value for group in access_groups]).exists()
            )
        return has_permission

    @classmethod
    def is_user_in_read_only_access_groups(cls, user):
        if user.is_superuser:
            return True

        has_permission = False
        if use_new_permissions(user=user):
            groups = cls.get_required_groups().read_only
            if groups:
                has_permission = user.groups.filter(
                    name__in=[group.value for group in groups]
                ).exists()
        return has_permission


class GroupPermissionMixin(BaseIsInGroupMixin, AccessMixin):
    """View mixin that restricts access based on user's groups."""

    def has_view_permission(self, request, obj=None):  # pylint: disable=unused-argument
        return self.is_user_in_full_access_groups(
            request.user
        ) or self.is_user_in_read_only_access_groups(request.user)

    def has_change_permission(self, request, obj=None):  # pylint: disable=unused-argument
        return self.is_user_in_full_access_groups(request.user)

    def has_add_permission(self, request):
        return self.is_user_in_full_access_groups(request.user)

    def has_delete_permission(self, request, obj=None):  # pylint: disable=unused-argument
        return self.is_user_in_full_access_groups(request.user)

    def dispatch(self, request, *args, **kwargs):
        if self.is_user_in_full_access_groups(request.user):
            return super().dispatch(request, *args, **kwargs)

        return self.handle_no_permission()


class USIsInGroupPermissionMixin(BaseIsInGroupMixin):
    @classmethod
    def is_user_in_full_access_groups(cls, user):
        if settings.API_COUNTRY != Country.US:
            return True
        return super().is_user_in_full_access_groups(user)

    @classmethod
    def is_user_in_read_only_access_groups(cls, user):
        if settings.API_COUNTRY != Country.US:
            return True
        return super().is_user_in_read_only_access_groups(user)


class LegacyPermissionBypassMixin:
    """
    Overrides get_permission_required to bypass legacy permission checks
    when the UseNewAdminGroupPermissions feature flag is active for the user.
    This should be placed before other permission-related mixins or views
    in the MRO.
    """

    def get_permission_required(self):
        if use_new_permissions(self.request.user):
            return ()
        return super().get_permission_required()
