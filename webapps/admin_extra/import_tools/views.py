import base64

from django.contrib import messages

from webapps.admin_extra.custom_permissions_classes import FormView, GroupPermissionMixin
from webapps.admin_extra.views.utils import (
    ImportFileMixin,
    admin_logger,
)
from webapps.user.groups import GroupNameV2
from webapps.warehouse.models import Barcode
from webapps.warehouse.tasks import (
    parse_and_import_warehouse_commodities,
    import_commodities_dry_run_with_email_report_task,
)


class BaseCommoditiesImportView(ImportFileMixin, GroupPermissionMixin, FormView):
    permission_required = ()
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    success_message = 'File loaded successfully. Check {email} for import report.'
    dry_run_with_email_message = 'Please check your email for dry run result'
    data_loader = None
    importer_name = ''

    def get(self, *args, **kwargs):
        admin_logger.info("%s %s GET", self.request.user.email, self.importer_name)
        return super().get(*args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['barcode_types'] = list(Barcode.BARCODE_TYPES)
        context['default_volume_unit'] = self.form_class.DEFAULT_VOLUME_UNIT
        return context

    def post(self, request, *args, **kwargs):
        form = self.get_form()
        commodities, errors = [], []
        omitted, total_num_com = 0, 0
        if not form.is_valid():
            context = self.get_context_data(form=form)
            context.update(
                {
                    'commodities': commodities,
                    'omitted': omitted,
                    'total_num_com': total_num_com,
                    'num_commodities_import': (total_num_com - omitted),
                    'errors': errors,
                    'barcode_types': list(Barcode.BARCODE_TYPES),
                }
            )
            return self.render_to_response(context)

        admin_logger.info("%s %s POST", request.user.email, self.importer_name)
        import_file = form.cleaned_data['import_file']
        import_file_data = import_file.read()
        business_id = form.cleaned_data['business_id']
        email = form.cleaned_data.get('email')

        if form.cleaned_data['dry_run_with_email_report']:
            import_commodities_dry_run_with_email_report_task.delay(
                base64.b64encode(import_file_data).decode(),
                business_id,
                email,
                self.importer_name,
            )
            messages.success(self.request, self.dry_run_with_email_message)
        elif form.cleaned_data['dry_run']:
            # load commodities list of dicts
            commodities, omitted, total_num_com = self.data_loader.load_items_without_spec(
                import_file_data,
                business_id=business_id,
            )
        else:
            # CELERY
            parse_and_import_warehouse_commodities.delay(
                data=base64.b64encode(import_file_data).decode(),
                business_id=business_id,
                importer_name=self.importer_name,
                email_report=email,
            )
            messages.add_message(
                request, messages.SUCCESS, self.success_message.format(email=email)
            )

        context = self.get_context_data(form=form)
        context.update(
            {
                'commodities': commodities,
                'omitted': omitted,
                'total_num_com': total_num_com,
                'num_commodities_import': (total_num_com - omitted),
                'errors': errors,
                'barcode_types': list(Barcode.BARCODE_TYPES),
            }
        )
        return self.render_to_response(context)
