from webapps.admin_extra.boost_mass_tools import BusinessRow, BusinessesToChangeBaseClass
from webapps.admin_extra.import_utils import load_stripped_xlsx
from webapps.public_partners.models import BusinessPartnerData


def get_list_duplicates(ids_list: list) -> list:
    seen = set()
    dupes = [x for x in ids_list if x in seen or seen.add(x)]
    return dupes


class BusinessesToAttachImportUID(BusinessesToChangeBaseClass):
    class Row(BusinessRow):
        def __init__(self, raw_row):
            super().__init__(raw_row)
            self.import_uid = None
            self.importer_name = None
            self.warnings = []

            if len(raw_row) < 2:
                self.errors.append('File must have at least 2 columns: Business ID and Import UID')
            elif len(raw_row) > 3:
                self.errors.append(
                    'Remove extra columns from file. Check out tips for file structure'
                )
            else:
                self.validate_import_uid()
                self.validate_importer_name()

        def validate_import_uid(self):
            try:
                import_uid = (self._row[1] or '').strip()
                if import_uid:
                    if BusinessPartnerData.objects.filter(import_uid=import_uid).exists():
                        self.warnings.append(
                            'Record with given imported_uid already exists and will be duplicated.'
                        )
                        self.import_uid = import_uid
                    else:
                        self.import_uid = import_uid
                else:
                    self.errors.append('Import_uid cannot be empty')
            except IndexError:
                self.errors.append('Import_uid cannot be empty')

        def validate_importer_name(self):
            try:
                importer_name = (self._row[2] or '').strip()
                if importer_name:
                    self.importer_name = importer_name
                else:
                    self.warnings.append('Importer name is empty, will use default value')
            except IndexError:
                self.warnings.append('Importer name column missing, will use default value')

        def check_connected_import_uid(self):
            if BusinessPartnerData.objects.filter(business=self.business).exists():
                self.warnings.append(
                    'Business with that business_id already exists in '
                    'public_partners_businesspartnerdata table and its import_uid will be updated.'
                )

        def get_message_field(self, message_type):
            message_field_type = {'errors': self.errors, 'warnings': self.warnings}
            return message_field_type[message_type]

    @classmethod
    def validate_from_file(cls, imported_file):  # pylint: disable=arguments-differ
        raw_file = imported_file.read()
        raw_rows = load_stripped_xlsx(raw_file)
        if not raw_rows:
            raise ValueError('Empty file. Please fill out the file')
        if business_ids_dup := get_list_duplicates([row[0] for row in raw_rows]):
            raise ValueError(f'Remove duplicated business ids : {business_ids_dup}')

        # Create the validation object first
        validation_obj = cls(raw_rows)

        # Check for import_uid duplicates only for rows that have enough columns
        import_uids = []
        for row in raw_rows:
            if len(row) >= 2:
                import_uids.append(row[1])

        if import_uids_dup := get_list_duplicates(import_uids):
            raise ValueError(f'Remove duplicated imported uids : {import_uids_dup}')

        return validation_obj.validate_businesses()

    def create_error_messages(self):
        return self._create_messages('errors')

    def create_warning_messages(self):
        return self._create_messages('warnings')

    def _create_messages(self, message_type):
        messages = []
        for row_num, business_to_attach_imported_uid in enumerate(self.rows_list, start=1):
            messages.extend(
                [
                    f'Row {row_num} | {msg_str}'
                    for msg_str in business_to_attach_imported_uid.get_message_field(message_type)
                ]
            )
        return messages

    def check_if_business_has_connected_import_uid(self):
        for row in self.rows_list:
            row.check_connected_import_uid()
        return self

    def validate_businesses(self):  # pylint: disable=arguments-differ
        self.load_businesses()
        self.check_if_business_has_connected_import_uid()
        return self
