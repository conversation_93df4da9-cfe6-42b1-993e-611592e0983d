from django import forms
from django.core.exceptions import ValidationError

from webapps.user.models import User


class EnforcePasswordResetForm(forms.Form):
    def __init__(self, *args, max_ids_nbr, **kwargs):
        self.max_ids_nbr = max_ids_nbr
        super().__init__(*args, **kwargs)

        max_length = self.max_ids_nbr * 15  # chars per line (id + newline char)
        self.fields['user_ids'] = forms.CharField(max_length=max_length, widget=forms.Textarea())

    def clean_user_ids(self):
        if not (user_ids := self.cleaned_data.get('user_ids', '').strip()):
            raise ValidationError("User IDs cannot be empty.")

        user_ids = user_ids.splitlines()

        if len(user_ids) > self.max_ids_nbr:
            raise ValidationError(f"You can only enter up to {self.max_ids_nbr} user IDs.")

        if not all(user_id.strip().isdigit() for user_id in user_ids):
            raise ValidationError("All User IDs must be valid numbers.")

        user_ids = list(map(int, user_ids))
        existing_user_ids = set(User.objects.filter(id__in=user_ids).values_list('id', flat=True))
        not_found_ids = set(user_ids) - existing_user_ids

        if not_found_ids:
            raise ValidationError(f"User IDs {', '.join(map(str, not_found_ids))} do not exist.")

        return user_ids
