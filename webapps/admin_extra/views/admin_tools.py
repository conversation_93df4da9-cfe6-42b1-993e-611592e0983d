from django.shortcuts import render
from django.urls import (
    NoReverseMatch,
    reverse,
)
from django.utils.functional import cached_property

from webapps.admin_extra.custom_permissions_classes import View, GroupPermissionMixin
from webapps.user.groups import GroupNameV2


class BaseAdminToolsView(View):
    template = 'admin/custom_views/admin_tools.html'

    def __init__(self, **kwargs):
        from django.contrib import admin

        super().__init__(**kwargs)
        self.registered_views = admin.site.custom_views

    def get(self, request):
        sections = self.get_sections(request)
        return render(
            request,
            self.template,
            {'sections': sections},
        )

    def get_sections(self, request):
        raise NotImplementedError(
            f'{self.__class__.__name__} is missing the get_sections method. Define get_sections().'
        )

    def get_tool(self, view):
        return {
            'name': view.name if view.name else view.path,
            'url': self._get_url(view),
            'description': view.view.__doc__,
            'group': self._get_group(view.view),
        }

    @staticmethod
    def _get_url(view):
        try:
            return reverse(f'admin:{view.urlname if view.urlname else view.path}')
        except NoReverseMatch:
            return view.path

    @staticmethod
    def _get_group(view_func):
        try:
            return view_func.view_class.group
        except AttributeError:
            return None


class AdminToolsView(BaseAdminToolsView):
    permission_required = ()

    def get_sections(self, request):
        sections = [self.visible_tools]
        if request.user.is_superuser:
            sections.append(self.superuser_tools)
        return sections

    @cached_property
    def visible_tools(self):
        return {
            'section': 'Tools',
            'tools': [self.get_tool(view) for view in self.registered_views if view.visible],
        }

    @cached_property
    def superuser_tools(self):
        return {
            'section': 'Superuser',
            'tools': [self.get_tool(view) for view in self.registered_views if not view.visible],
        }


class MigrationsAndImportersView(GroupPermissionMixin, BaseAdminToolsView):
    permission_required = ()
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    template = 'admin/custom_views/migrations_and_importers.html'

    importer_views_names = [
        'Customer Import',
        'Product (Inventory) Import',
        'Images Import',
        'Versum Data Import',
        'Review Import',
        'Business Listing Import',
    ]
    remover_views_names = [
        'Bookings Remover',
    ]
    removers_with_business_id = ['Customer Remover', 'Reviews Remover']
    other_tools_views_names = [
        'Import business GDPR data',
        'Subscription import',
        'Import subscription buyers',
        'Mass Subscription Product Import',
        'Pattern Zip Codes Importer',
        'Import subscription transactions',
        'Enterprise Data Import',
        'New Bank Importer',
        'Zip Codes To Areas Importer',
        'Experimental User Gender Importer',
        'Experiment data importer',
    ]

    def get_sections(self, request):  # pylint: disable=unused-argument
        return [self.importers, self.removers, self.extras]

    @cached_property
    def importers(self):
        return self._get_section_tools('Migrations & importers', self.importer_views_names)

    @cached_property
    def removers(self):
        section_tools = self._get_section_tools('Removers', self.remover_views_names)
        section_tools['tools'] += self.get_tools_with_input_and_redirect()
        return section_tools

    @cached_property
    def extras(self):
        return self._get_section_tools('Extras', self.other_tools_views_names)

    def _get_section_tools(self, section_name: str, tool_names: list) -> dict:
        return {
            'section': section_name,
            'tools': [
                self.get_tool(view)
                for view in self.registered_views
                if view.visible and view.name in tool_names
            ],
        }

    @staticmethod
    def get_tools_with_input_and_redirect():
        return [
            {
                'description': 'Deletes imported reviews from the business. '
                'To use it, enter the business ID and press \'Go\'.',
                'group': None,
                'redirect_url': 'business/#biz_id#/delete_imported_reviews/',
            },
            {
                'description': "Delete imported customer base. "
                "If the customer is related to a booking,"
                " it will be hidden from the PX (status False in Visible column)."
                " Please be aware not to use the 'DELETE ALL' option recklessly, "
                "as it deletes the entire business customer database."
                " To use it, enter the business ID and press 'Go'.",
                'group': None,
                'redirect_url': 'business/#biz_id#/customer_imports/',
            },
        ]
