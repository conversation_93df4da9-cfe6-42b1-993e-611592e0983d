from django.http import HttpResponse
from django.shortcuts import render
from openpyxl import Workbook

from lib.spreadsheet import columns_auto_width
from webapps.admin_extra import consts
from webapps.admin_extra.custom_permissions_classes import View, GroupPermissionMixin
from webapps.third_tier_wait_list.models import WaitList
from webapps.user.groups import GroupNameV2


class WaitListView(GroupPermissionMixin, View):
    permission_required = ()
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    template_name = 'admin/custom_views/wait_list.html'

    def get(self, request, *_args, **_kwargs):
        emails = (
            WaitList.objects.all()
            .only(
                'created',
                'email',
                'country_code',
            )
            .order_by('-created')
        )

        if 'download_wait_list' in request.GET:
            workbook = Workbook()
            worksheet = workbook.active
            mime = consts.XLS_MIME

            worksheet.append(['created (UTC)', 'email', 'country_code'])
            for obj in emails:
                worksheet.append([obj.created.replace(tzinfo=None), obj.email, obj.country_code])
            columns_auto_width(worksheet)

            response = HttpResponse(content_type=mime)
            response['Content-Disposition'] = 'attachment; filename=wait_list.xlsx'
            workbook.save(response)
            return response

        return render(
            request=request,
            template_name='admin/custom_views/wait_list.html',
            context={
                'table': emails[:30],
                'elements': emails.count(),
            },
        )
