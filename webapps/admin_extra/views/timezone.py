from django.contrib.messages.views import SuccessMessageMixin

from webapps.admin_extra.custom_permissions_classes import FormView, GroupPermissionMixin
from webapps.admin_extra.forms.timezone import TimeZoneForm
from webapps.user.groups import GroupNameV2


class TimeZoneView(SuccessMessageMixin, GroupPermissionMixin, FormView):
    permission_required = ()
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    form_class = TimeZoneForm
    template_name = 'admin/custom_views/generic_form_template.html'
    success_url = 'manual_user_timezone'
    success_message = 'Time zone updated successfully'

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs.update(request=self.request)
        return kwargs

    def form_valid(self, form: TimeZoneForm):
        self.request.session['manual_user_timezone'] = form.cleaned_data['timezone']
        return super().form_valid(form)
