from bo_obs.datadog.enums import BooksyTeams
from django.contrib.messages.views import SuccessMessageMixin
from django.db.transaction import atomic
from django.template.defaultfilters import register

from webapps.admin_extra.archive_bookings import archive_bookings
from webapps.admin_extra.custom_permissions_classes import FormView, GroupPermissionMixin
from webapps.admin_extra.forms.booking_remove import BookingRemoveForm
from webapps.user.groups import GroupName, GroupNameV2


# this django template custom filter is used to display value of __all__ key from errors dict
# in django template, as it's impossible to do by default
# https://docs.djangoproject.com/en/4.0/ref/templates/language/#variables
# (variables may not start with an underscore)
@register.filter(name='get')
def get(d, k):
    return d.get(k, None)


class BookingRemoveView(GroupPermissionMixin, SuccessMessageMixin, FormView):
    """
    Deletes only imported data in a range as in the tool's description.
    Does not manage business bookings.
    """

    booksy_teams = (BooksyTeams.CUSTOMER_BOOKING,)
    permission_required = ()
    form_class = BookingRemoveForm
    template_name = 'admin/custom_views/bookings_remover.html'
    success_url = 'bookings_remover'
    full_access_groups = (
        GroupName.BOOKING_REMOVE,
        GroupNameV2.BUSINESS_EDITOR,
    )

    def post(self, request, *args, **kwargs):
        form = self.get_form()

        if not form.is_valid():
            return self.form_invalid(form)

        with atomic():
            bookings_count = archive_bookings(
                form.cleaned_data['business_id'],
                form.cleaned_data['from_date'],
                form.cleaned_data['to_date'],
                form.cleaned_data['last_hrs'],
                form.cleaned_data['only_imported'],
            )
        self.deleted_bookings_count = bookings_count

        return self.form_valid(form)

    def get_success_message(self, cleaned_data):
        business_id = cleaned_data['business_id']
        if (from_date := cleaned_data['from_date']) and (to_date := cleaned_data['to_date']):
            return (
                f'{self.deleted_bookings_count} bookings created from {from_date} to {to_date} for'
                f' biz: {business_id} deleted!'
            )
        return f'{self.deleted_bookings_count} bookings for biz: {business_id} deleted!'
