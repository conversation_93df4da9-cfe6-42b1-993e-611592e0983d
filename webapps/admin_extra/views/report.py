from django.contrib.messages.views import SuccessMessageMixin
from django.http import HttpResponse
from django.utils.translation import get_language

from webapps.admin_extra.custom_permissions_classes import (
    FormView,
    GroupPermissionMixin,
    View,
)
from webapps.admin_extra.forms.report import (
    ReportDoubleSubscriptionForm,
    ReportGenerateStatisticForm,
    ReportInvoiceSummaryForm,
)
from webapps.admin_extra.reports import RetentionReportGenerator
from webapps.business.models import Business
from webapps.purchase.tasks.reports import send_double_subscriptions_report
from webapps.statistics.reports import SpreadsheetReport
from webapps.user.groups import GroupName, GroupNameV2


class ReportGenerateStatisticView(GroupPermissionMixin, SuccessMessageMixin, FormView):
    permission_required = 'user.create_reports'
    form_class = ReportGenerateStatisticForm
    template_name = 'admin/custom_views/generate_statistics_report.html'
    success_url = 'generate_statistics_report'
    full_access_groups = (GroupName.REMOVAL_CANDIDATE, GroupNameV2.BASIC_MODERATOR)
    read_only_access_groups = ()

    def get(self, request, *args, **kwargs):
        self._admin_user = request.user.user
        return super().get(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        self._admin_user = request.user.user
        return super().post(request, *args, **kwargs)

    def get_initial(self):
        initial = self.initial or {}
        initial['email'] = self._admin_user.email
        return initial

    def get_success_message(self, cleaned_data):
        email = cleaned_data['email']
        message = f'Report sent to {email}'
        return message

    def form_valid(self, form):
        from webapps.statistics.tasks import generate_statistics_report
        from webapps.statistics.tools import get_statistics_report_admin

        report = get_statistics_report_admin(
            business=form.cleaned_data['business_id'],
            scope_type=form.cleaned_data['scope_type'],
            scope_date=form.cleaned_data['scope_date'],
            end_date=form.cleaned_data.get('end_date'),
        )
        report.maybe_aggregate_today()

        generate_statistics_report.delay(
            business_id=form.cleaned_data['business_id'],
            user_id=self._admin_user.id,
            scope_type=form.cleaned_data['scope_type'],
            scope_date=form.cleaned_data['scope_date'].isoformat(),
            report_type=form.cleaned_data['report_type'],
            email=form.cleaned_data['email'],
            language=get_language(),
            end_date=(
                form.cleaned_data['end_date'].isoformat()
                if form.cleaned_data.get('end_date')
                else None
            ),
            request_type=SpreadsheetReport.SPREADSHEET_REPORT__ADMIN_TYPE,
        )
        return super().form_valid(form)


class ReportInvoiceSummaryView(SuccessMessageMixin, GroupPermissionMixin, FormView):
    permission_required = 'user.create_reports'
    form_class = ReportInvoiceSummaryForm
    template_name = 'admin/custom_views/generate_summary_report.html'
    success_url = 'summary_report'
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()

    def get(self, request, *args, **kwargs):
        self._admin_user = request.user.user
        return super().get(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        self._admin_user = request.user.user
        return super().post(request, *args, **kwargs)

    def get_initial(self):
        initial = self.initial or {}
        initial['email'] = self._admin_user.email
        return initial

    def get_success_message(self, cleaned_data):
        email = cleaned_data['email']
        message = f'Report sent to {email}'
        return message

    def form_valid(self, form):
        from webapps.statistics.tasks import generate_summary_report

        generate_summary_report.delay(
            email=form.cleaned_data['email'],
            start_date=form.cleaned_data['start_date'].isoformat(),
            end_date=form.cleaned_data['end_date'].isoformat(),
            report_type=form.cleaned_data['report_type'],
        )
        return super().form_valid(form)


class ReportDoubleSubscriptionView(GroupPermissionMixin, SuccessMessageMixin, FormView):
    form_class = ReportDoubleSubscriptionForm
    template_name = 'admin/custom_views/generic_form_template.html'
    success_url = 'double_subscriptions_report'
    permission_required = ()
    full_access_groups = (GroupName.SUBSCRIPTION_REPORTS,)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Send double subscriptions report'
        context['save_button_title'] = 'Send'
        return context

    def get_initial(self):
        initial_data = {'email': self.request.user.email}
        return initial_data

    def get_success_message(self, cleaned_data):
        return 'Yay! Please wait +-10 min for an email.'

    def form_valid(self, form):
        send_double_subscriptions_report.delay(
            emails=[
                form.cleaned_data['email'],
            ]
        )
        return super().form_valid(form)


class RetentionBusinessReports(View):
    permission_required = ()

    def get(self, request, *_args, **kwargs):
        business_id = kwargs.get('business_id')
        business = Business.objects.get(id=business_id)
        report = RetentionReportGenerator.generate(business_id)
        response = HttpResponse(
            report.getvalue(),
            content_type='application/vnd.ms-excel',
        )
        date_iso = business.tznow.isoformat()[:16]
        report_name = f'Retention report {business_id} {date_iso}.xlsx'
        response['Content-Disposition'] = 'attachment; filename=' + report_name
        response['Content-length'] = len(report.getvalue())
        return response
