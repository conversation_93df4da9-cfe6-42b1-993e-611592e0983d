from django.contrib.messages.views import SuccessMessageMixin

from webapps.admin_extra.custom_permissions_classes import FormView, GroupPermissionMixin
from webapps.admin_extra.forms.refresh_statistics import RefreshStatisticsForm
from webapps.statistics.tools import StatisticsAggregator
from webapps.user.groups import GroupNameV2


class RefreshStatisticsView(SuccessMessageMixin, GroupPermissionMixin, FormView):
    """
    Refreshes statistics for given business_id and date.
    """

    permission_required = ()
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    form_class = RefreshStatisticsForm
    template_name = 'admin/custom_views/generic_form_template.html'
    success_message = 'Statistics will be updated in a few minutes.'
    success_url = 'refresh_statistics'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Refresh Statistics'
        context['instructions'] = self.__doc__
        return context

    def form_valid(self, form):
        business_id = form.cleaned_data['business_id']
        date = form.cleaned_data['date']
        self.refresh_statistics(business_id, date)
        return super().form_valid(form)

    @staticmethod
    def refresh_statistics(business_id, date):
        StatisticsAggregator(
            business_id=business_id,
            from_date=date,
            till_date=date,
        ).run()
