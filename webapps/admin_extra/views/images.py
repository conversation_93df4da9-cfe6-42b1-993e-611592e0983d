from botocore.exceptions import ClientError
from django.shortcuts import render

from webapps.admin_extra.custom_permissions_classes import (
    FormView,
    GroupPermissionMixin,
    LegacyPermissionBypassMixin,
)
from webapps.admin_extra.forms.images import ImagesImportForm
from webapps.admin_extra.tasks import images_import_task
from webapps.admin_extra.views.utils import ImportFileMixin
from webapps.business.models import Business
from webapps.images.enums import ImageTypeEnum
from webapps.user.groups import GroupNameV2


class ImagesImportView(
    ImportFileMixin, LegacyPermissionBypassMixin, GroupPermissionMixin, FormView
):
    """
    For PX business portfolio images importer. Max size of compressed zip file: 12MB
    """

    permission_required = (
        'images.add_image',
        'images.change_image',
        'business.add_business',
        'business.change_business',
    )
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    form_class = ImagesImportForm
    template_name = 'admin/custom_views/images_import.html'

    success_message = 'Type of all fields are correct. Images will be uploaded in couple minutes'
    import_directory = 'zip_images'

    def post(self, request, *args, **kwargs):
        form = self.get_form()
        success_message = ''
        category = ImageTypeEnum.INSPIRATION
        if form.is_valid():
            business_id = form.cleaned_data['business_id']
            biz = Business.objects.get(id=business_id)
            try:
                zip_path = self.save_file('import_file', form)
            except ClientError:
                form.add_error('import_file', 'Unable to upload file to S3')
                return self.form_invalid(form)

            images_import_task.delay(zip_path, biz.id, category)
            success_message = self.success_message
        return render(
            request,
            self.template_name,
            {
                'form': form,
                'success_message': success_message,
            },
        )
