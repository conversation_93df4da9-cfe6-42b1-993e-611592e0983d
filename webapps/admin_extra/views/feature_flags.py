from operator import itemgetter

from django.shortcuts import render

from lib.feature_flag.flag_base import BooleanFlag, DictFlag, IntegerFlag, StringFlag, FloatFlag
from webapps.admin_extra.custom_permissions_classes import View, GroupPermissionMixin
from webapps.user.groups import GroupNameV2


class FeatureFlagsView(GroupPermissionMixin, View):
    permission_required = ()
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()

    def get(self, request):
        return render(
            request,
            'admin/custom_views/feature_flags.html',
            {'sections': [get_flags()]},
        )


def get_flags():
    return {
        'section': 'Flags',
        'flags': sorted(
            [
                get_flag(flag)
                for flag in BooleanFlag.__subclasses__()
                + DictFlag.__subclasses__()
                + IntegerFlag.__subclasses__()
                + StringFlag.__subclasses__()
                + FloatFlag.__subclasses__()
            ],
            key=itemgetter('adapter', 'name'),
        ),
    }


def get_flag(flag):
    return {
        'name': flag.flag_name,
        'value': flag(),
        'adapter': flag.adapter.name if hasattr(flag, 'adapter') else '',
    }
