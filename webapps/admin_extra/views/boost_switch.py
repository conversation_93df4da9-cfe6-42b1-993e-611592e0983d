from django.conf import settings
from django.contrib import messages

from lib.feature_flag.feature.boost import BoostBanIsTheOnlyBanningFlowFlag
from webapps.admin_extra.boost_mass_tools import (
    BusinessesToSwitchBoost,
    ImportFileWithXLSXExampleMixin,
)
from webapps.admin_extra.custom_permissions_classes import FormView, GroupPermissionMixin
from webapps.admin_extra.forms.boost_switch import BoostSwitchForm
from webapps.admin_extra.views.utils import (
    admin_logger,
)
from webapps.business.tasks import boost_switch_availability_task
from webapps.user.groups import GroupNameV2


class BoostSwitchView(ImportFileWithXLSXExampleMixin, GroupPermissionMixin, FormView):
    """
    Enable or disable boost for given businesses in xlsx
    """

    permission_required = ()
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    form_class = BoostSwitchForm
    template_name = 'admin/custom_views/boost_switch.html'
    example_file_name = 'boost_switch_businesses.xlsx'

    def visible(self):
        if settings.BOOST.BANS_ENABLED and BoostBanIsTheOnlyBanningFlowFlag():
            return False
        return super().visible()

    def post(self, request, *args, **kwargs):
        form = self.get_form()
        context = self.get_context_data(form=form)
        if form.is_valid():
            admin_logger.info('%s boost_switch POST', request.user.email)
            import_file = form.cleaned_data['import_file']
            mode = form.cleaned_data['mode']
            validated_businesses = BusinessesToSwitchBoost.validate_from_file(import_file, mode)
            errors = validated_businesses.check_errors()
            if errors:
                error_messages = validated_businesses.create_error_messages()
                context.update({'errors': error_messages})
            if form.cleaned_data['dry_run'] and not errors:
                context.update(
                    {
                        'success_message': "No errors.",
                    }
                )
            elif not errors:
                email = form.cleaned_data.get('email')
                boost_switch_availability_task.delay(
                    businesses_ids=validated_businesses.businesses_ids(),
                    mode=mode,
                    email_report=email,
                )
                messages.add_message(
                    request,
                    messages.SUCCESS,
                    self.success_message.format(email=email),
                )
                context.update(
                    {
                        'success_message': (
                            "No errors. Boost status will be changed for all businesses. "
                            "Check your email for report."
                        ),
                    }
                )

        return self.render_to_response(context)
