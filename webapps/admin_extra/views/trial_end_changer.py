from botocore.exceptions import ClientError
from django.contrib import messages
from django.shortcuts import render
from django.utils.translation import gettext as _

from lib.tools import WrongColumnNames
from webapps.admin_extra import consts
from webapps.admin_extra.custom_permissions_classes import (
    FormView,
    GroupPermissionMixin,
    LegacyPermissionBypassMixin,
)
from webapps.admin_extra.forms.trial_end import TrialEndForm
from webapps.admin_extra.tasks.trial_end_helpers import (
    load_and_parse_businesses_trials,
    trial_end_change_task,
)
from webapps.admin_extra.views.utils import (
    ImportFileMixin,
    admin_logger,
)
from webapps.user.groups import GroupNameV2


class TrialEndChangerView(
    ImportFileMixin, LegacyPermissionBypassMixin, GroupPermissionMixin, FormView
):
    permission_required = ('business.edit_business',)
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    form_class = TrialEndForm
    template_name = 'admin/custom_views/trial_end_changer.html'
    success_message = 'Businesses trials scheduled for update'
    import_directory = 'trial_end_changer'
    import_file_name = 'import_file'
    import_file_names = (import_file_name,)

    @staticmethod
    def get_example_files():
        return [
            {
                'param': 'download_template',
                'path': 'statics/xlsx/trial_end_changer/template_trial_end_changer.xlsx',
                'mime': consts.XLS_MIME,
            },
        ]

    def get(self, *args, **kwargs):
        admin_logger.info('%s customer_import GET', self.request.user.email)
        return super().get(*args, **kwargs)

    def post(self, request, *args, **kwargs):
        form = self.get_form()
        trials, errors = [], []
        omitted, total_num_trials = 0, 0
        context = {
            'trials': trials,
            'omitted': omitted,
            'total_num_trials': total_num_trials,
            'errors': errors,
            'form': form,
        }

        if not form.is_valid():
            return self.form_invalid(form)

        admin_logger.info('%s business_trial_end_changer POST', request.user.email)
        try:
            excel_path = self.save_file(self.import_file_name, form)
        except ClientError:
            form.add_error(
                self.import_file_name,
                'Unable to upload file to S3',
            )
            return self.form_invalid(form)
        try:
            trials, omitted = load_and_parse_businesses_trials(excel_path)
        except (WrongColumnNames, IOError) as exc:
            messages.error(self.request, str(exc))

        context.update(
            {
                'trials': trials,
                'omitted': omitted,
                'total_num_trials': len(trials) + omitted,
                'num_trials_extended': len(trials),
            }
        )
        if not trials:
            messages.error(self.request, _('File does not contain valid rows'))
        elif not form.cleaned_data['dry_run']:
            email = form.cleaned_data['email']
            trial_end_change_task.delay(trials, email)
            messages.success(self.request, self.success_message)
        return render(request, self.template_name, context)
