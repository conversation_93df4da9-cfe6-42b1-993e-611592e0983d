from django.db.models import Prefetch

from lib.admin_helpers import admin_link
from webapps.admin_extra.custom_permissions_classes import (
    FormView,
    GroupPermissionMixin,
    LegacyPermissionBypassMixin,
)
from webapps.admin_extra.forms.business_work_schedule import (
    BusinessesWorkScheduleForm,
)
from webapps.booking.time_slot_tools import BookingRanges
from webapps.business.enums import DAYS_OF_THE_WEEK
from webapps.business.models import Business, Resource
from webapps.schedule.models import Schedule
from webapps.user.groups import GroupNameV2


class BusinessesWorkScheduleView(LegacyPermissionBypassMixin, GroupPermissionMixin, FormView):
    permission_required = ('business.change_business',)
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    form_class = BusinessesWorkScheduleForm
    template_name = 'admin/custom_views/businesses_work_schedule.html'
    success_url = 'businesses_work_schedule'

    def post(self, request, *args, **kwargs):
        form = self.get_form()
        ctx = self.get_context_data(form=form)
        if form.is_valid():
            cleaned_data = form.cleaned_data
            ctx['dates'], ctx['businesses'] = self._prepare_context_data(
                cleaned_data['businesses_id'],
                cleaned_data['date_from'],
                cleaned_data['date_to'],
            )
        else:
            ctx['dates'] = None

        return self.render_to_response(ctx)

    @staticmethod
    def _generate_available_ranges(businesses_id, date_from, date_to):
        businesses = Business.objects.filter(
            id__in=businesses_id,
        ).prefetch_related(
            Prefetch(
                'resources',
                queryset=Resource.objects.select_related('business', 'staff_user').filter(
                    active=True,
                    type=Resource.STAFF,
                    deleted__isnull=True,
                ),
                to_attr='active_resources',
            ),
            Prefetch(
                'schedules',
                queryset=Schedule.objects.filter(
                    resource__isnull=False,
                    date__gte=date_from,
                    date__lte=date_to,
                    hours=[],
                    resource__active=True,
                    resource__type=Resource.STAFF,
                    resource__deleted__isnull=True,
                ),
                to_attr='current_schedules',
            ),
        )
        for biz in businesses:
            booking_ranges = BookingRanges(
                business=biz,
                start=date_from,
                end=date_to,
                ignore_invisible_staff=False,
                service_variant=True,
                slots_mode=True,
            )
            resources = list(biz.active_resources)
            booking_ranges.checked_resources = resources
            resources_dct = {r.id: r for r in resources}
            available_ranges = booking_ranges.available_ranges(half_pairs=True)
            time_offs = {}
            for schedule in biz.current_schedules:
                time_offs.setdefault(schedule.date, set()).add(schedule.resource_id)

            yield (available_ranges, booking_ranges.working_hours, time_offs, biz, resources_dct)

    def _prepare_context_data(self, businesses_id, date_from, date_to):
        def _fmt_date(date):
            _, day_name = DAYS_OF_THE_WEEK[date.weekday()]
            date_str = date.strftime('%Y-%m-%d')
            return date_str, day_name

        def _fmt_range(tz, start, end):
            return (
                f'{start.astimezone(tz).strftime("%H:%M")} - {end.astimezone(tz).strftime("%H:%M")}'
            )

        dates = None
        result = {}

        for (
            avbl_ranges,
            working_hours_dct,
            time_offs,
            biz,
            resources,
        ) in self._generate_available_ranges(businesses_id, date_from, date_to):
            tz = biz.get_timezone()
            biz_key = (admin_link(biz), biz.name)
            result[biz_key] = {}
            avbl_ranges.pop(0, None)
            if dates is None:
                dates = sorted(_fmt_date(d) for d in avbl_ranges.keys())

            for date, data in avbl_ranges.items():
                date_str = _fmt_date(date)
                for staffer_id, _, ranges in data:
                    staffer_key = self._get_staffer_key(staffer_id, resources)
                    working_hours = []
                    available_hours = []
                    result[biz_key].setdefault(staffer_key, {})[date_str] = (
                        working_hours,
                        available_hours,
                        False,
                    )
                    for start, end in ranges:
                        available_hours.append(_fmt_range(tz, start, end))
                    for start, end in working_hours_dct[staffer_id][date]:
                        working_hours.append(_fmt_range(tz, start, end))
                for staffer_id in time_offs.get(date, []):
                    staffer_key = self._get_staffer_key(staffer_id, resources)
                    result[biz_key].setdefault(staffer_key, {})[date_str] = (None, None, True)

        return dates, result

    @staticmethod
    def _get_staffer_key(staffer_id, resources):
        resource = resources[staffer_id]
        staffer = resource.staff_user
        if staffer:
            contact = [staffer.cell_phone or staffer.work_phone, staffer.email]
            name = staffer.full_name
        else:
            contact = [resource.staff_cell_phone, resource.staff_email]
            name = resource.name
        contact = tuple(filter(None, contact))
        return admin_link(resource), name, contact, resource.staff_access_level
