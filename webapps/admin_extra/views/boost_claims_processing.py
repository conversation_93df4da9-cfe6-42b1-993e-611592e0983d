import base64

from django.contrib import messages

from webapps.admin_extra.boost_mass_tools import ImportFileWithXLSXExampleMixin
from webapps.admin_extra.claim_pending_report import (
    ClaimPendingReportWithDecisions,
    IncorrectReportError,
)
from webapps.admin_extra.custom_permissions_classes import FormView, GroupPermissionMixin
from webapps.admin_extra.forms.boost_claims_processing import (
    BoostClaimsProcessingForm,
)
from webapps.admin_extra.views.utils import admin_logger
from webapps.business.tasks import mass_claim_processing
from webapps.user.groups import GroupNameV2


class BoostClaimsProcessingView(ImportFileWithXLSXExampleMixin, GroupPermissionMixin, FormView):
    """
    Mass claims processing for given MarketplaceTransactionRows in xlsx
    """

    permission_required = ()
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    form_class = BoostClaimsProcessingForm
    template_name = 'admin/custom_views/boost_claims_processing.html'
    example_file_name = 'boost_claims_processing_template.xlsx'

    def post(self, request, *args, **kwargs):
        form = self.get_form()
        context = self.get_context_data(form=form)
        if not form.is_valid():
            context.update({'errors': form.errors})
            return self.render_to_response(context)

        mode = "validate" if form.cleaned_data['dry_run'] else "process"
        admin_logger.info('%s boost_claims_processing %s mode POST', request.user.email, mode)
        raw_file = form.cleaned_data['import_file'].read()

        try:
            ClaimPendingReportWithDecisions.from_file(raw_file=raw_file)
        except IncorrectReportError as err:
            if mode == "process":
                context.update(
                    {
                        'errors': [
                            'Cannot process the file - at least'
                            f' {len(err.error_list)} errors found. First make sure '
                            'that validation passes on your file.'
                        ]
                    }
                )
                return self.render_to_response(context)

            context.update({'errors': err.error_list})
            return self.render_to_response(context)

        if mode == "validate":
            messages.add_message(
                request, messages.SUCCESS, 'Validation passed: the file is correctly formatted.'
            )
        elif mode == "process":
            email = form.cleaned_data.get('email')
            mass_claim_processing.delay(
                raw_sheet_file=base64.b64encode(raw_file).decode(),
                email_report=email,
            )
            messages.add_message(
                request,
                messages.SUCCESS,
                self.success_message.format(email=email),
            )

        return self.render_to_response(context)
