from django.views.generic import FormView

from webapps.admin_extra.custom_permissions_classes import (
    GroupPermissionMixin,
    LegacyPermissionBypassMixin,
)
from webapps.admin_extra.forms.enterprise_batch_update import (
    AboutUsServicesForm,
    BatchUpdateBusinessOpeningHoursForm,
    BatchUpdateBusinessSecuritySettingsForm,
    BatchUpdateBusinessVisibilityForm,
    BatchUpdateImageBundlesForm,
    BatchUpdateLogosForm,
    BatchUpdateResourcesForm,
    BatchUpdateServicesForm,
    CalendarVisibilityEditForm,
)
from webapps.admin_extra.tasks import (
    batch_update_about_us_task,
    batch_update_business_logos_task,
    batch_update_business_opening_hours_task,
    batch_update_business_security_settings_task,
    batch_update_business_visibility_task,
    batch_update_image_bundles_task,
    batch_update_resources_task,
    batch_update_services_task,
    calendar_visibility_edit_task,
)
from webapps.admin_extra.views.utils import (
    CeleryFileTaskMixin,
    CeleryTaskMixin,
)
from webapps.user.groups import GroupNameV2


class BatchUpdateView(FormView):
    _business_instructions = """
    Businesses whose services will be changed may be specified in three 
    ways:
    <ul>
      <li><strong>Business IDs</strong> - businesses specified by Booksy 
      business IDs (separated by <code>;</code>).</li>
      <li><strong>Business Network</strong> - businesses umbrellaed under 
      the chosen business network.</li>
      <li><strong>Partner</strong> - businesses connected to a chosen Booksy
      partner.</li>
    </ul>
    The businesses are chosen in the above order, i.e. if you enumerate 
    business IDs in the <code>Business IDs</code> input field, the 
    <code>Business Network</code> and <code>Partner</code> fields will be 
    both ignored.
    """

    permission_required = (
        'business.add_business',
        'user.add_user',
    )
    template_name = 'admin/custom_views/enterprise_batch_update_template.html'


class BatchUpdateServicesView(CeleryFileTaskMixin, GroupPermissionMixin, BatchUpdateView):
    """
    Update (add, update, delete) services for enterprise businesses.
    """

    _instructions = f"""
    <ul>
      <li>
        In order to update services please provide an excel file defining 
        changes. There are three types of action:
        <ul>
          <li>
              <strong>ADD</strong> - adds a service to all businesses. This 
              action requires the following fields: <code>No.</code>, 
              <code>Service Order</code>, 
              <code>Service Name</code>, 
              <code>Duration</code>, 
              <code>Padding Time</code>, and <code>Interval</code>. 
              <code>Service Category</code> is optional. 
              Both <code>Find By Booksy Service ID</code> and 
              <code>Find By Service Name</code> are not needed.
          </li>
          <li>
              <strong>UPDATE</strong> - updates a service. To change a specific 
              field, put a new value in that field. If you want to remove 
              existing value, put <code>-</code> in the appropriate field. 
              Keep in mind that some of the fields are required by the Booksy 
              system, so you can remove their value without specifying a new 
              one. 
              This action requires only two fields: 
              <code>No.</code> and one of: <code>Find By Booksy Service ID
              </code>, <code>Find By Service Name</code>. All other fields 
              are optional.
          </li>
          <li>
              <strong>DELETE</strong> - deletes (or, to be more precise, turns 
              off) a service. 
              Only two fields are required: <code>No.</code> and one of: 
              <code>Find By Booksy Service ID</code>,  
              <code>Find By Service Name</code>.
          </li>
        </ul>
      </li>
      <li>
        {BatchUpdateView._business_instructions}
      </li>
    </ul>        
    """

    view_base_name = 'batch_update_services'

    celery_task = batch_update_services_task
    celery_task_kwargs = (
        'business_ids_raw',
        'business_network_name',
        'partner_uuid',
        'email',
    )
    form_class = BatchUpdateServicesForm
    import_directory = view_base_name
    import_file_names = ('import_file',)
    success_url = view_base_name
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Batch Update :: Services'
        context['instructions'] = self._instructions
        context['save_button_title'] = 'Update'
        return context


class BatchUpdateResourcesView(CeleryFileTaskMixin, GroupPermissionMixin, BatchUpdateView):
    """
    Update (add, update, delete) resource for enterprise businesses.
    """

    _instructions = """
    In order to update resources please provide an excel file with changes to 
    make.
    Currently, there are three types of actions that may be performed:
    <ul>
      <li>
        <strong>ADD</strong> - adds a resource to the business with ID 
        specified in the <code>Booksy Business ID</code> field.
        <br />
        <ul>
          <li> 
            The following fields are <strong>required</strong>:
            <code>No.</code>,  
            <code>Employee name, surname or position</code>, 
            <code>Employee email address</code>, 
            <code>Access type for employee</code>, 
            <code>Employee visibility (False/True)</code>,
          </li>
          <li>
            These fields are <strong>optional</strong>:
            <code>Employee phone number</code>, 
            <code>Service number assigned to employee</code>,
            <code>Employee ID</code>.
          </li>
          <li>
            The <code>Booksy Resource ID</code> is <strong>not needed</strong>.
          </li>
        </ul> 
      </li>
      <li>
        <strong>UPDATE</strong> - updates a resource with the given 
        <code>Booksy Resource ID</code>.
        <ul>
          <li>
            For this action, the only <strong>required</strong> fields are 
            the <code>No.</code> field and the mentioned 
            <code>Booksy Resource ID</code>.
          </li>
          <li>
            These fields are <strong>optional</strong>: 
            <code>Employee name, surname or position</code>, 
            <code>Employee phone number</code>, 
            <code>Employee email address</code>, 
            <code>Employee ID</code>, 
            <code>Service number assigned to employee</code>, 
            <code>Access type for employee</code>, 
            <code>Employee visibility (False/True)</code>.
          </li>
          <li>
            These fields are <strong>not used</strong>: 
            <code>Booksy Business ID</code>.
          </li>
        </ul>
      </li>
      <li>
        <strong>DELETE</strong> - deletes (or, to be more precise, marks as 
        deleted) a resource.
        <ul>
          <li>
            Only <strong>required</strong> fields here are: <code>No.</code>, 
            <code>Booksy Resource ID</code>.
          </li> 
          <li>
            All other fields are <strong>not needed</strong>.
          </li>
        </ul>
      </li>
    </ul>
    """

    view_base_name = 'batch_update_resources'

    celery_task = batch_update_resources_task
    celery_task_kwargs = ('email',)
    form_class = BatchUpdateResourcesForm
    import_directory = view_base_name
    import_file_names = ('import_file',)
    success_url = view_base_name
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Batch Update :: Resources'
        context['instructions'] = self._instructions
        context['save_button_title'] = 'Update'
        return context


class BatchUpdateBusinessVisibilityView(CeleryTaskMixin, GroupPermissionMixin, BatchUpdateView):
    """
    On/Off visibility for enterprise businesses.
    """

    _instructions = f"""
    <ul>
      <li>
        The <code>Visibility in Marketplace</code> field offers three values:
        <ul>
          <li>
            <strong>-</strong> - do nothing.
          </li>
          <li>
            <strong>Visible</strong> - enables all the chosen businesses in the 
            marketplace.
          </li>
          <li>
            <strong>Hidden</strong> - disables all the chosen businesses in the 
            marketplace.
          </li>
        </ul>
      </li>
      <li>
        The <code>Validity</code> field offers three values:
        <ul>
          <li><strong>-</strong> - do nothing.
          <li>
            <strong>Make Valid</strong> - changes business status to 
            <code>PAID</code> and the <code>active</code> field to 
            <code>True</code>. 
          </li>
          <li>
            <strong>Make Invalid</strong> - changes business status to 
            <code>BLOCKED</code> (i.e. invalid) and the <code>active</code> 
            field to <code>False</code>. 
          </li>
        </ul>
      </li>
      <li>
        
      </li>
        The <code>Allow BListing</code> field offers three values:
        <ul>
          <li><strong>-</strong> - do nothing.</li>
          <li>
            <strong>Allow Transform to BListing</strong> - checks 
            <code>business.custom_data.can_transform_into_b_listing</code> 
            field that allows business to be moved to BListing.
          </li>
          <li>
            <strong>Disallow Transform to BListing</strong> - unchecks the 
            <code>business.custom_data.can_transform_into_b_listing</code> 
            field that disallows business to be moved to BListing.
          </li>
        </ul>
      <li>
        {BatchUpdateView._business_instructions}
      </li>
    </ul>
    """

    BUSINESS_IDS_SEPARATOR = ';'

    view_base_name = 'batch_update_business_visibility'

    celery_task = batch_update_business_visibility_task
    celery_task_kwargs = (
        'business_ids_raw',
        'business_network_name',
        'partner_uuid',
        'email',
        'visibility_data',
    )
    form_class = BatchUpdateBusinessVisibilityForm
    success_url = view_base_name
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Batch Update :: Business Visibility'
        context['instructions'] = self._instructions
        context['save_button_title'] = 'Run Updating'
        return context


class BatchUpdateLogosView(CeleryFileTaskMixin, GroupPermissionMixin, BatchUpdateView):
    """
    Adding logos to enterprise businesses.
    """

    _instructions = f"""
    <ul>
      <li>
        Adds the selected image file as a logo to all of the chosen businesses. 
        The image should be in the PNG or JPG (JPEG) format.
      </li>
      <li>
        {BatchUpdateView._business_instructions}
      </li>
    </ul>
    """

    view_base_name = 'batch_update_logos'

    celery_task = batch_update_business_logos_task
    celery_task_kwargs = (
        'business_ids_raw',
        'business_network_name',
        'partner_uuid',
        'email',
    )
    form_class = BatchUpdateLogosForm
    import_directory = view_base_name
    import_file_names = ('import_file',)
    success_url = view_base_name
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Batch Update :: Logos'
        context['instructions'] = self._instructions
        context['save_button_title'] = 'Update'
        return context


class BatchUpdateImageBundlesView(CeleryFileTaskMixin, GroupPermissionMixin, BatchUpdateView):
    """
    Uploads image bundles for enterprise businesses.
    """

    _instructions = """
    In order to add an image bundle please follow these steps:
    <ol>
        <li>
            Prepare a zip file containing images in folders. Folders names 
            should be business IDs to which the images are going to be added.
            You may add a folder named 'default' to add the same images to all
            the businesses selected below.
            <br />
            
            An example structure of the zip file looks like this:
            <ul>
                <li>
                    123456/
                    <ul>
                        <li>test1.png</li>
                        <li>test2.png</li>
                    </ul>
                <li>
                    234567/
                    <ul>
                        <li>test3.png</li>
                    </ul>
                </li>
                <li>
                    default/
                    <ul>
                        <li>test5.jpg</li>
                    </ul>
                </li> 
            </ul>
            
            Mind that there should not be a root folder in the zip file! 
        </li>
        <li>
            Select businesses to which the images are going to be added. There
            are three ways to selected businesses:
            <ul>
                <li>
                    Directly by business IDs, separated by semicolon. E.g.:
                    <code>123456;234567;345678</code>
                </li>
                <li>
                    By business network: each business IDs umbrellaed by 
                    the selected network will be used.
                </li>
                <li>
                    By partner: each business IDs attached to the selected 
                    partner object will be used. 
                </li>
            </ul>
        </li>
        <li>
            Optionally, if you want the images to be used as cover photos
            make sure that the 'Set as cover photos' is selected. 
        </li>
        <li>
            Click 'Run Updating' button and wait a few minutes for an email 
            with a report describing the outcome of this uploading process.
            If everything went as intended, now you may enjoy the newly
            uploaded photos!
        </li>
    </ol>
    """

    view_base_name = 'batch_update_image_bundles'

    celery_task = batch_update_image_bundles_task
    celery_task_kwargs = (
        'business_ids_raw',
        'business_network_name',
        'partner_uuid',
        'email',
        'set_as_cover',
    )
    form_class = BatchUpdateImageBundlesForm
    import_directory = view_base_name
    import_file_names = ('import_file',)
    success_url = view_base_name
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Batch Update :: Image Bundles'
        context['instructions'] = self._instructions
        context['save_button_title'] = 'Run Updating'
        return context


class BatchUpdateBusinessSecuritySettingsView(CeleryTaskMixin, BatchUpdateView):
    """
    Restricting access to booksy-biz by selecting IP addresses.
    """

    _instructions = f"""
    <ul>
      <li>
        To add whitelisting by IP for the chosen businesses, please provide 
        IP range(s) in the <code>Allowed IPs</code> field
        (separated by <code>;</code>). An IP range specifies the pool of IP
        addresses from which the access will be allowed. Here is an example of
        an IP range: <code>*********/24</code>.        
      </li>
      <li>
        To switch off whitelisting for the selected businesses, check on the 
        <code>Switch Off IP Checking</code>. You do not need fill in the 
        <code>Allowed IPs</code> field in that case.
      </li>
      <li>
        {BatchUpdateView._business_instructions}
      </li>
    </li>
    """

    view_base_name = 'batch_update_business_security_settings'

    celery_task = batch_update_business_security_settings_task
    celery_task_kwargs = (
        'business_ids_raw',
        'business_network_name',
        'partner_uuid',
        'email',
        'allowed_ips_raw',
        'switch_off_ip_checking',
    )
    form_class = BatchUpdateBusinessSecuritySettingsForm
    import_directory = view_base_name
    import_file_names = ('import_file',)
    success_url = view_base_name

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Batch Update :: Security Settings'
        context['instructions'] = self._instructions
        context['save_button_title'] = 'Update'
        return context


class BatchUpdateBusinessOpeningHoursView(CeleryTaskMixin, GroupPermissionMixin, BatchUpdateView):
    """
    Updates opening hours for enterprise businesses.
    """

    _instructions = """
    <ul>
        <li>
            If you want to change opening hours for a specific day put them in 
            the relevant input field using the following format: HH:MM-HH:MM, 
            for example: 10:30-18:15.
        </li>
        <li>
            If you don't want to change opening hours for a specific day, leave 
            the field empty for that day.
        </li>
        <li>
            If you want to remove opening hours and not specify new ones, put 
            the 'closed' text  in the relevant field.
        </li>
    </ul>  
    """

    view_base_name = 'batch_update_business_opening_hours'

    celery_task = batch_update_business_opening_hours_task
    celery_task_kwargs = (
        'business_ids_raw',
        'business_network_name',
        'partner_uuid',
        'email',
        'opening_hours',
    )
    form_class = BatchUpdateBusinessOpeningHoursForm
    success_url = view_base_name
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Batch Update :: Business Opening Hours'
        context['instructions'] = self._instructions
        context['save_button_title'] = 'Run Updating'
        return context


class AboutUsServicesView(
    CeleryTaskMixin, LegacyPermissionBypassMixin, GroupPermissionMixin, FormView
):
    view_base_name = 'about_us_update'
    permission_required = (
        'business.add_business',
        'user.add_user',
    )
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    form_class = AboutUsServicesForm
    template_name = 'admin/custom_views/generic_form_template.html'

    celery_task = batch_update_about_us_task
    celery_task_kwargs = (
        'business_ids_raw',
        'business_network_name',
        'partner_uuid',
        'email',
        'description',
    )
    success_url = view_base_name

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Batch Update :: About us'
        context['save_button_title'] = 'Run Updating'
        return context


class CalendarVisibilityEditView(CeleryTaskMixin, GroupPermissionMixin, FormView):
    view_base_name = 'calendar_visibility'

    celery_task = calendar_visibility_edit_task
    celery_task_kwargs = (
        'business_ids_raw',
        'business_network_name',
        'partner_uuid',
        'date_since',
        'date_till',
        'hours',
        'email',
        'change_business',
    )
    form_class = CalendarVisibilityEditForm
    template_name = 'admin/custom_views/calendar_visibility_template.html'

    success_url = view_base_name
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Batch Update :: Calendar Visibility'
        context['save_button_title'] = 'Update'
        return context
