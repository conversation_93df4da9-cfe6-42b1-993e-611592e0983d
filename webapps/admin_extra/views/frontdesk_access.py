import pandas
from django.contrib import messages
from django.shortcuts import redirect, render
from django.urls import reverse

from webapps.admin_extra.custom_permissions_classes import FormView, GroupPermissionMixin

from webapps.admin_extra.forms.frontdesk_access import FrontdeskAccessForm
from webapps.business.enums import CustomData
from webapps.business.models import Business
from webapps.business.models.business_change import BusinessChange
from webapps.user.groups import GroupNameV2
from webapps.user.tools import get_user_from_django_request


class UnknownBusinessIdsError(Exception):
    def __init__(self, missing_ids, msg='Unable to find businesses with given ids'):
        super().__init__(msg)

        self.missing_ids = missing_ids


def update_accesses_from_csv(data_sheet, user, grant_access=True):
    data_reader = pandas.read_csv(data_sheet, header=None)
    business_ids = data_reader[0].to_list()
    businesses = Business.objects.filter(
        id__in=business_ids,
    )

    missing_ids = set(business_ids) - set(businesses.values_list('id', flat=True))

    if len(missing_ids) != 0:
        raise UnknownBusinessIdsError(missing_ids)

    changed_business_ids = []
    old_obj_vars = {}
    new_obj_vars = {}

    for business in businesses.iterator():
        has_access = business.has_frontdesk_enabled()

        can_use_frontdesk_change = None

        if has_access and not grant_access:
            can_use_frontdesk_change = False
        elif not has_access and grant_access:
            can_use_frontdesk_change = True

        if can_use_frontdesk_change is not None:
            old_obj_vars[business.id] = {
                'custom_data': dict(business.custom_data),
            }

            business.custom_data[CustomData.CAN_USE_FRONTDESK] = can_use_frontdesk_change
            business.save()

            changed_business_ids.append(business.id)

            new_obj_vars[business.id] = {
                'custom_data': dict(business.custom_data),
            }

    BusinessChange.bulk_change(changed_business_ids, user, old_obj_vars, new_obj_vars)

    return len(changed_business_ids)


class FrontdeskAccessView(GroupPermissionMixin, FormView):
    permission_required = ()
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    template_name = 'admin/custom_views/frontdesk_access.html'
    form_class = FrontdeskAccessForm

    def post(self, request, *args, **kwargs):
        form = FrontdeskAccessForm(request.POST, request.FILES)

        if not form.is_valid():
            return render(request, self.template_name, {'form': form})

        try:
            disable = form.cleaned_data.get('disable', False)
            business_id_csv = form.cleaned_data['sheet']

            user = get_user_from_django_request(request)

            update_accesses_from_csv(business_id_csv, user, not disable)
        except UnknownBusinessIdsError as ex:
            missing_ids = ', '.join(map(str, list(ex.missing_ids)[:10]))

            messages.error(
                request,
                'CSV file contained non-existent businesses. \n'
                'For example (Note that this is list of up'
                ' to 10 missing ids without order guarantees.): \n'
                f'[{missing_ids}] \n'
                'Please check provided csv file and remove wrong ids.',
            )
        except pandas.errors.EmptyDataError:
            messages.error(request, 'Empty *.csv file was provided.')
        except ValueError:
            messages.error(request, 'Error when parsing *.csv file. \nPlease check file format.')
        except Exception as ex:  # pylint: disable=broad-except
            messages.error(
                request,
                'Unexpected error occurred. \n'
                'Please check your *.csv file or contact development team.'
                f'{ex}',
            )
        else:
            if disable:
                messages.success(
                    request, 'All businesses listed in csv file have frontdesk access disabled.'
                )
            else:
                messages.success(
                    request, 'All businesses listed in csv file have frontdesk access granted.'
                )
        return redirect(reverse('admin:frontdesk_access'))
