from botocore.exceptions import ClientError
from django.utils.translation import gettext

from webapps.admin_extra.custom_permissions_classes import (
    FormView,
    GroupPermissionMixin,
    LegacyPermissionBypassMixin,
)
from webapps.admin_extra.forms.enterprise_batch_import import (
    EnterpriseDataImportForm,
)
from webapps.admin_extra.tasks import enterprise_import_task
from webapps.admin_extra.views.utils import (
    ImportFileMixin,
    admin_logger,
)
from webapps.business.models.category import BusinessCategory
from webapps.user.groups import GroupNameV2


class EnterpriseDataImportView(
    ImportFileMixin, LegacyPermissionBypassMixin, GroupPermissionMixin, FormView
):
    permission_required = (
        'business.add_business',
        'user.add_user',
    )
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    form_class = EnterpriseDataImportForm
    template_name = 'admin/custom_views/enterprise_batch_import_template.html'
    success_url = 'enterprise_import'
    import_directory = 'enterprise_data_importer'

    def get(self, request, *args, **kwargs):  # pylint: disable=W0221
        admin_logger.info('%s enterprise_data_import GET', request.user.email)
        self.user = request.user
        return super().get(request, *args, **kwargs)

    def get_initial(self):
        initial = self.initial or {}
        default_category = BusinessCategory.objects.filter(
            type=BusinessCategory.CATEGORY,
            name=gettext('Financial Institutions'),
        ).first()
        if default_category:
            initial['category'] = default_category
        initial['report_email'] = self.user.email
        return initial

    def get_success_message(self, cleaned_data):
        email = cleaned_data.get('report_email').lower()
        return f'Report with result of enterprise import will be sent to {email}'

    def post(self, request, *args, **kwargs):
        admin_logger.info('%s enterprise_data_import POST', request.user.email)
        self.user = request.user
        return super().post(request, *args, **kwargs)

    def form_valid(self, form):
        cleaned_data = form.cleaned_data

        try:
            file_path = self.save_file('import_file', form)
        except ClientError:
            form.add_error('import_file', 'Unable to upload file to S3')
            return self.form_invalid(form)

        partner = cleaned_data['partner']
        categories_ids = list(cleaned_data['categories'].values_list('id', flat=True))

        enterprise_import_data = {
            'primary_category_id': cleaned_data['primary_category'].id,
            'categories_ids': categories_ids if categories_ids else [],
            'add_to_existing_network': cleaned_data['add_to_existing_network'],
            'network_name': cleaned_data['network_name'],
            'network_slug': cleaned_data['network_slug'],
            'owner_email': cleaned_data['owner_email'].lower(),
            'partner_uuid': partner.uuid if partner else None,
            'staffers_email': cleaned_data['staffers_email'].lower(),
        }

        enterprise_import_task.delay(
            businesses_file_path=file_path,
            report_email=cleaned_data['report_email'].lower(),
            enterprise_import_data=enterprise_import_data,
        )

        return super().form_valid(form)
