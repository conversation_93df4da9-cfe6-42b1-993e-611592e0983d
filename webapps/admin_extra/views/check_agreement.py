from django.shortcuts import render

from lib import safe_json
from webapps.admin_extra.custom_permissions_classes import (
    FormView,
    GroupPermissionMixin,
    LegacyPermissionBypassMixin,
)
from webapps.admin_extra.forms.check_agreement import CheckAgreementForm
from webapps.business.models.business_change import BusinessChange
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.user.groups import GroupNameV2
from webapps.user.tools import get_user_from_django_request


class CheckAgreementView(LegacyPermissionBypassMixin, GroupPermissionMixin, FormView):
    """
    Tool that allows to overwrite GDPR for customer cards in one business.
    """

    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    form_class = CheckAgreementForm
    template_name = 'admin/custom_views/check_agreement.html'
    permission_required = ('business.change_business',)

    def post(self, request, *args, **kwargs):
        from lib.rivers import bump_document, River

        form = self.get_form()
        if form.is_valid():
            cleaned_data = form.cleaned_data
            business_id = cleaned_data.pop('business_id')
            operator = get_user_from_django_request(request)
            bcis = BusinessCustomerInfo.objects.filter(
                business_id=business_id,
            )
            updated = bcis.update(**cleaned_data)
            bump_document(
                River.BUSINESS_CUSTOMER,
                list(bcis.values_list('id', flat=True)),
                _origin='CheckAgreementView',
            )
            metadata = {
                'endpoint': 'CheckAgreementView',
                'number_bci_updated': updated,
                'params': cleaned_data,
            }
            business_log = BusinessChange(
                business_id=business_id,
                operator=operator,
                data=safe_json.dumps({}, pretty=True),
                metadata=safe_json.dumps(metadata, pretty=True),
            )
            business_log.save()
        form = self.form_class()
        return render(
            request,
            self.template_name,
            {'form': form},
        )
