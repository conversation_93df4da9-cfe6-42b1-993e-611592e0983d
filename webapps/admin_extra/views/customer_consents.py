from django.contrib.messages.views import SuccessMessageMixin

from bo_obs.datadog.enums import BooksyTeams
from webapps.admin_extra.custom_permissions_classes import (
    FormView,
    GroupPermissionMixin,
    LegacyPermissionBypassMixin,
)
from webapps.admin_extra.forms.customer_consents import CustomerConsentsForm
from webapps.business.tasks import change_bci_web_consents_to_false_task
from webapps.user.groups import GroupNameV2


class RemoveCustomerConsentsView(
    SuccessMessageMixin, LegacyPermissionBypassMixin, GroupPermissionMixin, FormView
):
    """
    Sets BCI's web_communication_agreement to False for selected businesses.
    """

    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)
    form_class = CustomerConsentsForm
    success_url = 'customer_consents'
    permission_required = ('business.edit_business',)
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    template_name = 'admin/custom_views/customer_consents.html'

    def get_success_message(self, cleaned_data):
        return 'Customer consents were removed.'

    def form_valid(self, form):
        change_bci_web_consents_to_false_task.delay(self.request.user.id, **form.cleaned_data)

        return super().form_valid(form)
