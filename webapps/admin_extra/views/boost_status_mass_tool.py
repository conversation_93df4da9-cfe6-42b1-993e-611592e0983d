from django.contrib import messages

from webapps.admin_extra.boost_mass_tools import (
    change_boost_status_run_tasks,
    BusinessesToChangeBoostStatus,
    ImportFileWithXLSXExampleMixin,
)
from webapps.admin_extra.custom_permissions_classes import FormView, GroupPermissionMixin
from webapps.admin_extra.forms.boost_status_mass_tool import BoostChangeStatusForm
from webapps.admin_extra.views.utils import admin_logger
from webapps.user.groups import GroupNameV2


class BoostChangeStatusView(ImportFileWithXLSXExampleMixin, GroupPermissionMixin, FormView):
    """
    Enable or disable boost for given businesses in xlsx
    """

    permission_required = ()
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    form_class = BoostChangeStatusForm
    template_name = 'admin/custom_views/boost_status_mass_tool.html'
    example_file_name = 'boost_change_status_businesses.xlsx'

    def post(self, request, *args, **kwargs):
        form = self.get_form()
        context = self.get_context_data(form=form)
        if not form.is_valid():
            context.update({'errors': form.errors})
            return self.render_to_response(context)

        admin_logger.info('%s boost_change_status POST', request.user.email)
        import_file = form.cleaned_data['import_file']
        mode = form.cleaned_data['mode']
        validated_businesses = BusinessesToChangeBoostStatus.validate_from_file(import_file, mode)
        if validated_businesses.check_errors():
            error_messages = validated_businesses.create_error_messages()
            context.update({'errors': error_messages})

        else:
            email = form.cleaned_data.get('email')
            change_boost_status_run_tasks(validated_businesses, mode, email, request.user)
            messages.add_message(
                request,
                messages.SUCCESS,
                self.success_message.format(email=email),
            )
            context.update(
                {
                    'success_message': (
                        "No errors. Boost status will be changed for all businesses."
                    ),
                }
            )
        return self.render_to_response(context)
