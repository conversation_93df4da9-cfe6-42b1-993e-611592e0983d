from django import forms
from webapps.admin_extra.import_tools.consts import FileImportConsts

from webapps.admin_extra.forms.mixins import ImportFileFormMixin

from webapps.admin_extra.custom_permissions_classes import FormView, GroupPermissionMixin
from webapps.admin_extra.views.utils import CeleryFileTaskMixin
from webapps.best_of_booksy.tasks import create_business_booksy_awards
from webapps.user.groups import GroupNameV2


class BestOfBooksyGeneratorForm(ImportFileFormMixin, forms.Form):
    CONTENT_TYPES = FileImportConsts.CONTENT_TYPES_XLSX_XLS


class BestOfBooksyBusinessAwardsView(CeleryFileTaskMixin, GroupPermissionMixin, FormView):
    """
    Upload best of booksy business awards
    """

    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    permission_required = ()
    form_class = BestOfBooksyGeneratorForm
    template_name = 'admin/custom_views/best_of_booksy_business_award_upload.html'
    import_directory = 'best_of_booksy_business_awards'
    import_file_name = 'import_file'
    import_file_names = (import_file_name,)
    celery_task = create_business_booksy_awards
    success_url = 'best_of_booksy_business_awards_upload'
    success_message = 'Successfully scheduled business awards generation.'
