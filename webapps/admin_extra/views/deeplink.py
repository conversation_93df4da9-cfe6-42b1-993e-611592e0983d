import json

from django.contrib import messages
from django.conf import settings
from django.contrib.messages.views import SuccessMessageMixin
from django.shortcuts import render
from django.utils.html import format_html

from lib.deeplink import (
    DEEP_LINKS,
    DeepLinkCache,
    generate_deeplink,
)
from webapps.admin_extra.custom_permissions_classes import (
    FormView,
    GroupPermissionMixin,
    LegacyPermissionBypassMixin,
    TemplateView,
)
from webapps.admin_extra.forms.deeplink import (
    CreateInviteMPDeeplinkForm,
    DeepLinkDetailsForm,
    DeepLinkGeneratorForm,
    GeneralDeepLinkGeneratorForm,
)
from webapps.business.models import Business
from webapps.business.models.category import BusinessCategory
from webapps.experiment_v3.management.commands.common import get_app_type_and_read_deeplink_details
from webapps.user.groups import GroupNameV2


class DeepLinkGeneratorView(
    SuccessMessageMixin, LegacyPermissionBypassMixin, GroupPermissionMixin, FormView
):
    permission_required = ('business.edit_business',)
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    template_name = 'admin/custom_views/deeplink_generate_form.html'
    form_class = DeepLinkGeneratorForm
    success_url = 'deeplink_generator'

    def get_success_message(self, cleaned_data):
        region = cleaned_data.get('region')
        if region is not None:
            region_id = cleaned_data['region'].id
        else:
            # get_operating_country().id
            region_id = -1

        mobile_text = self.get_mobile_deeplink_text(
            region_id,
            category_id=cleaned_data.get('category'),
            sort_order=cleaned_data.get('sort_order'),
        )
        desktop_url = self.get_desktop_deeplink_url(
            region=region,
            category_id=cleaned_data.get('category'),
            sort_order=cleaned_data.get('sort_order'),
        )
        deeplink = generate_deeplink(
            app_type='C',
            data={
                'mobile_deeplink': mobile_text,
                '$desktop_url': desktop_url,
            },
        )
        return format_html('deeplink: {}<br>destination: {}', deeplink, mobile_text)

    @staticmethod
    def get_mobile_deeplink_text(region_id, category_id=None, sort_order=None):
        if sort_order is None:
            sort_order = 'score'
        if category_id is None:
            category_id = -1

        mobile_deeplink = f'search/{category_id}/{region_id}/{sort_order}'
        return mobile_deeplink

    @staticmethod
    def get_desktop_deeplink_url(region=None, category_id=-1, sort_order=None):
        #  General form of link to mp
        #  text = 'nail-salon/11_new-york-city?sortOrder=popularity'
        parts = []
        if category_id != -1:
            category_slug = (
                BusinessCategory.objects.filter(id=category_id)
                .values_list('slug', flat=True)
                .first()
            )
            if category_slug:
                parts.append(f'{category_slug}')

        if region is not None:
            if parts:
                parts.append('/')
            parts.append(f'{region.id}_{region.slug}')

        if sort_order is not None:
            if not parts:
                parts.append('all')
            parts.append(f'?sortOrder={sort_order}')

        if not parts:
            text = 'all'
        else:
            text = ''.join(parts)
        return DeepLinkGeneratorView.desktop_url(text)

    @staticmethod
    def desktop_url(deeplink_text):
        link = f'https://{settings.BOOKSY_DOMAIN}/'

        if settings.LOCAL_DEPLOYMENT:
            # on localhost redirect to uat
            link = 'https://uat.booksy.pm/'

        url = f'{link}{settings.LANGUAGE_CODE[:2]}-{settings.API_COUNTRY}/{deeplink_text}'
        return url


class GeneralDeepLinkGeneratorView(
    SuccessMessageMixin, LegacyPermissionBypassMixin, GroupPermissionMixin, FormView
):
    permission_required = ('business.edit_business',)
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    template_name = 'admin/custom_views/general_deeplink_generate_form.html'
    form_class = GeneralDeepLinkGeneratorForm
    success_url = 'general_deeplink_generator'

    def get_success_message(self, cleaned_data):
        mobile_text = self.get_mobile_deeplink_text(
            deeplink_type=cleaned_data.get('deeplink_type'),
            deeplink_body=cleaned_data.get('deeplink_body'),
        )

        return generate_deeplink(
            app_type='C',
            data={
                'mobile_deeplink': mobile_text,
                '$desktop_url': settings.MARKETPLACE_URL,
            },
        )

    @staticmethod
    def get_mobile_deeplink_text(deeplink_type, deeplink_body):
        mobile_deeplink = f'{deeplink_type}{deeplink_body}'
        return mobile_deeplink


class DeepLinkListView(GroupPermissionMixin, TemplateView):
    """
    List of deeplinks available for country.
    """

    permission_required = ()
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    template_name = 'admin/custom_views/deeplink_list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['deeplinks'] = [(key, DeepLinkCache.get(key)) for key in DEEP_LINKS]
        return context


class DeepLinkDetailsView(GroupPermissionMixin, FormView):
    """
    Shows information about deeplink
    """

    permission_required = ()
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    template_name = 'admin/custom_views/deeplink_read_details_form.html'
    form_class = DeepLinkDetailsForm
    success_url = 'deeplink_details'

    def form_valid(self, form):
        try:
            deeplink_info = get_app_type_and_read_deeplink_details(
                form.cleaned_data['deeplink_url']
            )
        except ValueError:
            deeplink_info = {'error': 'BranchIO url that was provided is invalid.'}
        context = {
            "json_pretty": json.dumps(deeplink_info, indent=4, sort_keys=True),
        }
        return render(self.request, 'admin/custom_views/deeplink_details.html', context=context)


class GenerateInviteMPDeeplinkView(SuccessMessageMixin, FormView):
    """Create invite mp deeplink with custom channel, campaign and feature"""

    permission_required = ()
    form_class = CreateInviteMPDeeplinkForm
    template_name = 'admin/custom_views/generate_invite_mp_deeplink.html'
    success_url = 'generate_mp_invite_deeplink'

    def post(self, request, *args, **kwargs):
        business_id = int(kwargs.get('business_id'))
        form = self.get_form()
        if form.is_valid():
            business = Business.objects.get(id=business_id)
            dl = business.get_mp_deeplink(  # pylint: disable=invalid-name
                feature=form.cleaned_data['feature'],
                channel=form.cleaned_data['channel'],
                campaign=form.cleaned_data['campaign'],
                ignore_activity=True,
            )
            messages.success(
                request,
                f'Deeplink generated for {business.name}: {dl}',
            )
        return render(request, self.template_name, {'form': form})
