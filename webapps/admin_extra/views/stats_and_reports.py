import urllib

from django.contrib import messages
from django.http import HttpResponse

from webapps.admin_extra.custom_permissions_classes import (
    FormView,
    GroupPermissionMixin,
)
from webapps.admin_extra.forms.stats_and_reports import (
    DownloadStatisticsReportsForm,
)
from webapps.admin_extra.views.utils import FillEmailMixin
from webapps.stats_and_reports.serializers import (
    ReportRequestSerializer,
    ReportSendByEmailRequestSerializer,
)
from webapps.stats_and_reports.tasks import send_report_by_email_task
from webapps.user.groups import GroupNameV2


class DownloadStatisticsReportsView(FillEmailMixin, GroupPermissionMixin, FormView):
    """
    Statistics and Reports made in Booksy 3.0.
    """

    permission_required = ()
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    form_class = DownloadStatisticsReportsForm
    template_name = 'admin/custom_views/generic_form_template.html'
    success_message = 'Download started'
    success_url = 'download_reports'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Download Statistics Report 3.0'
        context['save_button_title'] = 'Download Report'
        return context

    def form_valid(self, form):
        if form.cleaned_data['email']:
            self.send_email_report(form)
        else:
            serializer = ReportRequestSerializer(
                data=form.cleaned_data,
                context={'business_id': form.cleaned_data['business_id']},
            )
            if serializer.is_valid():
                report = serializer.get_report(
                    form.cleaned_data['business'],
                    form.cleaned_data['language'],
                    paginate=False,
                )
                filename = urllib.parse.quote(report.filename)
                file = report.get_spreadsheet().read()
                resp = HttpResponse(file, content_type='application/octet-stream')
                resp['Content-Disposition'] = f'attachment; filename={filename}'
                return resp

            # Reports may be visible globally but disabled for some businesses.
            messages.warning(self.request, "Unsupported report")
            return self.form_invalid(form)

        return super().form_valid(form)

    @staticmethod
    def send_email_report(form):
        serializer = ReportSendByEmailRequestSerializer(
            data=form.cleaned_data,
            context={'business_id': form.cleaned_data['business_id']},
        )
        if serializer.is_valid():
            validated_data = serializer.validated_data
            send_report_by_email_task.delay(
                form.cleaned_data['business'].id,
                validated_data['date_from'].isoformat() if validated_data['date_from'] else None,
                validated_data['date_till'].isoformat() if validated_data['date_till'] else None,
                validated_data['time_span'],
                validated_data['report_key'],
                validated_data['email'],
                form.cleaned_data['language'],
            )
