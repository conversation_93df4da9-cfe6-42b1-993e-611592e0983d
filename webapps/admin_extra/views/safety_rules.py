from webapps.admin_extra.custom_permissions_classes import FormView, GroupPermissionMixin
from webapps.admin_extra.forms.safety_rules import SafetyRulesSetForm
from webapps.business.models import Business
from webapps.user.groups import GroupNameV2


class SafetyRulesSetView(GroupPermissionMixin, FormView):
    permission_required = ()
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    template_name = 'admin/custom_views/set_safety_rules.html'
    success_url = 'set_safety_rules'
    form_class = SafetyRulesSetForm

    def form_valid(self, form):
        business_ids = form.cleaned_data['business_ids']
        safety_rules = form.cleaned_data['safety_rules']

        businesses = Business.objects.filter(id__in=business_ids)

        for business in businesses:
            business.safety_rules.set(safety_rules)

        return super().form_valid(form)
