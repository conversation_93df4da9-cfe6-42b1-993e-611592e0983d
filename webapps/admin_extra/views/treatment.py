from bo_obs.datadog.enums import BooksyTeams
from django.contrib import messages
from django.db.models import Count
from django.forms import formset_factory
from django.shortcuts import render

from webapps.admin_extra.custom_permissions_classes import (
    FormView,
    GroupPermissionMixin,
    LegacyPermissionBypassMixin,
)
from webapps.admin_extra.forms.treatment import TreatmentMatchForm
from webapps.business.models import Service
from webapps.marketplace.models import MatchTreatmentLog
from webapps.user.groups import GroupNameV2


class TreatmentMatchView(LegacyPermissionBypassMixin, GroupPermissionMixin, FormView):
    booksy_teams = (BooksyTeams.CUSTOMER_SEARCH,)
    permission_required = 'business.match_treatment'
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    form_class = formset_factory(TreatmentMatchForm, extra=0)
    template_name = 'admin/custom_views/match_treatment.html'
    success_url = 'match_treatment'

    def get_matches(self):
        return MatchTreatmentLog.objects.filter(
            id__in=[obj.id for obj in MatchTreatmentLog.assign_new(self.request.user.id)]
        ).select_related(
            'service__business',
            'service__business__owner',
            'service__business__primary_category',
        )

    def get_form(self):  # pylint: disable=arguments-differ
        return self.form_class(
            initial=[
                {
                    'match_treatment_id': match_treatment.id,
                    'match_treatment': match_treatment,
                }
                for match_treatment in self.get_matches()
            ]
        )

    def get_filled_form(self, formset):
        initial = []
        for i, form in enumerate(formset):
            cleaned_data = form.cleaned_data
            match_treatment = MatchTreatmentLog.objects.get(
                id=cleaned_data.get('match_treatment_id')
            )

            initial.append(
                {
                    'match_treatment_id': match_treatment.id,
                    'match_treatment': match_treatment,
                    'treatment': form.data.get(f'form-{i}-treatment'),
                    'no_match': cleaned_data.get('no_match', False),
                }
            )
        return self.form_class(initial=initial)

    @staticmethod
    def set_counter_message(request):
        services_left = Service.get_unassigned_to_treatment().count()
        user_counter = dict(
            MatchTreatmentLog.objects.values_list('status')
            .filter(user=request.user)
            .annotate(Count('status'))
        )
        unconfirmed = user_counter.get('M', 0)
        confirmed = user_counter.get('C', 0)
        rejected = user_counter.get('R', 0)
        messages.info(
            request,
            f'Treatments matched. {unconfirmed} - unconfirmed, {confirmed} - confirmed,'
            f' {rejected} - rejected, {services_left} - left',
        )

    def get(self, request, *args, **kwargs):
        self.set_counter_message(request)
        return render(
            request,
            'admin/custom_views/match_treatment.html',
            {
                'form': self.get_form(),
            },
        )

    def post(self, request, *args, **kwargs):
        formset = self.form_class(data=request.POST)
        if formset.is_valid():
            for form in formset:
                cleaned_data = form.cleaned_data
                match_treatment = MatchTreatmentLog.objects.get(
                    id=cleaned_data['match_treatment_id']
                )
                treatment = cleaned_data.get('treatment')
                no_match = cleaned_data.get('no_match', False)
                match_treatment.treatment = treatment
                match_treatment.no_match = no_match
                match_treatment.save()

            MatchTreatmentLog.done(request.user.id)
            messages.success(request, "Sucessfully matched")
            formset = self.get_form()
        else:
            for err in formset.errors:
                messages.error(request, err)
            formset = self.get_filled_form(formset)
        self.set_counter_message(request)
        return render(
            request,
            'admin/custom_views/match_treatment.html',
            {
                'form': formset,
            },
        )
