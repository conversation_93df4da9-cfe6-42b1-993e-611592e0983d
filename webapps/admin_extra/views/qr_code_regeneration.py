from bo_obs.datadog.enums import BooksyTeams
from django.contrib.messages.views import SuccessMessageMixin

from webapps.admin_extra.custom_permissions_classes import FormView, GroupPermissionMixin
from webapps.admin_extra.forms.qr_code_regeneration import QrCodeRegenerationForm
from webapps.qr_code_origami.generator import (
    BusinessPDFQRCodeGenerator,
    BusinessQRCodeGenerator,
)
from webapps.user.groups import GroupNameV2


class QrCodeRegenerationView(SuccessMessageMixin, GroupPermissionMixin, FormView):

    booksy_teams = (BooksyTeams.PROVIDER_MARKETING,)
    permission_required = ()
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    template_name = 'admin/custom_views/qrcode_regeneration.html'
    success_url = 'qrcode_regeneration'
    form_class = QrCodeRegenerationForm

    def get_success_message(self, cleaned_data):
        return 'QR code was regenerated!'

    def form_valid(self, form):
        business_id = form.cleaned_data.get('business_id')
        language = form.cleaned_data.get('language_code')

        BusinessQRCodeGenerator(business_id=business_id).force_generate()
        if language:
            BusinessPDFQRCodeGenerator(business_id=business_id, language=language).force_generate()

        return super().form_valid(form)
