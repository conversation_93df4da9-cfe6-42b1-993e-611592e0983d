from webapps.admin_extra.custom_permissions_classes import (
    FormView,
    GroupPermissionMixin,
    LegacyPermissionBypassMixin,
)
from webapps.admin_extra.forms.update_padding_time import UpdatePaddingTimeForm
from webapps.admin_extra.tasks import update_padding_time_task
from webapps.user.groups import GroupNameV2


class UpdatePaddingTimeView(LegacyPermissionBypassMixin, GroupPermissionMixin, FormView):
    """
    Update padding time for all services for one business.
    """

    permission_required = ('business.edit_business',)
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    template_name = 'admin/custom_views/generic_form_template.html'
    success_url = 'update_padding_time'
    success_message = 'Service padding times updated successfully'
    form_class = UpdatePaddingTimeForm

    def form_valid(self, form):
        cleaned_data = form.cleaned_data
        update_padding_time_task.delay(
            business_id=cleaned_data['business_id'],
            padding_type=cleaned_data['padding_type'],
            padding_time=int(cleaned_data['padding_time']),
        )
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Update padding time and/or type of business services'
        context['save_button_title'] = 'Update'
        return context
