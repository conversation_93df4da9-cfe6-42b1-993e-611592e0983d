from django.contrib import messages

from webapps.admin_extra.boost_mass_tools import (
    BusinessesToChangeBoostPaymentSource,
    ImportFileWithXLSXExampleMixin,
)
from webapps.admin_extra.custom_permissions_classes import (
    FormView,
    GroupPermissionMixin,
)
from webapps.admin_extra.forms.boost_change_payment_source_mass_tool import (
    BoostChangePaymentSourceForm,
)
from webapps.admin_extra.views.utils import admin_logger
from webapps.boost.tasks import boost_payment_source_change_task
from webapps.user.groups import GroupNameV2


class BoostChangePaymentSourceView(ImportFileWithXLSXExampleMixin, GroupPermissionMixin, FormView):
    permission_required = ()
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    form_class = BoostChangePaymentSourceForm
    template_name = 'admin/custom_views/boost_change_payment_source_mass_tool.html'
    example_file_name = 'boost_change_payment_source_template.xlsx'

    def post(self, request, *args, **kwargs):
        form = self.get_form()
        context = self.get_context_data(form=form)
        if not form.is_valid():
            context.update({'errors': form.errors})
            return self.render_to_response(context)

        admin_logger.info('%s boost_change_payment_method POST', request.user.email)
        import_file = form.cleaned_data['import_file']

        validated_businesses = BusinessesToChangeBoostPaymentSource.validate_from_file(
            import_file, mode=None
        )
        if validated_businesses.check_errors():
            error_messages = validated_businesses.create_error_messages()
            context.update({'errors': error_messages})

        else:
            businesses_ids = validated_businesses.businesses_ids()
            email = form.cleaned_data.get('email')
            boost_payment_source_change_task.delay(
                businesses_ids=businesses_ids,
                email=email,
            )
            messages.add_message(
                request,
                messages.SUCCESS,
                self.success_message.format(email=email),
            )
            context.update(
                {
                    'success_message': (
                        "No errors. Boost payment sources will be changed for all given businesses."
                    ),
                }
            )
        return self.render_to_response(context)
