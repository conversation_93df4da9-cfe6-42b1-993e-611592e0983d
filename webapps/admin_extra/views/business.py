from botocore.exceptions import Client<PERSON>rror
from bo_obs.datadog.enums import BooksyTeams
from django.contrib import messages
from django.contrib.messages.views import SuccessMessageMixin

from lib.rivers import River, bump_document
from webapps.admin_extra import consts
from webapps.admin_extra.custom_permissions_classes import (
    FormView,
    GroupPermissionMixin,
    LegacyPermissionBypassMixin,
)
from webapps.admin_extra.forms.business import (
    BusinessChurnSwitchForm,
    BusinessFreezeSwitchForm,
    BusinessStatusChangeForm,
    BusinessTrialExtendForm,
)
from webapps.admin_extra.tasks import (
    business_churn_switch_task,
    business_freeze_switch_task,
    mass_business_old_fizjo_disable_task,
    mass_business_retrial_switch_task,
)
from webapps.admin_extra.tasks.mass_revert_business_from_invalid import (
    mass_business_revert_from_invalid_status_task,
)
from webapps.admin_extra.views.billing import CSVImportDryRunForm
from webapps.admin_extra.views.utils import (
    CeleryFileTaskMixin,
    FormViewWithUserFromRequest,
    ImportFileMixin,
    admin_logger,
)
from webapps.business.models import (
    Business,
)
from webapps.business.models.business_change import BusinessChange
from webapps.business.tasks import (
    extend_businesses_trial_task,
    segment_intercom_status_update_businesses,
)
from webapps.user.tools import get_user_from_django_request
from webapps.user.groups import GroupName, GroupNameV2


class BusinessTrialExtendView(
    ImportFileMixin, LegacyPermissionBypassMixin, GroupPermissionMixin, FormView
):
    """
    Possibility to extend trial for businesses for 1 month
    """

    permission_required = ('business.edit_business',)
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    form_class = BusinessTrialExtendForm
    template_name = 'admin/custom_views/business_trial_extension.html'
    import_directory = 'trial_extend'
    import_file_name = 'import_file'
    import_file_names = (import_file_name,)
    success_message = 'Successfully extended businesses trial'

    def post(self, request, *args, **kwargs):
        form = self.get_form()
        if form.is_valid():
            admin_logger.info('%s business_trial_extension POST', request.user.email)
            try:
                excel_path = self.save_file(self.import_file_name, form)
                trial_extend_by = form.cleaned_data.get('trial_extend_by')
            except ClientError:
                form.add_error(
                    self.import_file_name,
                    'Unable to upload file to S3',
                )
                return self.form_invalid(form)

            extend_businesses_trial_task.delay(
                trial_extend_by,
                excel_path,
            )
            messages.add_message(
                request,
                messages.SUCCESS,
                self.success_message,
            )

        context = self.get_context_data(form=form)
        return self.render_to_response(context)


class BusinessChurnSwitchView(
    CeleryFileTaskMixin,
    LegacyPermissionBypassMixin,
    GroupPermissionMixin,
    FormViewWithUserFromRequest,
):
    """
    Churn/undo churn businesses listed in .xlsx file,
    disregarding payment source.
    """

    permission_required = ('business.edit_business',)
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    form_class = BusinessChurnSwitchForm
    template_name = 'admin/custom_views/businesses_churn_switch.html'
    success_url = 'businesses_churn_switch'
    import_directory = 'business_churn'
    import_file_names = ('import_file',)
    celery_task = business_churn_switch_task
    celery_task_kwargs = ('action', 'email', 'operator_id')


class BusinessFreezeSwitchView(
    CeleryFileTaskMixin, LegacyPermissionBypassMixin, GroupPermissionMixin, FormView
):
    """
    Switch offline merchants from Paid to POB status (& vice versa).
    (Freeze/unfreeze businesses listed in .xls file.
    Takes only 1st column, should have column header).
    """

    permission_required = ('business.edit_business',)
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    form_class = BusinessFreezeSwitchForm
    template_name = 'admin/custom_views/businesses_freeze_switch.html'
    success_url = 'businesses_freeze_switch'
    import_directory = 'business_freeze'
    import_file_names = ('import_file',)
    celery_task = business_freeze_switch_task
    celery_task_kwargs = ('action', 'email')


class BusinessStatusChangeView(SuccessMessageMixin, GroupPermissionMixin, FormView):
    """
    Trial -> TEB; POA -> POB; Every status -> BI
    """

    permission_required = ()
    form_class = BusinessStatusChangeForm
    template_name = 'admin/custom_views/business_status_changer.html'
    success_url = 'business_status_changer'
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR, GroupName.MASS_STATUS_EDITOR)

    def post(self, request, *args, **kwargs):
        self._admin_user = request.user.user
        return super().post(request, *args, **kwargs)

    def get_success_message(self, cleaned_data):
        status = dict(Business.Status.choices())[cleaned_data['transition']['to']]
        message = f"Updated {len(cleaned_data['business_ids'])} businesses to status {status}"
        return message

    def form_valid(self, form):
        transition = form.cleaned_data['transition']
        data = {
            'status': transition['to'],
        }
        if transition.get('active') is not None:
            data['active'] = transition['active']
        if transition.get('visible') is not None:
            data['visible'] = transition['visible']
        self.bulk_change_business_data(
            business_ids=form.cleaned_data['business_ids'], data=data, user=self._admin_user
        )

        return super().form_valid(form)

    @staticmethod
    def bulk_change_business_data(business_ids, data, user):
        """
        For each business in business_ids updated values
        of data.keys() appropriate value and create BusinessChange
        :param business_ids: iterable. with valid business_ids
        :param data: dict. Where key is name of
                        business field and value is new value
        :param user: User. User call a change
        :return: None
        """
        business_values = list(data.keys())
        # extract old values
        old_obj_vars = BusinessChange.extract_old_values_business(
            business_ids,
            business_values,
        )
        # form new values
        new_obj_vars = {'default': data}
        Business.objects.filter(id__in=business_ids).update(**data)
        # write BusinessChange history
        BusinessChange.bulk_change(
            business_ids=business_ids,
            operator=user,
            old_obj_vars=old_obj_vars,
            new_obj_vars=new_obj_vars,
            metadata={'endpoint': 'BusinessStatusChangerView'},
        )
        bump_document(River.BUSINESS, business_ids)
        segment_intercom_status_update_businesses.delay(
            businesses_ids=business_ids,
        )

    @staticmethod
    def change_business_data(business, data, user):
        before = BusinessChange.extract_vars(business)
        for attr, value in list(data.items()):
            setattr(business, attr, value)
        business.save()
        BusinessChange.add(
            business,
            business,
            before,
            operator=user,
            metadata={'endpoint': 'BusinessStatusChangerView'},
        )


class BusinessReTrialSwitchView(
    CeleryFileTaskMixin, LegacyPermissionBypassMixin, GroupPermissionMixin, FormView
):
    permission_required = ('business.edit_business',)
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    template_name = 'admin/custom_views/mass_business_retrial_switch.html'
    form_class = CSVImportDryRunForm
    import_file_names = ('import_file',)
    import_directory = 'business_retrial_switch'
    celery_task = mass_business_retrial_switch_task
    celery_task_kwargs = ('email', 'operator_id', 'dry_run')
    success_url = 'business_retrial_switch'

    @staticmethod
    def get_example_files():
        return [
            {
                'param': 'download_template',
                'path': 'statics/csv/business/mass_business_retrial_switch.csv',
                'mime': consts.CSV_MIME,
            },
        ]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Business ReTrial switch'
        return context

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = get_user_from_django_request(self.request)
        return kwargs


class BusinessMassRestoreFromInvalidView(
    CeleryFileTaskMixin,
    FormViewWithUserFromRequest,
    GroupPermissionMixin,
):
    """
    Reverting Invalid businesses to previous status.
    Allowed only if previous status was Blocked.
    After revert, we recalculate the status, if at that time it should change from ex PAID->OVERDUE
    then status will be updated to OVERDUE.
    """

    template_name = 'admin/custom_views/mass_business_revert_from_invalid.html'
    form_class = CSVImportDryRunForm
    import_file_names = ('import_file',)
    import_directory = 'business_restore_from_invalid'
    celery_task = mass_business_revert_from_invalid_status_task
    celery_task_kwargs = ('email', 'operator_id', 'dry_run')
    success_url = 'business_restore_from_invalid'
    permission_required = ()
    full_access_groups = (GroupName.MASS_STATUS_EDITOR, GroupNameV2.BASIC_MODERATOR)
    read_only_access_groups = ()

    @staticmethod
    def get_example_files():
        return [
            {
                'param': 'download_template',
                'path': 'statics/csv/business/mass_business_revert_from_invalid.csv',
                'mime': consts.CSV_MIME,
            },
        ]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Mass revert businesses from Invalid'
        return context


class BusinessDisableOldFizjoView(
    CeleryFileTaskMixin, LegacyPermissionBypassMixin, GroupPermissionMixin, FormView
):
    """
    Disable Old Fizjo (physiotherapy_enabled) for given businesses.
    """

    booksy_teams = (BooksyTeams.PROVIDER_CONVERSION,)
    permission_required = ('business.edit_business',)
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    template_name = 'admin/custom_views/mass_business_fizjo_disable.html'
    form_class = CSVImportDryRunForm
    import_file_names = ('import_file',)
    import_directory = 'disable_old_fizjo'
    celery_task = mass_business_old_fizjo_disable_task
    celery_task_kwargs = ('email', 'dry_run')
    success_url = 'disable_old_fizjo'

    @staticmethod
    def get_example_files():
        return [
            {
                'param': 'download_template',
                'path': 'statics/csv/business/mass_business_old_fizjo_disable.csv',
                'mime': consts.CSV_MIME,
            },
        ]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Business old Fizjo (physiotherapy_enabled) disable'
        return context

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = get_user_from_django_request(self.request)
        return kwargs
