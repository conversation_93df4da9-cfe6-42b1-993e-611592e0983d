import random

from dateutil.tz.tz import gettz
from django.conf import settings
from django.core.cache import cache
from django.contrib import messages
from django.shortcuts import render

from lib.email import prepare_and_send_email
from lib.tools import tznow
from webapps.admin_extra.custom_permissions_classes import (
    FormView,
    GroupPermissionMixin,
)
from webapps.admin_extra.forms.custom_push import CustomPushSendForm
from webapps.admin_extra.tasks import (
    query_and_send_customer_push_task,
    send_test_custom_push_email_notification,
)
from webapps.user.groups import GroupName, GroupNameV2


class CustomPushSendView(GroupPermissionMixin, FormView):
    form_class = CustomPushSendForm
    template_name = 'admin/custom_views/custom_push.html'
    success_url = 'custom_push_send_view'
    permission_denied_message = 'No permissions'
    permission_required = ()
    full_access_groups = (GroupName.CUSTOM_PUSH_ALLOWED, GroupNameV2.BUSINESS_EDITOR)

    @staticmethod
    def finish_with_kwargs(request, **kwargs):
        return render(request, 'admin/custom_views/custom_push.html', kwargs)

    def get_form_kwargs(self):
        form_kwargs = super().get_form_kwargs()
        form_kwargs['disabled'] = self.is_too_late()
        return form_kwargs

    def get_initial(self):
        initial = self.initial or {}
        initial['user_email'] = self.request.user.email
        return initial

    def post(self, request, *args, **kwargs):
        form = self.get_form()

        too_late = self.is_too_late()
        if not form.is_valid() or too_late:
            if too_late:
                messages.error(request, "Too late, try again during the day")
            return super().post(request, *args, **kwargs)

        data = form.cleaned_data
        message = data['message']
        user_id = data['user_id']
        no_android = data['no_android']
        no_ios = data['no_ios']
        gender = data['gender']
        push_before_days = int(data['push_before'])
        last_booking_before_days = int(data['last_booking_before'])
        booking_in_category_last_year = data['booking_in_category']
        last_booking_in_category = data['last_booking_in_category']

        cache_key = '/'.join(['custom_push_send', self.request.user.email])
        android_delivery = not no_android
        ios_delivery = not no_ios
        specific_user = user_id or 'No'
        success_message = (
            f'Message: {message} \n'
            f'Android delivery: {android_delivery} \n'
            f'iOS users: {ios_delivery} \n'
            f'Specific user: {specific_user} \n'
            f'Gender: {gender} \n'
            f'Push before: {push_before_days} \n'
            f'Last booking before: {last_booking_before_days} \n'
            f'Booking in category: {booking_in_category_last_year} \n'
            f'Last booking in category: {last_booking_in_category}'
        )

        if 'send' in request.POST:
            query_and_send_customer_push_task.apply_async(
                kwargs={
                    'message': message,
                    'no_android': no_android,
                    'no_ios': no_ios,
                    'gender': gender,
                    'push_before_days': push_before_days,
                    'last_booking_before_days': last_booking_before_days,
                    'booking_in_category_last_year': booking_in_category_last_year,
                    'last_booking_in_category': last_booking_in_category,
                    'user_email': self.request.user.email,
                },
                custom_queue='custom_push_notification',
            )
            success_message = (
                f'Mass push successfully initialized as described below: \n{success_message}'
            )

            success_message = success_message.replace("\n", "<br>")
            messages.success(request, success_message)
            cache.delete(cache_key)

            prepare_and_send_email(
                to_addr=self.request.user.email,
                body=success_message,
                subject='[AUTOMATED REPORT] Custom push send',
                bcc=settings.MASS_CUSTOM_PUSH_BACKUP_MAIL,
            )
        elif 'test_send' in request.POST:
            password = self.generate_one_time_password()
            form_to_cache = dict(data)
            form_to_cache['onetimepassword'] = password
            cache.set(cache_key, form_to_cache, 86400)

            send_test_custom_push_email_notification.apply_async(
                kwargs={
                    'success_message': success_message,
                    'gender': gender,
                    'push_before_days': push_before_days,
                    'last_booking_before_days': last_booking_before_days,
                    'booking_in_category_last_year': booking_in_category_last_year,
                    'last_booking_in_category': last_booking_in_category,
                    'password': password,
                    'recipient': self.request.user.email,
                    'user_id': user_id,
                    'message': message,
                    'no_android': no_android,
                    'no_ios': no_ios,
                },
                custom_queue='custom_push_notification',
            )
            messages.success(
                request,
                "Success! Please wait for notification email with password",
            )

        return self.finish_with_kwargs(
            request,
            form=form,
            make=True,
        )

    @staticmethod
    def generate_one_time_password():
        return str(random.randint(100000, 1000000))

    @staticmethod
    def is_too_late():
        country_timezone_name = settings.COUNTRY_CONFIG.default_time_zone
        if not country_timezone_name:
            return True
        timezone = gettz(country_timezone_name)
        now = tznow()
        current_hour = now.astimezone(timezone).hour
        return not bool(9 <= current_hour <= 18)
