import mammoth
from django import forms
from django.contrib import messages
from django.core.validators import FileExtensionValidator
from django.http import HttpResponseBadRequest

from lib.spreadsheet import load_xlsx
from webapps.admin_extra.custom_permissions_classes import FormView, GroupPermissionMixin
from webapps.admin_extra.tasks.mail_pdf_sender import send_emails_with_pdf_task
from webapps.user.groups import GroupNameV2


def docx_to_html(docx_document):
    return mammoth.convert_to_html(docx_document).value


class MailPdfSenderForm(forms.Form):
    recipients_info_xlsx = forms.FileField(
        label='Xlsx file with data',
        required=True,
        validators=[FileExtensionValidator(allowed_extensions=['xlsx'])],
    )
    body_template_docx = forms.FileField(
        label='Docx file with email body template',
        required=True,
        validators=[FileExtensionValidator(allowed_extensions=['docx'])],
    )
    report_email_address = forms.EmailField(
        label='Email address where report from tool run will be sent', required=True
    )
    dry_run = forms.BooleanField(label='Dry run', required=False, initial=True)


class MailPdfSender(GroupPermissionMixin, FormView):
    permission_required = ()
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    template_name = 'admin/custom_views/mail_pdf_sender.html'
    form_class = MailPdfSenderForm
    success_url = 'mail_pdf_sender'

    def form_valid(self, form):
        xlsx_content = load_xlsx(form.cleaned_data['recipients_info_xlsx'].read())
        docx_content = docx_to_html(form.cleaned_data['body_template_docx'])
        dry_run = form.cleaned_data['dry_run']
        report_email_address = form.cleaned_data['report_email_address']
        send_emails_with_pdf_task.delay(
            xlsx_content=xlsx_content,
            docx_content=docx_content,
            dry_run=dry_run,
            report_email_address=report_email_address,
        )
        return super().form_valid(form)

    def form_invalid(self, form):
        messages.error(self.request, 'Form invalid')
        return HttpResponseBadRequest()
