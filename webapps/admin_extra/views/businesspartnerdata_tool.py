from django import forms

from webapps.admin_extra.businesspartnerdata import BusinessesToAttachImportUID
from webapps.admin_extra.custom_permissions_classes import FormView, GroupPermissionMixin
from webapps.admin_extra.forms.mixins import ImportFileFormMixin
from webapps.admin_extra.import_tools.consts import FileImportConsts
from webapps.admin_extra.views.utils import ImportFileMixin, admin_logger
from webapps.public_partners.tasks import create_business_partner_data_task
from webapps.user.groups import GroupNameV2


class BusinessPartnerDataForm(ImportFileFormMixin, forms.Form):
    CONTENT_TYPES = FileImportConsts.CONTENT_TYPES_XLSX_XLS


class BusinessPartnerDataView(ImportFileMixin, GroupPermissionMixin, FormView):
    """
    Create BusinessPartnerData objects with given business_id and import_uid
    """

    permission_required = ()
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    form_class = BusinessPartnerDataForm
    template_name = 'admin/custom_views/public_partners_businesspartnerdata_upload.html'

    def post(self, request, *args, **kwargs):
        form = self.get_form()
        context = self.get_context_data(form=form)
        if not form.is_valid():
            context.update({'errors': form.errors})
            return self.render_to_response(context)

        admin_logger.info('%s public_partners_businesspartnerdata POST', request.user.email)
        import_file = form.cleaned_data['import_file']
        try:
            validated_businesses = BusinessesToAttachImportUID.validate_from_file(import_file)
        except ValueError as error:
            context.update(
                {
                    'error_message': str(error),
                }
            )
            return self.render_to_response(context)

        if validated_businesses.check_errors():
            error_messages = validated_businesses.create_error_messages()
            context.update({'errors': error_messages})
        else:
            new_businesspartnerdata_objects = [
                [row.business_id, row.import_uid, getattr(row, 'importer_name', None)]
                for row in validated_businesses.rows_list
            ]
            create_business_partner_data_task.delay(new_businesspartnerdata_objects)
            context.update(
                {
                    'success_message': (
                        'No errors. Data from file will be updated to '
                        'public_partners_businesspartnerdata table'
                    ),
                    'warnings': validated_businesses.create_warning_messages(),
                }
            )

        return self.render_to_response(context)
