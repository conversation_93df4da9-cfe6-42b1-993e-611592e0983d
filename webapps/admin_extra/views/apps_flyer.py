from django.contrib import messages

from webapps.admin_extra.custom_permissions_classes import (
    FormView,
    GroupPermissionMixin,
    LegacyPermissionBypassMixin,
)
from webapps.admin_extra.forms.apps_flyer import AppsflyerLinkOperationForm
from webapps.admin_extra.tasks import (
    appsflyer_shortcut_generic_link_task,
    appsflyer_shortcut_link_task,
)
from webapps.user.groups import GroupNameV2


class AppsflyerLinkOperationsView(LegacyPermissionBypassMixin, GroupPermissionMixin, FormView):
    permission_required = ('business.edit_business',)
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    template_name = 'admin/custom_views/appsflyer_link_operations.html'
    form_class = AppsflyerLinkOperationForm
    success_url = 'appsflyer_operations'

    def post(self, request, *args, **kwargs):
        """
        :param request:
            file :
            row[0] -> Business id
            row[1] -> Appsflyer link
            row[2] -> Short link (segment id)
        :param args:
        :param kwargs:
        :return:
        """
        import csv
        from django.utils.encoding import smart_str
        from lib.tools import grouper

        form = self.get_form()
        if not form.is_valid():
            messages.error(request, 'Form is not valid')
            return super().post(request, *args, **kwargs)

        # prepare for more operations on appsflyer links
        # operation = form.cleaned_data['operation']

        email = form.cleaned_data['email']
        delimiter = smart_str(',')

        import_file = form.cleaned_data['import_file']

        try:
            data_for_links = [
                {
                    'biz_id': row[0].decode('utf-8-sig'),
                    'url': row[1].decode('utf-8-sig'),
                    'key': row[2].decode('utf-8-sig'),
                }
                for row in csv.reader(import_file, delimiter=delimiter)
                if row and all([row[0], row[1], row[2]])
            ]
        except ValueError:
            messages.error(
                request,
                'Error while parsing data for appsflyer link, change file encoding to UTF-8',
            )
        else:
            for data_for_links_group in grouper(data_for_links, 100):
                appsflyer_shortcut_link_task.delay(data_for_links_group, email)
            messages.success(request, 'file was uploaded, wait for email with report')

        return super().post(request, *args, **kwargs)


class AppsflyerGenericLinkOperationsView(GroupPermissionMixin, FormView):
    permission_required = 'business.edit_business'
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    template_name = 'admin/custom_views/appsflyer_generic_link_operations.html'
    form_class = AppsflyerLinkOperationForm
    success_url = 'appsflyer_operations'

    def post(self, request, *args, **kwargs):
        """
        :param request:
            file :
            row[0] -> Business id
            row[1] -> Appsflyer link
            row[2] -> Short link (segment id)
        :param args:
        :param kwargs:
        :return:
        """
        import csv
        from django.utils.encoding import smart_str
        from lib.tools import grouper

        form = self.get_form()
        if not form.is_valid():
            messages.error(request, 'Form is not valid')
            return super().post(request, *args, **kwargs)

        # prepare for more operations on appsflyer links
        # operation = form.cleaned_data['operation']

        email = form.cleaned_data['email']
        delimiter = smart_str(',')

        import_file = form.cleaned_data['import_file']

        try:
            data_for_links = [
                {
                    'url': row[0].decode('utf-8-sig'),
                    'key': row[1].decode('utf-8-sig'),
                }
                for row in csv.reader(import_file, delimiter=delimiter)
                if row and all([row[0], row[1]])
            ]
        except ValueError:
            messages.error(
                request,
                'Error while parsing data for appsflyer link, change file encoding to UTF-8',
            )
        else:
            for data_for_links_group in grouper(data_for_links, 100):
                appsflyer_shortcut_generic_link_task.delay(data_for_links_group, email)

            messages.success(request, 'file was uploaded, wait for email with report')

        return super().post(request, *args, **kwargs)
