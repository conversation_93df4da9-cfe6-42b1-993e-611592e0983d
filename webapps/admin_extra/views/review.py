import datetime
from base64 import b64encode

from django.contrib import messages
from django.contrib.auth.mixins import PermissionRequiredMixin
from django.contrib.messages.views import SuccessMessageMixin
from django.db import transaction
from django.shortcuts import (
    get_object_or_404,
    redirect,
    render,
)
from django.urls import reverse, reverse_lazy
from django.views.generic import DetailView

from lib.tools import WrongColumnNames, tznow
from webapps.admin_extra import consts
from webapps.admin_extra.custom_permissions_classes import (
    FormView,
    GroupPermissionMixin,
    LegacyPermissionBypassMixin,
    TemplateView,
)
from webapps.admin_extra.forms.review import (
    ReviewImportForm,
    ReviewImportedDeleteForm,
)
from webapps.admin_extra.import_utils import load_review_without_spec
from webapps.admin_extra.views.utils import (
    ImportFileMixin,
    admin_logger,
)
from webapps.photo.serializers import AdminReviewUploadPhotoSerializer
from webapps.reviews.import_reviews import (
    dry_run_with_email_report_task,
    setup_data_and_run_import_task,
)
from webapps.reviews.models import (
    Review,
    ReviewPhoto,
)
from webapps.reviews.tasks import recompute_business_reviews_task
from webapps.user.groups import GroupNameV2


class ReviewImportedDeleteView(SuccessMessageMixin, FormView):
    permission_required = ('reviews.change_review',)
    form_class = ReviewImportedDeleteForm
    template_name = 'admin/custom_views/delete_imported_reviews.html'

    def post(self, request, *args, **kwargs):
        business_id = int(kwargs.get('business_id'))
        form = self.get_form()
        if form.is_valid():
            queryset = Review.objects.filter(
                business_id=business_id,
                import_uid__isnull=False,
            )
            last = form.cleaned_data['last_hrs']
            if last:
                queryset = queryset.filter(created__gte=tznow() - datetime.timedelta(hours=last))
            deleted_reviews = queryset.delete()  # it's soft delete
            hrs_part = f'imported in last {last} hours ' if last else ''
            messages.success(
                request,
                f'{deleted_reviews} reviews {hrs_part}for biz: {business_id} deleted',
            )
            recompute_business_reviews_task.delay(
                business_id=business_id,
                business_reindex=True,
            )
        else:
            messages.error(
                request,
                form.errors,
            )
        return redirect(reverse('admin:delete_imported_reviews', args=(business_id,)))


class ReviewImportView(
    ImportFileMixin, LegacyPermissionBypassMixin, GroupPermissionMixin, FormView
):
    """
    Reviews importer
    """

    permission_required = ('reviews.add_review', 'reviews.change_review')
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    form_class = ReviewImportForm
    template_name = 'admin/custom_views/review_import.html'
    success_message = (
        "Reviews will be uploaded in up to 10 minutes if the file's format and headers are correct"
    )
    dry_run_with_email_message = 'Please check your email for dry run result'

    @staticmethod
    def get_example_files():
        return [
            {
                'param': 'download_template',
                'path': 'statics/xlsx/import_review_example/template_import_review.xlsx',
                'mime': consts.XLS_MIME,
            },
            {
                'param': 'download_example',
                'path': 'statics/xlsx/import_review_example/import_review_example.xlsx',
                'mime': consts.XLS_MIME,
            },
        ]

    def get(self, *args, **kwargs):
        admin_logger.info('%s customer_import GET', self.request.user.email)
        return super().get(*args, **kwargs)

    def post(self, request, *args, **kwargs):
        form = self.get_form()
        reviews, errors = [], []
        omitted, total_num_cus = 0, 0
        context = {
            'reviews': reviews,
            'omitted': omitted,
            'total_num_cus': total_num_cus,
            'num_customer_import': 0,
            'errors': errors,
            'form': form,
        }
        if not form.is_valid():
            return render(request, self.template_name, context)

        admin_logger.info("%s customer_import POST", request.user.email)
        import_file = form.cleaned_data['import_file']
        business_id = form.cleaned_data['business_id']
        source = form.cleaned_data['source']
        email = form.cleaned_data['email']
        if form.cleaned_data['dry_run_with_email_report']:
            dry_run_with_email_report_task.delay(
                b64encode(import_file.read()).decode(),
                business_id,
                email,
            )
            messages.success(self.request, self.dry_run_with_email_message)

        elif form.cleaned_data['dry_run']:
            try:
                reviews, omitted = load_review_without_spec(import_file, business_id)
            except WrongColumnNames as exc:
                messages.error(self.request, str(exc))
            context.update(
                {
                    'reviews': reviews,
                    'omitted': omitted,
                    'total_num_rev': len(reviews),
                    'num_reviews_import': (len(reviews) - omitted),
                }
            )

        else:
            setup_data_and_run_import_task.delay(
                raw_data=b64encode(import_file.read()).decode(),
                source=source,
                business_id=business_id,
                email=email,
            )
            messages.success(self.request, self.success_message)
        return render(request, self.template_name, context)


class ReviewPhotoUploadView(TemplateView):
    """Used for manual file upload."""

    permission_required = ('reviews.change_review',)
    template_name = 'admin/custom_views/review_photo_upload.html'

    def get_success_url(self):
        return reverse('admin:reviews_review_change', args=(self.kwargs['review_id'],))

    def get(self, request, review_id, *args, **kwargs):  # pylint: disable=W0221
        get_object_or_404(Review, id=review_id)
        return super().get(request, review_id, *args, **kwargs)

    def post(self, request, review_id):
        review = get_object_or_404(Review, id=review_id)

        photos = request.FILES.getlist('photo', [])
        photos = [{'photo': i} for i in photos]

        serializer = AdminReviewUploadPhotoSerializer(
            data=photos,
            many=True,
            context={'business_id': review.business_id},
        )
        if serializer.is_valid(raise_exception=False):
            with transaction.atomic():
                photos = serializer.save()
                ReviewPhoto.objects.bulk_create(
                    ReviewPhoto(review_id=review_id, photo=photo) for photo in photos
                )
            messages.success(request, 'Photos have been successfully uploaded.')
            return redirect(self.get_success_url())

        admin_logger.error('Error during review photo upload: %s', serializer.errors)
        messages.error(request, 'Wrong file format or file size!')
        return self.render_to_response(self.get_context_data())


class ReviewMarkFakeView(SuccessMessageMixin, PermissionRequiredMixin, DetailView):
    permission_required = ('reviews.change_review',)
    success_url = reverse_lazy('admin:reviews_review_changelist')
    template_name = 'admin/custom_views/mark_review_as_fake.html'
    model = Review

    FAKE_REVIEW_MARK = 'fake review'

    def get_object(self, queryset=None):
        return get_object_or_404(Review, id=self.kwargs.get('pk'))

    def post(self, request, *args, **kwargs):
        review = self.get_object()
        review.reason_for_deletion = self.FAKE_REVIEW_MARK
        review.delete()

        messages.success(request, f'Successfully marked as fake and removed {review}.')

        return redirect(self.success_url)
