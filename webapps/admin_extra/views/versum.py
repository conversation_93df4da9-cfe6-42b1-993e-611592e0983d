from botocore.exceptions import ClientError

from webapps.admin_extra import consts
from webapps.admin_extra.custom_permissions_classes import FormView, GroupPermissionMixin
from webapps.admin_extra.forms.versum import (
    VersumImportForm,
)
from webapps.admin_extra.tasks import versum_file_parser_task
from webapps.admin_extra.views.utils import (
    ImportFileMixin,
    admin_logger,
)
from webapps.user.groups import GroupNameV2


class VersumDataImportView(ImportFileMixin, GroupPermissionMixin, FormView):
    """
    Importer for versum files (services, customers and bookings).
    For customer imports we recommend using the "Customer Importer"
    """

    permission_required = 'business.edit_business'
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    form_class = VersumImportForm
    template_name = 'admin/custom_views/versum_import.html'
    success_url = 'versum_import'
    import_directory = 'versum'
    import_file_names = (
        'customer_file',
        'service_file',
        'booking_file',
    )

    @staticmethod
    def get_example_files():
        return [
            {
                'param': 'customers_example',
                'path': 'statics/xls/versum_import/versum_customers_example.xls',
                'mime': consts.XLS_MIME,
            },
            {
                'param': 'booking_example',
                'path': 'statics/xls/versum_import/versum_bookings_example.xls',
                'mime': consts.XLS_MIME,
            },
            {
                'param': 'service_example',
                'path': 'statics/xls/versum_import/versum_services_example.xls',
                'mime': consts.XLS_MIME,
            },
        ]

    def get(self, *args, **kwargs):
        admin_logger.info("%s versum_data_import GET", self.request.user.email)
        return super().get(*args, **kwargs)

    def get_success_message(self, cleaned_data):
        email = cleaned_data.get('email')
        if not email:
            return 'No email was provided. The report will not be sent'
        return f'Report with result of Versum-import will be sent to {email}'

    def form_valid(self, form):
        cleaned_data = form.cleaned_data
        try:
            file_paths = self.save_files(form)
        except ClientError:
            form.add_error('customer_file', 'Unable to upload file to S3')
            return self.form_invalid(form)
        if not file_paths:
            form.add_error('customer_file', 'No files selected.')
            return self.form_invalid(form)

        context = self.get_context_data(form=form)

        task_args = {
            'business_id': cleaned_data['business_id'],
            'email': cleaned_data.get('email'),
            'customer_file': file_paths.get('customer_file'),
            'service_file': file_paths.get('service_file'),
            'booking_file': file_paths.get('booking_file'),
            'notify_clients': cleaned_data.get('notify_clients'),
            'invite_clients': cleaned_data.get('invite_clients'),
            'dry_run_with_email_report': cleaned_data.get('dry_run_with_email_report'),
        }

        if form.cleaned_data['dry_run']:
            data_dict = versum_file_parser_task(**task_args, dry_run=True)
            context.update({'data_dict': data_dict})
            return self.render_to_response(context)

        versum_file_parser_task.delay(**task_args)
        return super().form_valid(form)
