import base64

from django.conf import settings
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from django.shortcuts import (
    get_object_or_404,
    redirect,
)
from django.urls import reverse

from bo_obs.datadog.enums import BooksyTeams
from webapps.admin_extra import consts
from webapps.admin_extra.custom_permissions_classes import (
    FormView,
    GroupPermissionMixin,
    LegacyPermissionBypassMixin,
    TemplateView,
)
from webapps.admin_extra.forms.customer import (
    CustomerImportedDeleteForm,
    CustomerImportForm,
    DeleteAllCustomersForm,
)
from webapps.admin_extra.import_utils import load_customers_without_spec
from webapps.admin_extra.models import BusinessCustomerImportLog
from webapps.admin_extra.views.utils import (
    ImportFileMixin,
    admin_logger,
)
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business.tasks import (
    delete_imported_cutomers_task,
    parse_and_import_customers,
    import_customers_dry_run_with_email_report_task,
)
from webapps.user.groups import GroupName, GroupNameV2


class CustomerImportView(
    ImportFileMixin, LegacyPermissionBypassMixin, GroupPermissionMixin, FormView
):
    """
    Best tool to import customer base to PX account.
    """

    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    permission_required = (
        'user.add_user',
        'user.change_user',
        'user.delete_user',
    )
    form_class = CustomerImportForm
    template_name = 'admin/custom_views/customer_import.html'
    success_message = 'File loaded successfully. Check {email} for import report.'
    dry_run_with_email_message = 'Please check your email for dry run result'

    def get_example_files(self):  # pylint: disable=arguments-differ
        form = self.get_form()
        context = self.get_context_data(form=form)
        file_suffix = '_gdpr' if context['is_gdpr'] else ''
        return [
            {
                'param': 'download_template',
                'path': (
                    'statics/xlsx/import_customer_example/'
                    f'template_import_customers{file_suffix}.xlsx'
                ),
                'mime': consts.XLS_MIME,
            },
            {
                'param': 'download_example',
                'path': (
                    'statics/xlsx/import_customer_example/'
                    f'import_customers_example{file_suffix}.xlsx'
                ),
                'mime': consts.XLS_MIME,
            },
        ]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        is_gdpr = settings.GDPR_COUNTRIES.get(
            settings.API_COUNTRY, settings.GDPR_COUNTRIES.get('default', False)
        )
        context['is_gdpr'] = is_gdpr
        return context

    def get(self, *args, **kwargs):
        admin_logger.info("%s customer_import GET", self.request.user.email)
        return super().get(*args, **kwargs)

    def post(self, request, *args, **kwargs):
        form = self.get_form()
        customers, errors = [], []
        omitted, total_num_cus, duplicated = 0, 0, 0
        if form.is_valid():
            admin_logger.info("%s customer_import POST", request.user.email)
            import_file = form.cleaned_data['import_file']
            import_file_data = import_file.read()
            business_id = form.cleaned_data['business_id']
            email = form.cleaned_data.get('email')

            if form.cleaned_data['dry_run_with_email_report']:
                import_customers_dry_run_with_email_report_task.delay(
                    base64.b64encode(import_file_data).decode(),
                    business_id,
                    email,
                )
                messages.success(self.request, self.dry_run_with_email_message)
            elif form.cleaned_data['dry_run']:
                # load customers list of dicts
                try:
                    customers, omitted, total_num_cus, duplicated = load_customers_without_spec(
                        import_file_data,
                        business_id=business_id,
                    )
                except Exception as e:  # pylint: disable=broad-except
                    errors.append(e.message)
            else:
                # CELERY
                parse_and_import_customers.delay(
                    data=base64.b64encode(import_file_data).decode(),
                    business_id=business_id,
                    email_report=email,
                    user_id=None,  # This value is intentionally set to None, don't touch it.
                )
                messages.add_message(
                    request, messages.SUCCESS, self.success_message.format(email=email)
                )

        context = self.get_context_data(form=form)
        context.update(
            {
                'customers': customers,
                'omitted': omitted,
                'total_num_cus': total_num_cus,
                'duplicated': duplicated,
                'num_customer_import': (total_num_cus - omitted),
                'errors': errors,
            }
        )
        return self.render_to_response(context)


class CustomerImportListView(TemplateView):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)
    permission_required = ('user.add_user', 'user.change_user')
    template_name = 'admin/custom_views/customer_imports_list.html'
    group = GroupName.CUSTOMER_IMPORT_CLEANER

    def get(self, request, business_id, *args, **kwargs):  # pylint: disable=arguments-differ
        self.business_id = business_id
        return super().get(request, *args, **kwargs)

    def _get_imports(self, page: int | None = 1, bcis_amount: int | None = 10) -> (dict, Paginator):
        """
        Returns dict with import_uids as key and a list of their BCIs as value
        """
        import_uids = (
            BusinessCustomerInfo.objects.filter(
                Q(import_uid__isnull=False) | Q(import_uid__exact=''),
                business_id=self.business_id,
            )
            .distinct()
            .order_by('import_uid')
            .values_list('import_uid', flat=True)
        )
        paginator = Paginator(import_uids, 3)  # Show 3 imports per page.
        customers = {
            import_uid: BusinessCustomerInfo.objects.filter(
                import_uid=import_uid,
                business_id=self.business_id,
            ).values(
                'id',
                'import_uid',
                'first_name',
                'last_name',
                'email',
                'cell_phone',
                'address_line_1',
                'address_line_2',
                'business_secret_note',
                'visible_in_business',
            )[
                :bcis_amount
            ]
            for import_uid in paginator.get_page(page).object_list
        }
        return customers, paginator

    def get_context_data(self, **kwargs):
        page_number = self.request.GET.get('page', 1)
        bcis_amount = self.request.GET.get('bcis_amount', 10)
        bcis_amount = int(bcis_amount) if bcis_amount != 'all' else None
        imports, paginator = self._get_imports(page=page_number, bcis_amount=bcis_amount)
        kwargs['imports'] = imports
        kwargs['business_id'] = self.business_id
        kwargs['has_user_delete_perm'] = self.has_delete_perm()
        page_obj = paginator.get_page(page_number)
        kwargs['page_obj'] = page_obj
        return super().get_context_data(**kwargs)

    def has_delete_perm(self):
        return (
            self.request.user.groups.filter(name=self.group).exists()
            or self.request.user.is_superuser
        )


class CustomerImportLogView(TemplateView):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)
    permission_required = ('user.add_user', 'user.change_user')
    template_name = 'admin/custom_views/customer_import_log.html'

    def get_context_data(self, **kwargs):
        import_log = get_object_or_404(BusinessCustomerImportLog, id=self.kwargs['import_log_id'])
        context = super().get_context_data(**kwargs)
        context['import_uid'] = import_log.import_uid
        context['business'] = import_log.business
        context['customers'] = BusinessCustomerInfo.objects.filter(
            import_uid=import_log.import_uid
        ).values(
            'id',
            'import_uid',
            'first_name',
            'last_name',
            'email',
            'cell_phone',
            'address_line_1',
            'address_line_2',
            'business_secret_note',
            'visible_in_business',
        )

        return context


class CustomerAllDeleteView(GroupPermissionMixin, FormView):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)
    form_class = DeleteAllCustomersForm
    permission_required = ()
    full_access_groups = (GroupName.CUSTOMER_IMPORT_CLEANER,)

    def get(self, request, business_id, *args, **kwargs):  # pylint: disable=arguments-differ
        return redirect(reverse('admin:customer_imports', args=(business_id,)))

    def post(self, request, business_id, *args, **kwargs):  # pylint: disable=arguments-differ
        form = self.get_form()
        if form.is_valid():
            delete_imported_cutomers_task.delay(
                None,
                _delete_all_for_business_id=business_id,
            )
            messages.success(
                request,
                'Client Cards will be deleted in up to 10 minutes',
            )
        else:
            messages.error(
                request,
                form.errors,
            )
        return redirect(reverse('admin:customer_imports', args=(business_id,)))


class CustomerImportedDeleteView(GroupPermissionMixin, FormView):
    booksy_teams = (BooksyTeams.PROVIDER_ENGAGEMENT,)
    permission_required = ()
    form_class = CustomerImportedDeleteForm
    full_access_groups = (GroupName.CUSTOMER_IMPORT_CLEANER,)

    def get(self, request, business_id, *args, **kwargs):  # pylint: disable=arguments-differ
        return redirect(reverse('admin:customer_imports', args=(business_id,)))

    def post(self, request, business_id, *args, **kwargs):  # pylint: disable=arguments-differ
        form = self.get_form()
        if form.is_valid():
            import_uid = form.data['import_uid']
            delete_imported_cutomers_task.delay(import_uid)
            messages.success(
                self.request,
                'Client Cards will be deleted in up to 10 minutes',
            )
        else:
            messages.error(
                request,
                form.errors,
            )
        return redirect(reverse('admin:customer_imports', args=(business_id,)))
