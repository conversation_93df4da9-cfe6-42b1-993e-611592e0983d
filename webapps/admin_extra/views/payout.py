from django.contrib.messages.views import SuccessMessageMixin

from webapps.admin_extra.custom_permissions_classes import FormView, GroupPermissionMixin
from webapps.admin_extra.forms.payout import (
    B2BReferralRewardPayOutForm,
)
from webapps.market_pay.tasks import (
    send_manual_reward_payout_task,
)
from webapps.user.groups import GroupNameV2


class B2BRewardPayoutView(SuccessMessageMixin, GroupPermissionMixin, FormView):
    """
    This is supposed to proceed with B2B referral reward manual payouts
    in countries where we do have the Adyen integration.
    """

    permission_required = ()
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    template_name = 'admin/custom_views/b2breward_payout.html'
    success_url = 'b2breferral_reward_payout'
    form_class = B2BReferralRewardPayOutForm

    def get_success_message(self, cleaned_data):
        return 'Payout was sent!'

    def form_valid(self, form):
        send_manual_reward_payout_task.delay(**form.cleaned_data)

        return super().form_valid(form)
