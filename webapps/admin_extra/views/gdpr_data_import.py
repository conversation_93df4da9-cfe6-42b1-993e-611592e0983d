from webapps.admin_extra.custom_permissions_classes import (
    Form<PERSON>iew,
    GroupPermissionMixin,
    LegacyPermissionBypassMixin,
)
from webapps.admin_extra.forms.gdpr_data_import import (
    BusinessGDPRDataImportForm,
)
from webapps.admin_extra.views.utils import CeleryFileTaskMixin
from webapps.business.tasks import import_business_gdpr_data_task
from webapps.user.groups import GroupNameV2


class BusinessGDPRDataImportView(
    CeleryFileTaskMixin, LegacyPermissionBypassMixin, GroupPermissionMixin, FormView
):
    permission_required = ('business.edit_business',)
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    form_class = BusinessGDPRDataImportForm
    template_name = 'admin/custom_views/generic_form_template.html'
    success_url = 'business_GDPR_data_import'
    success_message = 'Business GDPR data imported successfully'
    import_directory = 'business_GDPR_data'
    import_file_names = ('import_file',)
    celery_task = import_business_gdpr_data_task

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Import business data for gdpr annex'
        context['instructions'] = (
            'Provide xlsx file with columns in following order: '
            'business id, business name, e-mail, NIP, address'
        )
        context['save_button_title'] = 'Import'
        return context
