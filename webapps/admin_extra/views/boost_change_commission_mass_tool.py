from django.contrib import messages

from webapps.admin_extra.boost_mass_tools import (
    BusinessesToChangeCommission,
    ImportFileWithXLSXExampleMixin,
)
from webapps.admin_extra.custom_permissions_classes import FormView, GroupPermissionMixin
from webapps.admin_extra.forms.boost_change_commission_mass_tool import BoostChangeCommissionForm
from webapps.admin_extra.import_utils import load_stripped_xlsx
from webapps.admin_extra.views.utils import admin_logger
from webapps.user.groups import GroupNameV2


class BoostChangeCommissionMassTool(ImportFileWithXLSXExampleMixin, GroupPermissionMixin, FormView):
    permission_required = ()
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    form_class = BoostChangeCommissionForm
    template_name = 'admin/custom_views/boost_change_commission_mass_tool.html'
    example_file_name = 'boost_change_commission_template.xlsx'

    def post(self, request, *args, **kwargs):
        form = self.get_form()
        context = self.get_context_data(form=form)
        if not form.is_valid():
            context.update({'errors': form.errors})
            return self.render_to_response(context)

        admin_logger.info('%s boost_change_commission_mass_tool POST', request.user.email)
        import_file = form.cleaned_data['import_file']
        raw_file = import_file.read()
        raw_rows = load_stripped_xlsx(raw_file)

        validated_businesses = BusinessesToChangeCommission(raw_rows).validate_businesses()
        if validated_businesses.check_errors():
            error_messages = validated_businesses.create_error_messages()
            context.update({'errors': error_messages})

        else:
            email = form.cleaned_data.get('email')
            keep_payment_source_offline = form.cleaned_data.get('keep_payment_source_offline')
            validated_businesses.run_tasks(email, keep_payment_source_offline)
            messages.add_message(
                request,
                messages.SUCCESS,
                self.success_message.format(email=email),
            )
            context.update(
                {
                    'success_message': (
                        "No errors. Commissions will be changed for all given businesses."
                    ),
                }
            )
        return self.render_to_response(context)
