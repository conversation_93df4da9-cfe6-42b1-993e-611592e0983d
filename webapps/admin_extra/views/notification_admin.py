import typing

import pandas
from django.contrib import messages
from django.shortcuts import redirect, render
from django.urls import reverse

from lib.tools import grouper
from webapps.admin_extra.custom_permissions_classes import FormView, GroupPermissionMixin
from webapps.admin_extra.forms.manual_admin_notification import ManualAdminNotificationForm
from webapps.admin_extra.tasks import send_manual_admin_notification
from webapps.admin_extra.views.frontdesk_access import UnknownBusinessIdsError
from webapps.business.models import Business
from webapps.user.groups import GroupNameV2


class ManualAdminNotificationView(GroupPermissionMixin, FormView):
    permission_required = ()
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    template_name = 'admin/custom_views/manual_admin_notification.html'
    form_class = ManualAdminNotificationForm

    def post(self, request, *args, **kwargs):
        form = ManualAdminNotificationForm(request.POST, request.FILES)

        if not form.is_valid():
            return render(request, self.template_name, {'form': form})

        try:
            self.enqueue_notifications(form, self.request.user.email)
        except UnknownBusinessIdsError as err:
            messages.error(request, f'Notifications not queued! {err}: {err.missing_ids}')
            return redirect(reverse('admin:manual_admin_notification'))

        messages.success(request, 'Businesses notification queued successfully.')
        return redirect(reverse('admin:manual_admin_notification'))

    def enqueue_notifications(self, form, email):
        business_id_csv = form.cleaned_data['sheet']
        all_businesses = form.cleaned_data['all_businesses']

        if all_businesses:
            business_ids = Business.objects.filter(active=True).values_list('id', flat=True)
        else:
            business_ids = self.get_business_ids_from_file(business_id_csv)

        for package_ids in grouper(business_ids, 1000):
            send_manual_admin_notification.delay(
                package_ids,
                title=form.cleaned_data['title'],
                content=form.cleaned_data['content'],
                extra_text=form.cleaned_data['extra_text'],
                target_type=form.cleaned_data['target_type'],
                target_id=form.cleaned_data['target_id'] or None,
                size=int(form.cleaned_data['size']),
                icon=form.cleaned_data['icon'],
                crucial=form.cleaned_data['crucial'],
                relevance=int(form.cleaned_data['relevance']),
                to_addr=email,
            )

    @staticmethod
    def get_business_ids_from_file(data_sheet) -> typing.List[int]:
        data_reader = pandas.read_csv(data_sheet, header=None)
        file_business_ids = data_reader[0].to_list()
        business_ids = Business.objects.filter(id__in=file_business_ids, active=True).values_list(
            'id', flat=True
        )

        missing_ids = set(file_business_ids) - set(business_ids)

        if len(missing_ids) != 0:
            raise UnknownBusinessIdsError(missing_ids)

        return business_ids
