from bo_obs.datadog.enums import BooksyTeams
from django import forms
from django.conf import settings

from webapps.admin_extra import consts
from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin
from webapps.admin_extra.forms.mixins import DryRunFormMixin, ImportFileFormMixin
from webapps.admin_extra.import_tools.consts import FileImportConsts
from webapps.admin_extra.tasks import (
    mass_billing_business_discounts_task,
    mass_billing_business_offer_task,
    mass_billing_merchants_switcher_task,
    mass_billing_offer_purchase_task,
    mass_billing_offers_changer_task,
    mass_stripe_migration_tool,
    mass_switch_merchants_payment_processor_task,
    mass_switch_merchants_to_billing_task,
)
from webapps.admin_extra.tasks.mass_billing_purchase_flow_switcher import (
    mass_billing_purchase_flow_task,
)
from webapps.admin_extra.tasks.mass_business_extend_trial_by_given_days import (
    mass_business_extend_trial_by_given_days_task,
)
from webapps.admin_extra.tasks.mass_offline_to_billing_switch import mass_offline_to_billing_task
from webapps.admin_extra.tasks.mass_sms_price_and_limit_changer import mass_billing_sms_changer_task
from webapps.admin_extra.tasks.old_subscription_products import (
    mass_subscription_product_import_task,
)
from webapps.admin_extra.views.utils import CeleryFileTaskMixin, FormViewWithUserFromRequest
from webapps.billing.enums import PaymentProcessorType, PurchaseFlowAuthorization
from webapps.business.models import Business
from webapps.user.groups import GroupName, GroupNameV2


class UploadImportFileForm(ImportFileFormMixin, forms.Form):
    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user')
        super().__init__(*args, **kwargs)

    CONTENT_TYPES = FileImportConsts.CONTENT_TYPES_CSV
    email = forms.EmailField(label='Email for report')

    def clean(self):
        self.cleaned_data['operator_id'] = self.user.pk
        return self.cleaned_data


class CSVImportDryRunForm(DryRunFormMixin, UploadImportFileForm):
    pass


class MassSwitchMerchantsToBillingView(
    GroupPermissionMixin,
    CeleryFileTaskMixin,
    FormViewWithUserFromRequest,
):
    """
    Switch Merchants from old subscription to new Billing.
    """

    template_name = 'admin/custom_views/mass_switch_merchants_to_billing.html'
    form_class = CSVImportDryRunForm
    import_file_names = ('import_file',)
    import_directory = 'merchants_to_billing'
    celery_task = mass_switch_merchants_to_billing_task
    celery_task_kwargs = ('email', 'operator_id', 'dry_run')
    success_url = 'mass_switch_merchants_to_billing'
    permission_required = ()
    full_access_groups = (
        GroupName.BILLING_SUPERUSER,
        GroupName.BILLING_ADMIN,
        GroupName.BILLING_MANAGER,
    )

    @staticmethod
    def get_example_files():
        return [
            {
                'param': 'download_template',
                'path': 'statics/csv/billing/mass_switch_merchants_to_billing.csv',
                'mime': consts.CSV_MIME,
            },
        ]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Mass switch Merchants to Billing'
        return context


class MassSwitchMerchantsPaymentProcessorView(
    GroupPermissionMixin,
    CeleryFileTaskMixin,
    FormViewWithUserFromRequest,
):
    """
    Change Merchants processor type.
    """

    template_name = 'admin/custom_views/mass_switch_merchants_payment_processor.html'
    form_class = CSVImportDryRunForm
    import_file_names = ('import_file',)
    import_directory = 'merchants_payment_processor_change'
    celery_task = mass_switch_merchants_payment_processor_task
    celery_task_kwargs = ('email', 'dry_run')
    success_url = 'mass_switch_merchants_payment_processor'
    permission_required = ()
    full_access_groups = (
        GroupName.BILLING_SUPERUSER,
        GroupName.BILLING_ADMIN,
        GroupName.BILLING_MANAGER,
    )

    @staticmethod
    def get_example_files():
        return [
            {
                'param': 'download_template',
                'path': 'statics/csv/billing/mass_switch_merchants_payment_processor.csv',
                'mime': consts.CSV_MIME,
            },
        ]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Mass switch Merchants Payment Processor'
        context['avaliable_processors'] = ', '.join(PaymentProcessorType.choices_map().values())
        return context


class MassBusinessDiscountView(
    CeleryFileTaskMixin,
    FormViewWithUserFromRequest,
):
    """
    Creates discounts for billing businesses.
    """

    template_name = 'admin/custom_views/mass_billing_discounts_tool.html'
    form_class = CSVImportDryRunForm
    import_file_names = ('import_file',)
    import_directory = 'billing_discounts'
    celery_task = mass_billing_business_discounts_task
    celery_task_kwargs = ('email', 'operator_id', 'dry_run')
    success_url = 'mass_billing_discounts'
    permission_required = ('billing.add_billingbusinessdiscount',)

    @staticmethod
    def get_example_files():
        return [
            {
                'param': 'download_template',
                'path': 'statics/csv/billing/mass_business_discount_template.csv',
                'mime': consts.CSV_MIME,
            },
        ]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Mass billing discounts tool'
        return context


class MassBillingMerchantsSwitcherView(
    GroupPermissionMixin,
    CeleryFileTaskMixin,
    FormViewWithUserFromRequest,
):
    """
    Switch Merchants between old and new subscriptions.
    """

    template_name = 'admin/custom_views/mass_billing_switcher_tool.html'
    form_class = CSVImportDryRunForm
    import_file_names = ('import_file',)
    import_directory = 'merchants_switcher'
    celery_task = mass_billing_merchants_switcher_task
    celery_task_kwargs = ('email', 'operator_id', 'dry_run')
    success_url = 'mass_billing_merchants_switcher'
    permission_required = ()
    full_access_groups = (
        GroupName.BILLING_SUPERUSER,
        GroupName.BILLING_ADMIN,
        GroupName.BILLING_MANAGER,
    )

    @staticmethod
    def get_example_files():
        return [
            {
                'param': 'download_template',
                'path': 'statics/csv/billing/mass_billing_merchants_switcher_template.csv',
                'mime': consts.CSV_MIME,
            },
        ]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Mass switch Merchants between old and new subscriptions'
        context['payment_sources'] = Business.PaymentSource.choices()
        return context


class MassBillingStripeMigrator(
    GroupPermissionMixin,
    CeleryFileTaskMixin,
    FormViewWithUserFromRequest,
):
    """
    The migration tool should only be used with the csv
    file provided by Stripe after card token migration.
    """

    booksy_teams = (BooksyTeams.PROVIDER_CONVERSION,)
    template_name = 'admin/custom_views/mass_stripe_migration_tool.html'
    form_class = CSVImportDryRunForm
    import_file_names = ('import_file',)
    import_directory = 'mass_stripe_migration_tool'
    celery_task = mass_stripe_migration_tool
    celery_task_kwargs = ('email', 'dry_run')
    success_url = 'mass_stripe_migration_tool'
    permission_required = ()
    full_access_groups = (
        GroupName.BILLING_SUPERUSER,
        GroupName.BILLING_ADMIN,
        GroupName.BILLING_MANAGER,
    )

    @staticmethod
    def get_example_files():
        return [
            {
                'param': 'download_template',
                'path': 'statics/csv/billing/mass_stripe_migration_tool.csv',
                'mime': consts.CSV_MIME,
            },
        ]


class MassBillingSubscriptionSmsChanger(
    GroupPermissionMixin,
    CeleryFileTaskMixin,
    FormViewWithUserFromRequest,
):
    """
    Changing SMS limits and costs on active/pending subscriptions
    """

    template_name = 'admin/custom_views/mass_billing_sms_changer.html'
    form_class = CSVImportDryRunForm
    import_file_names = ('import_file',)
    import_directory = 'sms_changer'
    celery_task = mass_billing_sms_changer_task
    celery_task_kwargs = ('email', 'operator_id', 'dry_run')
    success_url = 'mass_sms_cost_and_limit_changer'
    permission_required = ()
    full_access_groups = (
        GroupName.BILLING_SUPERUSER,
        GroupName.BILLING_ADMIN,
        GroupName.BILLING_MANAGER,
    )

    @staticmethod
    def get_example_files():
        return [
            {
                'param': 'download_template',
                'path': 'statics/csv/billing/mass_sms_cost_and_limit_changer.csv',
                'mime': consts.CSV_MIME,
            },
        ]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Mass billing sms limit and cost changer'
        return context


class MassBillingBusinessOfferView(
    GroupPermissionMixin,
    CeleryFileTaskMixin,
    FormViewWithUserFromRequest,
):
    """
    Adds offer for businesses.
    """

    template_name = 'admin/custom_views/mass_billing_business_offer.html'
    form_class = CSVImportDryRunForm
    import_file_names = ('import_file',)
    import_directory = 'billing_business_offer'
    celery_task = mass_billing_business_offer_task
    celery_task_kwargs = ('email', 'operator_id', 'dry_run')
    success_url = 'mass_billing_business_offer'
    permission_required = ()
    full_access_groups = (
        GroupName.BILLING_SUPERUSER,
        GroupName.BILLING_ADMIN,
        GroupName.BILLING_MANAGER,
    )

    @staticmethod
    def get_example_files():
        return [
            {
                'param': 'download_template',
                'path': 'statics/csv/billing/mass_billing_business_offer_template.csv',
                'mime': consts.CSV_MIME,
            },
        ]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Mass billing Merchants offers'
        return context


class MassBillingOfferPurchaseView(
    GroupPermissionMixin,
    CeleryFileTaskMixin,
    FormViewWithUserFromRequest,
):
    """
    Creates subscriptions and does transaction.
    """

    template_name = 'admin/custom_views/mass_billing_offer_purchase.html'
    form_class = CSVImportDryRunForm
    import_file_names = ('import_file',)
    import_directory = 'billing_offer_purchase'
    celery_task = mass_billing_offer_purchase_task
    celery_task_kwargs = ('email', 'operator_id', 'dry_run')
    success_url = 'mass_billing_offer_purchase'
    permission_required = ()
    full_access_groups = (
        GroupName.BILLING_SUPERUSER,
        GroupName.BILLING_ADMIN,
        GroupName.BILLING_MANAGER,
    )

    @staticmethod
    def get_example_files():
        return [
            {
                'param': 'download_template',
                'path': 'statics/csv/billing/mass_billing_offer_purchase_template.csv',
                'mime': consts.CSV_MIME,
            },
        ]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Mass billing offer purchase'
        return context


class MassSubscriptionProductImport(
    GroupPermissionMixin, CeleryFileTaskMixin, FormViewWithUserFromRequest
):
    """
    Adds product Offers
    """

    template_name = 'admin/custom_views/old_subscription_products.html'
    form_class = CSVImportDryRunForm
    import_file_names = ('import_file',)
    import_directory = 'billing_product'
    celery_task = mass_subscription_product_import_task
    celery_task_kwargs = ('email', 'dry_run')
    success_url = 'mass_subscription_product_import'
    permission_required = ()
    full_access_groups = (
        GroupName.BILLING_SUPERUSER,
        GroupName.BILLING_ADMIN,
        GroupName.BILLING_MANAGER,
    )

    @staticmethod
    def get_example_files():
        return [
            {
                'param': 'download_template',
                'path': 'statics/csv/billing/mass_subscription_product_template.csv',
                'mime': consts.CSV_MIME,
            },
        ]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Mass Subscription Product Import'
        return context


class MassBillingOffersChangerView(
    GroupPermissionMixin,
    CeleryFileTaskMixin,
    FormViewWithUserFromRequest,
):
    """
    Adds offer for billing businesses.
    """

    template_name = 'admin/custom_views/mass_billing_offers_changer.html'
    form_class = CSVImportDryRunForm
    import_file_names = ('import_file',)
    import_directory = 'mass_billing_offers_changer'
    celery_task = mass_billing_offers_changer_task
    celery_task_kwargs = ('email', 'operator_id', 'dry_run')
    success_url = 'mass_billing_offers_changer'
    permission_required = ()
    full_access_groups = (
        GroupName.BILLING_SUPERUSER,
        GroupName.BILLING_ADMIN,
        GroupName.BILLING_MANAGER,
    )

    @staticmethod
    def get_example_files():
        return [
            {
                'param': 'download_template',
                'path': 'statics/csv/billing/mass_billing_offers_changer_template.csv',
                'mime': consts.CSV_MIME,
            },
        ]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Mass billing offers changer'
        return context


class MassBillingPurchaseFlowView(
    GroupPermissionMixin,
    CeleryFileTaskMixin,
    FormViewWithUserFromRequest,
):
    """
    Changing purchase flow
    """

    template_name = 'admin/custom_views/mass_billing_purchase_flow_changer.html'
    form_class = CSVImportDryRunForm
    import_file_names = ('import_file',)
    import_directory = 'mass_billing_purchase_flow_changer'
    celery_task = mass_billing_purchase_flow_task
    celery_task_kwargs = ('email', 'operator_id', 'dry_run')
    success_url = 'mass_billing_purchase_flow_changer'
    permission_required = ()
    full_access_groups = (
        GroupName.BILLING_SUPERUSER,
        GroupName.BILLING_ADMIN,
        GroupName.BILLING_MANAGER,
    )

    @staticmethod
    def get_example_files():
        return [
            {
                'param': 'download_template',
                'path': 'statics/csv/billing/mass_billing_purchase_flow_changer.csv',
                'mime': consts.CSV_MIME,
            },
        ]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['purchase_flows'] = PurchaseFlowAuthorization.choices()
        context['page_title'] = 'Mass billing purchase flow changer.'
        return context


class MassSwitchOfflineToBillingView(
    GroupPermissionMixin,
    CeleryFileTaskMixin,
    FormViewWithUserFromRequest,
):
    """
    Switch businesses from offline subscription to Billing.

    Note that this only starts campaign for offline/online
    migration for selected businesses.
    """

    template_name = 'admin/custom_views/mass_switch_offline_to_billing.html'
    form_class = CSVImportDryRunForm
    import_file_names = ('import_file',)
    import_directory = 'mass_switch_offline_to_billing'
    celery_task = mass_offline_to_billing_task
    celery_task_kwargs = ('email', 'operator_id', 'dry_run')
    success_url = 'mass_switch_offline_businesses_to_billing'
    permission_required = ()
    full_access_groups = (
        GroupName.BILLING_SUPERUSER,
        GroupName.BILLING_ADMIN,
        GroupName.BILLING_MANAGER,
    )

    @staticmethod
    def get_example_files():
        return [
            {
                'param': 'download_template',
                'path': 'statics/csv/billing/mass_switch_offline_to_billing.csv',
                'mime': consts.CSV_MIME,
            },
        ]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Mass switch offline businesses to Billing'
        context['campaing_buffer_hours'] = (
            settings.OFFLINE_TO_ONLINE_MIGRATION_CAMPAIGN_BUFFER_HOURS
        )
        return context


class MassBusinessExtendTrialByGivenDaysView(
    GroupPermissionMixin,
    CeleryFileTaskMixin,
    FormViewWithUserFromRequest,
):
    """
    Extend business trial end date by given days.
    """

    booksy_teams = (BooksyTeams.PROVIDER_CONVERSION,)
    template_name = 'admin/custom_views/mass_business_extend_trial_by_given_days.html'
    form_class = CSVImportDryRunForm
    import_file_names = ('import_file',)
    import_directory = 'trial_by_given_day_extender'
    celery_task = mass_business_extend_trial_by_given_days_task
    celery_task_kwargs = ('email', 'operator_id', 'dry_run')
    success_url = 'trial_by_given_day_extender'
    permission_required = ()
    full_access_groups = (
        GroupName.BILLING_SUPERUSER,
        GroupName.BILLING_ADMIN,
        GroupName.BILLING_MANAGER,
        GroupNameV2.BUSINESS_EDITOR,
    )

    @staticmethod
    def get_example_files():
        return [
            {
                'param': 'download_template',
                'path': 'statics/csv/billing/mass_business_extend_trial_by_given_days.csv',
                'mime': consts.CSV_MIME,
            },
        ]

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = (
            'Extend business trial end date by given days.(use only in emergency)'
        )
        return context
