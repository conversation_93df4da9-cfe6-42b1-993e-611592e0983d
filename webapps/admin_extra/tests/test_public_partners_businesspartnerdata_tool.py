from django.urls import reverse
from rest_framework import status

from webapps.admin_extra.tests import DjangoTestCase
from webapps.admin_extra.tests.test_boost_mass_tools import BoostMassToolsTestingMixin
from webapps.business.baker_recipes import business_recipe
from webapps.public_partners.models import BusinessPartnerData
from webapps.admin_extra.businesspartnerdata import get_list_duplicates


class BusinessPartnerDataTool(DjangoTestCase, BoostMassToolsTestingMixin):
    def setUp(self):
        self.login_admin()

    def get_response(self, raw_rows):
        response = self.client.post(
            reverse('admin:public_partners_businesspartnerdata_upload'),
            {'import_file': self._get_filestream(raw_rows)},
            follow=True,
        )
        return response

    def test_view_success(self):
        business = business_recipe.make()
        raw_rows = self._raw_rows_by_businesses_ids([business.id])
        raw_rows[0].append('1abc')
        self.assertFalse(BusinessPartnerData.objects.filter(business_id=business.id).exists())

        response = self.get_response(raw_rows)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        expected_message = (
            'No errors. Data from file will be updated to '
            'public_partners_businesspartnerdata table'
        )
        message = response.context_data['success_message']
        self.assertEqual(message, expected_message)
        self.assertTrue(BusinessPartnerData.objects.filter(business_id=business.id).exists())

    def test_view_success_with_importer_name(self):
        business = business_recipe.make()
        raw_rows = self._raw_rows_by_businesses_ids([business.id])
        raw_rows[0].append('1abc')
        raw_rows[0].append('Custom Importer')
        self.assertFalse(BusinessPartnerData.objects.filter(business_id=business.id).exists())

        response = self.get_response(raw_rows)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        expected_message = (
            'No errors. Data from file will be updated to '
            'public_partners_businesspartnerdata table'
        )
        message = response.context_data['success_message']
        self.assertEqual(message, expected_message)
        self.assertTrue(BusinessPartnerData.objects.filter(business_id=business.id).exists())

    def test_view_error_empty_file(self):
        raw_rows = []

        response = self.get_response(raw_rows)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        expected_message = 'Empty file. Please fill out the file'
        message = response.context_data['error_message']
        self.assertEqual(message, expected_message)

    def test_view_error_business_id_not_exists(self):
        raw_rows = self._raw_rows_by_businesses_ids(['7815223707'])
        raw_rows[0].append('1abc')

        response = self.get_response(raw_rows)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        expected_message = ['Row 1 | Business with this id does not exist']
        message = response.context_data['errors']
        self.assertEqual(message, expected_message)
        self.assertFalse(BusinessPartnerData.objects.filter(business_id=7815223707).exists())

    def test_view_error_business_id_invalid(self):
        raw_rows = self._raw_rows_by_businesses_ids(['business_id'])
        raw_rows[0].append('1abc')

        response = self.get_response(raw_rows)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        expected_message = ['Row 1 | Business id has to be a number.']
        message = response.context_data['errors']
        self.assertEqual(message, expected_message)
        self.assertFalse(BusinessPartnerData.objects.filter(import_uid='1abc').exists())

    def test_view_error_business_id_duplicates_in_file(self):
        business = business_recipe.make()
        raw_rows = self._raw_rows_by_businesses_ids([business.id, business.id])
        raw_rows[0].append('1abc')
        raw_rows[1].append('2abc')
        duplicates = get_list_duplicates([item[0] for item in raw_rows])

        response = self.get_response(raw_rows)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        expected_message = f'Remove duplicated business ids : {duplicates}'
        message = response.context_data['error_message']
        self.assertEqual(message, expected_message)
        self.assertFalse(BusinessPartnerData.objects.filter(business_id=business.id).exists())

    def test_view_error_import_uid_duplicates_in_file(self):
        business1 = business_recipe.make()
        business2 = business_recipe.make()
        raw_rows = self._raw_rows_by_businesses_ids([business1.id, business2.id])
        raw_rows[0].append('1abc')
        raw_rows[1].append('1abc')
        duplicates = get_list_duplicates([item[1] for item in raw_rows])

        response = self.get_response(raw_rows)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        expected_message = f'Remove duplicated imported uids : {duplicates}'
        message = response.context_data['error_message']
        self.assertEqual(message, expected_message)
        self.assertFalse(BusinessPartnerData.objects.filter(import_uid='1abc').exists())

    def test_view_error_import_uid_empty(self):
        business = business_recipe.make()
        raw_rows = self._raw_rows_by_businesses_ids([business.id])
        raw_rows[0].append(
            ' '
        )  # Use a space to avoid Excel stripping, but it should be treated as empty

        response = self.get_response(raw_rows)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        expected_message = ['Row 1 | Import_uid cannot be empty']
        message = response.context_data['errors']
        self.assertEqual(message, expected_message)
        self.assertFalse(BusinessPartnerData.objects.filter(import_uid='').exists())

    def test_view_error_business_id_empty(self):
        raw_rows = self._raw_rows_by_businesses_ids([''])
        raw_rows[0].append('1abc')

        response = self.get_response(raw_rows)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        expected_message = ['Row 1 | Rows cannot be empty.']
        message = response.context_data['errors']
        self.assertEqual(message, expected_message)
        self.assertFalse(BusinessPartnerData.objects.filter(import_uid='1abc').exists())

    def test_view_error_insufficient_columns(self):
        business = business_recipe.make()
        raw_rows = self._raw_rows_by_businesses_ids([business.id])
        # Only business_id, no import_uid

        response = self.get_response(raw_rows)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        expected_message = ['Row 1 | File must have at least 2 columns: Business ID and Import UID']
        message = response.context_data['errors']
        self.assertEqual(message, expected_message)
        self.assertFalse(BusinessPartnerData.objects.filter(business_id=business.id).exists())

    def test_view_error_extra_columns(self):
        business = business_recipe.make()
        raw_rows = self._raw_rows_by_businesses_ids([business.id])
        raw_rows[0].append('1abc')
        raw_rows[0].append('importer_name')
        raw_rows[0].append('extra column')

        response = self.get_response(raw_rows)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        expected_message = [
            'Row 1 | Remove extra columns from file. Check out tips for file structure'
        ]
        message = response.context_data['errors']
        self.assertEqual(message, expected_message)
        self.assertFalse(BusinessPartnerData.objects.filter(business_id=business.id).exists())

    def test_view_warning_object_with_business_id_exists(self):
        business = business_recipe.make()
        raw_rows = self._raw_rows_by_businesses_ids([business.id])
        raw_rows[0].append('1abc')
        raw_rows[0].append('Test Importer')  # Add importer column to avoid missing column warning
        BusinessPartnerData.objects.create(business=business, import_uid='2abc')

        response = self.get_response(raw_rows)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        expected_message = [
            'Row 1 | Business with that business_id already exists in '
            'public_partners_businesspartnerdata table and its import_uid will be updated.'
        ]
        message = response.context_data['warnings']
        self.assertEqual(message, expected_message)

    def test_view_warning_object_with_import_uid_exists(self):
        business1 = business_recipe.make()
        business2 = business_recipe.make()
        raw_rows = self._raw_rows_by_businesses_ids([business2.id])
        raw_rows[0].append('1abc')
        raw_rows[0].append('Test Importer')  # Add importer column to avoid missing column warning
        BusinessPartnerData.objects.create(business=business1, import_uid='1abc')

        response = self.get_response(raw_rows)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        expected_message = [
            'Row 1 | Record with given imported_uid already exists and will be duplicated.'
        ]
        message = response.context_data['warnings']
        self.assertEqual(message, expected_message)

    def test_view_warning_empty_importer_name(self):
        business = business_recipe.make()
        raw_rows = self._raw_rows_by_businesses_ids([business.id])
        raw_rows[0].append('1abc')
        raw_rows[0].append(' ')  # Empty importer name (space to prevent Excel stripping)

        response = self.get_response(raw_rows)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        expected_message = ['Row 1 | Importer name is empty, will use default value']
        message = response.context_data['warnings']
        self.assertEqual(message, expected_message)

    def test_view_object_with_business_id_exists_and_is_updated(self):
        business1 = business_recipe.make()
        raw_rows = self._raw_rows_by_businesses_ids([business1.id])
        raw_rows[0].append('new_uid')
        bpd = BusinessPartnerData.objects.create(business=business1, import_uid='1abc')

        response = self.get_response(raw_rows)
        bpd.refresh_from_db()
        self.assertEqual(BusinessPartnerData.objects.count(), 1)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(bpd.import_uid, 'new_uid')

    def test_view_object_with_import_uid_exists_and_is_duplicated(self):
        business1 = business_recipe.make()
        business2 = business_recipe.make()
        raw_rows = self._raw_rows_by_businesses_ids([business2.id])
        raw_rows[0].append('1abc')
        BusinessPartnerData.objects.create(business=business1, import_uid='1abc')
        response = self.get_response(raw_rows)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(BusinessPartnerData.objects.filter(import_uid='1abc').count(), 2)

    def test_view_business_integrations_updated_with_default_importer(self):
        business1 = business_recipe.make()
        raw_rows = self._raw_rows_by_businesses_ids([business1.id])
        raw_rows[0].append('1abc')
        response = self.get_response(raw_rows)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        business1.refresh_from_db()
        self.assertEqual(business1.integrations['importer'], 'Versum')

    def test_view_business_integrations_updated_with_custom_importer(self):
        business1 = business_recipe.make()
        raw_rows = self._raw_rows_by_businesses_ids([business1.id])
        raw_rows[0].append('1abc')
        raw_rows[0].append('Custom Partner Importer')
        response = self.get_response(raw_rows)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        business1.refresh_from_db()
        self.assertEqual(business1.integrations['importer'], 'Custom Partner Importer')

    def test_view_multiple_businesses_with_different_importers(self):
        business1 = business_recipe.make()
        business2 = business_recipe.make()
        raw_rows = self._raw_rows_by_businesses_ids([business1.id, business2.id])
        raw_rows[0].append('1abc')
        raw_rows[0].append('Partner A')
        raw_rows[1].append('2abc')
        raw_rows[1].append('Partner B')

        response = self.get_response(raw_rows)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        business1.refresh_from_db()
        business2.refresh_from_db()
        self.assertEqual(business1.integrations['importer'], 'Partner A')
        self.assertEqual(business2.integrations['importer'], 'Partner B')
