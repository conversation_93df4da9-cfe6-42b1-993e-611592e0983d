from http import <PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPStatus

import pytest
from django.contrib.auth.models import AnonymousUser
from django.core.exceptions import PermissionDenied
from django.test import RequestFactory

from country_config import Country
from lib.feature_flag.feature.admin import UseNewAdminGroupPermissions
from lib.tests.utils import override_eppo_feature_flag
from webapps.admin_extra.views.customer import CustomerImportView
from webapps.user.groups import GroupName, GroupNameV2


@pytest.mark.django_db
class TestGroupPermissionDispatch:
    @pytest.mark.parametrize(
        'ff_on',
        [True, False],
    )
    def test_access_denied_if_user_not_in_any_group(
        self,
        dispatchable_view_factory,
        user_not_in_any_group,
        ff_on,
    ):
        with override_eppo_feature_flag({UseNewAdminGroupPermissions.flag_name: ff_on}):
            dispatchable_view = dispatchable_view_factory(
                full_access_group_list=[
                    GroupName.BILLING_ADVANCED_USER,
                    GroupNameV2.BASIC_MODERATOR,
                ],
                read_only_group_list=[],
            )
            request = RequestFactory().get('/fake-url')
            request.user = user_not_in_any_group

            with pytest.raises(PermissionDenied):
                dispatchable_view.as_view()(request)

    @pytest.mark.parametrize(
        ('ff_on', 'expect_permission_denied'),
        [
            (True, True),
            (False, False),
        ],
    )
    def test_access_if_no_groups_defined(
        self,
        dispatchable_view_factory,
        user_not_in_any_group,
        ff_on,
        expect_permission_denied,
    ):
        with override_eppo_feature_flag({UseNewAdminGroupPermissions.flag_name: ff_on}):
            dispatchable_view = dispatchable_view_factory(
                full_access_group_list=[], read_only_group_list=[]
            )
            request = RequestFactory().get('/fake-url')
            request.user = user_not_in_any_group

            if expect_permission_denied:
                with pytest.raises(PermissionDenied):
                    dispatchable_view.as_view()(request)
            else:
                response = dispatchable_view.as_view()(request)
                assert response.status_code == HTTPStatus.OK

    @pytest.mark.parametrize(
        ('ff_on', 'expect_permission_denied'),
        [
            (True, True),
            (False, False),
        ],
    )
    @pytest.mark.parametrize(
        'request_method',
        [HTTPMethod.GET, HTTPMethod.POST, HTTPMethod.PUT, HTTPMethod.PATCH, HTTPMethod.DELETE],
    )
    def test_access_if_user_in_read_only_group(
        self,
        dispatchable_view_factory,
        user_in_groups_factory,
        request_method,
        ff_on,
        expect_permission_denied,
    ):
        with override_eppo_feature_flag({UseNewAdminGroupPermissions.flag_name: ff_on}):
            dispatchable_view = dispatchable_view_factory(
                full_access_group_list=[], read_only_group_list=[GroupNameV2.BASIC_MODERATOR]
            )
            request = getattr(RequestFactory(), request_method.lower())('/fake-url')
            request.user = user_in_groups_factory(group_list=[GroupNameV2.BASIC_MODERATOR])

            if expect_permission_denied:
                with pytest.raises(PermissionDenied):
                    dispatchable_view.as_view()(request)
            else:
                response = dispatchable_view.as_view()(request)
                assert response.status_code == HTTPStatus.OK

    @pytest.mark.parametrize(
        'ff_on',
        [True, False],
    )
    @pytest.mark.parametrize(
        'request_method',
        [HTTPMethod.GET, HTTPMethod.POST, HTTPMethod.PUT, HTTPMethod.PATCH, HTTPMethod.DELETE],
    )
    def test_get_access_granted_if_user_in_full_access_group(
        self,
        dispatchable_view_factory,
        user_in_groups_factory,
        request_method,
        ff_on,
    ):
        with override_eppo_feature_flag({UseNewAdminGroupPermissions.flag_name: ff_on}):
            dispatchable_view = dispatchable_view_factory(
                full_access_group_list=[
                    GroupName.BILLING_ADVANCED_USER,
                    GroupNameV2.BASIC_MODERATOR,
                ],
                read_only_group_list=[],
            )
            request = getattr(RequestFactory(), request_method.lower())('/fake-url')
            request.user = user_in_groups_factory(
                group_list=[GroupName.BILLING_ADVANCED_USER, GroupNameV2.BASIC_MODERATOR]
            )

            response = dispatchable_view.as_view()(request)

            assert response.status_code == HTTPStatus.OK

    @pytest.mark.parametrize(
        'ff_on',
        [True, False],
    )
    def test_superuser_has_get_access(self, dispatchable_view_factory, superuser, ff_on):
        with override_eppo_feature_flag({UseNewAdminGroupPermissions.flag_name: ff_on}):
            dispatchable_view = dispatchable_view_factory(
                full_access_group_list=[GroupNameV2.BASIC_MODERATOR], read_only_group_list=[]
            )
            request = RequestFactory().get('/fake-url')
            request.user = superuser

            response = dispatchable_view.as_view()(request)

            assert response.status_code == HTTPStatus.OK


@pytest.mark.django_db
class TestGroupPermissionMixinMethods:
    @pytest.mark.parametrize(
        ('ff_on', 'user_groups', 'full_access_groups', 'read_only_groups', 'expected'),
        [
            # FF ON
            (True, [GroupNameV2.BASIC_MODERATOR], [], [GroupNameV2.BASIC_MODERATOR], True),
            (True, [GroupNameV2.BASIC_MODERATOR], [GroupNameV2.BASIC_MODERATOR], [], True),
            (True, [], [GroupNameV2.BASIC_MODERATOR], [], False),
            # FF OFF
            (False, [GroupName.BILLING_ADMIN], [GroupName.BILLING_ADMIN], [], True),
            (False, [], [GroupName.BILLING_ADMIN], [], False),
            (
                False,
                [GroupNameV2.BASIC_MODERATOR],
                [GroupName.BILLING_ADMIN],
                [GroupNameV2.BASIC_MODERATOR],
                False,
            ),
        ],
    )
    def test_has_view_permission(
        self,
        protected_view_factory,
        user_in_groups_factory,
        ff_on,
        user_groups,
        full_access_groups,
        read_only_groups,
        expected,
    ):
        with override_eppo_feature_flag({UseNewAdminGroupPermissions.flag_name: ff_on}):
            user = user_in_groups_factory(user_groups)
            protected_view = protected_view_factory(
                full_access_group_list=full_access_groups,
                read_only_group_list=read_only_groups,
            )
            request = RequestFactory().get('/fake-url')
            request.user = user

            assert protected_view.has_view_permission(request) is expected

    @pytest.mark.parametrize(
        ('ff_on', 'user_groups', 'full_access_groups', 'read_only_groups', 'expected'),
        [
            # FF ON
            (True, [GroupNameV2.BASIC_MODERATOR], [], [GroupNameV2.BASIC_MODERATOR], False),
            (True, [GroupNameV2.BASIC_MODERATOR], [GroupNameV2.BASIC_MODERATOR], [], True),
            (True, [], [GroupNameV2.BASIC_MODERATOR], [], False),
            # FF OFF
            (False, [GroupName.BILLING_ADMIN], [GroupName.BILLING_ADMIN], [], True),
            (False, [], [GroupName.BILLING_ADMIN], [], False),
            (
                False,
                [GroupNameV2.BASIC_MODERATOR],
                [GroupName.BILLING_ADMIN],
                [GroupNameV2.BASIC_MODERATOR],
                False,
            ),
        ],
    )
    def test_has_change_permission(
        self,
        protected_view_factory,
        user_in_groups_factory,
        ff_on,
        user_groups,
        full_access_groups,
        read_only_groups,
        expected,
    ):
        with override_eppo_feature_flag({UseNewAdminGroupPermissions.flag_name: ff_on}):
            user = user_in_groups_factory(user_groups)
            protected_view = protected_view_factory(
                full_access_group_list=full_access_groups,
                read_only_group_list=read_only_groups,
            )
            request = RequestFactory().post('/fake-url')
            request.user = user

            assert protected_view.has_change_permission(request) is expected


@pytest.mark.django_db
class TestUSIsInGroupPermissionMixin:
    @pytest.mark.parametrize(
        'ff_on',
        [True, False],
    )
    def test_access_granted_for_non_us_country(
        self,
        settings,
        us_mixin_factory,
        user_not_in_any_group,
        ff_on,
    ):
        with override_eppo_feature_flag({UseNewAdminGroupPermissions.flag_name: ff_on}):
            settings.API_COUNTRY = Country.PL
            us_view = us_mixin_factory(
                full_access_group_list=[GroupNameV2.BASIC_MODERATOR], read_only_group_list=[]
            )

            assert us_view.is_user_in_full_access_groups(user_not_in_any_group) is True
            assert us_view.is_user_in_read_only_access_groups(user_not_in_any_group) is True

    @pytest.mark.parametrize(
        ('ff_on', 'full_access_expected', 'read_only_expected'),
        [
            (True, False, False),
            (False, True, False),
        ],
    )
    def test_access_for_us_country_without_group(
        self,
        settings,
        us_mixin_factory,
        user_not_in_any_group,
        ff_on,
        full_access_expected,
        read_only_expected,
    ):
        with override_eppo_feature_flag({UseNewAdminGroupPermissions.flag_name: ff_on}):
            settings.API_COUNTRY = Country.US
            us_view = us_mixin_factory(
                full_access_group_list=[GroupNameV2.BASIC_MODERATOR], read_only_group_list=[]
            )

            assert (
                us_view.is_user_in_full_access_groups(user_not_in_any_group) is full_access_expected
            )
            assert (
                us_view.is_user_in_read_only_access_groups(user_not_in_any_group)
                is read_only_expected
            )

    @pytest.mark.parametrize(
        ('ff_on', 'full_access_expected', 'read_only_access_expected'),
        [
            (True, False, True),
            (False, True, False),
        ],
    )
    def test_access_for_us_country_with_read_only_group(
        self,
        settings,
        us_mixin_factory,
        user_in_groups_factory,
        ff_on,
        full_access_expected,
        read_only_access_expected,
    ):
        with override_eppo_feature_flag({UseNewAdminGroupPermissions.flag_name: ff_on}):
            settings.API_COUNTRY = Country.US
            us_view = us_mixin_factory(
                full_access_group_list=[], read_only_group_list=[GroupNameV2.BASIC_MODERATOR]
            )
            user = user_in_groups_factory(group_list=[GroupNameV2.BASIC_MODERATOR])

            assert us_view.is_user_in_full_access_groups(user) is full_access_expected
            assert us_view.is_user_in_read_only_access_groups(user) is read_only_access_expected

    @pytest.mark.parametrize(
        ('ff_on', 'full_access_expected', 'read_only_access_expected'),
        [
            (True, True, False),
            (False, True, False),
        ],
    )
    def test_access_for_us_country_with_full_access_group(
        self,
        settings,
        us_mixin_factory,
        user_in_groups_factory,
        ff_on,
        full_access_expected,
        read_only_access_expected,
    ):
        with override_eppo_feature_flag({UseNewAdminGroupPermissions.flag_name: ff_on}):
            settings.API_COUNTRY = Country.US
            us_view = us_mixin_factory(
                full_access_group_list=[GroupNameV2.BASIC_MODERATOR], read_only_group_list=[]
            )
            user = user_in_groups_factory(group_list=[GroupNameV2.BASIC_MODERATOR])

            assert us_view.is_user_in_full_access_groups(user) is full_access_expected
            assert us_view.is_user_in_read_only_access_groups(user) is read_only_access_expected


@pytest.mark.django_db
class TestLegacyGroupPermissions:
    @override_eppo_feature_flag({UseNewAdminGroupPermissions.flag_name: True})
    def test_access_denied_if_ff_on_and_only_v1_groups_defined(
        self, protected_view_factory, user_in_groups_factory
    ):
        user = user_in_groups_factory([GroupName.BILLING_ADVANCED_USER])
        protected_view = protected_view_factory(
            full_access_group_list=[GroupName.BILLING_ADVANCED_USER],
            read_only_group_list=[],
        )
        request = RequestFactory().get('/fake-url')
        request.user = user

        assert protected_view.has_view_permission(request) is False
        assert protected_view.has_change_permission(request) is False

    @override_eppo_feature_flag({UseNewAdminGroupPermissions.flag_name: False})
    def test_access_granted_if_ff_off_and_only_v1_groups_defined(
        self, protected_view_factory, user_in_groups_factory
    ):
        user = user_in_groups_factory([GroupName.BILLING_ADVANCED_USER])
        protected_view = protected_view_factory(
            full_access_group_list=[GroupName.BILLING_ADVANCED_USER],
            read_only_group_list=[],
        )
        request = RequestFactory().get('/fake-url')
        request.user = user

        assert protected_view.has_view_permission(request) is True
        assert protected_view.has_change_permission(request) is True

    @override_eppo_feature_flag({UseNewAdminGroupPermissions.flag_name: False})
    def test_access_denied_if_ff_off_and_user_not_in_v1_group(
        self, protected_view_factory, user_in_groups_factory
    ):
        user = user_in_groups_factory([])
        protected_view = protected_view_factory(
            full_access_group_list=[GroupName.BILLING_ADVANCED_USER],
            read_only_group_list=[],
        )
        request = RequestFactory().get('/fake-url')
        request.user = user

        assert protected_view.has_view_permission(request) is False
        assert protected_view.has_change_permission(request) is False


class TestFeatureFlag:
    @override_eppo_feature_flag({UseNewAdminGroupPermissions.flag_name: False})
    def test_checks_only_for_v1_groups_if_ff_is_off(
        self, protected_view_factory, user_in_groups_factory
    ):
        user = user_in_groups_factory([GroupName.BILLING_ADVANCED_USER])
        protected_view = protected_view_factory(
            full_access_group_list=[GroupName.BILLING_ADVANCED_USER, GroupNameV2.BASIC_MODERATOR],
            read_only_group_list=[],
        )
        request = RequestFactory().get('/fake-url')
        request.user = user

        assert protected_view.has_view_permission(request) is True
        assert protected_view.has_change_permission(request) is True
        assert protected_view.has_add_permission(request) is True
        assert protected_view.has_delete_permission(request) is True

    @override_eppo_feature_flag(
        {
            UseNewAdminGroupPermissions.flag_name: True,
        }
    )
    def test_checks_only_for_v2_groups_if_ff_is_on(
        self, protected_view_factory, user_in_groups_factory
    ):
        user = user_in_groups_factory([GroupName.BILLING_ADVANCED_USER])
        protected_view = protected_view_factory(
            full_access_group_list=[GroupName.BILLING_ADVANCED_USER, GroupNameV2.BASIC_MODERATOR],
            read_only_group_list=[],
        )
        request = RequestFactory().get('/fake-url')
        request.user = user

        assert protected_view.has_view_permission(request) is False
        assert protected_view.has_change_permission(request) is False
        assert protected_view.has_add_permission(request) is False
        assert protected_view.has_delete_permission(request) is False

    @override_eppo_feature_flag(
        {
            UseNewAdminGroupPermissions.flag_name: False,
        }
    )
    def test_allows_access_if_ff_is_off_and_no_v1_groups(
        self, protected_view_factory, user_in_groups_factory
    ):
        user = user_in_groups_factory([])
        protected_view = protected_view_factory(
            full_access_group_list=[GroupNameV2.BASIC_MODERATOR],
            read_only_group_list=[],
        )
        request = RequestFactory().get('/fake-url')
        request.user = user

        assert protected_view.has_view_permission(request) is True
        assert protected_view.has_change_permission(request) is True
        assert protected_view.has_add_permission(request) is True
        assert protected_view.has_delete_permission(request) is True


@pytest.mark.django_db
class TestGetPermissionRequired:
    def test_get_permission_required_override_with_flag_on(self):
        with override_eppo_feature_flag({UseNewAdminGroupPermissions.flag_name: True}):
            request = RequestFactory().get('/fake-path')
            request.user = AnonymousUser()
            view = CustomerImportView()
            view.request = request
            assert view.get_permission_required() == ()

    def test_get_permission_required_override_with_flag_off(self):
        with override_eppo_feature_flag({UseNewAdminGroupPermissions.flag_name: False}):
            request = RequestFactory().get('/fake-path')
            request.user = AnonymousUser()
            view = CustomerImportView()
            view.request = request
            assert view.get_permission_required() == view.permission_required
