from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.utils.translation import gettext_lazy as _

from lib.enums import BaseEnum, PaymentMethodType, StrChoicesEnum, StrEnum
from lib.feature_flag.feature.billing import BillingSubscriptionOneDayPeriodFlag
from webapps.navision.models import TaxRate


class CreditCardType(StrChoicesEnum):
    AMERICAN_EXPRESS = 'A', 'American Express'
    VISA = 'V', 'Visa'
    MASTERCARD = 'M', 'MasterCard'
    MAESTRO = 'E', 'Maestro'
    CARTE_BLANCHE = 'C', 'Carte Blanche'
    CHINA_UNIONPAY = 'H', 'China UnionPay'
    DISCOVER = 'D', 'Discover'
    ELO = 'L', 'Elo'
    JCB = 'J', 'JCB'
    LASER = 'S', 'Laser'
    SOLO = 'O', 'Solo'
    SWITCH = 'W', 'Switch'
    CARTES_BANCAIRES = 'I', 'Cartes Bancaires'
    DINERS_CLUB = 'R', 'Diners Club'
    UNIONPAY = 'P', 'Unionpay'
    UNKNOWN = 'U', _('Unknown')


class CreditNetworkType(StrChoicesEnum):
    VISA = 'visa', 'Visa'
    MASTERCARD = 'mastercard', 'MasterCard'
    CARTES_BANCAIRES = 'cartes_bancaires', 'Cartes Bancaires'


CARD_TYPE_MAP = {
    'american express': CreditCardType.AMERICAN_EXPRESS,
    'maestro': CreditCardType.MAESTRO,
    'carte blanche': CreditCardType.CARTE_BLANCHE,
    'china unionpay': CreditCardType.CHINA_UNIONPAY,
    'elo': CreditCardType.ELO,
    'laser': CreditCardType.LASER,
    'solo': CreditCardType.SOLO,
    'switch': CreditCardType.SWITCH,
    'amex': CreditCardType.AMERICAN_EXPRESS,
    'cartes_bancaires': CreditCardType.CARTES_BANCAIRES,
    'cartes bancaires': CreditCardType.CARTES_BANCAIRES,
    'diners_club': CreditCardType.DINERS_CLUB,
    'diners club': CreditCardType.DINERS_CLUB,
    'diners': CreditCardType.DINERS_CLUB,
    'discover': CreditCardType.DISCOVER,
    'jcb': CreditCardType.JCB,
    'mastercard': CreditCardType.MASTERCARD,
    'visa': CreditCardType.VISA,
    'unionpay': CreditCardType.UNIONPAY,
}


def get_card_type(value: str) -> str:
    return CARD_TYPE_MAP.get(value.lower(), CreditCardType.UNKNOWN)


PAYMENT_TYPE_MAP = {
    'card': PaymentMethodType.CREDIT_CARD,
    'paypal': PaymentMethodType.PAYPAL,
    'venmo': PaymentMethodType.VENMO,
}


def get_payment_type(value: str) -> str:
    return PAYMENT_TYPE_MAP.get(value.lower())


class DiscountType(StrChoicesEnum):
    NO_DISCOUNT = 'X', 'No discount'
    PERCENTAGE = 'P', '%'
    FIXED = 'F', 'Fixed amount'

    @classmethod
    def discount_choices(cls):
        return [
            (cls.PERCENTAGE.value, cls.PERCENTAGE.label),
            (cls.FIXED.value, cls.FIXED.label),
        ]


class ExtendedDiscountType(StrChoicesEnum):
    """
    Allows to have both (% and fixed amount) discount types at the same time.
    """

    NO_DISCOUNT = 'X', 'No discount'
    PERCENTAGE = 'P', '%'
    FIXED = 'F', 'Fixed amount'
    BOTH = 'B', 'Both (% & fixed amount)'


class DiscountReason(StrChoicesEnum):
    OTHER = 'O', 'Other'
    DISCOUNT_CODE = 'C', 'Discount code'


class DiscountDuration(StrChoicesEnum):
    ONE = 1, '1 bc'
    TWO = 2, '2 bc'
    THREE = 3, '3 bc'
    FOUR = 4, '4 bc'
    FIVE = 5, '5 bc'
    SIX = 6, '6 bc'
    SEVEN = 7, '7 bc'
    EIGHT = 8, '8 bc'
    NINE = 9, '9 bc'
    TEN = 10, '10 bc'
    ELEVEN = 11, '11 bc'
    TWELVE = 12, '12 bc'
    SUB_DURATION = None, 'For duration of subscription'

    @classmethod
    def finite_choices(cls):
        return [
            (member.value, member.label)
            for member in list(cls.__members__.values())
            if isinstance(member.value, int)
        ]


class OneOffChargeType(StrChoicesEnum):
    # Types of one-off charges
    MIGRATION_FEE = 'MF', _('Migration Fee')


class ProductType(StrChoicesEnum):
    # Every subscription has to contain at least SAAS product.
    SAAS = 'SA', _('SaaS')
    STAFFER_SAAS = 'SS', _('Staffer SaaS')
    BUSY = 'BU', _('Busy')
    STAFFER_BUSY = 'SB', _('Staffer Busy')
    POSTPAID_SMS = 'PS', _('Postpaid SMS')
    PREPAID_SMS = 'RS', _('Prepaid SMS')
    # TODO: Boost team
    BOOST = 'BO', _('Boost')

    @classmethod
    def charged_per_staffer(cls):
        return {
            cls.STAFFER_SAAS,
            cls.STAFFER_BUSY,
        }

    @classmethod
    def standalone(cls):
        """Types of products that merchant can buy by himself."""
        return {
            cls.SAAS,
            cls.BUSY,
            cls.BOOST,
        }

    @classmethod
    def based_on_usage(cls):
        return {
            cls.POSTPAID_SMS,
        }

    @classmethod
    def postpaid(cls):
        return {
            cls.POSTPAID_SMS,
        }

    @classmethod
    def prepaid(cls):
        return {
            cls.SAAS,
            cls.STAFFER_SAAS,
            cls.BUSY,
            cls.STAFFER_BUSY,
            cls.PREPAID_SMS,
        }

    @staticmethod
    def as_tax_rate_service(product_type):
        return {
            ProductType.SAAS: TaxRate.Service.SAAS,
            ProductType.STAFFER_SAAS: TaxRate.Service.SAAS,
            ProductType.STAFFER_BUSY: TaxRate.Service.SAAS,
            ProductType.POSTPAID_SMS: TaxRate.Service.SAAS,
            ProductType.PREPAID_SMS: TaxRate.Service.SAAS,
            ProductType.BOOST: TaxRate.Service.BOOST,
            OneOffChargeType.MIGRATION_FEE: TaxRate.Service.SAAS,
        }.get(product_type)


class TransactionSource(StrChoicesEnum):
    BILLING_SUBSCRIPTION = 'billing_subscription', _('Billing subscription')
    ONE_OFF_CHARGE = 'one_off_charge', _('One-Off Charge')
    BOOST_OVERDUE = 'boost_overdue', _('Boost overdue')
    VISIBILITY_PROMOTION = 'visibility_promotion', _('Visibility Promotion')


class TransactionDescription(StrEnum):
    SUBSCRIPTION = 'Billing subscription'
    ONE_OFF_CHARGE = 'One-Off Charge'


class DiscountProductType(StrChoicesEnum):
    SAAS = ProductType.SAAS.value, _('SaaS')
    STAFFER_SAAS = ProductType.STAFFER_SAAS.value, _('Staffers')
    STAFFER_AND_SAAS = 'SX', _('SaaS & Staffers')

    @staticmethod
    def map_to_real_product_types(discount_product_type: str) -> list[ProductType]:
        if discount_product_type == DiscountProductType.STAFFER_AND_SAAS:
            return [ProductType.SAAS, ProductType.STAFFER_SAAS]
        return [ProductType.get_member(discount_product_type)]


class SubscriptionDuration(StrChoicesEnum):
    ONE_MONTH = 'M', _('1 month')
    INDEFINITE = 'I', _('Indefinite')


class SubscriptionStatus(StrChoicesEnum):
    # Those are statuses required by business spec
    PENDING = 'P', _('Pending')
    ACTIVE = 'A', _('Active')
    CLOSED = 'C', _('Closed')
    BLOCKED = 'B', _('Blocked')
    SUSPENDED = 'S', _('Suspended')
    SUSPENSION_PENDING = 'U', _('Suspension pending')


class TransactionStatus(StrChoicesEnum):
    INITIAL = 'I', 'Initial'
    PENDING = 'P', 'Pending'
    CHARGED = 'C', 'Charged'
    FAILED = 'F', 'Failed'
    CANCELLED = 'X', 'Canceled'
    REFUNDED = 'R', 'Refunded'
    SKIPPED = 'S', 'Skipped'


class BillingRefundStatus(StrChoicesEnum):
    INITIAL = 'initial', 'Initial (sent to PaymentProcessor)'
    PENDING = 'pending', 'Pending'
    SUCCEEDED = 'succeeded', 'Succeeded'
    FAILED = 'failed', 'Failed'
    CANCELED = 'canceled', 'Canceled'
    REQUIRES_ACTION = 'requires_action', 'Requires action'


class BillingOneOffStatus(StrChoicesEnum):
    INITIAL = 'initial', 'Initial (sent to PaymentProcessor)'
    SUCCEEDED = 'succeeded', 'Succeeded'
    FAILED = 'failed', 'Failed'


class TransactionResponseType(StrChoicesEnum):
    APPROVED = 'A', 'Approved'
    # Only soft declines can be retried
    SOFT_DECLINE = 'S', 'Soft declined'
    HARD_DECLINE = 'H', 'Hard declined'


class PaymentPeriodEnum(BaseEnum):
    def __init__(self, real_value, label):
        self.real_value = real_value
        self.label = label

    @property
    def choice(self):
        return (self.name, self.label)

    @classmethod
    def choices(cls):
        return [member.choice for member in list(cls.__members__.values())]

    @classmethod
    def get_real_value(cls, name):
        member = cls.__members__.get(name)
        if member:
            return member.real_value

    @classmethod
    def get_choice(cls, real_value):
        candidates = [
            member for member in cls.__members__.values() if member.real_value == real_value
        ]
        if candidates:
            return candidates[0].choice


class NotificationType(StrChoicesEnum):
    """Internal billing notification types sent to merchants.
    Each choice should map to one notifiaction class from
    webapps/billing/notifications.py .
    """

    BILLING = ('BILLING', 'Default billing')
    ONLINE_SUBSCRIPTION_CONFIRM = (
        'SUB_CONF',
        'Online Subscription Confirmation',
    )
    SMS_COST_ALERT = ('SMS_ALERT', 'SMS cost alert')


class PaymentProcessorType(StrChoicesEnum):
    BRAINTREE = 'B', 'Braintree'
    STRIPE = 'S', 'Stripe'


class PaymentActionType(StrEnum):
    INITIAL_PAYMENT_WITH_TRANSACTION_AUTH = (
        'initial_payment_with_transaction_auth'  # purchase with payment intent
    )
    INITIAL_PAYMENT_WITH_CARD_AUTH = 'initial_payment_with_card_auth'  # purchase with setup intent
    CYCLE_SWITCH = 'billing_cycle_switch'
    RETRY_CHARGE_WITH_CARD_AUTH = 'retry_charge_with_card_auth'
    RETRY_CHARGE_WITH_TRANSACTION_AUTH = (
        'retry_charge_with_transaction_auth'  # retry with payment intent
    )
    BATCH_BOOST_OVERDUE_CHARGE = 'batch_boost_overdue_charge'
    ONE_OFF_CHARGE = 'one_off_charge'

    @classmethod
    def retry_charge_values(cls):
        return [
            cls.RETRY_CHARGE_WITH_TRANSACTION_AUTH.value,
            cls.RETRY_CHARGE_WITH_CARD_AUTH.value,
        ]

    @classmethod
    def initial_payment_values(cls):
        return [
            cls.INITIAL_PAYMENT_WITH_TRANSACTION_AUTH.value,
            cls.INITIAL_PAYMENT_WITH_CARD_AUTH.value,
        ]

    @classmethod
    def boost_payment_values(cls):
        return [
            cls.BATCH_BOOST_OVERDUE_CHARGE.value,
        ]


class BillingErrorEventType(StrChoicesEnum):
    SUBSCRIPTION_CREATION = 'S', 'Subscription creation'
    CYCLE_SWITCH = 'C', 'Cycle switch'
    RETRY_PAYMENT = 'R', 'Retry payment'
    PAYMENT_METHOD = 'M', 'Payment method'
    MISSING_BILLING_TRANSACTION = 'T', 'Missing Billing Transaction'
    ONE_OFF_PAYMENT = 'F', 'One-off payment'
    BOOST_OVERDUE = 'O', 'Boost overdue'


class RefundReason(StrChoicesEnum):
    UNFIT_TO_CUSTOMER_NEEDS = 'unfit_to_customer_needs', 'Unfit customer needs'
    DUPLICATE = 'duplicate', 'Duplicate'
    BOOST_CLAIM_EXISTING_CUSTOMER = (
        'boost_claim_existing_customer',
        'Boost claim - existing customer',
    )
    INCORRECT_OFFER = 'incorrect_offer', 'Incorrect offer'
    TECHNICAL_ISSUES = 'technical_issues', 'Technical issues/bug'
    TEST_TRANSACTION = (
        'test_transaction',
        'Test Transaction',
    )
    OTHER = 'other', 'Other'


class MerchantMigrationSource(StrChoicesEnum):
    VERSUM = 'versum', 'Versum'


class MigratedSubscriptionInitialTaskType(StrChoicesEnum):
    PREPARE_REGULAR_SUBSCRIPTION = 'prepare_regular_subscription', 'Prepare regular subscription'


PeriodsDict = dict(
    ONE_MONTH=(relativedelta(months=1), '1 month'),
)

if settings.PYTEST or settings.BILLING_IN_SANDBOX_MODE:
    options = {
        'days': [1, 2, 3, 5, 7, 14, 21],
        'hours': [1, 2, 3] + list(range(5, 21, 5)),
        'minutes': range(5, 56, 5),
    }
    for granularity, values in options.items():
        PeriodsDict.update(
            dict(
                (
                    f"{granularity}_{value}",
                    (relativedelta(**{granularity: value}), f'{value} {granularity}'),
                )
                for value in values
            )
        )

PaymentPeriod = PaymentPeriodEnum(  # pylint: disable=unexpected-keyword-arg, no-value-for-parameter
    value='PaymentPeriod',
    names=PeriodsDict,
)


def get_default_payment_period():
    # for debug/tests purposes period equal to 1 day
    if not settings.LIVE_DEPLOYMENT and BillingSubscriptionOneDayPeriodFlag():
        return relativedelta(days=1)

    return PeriodsDict['ONE_MONTH'][0]


class BillingLongSubscriptionDuration(StrChoicesEnum):
    THREE_MONTHS = 3, '3 months'
    SIX_MONTHS = 6, '6 months'
    TWELVE_MONTHS = 12, '12 months'


class PurchaseFlowAuthorization(StrChoicesEnum):
    AUTHORIZE_WITH_CARD = 'C', 'Authorize with Card'
    AUTHORIZE_WITH_TRANSACTION = 'T', 'Authorize with Transaction'


LongSubStaffQtyDict = dict(
    (f"STAFF_QTY_{val}", (val, str(val)))
    for val, _ in enumerate(range(settings.BILLING_LONG_SUBSCRIPTION_STAFFER_COUNT_LIMIT), start=1)
)
LongSubStaffQtyDict.update(
    dict(STAFF_QTY_UNLIMITED=(settings.BILLING_STAFF_UNLIMITED_QTY, 'unlimited'))
)
BillingLongSubStaffQuantity = (
    StrChoicesEnum(  # pylint: disable=unexpected-keyword-arg, no-value-for-parameter
        "BillingLongSubStaffQuantity", names=LongSubStaffQtyDict
    )
)
