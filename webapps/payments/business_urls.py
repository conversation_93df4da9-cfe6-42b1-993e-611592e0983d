from django.urls import path

from webapps.payments.views.account_holder import (
    AccountHolderInfo,
    StripeAccountHolderSettings,
)
from webapps.payments.views.account_management import (
    ProviderAccountDetailsAPIView,
    CreatePayoutMethodAPIView,
    DeletePayoutMethodAPIView,
    SetPayoutMethodAsDefaultAPIView,
    CreateProviderAccountAPIView,
)
from webapps.payments.views.banners import BannersView
from webapps.payments.views.financial_center.financial_center_overview import (
    FinancialCenterOverviewView,
)
from webapps.payments.views.financial_center.payouts import BooksyWalletPayoutsViewSet
from webapps.payments.views.payment import BusinessRefundBasketPaymentView
from webapps.payments.views.payment.business_basket_refund_splits import (
    BusinessBasketPaymentRefundSplitsView,
)
from webapps.payments.views.payment.business_basket_details import BusinessBasketDetailsView
from webapps.payments.views.tap_to_pay import TapToPayCelebrationScreenView


urlpatterns = [
    path(
        'account/<str:provider_code>/',
        CreateProviderAccountAPIView.as_view(),
        name='payments__create_provider_account',
    ),
    path(
        'account_details/<str:provider_code>/',
        ProviderAccountDetailsAPIView.as_view(),
        name='payments__provider_account_details',
    ),
    path(
        'account_details/<str:provider_code>/payout_methods/',
        CreatePayoutMethodAPIView.as_view(),
        name='payments__payout_methods',
    ),
    path(
        'account_details/<str:provider_code>/payout_method/<str:payout_method_token>/',
        DeletePayoutMethodAPIView.as_view(),
        name='payments__payout_method',
    ),
    path(
        'account_details/<str:provider_code>/payout_method/<str:payout_method_token>/'
        'set_as_default/<str:payout_type>/',
        SetPayoutMethodAsDefaultAPIView.as_view(),
        name='payments__payout_method_set_as_default',
    ),
    path(
        'account_holder_info/',
        AccountHolderInfo.as_view(),
        name='payments__account_holder_info',
    ),
    path(
        'account_holder_settings/provider/stripe/',
        StripeAccountHolderSettings.as_view(),
        name='payments__stripe_account_holder_settings',
    ),
    path(
        'baskets/<str:basket_id>/',
        BusinessBasketDetailsView.as_view(),
        name='payments__basket_business_details',
    ),
    path(
        'ttp_celebration/',
        TapToPayCelebrationScreenView.as_view(),
        name='ttp_celebration_screen',
    ),
    path('banners/', BannersView.as_view(), name='payments__banners'),
    path(
        'financial_center/overview/',
        FinancialCenterOverviewView.as_view(),
        name='payments__financial_center_overview',
    ),
    path(
        'booksy_wallet/payouts/',
        BooksyWalletPayoutsViewSet.as_view({'get': 'list'}),
        name='payments__booksy_wallet_payouts',
    ),
    path(
        'basket_payment/<str:basket_payment_id>/refund/',
        BusinessRefundBasketPaymentView.as_view(),
        name='payments__refund_basket_payment',
    ),
    path(
        'basket_payment/<str:basket_payment_id>/refund_fees/',
        BusinessBasketPaymentRefundSplitsView.as_view(),
        name='payments__refund_fees_basket_payment',
    ),
]
