from datetime import datetime
from decimal import Decimal
from mock import patch

from django.urls import reverse
from django.test import override_settings

from freezegun import freeze_time
from model_bakery import baker

from drf_api.lib.base_drf_test_case import CustomerAPITestCase
from lib.baker_utils import get_or_create_booking_source
from lib.feature_flag.feature.payments_service import PaymentsServiceTriggerPaymentFlag
from lib.payment_gateway.enums import PaymentMethodType
from lib.payments.enums import PaymentProviderCode
from lib.point_of_sale.enums import BasketPaymentStatus
from lib.tests.utils import (
    create_or_set_booksy_gift_cards_settings_if_necessery,
    override_eppo_feature_flag,
)
from webapps.business.baker_recipes import (
    bci_recipe,
    service_variant_recipe,
    service_variant_payment_recipe,
)
from webapps.business.models import ServiceVariantPayment
from webapps.business.enums import PriceType
from webapps.booking.models import Appointment, BookingSources
from webapps.booking.tests.utils import create_appointment
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.user.models import User
from webapps.pos.booksy_gift_cards.responses import ValidateBooksyGiftCardCodeResponse
from webapps.pos.enums import PaymentTypeEnum, POSPlanPaymentTypeEnum, receipt_status
from webapps.pos.models import Transaction, POS
from webapps.point_of_sale.models import Basket, CancellationFeeAuth


@override_eppo_feature_flag({PaymentsServiceTriggerPaymentFlag.flag_name: True})
@patch(
    'webapps.payments.views.payment.make_payment.payments_service_trigger_payment',
    return_value=True,
)
class MakePaymentViewTestCase(CustomerAPITestCase):
    @classmethod
    def setUpTestData(cls):
        cls.business = baker.make('business.Business')
        cls.pos = baker.make(
            'pos.POS',
            business=cls.business,
            pay_by_app_status=POS.PAY_BY_APP_ENABLED,
            _force_stripe_pba=True,
            tips_enabled=True,
        )
        cls.user = baker.make(
            User,
            email='<EMAIL>',
            cell_phone='+**************',
        )
        cls.customer_booking_src = get_or_create_booking_source(
            app_type=BookingSources.CUSTOMER_APP,
            api_key='customer_key',
        )

    @patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    def setUp(self, _):
        super().setUp()
        self.wallet, _ = PaymentGatewayPort.get_or_create_customer_wallet(
            customer_user_id=self.user.id,
            email=self.user.email,
            phone=self.user.cell_phone,
            statement_name='',
        )
        self.customer = baker.make(
            'payment_providers.Customer',
        )
        self.tpm = baker.make(
            'payment_providers.TokenizedPaymentMethod',
            customer=self.customer,
            provider_code=PaymentProviderCode.STRIPE,
            method_type=PaymentMethodType.CARD,
            default=True,
            details={
                'brand': 'visa',
                'last_digits': '4321',
                'expiry_month': 9,
                'expiry_year': 2024,
                'cardholder_name': 'cardholder name',
            },
        )
        baker.make(
            'payment_providers.StripeTokenizedPaymentMethod',
            external_id='1233334',
            tokenized_payment_method=self.tpm,
        )
        self.card = baker.make(
            'pos.PaymentMethod',
            provider='stripe',
            card_last_digits='4321',
            user=self.user,
            default=True,
            tokenized_pm_id=self.tpm.id,
        )
        self.bci = bci_recipe.make(user=self.user, business=self.business)
        baker.make('pos.PaymentType', pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        baker.make('pos.PaymentType', pos=self.pos, code=PaymentTypeEnum.PREPAYMENT)

        self.price = Decimal('100.00')
        service_pp = baker.make('business.Service', business=self.business)
        self.service_variant_pp = service_variant_recipe.make(
            service=service_pp,
            price=self.price,
            type=PriceType.FIXED,
        )
        service_variant_payment_recipe.make(
            service_variant=self.service_variant_pp,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('40'),  # 40%
            saving_type=ServiceVariantPayment.PERCENTAGE,
        )

        service_cf = baker.make('business.Service', business=self.business)
        self.service_variant_cf = service_variant_recipe.make(
            service=service_cf,
            price=self.price,
            type=PriceType.FIXED,
        )
        service_variant_payment_recipe.make(
            service_variant=self.service_variant_cf,
            payment_type=ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            payment_amount=Decimal('20'),  # 20%
            saving_type=ServiceVariantPayment.PERCENTAGE,
        )
        baker.make(
            'stripe_integration.StripeAccount', pos=self.pos, kyc_verified_at_least_once=True
        )

    def _check_result_prepayment(self, payment_row_status, with_tip=True):
        self.assertEqual(Transaction.objects.count(), 1)
        trx = Transaction.objects.first()
        self.assertEqual(trx.latest_receipt.payment_rows.count(), 1)
        pr = trx.latest_receipt.payment_rows.first()
        self.assertEqual(pr.status, payment_row_status)
        basket_id = trx.basket_id
        self.assertIsNotNone(basket_id)

        self.assertEqual(Basket.objects.count(), 1)
        basket = Basket.objects.filter(id=basket_id).first()
        self.assertIsNotNone(basket)
        self.assertEqual(basket.payments.count(), 1)
        bp = basket.payments.first()
        self.assertEqual(bp.status, BasketPaymentStatus.PENDING)
        if with_tip:
            self.assertEqual(basket.tips.count(), 1)
            tip = basket.tips.first()
            self.assertEqual(tip.amount, 2500)

    def _check_result_cf(self, payment_row_status):
        self.assertEqual(Transaction.objects.count(), 1)
        trx = Transaction.objects.first()
        self.assertEqual(trx.latest_receipt.payment_rows.count(), 1)
        pr = trx.latest_receipt.payment_rows.first()
        self.assertEqual(pr.status, payment_row_status)
        self.assertIsNone(trx.basket_id)

        self.assertEqual(CancellationFeeAuth.objects.count(), 1)
        cf = CancellationFeeAuth.objects.first()
        self.assertEqual(cf.amount, 2000)

    @override_settings(POS__BLIK=True)
    def test_make_prepayment_with_blik_token(self, mock_trigger_payment):
        appt = create_appointment(
            [
                {'service_variant': self.service_variant_pp},
            ],
            total_type=PriceType.FIXED,
            total_value=self.price,
            business=self.business,
            updated_by=self.user,
            type=Appointment.TYPE.CUSTOMER,
            booked_for=self.bci,
            status=Appointment.STATUS.PENDING_PAYMENT,
        )

        response = self.client.post(
            reverse(
                'payments__make_payment',
            ),
            data={
                'appointment_id': appt.id,
                'payment_method': {
                    'type': 'blik',
                    'token': '123456',
                },
                'tip': {'type': 'percent', 'value': 2500},
            },
            content_type='application/json',
        )

        self.assertEqual(response.status_code, 200)
        self._check_result_prepayment(payment_row_status=receipt_status.CALL_FOR_PREPAYMENT)

    @patch(
        'webapps.pos.booksy_gift_cards.grpc_client.BooksyGiftCardsClient.validate_booksy_gift_card',
        return_value=ValidateBooksyGiftCardCodeResponse(
            is_valid=True,
            balance=10000,
            error_message='',
            error_code='',
            gift_cards_ids=['external_id'],
        ),
    )
    def test_make_prepayment_with_bgc_token(self, mock_bgc, mock_trigger_payment):
        create_or_set_booksy_gift_cards_settings_if_necessery(self.business, True)

        appt = create_appointment(
            [
                {'service_variant': self.service_variant_pp},
            ],
            total_type=PriceType.FIXED,
            total_value=self.price,
            business=self.business,
            updated_by=self.user,
            type=Appointment.TYPE.CUSTOMER,
            booked_for=self.bci,
            status=Appointment.STATUS.PENDING_PAYMENT,
        )

        response = self.client.post(
            reverse(
                'payments__make_payment',
            ),
            data={
                'appointment_id': appt.id,
                'payment_method': {
                    'type': 'booksy_gift_card',
                    'token': 'b8239023-aff4-47da-af68-32e4bd67e79a',
                },
            },
            content_type='application/json',
        )

        self.assertEqual(response.status_code, 200)
        # there are no tips in BGC
        self._check_result_prepayment(
            payment_row_status=receipt_status.GIFT_CARD_DEPOSIT, with_tip=False
        )

    @freeze_time(datetime(2024, 2, 1))
    def test_make_prepayment_with_card(self, mock_trigger_payment):
        appt = create_appointment(
            [
                {'service_variant': self.service_variant_pp},
            ],
            total_type=PriceType.FIXED,
            total_value=self.price,
            business=self.business,
            updated_by=self.user,
            type=Appointment.TYPE.CUSTOMER,
            booked_for=self.bci,
            status=Appointment.STATUS.PENDING_PAYMENT,
        )

        self.assertEqual(Transaction.objects.count(), 0)

        response = self.client.post(
            reverse(
                'payments__make_payment',
            ),
            data={
                'appointment_id': appt.id,
                'payment_method': {
                    'type': 'card',
                    'tokenized_pm_id': str(self.tpm.id),
                },
            },
            content_type='application/json',
        )

        self.assertEqual(response.status_code, 200)
        self._check_result_prepayment(
            payment_row_status=receipt_status.CALL_FOR_PREPAYMENT, with_tip=False
        )

    def test_make_prepayment_wrong_input(self, mock_trigger_payment):
        response = self.client.post(
            reverse(
                'payments__make_payment',
            ),
            data={
                'appointment_id': 123,
                'payment_method': {
                    'type': 'card',
                },
            },
            content_type='application/json',
        )

        self.assertEqual(response.status_code, 400)

    @freeze_time(datetime(2024, 2, 1))
    def test_make_cancellation_fee_with_card(self, mock_trigger_payment):
        posplan = baker.make('pos.POSPlan', plan_type=POSPlanPaymentTypeEnum.STRIPE_MOBILE_PAYMENT)
        self.pos.pos_plans.add(posplan)

        appt = create_appointment(
            [
                {'service_variant': self.service_variant_cf},
            ],
            total_type=PriceType.FIXED,
            total_value=self.price,
            business=self.business,
            updated_by=self.user,
            type=Appointment.TYPE.CUSTOMER,
            booked_for=self.bci,
            status=Appointment.STATUS.PENDING_PAYMENT,
        )

        self.assertEqual(Transaction.objects.count(), 0)

        response = self.client.post(
            reverse(
                'payments__make_payment',
            ),
            data={
                'appointment_id': appt.id,
                'payment_method': {
                    'type': 'card',
                    'tokenized_pm_id': str(self.tpm.id),
                },
            },
            content_type='application/json',
        )

        self.assertEqual(response.status_code, 200)
        self._check_result_cf(payment_row_status=receipt_status.CALL_FOR_DEPOSIT)
