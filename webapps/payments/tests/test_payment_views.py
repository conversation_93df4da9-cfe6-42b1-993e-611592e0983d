import uuid
from datetime import datetime, UTC
from decimal import Decimal
from unittest.mock import MagicMock, patch

import mock
from django.urls import reverse
from mock import mock
from model_bakery import baker
from parameterized import parameterized
from rest_framework.status import HTTP_404_NOT_FOUND, HTTP_200_OK, HTTP_400_BAD_REQUEST
from dateutil import tz

from drf_api.lib.base_drf_test_case import CustomerAPITestCase, BaseBusinessApiTestCase
from lib.baker_utils import get_or_create_booking_source
from lib.feature_flag.feature.payment import PartialRefundsFlag
from lib.payment_providers.entities import AuthorizePaymentMethodDataEntity, DeviceDataEntity
from lib.payments.enums import PaymentProviderCode
from lib.payments.enums import RefundError
from lib.smartlock import LockFailed
from lib.test_utils import user_recipe
from lib.point_of_sale.entities import (
    BasketPaymentEntity,
    ExtendedBasketEntity,
    ExtendedBasketItemEntity,
    BasketTipEntity,
)
from lib.point_of_sale.enums import (
    BasketPaymentAnalyticsTrigger,
    BasketPaymentSource,
    BasketPaymentStatus,
    BasketPaymentType,
    BasketStatus,
    BasketType,
    PaymentMethodType,
    BasketItemTaxType,
)
from lib.payment_providers.enums import ExternalPaymentMethodType
from lib.pos.entities import ReceiptDetailsEntity
from lib.tests.utils import override_eppo_feature_flag
from service.tests import dict_assert
from webapps.booking.models import BookingSources
from webapps.business.baker_recipes import business_recipe, staffer_recipe
from webapps.business.models import Resource
from webapps.business.ports.business_details import BusinessDetailsEntity
from webapps.payment_gateway.models import BalanceTransaction, Payment
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_providers.models import (
    TokenizedPaymentMethod,
)
from webapps.point_of_sale.models import BasketPayment
from webapps.pos.models import POS
from webapps.user.models import User


def get_basket_payment_entity(
    user_id: int,
    amount: int = 50,
    basket_payment_status: BasketPaymentStatus = BasketPaymentStatus.SUCCESS,
) -> BasketPaymentEntity:
    return BasketPaymentEntity(
        id=uuid.uuid4(),
        basket_id=uuid.uuid4(),
        amount=amount,
        payment_method=PaymentMethodType.CARD,
        payment_provider_code=None,
        balance_transaction_id=None,
        status=basket_payment_status,
        user_id=user_id,
        type=BasketPaymentType.PAYMENT,
        parent_basket_payment_id=None,
        error_code=None,
        action_required_details=None,
        metadata=None,
        auto_capture=False,
        source=BasketPaymentSource.PAYMENT,
        created=datetime.now(),
        operator_id=13,
        label='label',
    )


class MakePaymentViewTestCase(CustomerAPITestCase):
    @classmethod
    def setUpTestData(cls):
        cls.user = baker.make(
            User,
            email='<EMAIL>',
            cell_phone='+48 697 850 000',
        )
        cls.customer_booking_src = get_or_create_booking_source(
            app_type=BookingSources.CUSTOMER_APP,
            api_key='customer_key',
        )

    def setUp(self):
        super().setUp()
        self.wallet, _ = PaymentGatewayPort.get_or_create_customer_wallet(
            customer_user_id=self.user.id,
            email=self.user.email,
            phone=self.user.cell_phone,
            statement_name='',
        )

    @patch('webapps.point_of_sale.ports.BasketPaymentPort.get_basket_payment_entity')
    def test_404(self, get_basket_payment_entity_mock):
        get_basket_payment_entity_mock.return_value = None
        response = self.client.post(
            reverse(
                'payments__make_basket_payment',
                kwargs={'basket_payment_id': uuid.uuid4()},
            ),
            content_type='application/json',
        )
        self.assertEqual(response.status_code, HTTP_404_NOT_FOUND)

    @patch('webapps.point_of_sale.ports.BasketPaymentPort.get_basket_payment_entity')
    def test_no_payment_method(self, get_basket_payment_entity_mock):
        basket_payment_entity = get_basket_payment_entity(
            user_id=self.user.id,
        )
        get_basket_payment_entity_mock.return_value = basket_payment_entity

        response = self.client.post(
            reverse(
                'payments__make_basket_payment',
                kwargs={'basket_payment_id': basket_payment_entity.id},
            ),
            content_type='application/json',
        )

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.json()['errors'][0]['code'], 'missing_method')

    @patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    @patch('webapps.booking.ports.BookingPort.check_tokenized_payments_for_payment_creation')
    def test_action_non_allowed(self, check_tokenized_payments_for_payment_creation_mock, _):
        check_tokenized_payments_for_payment_creation_mock.return_value = {
            PaymentMethodType.APPLE_PAY: True,
            PaymentMethodType.GOOGLE_PAY: True,
        }
        self.basket_payment = baker.make(
            BasketPayment,
            user_id=self.user.id,
            status=BasketPaymentStatus.FAILED,
            amount=50,
        )
        self.tokenized_payment_method = baker.make(
            TokenizedPaymentMethod,
            customer_id=self.wallet.customer_id,
        )

        response = self.client.post(
            reverse(
                'payments__make_basket_payment',
                kwargs={'basket_payment_id': self.basket_payment.id},
            ),
            data={
                'external_payment_method': {
                    'partner': ExternalPaymentMethodType.APPLE_PAY,
                    'token': 'qwe',
                }
            },
            content_type='application/json',
        )
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.json()['errors'][0]['code'], 'invalid')
        self.assertIn('not allowed', response.json()['errors'][0]['description'])

    @mock.patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    @mock.patch('webapps.booking.ports.BookingPort.check_tokenized_payments_for_payment_creation')
    def test_minimal_amount(self, check_tokenized_payments_for_payment_creation_mock, _):
        check_tokenized_payments_for_payment_creation_mock.return_value = {
            PaymentMethodType.APPLE_PAY: True,
            PaymentMethodType.GOOGLE_PAY: True,
        }
        self.basket_payment = baker.make(
            BasketPayment,
            user_id=self.user.id,
            status=BasketPaymentStatus.PENDING,
            amount=49,
        )
        self.tokenized_payment_method = baker.make(
            TokenizedPaymentMethod,
            customer_id=self.wallet.customer_id,
        )

        response = self.client.post(
            reverse(
                'payments__make_basket_payment',
                kwargs={'basket_payment_id': self.basket_payment.id},
            ),
            data={
                'external_payment_method': {'partner': PaymentMethodType.APPLE_PAY, 'token': 'qwe'}
            },
            content_type='application/json',
        )
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.json()['errors'][0]['code'], 'pay_by_app_minimal_amount')

    @mock.patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    def test_external_payment_method_validation(self, _):
        self.basket_payment = baker.make(
            BasketPayment,
            user_id=self.user.id,
            status=BasketPaymentStatus.PENDING,
            amount=50,
        )

        self.tokenized_payment_method = baker.make(
            TokenizedPaymentMethod,
            customer_id=self.wallet.customer_id,
        )

        response = self.client.post(
            reverse(
                'payments__make_basket_payment',
                kwargs={'basket_payment_id': self.basket_payment.id},
            ),
            data={
                'external_payment_method': {
                    'partner': 'wrong data',
                    'token': 'qwe',
                }
            },
            content_type='application/json',
        )
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.json()['errors'][0]['code'], 'invalid_choice')

    @patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    @patch('webapps.booking.ports.BookingPort.check_tokenized_payments_for_payment_creation')
    def test_payment_not_possible(self, check_tokenized_payments_for_payment_creation_mock, _):
        check_tokenized_payments_for_payment_creation_mock.return_value = {
            PaymentMethodType.APPLE_PAY: False,
            PaymentMethodType.GOOGLE_PAY: False,
        }
        self.basket_payment = baker.make(
            BasketPayment,
            user_id=self.user.id,
            status=BasketPaymentStatus.FAILED,
            amount=50,
        )
        self.tokenized_payment_method = baker.make(
            TokenizedPaymentMethod,
            customer_id=self.wallet.customer_id,
        )

        response = self.client.post(
            reverse(
                'payments__make_basket_payment',
                kwargs={'basket_payment_id': self.basket_payment.id},
            ),
            data={
                'external_payment_method': {
                    'partner': ExternalPaymentMethodType.APPLE_PAY,
                    'token': 'qwe',
                }
            },
            content_type='application/json',
        )
        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.json()['errors'][0]['code'], 'invalid')

    @mock.patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    @mock.patch('webapps.point_of_sale.ports.BasketPaymentPort.make_basket_payment')
    def test_card_payment(self, make_basket_payment_mock, _):
        self.basket_payment = baker.make(
            BasketPayment,
            user_id=self.user.id,
            status=BasketPaymentStatus.PENDING,
            amount=50,
        )

        self.tokenized_payment_method = baker.make(
            TokenizedPaymentMethod,
            customer_id=self.wallet.customer_id,
        )
        response = self.client.post(
            reverse(
                'payments__make_basket_payment',
                kwargs={
                    'basket_payment_id': str(
                        self.basket_payment.id,
                    )
                },
            ),
            data={
                'tokenized_payment_method_id': str(
                    self.tokenized_payment_method.id,
                ),
            },
            content_type='application/json',
        )

        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            response.json()['basket_payment']['id'],
            str(self.basket_payment.id),
        )
        make_basket_payment_mock.assert_called_once_with(
            basket_payment_id=str(self.basket_payment.id),
            user_id=self.user.id,
            payment_method_data=AuthorizePaymentMethodDataEntity(
                tokenized_pm_id=str(self.tokenized_payment_method.id),
                payment_token=None,
            ),
            device_data=DeviceDataEntity(
                device_fingerprint=None,
                phone_number=self.user.cell_phone,
                user_agent=None,
                ip='127.0.0.1',
            ),
            trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__MOBILE_PAYMENT_CONFIRM,
        )

    @mock.patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    @mock.patch('webapps.booking.ports.BookingPort.check_tokenized_payments_for_payment_creation')
    @mock.patch('webapps.point_of_sale.ports.BasketPaymentPort.make_basket_payment')
    def test_apple_pay(
        self,
        make_basket_payment_mock,
        check_tokenized_payments_for_payment_creation_mock,
        _,
    ):
        check_tokenized_payments_for_payment_creation_mock.return_value = {
            PaymentMethodType.APPLE_PAY: True,
            PaymentMethodType.GOOGLE_PAY: True,
        }
        self.basket_payment = baker.make(
            BasketPayment,
            user_id=self.user.id,
            status=BasketPaymentStatus.PENDING,
            amount=50,
        )

        self.tokenized_payment_method = baker.make(
            TokenizedPaymentMethod,
            customer_id=self.wallet.customer_id,
        )
        response = self.client.post(
            reverse(
                'payments__make_basket_payment',
                kwargs={
                    'basket_payment_id': str(
                        self.basket_payment.id,
                    )
                },
            ),
            data={
                'external_payment_method': {'partner': PaymentMethodType.APPLE_PAY, 'token': 'qwe'},
            },
            content_type='application/json',
        )
        self.assertEqual(response.status_code, 200)
        self.assertEqual(
            response.json()['basket_payment']['id'],
            str(self.basket_payment.id),
        )
        make_basket_payment_mock.assert_called_once_with(
            basket_payment_id=str(self.basket_payment.id),
            user_id=self.user.id,
            payment_method_data=AuthorizePaymentMethodDataEntity(
                tokenized_pm_id=None,
                payment_token='qwe',
                external_token_type=PaymentMethodType.APPLE_PAY,
            ),
            device_data=DeviceDataEntity(
                device_fingerprint=None,
                phone_number=self.user.cell_phone,
                user_agent=None,
                ip='127.0.0.1',
            ),
            trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__MOBILE_PAYMENT_CONFIRM,
        )


class CancelPaymentViewTestCase(CustomerAPITestCase):
    @classmethod
    def setUpTestData(cls):
        cls.user = baker.make(
            User,
            email='<EMAIL>',
            cell_phone='+***********',
        )
        cls.customer_booking_src = get_or_create_booking_source(
            app_type=BookingSources.CUSTOMER_APP,
            api_key='customer_key',
        )

    def test_action_non_allowed(self):
        self.basket_payment = baker.make(
            BasketPayment,
            user_id=self.user.id,
            status=BasketPaymentStatus.FAILED,
            amount=50,
        )

        response = self.client.post(
            reverse(
                'payments__cancel_basket_payment',
                kwargs={'basket_payment_id': self.basket_payment.id},
            ),
            content_type='application/json',
        )

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.json()['errors'][0]['code'], 'invalid')
        self.assertIn('not allowed', response.json()['errors'][0]['description'])

    @parameterized.expand(
        [
            (True,),
            (False,),
        ]
    )
    @mock.patch('webapps.point_of_sale.ports.BasketPort.cancel_basket_payment')
    def test_customer_cancel_basket_payment_view(self, canceled, port_mock):
        self.basket_payment = baker.make(
            BasketPayment,
            user_id=self.user.id,
            status=BasketPaymentStatus.PENDING,
            amount=50,
        )

        port_mock.return_value = mock.MagicMock(entity=canceled)
        response = self.client.post(
            reverse(
                'payments__cancel_basket_payment',
                kwargs={'basket_payment_id': self.basket_payment.id},
            ),
            content_type='application/json',
        )
        port_mock.assert_called_once_with(
            basket_payment_id=self.basket_payment.id,
            user_id=self.user.id,
        )
        self.assertDictEqual(response.json(), {'result': canceled})


class CustomerListBasketsViewTestCase(CustomerAPITestCase):
    @classmethod
    def setUpTestData(cls):
        cls.user = baker.make(
            User,
            email='<EMAIL>',
            cell_phone='+***********',
        )
        cls.customer_booking_src = get_or_create_booking_source(
            app_type=BookingSources.CUSTOMER_APP,
            api_key='customer_key',
        )

    def _create_mock_basket_entity(self, basket_id=None):
        if basket_id is None:
            basket_id = uuid.uuid4()

        return ExtendedBasketEntity(
            id=basket_id,
            business_id=1,
            type=BasketType.PAYMENT,
            archived=None,
            customer_card_id=1,
            metadata={},
            created=datetime.now(),
            public_id=123,
            basket_number="ABC-123",
            status=BasketStatus.PENDING,
            total=10000,
            already_paid=7500,
            remaining=2500,
            payment_type=PaymentMethodType.CARD,
            payments=[],
            items=[],
            tips=[],
            transaction_id=15975,
            customer_data='jan kowalski',
            customer_email='<EMAIL>',
        )

    @patch('webapps.business.ports.business_details.BusinessDetailsPort.get_business_details')
    @patch('webapps.point_of_sale.ports.BasketPort.get_baskets')
    @patch('webapps.point_of_sale.ports.BasketPort.get_baskets_count')
    @patch('webapps.user.models.User.business_customer_infos')
    def test_get_baskets_success(
        self,
        business_customer_infos_mock,
        get_baskets_count_mock,
        get_baskets_mock,
        get_business_details_mock,
    ):
        business_customer_infos_mock.values_list.return_value = [1, 2, 3]
        basket_entities = [
            self._create_mock_basket_entity(),
            self._create_mock_basket_entity(),
        ]
        get_baskets_mock.return_value = basket_entities
        get_baskets_count_mock.return_value = 2

        business_details = BusinessDetailsEntity(
            id=1, name="Test Business", address="123 Test St", timezone=tz.gettz('UTC')
        )
        get_business_details_mock.return_value = business_details

        response = self.client.get(
            reverse('payments__list_baskets'),
            content_type='application/json',
        )

        self.assertEqual(response.status_code, HTTP_200_OK)
        results = response.json()['results']
        self.assertEqual(len(results), 2)
        self.assertEqual(response.json()['total'], 2)
        get_baskets_mock.assert_called_once_with(
            customer_card_ids=[1, 2, 3],
            page=1,  # Default page
            per_page=20,  # Default per_page
            basket_status=None,
        )

        for result in results:
            dict_assert(
                result,
                {
                    'id': result['id'],  # Skip UUID check
                    'created': result['created'],  # Skip exact datetime check
                    'public_id': 123,
                    'basket_number': 'ABC-123',
                    'payments_summary': {
                        'total': {'amount': 10000, 'amount_formatted': '$100.00'},
                        'already_paid': {
                            'amount': 7500,
                            'amount_formatted': '$75.00',
                        },
                        'remaining': {
                            'amount': 2500,
                            'amount_formatted': '$25.00',
                        },
                        'payment_type': {
                            'code': PaymentMethodType.CARD,
                            'label': PaymentMethodType.CARD.label,
                        },
                        'status': {'code': BasketStatus.PENDING, 'label': 'Pending'},
                    },
                    'business_details': {
                        'id': 1,
                        'name': 'Test Business',
                        'address': '123 Test St',
                    },
                },
            )

    @patch('webapps.business.ports.business_details.BusinessDetailsPort.get_business_details')
    @patch('webapps.point_of_sale.ports.BasketPort.get_baskets')
    @patch('webapps.user.models.User.business_customer_infos')
    def test_get_baskets_pagination(
        self,
        business_customer_infos_mock,
        get_baskets_mock,
        get_business_details_mock,
    ):
        business_customer_infos_mock.values_list.return_value = [1, 2, 3]
        basket_entities = [
            self._create_mock_basket_entity(),
        ]
        get_baskets_mock.return_value = basket_entities

        business_details = BusinessDetailsEntity(
            id=1, name="Test Business", address="123 Test St", timezone=tz.gettz('UTC')
        )
        get_business_details_mock.return_value = business_details

        response = self.client.get(
            reverse('payments__list_baskets') + '?page=2&per_page=5',
            content_type='application/json',
        )

        self.assertEqual(response.status_code, HTTP_200_OK)
        self.assertEqual(response.json()['page'], 2)
        self.assertEqual(response.json()['per_page'], 5)
        get_baskets_mock.assert_called_once_with(
            customer_card_ids=[1, 2, 3],
            page=2,
            per_page=5,
            basket_status=None,
        )

        results = response.json()['results']
        self.assertEqual(len(results), 1)

    @patch('webapps.point_of_sale.ports.BasketPort.get_baskets')
    @patch('webapps.user.models.User.business_customer_infos')
    def test_get_baskets_empty_results(self, business_customer_infos_mock, get_baskets_mock):
        business_customer_infos_mock.values_list.return_value = [1, 2, 3]
        get_baskets_mock.return_value = []

        response = self.client.get(
            reverse('payments__list_baskets'),
            content_type='application/json',
        )

        self.assertEqual(response.status_code, HTTP_200_OK)
        self.assertEqual(len(response.json()['results']), 0)
        self.assertEqual(response.json()['page'], 1)
        self.assertEqual(response.json()['per_page'], 20)
        get_baskets_mock.assert_called_once_with(
            customer_card_ids=[1, 2, 3],
            page=1,
            per_page=20,
            basket_status=None,
        )

    @patch('webapps.business.ports.business_details.BusinessDetailsPort.get_business_details')
    @patch('webapps.point_of_sale.ports.BasketPort.get_baskets')
    @patch('webapps.user.models.User.business_customer_infos')
    def test_get_baskets_with_status_filter(
        self,
        business_customer_infos_mock,
        get_baskets_mock,
        get_business_details_mock,
    ):
        business_customer_infos_mock.values_list.return_value = [1, 2, 3]
        basket_entities = [
            self._create_mock_basket_entity(),
        ]
        get_baskets_mock.return_value = basket_entities

        business_details = BusinessDetailsEntity(
            id=1, name="Test Business", address="123 Test St", timezone=tz.gettz('UTC')
        )
        get_business_details_mock.return_value = business_details

        response = self.client.get(
            reverse('payments__list_baskets') + '?basket_status=pending',
            content_type='application/json',
        )

        self.assertEqual(response.status_code, HTTP_200_OK)
        self.assertEqual(len(response.json()['results']), 1)
        get_baskets_mock.assert_called_once_with(
            customer_card_ids=[1, 2, 3],
            page=1,
            per_page=20,
            basket_status=BasketStatus.PENDING,
        )

    @patch('webapps.point_of_sale.ports.BasketPort.get_baskets')
    @patch('webapps.user.models.User.business_customer_infos')
    def test_get_baskets_with_invalid_status(self, business_customer_infos_mock, get_baskets_mock):
        business_customer_infos_mock.values_list.return_value = [1, 2, 3]

        response = self.client.get(
            reverse('payments__list_baskets') + '?basket_status=invalid_status',
            content_type='application/json',
        )

        self.assertEqual(response.status_code, 400)
        self.assertIn('basket_status', response.json())
        get_baskets_mock.assert_not_called()


class CustomerBasketDetailsViewTestCase(CustomerAPITestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.user = baker.make(
            User,
            email='<EMAIL>',
            cell_phone='+***********',
        )
        cls.business = business_recipe.make()
        cls.pos = baker.make(
            POS, business=cls.business, receipt_footer_line_1='test', receipt_footer_line_2='Test'
        )

    def _create_mock_basket_item(self, basket_id, item_id=None):
        return ExtendedBasketItemEntity(
            id=item_id or uuid.uuid4(),
            basket_id=basket_id,
            item_price=5000,
            discount_rate=0,
            gross_total=5000,
            type="service",
            name_line_1="Awesome Service",
            name_line_2="Provided by expert",
            total=5000,
            net_total=5000,
            quantity=1,
            tax_amount=0,
            tax_rate=0,
            discounted_total=5000,
            tax_type=BasketItemTaxType.EXCLUDED,
        )

    def _create_mock_basket_payment(self, basket_id, payment_id=None):
        return BasketPaymentEntity(
            id=payment_id or uuid.uuid4(),
            basket_id=basket_id,
            amount=10000,
            payment_method=PaymentMethodType.CARD,
            payment_provider_code=PaymentProviderCode.STRIPE,
            balance_transaction_id=uuid.uuid4(),
            status=BasketPaymentStatus.SUCCESS,
            user_id=self.user.id,
            type=BasketPaymentType.PAYMENT,
            parent_basket_payment_id=None,
            error_code=None,
            action_required_details=None,
            metadata={'internal_ref': 'xyz'},
            auto_capture=True,
            source=BasketPaymentSource.PREPAYMENT,
            created=datetime.now(),
            operator_id=None,
            label='label',
        )

    def _create_mock_basket_tip(self, tip_id=None):
        return BasketTipEntity(
            id=tip_id or uuid.uuid4(),
            amount=1000,
        )

    def _create_mock_extended_basket_entity(
        self, basket_id=None, include_items=True, include_payments=True, include_tips=True
    ):
        basket_uuid = basket_id or uuid.uuid4()
        items_list = []
        if include_items:
            items_list.append(self._create_mock_basket_item(basket_id=basket_uuid))
            items_list.append(
                self._create_mock_basket_item(basket_id=basket_uuid, item_id=uuid.uuid4())
            )

        payments_list = []
        if include_payments:
            payments_list.append(self._create_mock_basket_payment(basket_id=basket_uuid))

        tips_list = []
        if include_tips:
            tips_list.append(self._create_mock_basket_tip())

        return ExtendedBasketEntity(
            id=basket_uuid,
            business_id=self.business.id,
            type=BasketType.PAYMENT,
            archived=None,
            metadata={},
            customer_card_id=123,
            created=datetime.now(),
            public_id=1234,
            basket_number="BASKET-XYZ-789",
            status=BasketStatus.PENDING,
            total=11000,
            already_paid=10000,
            remaining=1000,
            payment_type=PaymentMethodType.CARD,
            items=items_list,
            payments=payments_list,
            tips=tips_list,
            transaction_id=15975,
            customer_data='jan kowalski',
            customer_email='<EMAIL>',
        )

    @patch('webapps.business.ports.business_details.BusinessDetailsPort.get_business_details')
    @patch('webapps.point_of_sale.ports.BasketPort.get_basket_details')
    def test_get_basket_details_success(self, mock_get_basket_details, get_business_details_mock):
        basket_uuid = uuid.uuid4()
        mock_basket_entity = self._create_mock_extended_basket_entity(basket_id=basket_uuid)
        mock_get_basket_details.return_value = mock_basket_entity

        business_details = BusinessDetailsEntity(
            id=1, name="Test Business", address="123 Test St", timezone=tz.gettz('UTC')
        )
        get_business_details_mock.return_value = business_details

        url = reverse(
            'payments__basket_customer_details', kwargs={'basket_payment_id': str(basket_uuid)}
        )
        response = self.client.get(url, content_type='application/json')

        self.assertEqual(response.status_code, HTTP_200_OK)
        mock_get_basket_details.assert_called_once_with(basket_id=str(basket_uuid))

        actual_response_result = response.json()['result']

        expected_result = {
            'id': actual_response_result.get('id'),
            'created': actual_response_result.get('created'),
            'public_id': mock_basket_entity.public_id,
            'basket_number': mock_basket_entity.basket_number,
            'business_details': {'id': 1, 'name': 'Test Business', 'address': '123 Test St'},
            'payments_summary': {
                'total': {'amount': 11000, 'amount_formatted': '$110.00'},
                'already_paid': {'amount': 10000, 'amount_formatted': '$100.00'},
                'remaining': {'amount': 1000, 'amount_formatted': '$10.00'},
                'payment_type': {'code': 'card', 'label': 'Card (Mobile Payment)'},
                'status': {'code': 'pending', 'label': 'Pending'},
            },
            'total_elements': [
                {'label': 'Subtotal', 'amount': {'amount': 10000, 'amount_formatted': '$100.00'}},
                {'label': 'Tips', 'amount': {'amount': 1000, 'amount_formatted': '$10.00'}},
                {'label': 'Discount', 'amount': {'amount': 0, 'amount_formatted': '$0.00'}},
                {'label': 'Taxes & Fees', 'amount': {'amount': 0, 'amount_formatted': '$0.00'}},
            ],
            'payments': [
                {
                    'id': str(mock_basket_entity.payments[0].id),
                    'status': 'success',
                    'payment_type': {'code': 'card', 'source': 'prepayment', 'label': 'label'},
                    'amount': 100.0,
                    'amount_formatted': '$100.00',
                    'created': actual_response_result.get('payments')[0].get('created'),
                }
            ],
            'items': [
                {
                    'id': actual_response_result.get('items')[0].get('id'),
                    'name_line_1': 'Awesome Service',
                    'name_line_2': 'Provided by expert',
                    'quantity': 1,
                    'discount_rate': 0,
                    'total': 5000,
                    'total_formatted': '$50.00',
                },
                {
                    'id': actual_response_result.get('items')[1].get('id'),
                    'name_line_1': 'Awesome Service',
                    'name_line_2': 'Provided by expert',
                    'quantity': 1,
                    'discount_rate': 0,
                    'total': 5000,
                    'total_formatted': '$50.00',
                },
            ],
            'send_receipt_email_allowed': True,
            'receipt_foot_line_1': self.pos.receipt_footer_line_1,
            'receipt_foot_line_2': self.pos.receipt_footer_line_2,
            'disclaimer': 'The above confirmation of sale is not a fiscal receipt.',
        }

        dict_assert(actual_response_result, expected_result)

    @patch('webapps.point_of_sale.ports.BasketPort.get_basket_details')
    def test_get_basket_details_not_found(self, mock_get_basket_details):
        basket_uuid = uuid.uuid4()
        mock_get_basket_details.return_value = None  # Simulate basket not found

        url = reverse(
            'payments__basket_customer_details', kwargs={'basket_payment_id': str(basket_uuid)}
        )
        response = self.client.get(url, content_type='application/json')

        self.assertEqual(response.status_code, HTTP_404_NOT_FOUND)
        mock_get_basket_details.assert_called_once_with(basket_id=str(basket_uuid))

    @patch('webapps.payments.tasks.send_email')
    @patch('webapps.user.models.User.business_customer_infos')
    @patch('webapps.business.ports.business_details.BusinessDetailsPort.get_business_details')
    @patch('webapps.pos.ports.TransactionPort.get_receipt_details')
    @patch('webapps.point_of_sale.ports.BasketPort.get_basket')
    @patch('webapps.point_of_sale.ports.BasketPort.get_basket_details')
    def test_send_basket_details_success(
        self,
        get_basket_details_mock,
        get_basket_mock,
        get_receipt_details_mock,
        get_business_details_mock,
        business_customer_infos_mock,
        send_email_mock,
    ):
        business_customer_infos_mock.values_list.return_value = [1, 2, 123]

        basket = self._create_mock_extended_basket_entity()
        get_basket_details_mock.return_value = basket
        get_basket_mock.return_value = basket

        business_details = BusinessDetailsEntity(
            id=1, name="Test Business", address="123 Test St", timezone=tz.gettz('UTC')
        )
        get_business_details_mock.return_value = business_details

        receipt_details = ReceiptDetailsEntity(
            id=1,
            receipt_number="Receipt number",
            assigned_number="Assigned number",
            transaction_id=123,
            customer_data="John Kowalski, <EMAIL>",
            customer_id=123,
            customer_name="John Kowalski",
            customer_email="<EMAIL>",
        )
        get_receipt_details_mock.return_value = receipt_details

        response = self.client.post(
            reverse(
                'payments__send_basket_details',
                kwargs={'basket_id': basket.id},
            ),
            content_type='application/json',
        )

        self.assertEqual(response.status_code, HTTP_200_OK)

        get_basket_mock.assert_called_once_with(
            str(basket.id),
        )

        get_basket_details_mock.assert_called_once_with(
            basket_id=str(basket.id),
        )

        get_business_details_mock.assert_called_once_with(
            basket.business_id,
        )
        get_receipt_details_mock.assert_called_once_with(
            basket_id=str(basket.id),
        )
        send_email_mock.assert_called_once_with(
            to_addr=self.user.email,
            body=mock.ANY,
            history_data=mock.ANY,
            from_data=('Test Business', '<EMAIL>'),
            to_name='John Kowalski',
        )

    @patch('webapps.user.models.User.business_customer_infos')
    @patch('webapps.point_of_sale.ports.BasketPort.get_basket')
    def test_send_basket_details_no_permission(
        self,
        get_basket_mock,
        business_customer_infos_mock,
    ):
        business_customer_infos_mock.values_list.return_value = [100]  # wrong ID

        basket = self._create_mock_extended_basket_entity()
        get_basket_mock.return_value = basket

        response = self.client.post(
            reverse(
                'payments__send_basket_details',
                kwargs={'basket_id': basket.id},
            ),
            content_type='application/json',
        )

        self.assertEqual(response.status_code, HTTP_400_BAD_REQUEST)


# pylint: disable=line-too-long
@patch('webapps.point_of_sale.services.basket_payment.BasketPaymentService.is_refund_possible')
@patch(
    'webapps.point_of_sale.services.basket_payment.BasketPaymentService.initialize_refund_basket_payment'
)
@patch('webapps.payments.views.payment.business_refund_basket_payment.SmartLock')
class BusinessRefundBasketPaymentViewTestCase(BaseBusinessApiTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        roles = [
            Resource.STAFF_ACCESS_LEVEL_OWNER,
            Resource.STAFF_ACCESS_LEVEL_MANAGER,
            Resource.STAFF_ACCESS_LEVEL_RECEPTION,
            Resource.STAFF_ACCESS_LEVEL_ADVANCED,
            Resource.STAFF_ACCESS_LEVEL_STAFF,
        ]
        cls.cx_user = user_recipe.make()
        cls.px_users = {role: user_recipe.make() for role in roles}
        cls.business = business_recipe.make(
            active_from=datetime(2024, 6, 1, 10, tzinfo=UTC),
            owner=cls.px_users[Resource.STAFF_ACCESS_LEVEL_OWNER],
        )
        cls.staffers = {
            role: staffer_recipe.make(
                business=cls.business,
                staff_user=user,
                staff_access_level=role,
            )
            for role, user in cls.px_users.items()
        }
        cls.biz_booking_src = get_or_create_booking_source(
            app_type=BookingSources.BUSINESS_APP,
            api_key='business_key',
        )

    def setUp(self) -> None:
        # By default, assign the owner as a request user - override in tests if needed.
        self.user = self.px_users[Resource.STAFF_ACCESS_LEVEL_OWNER]
        super().setUp()

    @parameterized.expand(
        [
            (Resource.STAFF_ACCESS_LEVEL_OWNER, 200, True),
            (Resource.STAFF_ACCESS_LEVEL_MANAGER, 200, True),
            (Resource.STAFF_ACCESS_LEVEL_RECEPTION, 200, True),
            (Resource.STAFF_ACCESS_LEVEL_ADVANCED, 200, True),
            (Resource.STAFF_ACCESS_LEVEL_STAFF, 404, False),
        ]
    )
    @patch('webapps.point_of_sale.services.basket_payment.mark_sent_for_refund_adapter')
    @patch('webapps.point_of_sale.services.basket_payment.update_basket_payment_id_adapter')
    @patch('webapps.point_of_sale.services.basket_payment.basket_payment_refunded_event')
    def test_staffer_access(
        self,
        access_level: str,
        expected_status: int,
        should_call_refund: bool,
        mock_basket_payment_refunded_event: MagicMock,
        mock_update_basket_payment_id_adapter: MagicMock,
        mock_mark_sent_for_refund_adapter: MagicMock,
        mock_smart_lock: MagicMock,
        mock_initialize_refund_basket_payment: MagicMock,
        mock_is_refund_possible: MagicMock,
    ) -> None:
        """
        Scenario:
        Staffer with different roles attempt to refund a basket payment.
        """
        self.user = self.px_users[access_level]
        self.authorized_user = getattr(self.client, self.AUTH_METHOD)(
            user=self.user,
            source=self.biz_booking_src,
        )
        original_basket_payment = baker.make(
            BasketPayment,
            user_id=self.cx_user.id,
            status=BasketPaymentStatus.SUCCESS,
            amount=50,
            basket__business_id=self.business.id,
        )
        refund_basket_payment = baker.make(
            BasketPayment,
            basket=original_basket_payment.basket,
            parent_basket_payment=original_basket_payment,
            balance_transaction_id=3,
            type=BasketPaymentType.REFUND,
            status=BasketPaymentStatus.PENDING,
            payment_method=PaymentMethodType.CARD,
        )
        mock_initialize_refund_basket_payment.return_value = refund_basket_payment
        mock_is_refund_possible.return_value = True, None
        refund_row = MagicMock(id=123)
        mock_mark_sent_for_refund_adapter.return_value = refund_row
        operator_id = self.staffers[access_level].id

        response = self.client.post(
            reverse(
                'payments__refund_basket_payment',
                kwargs={
                    'business_pk': self.business.id,
                    'basket_payment_id': original_basket_payment.id,
                },
            ),
            content_type='application/json',
        )

        self.assertEqual(response.status_code, expected_status)
        mock_smart_lock.assert_called_once_with(
            key=f'refund_basket_payment:{original_basket_payment.id}'
        )
        if should_call_refund:
            self.assertEqual(response.json(), {})
            mock_mark_sent_for_refund_adapter.assert_called_once_with(
                original_basket_payment.id,
                operator_id,
                None,
            )
            mock_initialize_refund_basket_payment.assert_called_once_with(
                original_basket_payment=original_basket_payment,
                operator_id=operator_id,
                amount=None,
            )
            mock_update_basket_payment_id_adapter.assert_called_once_with(
                refund_row.id, refund_basket_payment.id
            )
            mock_basket_payment_refunded_event.send.assert_called_once_with(
                refund_basket_payment.id
            )
        else:
            mock_mark_sent_for_refund_adapter.assert_not_called()
            mock_initialize_refund_basket_payment.assert_not_called()
            mock_update_basket_payment_id_adapter.assert_not_called()
            mock_basket_payment_refunded_event.assert_not_called()

    def test_refund_basket_payment_from_different_business(
        self,
        mock_smart_lock: MagicMock,
        mock_initialize_refund_basket_payment: MagicMock,
        mock_is_refund_possible: MagicMock,
    ) -> None:
        """
        Scenario:
        A Business_A owner attempts to refund a Business_B-owned basket payment.
        """
        other_business = business_recipe.make(
            active_from=datetime(2024, 6, 1, 10, tzinfo=UTC),
        )
        basket_payment = baker.make(
            BasketPayment,
            user_id=self.cx_user.id,
            status=BasketPaymentStatus.SUCCESS,
            amount=50,
            basket__business_id=other_business.id,
        )
        mock_is_refund_possible.return_value = True, None

        response = self.client.post(
            reverse(
                'payments__refund_basket_payment',
                kwargs={'business_pk': self.business.id, 'basket_payment_id': basket_payment.id},
            ),
            content_type='application/json',
        )

        self.assertEqual(response.status_code, 404)
        mock_smart_lock.assert_called_once_with(key=f'refund_basket_payment:{basket_payment.id}')
        mock_initialize_refund_basket_payment.assert_not_called()
        self.assertEqual(
            response.json(),
            {
                'errors': [
                    {
                        'type': 'invalid',
                        'code': 'not_found',
                        'description': 'Requested object not found',
                    }
                ]
            },
        )

    @parameterized.expand(
        [
            (RefundError.ALREADY_REFUNDED, 'already_refunded', 'This payment was already refunded'),
            (RefundError.EXPIRED, 'expired', 'This payment can\'t be refunded anymore'),
            (RefundError.INVALID_REFUND_AMOUNT, 'invalid_amount', 'Invalid refund amount'),
            (
                RefundError.MISSING_BALANCE,
                'missing_balance',
                'There is not enough balance to create a refund',
            ),
            (
                RefundError.INVALID_PAYMENT_STATUS,
                'invalid_payment_status',
                'This payment cannot be refunded',
            ),
        ]
    )
    def test_refund_not_possible(
        self,
        mock_smart_lock: MagicMock,
        mock_initialize_refund_basket_payment: MagicMock,
        mock_is_refund_possible: MagicMock,
        refund_error: RefundError,
        error_value: str,
        error_label: str,
    ) -> None:
        """
        Scenario:
        Attempt to proceed with a non-refundable basket payment.
        """
        basket_payment = baker.make(
            BasketPayment,
            user_id=self.cx_user.id,
            status=BasketPaymentStatus.SUCCESS,
            amount=50,
            basket__business_id=self.business.id,
        )
        mock_is_refund_possible.return_value = False, refund_error

        response = self.client.post(
            reverse(
                'payments__refund_basket_payment',
                kwargs={'business_pk': self.business.id, 'basket_payment_id': basket_payment.id},
            ),
            content_type='application/json',
        )

        self.assertEqual(response.status_code, 400)
        mock_smart_lock.assert_called_once_with(key=f'refund_basket_payment:{basket_payment.id}')
        mock_initialize_refund_basket_payment.assert_not_called()
        self.assertEqual(
            response.json(),
            {
                'errors': [
                    {
                        'code': error_value,
                        'description': error_label,
                    }
                ]
            },
        )

    @patch('webapps.pos.ports.PaymentRowPort.mark_sent_for_refund')
    @patch('webapps.pos.ports.PaymentRowPort.update_basket_payment_id')
    @patch('webapps.point_of_sale.services.basket_payment.basket_payment_refunded_event.send')
    def test_refund(
        self,
        _mock_basket_payment_refunded_event_send: MagicMock,
        _mock_update_basket_payment_id: MagicMock,
        mock_mark_sent_for_refund: MagicMock,
        mock_smart_lock: MagicMock,
        mock_initialize_refund_basket_payment: MagicMock,
        mock_is_refund_possible: MagicMock,
    ) -> None:
        user_id = self.cx_user.id
        basket_amount = 5000
        basket_payment = baker.make(
            BasketPayment,
            user_id=user_id,
            status=BasketPaymentStatus.SUCCESS,
            amount=basket_amount,
            basket__business_id=self.business.id,
        )
        mock_is_refund_possible.return_value = True, None

        response = self.client.post(
            reverse(
                'payments__refund_basket_payment',
                kwargs={'business_pk': self.business.id, 'basket_payment_id': basket_payment.id},
            ),
            content_type='application/json',
        )

        mock_smart_lock.assert_called_once_with(key=f'refund_basket_payment:{basket_payment.id}')
        mock_initialize_refund_basket_payment.assert_called_once_with(
            original_basket_payment=basket_payment,
            amount=None,
            operator_id=mock.ANY,
        )
        mock_mark_sent_for_refund.assert_called_once_with(
            basket_payment_id=basket_payment.id,
            operator_id=mock.ANY,
            amount=None,
        )

        self.assertEqual(response.status_code, 200)

    @override_eppo_feature_flag({PartialRefundsFlag.flag_name: True})
    @patch('webapps.pos.ports.PaymentRowPort.mark_sent_for_refund')
    @patch('webapps.pos.ports.PaymentRowPort.update_basket_payment_id')
    @patch('webapps.point_of_sale.services.basket_payment.basket_payment_refunded_event.send')
    def test_partial_refund(
        self,
        _mock_basket_payment_refunded_event_send: MagicMock,
        _mock_update_basket_payment_id: MagicMock,
        mock_mark_sent_for_refund: MagicMock,
        mock_smart_lock: MagicMock,
        mock_initialize_refund_basket_payment: MagicMock,
        mock_is_refund_possible: MagicMock,
    ) -> None:
        user_id = self.cx_user.id
        refund_amount = 2510
        basket_payment = baker.make(
            BasketPayment,
            user_id=user_id,
            status=BasketPaymentStatus.SUCCESS,
            amount=5000,
            basket__business_id=self.business.id,
        )
        mock_is_refund_possible.return_value = True, None

        response = self.client.post(
            reverse(
                'payments__refund_basket_payment',
                kwargs={'business_pk': self.business.id, 'basket_payment_id': basket_payment.id},
            ),
            data={'refund_amount': refund_amount},
            content_type='application/json',
        )

        self.assertEqual(response.status_code, 200)
        mock_smart_lock.assert_called_once_with(key=f'refund_basket_payment:{basket_payment.id}')
        mock_initialize_refund_basket_payment.assert_called_once_with(
            original_basket_payment=basket_payment,
            amount=refund_amount,
            operator_id=mock.ANY,
        )
        mock_mark_sent_for_refund.assert_called_once_with(
            basket_payment_id=basket_payment.id,
            operator_id=mock.ANY,
            amount=Decimal(refund_amount) / 100,
        )

    @override_eppo_feature_flag({PartialRefundsFlag.flag_name: False})
    def test_partial_refund_ff_turned_off(
        self,
        mock_smart_lock: MagicMock,
        mock_initialize_refund_basket_payment: MagicMock,
        mock_is_refund_possible: MagicMock,
    ) -> None:
        basket_payment = baker.make(
            BasketPayment,
            user_id=self.cx_user.id,
            status=BasketPaymentStatus.SUCCESS,
            amount=5000,
            basket__business_id=self.business.id,
        )

        response = self.client.post(
            reverse(
                'payments__refund_basket_payment',
                kwargs={'business_pk': self.business.id, 'basket_payment_id': basket_payment.id},
            ),
            data={'refund_amount': 2510},
            content_type='application/json',
        )

        self.assertEqual(response.status_code, 400)
        self.assertEqual(response.json()['errors'][0]['code'], 'partial_refunds_disabled')

    def test_lock(
        self,
        mock_smart_lock: MagicMock,
        mock_initialize_refund_basket_payment: MagicMock,
        mock_is_refund_possible: MagicMock,
    ) -> None:
        """
        Scenario:
        Too many attempts to refund a basket payment (race condition).
        """
        mock_smart_lock.return_value.__enter__.side_effect = LockFailed()
        basket_payment = baker.make(
            BasketPayment,
            user_id=self.cx_user.id,
            status=BasketPaymentStatus.SUCCESS,
            amount=50,
            basket__business_id=self.business.id,
        )

        response = self.client.post(
            reverse(
                'payments__refund_basket_payment',
                kwargs={'business_pk': self.business.id, 'basket_payment_id': basket_payment.id},
            ),
            content_type='application/json',
        )

        self.assertEqual(response.status_code, 409)
        mock_smart_lock.assert_called_once_with(key=f'refund_basket_payment:{basket_payment.id}')
        mock_is_refund_possible.assert_not_called()
        mock_initialize_refund_basket_payment.assert_not_called()
        self.assertEqual(
            response.json(),
            {
                'errors': [
                    {
                        'code': 'lock_error',
                        'description': 'Could not perform action',
                    },
                ],
            },
        )


class BusinessRefundBasketRefundSplitsViewTestCase(BaseBusinessApiTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        roles = [
            Resource.STAFF_ACCESS_LEVEL_OWNER,
            Resource.STAFF_ACCESS_LEVEL_MANAGER,
            Resource.STAFF_ACCESS_LEVEL_RECEPTION,
            Resource.STAFF_ACCESS_LEVEL_ADVANCED,
            Resource.STAFF_ACCESS_LEVEL_STAFF,
        ]
        cls.cx_user = user_recipe.make()
        cls.px_users = {role: user_recipe.make() for role in roles}
        cls.business = business_recipe.make(
            active_from=datetime(2024, 6, 1, 10, tzinfo=UTC),
            owner=cls.px_users[Resource.STAFF_ACCESS_LEVEL_OWNER],
        )
        cls.staffers = {
            role: staffer_recipe.make(
                business=cls.business,
                staff_user=user,
                staff_access_level=role,
            )
            for role, user in cls.px_users.items()
        }
        cls.biz_booking_src = get_or_create_booking_source(
            app_type=BookingSources.BUSINESS_APP,
            api_key='business_key',
        )

    def setUp(self) -> None:
        # By default, assign the owner as a request user - override in tests if needed.
        self.user = self.px_users[Resource.STAFF_ACCESS_LEVEL_OWNER]
        super().setUp()

    def test_refund_splits(
        self,
    ) -> None:
        user_id = self.cx_user.id
        basket_amount = 5000

        business_wallet, _ = PaymentGatewayPort.get_or_create_business_wallet(
            business_id=self.business.id,
            statement_name='statement name',
        )
        balance_transaction = baker.make(
            BalanceTransaction,
            basket__business_id=self.business.id,
            receiver_id=business_wallet.id,
        )
        fees = {"fixed_fee": 49, "percentage_fee": "1.89"}
        baker.make(
            Payment,
            balance_transaction_id=balance_transaction.id,
            refund_splits=fees,
            payment_splits=fees,
            dispute_splits=fees,
        )
        basket_payment = baker.make(
            BasketPayment,
            user_id=user_id,
            status=BasketPaymentStatus.SUCCESS,
            amount=basket_amount,
            balance_transaction_id=balance_transaction.id,
            basket__business_id=self.business.id,
        )

        response = self.client.get(
            reverse(
                'payments__refund_fees_basket_payment',
                kwargs={'business_pk': self.business.id, 'basket_payment_id': basket_payment.id},
            ),
            content_type='application/json',
        )

        self.assertEqual(response.status_code, 200)
        fees['label'] = '1.89% + $0.49'
        self.assertEqual(response.json(), fees)
