import uuid
from datetime import datetime
from unittest.mock import patch

from dateutil import tz
from django.test.testcases import TestCase

from lib.payments.enums import PaymentProviderCode
from lib.point_of_sale.entities import (
    BasketPaymentEntity,
    ExtendedBasketEntity,
    ExtendedBasketItemEntity,
    BasketTipEntity,
)
from lib.point_of_sale.enums import (
    BasketPaymentSource,
    BasketPaymentStatus,
    BasketPaymentType,
    BasketStatus,
    BasketType,
    PaymentMethodType,
    BasketItemTaxType,
)
from lib.pos.entities import POSEntity
from service.tests import dict_assert
from webapps.business.ports.business_details import BusinessDetailsEntity
from webapps.invoicing.ports import CustomerInvoiceEntity
from webapps.payments.services.business_basket import BusinessBasketService


class BusinessBasketServiceTestCase(TestCase):

    @staticmethod
    def _create_mock_basket_item(basket_id, item_id=None):
        return ExtendedBasketItemEntity(
            id=item_id or uuid.uuid4(),
            basket_id=basket_id,
            item_price=5000,
            discount_rate=0,
            gross_total=5000,
            type="service",
            name_line_1="Awesome Service",
            name_line_2="Provided by expert",
            total=5000,
            net_total=5000,
            quantity=1,
            tax_amount=0,
            tax_rate=0,
            discounted_total=5000,
            tax_type=BasketItemTaxType.EXCLUDED,
        )

    @staticmethod
    def _create_mock_basket_payment(basket_id, payment_id=None):
        return BasketPaymentEntity(
            id=payment_id or uuid.uuid4(),
            basket_id=basket_id,
            amount=10000,
            payment_method=PaymentMethodType.CARD,
            payment_provider_code=PaymentProviderCode.STRIPE,
            balance_transaction_id=uuid.uuid4(),
            status=BasketPaymentStatus.SUCCESS,
            user_id=1,
            type=BasketPaymentType.PAYMENT,
            parent_basket_payment_id=None,
            error_code=None,
            action_required_details=None,
            metadata={'internal_ref': 'xyz'},
            auto_capture=True,
            source=BasketPaymentSource.PREPAYMENT,
            created=datetime.now(),
            operator_id=None,
            label='label',
        )

    @staticmethod
    def _create_mock_basket_tip(tip_id=None):
        return BasketTipEntity(
            id=tip_id or uuid.uuid4(),
            amount=1000,
        )

    def _create_mock_extended_basket_entity(
        self, basket_id=None, include_items=True, include_payments=True, include_tips=True
    ):
        basket_uuid = basket_id or uuid.uuid4()
        items_list = []
        if include_items:
            items_list.append(self._create_mock_basket_item(basket_id=basket_uuid))
            items_list.append(
                self._create_mock_basket_item(basket_id=basket_uuid, item_id=uuid.uuid4())
            )

        payments_list = []
        if include_payments:
            payments_list.append(self._create_mock_basket_payment(basket_id=basket_uuid))

        tips_list = []
        if include_tips:
            tips_list.append(self._create_mock_basket_tip())

        return ExtendedBasketEntity(
            id=basket_uuid,
            business_id=1,
            type=BasketType.PAYMENT,
            archived=None,
            metadata={},
            customer_card_id=123,
            created=datetime.now(),
            public_id=1234,
            basket_number="BASKET-XYZ-789",
            status=BasketStatus.PENDING,
            total=11000,
            already_paid=10000,
            remaining=1000,
            payment_type=PaymentMethodType.CARD,
            items=items_list,
            payments=payments_list,
            tips=tips_list,
            transaction_id=15975,
            customer_data='jan kowalski',
            customer_email='<EMAIL>',
        )

    @patch('webapps.pos.ports.POSPort.get_pos_by_business_id')
    @patch('webapps.business.ports.business_details.BusinessDetailsPort.get_business_details')
    @patch('webapps.point_of_sale.ports.BasketPaymentPort.is_refund_possible')
    @patch('webapps.invoicing.ports.CustomerInvoicePort.get_customer_invoice_info')
    def test_get_business_basket_details(
        self,
        get_customer_invoice_info,
        is_refund_possible_mock,
        get_business_details_mock,
        get_pos_by_business_id_mock,
    ):
        is_refund_possible_mock.return_value = (True, None)
        get_business_details_mock.return_value = BusinessDetailsEntity(
            id=1, name="Test Business", address="123 Test St", timezone=tz.gettz('UTC')
        )
        get_pos_by_business_id_mock.return_value = POSEntity(
            id=1,
            receipt_footer_line_1='test',
            receipt_footer_line_2='Test',
            commissions_enabled=True,
            registers_enabled=False,
        )
        get_customer_invoice_info.return_value = CustomerInvoiceEntity(id=None, number=None)

        basket_id = uuid.uuid4()
        basket_extended_entity = self._create_mock_extended_basket_entity(basket_id=basket_id)

        basket_data = BusinessBasketService.get_basket_details(basket_extended_entity)

        dict_assert(
            basket_data,
            {
                'id': basket_extended_entity.id,
                'created': basket_extended_entity.created.astimezone(tz.gettz('UTC')),
                'public_id': 1234,
                'basket_number': 'BASKET-XYZ-789',
                'transaction_id': 15975,
                'business_details': BusinessDetailsEntity(
                    id=1,
                    name='Test Business',
                    address='123 Test St',
                    timezone=tz.tzfile('/usr/share/zoneinfo/UTC'),
                ),
                'payments_summary': {
                    'total': 11000,
                    'already_paid': 10000,
                    'remaining': 1000,
                    'payment_type': PaymentMethodType.CARD,
                    'status': BasketStatus.PENDING,
                },
                'customer_card_id': 123,
                'total_elements': [
                    {'label': 'Subtotal', 'amount': 10000},
                    {'label': 'Tips', 'amount': 1000},
                    {'label': 'Discount', 'amount': 0},
                    {'label': 'Taxes & Fees', 'amount': 0},
                ],
                'payments': [
                    {
                        'id': basket_extended_entity.payments[0].id,
                        'status': 'success',
                        'payment_type': {
                            'code': 'card',
                            'source': BasketPaymentSource.PREPAYMENT,
                            'label': 'label',
                        },
                        'amount': 100.0,
                        'amount_formatted': '$100.00',
                        'created': basket_extended_entity.payments[0].created,
                        'refundable': True,
                    }
                ],
                'items': [
                    {
                        'id': basket_extended_entity.items[0].id,
                        'name_line_1': 'Awesome Service',
                        'name_line_2': 'Provided by expert',
                        'quantity': 1,
                        'discount_rate': 0,
                        'total': 5000,
                        'total_formatted': '$50.00',
                    },
                    {
                        'id': basket_extended_entity.items[1].id,
                        'name_line_1': 'Awesome Service',
                        'name_line_2': 'Provided by expert',
                        'quantity': 1,
                        'discount_rate': 0,
                        'total': 5000,
                        'total_formatted': '$50.00',
                    },
                ],
                'tax_summary': {
                    'tax_rate': 0,
                    'total_tax_amount': 0,
                    'total_net_value': 10000,
                    'total_gross_value': 10000,
                },
                'commissions_enabled': True,
                'send_receipt_email_allowed': True,
                'receipt_foot_line_1': 'test',
                'receipt_foot_line_2': 'Test',
                'disclaimer': 'The above confirmation of sale is not a fiscal receipt.',
                'customer_invoice': None,
                'customer_invoice_number': None,
                'customer_info': {'full_name': 'jan kowalski', 'email': '<EMAIL>'},
                'can_generate_invoice': False,
                'lock': True,
            },
        )
