from decimal import Decimal
from datetime import datetime, time
from dateutil import tz
import freezegun

from django.urls import reverse
from model_bakery import baker

from drf_api.lib.base_drf_test_case import CustomerAPITestCase
from lib.baker_utils import get_or_create_booking_source
from webapps.appointment_drafts.baker import draft_item_recipe, draft_recipe
from webapps.business.baker_recipes import (
    bci_recipe,
    service_variant_recipe,
    service_variant_payment_recipe,
    staffer_recipe,
)
from webapps.business.models import ServiceVariantPayment
from webapps.business.enums import PriceType
from webapps.booking.models import Appointment, BookingSources
from webapps.booking.v2.commons.utils import timedelta_to_minutes, to_day_minutes
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.pos.enums import PaymentTypeEnum
from webapps.pos.models import POS
from webapps.schedule.enums import DayOfWeek
from webapps.schedule.ports import (
    set_business_default_hours,
    set_many_resources_default_hours,
)
from webapps.user.models import User


TZ_NAME = 'UTC'
TZ = tz.gettz(TZ_NAME)
DEFAULT_HOURS = [(time(10, 0, tzinfo=tz.gettz(TZ_NAME)), time(18, 30, tzinfo=tz.gettz(TZ_NAME)))]
DEFAULT_OPENING_HOURS = {
    DayOfWeek.monday: DEFAULT_HOURS,
    DayOfWeek.tuesday: DEFAULT_HOURS,
    DayOfWeek.wednesday: DEFAULT_HOURS,
    DayOfWeek.thursday: DEFAULT_HOURS,
    DayOfWeek.friday: DEFAULT_HOURS,
    DayOfWeek.saturday: DEFAULT_HOURS,
}
BOOKED_FROM = datetime(2024, 2, 14, 17, 30, tzinfo=TZ)


@freezegun.freeze_time(datetime(2024, 2, 12, 9, 0, tzinfo=TZ))
class CalculatePaymentAmountViewTestCase(CustomerAPITestCase):
    @classmethod
    def setUpTestData(cls):
        cls.business = baker.make('business.Business')
        cls.pos = baker.make(
            'pos.POS',
            business=cls.business,
            pay_by_app_status=POS.PAY_BY_APP_ENABLED,
            _force_stripe_pba=True,
            tips_enabled=True,
        )
        cls.user = baker.make(
            User,
            email='<EMAIL>',
            cell_phone='+**************',
        )
        cls.customer_booking_src = get_or_create_booking_source(
            app_type=BookingSources.CUSTOMER_APP,
            api_key='customer_key',
        )

    def setUp(self):
        super().setUp()
        self.wallet, _ = PaymentGatewayPort.get_or_create_customer_wallet(
            customer_user_id=self.user.id,
            email=self.user.email,
            phone=self.user.cell_phone,
            statement_name='',
        )
        self.bci = bci_recipe.make(user=self.user, business=self.business)
        baker.make('pos.PaymentType', pos=self.pos, code=PaymentTypeEnum.PAY_BY_APP)
        baker.make('pos.PaymentType', pos=self.pos, code=PaymentTypeEnum.PREPAYMENT)

        self.price = Decimal('100.00')
        self.service_pp = baker.make('business.Service', business=self.business)
        self.staffer = staffer_recipe.make(
            business=self.business,
            services=[self.service_pp],
        )

        self.service_variant_pp = service_variant_recipe.make(
            service=self.service_pp,
            price=self.price,
            type=PriceType.FIXED,
            resources=[self.staffer],
        )
        service_variant_payment_recipe.make(
            service_variant=self.service_variant_pp,
            payment_type=ServiceVariantPayment.PRE_PAYMENT_TYPE,
            payment_amount=Decimal('40'),  # 40%
            saving_type=ServiceVariantPayment.PERCENTAGE,
        )

        timezone = self.business.get_timezone()
        set_business_default_hours(
            business_id=self.business.id,
            hours=DEFAULT_OPENING_HOURS,
            tz=timezone,
        )
        set_many_resources_default_hours(
            business_id=self.business.id,
            resource_ids=[self.staffer.id],
            hours=DEFAULT_OPENING_HOURS,
            tz=timezone,
        )

    def _create_draft(self):
        draft = draft_recipe.make(
            user_id=self.user.id,
            business_id=self.business.id,
            booked_from=BOOKED_FROM,
            customer_note='Boring conversation anyway',
            total='$15.00',
        )
        draft_item_recipe.make(
            appointment=draft,
            variant_id=self.service_variant_pp.id,
            variant_name=self.service_variant_pp.label,
            variant_duration=timedelta_to_minutes(self.service_variant_pp.duration),
            price="$15.00",
            staffer_id=self.staffer.id,
            service_id=self.service_pp.id,
            service_name=self.service_pp.name,
            booked_from=BOOKED_FROM,
            booked_till=datetime(
                year=2024,
                month=2,
                day=14,
                hour=18,
                minute=0,
                tzinfo=self.business.get_timezone(),
            ),
            time_slot={
                'date': BOOKED_FROM.date().isoformat(),
                'start_time': to_day_minutes(BOOKED_FROM.time()),
            },
            service_price="$15.00",
            omnibus_price='$14.00',
            price_before_discount='$16.00',
            discount='$1.00',
        )
        return draft

    def test_calculate_amount_with_tip(self):
        self.assertEqual(Appointment.objects.count(), 0)

        draft = self._create_draft()

        response = self.client.post(
            reverse(
                'payments__calc_payment_amount',
            ),
            data={
                'draft_id': str(draft.id),
                'tip': {'type': 'percent', 'value': 2500},
            },
            content_type='application/json',
        )
        self.assertEqual(response.status_code, 200)

        self.assertEqual(Appointment.objects.count(), 0)

    def test_calculate_amount_without_tip(self):
        self.assertEqual(Appointment.objects.count(), 0)

        draft = self._create_draft()

        response = self.client.post(
            reverse(
                'payments__calc_payment_amount',
            ),
            data={
                'draft_id': str(draft.id),
            },
            content_type='application/json',
        )
        self.assertEqual(response.status_code, 200)

        self.assertEqual(Appointment.objects.count(), 0)
