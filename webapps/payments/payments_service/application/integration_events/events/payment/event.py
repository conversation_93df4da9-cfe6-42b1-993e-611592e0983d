from dataclasses import dataclass
from datetime import datetime

from webapps.payments.payments_service.application.integration_events.events.base import (
    IntegrationEvent,
)


@dataclass(frozen=True)
class BasePaymentEventData:
    basket_id: str
    basket_payment_id: str
    balance_transaction_id: str | None
    business_id: int
    user_id: int
    payment_method: str
    source: str
    status: str
    created: datetime


@dataclass(frozen=True)
class PaymentSucceededEventData(BasePaymentEventData):
    pass


@dataclass(frozen=True)
class PaymentFailedEventData(BasePaymentEventData):
    error_code: str | None
    error_message: str | None


@dataclass(frozen=True)
class PaymentActionRequiredEventData(BasePaymentEventData):
    action_required_details: dict | None


@dataclass(frozen=True)
class PaymentCanceledEventData(BasePaymentEventData):
    pass


@dataclass(frozen=True)
class PaymentProcessEventPaymentMethod:
    type: str
    tokenized_pm_id: str | None = None
    token: str | None = None


@dataclass(frozen=True)
class PaymentProcessEventTip:
    type: str
    value: int


@dataclass(frozen=True)
class PaymentProcessEventData:
    basket_id: str
    payment_method: PaymentProcessEventPaymentMethod
    tip: PaymentProcessEventTip | None = None


PaymentEventData = (
    PaymentSucceededEventData
    | PaymentFailedEventData
    | PaymentActionRequiredEventData
    | PaymentCanceledEventData
    | PaymentProcessEventData
)


@dataclass(frozen=True)
class PaymentIntegrationEvent(IntegrationEvent[PaymentEventData]):
    pass
