import dataclasses
from dataclasses import dataclass, field
from datetime import datetime
from typing import Generic, TypeVar


EventDataT = TypeVar('EventDataT')


@dataclass(frozen=True)
class IntegrationEvent(Generic[EventDataT]):
    """
    A base data class for integration  events (payload only).
    Transport-related attributes (producer/consumers) are carried separately
    via IntegrationEventAttributes.
    """

    event_id: str
    event_type: str
    event_version: str
    created: datetime
    data: EventDataT


@dataclasses.dataclass(frozen=True)
class IntegrationEventAttributes:
    """Transport-level attributes for an integration event."""

    producer: str
    consumers: list[str] = field(default_factory=list)


@dataclasses.dataclass(frozen=True)
class IntegrationEventContext:
    """
    A base, immutable contextual data for a business operation.
    It's intended to be subclassed for specific use cases.

    It's used to provide extra context required to build an integration event.
    """
