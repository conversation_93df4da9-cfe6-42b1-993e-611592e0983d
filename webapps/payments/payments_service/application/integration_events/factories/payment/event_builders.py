from abc import ABC, abstractmethod

from webapps.payments.payments_service.application.integration_events.events.payment.event import (
    PaymentActionRequiredEventData,
    PaymentCanceledEventData,
    PaymentFailedEventData,
    PaymentIntegrationEvent,
    PaymentProcessEventData,
    PaymentProcessEventPaymentMethod,
    PaymentProcessEventTip,
    PaymentSucceededEventData,
)
from webapps.payments.payments_service.application.integration_events.enums import (
    IntegrationEventType,
)
from webapps.payments.payments_service.domain.entities.basket import Basket
from webapps.payments.payments_service.domain.events.base import DomainEvent
from webapps.payments.payments_service.domain.events.basket_payment import (
    BasketPaymentInitiated,
    BasketPaymentSucceeded,
    BasketPaymentFailed,
    BasketPaymentActionRequired,
    BasketPaymentCanceled,
)


class PaymentIntegrationEventBuilder(ABC):
    @abstractmethod
    def build(
        self,
        event: DomainEvent,
        basket: Basket,
    ) -> PaymentIntegrationEvent | None:
        raise NotImplementedError


class PaymentProcessIntegrationEventBuilder(PaymentIntegrationEventBuilder):
    def build(
        self,
        event: BasketPaymentInitiated,
        basket: Basket,
    ) -> PaymentIntegrationEvent | None:
        event_data = PaymentProcessEventData(
            basket_id=str(basket.basket_id),
            payment_method=PaymentProcessEventPaymentMethod(
                type=event.payment_method.value,
                tokenized_pm_id=str(event.tokenized_pm_id),
                token=event.token,
            ),
            tip=PaymentProcessEventTip(
                type=event.tip_type,
                value=event.tip_value,
            ),
        )

        return PaymentIntegrationEvent(
            event_id=str(event.event_id),
            event_type=IntegrationEventType.PAYMENT_PROCESS,
            event_version=event.event_version(),
            created=event.occurred_at,
            data=event_data,
        )


class PaymentSucceededIntegrationEventBuilder(PaymentIntegrationEventBuilder):
    def build(
        self,
        event: BasketPaymentSucceeded,
        basket: Basket,
    ) -> PaymentIntegrationEvent | None:
        payment = basket.latest_basket_payment
        if not payment:
            return None

        event_data = PaymentSucceededEventData(
            basket_id=str(basket.basket_id),
            basket_payment_id=str(payment.basket_payment_id),
            balance_transaction_id=(
                str(payment.balance_transaction_id) if payment.balance_transaction_id else None
            ),
            business_id=basket.business_id,
            user_id=payment.user_id,
            payment_method=payment.payment_method.value,
            source=payment.source.value,
            status=payment.status.value,
            created=payment.created,
        )

        return PaymentIntegrationEvent(
            event_id=str(event.event_id),
            event_type=IntegrationEventType.PAYMENT_SUCCEEDED,
            event_version=event.event_version(),
            created=event.occurred_at,
            data=event_data,
        )


class PaymentFailedIntegrationEventBuilder(PaymentIntegrationEventBuilder):
    def build(
        self,
        event: BasketPaymentFailed,
        basket: Basket,
    ) -> PaymentIntegrationEvent | None:
        payment = basket.latest_basket_payment
        if not payment:
            return None

        event_data = PaymentFailedEventData(
            basket_id=str(basket.basket_id),
            basket_payment_id=str(payment.basket_payment_id),
            balance_transaction_id=(
                str(payment.balance_transaction_id) if payment.balance_transaction_id else None
            ),
            business_id=basket.business_id,
            user_id=payment.user_id,
            payment_method=payment.payment_method.value,
            source=payment.source.value,
            status=payment.status.value,
            created=payment.created,
            error_code=event.error_code,
            error_message=event.error_message,
        )

        return PaymentIntegrationEvent(
            event_id=str(event.event_id),
            event_type=IntegrationEventType.PAYMENT_FAILED,
            event_version=event.event_version(),
            created=event.occurred_at,
            data=event_data,
        )


class PaymentActionRequiredIntegrationEventBuilder(PaymentIntegrationEventBuilder):
    def build(
        self,
        event: BasketPaymentActionRequired,
        basket: Basket,
    ) -> PaymentIntegrationEvent | None:
        payment = basket.latest_basket_payment
        if not payment:
            return None

        event_data = PaymentActionRequiredEventData(
            basket_id=str(basket.basket_id),
            basket_payment_id=str(payment.basket_payment_id),
            balance_transaction_id=(
                str(payment.balance_transaction_id) if payment.balance_transaction_id else None
            ),
            business_id=basket.business_id,
            user_id=payment.user_id,
            payment_method=payment.payment_method.value,
            source=payment.source.value,
            status=payment.status.value,
            created=payment.created,
            action_required_details=event.action_required_details,
        )

        return PaymentIntegrationEvent(
            event_id=str(event.event_id),
            event_type=IntegrationEventType.PAYMENT_ACTION_REQUIRED,
            event_version=event.event_version(),
            created=event.occurred_at,
            data=event_data,
        )


class PaymentCanceledIntegrationEventBuilder(PaymentIntegrationEventBuilder):
    def build(
        self,
        event: BasketPaymentCanceled,
        basket: Basket,
    ) -> PaymentIntegrationEvent | None:
        payment = basket.latest_basket_payment
        if not payment:
            return None

        event_data = PaymentCanceledEventData(
            basket_id=str(basket.basket_id),
            basket_payment_id=str(payment.basket_payment_id),
            balance_transaction_id=(
                str(payment.balance_transaction_id) if payment.balance_transaction_id else None
            ),
            business_id=basket.business_id,
            user_id=payment.user_id,
            payment_method=payment.payment_method.value,
            source=payment.source.value,
            status=payment.status.value,
            created=payment.created,
        )

        return PaymentIntegrationEvent(
            event_id=str(event.event_id),
            event_type=IntegrationEventType.PAYMENT_CANCELED,
            event_version=event.event_version(),
            created=event.occurred_at,
            data=event_data,
        )
