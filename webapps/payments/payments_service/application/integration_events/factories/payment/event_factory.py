import logging

from webapps.payments.payments_service.application.integration_events.events.payment import (
    PaymentIntegrationEvent,
    PaymentIntegrationEventContext,
)
from webapps.payments.payments_service.application.integration_events.factories.base import (
    AbstractIntegrationEventFactory,
)
from webapps.payments.payments_service.application.integration_events.factories.payment.event_builders import (  # pylint: disable=line-too-long
    PaymentProcessIntegrationEventBuilder,
    PaymentSucceededIntegrationEventBuilder,
    PaymentFailedIntegrationEventBuilder,
    PaymentActionRequiredIntegrationEventBuilder,
    PaymentCanceledIntegrationEventBuilder,
)
from webapps.payments.payments_service.domain.events.base import DomainEvent
from webapps.payments.payments_service.domain.events.basket_payment import (
    BasketPaymentInitiated,
    BasketPaymentSucceeded,
    BasketPaymentFailed,
    BasketPaymentActionRequired,
    BasketPaymentCanceled,
)


logger = logging.getLogger(__name__)


class PaymentIntegrationEventFactory(AbstractIntegrationEventFactory):
    """
    This class translates domain events into integration events for payment operations.

    Important note:
    PaymentIntegrationEvent represents an integration event (public/external)
    built based on a domain event (private/internal).
    """

    def __init__(self):
        self._builders = {
            BasketPaymentInitiated: PaymentProcessIntegrationEventBuilder(),
            BasketPaymentSucceeded: PaymentSucceededIntegrationEventBuilder(),
            BasketPaymentFailed: PaymentFailedIntegrationEventBuilder(),
            BasketPaymentActionRequired: PaymentActionRequiredIntegrationEventBuilder(),
            BasketPaymentCanceled: PaymentCanceledIntegrationEventBuilder(),
        }

    def create_integration_event(
        self, event: DomainEvent, context: PaymentIntegrationEventContext
    ) -> PaymentIntegrationEvent | None:
        event_class = type(event)
        builder = self._builders.get(event_class)

        if not builder:
            logger.warning('No message builder found for event type: %s', event_class.__name__)
            return None

        basket = context.basket

        if not basket:
            logger.error(
                "Required 'basket' not found in EventContext for event %s",
                event_class.__name__,
            )
            return None

        return builder.build(event, basket)
