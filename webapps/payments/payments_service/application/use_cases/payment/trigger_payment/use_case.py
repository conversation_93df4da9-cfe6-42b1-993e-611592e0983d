import logging

from lib.point_of_sale.enums import BasketPaymentStatus

from webapps.payments.payments_service.application.use_cases.payment.trigger_payment.dto import (
    TriggerPaymentInputDTO,
    TriggerPaymentOutputDTO,
)
from webapps.payments.payments_service.application.ports import PaymentsDataProvider
from webapps.payments.payments_service.domain.events.base import DomainEventRegistry
from webapps.payments.payments_service.domain.services.payment_event_domain_service import (
    PaymentEventDomainService,
)

logger = logging.getLogger(__name__)


class TriggerPaymentUseCase:

    def __init__(
        self,
        domain_service: PaymentEventDomainService,
        payments_data_provider: PaymentsDataProvider,
    ):
        self._domain_service = domain_service
        self._payments_data_provider = payments_data_provider

    def execute(  # pylint: disable=too-many-return-statements
        self,
        request: TriggerPaymentInputDTO,
        event_registry: DomainEventRegistry | None = None,
    ) -> TriggerPaymentOutputDTO:
        basket_id = request.basket_id
        basket = self._payments_data_provider.get_basket(basket_id)

        # XOR - allow either card ID or token - not both
        if bool(request.tokenized_pm_id) == bool(request.token):
            return TriggerPaymentOutputDTO(
                success=False,
                error_message=f'Provided both card ID and token for Basket with ID {basket_id}',
            )

        if not basket or not basket.latest_basket_payment:
            # Note - a basket payment is expected to be initialized already on this step (pending)
            return TriggerPaymentOutputDTO(
                success=False,
                error_message=f'Basket with ID {basket_id} not found or has no payment.',
            )

        if basket.latest_basket_payment.status != BasketPaymentStatus.PENDING:
            return TriggerPaymentOutputDTO(
                success=False,
                error_message=(
                    f'The latest Basket Payment of Basket with ID {basket_id} '
                    'is not in a pending status.'
                ),
            )

        domain_event = self._domain_service.create_basket_payment_initiated_event(
            basket=basket,
            payment_method=request.payment_method,
            tokenized_pm_id=request.tokenized_pm_id,
            token=request.token,
            tip_type=request.tip_type,
            tip_value=request.tip_value,
        )

        if event_registry and domain_event:
            event_registry.add_event(domain_event)
            return TriggerPaymentOutputDTO(success=True, basket=basket)

        return TriggerPaymentOutputDTO(
            success=False,
            basket=basket,
            error_message='No events created for trigger payment use case.',
        )
