from dataclasses import dataclass
from uuid import UUID

from lib.point_of_sale.enums import BasketTipType, PaymentMethodType
from webapps.payments.payments_service.domain.entities.basket import Basket


@dataclass(frozen=True)
class TriggerPaymentInputDTO:
    basket_id: UUID
    payment_method: PaymentMethodType
    tokenized_pm_id: UUID | None = None
    token: str | None = None
    tip_type: BasketTipType | None = None
    tip_value: int | None = None


@dataclass(frozen=True)
class TriggerPaymentOutputDTO:
    success: bool
    basket: Basket | None = None
    error_message: str | None = None
