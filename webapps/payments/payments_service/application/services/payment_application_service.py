import logging
from uuid import UUID

from lib.point_of_sale.enums import BasketTipType, PaymentMethodType
from webapps.payments.payments_service.application.integration_events import (
    PaymentIntegrationEventContext,
)
from webapps.payments.payments_service.application.integration_events.enums import Consumer
from webapps.payments.payments_service.application.ports.event_publisher import (
    PublicationResult,
)
from webapps.payments.payments_service.application.services.event_dispatcher import (
    EventDispatcher,
)
from webapps.payments.payments_service.application.use_cases import (
    PaymentProcessedInputDTO,
    PaymentProcessedUseCase,
    ProcessPaymentUseCase,
    TriggerPaymentInputDTO,
    TriggerPaymentUseCase,
)
from webapps.payments.payments_service.domain.events.base import DomainEventRegistry

logger = logging.getLogger(__name__)


class PaymentApplicationService:
    """
    Application service to orchestrate payment-related use cases.

    This service coordinates the execution of use cases and the dispatching
    of resulting domain events.
    """

    def __init__(
        self,
        trigger_payment_use_case: TriggerPaymentUseCase,
        process_payment_use_case: ProcessPaymentUseCase,
        payment_processed_use_case: PaymentProcessedUseCase,
        event_dispatcher: EventDispatcher,
    ):
        self._trigger_payment_use_case = trigger_payment_use_case
        self._process_payment_use_case = process_payment_use_case
        self._payment_processed_use_case = payment_processed_use_case
        self._event_dispatcher = event_dispatcher

    def handle_trigger_payment(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        self,
        basket_id: UUID,
        payment_method: PaymentMethodType,
        tokenized_pm_id: UUID | None = None,
        token: str | None = None,
        tip_type: BasketTipType | None = None,
        tip_value: int | None = None,
    ) -> list[PublicationResult]:
        event_registry = DomainEventRegistry()
        input_dto = TriggerPaymentInputDTO(
            basket_id=basket_id,
            payment_method=payment_method,
            tokenized_pm_id=tokenized_pm_id,
            token=token,
            tip_type=tip_type,
            tip_value=tip_value,
        )
        use_case_result = self._trigger_payment_use_case.execute(input_dto, event_registry)

        if not use_case_result.success:
            logger.error(
                'TriggerPaymentUseCase failed for basket %s: %s',
                basket_id,
                use_case_result.error_message,
            )
            return [PublicationResult(success=False, error=use_case_result.error_message)]

        context = PaymentIntegrationEventContext(
            basket=use_case_result.basket, consumers=[Consumer.PAYMENTS_SERVICE]
        )

        return self._event_dispatcher.dispatch(event_registry, context)

    def handle_process_payment(self):
        """Placeholder for handling the process_payment use case."""
        # To be implemented
        logger.info('`handle_process_payment` is not yet implemented.')

    def handle_payment_processed(
        self, basket_id: UUID, consumers: list[Consumer] | None = None
    ) -> list[PublicationResult]:
        event_registry = DomainEventRegistry()

        input_dto = PaymentProcessedInputDTO(basket_id=basket_id, consumers=consumers)
        use_case_result = self._payment_processed_use_case.execute(
            request=input_dto, event_registry=event_registry
        )

        if not use_case_result.success:
            logger.error(
                'PaymentProcessedUseCase failed for basket %s: %s',
                basket_id,
                use_case_result.error_message,
            )
            return [PublicationResult(success=False, error=use_case_result.error_message)]

        context = PaymentIntegrationEventContext(basket=use_case_result.basket, consumers=consumers)

        return self._event_dispatcher.dispatch(event_registry, context)
