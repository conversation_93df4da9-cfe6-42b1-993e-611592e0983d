import logging

from webapps.payments.payments_service.application.integration_events.enums import Producer
from webapps.payments.payments_service.application.integration_events.events.base import (
    IntegrationEventContext,
    IntegrationEventAttributes,
)
from webapps.payments.payments_service.application.integration_events.factories.base import (
    AbstractIntegrationEventFactory,
)
from webapps.payments.payments_service.application.ports.event_publisher import (
    EventPublisher,
    PublicationResult,
)
from webapps.payments.payments_service.domain.events.base import DomainEventRegistry


logger = logging.getLogger(__name__)


class EventDispatcher:
    """
    A generic service responsible for dispatching events from a registry.

    It orchestrates the process of getting domain events from a registry, creating
    integration events from them via a factory, publishing them, and clearing the registry.
    """

    def __init__(
        self,
        event_publisher: EventPublisher,
        integration_event_factory: AbstractIntegrationEventFactory,
    ):
        self._event_publisher = event_publisher
        self._integration_event_factory = integration_event_factory

    def dispatch(
        self, registry: DomainEventRegistry, context: IntegrationEventContext
    ) -> list[PublicationResult]:
        """
        Dispatch all domain events from the provided registry.
        """
        results = []
        domain_events = registry.get_events()

        if not domain_events:
            logger.info('No domain events to dispatch')
            registry.clear_events()
            return results

        logger.info('Dispatching %s domain events', len(domain_events))

        for domain_event in domain_events:
            integration_event = self._integration_event_factory.create_integration_event(
                domain_event, context
            )
            if not integration_event:
                logger.warning(
                    'Integration event creation failed for event %s, skipping dispatch.',
                    domain_event.event_id,
                )
                continue

            integration_event_attributes = IntegrationEventAttributes(
                producer=Producer.PAYMENTS_SERVICE,
                consumers=[c.value for c in getattr(context, 'consumers', []) or []],
            )
            result = self._event_publisher.publish_event(
                integration_event, integration_event_attributes
            )
            results.append(result)

            if result.success:
                logger.info('Successfully dispatched event %s', domain_event.event_id)
            else:
                logger.error(
                    'Failed to publish event %s (id: %s): %s',
                    domain_event.event_name(),
                    domain_event.event_id,
                    result.error,
                )

        registry.clear_events()
        logger.info('Cleared domain event registry')
        return results
