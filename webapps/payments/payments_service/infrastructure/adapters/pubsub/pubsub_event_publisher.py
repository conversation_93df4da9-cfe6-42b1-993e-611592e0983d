import logging
from dataclasses import dataclass

from webapps.payments.payments_service.application.integration_events import PaymentIntegrationEvent
from webapps.payments.payments_service.application.integration_events.events.base import (
    IntegrationEventAttributes,
)
from webapps.payments.payments_service.application.ports.event_publisher import (
    EventPublisher,
    PublicationResult,
)
from webapps.payments.payments_service.infrastructure.adapters.pubsub.messages import (
    PAYMENT_EVENT_MESSAGE_REGISTRY,
)

logger = logging.getLogger(__name__)


@dataclass(frozen=True)
class Publishable:
    event: PaymentIntegrationEvent
    attributes: IntegrationEventAttributes


class PubSubPaymentEventPublisher(EventPublisher):
    """
    Google Pub/Sub implementation of EventPublisher.

    Uses dedicated Message classes with protobuf encoding for Payments Service.
    """

    def publish_event(
        self,
        integration_event: PaymentIntegrationEvent,
        integration_event_attributes: IntegrationEventAttributes,
    ) -> PublicationResult:
        message_class = PAYMENT_EVENT_MESSAGE_REGISTRY.get(integration_event.event_type)

        if not message_class:
            error_msg = f'No Message class found for event_type: {integration_event.event_type}'
            logger.error(error_msg)
            return PublicationResult(success=False, error=error_msg)

        pubsub_message = message_class(
            Publishable(event=integration_event, attributes=integration_event_attributes)
        )
        pubsub_message.publish(on_commit=False, blocking=True)

        logger.info(
            'Successfully published event %s with id: %s',
            integration_event.event_type,
            integration_event.event_id,
        )
        return PublicationResult(success=True, event_id=integration_event.event_id)
