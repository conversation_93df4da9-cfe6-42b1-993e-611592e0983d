# pylint: disable=no-name-in-module
from django.conf import settings
from django_socio_grpc.proto_serializers import ProtoSerializer
from rest_framework import serializers

from lib.protobuf.fields import ProtobufDateTimeField

from booksy.pubsub.payments.payments_service.payment_process_event_pb2 import (  # pylint: disable=no-name-in-module
    PaymentProcessEvent,
)
from booksy.pubsub.payments.payments_service.payment_succeeded_event_pb2 import (  # pylint: disable=no-name-in-module
    PaymentSucceededEvent,
)
from booksy.pubsub.payments.payments_service.payment_failed_event_pb2 import (  # pylint: disable=no-name-in-module
    PaymentFailedEvent,
)
from booksy.pubsub.payments.payments_service.payment_action_required_event_pb2 import (  # pylint: disable=no-name-in-module
    PaymentActionRequiredEvent,
)
from booksy.pubsub.payments.payments_service.payment_canceled_event_pb2 import (  # pylint: disable=no-name-in-module
    PaymentCanceledEvent,
)
from webapps.pubsub.base import TopicOptions
from webapps.pubsub.message import Message


class TipSerializer(ProtoSerializer):
    class Meta:
        proto_class = PaymentProcessEvent.Tip

    type = serializers.CharField()
    value = serializers.IntegerField()


class PaymentMethodSerializer(ProtoSerializer):
    class Meta:
        proto_class = PaymentProcessEvent.PaymentMethod

    type = serializers.CharField()
    card_id = serializers.IntegerField(required=False)
    token = serializers.CharField(required=False)


class PaymentProcessEventDataSerializer(ProtoSerializer):
    class Meta:
        proto_class = PaymentProcessEvent.PaymentProcessEventData

    basket_id = serializers.CharField()
    payment_method = PaymentMethodSerializer()
    tip = TipSerializer(allow_null=True, required=False)


class BasePaymentDataSerializer(ProtoSerializer):
    basket_id = serializers.CharField()
    basket_payment_id = serializers.CharField()
    balance_transaction_id = serializers.CharField(allow_null=True, required=False)
    business_id = serializers.IntegerField()
    user_id = serializers.IntegerField()
    payment_method = serializers.CharField()
    source = serializers.CharField()
    status = serializers.CharField()
    created = ProtobufDateTimeField()


class PaymentSucceededEventDataSerializer(BasePaymentDataSerializer):
    class Meta:
        proto_class = PaymentSucceededEvent.PaymentSucceededEventData


class PaymentFailedEventDataSerializer(BasePaymentDataSerializer):
    class Meta:
        proto_class = PaymentFailedEvent.PaymentFailedEventData

    error_code = serializers.CharField(allow_null=True, required=False)
    error_message = serializers.CharField(allow_null=True, required=False)


class PaymentActionRequiredEventDataSerializer(BasePaymentDataSerializer):
    class Meta:
        proto_class = PaymentActionRequiredEvent.PaymentActionRequiredEventData

    action_required_details = serializers.CharField(allow_null=True, required=False)


class PaymentCanceledEventDataSerializer(BasePaymentDataSerializer):
    class Meta:
        proto_class = PaymentCanceledEvent.PaymentCanceledEventData


class PaymentEventAttributesSerializer(serializers.Serializer):
    """Serializer for Pub/Sub message attributes.

    Moves "producer" and "consumers" to Pub/Sub attributes so that they do not
    need to live in the message payload. Values must be strings due to Pub/Sub
    constraints, therefore the consumer list is joined as a comma-separated string.
    """

    producer = serializers.CharField(required=False, allow_blank=True)

    def to_representation(self, instance):
        producer = instance.producer
        consumers = instance.consumers

        attrs: dict[str, str] = {}
        if producer:
            attrs['producer'] = producer

        for consumer in consumers:
            attrs[f'consumers__{consumer}'] = 'true'

        return attrs


class PaymentProcessEventSerializer(ProtoSerializer):
    class Meta:
        proto_class = PaymentProcessEvent

    event_id = serializers.CharField()
    event_type = serializers.CharField()
    event_version = serializers.CharField()
    created = ProtobufDateTimeField()
    data = PaymentProcessEventDataSerializer()


class PaymentSucceededEventSerializer(ProtoSerializer):
    class Meta:
        proto_class = PaymentSucceededEvent

    event_id = serializers.CharField()
    event_type = serializers.CharField()
    event_version = serializers.CharField()
    created = ProtobufDateTimeField()
    data = PaymentSucceededEventDataSerializer()


class PaymentFailedEventSerializer(ProtoSerializer):
    class Meta:
        proto_class = PaymentFailedEvent

    event_id = serializers.CharField()
    event_type = serializers.CharField()
    event_version = serializers.CharField()
    created = ProtobufDateTimeField()
    data = PaymentFailedEventDataSerializer()


class PaymentActionRequiredEventSerializer(ProtoSerializer):
    class Meta:
        proto_class = PaymentActionRequiredEvent

    event_id = serializers.CharField()
    event_type = serializers.CharField()
    event_version = serializers.CharField()
    created = ProtobufDateTimeField()
    data = PaymentActionRequiredEventDataSerializer()


class PaymentCanceledEventSerializer(ProtoSerializer):
    class Meta:
        proto_class = PaymentCanceledEvent

    event_id = serializers.CharField()
    event_type = serializers.CharField()
    event_version = serializers.CharField()
    created = ProtobufDateTimeField()
    data = PaymentCanceledEventDataSerializer()


class BasePubSubMessage(Message):
    topic_options = TopicOptions(
        by_country=True,
        project_id=settings.PAYMENTS_SERVICE_GC_PROJECT_ID,
        service_name='payments_service',
    )
    attributes_serializer = PaymentEventAttributesSerializer

    def get_message(self, instance, context=None):
        return super().get_message(instance.event, context)

    def get_message_attributes(self, instance, context=None):
        return super().get_message_attributes(instance.attributes, context)


class PaymentProcessPubSubMessage(BasePubSubMessage):
    topic_proto_class = PaymentProcessEvent
    serializer = PaymentProcessEventSerializer


class PaymentSucceededPubSubMessage(BasePubSubMessage):
    topic_proto_class = PaymentSucceededEvent
    serializer = PaymentSucceededEventSerializer


class PaymentFailedPubSubMessage(BasePubSubMessage):
    topic_proto_class = PaymentFailedEvent
    serializer = PaymentFailedEventSerializer


class PaymentActionRequiredPubSubMessage(BasePubSubMessage):
    topic_proto_class = PaymentActionRequiredEvent
    serializer = PaymentActionRequiredEventSerializer


class PaymentCanceledPubSubMessage(BasePubSubMessage):
    topic_proto_class = PaymentCanceledEvent
    serializer = PaymentCanceledEventSerializer


PAYMENT_EVENT_MESSAGE_REGISTRY = {
    'payment.process': PaymentProcessPubSubMessage,
    'payment.succeeded': PaymentSucceededPubSubMessage,
    'payment.failed': PaymentFailedPubSubMessage,
    'payment.action_required': PaymentActionRequiredPubSubMessage,
    'payment.canceled': PaymentCanceledPubSubMessage,
}
