import logging
from uuid import UUID

from lib.point_of_sale.enums import (
    BasketTipType,
    PaymentMethodType,
)
from webapps.payments.payments_service.application.integration_events import (
    PaymentIntegrationEventFactory,
)
from webapps.payments.payments_service.application.integration_events.enums import Consumer
from webapps.payments.payments_service.application.services import (
    EventDispatcher,
    PaymentApplicationService,
)
from webapps.payments.payments_service.application.use_cases import (
    PaymentProcessedUseCase,
    ProcessPaymentUseCase,
    TriggerPaymentUseCase,
)
from webapps.payments.payments_service.domain.services import PaymentEventDomainService
from webapps.payments.payments_service.infrastructure.adapters import (
    PaymentsDataAdapter,
    PubSubPaymentEventPublisher,
)
from webapps.point_of_sale.ports import BasketPort, BasketPaymentPort

logger = logging.getLogger(__name__)


class PaymentsServiceContainer:
    """
    Container for dependency injection in the payments service.

    This class manages the creation and configuration of all services
    and their dependencies in a centralized way.
    """

    def __init__(self):
        # External dependencies
        self._event_publisher = None
        self._payments_data_provider = None

        # Pub/Sub related interfaces
        self._integration_event_factory = None
        self._event_dispatcher = None

        # Payment processing related dependencies
        self._payment_event_domain_service = None
        self._payment_application_service = None
        self._payment_processed_use_case = None
        self._trigger_payment_use_case = None
        self._process_payment_use_case = None

    def get_event_publisher(self) -> PubSubPaymentEventPublisher:
        if self._event_publisher is None:
            # Project ID is configured in TopicOptions in Message classes
            self._event_publisher = PubSubPaymentEventPublisher()
        return self._event_publisher

    def get_payments_data_provider(self) -> PaymentsDataAdapter:
        if self._payments_data_provider is None:
            self._payments_data_provider = PaymentsDataAdapter(
                basket_port=BasketPort(),
                basket_payment_port=BasketPaymentPort(),
            )
        return self._payments_data_provider

    def get_integration_event_factory(self) -> PaymentIntegrationEventFactory:
        if self._integration_event_factory is None:
            self._integration_event_factory = PaymentIntegrationEventFactory()
        return self._integration_event_factory

    def get_event_dispatcher(self) -> EventDispatcher:
        if self._event_dispatcher is None:
            self._event_dispatcher = EventDispatcher(
                event_publisher=self.get_event_publisher(),
                integration_event_factory=self.get_integration_event_factory(),
            )
        return self._event_dispatcher

    def get_payment_processed_use_case(self) -> PaymentProcessedUseCase:
        if self._payment_processed_use_case is None:
            self._payment_processed_use_case = PaymentProcessedUseCase(
                domain_service=self.get_payment_event_domain_service(),
                payments_data_provider=self.get_payments_data_provider(),
            )
        return self._payment_processed_use_case

    def get_trigger_payment_use_case(self) -> TriggerPaymentUseCase:
        if self._trigger_payment_use_case is None:
            self._trigger_payment_use_case = TriggerPaymentUseCase(
                domain_service=self.get_payment_event_domain_service(),
                payments_data_provider=self.get_payments_data_provider(),
            )
        return self._trigger_payment_use_case

    def get_process_payment_use_case(self) -> ProcessPaymentUseCase:
        if self._process_payment_use_case is None:
            self._process_payment_use_case = ProcessPaymentUseCase()
        return self._process_payment_use_case

    def get_payment_application_service(self) -> PaymentApplicationService:
        if self._payment_application_service is None:
            self._payment_application_service = PaymentApplicationService(
                trigger_payment_use_case=self.get_trigger_payment_use_case(),
                process_payment_use_case=self.get_process_payment_use_case(),
                payment_processed_use_case=self.get_payment_processed_use_case(),
                event_dispatcher=self.get_event_dispatcher(),
            )
        return self._payment_application_service

    def get_payment_event_domain_service(self) -> PaymentEventDomainService:
        if self._payment_event_domain_service is None:
            self._payment_event_domain_service = PaymentEventDomainService()
        return self._payment_event_domain_service


container = PaymentsServiceContainer()


def handle_trigger_payment(  # pylint: disable=too-many-arguments, too-many-positional-arguments
    basket_id: UUID,
    payment_method: PaymentMethodType,
    tokenized_pm_id: UUID | None = None,
    token: str | None = None,
    tip_type: BasketTipType | None = None,
    tip_value: int | None = None,
) -> bool:
    """
    Main integration function to trigger payment.
    """
    payment_application_service = container.get_payment_application_service()
    results = payment_application_service.handle_trigger_payment(
        basket_id=basket_id,
        payment_method=payment_method,
        tokenized_pm_id=tokenized_pm_id,
        token=token,
        tip_type=tip_type,
        tip_value=tip_value,
    )
    all_successful = all(result.success for result in results)

    if not all_successful:
        errors = [result.error for result in results if result.error]
        logger.error(
            'Failed to handle handle_trigger_payment for basket %s: %s',
            basket_id,
            errors,
        )
        return False

    event_ids = [result.event_id for result in results if result.event_id]
    logger.info(
        'Successfully handled handle_trigger_payment for basket %s, event_ids: %s',
        basket_id,
        event_ids,
    )
    return True


def handle_payment_processed(basket_id: UUID, consumers: list[str] | None = None) -> bool:
    """
    This is the main integration function that external services should use
    to trigger payment processed workflow.

    Note - for now, Booking should be the only consumer.
    """
    if consumers is None:
        consumers = [Consumer.BOOKING]

    # Anti-Corruption Layer: Convert string consumers to Consumer enums.
    consumer_enums = [Consumer(c) for c in consumers if c in [item.value for item in Consumer]]
    payment_application_service = container.get_payment_application_service()
    results = payment_application_service.handle_payment_processed(
        basket_id=basket_id, consumers=consumer_enums
    )

    all_successful = all(result.success for result in results)

    if not all_successful:
        errors = [result.error for result in results if result.error]
        logger.error(
            'Failed to handle handle_payment_processed for basket %s: %s',
            basket_id,
            errors,
        )
        return False

    event_ids = [result.event_id for result in results if result.event_id]
    logger.info(
        'Successfully handled handle_payment_processed for basket %s, event_ids: %s',
        basket_id,
        event_ids,
    )
    return True
