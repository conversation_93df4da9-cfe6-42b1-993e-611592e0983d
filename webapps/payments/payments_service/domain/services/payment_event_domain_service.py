from uuid import UUID
from lib.point_of_sale.enums import (
    BasketPaymentStatus,
    BasketTipType,
    PaymentMethodType,
)
from webapps.payments.payments_service.domain.entities.basket import Basket
from webapps.payments.payments_service.domain.events.basket_payment import (
    BasketPaymentInitiated,
    BasketPaymentSucceeded,
    BasketPaymentFailed,
    BasketPaymentActionRequired,
    BasketPaymentCanceled,
)


class PaymentEventDomainService:
    """
    Domain service for handling payment events logic.

    This service is responsible for creating lightweight domain events
    that represent facts about what happened in the payment domain.
    """

    # pylint: disable=too-many-arguments, too-many-positional-arguments
    def create_basket_payment_initiated_event(
        self,
        basket: Basket,
        payment_method: PaymentMethodType,
        tokenized_pm_id: UUID | None = None,
        token: str | None = None,
        tip_type: BasketTipType | None = None,
        tip_value: int | None = None,
    ) -> BasketPaymentInitiated:
        return BasketPaymentInitiated(
            basket_id=basket.basket_id,
            payment_method=payment_method,
            tokenized_pm_id=tokenized_pm_id,
            token=token,
            tip_type=tip_type,
            tip_value=tip_value,
        )

    def create_basket_payment_succeeded_event(
        self, basket: Basket
    ) -> BasketPaymentSucceeded | None:
        if (
            basket.latest_basket_payment
            and basket.latest_basket_payment.status == BasketPaymentStatus.SUCCESS
        ):
            return BasketPaymentSucceeded(basket_id=basket.basket_id)

        return None

    def create_basket_payment_failed_event(self, basket: Basket) -> BasketPaymentFailed | None:
        if (
            basket.latest_basket_payment
            and basket.latest_basket_payment.status == BasketPaymentStatus.FAILED
        ):
            return BasketPaymentFailed(
                basket_id=basket.basket_id,
                error_code=basket.latest_basket_payment.error_code,
            )

        return None

    def create_basket_payment_action_required_event(
        self, basket: Basket
    ) -> BasketPaymentActionRequired | None:
        if (
            basket.latest_basket_payment
            and basket.latest_basket_payment.status == BasketPaymentStatus.ACTION_REQUIRED
        ):
            return BasketPaymentActionRequired(
                basket_id=basket.basket_id,
                action_required_details=basket.latest_basket_payment.action_required_details,
            )

        return None

    def create_basket_payment_canceled_event(self, basket: Basket) -> BasketPaymentCanceled | None:
        if (
            basket.latest_basket_payment
            and basket.latest_basket_payment.status == BasketPaymentStatus.CANCELED
        ):
            return BasketPaymentCanceled(
                basket_id=basket.basket_id,
            )
        return None
