from dataclasses import dataclass
from uuid import UUID

from lib.point_of_sale.enums import BasketTipType, PaymentMethodType
from webapps.payments.payments_service.domain.events.base import DomainEvent


@dataclass
class BasketPaymentInitiated(DomainEvent):
    basket_id: UUID

    payment_method: PaymentMethodType
    tokenized_pm_id: UUID | None = None
    token: str | None = None

    tip_type: BasketTipType | None = None
    tip_value: int | None = None

    def aggregate_id(self) -> UUID:
        return self.basket_id


@dataclass
class BasketPaymentSucceeded(DomainEvent):
    basket_id: UUID

    def aggregate_id(self) -> UUID:
        return self.basket_id


@dataclass
class BasketPaymentFailed(DomainEvent):
    basket_id: UUID
    error_code: str | None = None
    error_message: str | None = None

    def aggregate_id(self) -> UUID:
        return self.basket_id


@dataclass
class BasketPaymentActionRequired(DomainEvent):
    basket_id: UUID
    action_required_details: dict | None = None

    def aggregate_id(self) -> UUID:
        return self.basket_id


@dataclass
class BasketPaymentCanceled(DomainEvent):
    basket_id: UUID

    def aggregate_id(self) -> UUID:
        return self.basket_id
