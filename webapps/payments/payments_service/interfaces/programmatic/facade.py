from uuid import UUID

from lib.point_of_sale.enums import BasketTipType, PaymentMethodType
from webapps.payments.payments_service.infrastructure.django.integration import (
    handle_trigger_payment,
)


# pylint: disable=too-many-arguments, too-many-positional-arguments
def payments_service_trigger_payment(
    basket_id: UUID,
    payment_method: PaymentMethodType,
    tokenized_pm_id: UUID | None = None,
    token: str | None = None,
    tip_type: BasketTipType | None = None,
    tip_value: int | None = None,
) -> bool:
    return handle_trigger_payment(
        basket_id=basket_id,
        payment_method=payment_method,
        tokenized_pm_id=tokenized_pm_id,
        token=token,
        tip_type=tip_type,
        tip_value=tip_value,
    )
