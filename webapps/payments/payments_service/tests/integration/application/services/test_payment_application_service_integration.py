# pylint: disable=redefined-outer-name
from unittest.mock import MagicMock, ANY
from uuid import uuid4

import pytest
from lib.point_of_sale.enums import (
    BasketPaymentStatus,
    PaymentMethodType,
)

from webapps.payments.payments_service.application.integration_events.enums import (
    Consumer,
    IntegrationEventType,
)
from webapps.payments.payments_service.application.integration_events.events.base import (
    IntegrationEventAttributes,
)
from webapps.payments.payments_service.application.integration_events.factories.payment import (
    PaymentIntegrationEventFactory,
)
from webapps.payments.payments_service.application.ports.event_publisher import (
    PublicationResult,
)
from webapps.payments.payments_service.application.services.event_dispatcher import (
    EventDispatcher,
)
from webapps.payments.payments_service.application.services.payment_application_service import (
    PaymentApplicationService,
)
from webapps.payments.payments_service.application.use_cases import (
    PaymentProcessedUseCase,
    TriggerPaymentUseCase,
)
from webapps.payments.payments_service.domain.services.payment_event_domain_service import (
    PaymentEventDomainService,
)


@pytest.fixture
def real_domain_service() -> PaymentEventDomainService:
    return PaymentEventDomainService()


@pytest.fixture
def payments_data_provider_mock() -> MagicMock:
    return MagicMock()


@pytest.fixture
def event_publisher_mock() -> MagicMock:
    publisher = MagicMock()
    publisher.publish_event.return_value = PublicationResult(success=True)
    return publisher


@pytest.fixture
def event_dispatcher(event_publisher_mock: MagicMock) -> EventDispatcher:
    return EventDispatcher(
        event_publisher=event_publisher_mock,
        integration_event_factory=PaymentIntegrationEventFactory(),
    )


@pytest.fixture
def application_service(
    real_domain_service: PaymentEventDomainService,
    payments_data_provider_mock: MagicMock,
    event_dispatcher: EventDispatcher,
) -> PaymentApplicationService:
    trigger_uc = TriggerPaymentUseCase(
        domain_service=real_domain_service,
        payments_data_provider=payments_data_provider_mock,
    )
    process_uc = MagicMock()  # placeholder use case
    processed_uc = PaymentProcessedUseCase(
        domain_service=real_domain_service,
        payments_data_provider=payments_data_provider_mock,
    )

    return PaymentApplicationService(
        trigger_payment_use_case=trigger_uc,
        process_payment_use_case=process_uc,
        payment_processed_use_case=processed_uc,
        event_dispatcher=event_dispatcher,
    )


def test_handle_trigger_payment_publishes_payment_process_event(
    application_service: PaymentApplicationService,
    payments_data_provider_mock: MagicMock,
    event_publisher_mock: MagicMock,
    basket_factory,
):
    basket = basket_factory(status=BasketPaymentStatus.PENDING)
    payments_data_provider_mock.get_basket.return_value = basket

    results = application_service.handle_trigger_payment(
        basket_id=basket.basket_id,
        payment_method=PaymentMethodType.CARD,
        tokenized_pm_id=uuid4(),
        tip_type=None,
        tip_value=None,
    )

    assert len(results) == 1
    assert results[0].success is True
    expected_attrs = IntegrationEventAttributes(
        producer='payments_service', consumers=['payments_service']
    )
    event_publisher_mock.publish_event.assert_called_once_with(ANY, expected_attrs)
    integration_event = event_publisher_mock.publish_event.call_args[0][0]
    assert integration_event.event_type == IntegrationEventType.PAYMENT_PROCESS
    assert integration_event.data.basket_id == str(basket.basket_id)
    assert integration_event.data.payment_method.type == PaymentMethodType.CARD.value


@pytest.mark.parametrize(
    'status,expected_type,extra_data',
    [
        (
            BasketPaymentStatus.SUCCESS,
            IntegrationEventType.PAYMENT_SUCCEEDED,
            {},
        ),
        (
            BasketPaymentStatus.FAILED,
            IntegrationEventType.PAYMENT_FAILED,
            {'error_code': 'connection_error'},
        ),
        (
            BasketPaymentStatus.ACTION_REQUIRED,
            IntegrationEventType.PAYMENT_ACTION_REQUIRED,
            {'action_required_details': {'client_secret': 'foobar'}},
        ),
        (
            BasketPaymentStatus.CANCELED,
            IntegrationEventType.PAYMENT_CANCELED,
            {},
        ),
    ],
)
def test_handle_payment_processed_publishes_expected_event(
    application_service: PaymentApplicationService,
    payments_data_provider_mock: MagicMock,
    event_publisher_mock: MagicMock,
    status: BasketPaymentStatus,
    expected_type: IntegrationEventType,
    extra_data: dict,
    basket_factory,
):
    basket = basket_factory(status=status, **extra_data)
    payments_data_provider_mock.get_basket.return_value = basket
    consumers = [Consumer.PAYMENTS_SERVICE]

    results = application_service.handle_payment_processed(
        basket_id=basket.basket_id,
        consumers=consumers,
    )

    assert len(results) == 1
    assert results[0].success is True
    expected_attrs = IntegrationEventAttributes(
        producer='payments_service', consumers=['payments_service']
    )
    event_publisher_mock.publish_event.assert_called_once_with(ANY, expected_attrs)
    integration_event = event_publisher_mock.publish_event.call_args[0][0]
    assert integration_event.event_type == expected_type
    assert integration_event.data.basket_id == str(basket.basket_id)

    if 'error_code' in extra_data:
        assert integration_event.data.error_code == extra_data['error_code']
    if 'action_required_details' in extra_data:
        assert (
            integration_event.data.action_required_details == extra_data['action_required_details']
        )


def test_handle_trigger_payment_does_not_publish_event_on_use_case_failure(
    application_service: PaymentApplicationService,
    payments_data_provider_mock: MagicMock,
    event_publisher_mock: MagicMock,
    basket_factory,
):
    # basket in SUCCESS status is invalid for trigger_payment
    basket = basket_factory(status=BasketPaymentStatus.SUCCESS)
    payments_data_provider_mock.get_basket.return_value = basket

    results = application_service.handle_trigger_payment(
        basket_id=basket.basket_id,
        payment_method=PaymentMethodType.CARD,
        tokenized_pm_id=uuid4(),
    )

    assert len(results) == 1
    assert results[0].success is False
    event_publisher_mock.publish_event.assert_not_called()


def test_handle_payment_processed_does_not_publish_event_on_use_case_failure(
    application_service: PaymentApplicationService,
    payments_data_provider_mock: MagicMock,
    event_publisher_mock: MagicMock,
):
    payments_data_provider_mock.get_basket.return_value = None

    results = application_service.handle_payment_processed(
        basket_id='some-non-existent-id',
        consumers=[],
    )

    assert len(results) == 1
    assert results[0].success is False
    event_publisher_mock.publish_event.assert_not_called()
