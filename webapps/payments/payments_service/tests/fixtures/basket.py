from uuid import uuid4
from datetime import datetime, timezone

import pytest
from lib.payments.enums import PaymentProviderCode
from lib.point_of_sale.enums import (
    BasketPaymentSource,
    BasketPaymentStatus,
    BasketPaymentType,
    BasketType,
    PaymentMethodType,
)

from webapps.payments.payments_service.domain.entities.basket import Basket
from webapps.payments.payments_service.domain.entities.basket_payment import BasketPayment


@pytest.fixture
def basket_factory():
    # pylint: disable=too-many-arguments
    def _create_basket(
        *,
        status: BasketPaymentStatus,
        payment_method: PaymentMethodType = PaymentMethodType.CARD,
        source: BasketPaymentSource = BasketPaymentSource.PAYMENT,
        error_code: str | None = None,
        action_required_details: dict | None = None,
        balance_transaction_id: str | None = None,
    ) -> Basket:
        basket_payment = BasketPayment(
            basket_payment_id=uuid4(),
            user_id=123,
            payment_method=payment_method,
            payment_provider_code=PaymentProviderCode.STRIPE,
            type=BasketPaymentType.PAYMENT,
            source=source,
            status=status,
            error_code=error_code,
            action_required_details=action_required_details,
            balance_transaction_id=balance_transaction_id,
            created=datetime.now(timezone.utc),
        )

        return Basket(
            basket_id=uuid4(),
            business_id=456,
            type=BasketType.PAYMENT,
            created=datetime.now(timezone.utc),
            latest_basket_payment=basket_payment,
        )

    return _create_basket
