from uuid import uuid4, UUID
from unittest.mock import patch

from webapps.payments.payments_service.interfaces.celery.tasks import (
    payments_service_payment_processed_task,
)


class TestPaymentProcessedTask:
    @patch('webapps.payments.payments_service.interfaces.celery.tasks.handle_payment_processed')
    def test_payments_service_payment_processed_task_calls_handler(
        self, mock_handle_payment_processed
    ):
        basket_id = uuid4()
        consumers = ['consumer1', 'consumer2']

        payments_service_payment_processed_task(basket_id=str(basket_id), consumers=consumers)

        mock_handle_payment_processed.assert_called_once_with(
            basket_id=basket_id,
            consumers=consumers,
        )

    @patch('webapps.payments.payments_service.interfaces.celery.tasks.handle_payment_processed')
    def test_payments_service_payment_processed_task_handles_uuid_conversion(
        self, mock_handle_payment_processed
    ):
        basket_id_str = '4df8fcec-108d-41ac-8170-2d5dbdf8cb87'
        basket_id_uuid = UUID(basket_id_str)

        payments_service_payment_processed_task(basket_id=basket_id_str, consumers=None)

        mock_handle_payment_processed.assert_called_once_with(
            basket_id=basket_id_uuid,
            consumers=None,
        )
