from unittest.mock import MagicMock
from uuid import uuid4

import pytest
from lib.point_of_sale.enums import BasketPaymentStatus, BasketTipType, PaymentMethodType

from webapps.payments.payments_service.application.integration_events.enums import Consumer
from webapps.payments.payments_service.application.ports.event_publisher import (
    PublicationResult,
)
from webapps.payments.payments_service.application.services.payment_application_service import (
    PaymentApplicationService,
)
from webapps.payments.payments_service.application.use_cases.payment.trigger_payment.dto import (
    TriggerPaymentInputDTO,
    TriggerPaymentOutputDTO,
)
from webapps.payments.payments_service.application.use_cases.payment.payment_processed.dto import (
    PaymentProcessedInputDTO,
    PaymentProcessedOutputDTO,
)


class TestPaymentApplicationService:

    @pytest.fixture
    def mock_trigger_payment_use_case(self):
        return MagicMock()

    @pytest.fixture
    def mock_process_payment_use_case(self):
        return MagicMock()

    @pytest.fixture
    def mock_payment_processed_use_case(self):
        return MagicMock()

    @pytest.fixture
    def mock_event_dispatcher(self):
        return MagicMock()

    @pytest.fixture
    def application_service(
        self,
        mock_trigger_payment_use_case,
        mock_process_payment_use_case,
        mock_payment_processed_use_case,
        mock_event_dispatcher,
    ):
        return PaymentApplicationService(
            trigger_payment_use_case=mock_trigger_payment_use_case,
            process_payment_use_case=mock_process_payment_use_case,
            payment_processed_use_case=mock_payment_processed_use_case,
            event_dispatcher=mock_event_dispatcher,
        )

    @pytest.fixture
    def basket_id(self):
        return uuid4()

    def test_handle_trigger_payment_success_with_tokenized_pm_id(
        self,
        application_service,
        basket_id,
        basket_factory,
        mock_trigger_payment_use_case,
        mock_event_dispatcher,
    ):
        # Arrange
        tokenized_pm_id = uuid4()
        payment_method = PaymentMethodType.CARD
        tip_type = BasketTipType.PERCENT
        tip_value = 15
        basket = basket_factory(status=BasketPaymentStatus.SUCCESS)
        success_output = TriggerPaymentOutputDTO(success=True, basket=basket)
        publication_results = [PublicationResult(success=True)]
        mock_trigger_payment_use_case.execute.return_value = success_output
        mock_event_dispatcher.dispatch.return_value = publication_results

        # Act
        results = application_service.handle_trigger_payment(
            basket_id=basket_id,
            payment_method=payment_method,
            tokenized_pm_id=tokenized_pm_id,
            tip_type=tip_type,
            tip_value=tip_value,
        )

        # Assert
        assert results == publication_results
        mock_trigger_payment_use_case.execute.assert_called_once()
        call_args = mock_trigger_payment_use_case.execute.call_args
        input_dto = call_args[0][0]
        assert isinstance(input_dto, TriggerPaymentInputDTO)
        assert input_dto.basket_id == basket_id
        assert input_dto.payment_method == payment_method
        assert input_dto.tokenized_pm_id == tokenized_pm_id
        assert input_dto.token is None
        assert input_dto.tip_type == tip_type
        assert input_dto.tip_value == tip_value
        mock_event_dispatcher.dispatch.assert_called_once()

    def test_handle_trigger_payment_success_with_token(
        self,
        application_service,
        basket_id,
        basket_factory,
        mock_trigger_payment_use_case,
        mock_event_dispatcher,
    ):
        # Arrange
        token = 'payment_token_123'
        payment_method = PaymentMethodType.CARD
        basket = basket_factory(status=BasketPaymentStatus.SUCCESS)
        success_output = TriggerPaymentOutputDTO(success=True, basket=basket)
        publication_results = [PublicationResult(success=True)]
        mock_trigger_payment_use_case.execute.return_value = success_output
        mock_event_dispatcher.dispatch.return_value = publication_results

        # Act
        results = application_service.handle_trigger_payment(
            basket_id=basket_id,
            payment_method=payment_method,
            token=token,
        )

        # Assert
        assert results == publication_results
        call_args = mock_trigger_payment_use_case.execute.call_args
        input_dto = call_args[0][0]
        assert input_dto.basket_id == basket_id
        assert input_dto.payment_method == payment_method
        assert input_dto.tokenized_pm_id is None
        assert input_dto.token == token

    def test_handle_trigger_payment_use_case_failure(
        self,
        application_service,
        basket_id,
        mock_trigger_payment_use_case,
        mock_event_dispatcher,
    ):
        # Arrange
        error_message = 'Basket with ID 123 not found or has no payment.'
        failure_output = TriggerPaymentOutputDTO(success=False, error_message=error_message)
        mock_trigger_payment_use_case.execute.return_value = failure_output

        # Act
        results = application_service.handle_trigger_payment(
            basket_id=basket_id,
            payment_method=PaymentMethodType.CARD,
            tokenized_pm_id=uuid4(),
        )

        # Assert
        assert len(results) == 1
        assert results[0].success is False
        assert results[0].error == error_message
        mock_event_dispatcher.dispatch.assert_not_called()

    def test_handle_payment_processed_success(
        self,
        application_service,
        basket_id,
        basket_factory,
        mock_payment_processed_use_case,
        mock_event_dispatcher,
    ):
        # Arrange
        consumers = [Consumer.PAYMENTS_SERVICE, Consumer.BOOKING]
        basket = basket_factory(status=BasketPaymentStatus.SUCCESS)
        success_output = PaymentProcessedOutputDTO(success=True, basket=basket)
        publication_results = [PublicationResult(success=True)]
        mock_payment_processed_use_case.execute.return_value = success_output
        mock_event_dispatcher.dispatch.return_value = publication_results

        # Act
        results = application_service.handle_payment_processed(
            basket_id=basket_id,
            consumers=consumers,
        )

        # Assert
        assert results == publication_results
        mock_payment_processed_use_case.execute.assert_called_once()
        request_dto = mock_payment_processed_use_case.execute.call_args[1]['request']
        assert isinstance(request_dto, PaymentProcessedInputDTO)
        assert request_dto.basket_id == basket_id
        assert request_dto.consumers == consumers
        mock_event_dispatcher.dispatch.assert_called_once()

    def test_handle_payment_processed_use_case_failure(
        self,
        application_service,
        basket_id,
        mock_payment_processed_use_case,
        mock_event_dispatcher,
    ):
        # Arrange
        error_message = 'Payment processing failed'
        failure_output = PaymentProcessedOutputDTO(success=False, error_message=error_message)
        mock_payment_processed_use_case.execute.return_value = failure_output

        # Act
        results = application_service.handle_payment_processed(basket_id=basket_id)

        # Assert
        assert len(results) == 1
        assert results[0].success is False
        assert results[0].error == error_message
        mock_event_dispatcher.dispatch.assert_not_called()

    def test_integration_event_context_creation_in_trigger_payment(
        self,
        application_service,
        basket_id,
        basket_factory,
        mock_trigger_payment_use_case,
        mock_event_dispatcher,
    ):
        # Arrange
        basket = basket_factory(status=BasketPaymentStatus.SUCCESS)
        success_output = TriggerPaymentOutputDTO(success=True, basket=basket)
        publication_results = [PublicationResult(success=True)]
        mock_trigger_payment_use_case.execute.return_value = success_output
        mock_event_dispatcher.dispatch.return_value = publication_results

        # Act
        application_service.handle_trigger_payment(
            basket_id=basket_id,
            payment_method=PaymentMethodType.CARD,
            tokenized_pm_id=uuid4(),
        )

        # Assert
        mock_event_dispatcher.dispatch.assert_called_once()
        context = mock_event_dispatcher.dispatch.call_args[0][1]
        assert context.basket == basket
        assert context.consumers == [Consumer.PAYMENTS_SERVICE]

    def test_integration_event_context_creation_in_payment_processed(
        self,
        application_service,
        basket_id,
        basket_factory,
        mock_payment_processed_use_case,
        mock_event_dispatcher,
    ):
        # Arrange
        consumers = [Consumer.BOOKING]
        basket = basket_factory(status=BasketPaymentStatus.SUCCESS)
        success_output = PaymentProcessedOutputDTO(success=True, basket=basket)
        publication_results = [PublicationResult(success=True)]

        mock_payment_processed_use_case.execute.return_value = success_output
        mock_event_dispatcher.dispatch.return_value = publication_results

        # Act
        application_service.handle_payment_processed(
            basket_id=basket_id,
            consumers=consumers,
        )

        # Assert
        mock_event_dispatcher.dispatch.assert_called_once()
        context = mock_event_dispatcher.dispatch.call_args[0][1]
        assert context.basket == basket
        assert context.consumers == consumers
