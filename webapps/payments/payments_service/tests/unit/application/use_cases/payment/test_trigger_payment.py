from unittest.mock import MagicMock
from uuid import uuid4

import pytest
from lib.point_of_sale.enums import BasketPaymentStatus, BasketTipType, PaymentMethodType

from webapps.payments.payments_service.application.use_cases import (
    TriggerPaymentInputDTO,
    TriggerPaymentUseCase,
)
from webapps.payments.payments_service.domain.events.base import DomainEventRegistry
from webapps.payments.payments_service.domain.entities.basket import Basket
from webapps.payments.payments_service.domain.entities.basket_payment import BasketPayment


class TestTriggerPaymentUseCase:

    @pytest.fixture
    def mock_domain_service(self):
        return MagicMock()

    @pytest.fixture
    def mock_payments_data_provider(self):
        return MagicMock()

    @pytest.fixture
    def use_case(self, mock_domain_service, mock_payments_data_provider):
        return TriggerPaymentUseCase(
            domain_service=mock_domain_service,
            payments_data_provider=mock_payments_data_provider,
        )

    @pytest.fixture
    def basket_id(self):
        return uuid4()

    @pytest.fixture
    def base_input_dto(self, basket_id):
        return TriggerPaymentInputDTO(
            basket_id=basket_id,
            payment_method=PaymentMethodType.CARD,
        )

    def test_execute_success_with_tokenized_pm_id(
        self,
        use_case,
        basket_id,
        basket_factory,
        mock_payments_data_provider,
        mock_domain_service,
    ):
        # Arrange
        tokenized_pm_id = uuid4()
        input_dto = TriggerPaymentInputDTO(
            basket_id=basket_id,
            payment_method=PaymentMethodType.CARD,
            tokenized_pm_id=tokenized_pm_id,
        )
        event_registry = DomainEventRegistry()
        mock_domain_event = MagicMock()
        basket = basket_factory(status=BasketPaymentStatus.PENDING)
        mock_payments_data_provider.get_basket.return_value = basket
        mock_domain_service.create_basket_payment_initiated_event.return_value = mock_domain_event

        # Act
        result = use_case.execute(input_dto, event_registry)

        # Assert
        assert result.success is True
        assert result.basket == basket
        assert result.error_message is None
        mock_payments_data_provider.get_basket.assert_called_once_with(basket_id)
        mock_domain_service.create_basket_payment_initiated_event.assert_called_once_with(
            basket=basket,
            payment_method=PaymentMethodType.CARD,
            tokenized_pm_id=tokenized_pm_id,
            token=None,
            tip_type=None,
            tip_value=None,
        )

    def test_execute_success_with_token(
        self,
        use_case,
        basket_id,
        basket_factory,
        mock_payments_data_provider,
        mock_domain_service,
    ):
        # Arrange
        token = 'payment_token_123'
        input_dto = TriggerPaymentInputDTO(
            basket_id=basket_id,
            payment_method=PaymentMethodType.CARD,
            token=token,
        )
        event_registry = DomainEventRegistry()
        mock_domain_event = MagicMock()
        basket = basket_factory(status=BasketPaymentStatus.PENDING)
        mock_payments_data_provider.get_basket.return_value = basket
        mock_domain_service.create_basket_payment_initiated_event.return_value = mock_domain_event

        # Act
        result = use_case.execute(input_dto, event_registry)

        # Assert
        assert result.success is True
        assert result.basket == basket
        assert result.error_message is None
        mock_domain_service.create_basket_payment_initiated_event.assert_called_once_with(
            basket=basket,
            payment_method=PaymentMethodType.CARD,
            tokenized_pm_id=None,
            token=token,
            tip_type=None,
            tip_value=None,
        )

    def test_execute_success_with_tip(
        self,
        use_case,
        basket_id,
        basket_factory,
        mock_payments_data_provider,
        mock_domain_service,
    ):
        # Arrange
        tokenized_pm_id = uuid4()
        input_dto = TriggerPaymentInputDTO(
            basket_id=basket_id,
            payment_method=PaymentMethodType.CARD,
            tokenized_pm_id=tokenized_pm_id,
            tip_type=BasketTipType.PERCENT,
            tip_value=15,
        )
        event_registry = DomainEventRegistry()
        mock_domain_event = MagicMock()
        basket = basket_factory(status=BasketPaymentStatus.PENDING)
        mock_payments_data_provider.get_basket.return_value = basket
        mock_domain_service.create_basket_payment_initiated_event.return_value = mock_domain_event

        # Act
        result = use_case.execute(input_dto, event_registry)

        # Assert
        assert result.success is True
        mock_domain_service.create_basket_payment_initiated_event.assert_called_once_with(
            basket=basket,
            payment_method=PaymentMethodType.CARD,
            tokenized_pm_id=tokenized_pm_id,
            token=None,
            tip_type=BasketTipType.PERCENT,
            tip_value=15,
        )

    @pytest.mark.parametrize(
        'tokenized_pm_id, token',
        [
            (uuid4(), 'token_123'),  # Both provided
            (None, None),  # Neither provided
        ],
    )
    def test_execute_fails_with_invalid_card_or_token(
        self, use_case, basket_id, tokenized_pm_id, token
    ):
        # Arrange
        input_dto = TriggerPaymentInputDTO(
            basket_id=basket_id,
            payment_method=PaymentMethodType.CARD,
            tokenized_pm_id=tokenized_pm_id,
            token=token,
        )

        # Act
        result = use_case.execute(input_dto)

        # Assert
        assert result.success is False
        assert (
            result.error_message
            == f'Provided both card ID and token for Basket with ID {basket_id}'
        )

    def test_execute_fails_when_basket_not_found(
        self, use_case, basket_id, mock_payments_data_provider
    ):
        # Arrange
        input_dto = TriggerPaymentInputDTO(
            basket_id=basket_id,
            payment_method=PaymentMethodType.CARD,
            tokenized_pm_id=uuid4(),
        )
        mock_payments_data_provider.get_basket.return_value = None

        # Act
        result = use_case.execute(input_dto)

        # Assert
        assert result.success is False
        assert result.error_message == f'Basket with ID {basket_id} not found or has no payment.'

    def test_execute_fails_when_basket_has_no_payment(
        self, use_case, basket_id, mock_payments_data_provider
    ):
        # Arrange
        input_dto = TriggerPaymentInputDTO(
            basket_id=basket_id,
            payment_method=PaymentMethodType.CARD,
            tokenized_pm_id=uuid4(),
        )
        basket = MagicMock(spec=Basket)
        basket.latest_basket_payment = None
        mock_payments_data_provider.get_basket.return_value = basket

        # Act
        result = use_case.execute(input_dto)

        # Assert
        assert result.success is False
        assert result.error_message == f'Basket with ID {basket_id} not found or has no payment.'

    def test_execute_fails_when_payment_not_pending(
        self, use_case, basket_id, mock_payments_data_provider
    ):
        # Arrange
        input_dto = TriggerPaymentInputDTO(
            basket_id=basket_id,
            payment_method=PaymentMethodType.CARD,
            tokenized_pm_id=uuid4(),
        )
        basket_payment = MagicMock(spec=BasketPayment)
        basket_payment.status = BasketPaymentStatus.SUCCESS
        basket = MagicMock(spec=Basket)
        basket.latest_basket_payment = basket_payment
        mock_payments_data_provider.get_basket.return_value = basket

        # Act
        result = use_case.execute(input_dto)

        # Assert
        assert result.success is False
        assert result.error_message == (
            f'The latest Basket Payment of Basket with ID {basket_id} is not in a pending status.'
        )

    def test_execute_fails_when_no_domain_event_created(
        self,
        use_case,
        basket_id,
        basket_factory,
        mock_payments_data_provider,
        mock_domain_service,
    ):
        # Arrange
        input_dto = TriggerPaymentInputDTO(
            basket_id=basket_id,
            payment_method=PaymentMethodType.CARD,
            tokenized_pm_id=uuid4(),
        )
        event_registry = DomainEventRegistry()
        basket = basket_factory(status=BasketPaymentStatus.PENDING)
        mock_payments_data_provider.get_basket.return_value = basket
        mock_domain_service.create_basket_payment_initiated_event.return_value = None

        # Act
        result = use_case.execute(input_dto, event_registry)

        # Assert
        assert result.success is False
        assert result.basket == basket
        assert result.error_message == 'No events created for trigger payment use case.'
