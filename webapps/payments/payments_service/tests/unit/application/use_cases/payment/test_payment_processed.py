from unittest.mock import MagicMock
from uuid import uuid4

import pytest
from lib.point_of_sale.enums import BasketPaymentStatus

from webapps.payments.payments_service.application.integration_events.enums import Consumer
from webapps.payments.payments_service.application.use_cases import (
    PaymentProcessedInputDTO,
    PaymentProcessedUseCase,
)
from webapps.payments.payments_service.domain.events.base import DomainEventRegistry
from webapps.payments.payments_service.domain.entities.basket import Basket


class TestPaymentProcessedUseCase:

    @pytest.fixture
    def mock_domain_service(self):
        mock_service = MagicMock()
        mock_service.create_basket_payment_succeeded_event = MagicMock()
        mock_service.create_basket_payment_failed_event = MagicMock()
        mock_service.create_basket_payment_action_required_event = MagicMock()
        mock_service.create_basket_payment_canceled_event = MagicMock()
        return mock_service

    @pytest.fixture
    def mock_payments_data_provider(self):
        return MagicMock()

    @pytest.fixture
    def use_case(self, mock_domain_service, mock_payments_data_provider):
        return PaymentProcessedUseCase(
            domain_service=mock_domain_service,
            payments_data_provider=mock_payments_data_provider,
        )

    @pytest.fixture
    def basket_id(self):
        return uuid4()

    @pytest.fixture
    def consumers(self):
        return [Consumer.PAYMENTS_SERVICE, Consumer.BOOKING]

    @pytest.mark.parametrize(
        'status, event_creator_name',
        [
            (
                BasketPaymentStatus.SUCCESS,
                'create_basket_payment_succeeded_event',
            ),
            (
                BasketPaymentStatus.FAILED,
                'create_basket_payment_failed_event',
            ),
            (
                BasketPaymentStatus.ACTION_REQUIRED,
                'create_basket_payment_action_required_event',
            ),
            (
                BasketPaymentStatus.CANCELED,
                'create_basket_payment_canceled_event',
            ),
        ],
    )
    def test_execute_success_for_various_statuses(
        self,
        use_case,
        basket_id,
        consumers,
        mock_payments_data_provider,
        mock_domain_service,
        basket_factory,
        status,
        event_creator_name,
    ):
        # Arrange
        basket = basket_factory(status=status)
        input_dto = PaymentProcessedInputDTO(basket_id=basket_id, consumers=consumers)
        event_registry = DomainEventRegistry()
        mock_domain_event = MagicMock()
        mock_payments_data_provider.get_basket.return_value = basket
        event_creator_mock = getattr(mock_domain_service, event_creator_name)
        event_creator_mock.return_value = mock_domain_event

        # Act
        result = use_case.execute(input_dto, event_registry)

        # Assert
        assert result.success is True
        assert result.basket == basket
        assert result.error_message is None
        mock_payments_data_provider.get_basket.assert_called_once_with(basket_id)
        event_creator_mock.assert_called_once_with(basket)
        assert event_registry.get_events() == [mock_domain_event]

    @pytest.mark.parametrize(
        'basket_mock_setup',
        [
            (None),  # Basket not found
            (MagicMock(spec=Basket, latest_basket_payment=None)),  # Basket has no payment
        ],
    )
    def test_execute_fails_when_basket_is_invalid(
        self, use_case, basket_id, mock_payments_data_provider, basket_mock_setup
    ):
        # Arrange
        input_dto = PaymentProcessedInputDTO(basket_id=basket_id)
        mock_payments_data_provider.get_basket.return_value = basket_mock_setup

        # Act
        result = use_case.execute(input_dto)

        # Assert
        assert result.success is False
        assert result.error_message == f'Basket with ID {basket_id} not found or has no payment.'

    def test_status_to_event_creator_mapping_unsupported_status(
        self,
        use_case,
        basket_id,
        mock_payments_data_provider,
        basket_factory,
    ):
        # Arrange
        basket = basket_factory(status=BasketPaymentStatus.PENDING)
        input_dto = PaymentProcessedInputDTO(basket_id=basket_id)
        event_registry = DomainEventRegistry()
        mock_payments_data_provider.get_basket.return_value = basket

        # Act
        result = use_case.execute(input_dto, event_registry)

        # Assert
        assert result.success is False
        assert result.basket == basket
        assert result.error_message == 'No events created for payment processed use case.'

    def test_create_domain_event_method_directly(
        self, use_case, mock_domain_service, basket_factory
    ):
        # Arrange
        basket = basket_factory(status=BasketPaymentStatus.SUCCESS)
        mock_domain_event = MagicMock()
        mock_domain_service.create_basket_payment_succeeded_event.return_value = mock_domain_event

        # Act
        result = use_case._create_domain_event(basket)  # pylint: disable=protected-access

        # Assert
        assert result == mock_domain_event
        mock_domain_service.create_basket_payment_succeeded_event.assert_called_once_with(basket)

    def test_create_domain_event_method_with_unknown_status(self, use_case, basket_factory):
        # Arrange
        basket = basket_factory(status=BasketPaymentStatus.PENDING)

        # Act
        result = use_case._create_domain_event(basket)  # pylint: disable=protected-access

        # Assert
        assert result is None
