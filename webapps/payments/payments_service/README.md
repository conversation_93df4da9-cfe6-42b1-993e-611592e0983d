# Payments Service Module 🏦

Payments Service implementing **Domain-Driven Design (DDD)**, **Clean Architecture**, and **Hexagonal Architecture** principles.

## 🎯 Overview

The Payments Service is responsible for generating and publishing domain events for basket payments. It acts as an **observer** of the Point of Sale (POS) system, translating payment state changes into meaningful business events for downstream consumers.

### Key Features
- ✅ **Event-driven architecture** with domain events (private) and integration events (public) for payment lifecycle
- ✅ **Clean separation** of business logic from infrastructure
- ✅ **Type-safe** implementation with strong typing throughout
- ✅ **Extensible design** ready for future payment/refund use cases

## 🏗️ Architecture Principles

### Domain-Driven Design (DDD)
- **Aggregate Root** - Basket entity (immutable snapshot from external domain)
- **Value Objects** with proper validation (e.g. `EventId`)
- **Domain Events** representing internal events that occurred within the domain (e.g. payment succeeded).
- **Integration Events** representing external events, derived from domain events and enriched with additional context, intended to be propagated to external consumers (e.g. payment succeeded).

- **Domain Services** for complex business logic
- **Ubiquitous Language** for consistent terminology across code and business

### Clean Architecture
- **Dependency Rule**: Dependencies point inward (Domain ← Application ← Infrastructure ← Interface)
- **Independence**: Core business logic isolated from frameworks and external services
- **Testability**: Each layer can be tested in isolation
- **Stable Abstractions**: Ports (interfaces) in application layer, adapters in infrastructure

### Hexagonal Architecture (Ports & Adapters)
- **Ports**: Abstract interfaces (e.g. `EventPublisher`)
- **Adapters**: Concrete implementations (e.g. `PubSubPaymentEventPublisher`)
- **Anti-Corruption Layer**: String-to-enum conversion at module boundaries

## 📁 Directory Structure

```
payments_service/
├── domain/                          # 🧠 Core Business Logic (Inner Ring)
│   ├── entities/
│   │   ├── basket.py               # Aggregate root (immutable snapshot)
│   │   └── basket_payment.py       # Payment entity with business rules
│   ├── value_objects/
│   │   ├── event_id.py             # Validated UUID wrapper
│   │   └── event_version.py        # Event schema versioning
│   ├── events/
│   │   ├── base.py                 # DomainEvent base class + Registry
│   │   └── basket_payment.py       # Payment-specific domain events
│   └── services/
│       └── payment_event_domain_service.py  # Business logic for event creation
│
├── application/                     # 🎯 Use Cases & Orchestration (Middle Ring)
│   ├── use_cases/                  # Feature-based organization
│   │   ├── payment/
│   │   │   ├── payment_processed/  # Payment processed use case
│   │   │   │   ├── dto.py          # Input/Output data transfer objects
│   │   │   │   └── use_case.py     # Business operation implementation
│   │   │   ├── trigger_payment/    # Triggering payment use case
│   │   │   └── process_payment/    # Payment processing use case
│   │   └── refund/                 # Future refund operations
│   │       ├── trigger_refund/
│   │       ├── process_refund/
│   │       └── refund_processed/
│   ├── services/
│   │   ├── payment_application_service.py  # Main orchestration facade
│   │   └── event_dispatcher.py             # Generic event dispatching
│   ├── ports/                         # Interfaces for external dependencies
│   │   ├── event_publisher.py         # Generic event publishing contract
│   │   └── payments_data_provider.py  # Payments data access abstraction
│   ├── dto                         # Data transfer objects
│   └── integration_events/
│       └── events/                 # Integration event data classes
│       └── factories/              # Integration event creation logic
│
├── infrastructure/                  # 🔧 Technical Implementation (Outer Ring)
│   ├── adapters/
│   │   ├── pubsub/
│   │   │   └── pubsub_event_publisher.py   # Google Pub/Sub implementation
│   │   └── payments/
│   │       └── payments_data_adapter.py    # `webapps.point_of_sale` data access
│   └── django/
│       └── integration.py          # Framework integration & DI container
│
└── interfaces/                      # 🌐 External API Layer (Presentation Ring)
    └── web/
        └── api/                    # Web APIs (e.g., REST endpoints)
```

### 📝 **interfaces/** Directory
The `interfaces/` directory is reserved for **Presentation Layer** components that will provide external APIs and interfaces for the Payments Service, like:

- **REST/GraphQL APIs**: Public HTTP endpoints for external systems
- **CLI Commands**: Command-line interfaces for administrative tasks  
- **Message Handlers**: Async message processing interfaces
- **Webhooks**: Incoming webhook processors from payment providers
- **gRPC Services**: High-performance inter-service communication

This follows Clean Architecture's **Presentation Ring** pattern, keeping external interfaces separate from core business logic and allowing multiple ways to interact with the same use cases.

## 🔄 System Flow Diagrams

### 🏗️ General Architecture Flow

```mermaid
flowchart TD
    subgraph "🌐 External Layer"
        EXT["External Systems"]
        CONS["Consumers"]
    end
    
    subgraph "🔌 Interface Layer"
        API["REST API"]
        CLI["CLI Commands"]
        WH["Webhooks"]
    end

    subgraph "🔧 Infrastructure Layer"
        DI["DI Container"]
        ADP["Adapters"]
    end
    
    subgraph "🎯 Application Layer"
        AS["Application Services"]
        UC["Use Cases"]
        PORTS["Ports"]
    end
    
    subgraph "🧠 Domain Layer"
        ENT["Entities"]
        DS["Domain Services"]
        DE["Domain Events"]
    end

    EXT --> WH
    EXT --> API
    EXT --> CLI
    
    WH & API & CLI --> DI
    DI --> AS
    AS --> UC
    UC --> DS
    UC --> PORTS
    DS --> ENT
    DS --> DE
    PORTS --> ADP
    ADP --> CONS
    
    style EXT fill:#ffeb3b
    style API fill:#4caf50
    style CLI fill:#4caf50
    style WH fill:#4caf50
    style AS fill:#2196f3
    style UC fill:#ff9800
    style DS fill:#9c27b0
    style ADP fill:#009688
    style CONS fill:#f44336
```

### 📊 Specific Use Case Example: `handle_payment_processed`

#### Detailed Version
```mermaid
flowchart TD
    subgraph "🌐 External Systems"
        A["🔔 External Event<br/>Payment Status Changed"]
        T["📬 External Consumers<br/>(e.g. booking)"]
    end

    subgraph "🔌 Interface Layer"
        B["🌐 Programmatic Interface (Facade)"]
    end

    subgraph "🔧 Infrastructure Layer"
        subgraph "Django Integration"
            C1["🌐 handle_payment_processed()"]
            C2["🏭 PaymentsServiceContainer<br/>get_payment_application_service()"]
        end
        
        subgraph "Adapters"
            H["🗄️ PaymentsDataAdapter<br/>get_basket()"]
            R["📡 PubSubPaymentEventPublisher<br/>publish_event()"]
            S["☁️ Google Pub/Sub<br/>publish_message()"]
        end
    end

    subgraph "🎯 Application Layer"
        D["🎯 PaymentApplicationService<br/>handle_payment_processed()"]
        E["📋 PaymentProcessedInputDTO"]
        F["⚙️ PaymentProcessedUseCase<br/>execute()"]
        G["🔍 PaymentsDataProvider<br/>get_basket()"]
        M["✅ PaymentProcessedOutputDTO"]
        N["🎯 PaymentIntegrationEventContext"]
        O["📤 EventDispatcher<br/>dispatch()"]
        P["🏗️ PaymentIntegrationEventFactory<br/>create_integration_event()"]
        Q["📄 PaymentIntegrationEvent"]
    end

    subgraph "🧠 Domain Layer"
        I["📦 Basket Entity"]
        J["🧠 PaymentEventDomainService<br/>create_basket_updated_event()"]
        K["📨 PaymentSucceededEvent<br/>DomainEvent"]
        L["📝 DomainEventRegistry<br/>add_event()"]
    end

    A --> B
    B --> C1
    C1 --> C2
    C2 --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I
    I --> F
    F --> J
    J --> K
    K --> L
    L --> F
    F --> M
    M --> D
    D --> N
    N --> O
    O --> P
    P --> Q
    Q --> R
    R --> S
    S --> T

    style A fill:#ffeb3b
    style B fill:#4caf50
    style D fill:#2196f3
    style F fill:#ff9800
    style J fill:#9c27b0
    style O fill:#795548
    style S fill:#009688
    style T fill:#f44336
```

#### Simplified Version
```mermaid
flowchart TD
    subgraph "🌐 External Systems"
        A["🔔 External Event<br/>Payment Status Changed"]
        Z["📬 External Consumers<br/>(e.g. booking)"]
    end

    subgraph "🔌 Interface Layer"
        B["🌐 Programmatic Interface (Facade)"]
    end

    subgraph "🔧 Infrastructure"
        C["🏭 PaymentsServiceContainer"]
        H["🗄️ PaymentsDataAdapter"]
        S["☁️ PubSubPaymentEventPublisher"]
    end

    subgraph "🎯 Application"
        D["🎯 PaymentApplicationService"]
        F["⚙️ PaymentProcessedUseCase"]
        O["📤 EventDispatcher"]
        OI["📨 PaymentIntegrationEvent<br/>(Public Integration Event)"]
    end

    subgraph "🧠 Domain"
        I["📦 Basket"]
        J["🧠 PaymentEventDomainService"]
        K["📨 PaymentSucceededEvent<br/>(Private Domain Event)"]
    end

    A --> B
    B --> C
    C --> D
    D --> F
    F --> H
    H --> I
    I --> F
    F --> J
    J --> K
    K --> F
    F --> O
    O --> OI
    OI --> S
    S --> Z

    style A fill:#ffeb3b
    style B fill:#4caf50
    style D fill:#2196f3
    style F fill:#ff9800
    style J fill:#9c27b0
    style O fill:#795548
    style S fill:#009688
    style Z fill:#f44336
```

## 🚀 Usage Examples

### Basic Payment Processing

```python
from webapps.payments.payments_service.infrastructure.django.integration import (
    handle_payment_processed
)

# Process a payment update (most common use case)
success = handle_payment_processed(
    basket_id="123e4567-e89b-12d3-a456-426614174000",
    consumers=["booking", "other"]
)
```

### Working with Domain Entities (Advanced)

```python
from webapps.payments.payments_service.application.services.payment_application_service import (
    PaymentApplicationService
)
from webapps.payments.payments_service.infrastructure.django.integration import container

# Get service from DI container
service = container.get_payment_application_service()

# Execute use case with full control
result = service.handle_payment_processed(
    basket_id="123e4567-e89b-12d3-a456-426614174000",
    consumers=[Consumer.BOOKING, Consumer.FOOBAR]
)
```

## 🏛️ Key Design Decisions

### 1. **Basket as Immutable Snapshot** 
The `Basket` entity represents a read-only snapshot from the external POS domain. It contains no state-modifying methods since this service is an **observer**, not an **owner**.

### 2. **Feature Folder Organization**
Use cases are organized by business feature (`payment/`, `refund/`) rather than technical layer, making it easier to understand and extend business capabilities.

### 3. **Thin Domain Events**
Domain events contain only essential identifiers and metadata. Full data is retrieved when needed, reducing redundancy and coupling. Domain events are intended to be private - just for internal bounded context.

### 4. **Integration Events**
Private domain events will be translated into public integration events, which are then propagated to external consumers. Only integration events are exposed publicly.

### 5. **Generic Event Infrastructure**
The `EventPublisher` port accepts any `EventMessage`, making it reusable for future event types beyond baskets (schema-type-agnostic, e.g. protobuf, Avro, JSON).

### 6. **Strategy Pattern for Message Building**
Different domain events use specialized message builders, making it easy to add new event types without modifying existing code.

## 🔧 Configuration

Add to your Django settings:
```python
# Pub/Sub Configuration
PAYMENTS_SERVICE_GC_PROJECT_ID = os.environ.get(
    'PAYMENTS_SERVICE_GC_PROJECT_ID', f'bks-payments-service-{"prd" if LIVE_DEPLOYMENT else "dev"}'
)
```

## 🧪 Testing Strategy

### Unit Tests
```python
# Test domain entities in isolation
def test_basket_creation():
    basket = Basket(basket_id=uuid4(), business_id=123, ...)
    assert basket.business_id == 123
    
# Test domain services
def test_domain_service_creates_event():
    service = PaymentEventDomainService()
    event = service.create_basket_payment_succeeded_event(basket)
    assert isinstance(event, BasketPaymentSucceeded)
```

### Integration Tests
```python
# Test adapters with real dependencies
def test_payments_data_adapter():
    adapter = PaymentsDataAdapter(basket_port, payment_port)
    basket = adapter.get_basket(basket_id)
    assert basket is not None
```

### Contract Tests
```python
# Verify port/adapter contracts
def test_event_publisher_contract():
    publisher = PubSubPaymentEventPublisher()
    message = PaymentIntegrationEvent(...)
    publisher.publish_event(message)  # Should not raise
```

## 🔍 Monitoring & Observability

### Structured Logging
- **Info**: Successful event processing with IDs
- **Warning**: Business rule violations or retryable failures  
- **Error**: System failures requiring investigation
- **Exception**: Unexpected errors with full stack traces

### Event Tracking
- Event generation metrics
- Publication success/failure rates
- Consumer-specific delivery tracking
- Processing latency monitoring

## 🔄 Future Extensions

The architecture is designed to easily support:

### Additional Event Types
```python
class OrderProcessedEvent(DomainEvent):
    pass
```

### Multiple Message Brokers
```python
class KafkaEventPublisher(EventPublisher):
    def publish_event(self, message: EventMessage) -> None:
        # Kafka implementation
```

## 📋 Development Guidelines

### Adding New Use Cases
1. Create feature folder: `application/use_cases/{feature}/{use_case}/`
2. Define DTOs in `dto.py`
3. Implement business logic in `use_case.py`
4. Add to `PaymentApplicationService` orchestration (or create a new service)
5. Create integration in `django/integration.py`

### Adding New Event Types
1. Define in `domain/events/{aggregate}.py`
2. Create its corresponding integration event builder in `application/integration_events/factories`
3. Register builder in factory
4. Update documentation and tests

### Architectural Rules
- ✅ **Domain layer** cannot import from application or infrastructure
- ✅ **Application layer** can import from domain, not infrastructure  
- ✅ **Infrastructure layer** can import from any layer
- ✅ **UTC timestamps** for all datetime objects
- ✅ **Strong typing** with proper type hints
- ✅ **Producing Events** - domain layer operates on dataclasses, publisher transforms it to the expected format (e.g. Pub/Sub - protobuf)
