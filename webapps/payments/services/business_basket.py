from django.utils.translation import gettext as _

from lib.french_certification.utils import french_certification_enabled
from lib.point_of_sale.entities import ExtendedBasketEntity
from lib.point_of_sale.enums import (
    BasketElementType,
    BasketItemTaxType,
    BasketStatus,
    PaymentMethodType,
    BasketPaymentSource,
)
from lib.pos.entities import POSEntity
from lib.tools import format_currency
from webapps.business.ports.business_details import BusinessDetailsPort
from webapps.invoicing.ports import CustomerInvoicePort, CustomerInvoiceEntity
from webapps.payments.tasks import _prepare_basket_tax_summary
from webapps.point_of_sale.ports import BasketPaymentPort, RelatedBasketItemPort, BasketPort
from webapps.pos.ports import POSPort, TransactionPort


class BusinessBasketService:

    @classmethod
    def get_basket_details(cls, basket_entity: ExtendedBasketEntity):
        # Get the business details using the port
        business_details = BusinessDetailsPort.get_business_details(basket_entity.business_id)

        payments_summary = {
            'total': basket_entity.total,
            'already_paid': basket_entity.already_paid,
            'remaining': basket_entity.remaining,
            'payment_type': basket_entity.payment_type if basket_entity.payment_type else None,
            'status': basket_entity.status if basket_entity.status else None,
        }

        payments = [
            {
                'id': payment.id,
                'status': payment.status.value,
                'payment_type': {
                    'code': payment.payment_method.value,
                    'source': payment.source,
                    'label': payment.label,
                },
                'amount': payment.amount / 100,
                'amount_formatted': format_currency(payment.amount / 100),
                'created': payment.created,
                'refundable': BasketPaymentPort.is_refund_possible(
                    basket_payment_id=payment.id,
                    business_id=business_details.id,
                )[0],
            }
            for payment in basket_entity.payments
        ]

        items = [
            {
                'id': item.id,
                'name_line_1': item.name_line_1,
                'name_line_2': item.name_line_2,
                'quantity': item.quantity,
                'discount_rate': item.discount_rate,
                'total': item.total,
                'total_formatted': format_currency(item.total / 100),
            }
            for item in basket_entity.items
        ]

        pos = POSPort.get_pos_by_business_id(business_id=basket_entity.business_id)
        customer_invoice = CustomerInvoicePort.get_customer_invoice_info(basket_id=basket_entity.id)

        return {
            'id': basket_entity.id,
            'created': (
                basket_entity.created.astimezone(business_details.timezone)
                if business_details
                else basket_entity.created
            ),
            'public_id': basket_entity.public_id,
            'basket_number': basket_entity.basket_number,
            'transaction_id': basket_entity.transaction_id,
            'business_details': business_details if business_details else None,
            'payments_summary': payments_summary,
            'customer_card_id': basket_entity.customer_card_id,
            'total_elements': cls._get_elements_from_transaction(basket_entity),
            'payments': payments,
            'items': items,
            'tax_summary': _prepare_basket_tax_summary(basket_entity)[0],
            'commissions_enabled': pos.commissions_enabled,
            'send_receipt_email_allowed': not french_certification_enabled(),
            'receipt_foot_line_1': pos.receipt_footer_line_1,
            'receipt_foot_line_2': pos.receipt_footer_line_2,
            'disclaimer': _("The above confirmation of sale is not a fiscal receipt."),
            'customer_invoice': customer_invoice.id,
            'customer_invoice_number': customer_invoice.number,
            'customer_info': {
                'full_name': basket_entity.customer_data,
                'email': basket_entity.customer_email,
            },
            'can_generate_invoice': cls._calculate_if_invoice_possible(
                basket_entity, customer_invoice
            ),
            'lock': cls._calculate_if_should_be_locked(basket_entity, pos),
        }

    @staticmethod
    def _calculate_if_invoice_possible(  # pylint: disable=too-many-return-statements
        basket_entity: ExtendedBasketEntity, customer_invoice: CustomerInvoiceEntity
    ) -> bool:
        if customer_invoice.id:
            return False
        if not basket_entity.payments:
            return False
        if basket_entity.status != BasketStatus.SUCCESS:
            return False
        if basket_entity.total == 0:
            return False
        if any(
            payment.payment_method
            in [
                PaymentMethodType.EGIFT_CARD,
                PaymentMethodType.VOUCHER,
                PaymentMethodType.MEMBERSHIP,
                PaymentMethodType.PACKAGE,
            ]
            for payment in basket_entity.payments
        ):
            return False
        return True

    @staticmethod
    def _calculate_if_should_be_locked(  # pylint: disable=too-many-return-statements
        basket_entity: ExtendedBasketEntity,
        pos: POSEntity,
    ) -> bool:
        """
        for now if it hard to map conditions from pos1 logic:
            if self.latest_receipt.status_code not in receipt_status.NOT_LOCKING_STATUSES:
                return True

            if len(self.children.all()):
                return True

            if any(
                pr.payment_type.should_lock for pr in self.latest_receipt.payment_rows.all()
            ) and (
                self.latest_receipt.status_code not in receipt_status.FAKE_EDIT_STATUSES
            ):
                return True
        """
        appointment_id = RelatedBasketItemPort.get_appointment_id_for_basket(basket_entity.id)
        if appointment_id:
            if BasketPort.is_appointment_in_inactive_status(appointment_id):
                return True
        if any(
            payment.payment_method
            in PaymentMethodType.basket_online_payments()
            + [
                PaymentMethodType.EGIFT_CARD,
                PaymentMethodType.VOUCHER,
                PaymentMethodType.MEMBERSHIP,
                PaymentMethodType.PACKAGE,
                PaymentMethodType.PAY_BY_APP_DONATIONS,
                PaymentMethodType.BOOKSY_GIFT_CARD,
            ]
            for payment in basket_entity.payments
        ):
            return True
        if any(
            payment.source
            in [
                BasketPaymentSource.BOOKSY_PAY,
                BasketPaymentSource.PREPAYMENT,
            ]
            for payment in basket_entity.payments
        ):
            return True
        if pos.registers_enabled:
            register_details = TransactionPort.get_register_details(basket_entity.id)
            if register_details.id and not register_details.is_open:
                return True
        return False

    @staticmethod
    def _get_elements_from_transaction(instance: ExtendedBasketEntity) -> list[dict]:
        # Based on webapps.pos.calculations.calculate
        tips = sum(tip.amount for tip in instance.tips)
        subtotal = sum(item.total for item in instance.items)
        tax_total = sum(item.tax_amount for item in instance.items)
        discount = sum(item.total - item.discounted_total for item in instance.items)

        # Based on webapps.pos.serializers SummaryTaxListSerializer and get_tax_mode
        tax_mode = instance.items[0].tax_type if instance.items else BasketItemTaxType.EXCLUDED

        result = [
            {
                'label': BasketElementType.SUBTOTAL.label,
                'amount': subtotal,
            },
            {
                'label': BasketElementType.TIPS.label,
                'amount': tips,
            },
            {
                'label': BasketElementType.DISCOUNT.label,
                'amount': discount,
            },
        ]

        if tax_mode == BasketItemTaxType.EXCLUDED:
            result.append(
                {
                    'label': BasketElementType.TAXES_AND_FEES.label,
                    'amount': tax_total,
                }
            )

        return result
