from django.urls import path

from webapps.payments.views.customer_check_balance_transaction_secret import (
    CustomerCheckBalanceTransactionSecretView,
)
from webapps.payments.views.customer_check_balance_transaction_status import (
    CustomerCheckBalanceTransactionStatusView,
)
from webapps.payments.views.payment.calculate import CalculateView
from webapps.payments.views.payment.customer_baskets import (
    CustomerListBasketsView,
    CustomerBasketDetailsSendView,
)
from webapps.payments.views.payment.customer_basket_details import CustomerBasketDetailsView
from webapps.payments.views.payment.make_payment import MakePaymentView
from webapps.payments.views.tokenized_payment_methods import (
    CreateSetupIntentView,
    GetSetupIntentView,
    ListTokenizedPaymentMethodsView,
    SetDefaultTokenizedPaymentMethodView,
    StripeApplePayPaymentTokenView,
    TokenizedPaymentMethodView,
)

from webapps.payments.views.payment import (
    CustomerCancelBasketPaymentView,
    CustomerMakeBasketPaymentView,
)

urlpatterns = [
    path(
        'balance_transaction/<str:balance_transaction_id>/',
        CustomerCheckBalanceTransactionStatusView.as_view(),
        name='payments__check_balance_transaction_status',
    ),
    path(
        'baskets/',
        CustomerListBasketsView.as_view(),
        name='payments__list_baskets',
    ),
    path(
        'baskets/<str:basket_payment_id>/',
        CustomerBasketDetailsView.as_view(),
        name='payments__basket_customer_details',
    ),
    path(
        'baskets/<str:basket_id>/send/',
        CustomerBasketDetailsSendView.as_view(),
        name='payments__send_basket_details',
    ),
    path(
        'balance_transaction/<str:balance_transaction_id>/client_secret/',
        CustomerCheckBalanceTransactionSecretView.as_view(),
        name='payments__check_balance_transaction_client_secret',
    ),
    path(
        'basket_payment/<str:basket_payment_id>/cancel/',
        CustomerCancelBasketPaymentView.as_view(),
        name='payments__cancel_basket_payment',
    ),
    path(
        'basket_payment/<str:basket_payment_id>/make_payment/',
        CustomerMakeBasketPaymentView.as_view(),
        name='payments__make_basket_payment',
    ),
    path(
        'setup_intent/',
        CreateSetupIntentView.as_view(),
        name='payments__create_setup_intent',
    ),
    path(
        'setup_intent/<str:setup_intent_id>/',
        GetSetupIntentView.as_view(),
        name='payments__setup_intent_details',
    ),
    path(
        'payment_method/set_default/<str:payment_method_id>/',
        SetDefaultTokenizedPaymentMethodView.as_view(),
        name='payments__set_default_tokenized_pm',
    ),
    path(
        'payment_methods/provider/<str:provider_code>/',
        ListTokenizedPaymentMethodsView.as_view(),
        name='payments__list_provider_tokenized_pms',
    ),
    path(
        'payment_methods/',
        ListTokenizedPaymentMethodsView.as_view(),
        name='payments__list_all_tokenized_pms',
    ),
    path(
        'payment_methods/<str:payment_method_id>/',
        TokenizedPaymentMethodView.as_view(),
        name='payments__tokenized_pm',
    ),
    path(
        'payment_token/apple_pay/stripe/',
        StripeApplePayPaymentTokenView.as_view(),
        name='payments__payment_token_apple_pay',
    ),
    path(
        'make_payment/',
        MakePaymentView.as_view(),
        name='payments__make_payment',
    ),
    path(
        'calc/',
        CalculateView.as_view(),
        name='payments__calc_payment_amount',
    ),
]
