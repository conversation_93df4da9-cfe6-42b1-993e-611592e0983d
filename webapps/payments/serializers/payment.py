from django.utils.translation import gettext as _
from rest_framework import serializers

from lib.french_certification.utils import french_certification_enabled
from lib.payment_providers.enums import ExternalPaymentMethodType
from lib.point_of_sale.entities import ExtendedBasketEntity
from lib.point_of_sale.enums import (
    BasketCustomerPaymentAction,
    BasketStatus,
    BasketElementType,
    PaymentMethodType,
    BasketItemTaxType,
    MakePaymentPaymentMethodType,
    BasketTipType,
)
from lib.serializers import PaginatorSerializer
from lib.tools import format_currency
from webapps.booking.ports import BookingPort
from webapps.business.ports.business_details import BusinessDetailsEntity, BusinessDetailsPort
from webapps.point_of_sale.ports import BasketPaymentPort
from webapps.pos.ports import POSPort


class CustomerBasketQueryParamsSerializer(PaginatorSerializer):
    """Serializer for query parameters in CustomerListBasketsView"""

    basket_status = serializers.ChoiceField(
        choices=BasketStatus.choices(),
        required=False,
        allow_null=True,
    )

    def validate_basket_status(self, value):
        if value is not None:
            return BasketStatus(value)
        return None


class ExternalPaymentMethodSerializer(serializers.Serializer):
    partner = serializers.ChoiceField(
        choices=ExternalPaymentMethodType.choices(),
        required=True,
    )
    token = serializers.CharField(required=True)

    def validate(self, attrs):
        attrs = super().validate(attrs)
        partner: str = attrs['partner']
        attrs['partner'] = ExternalPaymentMethodType(partner)

        if not BookingPort.check_tokenized_payments_for_payment_creation(partner):
            raise serializers.ValidationError(
                _('Payment is not possible'), code='payment_not_possible'
            )

        return attrs


class CancelPaymentRequestSerializer(serializers.Serializer):
    def validate(self, attrs):
        if not BasketPaymentPort.is_customer_action_allowed(
            basket_payment_id=self.instance.id,
            user_id=self.context['user'].id,
            action=BasketCustomerPaymentAction.CANCEL_PAYMENT,
        ):
            raise serializers.ValidationError(_('Action is not allowed'))

        return attrs


class MakePaymentRequestSerializer(serializers.Serializer):
    external_payment_method = ExternalPaymentMethodSerializer(required=False)
    tokenized_payment_method_id = serializers.CharField(required=False)

    def validate(self, attrs):
        tokenized_payment_method_id = attrs.get('tokenized_payment_method_id')
        external_payment_method = attrs.get('external_payment_method')

        if bool(tokenized_payment_method_id) == bool(external_payment_method):  # XOR
            raise serializers.ValidationError(
                'Payment_method xor external_payment_method should be provided',
                code='missing_method',
            )

        if not BasketPaymentPort.is_customer_action_allowed(
            basket_payment_id=self.instance.id,
            user_id=self.context['user'].id,
            action=BasketCustomerPaymentAction.MAKE_PAYMENT,
        ):
            raise serializers.ValidationError(_('Action is not allowed'))

        is_pba_amount_valid, minimal_amount = BasketPaymentPort.validate_pba_amount(
            amount=self.instance.amount,
        )
        if not is_pba_amount_valid:
            raise serializers.ValidationError(
                {
                    'payment_rows': _('Amount must be greater or equal to {}').format(
                        format_currency(minimal_amount),
                    )
                },
                code='pay_by_app_minimal_amount',
            )

        return attrs


class BusinessRefundRequestSerializer(serializers.Serializer):
    refund_amount = serializers.IntegerField(required=False)


class BusinessDetailsSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    name = serializers.CharField()
    address = serializers.CharField()

    def to_representation(self, instance: BusinessDetailsEntity):
        return {
            'id': instance.id,
            'name': instance.name,
            'address': instance.address,
        }


class AmountSerializer(serializers.Serializer):
    amount = serializers.IntegerField()  # i.e. 123 (equals 1,23 of given currency)
    amount_formatted = serializers.CharField()  # i.e. "1,23 €"

    def to_representation(self, instance: int):  # in cents
        return {
            'amount': instance,
            'amount_formatted': format_currency(instance / 100),
        }


class PaymentTypeSerializer(serializers.Serializer):
    code = serializers.CharField()
    label = serializers.CharField()

    def to_representation(self, instance: str):
        return {
            'code': PaymentMethodType(instance).value,
            'label': PaymentMethodType(instance).label,
        }


class BasketStatusSerializer(serializers.Serializer):
    code = serializers.CharField()
    label = serializers.CharField()

    def to_representation(self, instance: BasketStatus):
        return {
            'code': instance.value,
            'label': instance.label,
        }


class BasketSummarySerializer(serializers.Serializer):
    # summary of payments in the basket
    total = AmountSerializer()
    already_paid = AmountSerializer()
    remaining = AmountSerializer()
    payment_type = PaymentTypeSerializer()
    status = BasketStatusSerializer()


class CustomerBasketSerializer(serializers.Serializer):
    """
    Basic serializer (i.e., for a list view) for *customers*
    """

    id = serializers.UUIDField()
    created = serializers.DateTimeField()
    public_id = serializers.CharField()  # receipt id in old pos
    basket_number = serializers.CharField()  # receipt_number in old pos
    transaction_id = serializers.IntegerField()  # Transaction.id in old pos
    business_details = BusinessDetailsSerializer()
    payments_summary = BasketSummarySerializer()

    def to_representation(self, instance: ExtendedBasketEntity):
        # Get the business details using the port
        business_details = BusinessDetailsPort.get_business_details(instance.business_id)

        # Create the summary data using serializers
        payments_summary = {
            'total': AmountSerializer(instance.total).data,
            'already_paid': AmountSerializer(instance.already_paid).data,
            'remaining': AmountSerializer(instance.remaining).data,
            'payment_type': (
                PaymentTypeSerializer(instance.payment_type).data if instance.payment_type else None
            ),
            'status': BasketStatusSerializer(instance.status).data if instance.status else None,
        }

        # Return the complete representation
        return {
            'id': instance.id,
            'created': (
                instance.created.astimezone(business_details.timezone)
                if business_details
                else instance.created
            ),
            'public_id': instance.public_id,
            'basket_number': instance.basket_number,
            'transaction_id': instance.transaction_id,
            'business_details': (
                BusinessDetailsSerializer(business_details).data if business_details else None
            ),
            'payments_summary': payments_summary,
        }


class CustomerBasketDetailsSerializer(CustomerBasketSerializer):
    """
    Detailed serializer for a customer's basket, extending CustomerBasketSerializer.
    """

    def to_representation(
        self,
        instance: ExtendedBasketEntity,
    ):
        base_representation = super().to_representation(instance)

        payments = [
            {
                'id': payment.id,
                'status': payment.status.value,
                'payment_type': {
                    'code': payment.payment_method.value,
                    'source': payment.source,
                    'label': payment.label,
                },
                'amount': payment.amount / 100,
                'amount_formatted': format_currency(payment.amount / 100),
                'created': payment.created,
            }
            for payment in instance.payments
        ]

        items = [
            {
                'id': item.id,
                'name_line_1': item.name_line_1,
                'name_line_2': item.name_line_2,
                'quantity': item.quantity,
                'discount_rate': item.discount_rate,
                'total': item.total,
                'total_formatted': format_currency(item.total / 100),
            }
            for item in instance.items
        ]

        pos = POSPort.get_pos_by_business_id(business_id=instance.business_id)

        return {
            **base_representation,
            'total_elements': self._get_elements_from_transaction(instance),
            'payments': payments,
            'items': items,
            'send_receipt_email_allowed': not french_certification_enabled(),
            'receipt_foot_line_1': pos.receipt_footer_line_1,
            'receipt_foot_line_2': pos.receipt_footer_line_2,
            'disclaimer': _("The above confirmation of sale is not a fiscal receipt."),
            'customer_data': instance.customer_data,
        }

    def _get_elements_from_transaction(self, instance: ExtendedBasketEntity) -> list[dict]:
        # Based on webapps.pos.calculations.calculate
        tips = sum(tip.amount for tip in instance.tips)
        subtotal = sum(item.total for item in instance.items)
        tax_total = sum(item.tax_amount for item in instance.items)
        discount = sum(item.total - item.discounted_total for item in instance.items)

        # Based on webapps.pos.serializers SummaryTaxListSerializer and get_tax_mode
        tax_mode = instance.items[0].tax_type if instance.items else BasketItemTaxType.EXCLUDED

        result = [
            {
                'label': BasketElementType.SUBTOTAL.label,
                'amount': AmountSerializer(subtotal).data,
            },
            {
                'label': BasketElementType.TIPS.label,
                'amount': AmountSerializer(tips).data,
            },
            {
                'label': BasketElementType.DISCOUNT.label,
                'amount': AmountSerializer(discount).data,
            },
        ]

        if tax_mode == BasketItemTaxType.EXCLUDED:
            result.append(
                {
                    'label': BasketElementType.TAXES_AND_FEES.label,
                    'amount': AmountSerializer(tax_total).data,
                }
            )

        return result


class MakePaymentPaymentMethodSerializer(serializers.Serializer):
    type = serializers.ChoiceField(choices=MakePaymentPaymentMethodType.choices())
    token = serializers.CharField(required=False)
    tokenized_pm_id = serializers.UUIDField(required=False)

    def validate(self, attrs):
        super().validate(attrs)

        _type = attrs.get('type')
        if _type == MakePaymentPaymentMethodType.CARD and not attrs.get('tokenized_pm_id'):
            raise serializers.ValidationError('Card ID is required.')
        if _type in (
            MakePaymentPaymentMethodType.BLIK,
            MakePaymentPaymentMethodType.APPLE_PAY,
            MakePaymentPaymentMethodType.GOOGLE_PAY,
            MakePaymentPaymentMethodType.BOOKSY_GIFT_CARD,
        ) and not attrs.get('token'):
            raise serializers.ValidationError('Token is required.')
        return attrs


class MakePaymentTipSerializer(serializers.Serializer):
    type = serializers.ChoiceField(choices=BasketTipType.choices())
    value = serializers.IntegerField()


class MakePaymentSerializer(serializers.Serializer):
    appointment_id = serializers.IntegerField()
    payment_method = MakePaymentPaymentMethodSerializer()
    tip = MakePaymentTipSerializer(required=False)


class CalculateSerializer(serializers.Serializer):
    draft_id = serializers.UUIDField()
    tip = MakePaymentTipSerializer(required=False)


class TotalElementSerializer(serializers.Serializer):
    label = serializers.CharField()
    amount = AmountSerializer()


class BusinessPaymentTypeSerializer(serializers.Serializer):
    code = serializers.CharField()
    source = serializers.CharField()
    label = serializers.CharField()


class PaymentSerializer(serializers.Serializer):
    id = serializers.UUIDField()
    status = serializers.CharField()
    payment_type = BusinessPaymentTypeSerializer()
    amount = serializers.IntegerField()
    amount_formatted = serializers.CharField()
    created = serializers.DateTimeField()
    refundable = serializers.BooleanField()


class ItemSerializer(serializers.Serializer):
    id = serializers.UUIDField()
    name_line_1 = serializers.CharField()
    name_line_2 = serializers.CharField()
    quantity = serializers.IntegerField()
    discount_rate = serializers.IntegerField()
    total = serializers.IntegerField()
    total_formatted = serializers.CharField()


class TaxSummarySerializer(serializers.Serializer):
    tax_rate = serializers.IntegerField()
    total_tax_amount = serializers.IntegerField()
    total_net_value = serializers.IntegerField()
    total_gross_value = serializers.IntegerField()


class CustomerInfoSerializer(serializers.Serializer):
    full_name = serializers.CharField()
    email = serializers.CharField()


class BusinessBasketDetailSerializer(serializers.Serializer):

    id = serializers.UUIDField()
    created = serializers.DateTimeField()
    public_id = serializers.IntegerField()  # receipt id in old pos
    basket_number = serializers.CharField()  # receipt_number in old pos
    transaction_id = serializers.IntegerField()  # Transaction.id in old pos
    business_details = BusinessDetailsSerializer()
    payments_summary = BasketSummarySerializer()
    customer_card_id = serializers.IntegerField()
    total_elements = TotalElementSerializer(many=True)
    payments = PaymentSerializer(many=True)
    items = ItemSerializer(many=True)
    tax_summary = TaxSummarySerializer()
    commissions_enabled = serializers.BooleanField()
    send_receipt_email_allowed = serializers.BooleanField()
    receipt_foot_line_1 = serializers.CharField()
    receipt_foot_line_2 = serializers.CharField()
    disclaimer = serializers.CharField()
    customer_invoice = serializers.IntegerField()
    customer_invoice_number = serializers.IntegerField()
    customer_info = CustomerInfoSerializer()
    can_generate_invoice = serializers.BooleanField()
    lock = serializers.BooleanField()
