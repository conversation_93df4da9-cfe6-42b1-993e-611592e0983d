import uuid
from datetime import datetime, UTC
from unittest.mock import patch

from django.urls import reverse
from model_bakery import baker
from rest_framework.status import HTTP_404_NOT_FOUND, HTTP_200_OK

from drf_api.lib.base_drf_test_case import BaseBusinessApiTestCase
from lib.baker_utils import get_or_create_booking_source
from lib.payments.enums import PaymentProviderCode
from lib.point_of_sale.entities import (
    BasketPaymentEntity,
    ExtendedBasketEntity,
    ExtendedBasketItemEntity,
    BasketTipEntity,
)
from lib.point_of_sale.enums import (
    BasketPaymentSource,
    BasketPaymentStatus,
    BasketPaymentType,
    BasketStatus,
    BasketType,
    PaymentMethodType,
    BasketItemTaxType,
)
from lib.test_utils import user_recipe
from service.tests import dict_assert
from webapps.booking.models import BookingSources
from webapps.business.baker_recipes import business_recipe, staffer_recipe
from webapps.business.models import Resource
from webapps.business.ports.business_details import BusinessDetailsEntity
from webapps.pos.models import POS


class BusinessBasketDetailsViewTestCase(BaseBusinessApiTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.cx_user = user_recipe.make()
        cls.owner_user = user_recipe.make()
        cls.business = business_recipe.make(
            active_from=datetime(2024, 6, 1, 10, tzinfo=UTC),
            owner=cls.owner_user,
        )
        cls.pos = baker.make(
            POS, business=cls.business, receipt_footer_line_1='test', receipt_footer_line_2='Test'
        )
        cls.owner = staffer_recipe.make(
            business=cls.business,
            staff_user=cls.owner_user,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_OWNER,
        )

        cls.biz_booking_src = get_or_create_booking_source(
            app_type=BookingSources.BUSINESS_APP,
            api_key='business_key',
        )

    def setUp(self) -> None:
        self.user = self.owner_user
        super().setUp()

    def _create_mock_basket_item(self, basket_id, item_id=None):
        return ExtendedBasketItemEntity(
            id=item_id or uuid.uuid4(),
            basket_id=basket_id,
            item_price=5000,
            discount_rate=0,
            gross_total=5000,
            type="service",
            name_line_1="Awesome Service",
            name_line_2="Provided by expert",
            total=5000,
            net_total=5000,
            quantity=1,
            tax_amount=0,
            tax_rate=0,
            discounted_total=5000,
            tax_type=BasketItemTaxType.EXCLUDED,
        )

    def _create_mock_basket_payment(self, basket_id, payment_id=None):
        return BasketPaymentEntity(
            id=payment_id or uuid.uuid4(),
            basket_id=basket_id,
            amount=10000,
            payment_method=PaymentMethodType.CARD,
            payment_provider_code=PaymentProviderCode.STRIPE,
            balance_transaction_id=uuid.uuid4(),
            status=BasketPaymentStatus.SUCCESS,
            user_id=self.user.id,
            type=BasketPaymentType.PAYMENT,
            parent_basket_payment_id=None,
            error_code=None,
            action_required_details=None,
            metadata={'internal_ref': 'xyz'},
            auto_capture=True,
            source=BasketPaymentSource.PREPAYMENT,
            created=datetime.now(),
            operator_id=None,
            label='label',
        )

    def _create_mock_basket_tip(self, tip_id=None):
        return BasketTipEntity(
            id=tip_id or uuid.uuid4(),
            amount=1000,
        )

    def _create_mock_extended_basket_entity(
        self, basket_id=None, include_items=True, include_payments=True, include_tips=True
    ):
        basket_uuid = basket_id or uuid.uuid4()
        items_list = []
        if include_items:
            items_list.append(self._create_mock_basket_item(basket_id=basket_uuid))
            items_list.append(
                self._create_mock_basket_item(basket_id=basket_uuid, item_id=uuid.uuid4())
            )

        payments_list = []
        if include_payments:
            payments_list.append(self._create_mock_basket_payment(basket_id=basket_uuid))

        tips_list = []
        if include_tips:
            tips_list.append(self._create_mock_basket_tip())

        return ExtendedBasketEntity(
            id=basket_uuid,
            business_id=self.business.id,
            type=BasketType.PAYMENT,
            archived=None,
            metadata={},
            customer_card_id=123,
            created=datetime.now(),
            public_id=1234,
            basket_number="BASKET-XYZ-789",
            status=BasketStatus.PENDING,
            total=11000,
            already_paid=10000,
            remaining=1000,
            payment_type=PaymentMethodType.CARD,
            items=items_list,
            payments=payments_list,
            tips=tips_list,
            transaction_id=15975,
            customer_data='jan kowalski',
            customer_email='<EMAIL>',
        )

    @patch('webapps.point_of_sale.ports.BasketPort.get_basket_details')
    @patch('webapps.payments.services.business_basket.BusinessBasketService.get_basket_details')
    def test_get_basket_details_success(
        self,
        mock_service_get_basket_details,
        mock_port_get_basket_details,
    ):
        basket_uuid = uuid.uuid4()
        mock_basket_entity = self._create_mock_extended_basket_entity(basket_id=basket_uuid)
        mock_port_get_basket_details.return_value = mock_basket_entity
        mock_service_get_basket_details.return_value = self._mock_service_basket_details(
            mock_basket_entity
        )

        url = reverse(
            'payments__basket_business_details',
            kwargs={'business_pk': self.business.id, 'basket_id': str(basket_uuid)},
        )
        response = self.client.get(url, content_type='application/json')

        self.assertEqual(response.status_code, HTTP_200_OK)
        actual_response_result = response.json()['result']

        expected_result = {
            'id': actual_response_result.get('id'),
            'created': actual_response_result.get('created'),
            'public_id': mock_basket_entity.public_id,
            'basket_number': mock_basket_entity.basket_number,
            'business_details': {'id': 1, 'name': 'Test Business', 'address': '123 Test St'},
            'payments_summary': {
                'total': {'amount': 11000, 'amount_formatted': '$110.00'},
                'already_paid': {'amount': 10000, 'amount_formatted': '$100.00'},
                'remaining': {'amount': 1000, 'amount_formatted': '$10.00'},
                'payment_type': {'code': 'card', 'label': 'Card (Mobile Payment)'},
                'status': {'code': 'pending', 'label': 'Pending'},
            },
            'customer_card_id': mock_basket_entity.customer_card_id,
            'total_elements': [
                {'label': 'Subtotal', 'amount': {'amount': 10000, 'amount_formatted': '$100.00'}},
                {'label': 'Tips', 'amount': {'amount': 1000, 'amount_formatted': '$10.00'}},
                {'label': 'Discount', 'amount': {'amount': 0, 'amount_formatted': '$0.00'}},
                {'label': 'Taxes & Fees', 'amount': {'amount': 0, 'amount_formatted': '$0.00'}},
            ],
            'payments': [
                {
                    'id': str(mock_basket_entity.payments[0].id),
                    'status': 'success',
                    'payment_type': {'code': 'card', 'source': 'prepayment', 'label': 'label'},
                    'amount': 100.0,
                    'amount_formatted': '$100.00',
                    'created': actual_response_result.get('payments')[0].get('created'),
                }
            ],
            'items': [
                {
                    'id': actual_response_result.get('items')[0].get('id'),
                    'name_line_1': 'Awesome Service',
                    'name_line_2': 'Provided by expert',
                    'quantity': 1,
                    'discount_rate': 0,
                    'total': 5000,
                    'total_formatted': '$50.00',
                },
                {
                    'id': actual_response_result.get('items')[1].get('id'),
                    'name_line_1': 'Awesome Service',
                    'name_line_2': 'Provided by expert',
                    'quantity': 1,
                    'discount_rate': 0,
                    'total': 5000,
                    'total_formatted': '$50.00',
                },
            ],
            'tax_summary': {
                'tax_rate': 0,
                'total_tax_amount': 0,
                'total_net_value': 10000,
                'total_gross_value': 10000,
            },
            'send_receipt_email_allowed': True,
            'commissions_enabled': True,
            'receipt_foot_line_1': self.pos.receipt_footer_line_1,
            'receipt_foot_line_2': self.pos.receipt_footer_line_2,
            'disclaimer': 'The above confirmation of sale is not a fiscal receipt.',
            'customer_invoice': None,
            'customer_invoice_number': None,
            'customer_info': {
                'full_name': mock_basket_entity.customer_data,
                'email': mock_basket_entity.customer_email,
            },
            'can_generate_invoice': False,
            'lock': True,
        }
        dict_assert(actual_response_result, expected_result)

    def test_get_basket_details_failed_basic_staffer(self):
        user = user_recipe.make()
        staffer_recipe.make(
            business=self.business,
            staff_user=user,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_STAFF,
        )
        self.authorized_user = getattr(self.client, self.AUTH_METHOD)(
            user=user,
            source=self.biz_booking_src,
        )
        url = reverse(
            'payments__basket_business_details',
            kwargs={'business_pk': self.business.id, 'basket_id': str(uuid.uuid4())},
        )
        response = self.client.get(url, content_type='application/json')
        self.assertEqual(response.status_code, HTTP_404_NOT_FOUND)

    def _mock_service_basket_details(self, mocked_basket_entity):
        return {
            'id': mocked_basket_entity.id,
            'created': mocked_basket_entity.created,
            'public_id': 1234,
            'basket_number': 'BASKET-XYZ-789',
            'transaction_id': 15975,
            'business_details': BusinessDetailsEntity(
                id=1,
                name='Test Business',
                address='123 Test St',
                timezone=self.business.get_timezone(),
            ),
            'payments_summary': {
                'total': 11000,
                'already_paid': 10000,
                'remaining': 1000,
                'payment_type': PaymentMethodType.CARD,
                'status': BasketStatus.PENDING,
            },
            'customer_card_id': 123,
            'total_elements': [
                {'label': 'Subtotal', 'amount': 10000},
                {'label': 'Tips', 'amount': 1000},
                {'label': 'Discount', 'amount': 0},
                {'label': 'Taxes & Fees', 'amount': 0},
            ],
            'payments': [
                {
                    'id': mocked_basket_entity.payments[0].id,
                    'status': 'success',
                    'payment_type': {
                        'code': 'card',
                        'source': BasketPaymentSource.PREPAYMENT,
                        'label': 'label',
                    },
                    'amount': 100.0,
                    'amount_formatted': '$100.00',
                    'created': mocked_basket_entity.payments[0].created,
                    'refundable': True,
                }
            ],
            'items': [
                {
                    'id': mocked_basket_entity.items[0].id,
                    'name_line_1': 'Awesome Service',
                    'name_line_2': 'Provided by expert',
                    'quantity': 1,
                    'discount_rate': 0,
                    'total': 5000,
                    'total_formatted': '$50.00',
                },
                {
                    'id': mocked_basket_entity.items[1].id,
                    'name_line_1': 'Awesome Service',
                    'name_line_2': 'Provided by expert',
                    'quantity': 1,
                    'discount_rate': 0,
                    'total': 5000,
                    'total_formatted': '$50.00',
                },
            ],
            'tax_summary': {
                'tax_rate': 0,
                'total_tax_amount': 0,
                'total_net_value': 10000,
                'total_gross_value': 10000,
            },
            'commissions_enabled': True,
            'send_receipt_email_allowed': True,
            'receipt_foot_line_1': 'test',
            'receipt_foot_line_2': 'Test',
            'disclaimer': 'The above confirmation of sale is not a fiscal receipt.',
            'customer_invoice': None,
            'customer_invoice_number': None,
            'customer_info': {
                'full_name': 'jan kowalski',
                'email': '<EMAIL>',
            },
            'can_generate_invoice': False,
            'lock': True,
        }
