import uuid

from django.urls import reverse
from model_bakery import baker
from rest_framework import status
from rest_framework.status import HTTP_404_NOT_FOUND

from drf_api.lib.base_drf_test_case import CustomerAPITestCase
from lib.baker_utils import get_or_create_booking_source
from lib.payment_gateway.enums import BalanceTransactionStatus
from lib.payments.enums import PaymentError, PaymentProviderCode
from service.tests import dict_assert
from webapps.booking.models import BookingSources
from webapps.payment_gateway.models import BalanceTransaction
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_providers.models import Payment
from webapps.user.models import User


class CustomerCheckBalanceTransactionStatusViewTestCase(CustomerAPITestCase):
    @classmethod
    def setUpTestData(cls):
        cls.user = baker.make(
            User,
            email='<EMAIL>',
            cell_phone='+48 697 850 000',
        )
        cls.customer_booking_src = get_or_create_booking_source(
            app_type=BookingSources.CUSTOMER_APP,
            api_key='customer_key',
        )

    def setUp(self):
        super().setUp()
        self.wallet, _ = PaymentGatewayPort.get_or_create_customer_wallet(
            customer_user_id=self.user.id,
            email=self.user.email,
            phone=self.user.cell_phone,
            statement_name='',
        )

    def test_balance_transaction_status(self):
        balance_transaction = baker.make(
            BalanceTransaction,
            sender_id=self.wallet.id,
        )
        response = self._make_get_request(balance_transaction.id)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        dict_assert(response.json(), {'status': 'processing'})

    def test_error_message(self):
        payment = baker.make(
            Payment, error_code=PaymentError.GENERIC_ERROR, provider_code=PaymentProviderCode.STRIPE
        )

        balance_transaction = baker.make(
            BalanceTransaction,
            external_id=payment.id,
            sender_id=self.wallet.id,
            status=BalanceTransactionStatus.FAILED,
        )

        response = self._make_get_request(balance_transaction.id)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        dict_assert(
            response.json(),
            {
                'status': 'failed',
                'error_code': PaymentError.GENERIC_ERROR.value,
                'error_message': PaymentError.GENERIC_ERROR.label,
            },
        )

    def test_404(self):
        response = self._make_get_request(
            balance_transaction_id=self._get_fake_balance_transaction_id(),
        )
        self.assertEqual(response.status_code, HTTP_404_NOT_FOUND)

    def _get_fake_balance_transaction_id(self):
        random_uuid = uuid.uuid4()
        if random_uuid == self.wallet.id:
            random_uuid = self._get_fake_balance_transaction_id()
        return random_uuid

    def _make_get_request(self, balance_transaction_id: uuid.UUID):
        return self.client.get(
            reverse(
                'payments__check_balance_transaction_status',
                kwargs={
                    'balance_transaction_id': balance_transaction_id,
                },
            ),
            content_type='application/json',
        )
