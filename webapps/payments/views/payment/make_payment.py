from bo_obs.datadog.enums import BooksyTeams
from rest_framework import status
from rest_framework.generics import GenericAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from drf_api.base_views import BaseBooksySessionAPIView
from lib.feature_flag.feature.payments_service import PaymentsServiceTriggerPaymentFlag
from lib.pos.entities import (
    MakePaymentRequestEntity,
    MakePaymentResponseEntity,
    ExtraData,
    MakePaymentPaymentData,
    TipData,
)
from service.exceptions import ServiceError
from service.mixins.throttling import get_django_user_ip
from webapps.payments.payments_service.interfaces import payments_service_trigger_payment
from webapps.payments.serializers.payment import MakePaymentSerializer
from webapps.pos.ports import POSPort


class MakePaymentView(BaseBooksySessionAPIView, GenericAPIView):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)
    permission_classes = (IsAuthenticated,)
    serializer_class = MakePaymentSerializer

    def post(self, request):
        if not PaymentsServiceTriggerPaymentFlag():
            return Response(status=status.HTTP_400_BAD_REQUEST)

        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data
        tip = data.get('tip')

        make_payment_request = MakePaymentRequestEntity(
            appointment_id=data['appointment_id'],
            user_id=self.request.user.id,
            payment_data=MakePaymentPaymentData(
                payment_method=data['payment_method']['type'],
                tokenized_pm_id=data['payment_method'].get('tokenized_pm_id'),
                token=data['payment_method'].get('token'),
                tip=(
                    TipData(
                        type=tip.get('type'),
                        value=tip.get('value'),
                    )
                    if tip
                    else None
                ),
            ),
            extra_data=ExtraData(
                forwarded_ip=str(get_django_user_ip(self.request)),
                user_agent=self.user_agent,
                language=self.language,
                fingerprint=self.fingerprint,
            ),
        )
        response: MakePaymentResponseEntity = POSPort.make_payment_transaction(
            data=make_payment_request,
        )
        if response.errors:
            return Response(status=status.HTTP_400_BAD_REQUEST)

        tip = make_payment_request.payment_data.tip
        result = payments_service_trigger_payment(
            basket_id=response.basket_id,
            payment_method=make_payment_request.payment_data.payment_method,
            tokenized_pm_id=make_payment_request.payment_data.tokenized_pm_id,
            token=make_payment_request.payment_data.token,
            tip_type=tip.type if tip else None,
            tip_value=tip.value if tip else None,
        )
        if result is False:
            raise ServiceError(
                code=status.HTTP_400_BAD_REQUEST,
                errors=[
                    {
                        'code': 'payments_service_trigger_payment_error',
                        'description': 'There was an error processing your payment',
                    }
                ],
            )

        return Response(data={'basket_id': response.basket_id}, status=status.HTTP_200_OK)
