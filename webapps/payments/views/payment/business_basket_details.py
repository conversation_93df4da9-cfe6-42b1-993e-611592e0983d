import uuid

from bo_obs.datadog.enums import BooksyTeams
from rest_framework import status
from rest_framework.generics import GenericAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from drf_api.base_views import BaseBooksySessionAPIView
from drf_api.service.business.validators.access import get_business_advanced_staffer_validator
from webapps.payments.serializers.payment import BusinessBasketDetailSerializer
from webapps.payments.services.business_basket import BusinessBasketService
from webapps.point_of_sale.ports import BasketPort


class BusinessBasketDetailsView(
    BaseBooksySessionAPIView,
    GenericAPIView,
):
    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)
    permission_classes = (IsAuthenticated,)
    serializer_class = BusinessBasketDetailSerializer
    business_basket_service = BusinessBasketService

    def _validate_staffer(self, business_pk):
        validator = get_business_advanced_staffer_validator(
            business=business_pk,
            request=self.request,
            user=self.request.user,
        )
        validator.validate()

    def get(self, request, business_pk: int, basket_id: str | uuid.UUID):
        self._validate_staffer(business_pk)
        basket = BasketPort.get_basket_details(basket_id=basket_id)
        if basket is None:
            return Response(status=status.HTTP_404_NOT_FOUND)
        basket_data = self.business_basket_service.get_basket_details(basket_entity=basket)
        serializer = self.serializer_class(basket_data)

        return Response(
            data={
                'result': serializer.data,
            },
            status=status.HTTP_200_OK,
        )
