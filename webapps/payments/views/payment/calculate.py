from bo_obs.datadog.enums import BooksyTeams
from rest_framework import status
from rest_framework.generics import GenericAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from drf_api.base_views import BaseBooksySessionAPIView
from lib.pos.entities import (
    TipData,
    CalculatePaymentAmountResponseEntity,
    CalculatePaymentAmountRequestEntity,
    ExtraData,
)
from service.mixins.throttling import get_django_user_ip
from webapps.payments.serializers.payment import CalculateSerializer
from webapps.pos.ports import POSPort


class CalculateView(BaseBooksySessionAPIView, GenericAPIView):
    """
    Calculate total amount for the draft appointment including the tip
    """

    booksy_teams = (BooksyTeams.PAYMENT_PROCESSING,)
    permission_classes = (IsAuthenticated,)
    serializer_class = CalculateSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        tip = serializer.validated_data.get('tip')

        response: CalculatePaymentAmountResponseEntity = POSPort.calculate_payment_amount(
            data=CalculatePaymentAmountRequestEntity(
                draft_id=serializer.validated_data['draft_id'],
                user_id=request.user.id,
                extra_data=ExtraData(
                    forwarded_ip=str(get_django_user_ip(self.request)),
                    user_agent=self.user_agent,
                    language=self.language,
                    fingerprint=self.fingerprint,
                ),
                tip=(
                    TipData(
                        type=tip['type'],
                        value=tip['value'],
                    )
                    if tip
                    else None
                ),
                booking_source_id=self.booking_source.id if self.booking_source else None,
            )
        )

        if response.errors:
            return Response(status=status.HTTP_400_BAD_REQUEST)
        return Response(status=status.HTTP_200_OK)
