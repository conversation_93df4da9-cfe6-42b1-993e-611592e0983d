import uuid

from bo_obs.datadog.enums import BooksyTeams
from django.http import Http404
from django.utils.translation import gettext as _
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.generics import GenericAPIView

from drf_api.base_views import BaseBooksySessionAPIView
from drf_api.service.business.validators.access import get_business_advanced_staffer_validator
from lib.feature_flag.feature.payment import PartialRefundsFlag
from lib.smartlock import LockFailed, SmartLock
from service.exceptions import ServiceError
from service.mixins.validation import validate_serializer
from webapps.payments.serializers.payment import BusinessRefundRequestSerializer
from webapps.point_of_sale.exceptions import BasketPaymentNotFound, RefundNotAllowed
from webapps.point_of_sale.ports import BasketPaymentPort


class BusinessRefundBasketPaymentView(
    BaseBooksySessionAPIView,
    GenericAPIView,
):
    booksy_teams = (
        BooksyTeams.PAYMENT_NEXUS,
        BooksyTeams.PAYMENT_PROCESSING,
    )
    serializer_class = BusinessRefundRequestSerializer
    permission_classes = (IsAuthenticated,)
    lookup_url_kwarg = 'basket_payment_id'

    def _validate_staffer(self, business_pk):
        validator = get_business_advanced_staffer_validator(
            business=business_pk,
            request=self.request,
            user=self.request.user,
        )
        validator.validate()

        return validator

    def _handle_refund(
        self, business_pk: int, basket_payment_id: str | uuid.UUID, refund_amount: int
    ) -> Response:
        validator = self._validate_staffer(business_pk)
        operator_id = validator.fetcher.user_staffer_id

        try:
            BasketPaymentPort.refund_basket_payment(
                basket_payment_id=basket_payment_id,
                business_id=business_pk,
                operator_id=operator_id,
                amount=refund_amount,
            )
        except BasketPaymentNotFound as exception:
            raise Http404 from exception
        except RefundNotAllowed as exception:
            refund_error = exception.refund_error
            raise ServiceError(
                code=status.HTTP_400_BAD_REQUEST,
                errors=[
                    {
                        'code': refund_error.value,
                        'description': refund_error.label,
                    }
                ],
            ) from exception

        return Response(data={}, status=status.HTTP_200_OK)

    def post(self, request, business_pk: int, basket_payment_id: str | uuid.UUID) -> Response:

        serializer = self.get_serializer(
            data=request.data,
            context={'user': self.user},
        )

        validated_data = validate_serializer(serializer)

        if not PartialRefundsFlag() and validated_data.get('refund_amount'):
            raise ServiceError(
                code=status.HTTP_400_BAD_REQUEST,
                errors=[
                    {
                        'code': 'partial_refunds_disabled',
                        'description': 'Partial refunds are disabled.',
                    }
                ],
            )

        try:
            with SmartLock(key=f'refund_basket_payment:{basket_payment_id}'):
                return self._handle_refund(
                    business_pk,
                    basket_payment_id,
                    refund_amount=validated_data.get('refund_amount'),
                )
        except LockFailed:
            return Response(
                data={
                    'errors': [
                        {
                            'code': 'lock_error',
                            'description': _('Could not perform action'),
                        },
                    ]
                },
                status=status.HTTP_409_CONFLICT,
            )
