from django import forms
from django.conf import settings
from django.contrib import messages
from django.contrib.admin.models import CHANGE
from django.contrib.admin.models import LogEntry
from django.core.exceptions import ValidationError
from django.urls import reverse

from country_config import Country
from webapps.admin_extra.custom_permissions_classes import FormView
from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin
from webapps.business.models import Business
from webapps.subdomain_grpc.client import SubdomainGRPC
from webapps.subdomain_grpc.client import SubdomainGRPCError
from webapps.user.groups import GroupName, GroupNameV2


class SubdomainSearchByIdForm(forms.Form):
    business_id = forms.IntegerField(
        label='Business id',
        min_value=1,
        required=False,
    )
    country_code = forms.ChoiceField(
        label='Country',
        choices=Country.choices(),
        required=True,
        initial=settings.API_COUNTRY,
    )


class SubdomainSearchByNameForm(forms.Form):
    subdomain = forms.SlugField(label='Subdomain', min_length=4, required=True)

    def clean(self):
        return super().clean() | {'country_code': None}


class SubdomainsSearchView(GroupPermissionMixin, FormView):
    permission_required = ()
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    template_name = 'admin/custom_views/subdomains.html'
    forms = {
        'form_search_by_id': SubdomainSearchByIdForm,
        'form_search_by_name': SubdomainSearchByNameForm,
    }

    def get_context_data(self, **kwargs):
        for form_name, form_class in self.forms.items():
            if form_name not in kwargs:
                kwargs.setdefault('forms', {})[form_name] = self.get_form(form_class)
        return kwargs

    def post(self, request, *args, **kwargs):
        form = None
        context = {'forms': {}}
        for form_name, form_class in self.forms.items():
            if form_name in self.request.POST:
                form = context['forms'][form_name] = self.get_form(form_class)
            else:
                context['forms'][form_name] = form_class(initial=self.get_initial())
                form = form or context['forms'][form_name]

        if form.is_valid():
            try:
                context['data'] = SubdomainGRPC.search(
                    data={'limit': 20, 'master_db': True, **form.cleaned_data}
                )
            except SubdomainGRPCError as e:
                messages.error(request, f'Subdomain grpc error: {e.errors}')
            else:
                if not context['data']:
                    messages.warning(request, 'Not found')

        return self.render_to_response(context)

    def get(self, request, *args, **kwargs):
        context = self.get_context_data()
        return self.render_to_response(context)


readonly_kwargs = {
    'required': False,
    'widget': forms.TextInput(attrs={'readonly': 'readonly'}),
}


class SubdomainEditForm(forms.Form):
    business_id = forms.IntegerField(
        label='Business id',
        min_value=1,
        required=True,
    )
    uuid = forms.CharField(label='id', **readonly_kwargs)
    deeplinks = forms.JSONField(disabled=True, initial='""')
    created = forms.DateTimeField(**readonly_kwargs)
    updated = forms.DateTimeField(**readonly_kwargs)
    subdomain = forms.SlugField(**readonly_kwargs)
    domain = forms.CharField(**readonly_kwargs)
    country_code = forms.CharField(**readonly_kwargs)

    def clean_business_id(self):
        business_id = self.cleaned_data['business_id']
        if Business.objects.filter(pk=business_id).exists():
            return business_id
        raise ValidationError(f'Business with id {business_id} does not exist.')


class SubdomainEditView(GroupPermissionMixin, FormView):
    permission_required = ()
    template_name = 'admin/custom_views/subdomain_edit.html'
    form_class = SubdomainEditForm
    success_url = 'admin:subdomains'
    full_access_groups = (GroupName.SUBDOMAIN_EDITOR,)

    def get_success_url(self):
        return reverse(super().get_success_url())

    def get(self, request, uuid, *args, **kwargs):  # pylint: disable=arguments-differ
        context_kwargs = {}
        try:
            subdomain = SubdomainGRPC.search(
                data={'uuid': uuid, 'master_db': True, 'country_code': None}
            )[0]
        except SubdomainGRPCError as e:
            messages.error(request, f'Subdomain grpc error: {e.errors}')
        except IndexError:
            messages.warning(request, 'Subdomain not found')
        else:
            initial = self.get_initial()
            initial.update(subdomain)
            context_kwargs['form'] = self.form_class(initial=initial)

        return self.render_to_response(self.get_context_data(**context_kwargs))

    def post(self, request, *args, **kwargs):
        form = self.get_form()

        if form.is_valid():
            business_id = form.cleaned_data['business_id']
            try:
                business = Business.objects.get(pk=business_id)
                deeplinks = business.generate_new_deeplinks()
                data = {
                    'uuid': kwargs['uuid'],
                    'business_id': business_id,
                    'country_code': settings.API_COUNTRY,
                    'deeplinks': deeplinks,
                }
                subdomain_data = SubdomainGRPC.edit(data=data)
                subdomain = subdomain_data['subdomain']
            except SubdomainGRPCError as e:
                messages.error(request, f'Subdomain grpc error: {e.errors}')
            except KeyError:
                messages.warning(request, 'Subdomain not found')
            else:
                messages.success(request, f'Subdomain “{subdomain}” was changed successfully.')
                LogEntry.objects.log_action(
                    user_id=request.user.id,
                    content_type_id=None,
                    object_id=None,
                    object_repr=subdomain,
                    action_flag=CHANGE,
                    change_message='edit_subdomain',
                )
                return self.form_valid(form)

        return self.form_invalid(form)
