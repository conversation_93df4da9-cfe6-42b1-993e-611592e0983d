from django.db import models

from lib.models import ArchiveModel
from webapps.onboarding_space.domain.enums import ProfileSetupStepsEnum
from webapps.onboarding_space.domain.models import OnboardingSpaceProgress as OnbSpaceDomainModel


class OnboardingSpaceProgress(ArchiveModel):
    business = models.OneToOneField(
        'business.Business',
        primary_key=True,
        related_name='onboarding_space_progress',
        on_delete=models.CASCADE,
    )
    active_from = models.DateTimeField(
        null=True, blank=True, verbose_name='Business active from (UTC)'
    )
    cover_photo = models.BooleanField(
        null=False,
        blank=False,
        default=OnbSpaceDomainModel.steps_defaults.get(ProfileSetupStepsEnum.ADD_COVER_PHOTO),
    )
    portfolio_count = models.IntegerField(
        null=False,
        blank=False,
        default=OnbSpaceDomainModel.steps_defaults.get(ProfileSetupStepsEnum.ADD_PORTFOLIO),
    )
    service_count = models.IntegerField(
        null=False,
        blank=False,
        default=OnbSpaceDomainModel.steps_defaults.get(ProfileSetupStepsEnum.ADD_SERVICES),
    )
    share_profile = models.BooleanField(
        null=False,
        blank=False,
        default=OnbSpaceDomainModel.steps_defaults.get(ProfileSetupStepsEnum.SHARE_PROFILE),
    )
    social_post_count = models.IntegerField(
        null=False,
        blank=False,
        default=OnbSpaceDomainModel.steps_defaults.get(ProfileSetupStepsEnum.CREATE_POST),
    )
    import_and_invite_count = models.IntegerField(
        null=False,
        blank=False,
        default=OnbSpaceDomainModel.steps_defaults.get(ProfileSetupStepsEnum.IMPORT_AND_INVITE),
    )
    appointment_count = models.IntegerField(null=True, blank=True)
    business_primary_category_internal_name = models.CharField(
        null=True,
        blank=True,
    )
