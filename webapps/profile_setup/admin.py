from django.contrib import admin

from lib.admin_helpers import BaseModelAdmin, ReadOnlyTabular
from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin
from webapps.profile_setup.models import ProfileSetupProgress
from webapps.user.groups import GroupNameV2


class ProfileSetupProgressAdmin(ReadOnlyTabular, GroupPermissionMixin, BaseModelAdmin):
    model = ProfileSetupProgress
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = ()
    list_display = (
        'business',
        'cover_photo',
        'portfolio',
        'business_hours',
        'services',
    )
    list_display_links = ('business',)
    search_fields = (
        'business__id',
        'business__name',
    )
    list_filter = (
        'created',
        'updated',
    )
    exclude = ('deleted',)
    readonly_fields = list_display


admin.site.register(ProfileSetupProgress, ProfileSetupProgressAdmin)
