from django.contrib import admin

from lib.admin_helpers import (
    BaseModelAdmin,
    NoAddDelMixin,
    ReadOnlyFieldsMixin,
)
from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin
from webapps.feeds.models import (
    GrouponBooking,
)
from webapps.user.groups import GroupNameV2


class GrouponBookingAdmin(ReadOnlyFieldsMixin, NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    list_display = [
        'id',
        'business',
        'booking_id',
        'groupon_booking_id',
        'availability_id',
        'booking_type',
        'created',
    ]
    list_filter = ('booking_type',)
    list_per_page = 100
    search_fields = (
        'business__name',
        '=business__id',
        'booking__id',
        'groupon_booking_id',
    )
    raw_id_fields = ['business']
    date_hierarchy = 'created'

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        return qs.select_related('business')


admin.site.register(GrouponBooking, GrouponBookingAdmin)
