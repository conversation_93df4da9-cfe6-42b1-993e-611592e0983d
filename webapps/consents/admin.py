import mimetypes
from email.utils import formatdate
from functools import update_wrapper

from django.contrib import admin
from django.http import Http404, HttpResponse
from django.urls.base import reverse
from django.utils.html import escape, format_html
from django.utils.safestring import mark_safe

from lib.admin_helpers import (
    admin_link,
    BaseModelAdmin,
    NoAddDelMixin,
    ReadOnlyFieldsMixin,
)
from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin
from webapps.consents.fields import empty
from webapps.consents.models import Consent
from webapps.user.groups import GroupNameV2

RETRY_AFTER = 10


def serve_file(
    file, last_modified=None, content_length=None, content_type=None, content_disposition='inline'
):
    if not content_type:
        content_type = mimetypes.guess_type(file.name)[0]
        content_type = content_type or 'application/octet-stream'

    # response = FileResponse(file, content_type=content_type)

    # for some reason FileResponse(file, ...) does not work with nginx
    response = HttpResponse(file.read(), content_type=content_type)

    if content_disposition:
        response['Content-Disposition'] = content_disposition

    if content_length is not None:
        response['Content-Length'] = content_length

    if last_modified:
        response['Last-Modified'] = formatdate(last_modified)

    return response


class ConsentAdmin(NoAddDelMixin, ReadOnlyFieldsMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    model = Consent
    search_fields = (
        '=uuid',
        '=form__business__id',
        '=customer__id',
        '=customer__user__id',
        'customer_full_name',
        'customer_email',
        'customer_cell_phone',
    )
    date_hierarchy = 'created'
    list_display = [
        'uuid',
        'customer',
        'business',
        'form_title',
        'signed',
    ]
    raw_id_fields = (
        'form',
        'customer',
    )
    readonly_fields = [
        'booking',
        'multibooking',
    ]
    fields = [
        'uuid',
        'business',
        'business_name',
        'business_address',
        'form',
        'form_title',
        'form_updated',
        'form_fields',
        'customer',
        'customer_full_name',
        'customer_email',
        'customer_cell_phone',
        'customer_address',
        'signature',
        'photo',
        'booking',
        'multibooking',
        'signed',
        'created',
        'updated',
        'deleted',
    ]

    def customer(self, obj):
        if obj.customer is not None:
            customer = obj.customer
            return format_html(
                '<a href="{}">{}: {}</a>',
                admin_link(customer),
                customer.id,
                escape(obj.customer_full_name or ''),
            )

        return obj.customer_full_name or ''

    def business(self, obj):
        if obj.form is None or obj.form.business is None:
            return ''

        business = obj.form.business
        return format_html(
            '<a href="{}">{}: {} ({})</a>',
            admin_link(business),
            business.id,
            escape(business.name),
            escape(business.owner.email),
        )

    def booking(self, obj):
        if obj.subbooking_id is None:
            return ''

        return format_html(
            '<a href="{}">{}</a>',
            admin_link(obj.booking),
            obj.subbooking_id,
        )

    def multibooking(self, obj):
        if obj.appointment_id is None:
            return ''

        return format_html(
            '<a href="{}">{}</a>',
            admin_link(obj.appointment),
            obj.appointment_id,
        )

    def form_fields(self, obj):
        if not obj.form_fields:
            return ''

        contents = ['<table><tbody>']
        for field in obj.form_fields:
            contents.append(f'<tr><th align="left">{escape(field.label)}</th></tr>')

            if not field.is_empty() and field.value is not empty:
                contents.append(f'<tr><td align="left">{escape(str(field.value))}</td></tr>')
        contents.extend('</tbody></table>')

        return mark_safe(''.join(contents))  # nosemgrep: avoid-mark-safe

    form_fields.short_description = 'Form contents'

    def get_form(self, request, obj=None, **kwargs):  # pylint: disable=arguments-differ
        form = super().get_form(request, obj, **kwargs)

        self.download_pdf_button = self.get_download_pdf_button(obj)

        return form

    def get_readonly_fields(self, request, obj=None):
        readonly_fields = super().get_readonly_fields(request, obj)

        if obj is not None:
            readonly_fields.extend(['business'])

        return readonly_fields

    def get_download_pdf_button(self, obj=None):
        if obj is None:
            return ''
        return format_html(
            '<a href="{}" class="btn">Download PDF</a>',
            reverse(
                'admin:consents_consent_download_pdf',
                kwargs={
                    'object_id': str(obj.uuid),
                },
            ),
        )

    def get_queryset(self, request):
        """
        Return a QuerySet of all model instances that can be edited by the
        admin site. This is used by changelist_view.
        """
        return Consent.all_objects.all()

    def download_pdf_view(
        self, request, object_id, extra_context=None
    ):  # pylint: disable=unused-argument
        obj = self.get_object(request, object_id)
        if obj is None:
            raise Http404(f'Consent with UUID {object_id} does not exist')

        force_update = bool(request.GET.get('force_update', ''))
        try:
            if obj.signed and obj.photo:
                consent_pdf = obj.render_photo_pdf()
            else:
                consent_pdf = obj.render_pdf(force_update=force_update)

            response = serve_file(
                consent_pdf,
                content_type='application/pdf',
                content_disposition=f'inline; filename="{object_id}.pdf"',
            )
            response['Cache-Control'] = 'no-store'

            return response
        except IOError:
            # try returning 503 if there are connection issues with the storage
            response = HttpResponse(status=503)
            response['Retry-After'] = RETRY_AFTER

            return response

    def get_urls(self):
        from django.urls import path

        def wrap(view):
            def wrapper(*args, **kwargs):
                return self.admin_site.admin_view(view)(*args, **kwargs)

            wrapper.model_admin = self
            return update_wrapper(wrapper, view)

        urlpatterns = super().get_urls()
        urlpatterns.insert(
            0,
            path(
                '<path:object_id>/download_pdf/',
                wrap(self.download_pdf_view),
                name='consents_consent_download_pdf',
            ),
        )

        return urlpatterns


admin.site.register(Consent, ConsentAdmin)
