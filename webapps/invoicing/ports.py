import datetime
import typing as t
import uuid
from dataclasses import dataclass
from decimal import Decimal
from typing import Optional

from django.utils.translation import gettext as _

from webapps.invoicing.models import Buyer, CustomerInvoice
from webapps.invoicing.serializers.invoice import get_invoice_serializer_factory
from webapps.pos.enums import PaymentTypeEnum


@dataclass(frozen=True)
class CustomerInvoiceEntity:
    id: int | None
    number: int | None


@dataclass
class ReceiptRowData:
    name: str
    sku: str
    quantity: int
    item_price: Decimal
    tax_rate: Decimal
    transaction_row_id: int
    basket_item_id: Optional[uuid.UUID]


@dataclass
class ReceiptData:
    id: int  # pylint: disable=invalid-name
    receipt_number: str
    transaction_id: int
    basket_id: Optional[uuid.UUID]
    created: datetime.datetime
    is_paid: bool
    payment_type_code: str
    discount_amount: Decimal
    rows: t.List[ReceiptRowData]


@dataclass
class BusinessCustomerInfoData:
    id: int  # pylint: disable=invalid-name
    first_name: str
    last_name: str
    email: str
    address_line_1: str
    address_line_2: str
    city: str
    zipcode: str
    tax_id: str


def _coerce_payment_method(payment_type_code: str) -> str:
    """Helper function that translates PaymentType codes to
    CustomerInvoice.PaymentMethod
    """
    methods = {
        PaymentTypeEnum.CREDIT_CARD: CustomerInvoice.PaymentMethod.CARD,
        PaymentTypeEnum.BANK_TRANSFER: CustomerInvoice.PaymentMethod.TRANSFER,
        PaymentTypeEnum.CASH: CustomerInvoice.PaymentMethod.CASH,
        PaymentTypeEnum.PAY_BY_APP: CustomerInvoice.PaymentMethod.MOBILE_PAYMENT,
    }
    return methods.get(payment_type_code, CustomerInvoice.PaymentMethod.CASH)


def _create_buyer_for_customer(bci_data: BusinessCustomerInfoData) -> Buyer:
    return Buyer.objects.create(
        name=' '.join([bci_data.first_name, bci_data.last_name]),
        address=' '.join([bci_data.address_line_1, bci_data.address_line_2]),
        zip_code=bci_data.zipcode,
        city=bci_data.city,
        tax_id_number=bci_data.tax_id,
        customer_id=bci_data.id,
        email=bci_data.email,
    )


def create_invoice_port(  # pylint: disable=too-many-arguments, too-many-positional-arguments
    business_id: int,
    issuing_staffer_id: int,
    buyer_id: t.Optional[int],
    receipt_data: ReceiptData,
    customer_data: t.Optional[BusinessCustomerInfoData],
    dry_run: bool = False,
) -> dict:
    from webapps.business.models import Business

    business = Business.objects.get(id=business_id)
    internal_description = (_('Invoice for receipt no. {}')).format(receipt_data.receipt_number)
    payment_method = _coerce_payment_method(receipt_data.payment_type_code)

    if not buyer_id and customer_data:
        # Buyer not selected -> try to create one based on customer data
        new_buyer = _create_buyer_for_customer(customer_data)
        buyer_id = new_buyer.id

    serializer_factory = get_invoice_serializer_factory(business_id)
    serializer = serializer_factory.get_customer_invoice_create_serializer(
        data={
            'internal_description': internal_description,
            'issuing_staffer': issuing_staffer_id,
            'issue_date': receipt_data.created.date(),
            'sale_date': receipt_data.created.date(),
            'payment_term': receipt_data.created.date(),
            'payment_method': payment_method,
            'is_paid': receipt_data.is_paid,
            'buyer': buyer_id,
            'transaction': receipt_data.transaction_id,
            'basket_id': receipt_data.basket_id,
            'discount_amount': receipt_data.discount_amount,
            'items': [
                {
                    'name': row.name,
                    'sku': row.sku,
                    'quantity': row.quantity,
                    'unit_symbol': _('piece'),
                    'net_unit_price': row.item_price,
                    'tax_rate': row.tax_rate,
                    'transaction_row': row.transaction_row_id,
                    'basket_item_id': row.basket_item_id,
                }
                for row in receipt_data.rows
            ],
            'how_to_send': 'by_email',
        },
        context={'business': business},
    )
    serializer.is_valid(raise_exception=True)
    if not dry_run:
        invoice = serializer.save()
        return serializer_factory.get_customer_invoice_serializer(instance=invoice).data

    return serializer.data


class CustomerInvoicePort:
    @staticmethod
    def get_customer_invoice_info(basket_id: uuid.UUID) -> CustomerInvoiceEntity:
        """
        Fetch customer invoice details by related basket id

        :param basket_id: ID of the related basket PointOfSale.Basket
        :return: CustomerInvoiceEntity
        """
        customer_invoice = CustomerInvoice.objects.filter(basket_id=basket_id).first()
        return CustomerInvoiceEntity(
            id=customer_invoice.id if customer_invoice else None,
            number=customer_invoice.number if customer_invoice else None,
        )
