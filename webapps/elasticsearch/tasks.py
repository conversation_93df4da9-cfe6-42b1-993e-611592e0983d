import itertools
import logging
from datetime import datetime, timedelta

from billiard.exceptions import SoftTimeLimitExceeded
from celery.exceptions import Retry
from django.conf import settings
from django.db import DatabaseError, OperationalError
from django.utils.encoding import force_str
from elasticsearch.exceptions import ElasticsearchException, TransportError

from lib.celery_tools import celery_task, retry_post_transaction_task
from lib.datadog.celery.resource_name_util import append_current_celery_span_resource_name
from lib.db import PRIMARY_DB, READ_ONLY_DB, using_db_for_reads, REPORTS_READ_ONLY_DB
from lib.elasticsearch.consts import ESDocType
from lib.elasticsearch.tools import es_delete_by_id
from lib.feature_flag.killswitch import AlloyDBReportsReplicaCeleryTaskFlag
from lib.rivers import River, bump_document, pop_document
from lib.tools import chunker, grouper
from webapps.kill_switch.models import KillSwitch

_logger = logging.getLogger('booksy.es_tasks')


def _convert_to_id(value):
    if isinstance(value, int):
        return value
    elif isinstance(value, (bytes, str)):
        return int(force_str(value))
    else:
        return value


def _convert_bytelist_to_list(ids):
    return [
        int(_id.strip()) for _id in force_str(ids).strip('[]').split(',') if _id.strip().isdigit()
    ]


def _fix_pop_document_ids(ids):
    """Temporary code that filters ids returned from pop_document.

    Remove this when source of malformed ids is found.
    """
    # sometimes ids == b'[]' or ids == b'[1, 2, 3]'
    if isinstance(ids, (bytes, str)):
        ids = [ids]

    ids = list(itertools.chain.from_iterable(_convert_bytelist_to_list(element) for element in ids))
    return ids


@celery_task
def es_delete_object(id=None, document_type=None, **_):
    """
    Celery task for deleting ES document of type 'object_type' with id 'object_id'.
    """

    try:
        es_delete_by_id(document_type, id)
    except TransportError:
        _logger.debug('Object %s with ID %s not found' % (document_type, id))


@celery_task
def document_river_task(document_type: River):
    """
    Task for river-like indexing of ES document of type 'document_type'.
    Works by just firing off the river SQL. In future this SQL code could be
    moved here for easier maintainability.
    """
    append_current_celery_span_resource_name(f'__{str(document_type)}')
    if isinstance(document_type, River):
        document_type = document_type.value
    ids = pop_document(document_type)
    ids = _fix_pop_document_ids(ids)
    if ids:
        from webapps.elasticsearch.elastic import ELASTIC

        document = ELASTIC.documents[document_type]
        # split ids into chunks and reindex using celery
        try:
            document.reindex(
                ids,
                use_celery=True,
                verbose=False,
            )
        except (OperationalError, ElasticsearchException, SoftTimeLimitExceeded) as exc:
            bump_document(document_type, ids)
            raise Retry(message='ids were returned to River queue', exc=exc)
    return document_type, len(ids) if ids else 0


@retry_post_transaction_task
@using_db_for_reads(REPORTS_READ_ONLY_DB, condition=AlloyDBReportsReplicaCeleryTaskFlag)
def document_index_task(document_type: ESDocType, ids, _origin=None):
    from webapps.elasticsearch.elastic import ELASTIC

    append_current_celery_span_resource_name(f'__{str(document_type)}')
    document = ELASTIC.documents[document_type]
    ids = [_convert_to_id(val) for val in ids]
    try:
        success, errors = document.reindex(
            ids,
            use_celery=False,
            verbose=False,
            # refresh is expensive operation and shouldn't not be needed on working elasticsearch
            refresh_index=False,
        )
        origin = ':'.join(_f for _f in [document_type, _origin] if _f)
        return origin, success
    except SoftTimeLimitExceeded as exc:
        if len(ids) > 100:
            for package in grouper(ids, 100):
                document_index_task.delay(document_type, package)
        elif len(ids) > 1:
            for id_ in ids:
                document_index_task.delay(document_type, [id_])
        raise TimeoutError(f'document_type: {document_type} with ids:{str(ids)}') from exc


@celery_task
@using_db_for_reads(PRIMARY_DB if settings.REINDEX_FROM_MASTER else READ_ONLY_DB)
def document_update_task(document_type: ESDocType, ids, updates, _origin=None):
    from webapps.elasticsearch.elastic import ELASTIC

    document = ELASTIC.documents[document_type]
    success, errors = document.bulk_update(
        [_convert_to_id(val) for val in ids],
        updates,
        use_celery=False,
        verbose=False,
        # refresh is expensive operation and shouldn't not be needed on working elasticsearch
        refresh_index=False,
    )
    origin = ':'.join([_f for _f in [document_type, _origin] if _f])
    return origin, success


@celery_task(
    default_retry_delay=20,
    autoretry_for=(DatabaseError,),
    retry_kwargs={'max_retries': 3},
)
@using_db_for_reads(READ_ONLY_DB)
def BusinessAvailabilityRiverTask():
    """Availability River Task.
    Update Business availability slots in elasticsearch
    """
    from webapps.business.models import Business

    ids = pop_document(River.AVAILABILITY)
    ids = _fix_pop_document_ids(ids)
    if ids:
        ids = list(
            Business.objects.filter(
                id__in=ids,
            )
            .indexed_in_elasticsearch()
            .values_list(
                'id',
                flat=True,
            )
        )
        # update instead of reindex
        batch_size = 100
        for n, batch in enumerate(chunker(ids, batch_size)):
            bulk_update_business_availability_task.apply_async((batch,), countdown=n * 5 % 300)
        # from webapps.elasticsearch.elastic import ELASTIC
        # document = ELASTIC.documents[document_type]
        # split ids into chunks and reindex using celery
        # document.reindex(ids, use_celery=True, verbose=False)
        bump_document(River.AVAILABILITY_INDEXED, ids)
    return len(ids) if ids else 0


@celery_task(
    time_limit=16 * 60,
    soft_time_limit=15 * 60,
    default_retry_delay=20,
    autoretry_for=(DatabaseError,),
    retry_kwargs={'max_retries': 3},
)
@using_db_for_reads(READ_ONLY_DB)
def all_business_availability_task():
    """
    Index availability for all businesses not indexed in the last day
    """
    from webapps.business.models import Business

    # Get all businesses for which availability was not calculated during the day
    all_businesses = set(Business.objects.indexed_in_elasticsearch().values_list('id', flat=True))
    indexed = pop_document(River.AVAILABILITY_INDEXED, num_docs=1_000_000)
    indexed = _fix_pop_document_ids(indexed)
    to_index = all_businesses - set(indexed)

    total = 0
    # the package could be big so divide it
    batch_size = 100
    for counter, chunk_businesses in enumerate(chunker(to_index, batch_size), 1):

        bulk_update_business_availability_task.apply_async(
            kwargs=dict(
                business_ids=chunk_businesses,
            ),
            # do not spawn all task simultaneously
            countdown=counter * 10,
        )
        total += len(chunk_businesses)
    return total


@celery_task
def CleanOldBusinessAvailabilityTask():
    """Clean business_availability documents older than now."""
    from webapps.business.searchables.business import PastAvailabilitySearchable

    days_ago = 1
    cutoff = datetime.now() - timedelta(days=days_ago)

    PastAvailabilitySearchable(
        ESDocType.AVAILABILITY,
    ).search(
        dict(
            date=cutoff.strftime(settings.ES_DATE_FORMAT),
            date_active_till=cutoff.strftime(settings.ES_DATETIME_FORMAT),
        )
    ).delete()


@celery_task
def fix_out_of_sync_docs_task(check_region=False):
    from cliapps.es.reindex import resolve_generic_document_types

    document_types = 'ALL' if check_region else 'NON-REGION'
    for i, document_type in enumerate(resolve_generic_document_types(document_types)):
        # in case of increasing the gap, please keep celery 'visibility_timeout' setting in mind
        countdown = 15 * 60 * i
        fix_out_of_sync_single_doc_type_task.apply_async(
            args=(document_type,),
            countdown=countdown,
            expires=60 * 60 * 23,
        )


@celery_task(time_limit=16 * 60, soft_time_limit=15 * 60)
def fix_out_of_sync_single_doc_type_task(document_type: str):
    from cliapps.es.fix import main as fix

    fix(
        document_types=[document_type],
        delete=True,
        reindex=True,
    )


@celery_task
@using_db_for_reads(READ_ONLY_DB)
def update_business_availability_task(business_id):
    """
    Compute and update availability for single business in ElasticSearch.

    :param business_id: int. pk for Business
    :return: None
    """
    from django.db.models.query import Prefetch

    from lib.elasticsearch.tools import get_by_id
    from webapps.business.elasticsearch.availability import (
        BusinessAvailabilitySerialializer,
        BusinessAvailabilityPerCategorySerializer,
    )
    from webapps.business.models import Business
    from webapps.business.models.category import BusinessCategory
    from webapps.utt.models import Category, Treatment

    business = (
        Business.objects.filter(id=business_id)
        .indexed_in_elasticsearch()
        .prefetch_related(
            Prefetch('categories', queryset=BusinessCategory.objects.only('id', 'type')),
            Prefetch('treatments', queryset=BusinessCategory.objects.only('id', 'type')),
            Prefetch('categories_utt', queryset=Category.objects.only('id')),
            Prefetch('treatments_utt', queryset=Treatment.objects.only('id', 'level')),
        )
        .only(
            'id',
            'booking_min_lead_time',
            'booking_max_lead_time',
            'time_zone_name',
            'active',
            'visible',
        )
        .first()
    )
    if business is None:
        # business do not satisfy search criteria
        # see indexed_in_elasticsearch
        return
    business_doc = get_by_id(business_id, ESDocType.BUSINESS)
    if business_doc is not None:
        serializer = BusinessAvailabilityPerCategorySerializer(
            read_only=True, source='*', context=dict(use_max_lead_time=True)
        )
        availability = serializer.to_representation(instance=business)
        update_data = dict(
            availability=availability,
        )

        if KillSwitch.alive(KillSwitch.System.UTT2_BACKEND):
            serializer = BusinessAvailabilitySerialializer(
                read_only=True, source='*', context=dict(use_max_lead_time=True)
            )
            availability_utt = serializer.to_representation(instance=business)
            update_data['availability_utt'] = availability_utt

        business_doc.update(**update_data)
        # This will be deleted in the future. See #71805.
        # update_availability_from_es.delay(business_id)
    elif business.active and business.visible:
        business.reindex()


# exceptional 15minutes hard limit because BusinessAvailabilityRiverTask
# is fired every 15 minutes
@celery_task(
    time_limit=15 * 60,
    soft_time_limit=14 * 60,
    default_retry_delay=20,
    autoretry_for=(DatabaseError,),
    retry_kwargs={'max_retries': 3},
)
@using_db_for_reads(READ_ONLY_DB)
def bulk_update_business_availability_task(business_ids):
    """
    BULK. Compute and update availability for all businesses in ElasticSearch.

    :param business_ids: list. of pk for Business
    :return: None
    """
    from django.db.models.query import Prefetch

    from lib.searchables.common import IdsSearchable
    from lib.tools import benchmark
    from webapps.business.elasticsearch import BusinessDocument
    from webapps.business.elasticsearch.availability import (
        BusinessAvailabilitySerialializer,
        BusinessAvailabilityPerCategorySerializer,
    )
    from webapps.business.models import Business
    from webapps.business.models.category import BusinessCategory
    from webapps.search_engine_tuning.tasks import update_availability_bulk_task
    from webapps.utt.models import Category, Treatment

    es_query = (
        IdsSearchable(
            ESDocType.BUSINESS,
        )
        .params(
            _source_includes=['id'],
            size=len(business_ids),
        )
        .search(dict(ids=business_ids))
    )
    resp = es_query.execute()
    # get all business existing in es
    # because we want to make update
    existing_business_ids = []
    for doc in resp.hits:
        try:
            existing_business_ids.append(int(doc.id))
        except ValueError:
            # routing for business changed
            # parse and use 'id'
            continue
    total = 0
    with benchmark(_logger, 'FETCH_AVAILABILITY'):
        for chunk_businesses in grouper(existing_business_ids, 100):
            # we split by 100 and split here
            # because elasticsearch has 2 seconds timeout
            # so if we  try to through 1000 in 2 seconds it will
            # through timeout
            prefetch_fields = [
                Prefetch('categories', queryset=BusinessCategory.objects.only('id', 'type')),
                Prefetch('treatments', queryset=BusinessCategory.objects.only('id', 'type')),
            ]
            if KillSwitch.alive(KillSwitch.System.UTT2_BACKEND):
                prefetch_fields += [
                    Prefetch('categories_utt', queryset=Category.objects.only('id')),
                    Prefetch('treatments_utt', queryset=Treatment.objects.only('id', 'level')),
                ]

            businesses = (
                Business.objects.filter(
                    id__in=chunk_businesses,
                )
                .indexed_in_elasticsearch()
                .exclude(
                    status__in=[
                        Business.Status.VENUE,
                        Business.Status.B_LISTING,
                    ],
                )
                .prefetch_related(*prefetch_fields)
                .only(
                    'id',
                    'booking_min_lead_time',
                    'booking_max_lead_time',
                    'time_zone_name',
                )
            )
            updates = []
            ids = []

            for business in businesses:
                # always create  new instance of serializer
                # for each business because  of cached properties
                serializer = BusinessAvailabilityPerCategorySerializer(
                    read_only=True, source='*', context=dict(use_max_lead_time=True)
                )
                availability = serializer.to_representation(instance=business)
                update_data = dict(
                    id=business.id,
                    availability=availability,
                )

                if KillSwitch.alive(KillSwitch.System.UTT2_BACKEND):
                    serializer_utt = BusinessAvailabilitySerialializer(
                        read_only=True, source='*', context=dict(use_max_lead_time=True)
                    )
                    availability_utt = serializer_utt.to_representation(instance=business)
                    update_data['availability_utt'] = availability_utt

                # make sure same ids
                ids.append(business.id)
                updates.append(update_data)

            total += len(chunk_businesses)
            success, errors = BusinessDocument().bulk_update(
                ids=ids,
                updates=updates,
                use_celery=False,
                verbose=False,
                refresh_index=False,
            )
            if errors:
                _logger.error('ERRORS in update availability %s' % len(errors))
    # sync
    for business_ids in grouper(existing_business_ids, 10):
        update_availability_bulk_task.delay(business_ids)
