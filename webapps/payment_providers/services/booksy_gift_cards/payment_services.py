from lib.payment_providers.entities import BooksyGiftCardsAuthAdditionalDataEntity
from lib.payment_providers.enums import PaymentStatus
from lib.payments.enums import PaymentError
from webapps.payment_providers.models import TokenizedPaymentMethod
from webapps.payment_providers.models.common import Payment
from webapps.payment_providers.services.base import BasePaymentServices
from webapps.pos.booksy_gift_cards.ports import use_booksy_gift_card


class BooksyGiftCardsPaymentServices(BasePaymentServices):
    @staticmethod
    def initialize_payment(
        payment: Payment, payment_token: str = None
    ):  # pylint: disable=unused-argument
        return None

    @staticmethod
    def capture_payment(
        payment: Payment,
    ) -> Payment:
        from webapps.payment_providers.services.common import (  # pylint: disable=cyclic-import
            CommonPaymentServices,
        )

        CommonPaymentServices.update_status(
            payment=payment,
            status=PaymentStatus.CAPTURED,
        )
        return payment

    @staticmethod
    def authorize_payment(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        payment: Payment,
        tokenized_pm: TokenizedPaymentMethod = None,
        additional_data: BooksyGiftCardsAuthAdditionalDataEntity | None = None,
        payment_token: str = None,
        gift_cards_ids: list[str] = None,
        off_session: bool | None = None,
    ) -> (Payment, PaymentError | None):
        # pylint: disable=cyclic-import
        from webapps.payment_providers.services.common import CommonPaymentServices

        success = use_booksy_gift_card(
            user_id=additional_data.user_id,
            gift_cards_ids=gift_cards_ids,
            gift_card_hold=payment.amount,
        )
        if success:
            CommonPaymentServices.update_status(
                payment=payment,
                status=PaymentStatus.AUTHORIZED,
                error_code=None,
            )

        return (
            payment,
            PaymentError.BOOKSY_GIFT_CARD_PAYMENT_FAILED if not success else None,
        )

    @staticmethod
    def update_account_holder(
        account_holder: None,  # pylint: disable=unused-argument
    ):
        return None

    @staticmethod
    def get_account_holder_info(account_holder):  # pylint: disable=unused-argument
        return None

    @staticmethod
    def get_provider_payment_details(payment: Payment) -> str | None:
        return None
