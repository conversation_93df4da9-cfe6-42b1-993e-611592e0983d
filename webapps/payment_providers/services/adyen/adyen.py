from typing import Optional

from lib.payment_providers.entities import (
    AdyenAuthAdditionalDataEntity,
)
from lib.payments.enums import PaymentError
from webapps.payment_providers.models import (
    AdyenPayment,
    Payment,
    TokenizedPaymentMethod,
)
from webapps.payment_providers.services.base import (
    BasePaymentServices,
)


class AdyenPaymentServices(BasePaymentServices):
    @staticmethod
    def mark_payment_as_failed(
        payment: Payment,
        error_code: Optional[str] = None,
    ) -> None:
        raise NotImplementedError

    @staticmethod
    def modify_payment(
        payment: Payment,
        fee_amount: int,
    ) -> None:
        raise NotImplementedError

    @staticmethod
    def get_payment_client_token(payment: Payment) -> str:
        raise NotImplementedError

    @staticmethod
    def get_provider_payment_details(payment: Payment) -> str | None:
        return None

    @staticmethod
    def initialize_payment(payment: Payment, payment_token: str = None) -> AdyenPayment:
        """
        Create adyen payment based on common Payment
        """
        adyen_payment = AdyenPayment.objects.create(
            payment=payment,
        )

        return adyen_payment

    @staticmethod
    def authorize_payment(  # pylint: disable=signature-differs, too-many-arguments, too-many-positional-arguments
        payment: Payment,
        tokenized_pm: TokenizedPaymentMethod,
        additional_data: AdyenAuthAdditionalDataEntity = None,
        payment_token: str = None,
        gift_cards_ids: list[str] = None,
        off_session: bool | None = None,
    ) -> (Payment, PaymentError | None):
        return payment, None

    @staticmethod
    def capture_payment(
        payment: Payment,
    ) -> Payment:
        return payment

    @staticmethod
    def cancel_payment(payment: Payment) -> bool:
        return True
