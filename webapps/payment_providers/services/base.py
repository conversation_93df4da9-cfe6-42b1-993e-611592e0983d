from abc import ABC, abstractmethod
from datetime import datetime
from typing import Type, Union, Optional

from lib.enums import PaymentMethodType
from lib.models import UUIDArchiveModel
from lib.payment_providers.entities import (
    AccountStatusResponse,
    ConcreteAccountHolderEntity,
    ConcreteAccountHolderSettingsEntity,
    CreateStripeAccountHolderAdditionalData,
    PayoutDetailsEntity,
    PayoutMethodDetails,
    ProviderAccountDetails,
    ProviderAccountStatus,
    StripeAccountHolderModifyData,
)
from lib.payments.enums import PaymentError, PayoutType
from webapps.payment_providers.models import (
    AccountHolder,
    Customer,
    Payment,
    PaymentOperation,
    Payout,
    SetupIntent,
    TokenizedPaymentMethod,
    TransferFund,
)


class BaseAccountHolderServices(ABC):
    @staticmethod
    @abstractmethod
    def create_account_holder_from_external_source(
        account_holder: AccountHolder,
        additional_data,
    ): ...

    @staticmethod
    @abstractmethod
    def create_account_holder_with_user_input_kyc(
        account_holder: AccountHolder,
        additional_data,
    ): ...

    @staticmethod
    @abstractmethod
    def create_account_holder(
        account_holder: AccountHolder,
        additional_data: CreateStripeAccountHolderAdditionalData | None,
    ) -> Type[UUIDArchiveModel]: ...

    @staticmethod
    @abstractmethod
    def get_account_holder_info(
        account_holder: AccountHolder,
    ) -> Type[ConcreteAccountHolderEntity]: ...

    @staticmethod
    @abstractmethod
    def get_provider_account_online_balance(
        account_holder: AccountHolder,
    ): ...

    @staticmethod
    @abstractmethod
    def get_account_holder_settings(
        account_holder: AccountHolder,
    ) -> Type[ConcreteAccountHolderSettingsEntity]: ...

    @staticmethod
    @abstractmethod
    def set_account_holder_settings(
        account_holder: AccountHolder,
        account_holder_settings_entity: ConcreteAccountHolderSettingsEntity,
    ) -> Type[ConcreteAccountHolderSettingsEntity]: ...

    @staticmethod
    @abstractmethod
    def get_provider_account_details(
        account_holder: AccountHolder,
    ) -> ProviderAccountDetails | None: ...

    @staticmethod
    @abstractmethod
    def get_provider_account_status(
        account_holder: AccountHolder,
    ) -> ProviderAccountStatus | None: ...

    @staticmethod
    @abstractmethod
    def add_provider_payout_method(
        account_holder: AccountHolder,
        token: str,
    ) -> [PayoutMethodDetails, None]: ...

    @staticmethod
    @abstractmethod
    def remove_provider_payout_method(
        account_holder: AccountHolder,
        token: str,
    ) -> None: ...

    @staticmethod
    @abstractmethod
    def set_provider_payout_method_as_default(
        account_holder: AccountHolder,
        payout_type: PayoutType,
        token: str,
    ) -> None: ...

    @staticmethod
    @abstractmethod
    def get_kyc_link(
        account_holder: AccountHolder,
        refresh_url: str,
        return_url: str,
    ) -> str: ...

    @staticmethod
    @abstractmethod
    def update_account_holder(account_holder: AccountHolder) -> Type[UUIDArchiveModel]: ...

    @staticmethod
    @abstractmethod
    def update_provider_account_holder(
        account_holder: AccountHolder,
        data: Union[StripeAccountHolderModifyData],
    ): ...

    @staticmethod
    @abstractmethod
    def get_account_holder_status_info(
        account_holder: AccountHolder,
    ) -> AccountStatusResponse | None: ...


class BaseCustomerServices(ABC):
    @staticmethod
    @abstractmethod
    def get_or_create_customer(customer: Customer) -> Type[UUIDArchiveModel]: ...

    @staticmethod
    @abstractmethod
    def update_customer(customer: Customer) -> Type[UUIDArchiveModel]: ...


class BasePaymentServices(ABC):

    @staticmethod
    @abstractmethod
    def initialize_payment(
        payment: Payment, payment_token: str = None
    ) -> Type[UUIDArchiveModel]: ...

    @staticmethod
    @abstractmethod
    def authorize_payment(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        payment: Payment,
        tokenized_pm: TokenizedPaymentMethod = None,
        additional_data=None,
        payment_token: str = None,
        gift_cards_ids: list[str] = None,
        off_session: bool | None = None,
    ) -> (Payment, PaymentError | None): ...

    @staticmethod
    @abstractmethod
    def capture_payment(payment: Payment) -> Type[UUIDArchiveModel]: ...

    @staticmethod
    @abstractmethod
    def cancel_payment(payment: Payment) -> bool: ...

    @staticmethod
    @abstractmethod
    def get_payment_client_token(payment: Payment) -> str: ...

    @staticmethod
    @abstractmethod
    def get_provider_payment_details(payment: Payment) -> str: ...

    @staticmethod
    @abstractmethod
    def mark_payment_as_failed(
        payment: Payment,
        error_code: Optional[str] = None,
    ) -> None: ...

    @staticmethod
    @abstractmethod
    def modify_payment(
        payment: Payment,
        fee_amount: int,
    ) -> None: ...


class BasePaymentMethodServices(ABC):
    @staticmethod
    @abstractmethod
    def add_tokenized_pm(
        tokenized_pm: TokenizedPaymentMethod,
        external_data: dict,
    ): ...

    @staticmethod
    @abstractmethod
    def remove_tokenized_pm(
        tokenized_pm: TokenizedPaymentMethod,
        make_psp_call: bool,
    ): ...

    @staticmethod
    @abstractmethod
    def initialize_setup_intent(
        setup_intent: SetupIntent,
        customer: Customer,
        method_type: PaymentMethodType,
    ) -> str: ...

    @staticmethod
    @abstractmethod
    def create_payment_token(
        external_data: dict,
    ) -> str: ...


class BaseTransferFundServices(ABC):
    @staticmethod
    @abstractmethod
    def initialize_transfer_fund(
        transfer_fund: TransferFund,
    ) -> Type[UUIDArchiveModel]: ...

    @staticmethod
    @abstractmethod
    def process_transfer_fund(
        transfer_fund: TransferFund,
        additional_data=None,
        refund_fee_metadata: dict[str, str] = None,
        metadata: dict[str, str] | None = None,
    ) -> Type[UUIDArchiveModel]: ...

    @staticmethod
    @abstractmethod
    def get_provider_transfer_fund_details(
        transfer_fund: TransferFund,
    ) -> (str, datetime): ...


class BasePaymentOperationServices(ABC):
    @staticmethod
    @abstractmethod
    def send_for_refund(
        payment: Payment,
        payment_operation: PaymentOperation,
    ) -> Type[UUIDArchiveModel]: ...

    @staticmethod
    @abstractmethod
    def process_dispute(
        payment_operation: PaymentOperation,
        external_id: str,
    ): ...


class BasePayoutServices(ABC):
    @staticmethod
    @abstractmethod
    def add_payout(payout: Payout, external_id: str, metadata: dict) -> Type[UUIDArchiveModel]: ...

    @staticmethod
    @abstractmethod
    def get_available_payout_amount(account_holder: AccountHolder) -> dict: ...

    @staticmethod
    @abstractmethod
    def initialize_payout(
        account_holder: AccountHolder,
        amount: int,
        payout_type: PayoutType,
    ) -> tuple: ...

    @staticmethod
    @abstractmethod
    def get_payout_details(payout: Payout) -> PayoutDetailsEntity: ...


class BaseTerminalServices(ABC):
    @staticmethod
    @abstractmethod
    def get_terminal_connection_token() -> str: ...
