# pylint: disable=protected-access
import dataclasses

import mock
import pytest
from django.test import TestCase
from model_bakery import baker
from parameterized import parameterized

from lib.payment_providers.entities import AuthorizePaymentMethodDataEntity
from lib.payment_providers.enums import (
    PaymentMethodType,
    PaymentStatus,
)
from lib.payments.enums import (
    BGCReportingCategoryEnum,
    PaymentError,
    PaymentProviderCode,
)
from lib.tools import tznow
from webapps.payment_providers.exceptions.common import (
    AlreadyAuthorizedException,
    AlreadyCapturedException,
    AlreadySentForAuthorizationException,
    AlreadySentForCaptureException,
    MissingCustomer,
    WrongPaymentActionException,
)
from webapps.payment_providers.models import (
    AccountHolder,
    Customer,
    Payment,
    TokenizedPaymentMethod,
)
from webapps.payment_providers.ports.payment_ports import PaymentProvidersPaymentPort
from webapps.payment_providers.services.common import CommonPaymentServices


def patch_get_payment_service_class():
    return mock.patch(
        'webapps.payment_providers.services.common.CommonPaymentServices._get_service_class',
    )


def patch_payment_providers_payment_updated_event():
    return mock.patch(
        'lib.payment_providers.events.payment_providers_payment_updated_event.send',
    )


@pytest.mark.django_db
class TestCommonPayments(TestCase):
    def setUp(self):
        super().setUp()
        self.customer = baker.make(
            Customer,
        )
        self.account_holder = baker.make(
            AccountHolder,
        )
        self.amount = 1000
        self.fee_amount = 100

    @patch_get_payment_service_class()
    def test_initialize_payment_port(self, _get_payment_service_class_mock):
        payment_method = PaymentMethodType.TERMINAL
        payment_provider = PaymentProviderCode.STRIPE
        response = PaymentProvidersPaymentPort.initialize_payment(
            account_holder_id=self.account_holder.id,
            payment_provider_code=payment_provider,
            payment_method_type=payment_method,
            amount=self.amount,
            fee_amount=self.fee_amount,
            auto_capture=True,
            customer_id=self.customer.id,
            payment_token=None,
        )
        payment = Payment.objects.get(id=response.entity.id)
        self.assertDictEqual(
            dataclasses.asdict(response)['entity'],
            {
                'id': payment.id,
                'status': PaymentStatus.NEW,
                'amount': self.amount,
                'fee_amount': self.fee_amount,
                'additional_data': {},
                'error_code': None,
                'action_required_details': None,
                'metadata': {},
                'auto_capture': True,
                'provider_external_id': None,
                'tokenized_pm_id': None,
            },
        )

        self.assertEqual(payment.account_holder, self.account_holder)
        self.assertEqual(payment.customer, self.customer)
        self.assertEqual(payment.status, PaymentStatus.NEW)
        self.assertEqual(payment.amount, self.amount)
        self.assertEqual(payment.payment_method, payment_method)
        self.assertEqual(payment.provider_code, payment_provider)
        self.assertEqual(_get_payment_service_class_mock.call_count, 1)
        _get_payment_service_class_mock.return_value.initialize_payment.assert_called_once()

    def test_get_payment(self):
        payment = baker.make(
            Payment,
            account_holder=self.account_holder,
            customer=self.customer,
            amount=100,
            fee_amount=20,
            additional_data={'some': 'data'},
        )
        received_payment = PaymentProvidersPaymentPort.get_payment(payment.id)
        self.assertFalse(received_payment.errors)
        self.assertDictEqual(
            dataclasses.asdict(received_payment)['entity'],
            {
                'id': payment.id,
                'status': 'new',
                'amount': 100,
                'fee_amount': 20,
                'additional_data': {'some': 'data'},
                'error_code': None,
                'action_required_details': None,
                'metadata': {},
                'auto_capture': False,
                'provider_external_id': None,
                'tokenized_pm_id': None,
            },
        )

    @mock.patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    def test_get_payment_with_tokenized_pm(self, _):
        tokenized_pm = baker.make(TokenizedPaymentMethod, provider_code=PaymentProviderCode.STRIPE)
        payment = baker.make(
            Payment,
            provider_code=PaymentProviderCode.STRIPE,
            account_holder=self.account_holder,
            customer=self.customer,
            amount=100,
            fee_amount=20,
            additional_data={'some': 'data'},
            tokenized_payment_method=tokenized_pm,
        )
        received_payment = PaymentProvidersPaymentPort.get_payment(payment.id)
        self.assertFalse(received_payment.errors)
        self.assertDictEqual(
            dataclasses.asdict(received_payment)['entity'],
            {
                'id': payment.id,
                'status': 'new',
                'amount': 100,
                'fee_amount': 20,
                'additional_data': {'some': 'data'},
                'error_code': None,
                'action_required_details': None,
                'metadata': {},
                'auto_capture': False,
                'provider_external_id': None,
                'tokenized_pm_id': tokenized_pm.id,
            },
        )

    @patch_payment_providers_payment_updated_event()
    @patch_get_payment_service_class()
    @mock.patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    def test_authorize_payment_port(
        self,
        _,
        _get_payment_service_class_mock,
        _payment_update_signal_mock,
    ):
        payment = baker.make(
            Payment,
            status=PaymentStatus.NEW,
            payment_method=PaymentMethodType.CARD,
            customer=self.customer,
            account_holder=self.account_holder,
            amount=self.amount,
            fee_amount=self.fee_amount,
        )
        _get_payment_service_class_mock.return_value.authorize_payment.return_value = payment, None
        PaymentProvidersPaymentPort.authorize_payment(
            payment_id=payment.id,
            payment_method_data=AuthorizePaymentMethodDataEntity(
                tokenized_pm_id=baker.make(TokenizedPaymentMethod).id,
            ),
        )
        self.assertEqual(_get_payment_service_class_mock.call_count, 1)
        _get_payment_service_class_mock.return_value.authorize_payment.assert_called_once()

        self.assertEqual(_payment_update_signal_mock.call_count, 1)

        payment.refresh_from_db()
        self.assertEqual(payment.status, PaymentStatus.SENT_FOR_AUTHORIZATION)

    @patch_payment_providers_payment_updated_event()
    @patch_get_payment_service_class()
    @mock.patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    def test_authorize_payment_port_blik_no_customer(
        self,
        _,
        _get_payment_service_class_mock,
        _payment_update_signal_mock,
    ):
        payment = baker.make(
            Payment,
            status=PaymentStatus.NEW,
            payment_method=PaymentMethodType.BLIK,
            account_holder=self.account_holder,
            amount=self.amount,
            fee_amount=self.fee_amount,
        )
        _get_payment_service_class_mock.return_value.authorize_payment.return_value = payment, None
        PaymentProvidersPaymentPort.authorize_payment(
            payment_id=payment.id,
            payment_method_data=AuthorizePaymentMethodDataEntity(
                payment_token='123456',
            ),
        )
        payment.refresh_from_db()

        self.assertEqual(_get_payment_service_class_mock.call_count, 1)
        _get_payment_service_class_mock.return_value.authorize_payment.assert_called_once()

        self.assertEqual(_payment_update_signal_mock.call_count, 1)

        self.assertEqual(payment.status, PaymentStatus.SENT_FOR_AUTHORIZATION)

    @patch_payment_providers_payment_updated_event()
    @patch_get_payment_service_class()
    @mock.patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    def test_authorize_payment_port_blik_no_code(
        self,
        _,
        _get_payment_service_class_mock,
        _payment_update_signal_mock,
    ):
        payment = baker.make(
            Payment,
            status=PaymentStatus.NEW,
            payment_method=PaymentMethodType.BLIK,
            account_holder=self.account_holder,
            amount=self.amount,
            fee_amount=self.fee_amount,
        )
        _get_payment_service_class_mock.return_value.authorize_payment.return_value = payment, None
        PaymentProvidersPaymentPort.authorize_payment(
            payment_id=payment.id,
            payment_method_data=AuthorizePaymentMethodDataEntity(
                payment_token='',
            ),
        )
        payment.refresh_from_db()

        self.assertEqual(payment.error_code, PaymentError.GENERIC_ERROR)

    @patch_get_payment_service_class()
    @mock.patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    @patch_payment_providers_payment_updated_event()
    def test_authorize_payment_port_wrong_status_exceptions(
        self, _, __, _get_payment_service_class_mock
    ):
        payment = baker.make(
            Payment,
            status=PaymentStatus.SENT_FOR_AUTHORIZATION,
            payment_method=PaymentMethodType.CARD,
            customer=self.customer,
            account_holder=self.account_holder,
            amount=self.amount,
            fee_amount=self.fee_amount,
        )
        _get_payment_service_class_mock.return_value.authorize_payment.return_value = payment, None
        with self.assertRaises(AlreadySentForAuthorizationException):
            PaymentProvidersPaymentPort.authorize_payment(
                payment_id=payment.id,
                payment_method_data=AuthorizePaymentMethodDataEntity(
                    tokenized_pm_id=baker.make(TokenizedPaymentMethod).id,
                ),
            )

        payment = baker.make(
            Payment,
            status=PaymentStatus.AUTHORIZED,
            payment_method=PaymentMethodType.CARD,
            customer=self.customer,
            account_holder=self.account_holder,
            amount=self.amount,
            fee_amount=self.fee_amount,
        )
        _get_payment_service_class_mock.return_value.authorize_payment.return_value = payment, None
        with self.assertRaises(AlreadyAuthorizedException):
            PaymentProvidersPaymentPort.authorize_payment(
                payment_id=payment.id,
                payment_method_data=AuthorizePaymentMethodDataEntity(
                    tokenized_pm_id=baker.make(TokenizedPaymentMethod).id,
                ),
            )

        payment = baker.make(
            Payment,
            status=PaymentStatus.CAPTURED,
            payment_method=PaymentMethodType.CARD,
            customer=self.customer,
            account_holder=self.account_holder,
            amount=self.amount,
            fee_amount=self.fee_amount,
        )
        _get_payment_service_class_mock.return_value.authorize_payment.return_value = payment, None
        with self.assertRaises(WrongPaymentActionException):
            PaymentProvidersPaymentPort.authorize_payment(
                payment_id=payment.id,
                payment_method_data=AuthorizePaymentMethodDataEntity(
                    tokenized_pm_id=baker.make(TokenizedPaymentMethod).id,
                ),
            )

    @patch_get_payment_service_class()
    @patch_payment_providers_payment_updated_event()
    @mock.patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    def test_authorize_payment_port_missing_customer(self, *args):
        payment = baker.make(
            Payment,
            status=PaymentStatus.SENT_FOR_AUTHORIZATION,
            payment_method=PaymentMethodType.CARD,
            account_holder=self.account_holder,
            amount=self.amount,
            fee_amount=self.fee_amount,
        )
        with self.assertRaises(MissingCustomer):
            PaymentProvidersPaymentPort.authorize_payment(
                payment_id=payment.id,
                payment_method_data=AuthorizePaymentMethodDataEntity(
                    tokenized_pm_id=baker.make(TokenizedPaymentMethod).id,
                ),
            )

    @patch_get_payment_service_class()
    @patch_payment_providers_payment_updated_event()
    def test_authorize_payment_tokenized_payment_method_exception(
        self,
        _,
        __,
    ):
        payment = baker.make(
            Payment,
            status=PaymentStatus.NEW,
            payment_method=PaymentMethodType.CARD,
            customer=self.customer,
            account_holder=self.account_holder,
            amount=self.amount,
            fee_amount=self.fee_amount,
        )

        payment_entity = PaymentProvidersPaymentPort.authorize_payment(
            payment_id=payment.id,
            payment_method_data=AuthorizePaymentMethodDataEntity(
                payment_token=None,
            ),
        )
        self.assertEqual(
            payment_entity.entity.error_code,
            PaymentError.GENERIC_ERROR,
        )

    @patch_get_payment_service_class()
    @patch_payment_providers_payment_updated_event()
    def test_authorize_payment_port_missing_required_data(
        self,
        _,
        __,
    ):
        payment = baker.make(
            Payment,
            status=PaymentStatus.NEW,
            payment_method=PaymentMethodType.APPLE_PAY,
            customer=self.customer,
            account_holder=self.account_holder,
            amount=self.amount,
            fee_amount=self.fee_amount,
        )

        payment_entity = PaymentProvidersPaymentPort.authorize_payment(
            payment_id=payment.id,
            payment_method_data=AuthorizePaymentMethodDataEntity(
                payment_token=None,
            ),
        )
        self.assertEqual(
            payment_entity.entity.error_code,
            PaymentError.GENERIC_ERROR,
        )

    @patch_payment_providers_payment_updated_event()
    @patch_get_payment_service_class()
    def test_capture_payment_port(
        self,
        _get_payment_service_class_mock,
        _payment_update_signal_mock,
    ):
        payment = baker.make(
            Payment,
            status=PaymentStatus.AUTHORIZED,
            customer=self.customer,
            account_holder=self.account_holder,
            amount=self.amount,
            fee_amount=self.fee_amount,
        )
        PaymentProvidersPaymentPort.capture_payment(payment.id)
        self.assertEqual(_get_payment_service_class_mock.call_count, 1)
        _get_payment_service_class_mock.return_value.capture_payment.assert_called_once()

        self.assertEqual(_payment_update_signal_mock.call_count, 1)

        payment.refresh_from_db()
        self.assertEqual(payment.status, PaymentStatus.SENT_FOR_CAPTURE)

    @patch_get_payment_service_class()
    def test_capture_payment_port_wrong_status_exceptions(self, _get_payment_service_class_mock):
        payment = baker.make(
            Payment,
            status=PaymentStatus.SENT_FOR_CAPTURE,
            customer=self.customer,
            account_holder=self.account_holder,
            amount=self.amount,
            fee_amount=self.fee_amount,
        )
        with self.assertRaises(AlreadySentForCaptureException):
            PaymentProvidersPaymentPort.capture_payment(payment.id)

        payment = baker.make(
            Payment,
            status=PaymentStatus.CAPTURED,
            customer=self.customer,
            account_holder=self.account_holder,
            amount=self.amount,
            fee_amount=self.fee_amount,
        )
        with self.assertRaises(AlreadyCapturedException):
            PaymentProvidersPaymentPort.capture_payment(payment.id)

        payment = baker.make(
            Payment,
            status=PaymentStatus.CAPTURE_FAILED,
            customer=self.customer,
            account_holder=self.account_holder,
            amount=self.amount,
            fee_amount=self.fee_amount,
        )
        with self.assertRaises(WrongPaymentActionException):
            PaymentProvidersPaymentPort.capture_payment(payment.id)

    @parameterized.expand([PaymentMethodType.TERMINAL, PaymentMethodType.TAP_TO_PAY])
    @patch_payment_providers_payment_updated_event()
    @patch_get_payment_service_class()
    @mock.patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    def test_payment_flow(
        self, payment_method, _, _get_payment_service_class_mock, _payment_update_signal_mock
    ):
        # initialize
        payment_provider = PaymentProviderCode.STRIPE
        payment_id = PaymentProvidersPaymentPort.initialize_payment(
            account_holder_id=self.account_holder.id,
            payment_provider_code=payment_provider,
            payment_method_type=payment_method,
            amount=self.amount,
            fee_amount=self.fee_amount,
            auto_capture=True,
            customer_id=self.customer.id,
        ).entity.id
        payment = Payment.objects.get(id=payment_id)
        self.assertEqual(payment.status, PaymentStatus.NEW)

        # authorize
        _get_payment_service_class_mock.return_value.authorize_payment.return_value = payment, None
        PaymentProvidersPaymentPort.authorize_payment(
            payment_id=payment.id,
            payment_method_data=AuthorizePaymentMethodDataEntity(
                tokenized_pm_id=baker.make(TokenizedPaymentMethod).id,
            ),
        )
        payment.refresh_from_db()
        self.assertEqual(payment.status, PaymentStatus.SENT_FOR_AUTHORIZATION)

        payment.status = PaymentStatus.AUTHORIZED
        payment.save(update_fields=['status'])

        # capture
        PaymentProvidersPaymentPort.capture_payment(payment.id)
        payment.refresh_from_db()
        self.assertEqual(payment.status, PaymentStatus.SENT_FOR_CAPTURE)

        history = payment.history.values_list('data', flat=True)
        self.assertEqual(history[0]['status'], PaymentStatus.SENT_FOR_CAPTURE)
        self.assertEqual(history[1]['status'], PaymentStatus.SENT_FOR_AUTHORIZATION)

    @patch_payment_providers_payment_updated_event()
    @patch_get_payment_service_class()
    @mock.patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    def test_payment_to_booksy_flow(
        self, _, _get_payment_service_class_mock, _payment_update_signal_mock
    ):
        # initialize
        payment_method = PaymentMethodType.CARD
        payment_provider = PaymentProviderCode.STRIPE
        payment_id = PaymentProvidersPaymentPort.initialize_payment(
            account_holder_id=self.account_holder.id,
            payment_provider_code=payment_provider,
            payment_method_type=payment_method,
            amount=self.amount,
            fee_amount=0,
            auto_capture=True,
            customer_id=self.customer.id,
            metadata={
                'booksy_gift_card_id': '123456',
                'booksy_gift_card_external_id': '654321',
                'reporting_category': BGCReportingCategoryEnum.CHARGE_BGC,
            },
        ).entity.id
        payment = Payment.objects.get(id=payment_id)
        self.assertEqual(payment.status, PaymentStatus.NEW)
        self.assertEqual(
            payment.metadata,
            {
                'booksy_gift_card_id': '123456',
                'booksy_gift_card_external_id': '654321',
                'reporting_category': BGCReportingCategoryEnum.CHARGE_BGC,
            },
        )

        # authorize
        _get_payment_service_class_mock.return_value.authorize_payment.return_value = payment, None
        PaymentProvidersPaymentPort.authorize_payment(
            payment_id=payment.id,
            payment_method_data=AuthorizePaymentMethodDataEntity(
                tokenized_pm_id=baker.make(TokenizedPaymentMethod).id,
            ),
            off_session=True,
        )
        payment.refresh_from_db()
        self.assertEqual(payment.status, PaymentStatus.SENT_FOR_AUTHORIZATION)

        # simulate status change due to Stripe webhook event
        CommonPaymentServices.update_status(
            payment=payment,
            status=PaymentStatus.CAPTURED,
            error_code=None,
        )
        payment.refresh_from_db()
        history = payment.history.values_list('data', flat=True)
        self.assertEqual(history[0]['status'], PaymentStatus.CAPTURED)
        self.assertEqual(history[1]['status'], PaymentStatus.SENT_FOR_AUTHORIZATION)

    @patch_payment_providers_payment_updated_event()
    @mock.patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    @mock.patch(
        'webapps.payment_providers.services.common.'
        'check_tokenized_payment_method_menu_warnings_for_customer'
    )
    def test_authorize_payment_failed_expired_card(
        self,
        mock_check_tokenized_payment_method_menu_warnings_for_customer,
        mock_synchronize_tokenized_payment_method_for_stripe,
        mock_send,
    ):
        payment = baker.make(
            Payment,
            status=PaymentStatus.NEW,
            payment_method=PaymentMethodType.CARD,
            customer=self.customer,
            account_holder=self.account_holder,
            amount=self.amount,
            fee_amount=self.fee_amount,
        )
        expired_tpm = baker.make(
            TokenizedPaymentMethod,
            customer=self.customer,
            provider_code=PaymentProviderCode.STRIPE,
            method_type=PaymentMethodType.CARD,
            default=True,
            details={
                'brand': 'visa',
                'last_digits': '5678',
                'expiry_month': 1,
                'expiry_year': 2022,
                'cardholder_name': 'cardholder name',
            },
        )
        port_response = PaymentProvidersPaymentPort.authorize_payment(
            payment_id=payment.id,
            payment_method_data=AuthorizePaymentMethodDataEntity(
                tokenized_pm_id=expired_tpm.id,
            ),
        )
        self.assertEqual(port_response.entity.error_code, PaymentError.EXPIRED_CARD)
        assert mock_check_tokenized_payment_method_menu_warnings_for_customer.delay.call_count == 1

    @patch_payment_providers_payment_updated_event()
    @mock.patch('webapps.pos.actions.synchronize_tokenized_payment_method_for_stripe')
    @mock.patch(
        'webapps.payment_providers.services.common.'
        'check_tokenized_payment_method_menu_warnings_for_customer'
    )
    def test_authorize_payment_failed_internal_status_invalid(
        self,
        mock_check_tokenized_payment_method_menu_warnings_for_customer,
        mock_synchronize_tokenized_payment_method_for_stripe,
        mock_send,
    ):
        payment = baker.make(
            Payment,
            provider_code=PaymentProviderCode.STRIPE,
            status=PaymentStatus.NEW,
            payment_method=PaymentMethodType.CARD,
            customer=self.customer,
            account_holder=self.account_holder,
            amount=self.amount,
            fee_amount=self.fee_amount,
        )
        expired_tpm = baker.make(
            TokenizedPaymentMethod,
            customer=self.customer,
            provider_code=PaymentProviderCode.STRIPE,
            method_type=PaymentMethodType.CARD,
            default=True,
            details={
                'brand': 'visa',
                'last_digits': '5678',
                'expiry_month': 1,
                'expiry_year': tznow().year + 1,
                'cardholder_name': 'cardholder name',
            },
            internal_status=TokenizedPaymentMethod.INTERNAL_STATUS.INVALID,
        )
        port_response = PaymentProvidersPaymentPort.authorize_payment(
            payment_id=payment.id,
            payment_method_data=AuthorizePaymentMethodDataEntity(
                tokenized_pm_id=expired_tpm.id,
            ),
        )
        self.assertEqual(port_response.entity.error_code, PaymentError.GENERIC_ERROR)
        assert mock_check_tokenized_payment_method_menu_warnings_for_customer.delay.call_count == 1
