import json
import logging

import requests
from django.conf import settings
from django.core.cache import cache

from lib.payment_providers.entities import (
    AuthorizePaymentMethodDataEntity,
    AuthAdditionalDataEntity,
)
from lib.payment_providers.enums import PaymentMethodType
from webapps.billing.entities.transaction import BillingTransactionEntity
from webapps.billing.ports import BillingPort
from webapps.payment_providers.models import Payment


nethone_logger = logging.getLogger('booksy.payment_providers.nethone')


class NethoneProvider:
    @staticmethod
    def send_data_on_pay_by_app(
        payment: Payment,
        payment_method_data: AuthorizePaymentMethodDataEntity,
        additional_data: AuthAdditionalDataEntity | None = None,
    ):
        nethone_attempt_reference = None
        if additional_data and additional_data.fraud_prevention:
            nethone_attempt_reference = additional_data.fraud_prevention.nethone_attempt_reference

        if not nethone_attempt_reference or payment.payment_method != PaymentMethodType.CARD:
            return  # in the POC stage it is not strictly enforced, and is only for PBA (cards)

        business_id = int(payment.account_holder.metadata.get("business_id"))
        business_data = BillingPort.get_business_billing_data(business_id)
        customer_data = {}
        if business_data:
            customer_data = {
                "person_type": "legal",
                "email": business_data.email,
                "user_reference": str(business_data.id),
                "name": business_data.name,
                "representative": {
                    "first_name": business_data.owner_first_name,
                    "last_name": business_data.owner_last_name,
                    "phone_number": business_data.phone_number,
                },
                "registration_datetime": business_data.created_at.isoformat(),
                "last_update_datetime": business_data.updated_at.isoformat(),
                "billing_address": {
                    "country": business_data.country.upper() if business_data.country else None,
                    "city": business_data.city,
                    "postal_code": business_data.postal_code,
                    "street": business_data.address,
                    "latitude": business_data.latitude,
                    "longitude": business_data.longitude,
                },
            }
        customer_data['person_type'] = 'legal'
        customer_data['user_reference'] = str(business_data.id)

        payload = json.dumps(
            {
                "reference": str(payment.id),
                "attempt_reference": nethone_attempt_reference,
                "type": "transaction",
                "origin": "Pay By App - Provider",
                "source": {},
                "payment_methods": [
                    {
                        "method": "card",
                        "payment_gateway": payment.provider_code,
                        "card_token": payment_method_data.payment_token,
                        "value": {
                            "amount": payment.amount,
                            "currency": payment.currency,
                        },
                    }
                ],
                "transaction_amount": payment.amount,
                "transaction_currency": payment.currency,
                "customer": customer_data,
                "device": {
                    "ipaddr": additional_data.device_data.ip,
                },
                "merchant_country": settings.API_COUNTRY,
                "items": [
                    {
                        "type": "transfer",
                        "recipient": {
                            "id": str(payment.customer.metadata.get("user_id")),
                        },
                    }
                ],
            }
        )

        return NethoneProvider.send_nethone_data(payload)

    @staticmethod
    def pop_or_set_from_cache_profiling_data(business_id: int, **profiling_data):
        if nethone_additional_data := cache.get(business_id):
            cache.delete(business_id)
            return nethone_additional_data
        cache.set(business_id, profiling_data, timeout=24 * 60 * 60)
        return profiling_data

    @staticmethod
    def send_data_on_subscribe(billing_transaction_entity: BillingTransactionEntity):
        additional_data = NethoneProvider.pop_or_set_from_cache_profiling_data(
            billing_transaction_entity.business.id
        )
        card_data = billing_transaction_entity.credit_card
        business = billing_transaction_entity.business

        payload = json.dumps(
            {
                "reference": str(billing_transaction_entity.id),
                "attempt_reference": additional_data.get('nethone_attempt_reference'),
                "type": "register",
                "origin": "New subscription",
                "source": {
                    "channel": additional_data.get('booking_source'),
                },
                "payment_methods": [
                    {
                        "method": "card",
                        "card_number_last4": card_data.last_4_digits,
                        "card_expiry_year": card_data.expiration_year,
                        "card_expiry_month": card_data.expiration_month,
                        "cardholder_name": card_data.cardholder_name,
                        "value": {
                            "amount": str(billing_transaction_entity.amount),
                            "currency": billing_transaction_entity.currency.upper(),
                        },
                    }
                ],
                "transaction_amount": str(billing_transaction_entity.amount),
                "transaction_currency": billing_transaction_entity.currency.upper(),
                "customer": {
                    "person_type": "legal",
                    "email": business.email,
                    "user_reference": str(business.id),
                    "name": business.name,
                    "representative": {
                        "first_name": business.owner_first_name,
                        "last_name": business.owner_last_name,
                        "phone_number": business.phone_number,
                    },
                    "registration_datetime": business.created_at.isoformat(),
                    "last_update_datetime": business.updated_at.isoformat(),
                    "billing_address": {
                        "country": business.country.upper(),
                        "city": business.city,
                        "postal_code": business.postal_code,
                        "street": business.address,
                        "latitude": business.latitude,
                        "longitude": business.longitude,
                    },
                },
                "device": {
                    "ipaddr": additional_data.get('device_ip'),
                },
                "merchant_country": settings.API_COUNTRY,
            }
        )
        return NethoneProvider.send_nethone_data(payload)

    @staticmethod
    def send_nethone_data(payload: str):

        try:
            url = "https://api.nethone.io/v1/inquiries"

            headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            }

            session = requests.Session()
            session.auth = (
                settings.NETHONE_BASIC_AUTH_USERNAME,
                settings.NETHONE_BASIC_AUTH_PASSWORD,
            )

            response = session.request("POST", url, headers=headers, data=payload)
            # right now don't do anything with the response
            nethone_logger.info(
                "Nethone response code [%s] body: %s",
                response.status_code,
                response.text,
            )
        except Exception as exc:  # pylint: disable=broad-exception-caught
            nethone_logger.exception(
                "Unable to send Nethone attempt reference: %s",
                exc,
            )
