import logging

from django.db import transaction

from webapps.french_certification.management.fix_order_jets_fiscalreceipts import detect, fix
from webapps.script_runner.mixins import ReusableScript
from webapps.script_runner.runners import DBScriptRunner

log = logging.getLogger('booksy.script_runner')


class Script(DBScriptRunner, ReusableScript):
    version = 1

    def run(self):
        with transaction.atomic():
            businesses_affected = detect()
            if businesses_affected:
                log.warning(
                    "detected %d affected business(es)",
                    len(businesses_affected),
                    extra={
                        "businesses": businesses_affected,
                    },
                )
            for business in businesses_affected:
                fix(business)
