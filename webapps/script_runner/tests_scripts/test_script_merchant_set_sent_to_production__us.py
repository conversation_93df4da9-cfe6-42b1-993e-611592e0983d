import pytest
from model_bakery import baker

from lib.tools import tznow
from webapps.navision.models import Merchant
from webapps.script_runner.scripts.script_merchant_set_sent_to_production__us import Script


@pytest.mark.django_db
def test_merchants():
    baker.make(
        Merchant,
        entity_name='Active Merchant',
        tax_id='TAX123',
        synced_at=None,
        sent_to_production=False,
    )
    baker.make(
        Merchant,
        entity_name='Merchant1',
        tax_id='TAX456',
        synced_at=None,
        sent_to_production=True,
    )
    baker.make(
        Merchant,
        entity_name='Merchant2',
        tax_id='TAX789',
        synced_at=tznow(),
        sent_to_production=True,
    )

    script = Script()
    script.run()

    assert Merchant.objects.filter(synced_at__isnull=True).count() == 3
    assert Merchant.objects.filter(sent_to_production=False).count() == 3
    assert Merchant.objects.filter(synced_at__isnull=False).count() == 0
    assert Merchant.objects.filter(sent_to_production=True).count() == 0
