import logging
from collections.abc import Iterable
from unittest.mock import ANY, Mock, call, patch

import pytest

from webapps.business.baker_recipes import business_recipe
from webapps.business.models import Business
from webapps.script_runner.scripts.script__fix_order_of_jets_and_fiscalreceipts__fr import Script

MODNAME = "webapps.script_runner.scripts.script__fix_order_of_jets_and_fiscalreceipts__fr"


@pytest.mark.django_db
class TestScript:
    @staticmethod
    @pytest.fixture(autouse=True)
    def mock_detect() -> Iterable[Mock]:
        with patch(f"{MODNAME}.detect") as mock:
            yield mock

    @staticmethod
    @pytest.fixture(autouse=True)
    def mock_fix() -> Iterable[Mock]:
        with patch(f"{MODNAME}.fix") as mock:
            yield mock

    @staticmethod
    @pytest.fixture
    def sut() -> Script:
        return Script()

    def test_fixes_affected_businesses(
        self,
        mock_detect: Mock,
        mock_fix: Mock,
        sut: <PERSON>rip<PERSON>,
    ) -> None:
        mock_detect.return_value = businesses_affected = [1, 3, 500]
        sut.run()
        assert mock_fix.call_args_list == [call(bid) for bid in businesses_affected]

    def test_logs_affected_businesses(
        self,
        caplog: pytest.LogCaptureFixture,
        mock_detect: Mock,
        sut: Script,
        affected_count=14,
    ) -> None:
        mock_detect.return_value = []
        with caplog.at_level(logging.WARNING):
            sut.run()
        assert caplog.messages == []

        mock_detect.return_value = list(range(affected_count))
        with caplog.at_level(logging.WARNING):
            sut.run()
        assert caplog.messages == [ANY] and f" {affected_count} " in caplog.messages[0]

    def test_fixes_all_or_nothing(
        self,
        mock_detect: Mock,
        mock_fix: Mock,
        sut: Script,
        error=RuntimeError("triggered for atomicity tests"),
    ) -> None:
        def fake_fix(business_id: int) -> None:
            if business_id >= 100:
                raise error
            business_recipe.make(id=business_id)

        mock_detect.return_value = businesses_affected = [1, 2, 100]
        mock_fix.side_effect = fake_fix

        with pytest.raises(type(error), match=str(error)):
            sut.run()

        assert not Business.objects.exists()

        businesses_affected.pop()
        sut.run()

        assert (
            list(Business.objects.values_list("id", flat=True).order_by("id"))
            == businesses_affected
        )
