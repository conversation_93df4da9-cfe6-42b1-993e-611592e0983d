# pylint: disable=unused-argument
import copy
import csv
import json
import urllib.parse

import braintree
from ckeditor.widgets import CKEditorWidget
from django import forms
from django.conf import settings
from django.contrib import admin, messages
from django.contrib.admin import <PERSON><PERSON>ist<PERSON><PERSON><PERSON>, TabularInline
from django.contrib.admin.actions import delete_selected as delete_selected_
from django.db import transaction
from django.db.models import Exists, OuterRef, Q, Subquery
from django.forms import BaseInlineFormSet
from django.http import HttpResponse
from django.shortcuts import redirect
from django.urls import re_path as url
from django.urls import reverse
from django.utils.html import format_html

from lib.admin_helpers import BaseModelAdmin, NoAddDelMixin, admin_link
from lib.feature_flag.feature.boost import BoostBanIsTheOnlyBanningFlowFlag
from lib.rivers import River, bump_document
from lib.tools import tznow
from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin, use_new_permissions
from webapps.billing.permissions import BillingUserPermission
from webapps.boost.enums import MarketplaceTransactionDeclineType, MarketplaceTransactionType
from webapps.boost.models import BoostAppointment
from webapps.business.elasticsearch import BusinessDocument
from webapps.images.enums import ImageTypeEnum
from webapps.images.models import Image
from webapps.marketplace.cms.enums import SeoCmsContentType
from webapps.marketplace.enums import SEO_METADATA_TYPE__FIXED
from webapps.marketplace.forms import (
    BusinessMarketPlaceSlotForm,
    BusinessNetworkForm,
    CmsContentForm,
    CommissionForm,
    MatchTreatmentLogInlineForm,
    SeoMetadataForm,
)
from webapps.marketplace.models import (
    BUSINESS_ACQUISITION_TYPE__CLAIM,
    MATCH_TRATMENT_CORRECT,
    MATCH_TRATMENT_REJECTED,
    BoostClientCard,
    BusinessMarketPlaceSlot,
    BusinessNetwork,
    CmsContent,
    MarketplaceBListingAcquisition,
    MarketplaceClaimDenyReason,
    MarketplaceCommission,
    MarketplaceStage,
    MarketplaceStageStatus,
    MarketplaceTransaction,
    MarketplaceTransactionLog,
    MarketplaceTransactionRow,
    MarketplaceTransactionStatus,
    MatchTreatmentBatch,
    MatchTreatmentLog,
    SeoContent,
    SeoContentData,
    SeoFeatureFlag,
    SeoMetadata,
    SeoRecommended4U,
    SeoRegionCategoryListing,
    SeoRegionCategoryListingTemplate,
    SeoRegionHomepage,
    SeoRegionHomepageCategoryTemplate,
)
from webapps.marketplace.seo_metadata import SeoMetadataHelper
from webapps.marketplace.tasks import boost_pay_task
from webapps.marketplace.utils import get_status_description
from webapps.survey.models import PollChoice
from webapps.user.groups import GroupNameV2
from webapps.user.models import ExternalUser
from webapps.user.tools import get_user_from_django_request


class RegionFilter(admin.SimpleListFilter):
    title = 'region'
    parameter_name = 'region'

    def lookups(self, request, model_admin):
        return {
            (int(region_info[0]), region_info[-1].encode('utf-8'))
            for region_info in model_admin.model.objects.values_list('region__id', 'region__name')
        }

    def queryset(self, request, queryset):
        if self.value() is None:
            return queryset

        return queryset.filter(region_id=self.value())


class BusinessMarketPlaceSlotAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    form = BusinessMarketPlaceSlotForm

    raw_id_fields = (
        'business',
        'region',
    )
    list_filter = [
        RegionFilter,
    ]
    ordering = ('region_id', 'slot_number')
    search_fields = (
        '=id',
        '=business__id',
        '=region__id',
        'region__name',
        'business__name',
    )
    list_display = [
        'id',
        'slot_region',
        'slot_number',
        'business_id',
        'redirect_url',
    ]

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        qs = qs.prefetch_related('business', 'region')
        return qs

    @staticmethod
    def slot_region(obj):
        return f'{obj.region.name}; Region id: {obj.region.id}'

    slot_region.short_description = 'Region for Promoted Business Slot'


class CmsContentImageFormset(BaseInlineFormSet):
    @staticmethod
    def update_instance_cateogry(instance):
        instance.category = ImageTypeEnum.CMS_CONTENT
        instance.save()

    def save_new_objects(self, commit=True):
        saved_instances = super().save_new_objects(commit)
        if commit:
            for instance in saved_instances:
                self.update_instance_cateogry(instance)
            return saved_instances

    def save_existing_objects(self, commit=True):
        saved_instances = super().save_existing_objects(commit)
        if commit:
            for instance in saved_instances:
                self.update_instance_cateogry(instance)
            return saved_instances


class CmsContentImagesInline(TabularInline):
    model = Image
    formset = CmsContentImageFormset
    extra = 0
    fields = [
        'image',
        'order',
    ]


class CmsContentAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    form = CmsContentForm

    inlines = [
        CmsContentImagesInline,
    ]

    raw_id_fields = (
        'region',
        'category',
    )
    list_filter = [
        RegionFilter,
        # CategoryListFilter,
    ]
    ordering = (
        'region_id',
        'category_id',
        'title',
    )
    search_fields = (
        '=region__id',
        'region__name',
        '=category__id',
        'category__name',
        'search_order',
        'title',
        'author',
    )
    list_display = [
        'id',
        'region_id',
        'category_id',
        'search_order',
        'title',
        'visible',
        'author',
        'url',
    ]

    @staticmethod
    def get_preview(obj=None):
        if obj is None:
            return ''

        params = {
            'type': obj.type,
            'title': obj.title,
            'body': obj.body,
            'image': obj.image.url if obj.image else None,
        }
        marketplace_url = urllib.parse.urljoin(
            settings.MARKETPLACE_URL,
            'en-us/dynamic/cms-content',
        )

        return format_html(
            """

        <a  class="btn" id="get-perview">
                Preview
        </a>
        <script>
        function post(event) {{
            event.preventDefault();
            params = {}
            var form = document.createElement("form");
            form.setAttribute("method", "post");
            form.setAttribute("action", "{}");
            for(var key in params) {{
                if(params.hasOwnProperty(key)) {{
                    var hiddenField = document.createElement("input");
                    hiddenField.setAttribute("type", "hidden");
                    hiddenField.setAttribute("name", key);
                    hiddenField.setAttribute("value", params[key]);

                    form.appendChild(hiddenField);
                }}
            }}

            document.body.appendChild(form);
            console.log(form)
            form.submit();
        }}
        document.getElementById("get-perview").addEventListener(
                                    "click", post, false);
        </script>
        """,
            json.dumps(params),
            marketplace_url,
        )

    def get_form(self, request, obj=None, change=False, **kwargs):
        self.preview = self.get_preview(obj)
        form = super().get_form(request, obj, change, **kwargs)
        return form

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        qs = qs.prefetch_related('region', 'category')
        return qs

    @staticmethod
    def slot_region(obj):
        return f'{obj.region.name.encode("utf-8")}; Region id: {obj.region.id}'

    slot_region.short_description = 'Region for Promoted Business Slot'


class SeoMetadataAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    form = SeoMetadataForm
    change_form_template = 'admin/change_forms/change_form__seometadata.html'

    fields = (
        'active',
        'type',
        'sub_type',
        'region',
        'category',
        'title_pattern',
        'title_preview',
        'description_pattern',
        'description_preview',
        'h1_pattern',
        'h1_preview',
    )
    raw_id_fields = (
        'region',
        'category',
    )
    readonly_fields = ('type',)

    list_display = [
        'id',
        'type',
        'sub_type',
        'region',
        'category',
    ]

    @staticmethod
    def delete_selected_seo_metadata(modeladmin, request, queryset):
        if queryset.filter(type=SEO_METADATA_TYPE__FIXED).exists():
            messages.error(
                request,
                'Cannot delete selected objects. One or more selected objects is type "Fixed".',
            )
        else:
            return delete_selected_(modeladmin, request, queryset)

    @staticmethod
    def export_selected_seo_metadata(_, request, queryset):
        queryset = queryset.order_by('id')
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="seo_metadata.csv"'

        writer = csv.writer(response, delimiter='|')
        writer.writerow(
            [
                'Active',
                'ID',
                'Type',
                'Sub Type',
                'Region',
                'Category',
                'Title Pattern',
                'Title Preview',
                'Description Pattern',
                'Description Preview',
                'H1 Pattern',
                'H1 Preview',
            ]
        )
        for seo_object in queryset:
            seo_data = {
                'category': seo_object.category,
                'region': seo_object.region,
                'title_pattern': seo_object.title_pattern,
                'description_pattern': seo_object.description_pattern,
                'h1_pattern': seo_object.h1_pattern,
            }
            preview = SeoMetadataHelper.generate_preview(seo_data)
            if preview:
                writer.writerow(
                    [
                        seo_object.active,
                        seo_object.id,
                        seo_object.get_type_display(),
                        seo_object.get_sub_type_display(),
                        seo_object.region,
                        seo_object.category,
                        seo_object.title_pattern,
                        preview['title'],
                        seo_object.description_pattern,
                        preview['description'],
                        seo_object.h1_pattern,
                        preview['h1'],
                    ]
                )
        return response

    def has_delete_permission(self, request, obj=None):
        if obj and obj.type == SEO_METADATA_TYPE__FIXED:
            return False
        return super().has_delete_permission(request, obj)

    def get_actions(self, request):
        actions = super().get_actions(request)
        delete_action = (
            self.delete_selected_seo_metadata,
            'delete_selected',
            actions['delete_selected'][2],
        )
        export_action = (
            self.export_selected_seo_metadata,
            'export_selected',
            'Export selected SEO metadata',
        )
        actions['delete_selected'] = delete_action
        actions['export_selected'] = export_action
        return actions

    def get_readonly_fields(self, request, obj=None):
        fields = super().get_readonly_fields(request, obj)
        if obj and obj.type == SEO_METADATA_TYPE__FIXED:
            fields += (
                'sub_type',
                'region',
                'category',
                'active',
            )
        return fields


class BusinessNetworkAdmin(
    GroupPermissionMixin,
    BaseModelAdmin,
    admin.ModelAdmin,
):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    form = BusinessNetworkForm

    ordering = (
        'name',
        'slug',
    )
    search_fields = (
        'business_ids__contains',
        'name',
        'slug',
    )
    hide_keyword_field = True
    list_display = [
        'name',
        'slug',
        'is_enterprise',
        'business_ids',
        'deeplink_ids',
        'deeplink_slug',
    ]
    list_filter = [
        'is_enterprise',
    ]
    readonly_fields = [
        'deeplink_ids',
        'deeplink_slug',
        'created',
        'updated',
        'deleted',
    ]

    def get_urls(self):
        urls = super().get_urls()
        additional_urls = [
            url(
                r'^reindex_network_businesses/(?P<obj_id>\d+)$',
                self.admin_site.admin_view(self.reindex_businesses),
                name='reindex_network_businesses',
            ),
        ]
        return urls + additional_urls

    def get_form(self, request, obj=None, change=False, **kwargs):
        self.reindex_business_network = self.get_reindex_button(obj)
        form = super().get_form(request, obj, change, **kwargs)
        return form

    @staticmethod
    def get_reindex_button(obj=None):
        if obj is None:
            return ''
        target = reverse('admin:reindex_network_businesses', args=[obj.id])
        inner_html = 'Reindex Network Businesses'

        return format_html('<a href="{}" class="btn ">{}</a>', target, inner_html)

    @staticmethod
    def reindex_businesses(request, obj_id):
        business_network = BusinessNetwork.objects.filter(id=obj_id).first()
        BusinessDocument.reindex(business_network.business_ids, use_celery=True)
        messages.success(request, 'Businesses has been reindexed')

        return redirect(request.META.get('HTTP_REFERER', '/'))


class ActiveCommissionFilter(admin.SimpleListFilter):
    title = 'active'
    parameter_name = 'active'

    def lookups(self, request, model_admin):
        return (True, 'Yes'), (False, 'No')

    def queryset(self, request, queryset):
        null_end_date_filter = Q(end_date__isnull=True)
        value = self.value()
        if value is None:
            return queryset
        if value == 'True':
            return queryset.filter(null_end_date_filter)

        return queryset.exclude(null_end_date_filter)


class CommissionAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    form = CommissionForm
    add_form_template = 'admin/change_forms/change_form__marketplacecommission.html'
    change_form_template = 'admin/change_forms/change_form__marketplacecommission.html'
    list_per_page = 20

    @staticmethod
    def delete_selected_objects(modeladmin, request, queryset):  # pylint: disable=unused-argument
        for obj in queryset:
            obj.delete()

    @admin.action(description='Move providers for selected commissions to regional commission')
    def move_to_regional_commission(self, request, queryset):
        if queryset.filter(region__isnull=False).exists():
            messages.error(request, 'Regional commission must not be removed')
            return
        if queryset.filter(boostban__isnull=False).exists():
            messages.error(request, 'Cannot move to regional commission a provider that is banned')
            return
        for obj in queryset:
            obj.delete()

    actions = ['move_to_regional_commission']

    def get_actions(self, request):
        actions = super().get_actions(request)
        if settings.BOOST.BANS_ENABLED and BoostBanIsTheOnlyBanningFlowFlag():
            if actions.get('delete_selected'):
                del actions['delete_selected']
            return actions

        delete_action = (
            self.delete_selected_objects,
            'delete_selected',
            actions['delete_selected'][2],
        )
        actions['delete_selected'] = delete_action
        return actions

    search_fields = (
        'region_id',
        'business_id',
    )
    hide_keyword_field = True
    list_filter = (
        ActiveCommissionFilter,
        'marketplace',
    )
    list_display = [
        'id',
        'region',
        'business',
        'commission',
        'bottom_cap',
        'max',
        'minimum_commission',
        'flat_fee',
        'marketplace',
        'start_date',
        'end_date',
    ]
    raw_id_fields = (
        'business',
        'region',
    )

    fields = [
        'business',
        'region',
        'commission',
        'bottom_cap',
        'max',
        'minimum_commission',
        'flat_fee',
        'marketplace',
        'parent_id',
        'start_date',
        'end_date',
    ]

    readonly_fields = [
        'start_date',
        'end_date',
        'parent_id',
        'flat_fee',
    ]

    def _changeform_view(self, request, object_id, form_url, extra_context):
        if not extra_context:
            extra_context = {"show_save_and_continue": False}
        else:
            extra_context["show_save_and_continue"] = False
        return super()._changeform_view(
            request,
            object_id,
            form_url,
            extra_context,
        )


class MatchTreatmentPermissionsMixin:
    permissions_required = ('business.match_treatment_moderator',)

    def has_permission(self, request):
        return request.user.has_perms(self.permissions_required)


class MatchTreatmentLogInline(
    MatchTreatmentPermissionsMixin, NoAddDelMixin, GroupPermissionMixin, TabularInline
):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    form = MatchTreatmentLogInlineForm
    model = MatchTreatmentLog
    fields = readonly_fields = readonly_fields = (
        'service',
        'service_category',
        'description',
        'treatment',
        'no_match',
    )

    @staticmethod
    def service_category(obj):
        return obj.service.service_category.name

    @staticmethod
    def description(obj):
        return obj.service.description

    def has_view_permission(self, request, obj=None):
        if use_new_permissions(request.user):
            return super().has_view_permission(request, obj=obj)
        return self.has_permission(request)

    def has_change_permission(self, request, obj=None):
        if use_new_permissions(request.user):
            return super().has_change_permission(request, obj=obj)
        return self.has_permission(request)

    def get_fields(self, request, obj=None):
        fields = self.fields
        if obj and obj.status not in [
            MATCH_TRATMENT_CORRECT,
            MATCH_TRATMENT_REJECTED,
        ]:
            fields = ('selected',) + fields
        return fields

    # needed to render field
    @staticmethod
    def selected(_obj):
        return True


class UserFilter(SimpleListFilter):
    title = 'user'
    parameter_name = 'user'

    def lookups(self, request, model_admin):
        users = ExternalUser.objects.filter(created_batches__isnull=False).distinct()
        return [(user.id, user.email) for user in users]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(user_id=self.value())
        return queryset


class MatchTreatmentBatchAdmin(
    MatchTreatmentPermissionsMixin, NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin
):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    raw_id_fields = ('user',)
    inlines = (MatchTreatmentLogInline,)
    readonly_fields = ['user', 'status', 'moderator', 'created', 'updated', 'deleted']
    list_display = (
        'id',
        'user',
        'moderator',
        'all_match_count',
        'no_match_count',
        'status',
    )
    list_filter = (
        UserFilter,
        'status',
    )
    ordering = ('id', 'user', 'status')

    def has_module_permission(self, request):
        if use_new_permissions(request.user):
            return super().has_module_permission(request)
        return self.has_permission(request)

    def has_change_permission(self, request, obj=None):
        if use_new_permissions(request.user):
            return super().has_change_permission(request, obj=obj)
        return self.has_permission(request)

    def construct_change_message(self, request, form, formsets, add=False):
        return []

    def save_formset(self, request, form, formset, change):
        action = request.POST['accept_or_reject']
        selected_instances_ids = []
        for _form in formset:
            if _form.cleaned_data['selected']:
                selected_instances_ids.append(_form.instance.id)
        if not selected_instances_ids:
            return
        batch_instance = MatchTreatmentLog.objects.get(
            id=selected_instances_ids[0],
        ).batch
        user_id = request.user.id
        # not all Treatments are selected
        if len(formset) != len(selected_instances_ids):
            batch_instance = MatchTreatmentBatch.objects.create(
                user=batch_instance.user,
                status=MATCH_TRATMENT_CORRECT if action == 'accept' else MATCH_TRATMENT_REJECTED,
            )
            MatchTreatmentLog.objects.filter(
                id__in=selected_instances_ids,
            ).update(
                batch=batch_instance,
            )
        if action == 'accept':
            batch_instance.accept(user_id)
        elif action == 'reject':
            batch_instance.reject(user_id)

    @staticmethod
    def accept(obj=None):
        if obj is None or obj.status in [
            MATCH_TRATMENT_CORRECT,
            MATCH_TRATMENT_REJECTED,
        ]:
            return ''
        return "<button id='accept_matchtreatmentbatch'" " class='btn btn-success'>Accept</button>"

    @staticmethod
    def reject(obj=None):
        if obj is None or obj.status in [
            MATCH_TRATMENT_CORRECT,
            MATCH_TRATMENT_REJECTED,
        ]:
            return ''
        return "<button id='reject_matchtreatmentbatch'" " class='btn btn-danger'>Reject</button>"

    @staticmethod
    def all_match_count(obj):
        return obj.matchtreatmentlog_set.count()

    @staticmethod
    def no_match_count(obj):
        return obj.matchtreatmentlog_set.filter(no_match=True).count()

    def get_form(self, request, obj=None, change=False, **kwargs):
        self.accept_match_treatments = self.accept(obj)
        self.reject_match_treatments = self.reject(obj)
        form = super().get_form(request, obj, change, **kwargs)
        return form


class MarketplaceTransactionStatusInline(NoAddDelMixin, TabularInline):
    model = MarketplaceTransactionStatus
    fields = readonly_fields = (
        'status',
        'transaction',
        'external_id',
        'amount',
        'charge_date',
        'response_code',
        'response_text',
        'braintree_description',
        'decline_type',
        'gateway_rejection_reason',
        'type',
        'boost_appointment_ids',
        'errors',
    )

    @staticmethod
    def boost_appointment_ids(obj):
        return ','.join(str(i) for i in obj.boost_appointments.all().values_list('id', flat=True))

    @staticmethod
    def braintree_description(obj):
        return get_status_description(obj.response_code)

    @staticmethod
    def decline_type(obj):
        return obj.response_type


class MarketplaceTransactionLogInline(NoAddDelMixin, TabularInline):
    model = MarketplaceTransactionLog
    fields = readonly_fields = ('status', 'updated')

    @staticmethod
    def status(obj):
        return obj.marketplacetransactionstatus.status


class DeclineTypeFilter(SimpleListFilter):
    title = 'DeclineType'
    parameter_name = 'DeclineType'

    def lookups(self, request, model_admin):
        return MarketplaceTransactionDeclineType.choices()

    def queryset(self, request, queryset):
        value = self.value()
        if value is None:
            return queryset

        decline_type = dict(MarketplaceTransactionDeclineType.choices())[value]
        return queryset.annotate(
            decline_type=Subquery(
                MarketplaceTransactionStatus.objects.filter(
                    transaction_id=OuterRef('id'),
                )
                .order_by('-id')
                .values('response_type')[:1]
            )
        ).filter(decline_type=decline_type)


class TransactionStatusCodeFilter(SimpleListFilter):
    title = 'TransactionStatusCode'
    parameter_name = 'TransactionStatusCode'

    def lookups(self, request, model_admin):
        return (
            (code, code)
            for code in sorted(
                MarketplaceTransactionStatus.objects.filter(response_code__isnull=False)
                .values_list('response_code', flat=True)
                .distinct()
            )
        )

    def queryset(self, request, queryset):
        value = self.value()
        if value is None:
            return queryset

        return queryset.filter(last_transaction_status_code=value)


class BoostAppointmentInline(NoAddDelMixin, TabularInline):
    model = BoostAppointment
    fields = readonly_fields = (
        'boost_appointment_id',
        'appointment_id',
        'gross_amount' if settings.BOOST.GROSS_VALUE else 'amount',
        'status',
        'deleted',
        'rows_included',
    )

    def get_queryset(self, request):
        return self.model.all_objects.get_queryset().prefetch_related('rows')

    @staticmethod
    def boost_appointment_id(obj):
        return format_html('<a href="{}">{}</a>', admin_link(obj), obj.id)

    @staticmethod
    def rows_included(obj):
        row_links = [
            format_html('<a href="{}">{}</a>', admin_link(row), row.id) for row in obj.rows.all()
        ]
        return format_html('<br/>'.join(row_links))


@admin.action(description='Retry charge selected transactions', permissions=['retry_charge'])
def retry_charge_action(_, request, queryset):
    business_ids = {row.business_id for row in queryset}

    if len(business_ids) != 1:
        messages.error(
            request,
            'Cannot be recharged: at least one transaction belongs to other business.',
        )
        return

    business_id = business_ids.pop()

    if (
        MarketplaceTransaction.objects.boost_debts(business_id=business_id)
        .filter(id__in=[row.id for row in queryset])
        .count()
        != queryset.count()
    ):
        messages.error(
            request,
            'Cannot be recharged: at least one transaction is not allowed to be recharged.',
        )
        return

    from webapps.billing.apis.boost import BillingBoostAPI

    task_id = BillingBoostAPI.async_charge_overdues(
        business_id=business_id,
        selected_boost_ids=[row.public_id for row in queryset],
        operator_id=get_user_from_django_request(request).id,
    )

    task_url = reverse('admin:celery_task_status', args=[task_id])
    message = format_html(
        'Task successfully scheduled. Check the result <a href="{}">here</a> - WARNING:'
        ' please look at the very bottom of the page to make sure there are no sections'
        ' like "message" or "error_code", as this would mean, that the payment failed!',
        task_url,
    )
    messages.warning(request, message)


class MarketplaceTransactionAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    list_display = (
        'id',
        'status',
        'business_id',
        'total',
        'decline_type',
        'last_transaction_status_code',
        'payment_source',
        'created',
        'updated',
    )
    readonly_fields = (
        'status',
        'business',
        'total',
        'decline_type',
        'created',
        'updated',
        'deleted',
        'last_transaction_status_code',
        'payment_source',
    )
    inlines = (
        BoostAppointmentInline,
        MarketplaceTransactionStatusInline,
    )

    search_fields = (
        'id',
        'business_id',
    )
    list_filter = (
        'status',
        'payment_source',
        DeclineTypeFilter,
        TransactionStatusCodeFilter,
    )
    hide_keyword_field = True
    list_per_page = 20
    actions = [
        retry_charge_action,
    ]

    def has_retry_charge_permission(self, request):
        return BillingUserPermission(request).is_billing_user

    @staticmethod
    def appointment_id(obj):
        return obj.total

    @staticmethod
    def decline_type(obj):
        status = (
            MarketplaceTransactionStatus.objects.filter(
                transaction=obj,
            )
            .order_by('id')
            .last()
        )
        if status:
            return status.response_type

    @staticmethod
    def last_transaction_status_code(obj):
        return obj.last_transaction_status_code or '-'

    @staticmethod
    def repay(obj=None):
        link = reverse('admin:retry_payments', kwargs={'transaction_id': obj.id})
        return f"""
            <a href="{link}" class="btn btn-success">
                Retry business payments
            </a>
        """

    def get_queryset(self, request):
        return self.model.all_objects.annotate(
            last_transaction_status_code=Subquery(
                MarketplaceTransactionStatus.objects.filter(
                    transaction_id=OuterRef('id'),
                )
                .order_by('-id')
                .values('response_code')[:1]
            )
        )

    @staticmethod
    def repay_view(request, transaction_id):
        business_id = (
            MarketplaceTransaction.objects.filter(id=transaction_id)
            .values_list('business_id', flat=True)
            .first()
        )

        transactions_ids = MarketplaceTransaction.objects.all_debts(
            business_id=business_id
        ).values_list('id', flat=True)

        for i, transaction_id_ in enumerate(transactions_ids):
            boost_pay_task.apply_async((transaction_id_,), countdown=5 * i)

        return redirect(reverse('admin:marketplace_marketplacetransaction_changelist'))

    def get_form(self, request, obj=None, change=False, **kwargs):
        self.retry_busiess_payments = self.repay(obj)
        return super().get_form(request, obj, change, **kwargs)

    def get_urls(self):
        urls = super().get_urls()
        additional_urls = [
            url(
                r'^(?P<transaction_id>\d+)/retry_payments$',
                self.admin_site.admin_view(self.repay_view),
                name='retry_payments',
            ),
        ]
        return additional_urls + urls


class StageFilter(SimpleListFilter):  # deprecated by PXMAR-431, replaced by TransactionStageFilter
    title = 'Stage'
    parameter_name = 'Stage'

    def lookups(self, request, model_admin):
        return (
            ('R', 'Refunded'),
            ('C', 'Charged'),
            ('N', 'Not Charged'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'R':
            return queryset.filter(refunded=True)

        queryset = queryset.annotate(
            is_charged=Exists(
                MarketplaceTransactionStatus.objects.filter(
                    boost_appointments=OuterRef('boost_appointment'),
                    status__in=(
                        braintree.Transaction.Status.Settling,
                        braintree.Transaction.Status.Settled,
                    ),
                    type=MarketplaceTransactionType.CHARGE,
                )
            )
        )

        if self.value() == 'C':
            return queryset.filter(
                refunded=False,
                is_charged=True,
            )

        if self.value() == 'N':
            return queryset.filter(
                refunded=False,
                is_charged=False,
            )

        return queryset


class ClaimReasonFilter(SimpleListFilter):
    title = 'Claim reason'
    parameter_name = 'claim_reason'

    def lookups(self, request, model_admin):
        choices = PollChoice.objects.filter(poll__name='boost_claim').distinct()
        return [(choice.id, choice.choice) for choice in choices]

    def queryset(self, request, queryset):
        if self.value():
            return queryset.filter(claim_id=self.value())
        return queryset


class MarketplaceTransactionRowAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    list_display = (
        'id',
        'boost_appointment_link',
        'transaction_id',
        'subbooking_id',
        'appointment_id',
        'business_id',
        'service_name',
        'booking_price',
        'amount',
        'gross_amount',
        'uncapped_amount',
        'components',
        'status',
        'refunded',
        'booked_till',
        'deleted',
    )
    fields = readonly_fields = list_display + (
        'created',
        'conditions',
        'tax_amount',
        'tax_rate',
    )
    raw_id_fields = ('tax_rate',)
    search_fields = (
        'subbooking_id',
        'transaction__business_id',
        'transaction_id',
        'subbooking__booked_till__date',
    )
    list_filter = ('status', 'refunded', StageFilter, ClaimReasonFilter)
    hide_keyword_field = True
    inlines = (MarketplaceTransactionLogInline,)
    list_per_page = 20

    def get_queryset(self, request):
        return self.model.all_objects.get_queryset().select_related(
            'boost_appointment',
            'subbooking',
            'boost_appointment__boost_promotion',
        )

    @staticmethod
    def boost_appointment_link(obj):
        ba_obj = obj.boost_appointment
        return format_html(
            '<a href="{}">{}</a>', admin_link(ba_obj), f'BoostAppointment id={ba_obj.id}'
        )

    @staticmethod
    def business_id(obj):
        return obj.boost_appointment.boost_promotion.business_id

    @staticmethod
    def service_name(obj):
        return obj.subbooking.service_name

    @staticmethod
    def booked_till(obj):
        return obj.subbooking.booked_till

    @staticmethod
    def created(obj):
        return obj.subbooking.created


class BoostClientCardAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    hide_keyword_field = True
    search_fields = ('client_card__id',)
    list_display = [
        'id',
        'client_card',
        'business',
        'status',
        'status_description',
        'from_promo',
        'originally_came_from_boost',
    ]
    readonly_fields = [
        *list_display,
        'deleted',
    ]

    def get_queryset(self, request):
        return self.model.objects.get_queryset().select_related('client_card__business')

    @staticmethod
    def business(obj):
        business = obj.client_card.business
        return format_html('<a href="{}">{}</a>', admin_link(business), business)


class MarketplaceBListingAcquisitionAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    list_display_links = None
    search_fields = ('b_listing__id',)
    list_filter = ('acquisition_type',)
    list_display = [
        'id',
        'user',
        'b_listing_link',
        'acquisition_type',
        'created',
        'email',
        'name',
        'phone',
        'remove_bl_claim_link',
    ]
    readonly_fields = [
        'id',
        'user',
        'b_listing_link',
        'acquisition_type',
        'created',
        'email',
        'name',
        'phone',
    ]

    def get_queryset(self, request):
        return super().get_queryset(request).filter(deleted__isnull=True)

    @staticmethod
    def remove_bl_claim(request, claim_id):
        instance = MarketplaceBListingAcquisition.objects.get(id=claim_id)
        if instance.acquisition_type == BUSINESS_ACQUISITION_TYPE__CLAIM and not instance.deleted:
            instance.deleted = tznow()
            instance.save(update_fields=['deleted'])
            messages.success(
                request,
                'Succesfully deleted MarketplaceBListingAcquisition Claim object',
            )
        else:
            messages.error(
                request,
                "Can't delete this MarketplaceBListingAcquisition object!",
            )

        return redirect(
            reverse(
                'admin:marketplace_marketplaceblistingacquisition_changelist',
            )
        )

    @staticmethod
    def remove_bl_claim_link(obj):
        if obj.acquisition_type == BUSINESS_ACQUISITION_TYPE__CLAIM:
            return format_html(
                """<a href="{}" onclick="return confirm('Are you sure?')">
                    Remove claim
                </a>""",
                reverse('admin:remove_bl_claim', args={obj.id}),
            )
        return ''

    def get_urls(self):
        urls = super().get_urls()
        additional_urls = [
            url(
                r'^(?P<claim_id>\d+)/remove_bl_claim/$',
                self.admin_site.admin_view(self.remove_bl_claim),
                name='remove_bl_claim',
            ),
        ]

        return additional_urls + urls

    @staticmethod
    def b_listing_link(instance):
        return format_html(
            '<a href="{0}">{1}</a>',
            reverse(
                'admin:business_blisting_change',
                args=(instance.b_listing.id,),
            ),
            instance.b_listing,
        )

    b_listing_link.short_description = 'B Listing'


class MarketplaceClaimDenyReasonAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    list_display = ('id', 'short_name', 'description', 'visible')
    readonly_fields = ('created', 'updated', 'deleted')


class MarketplaceStageAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    list_display = (
        'id',
        'stage_name',
        'marketplace_stage',
        'stage_cardinality',
        'view_name',
        'available_for_sync',
    )

    def get_readonly_fields(self, request, obj=None):
        fields = ['created', 'updated', 'deleted']
        return fields


class StageNameFilter(admin.SimpleListFilter):
    title = 'stage name'
    parameter_name = 'stage_name'

    def lookups(self, request, model_admin):
        return (
            (stage, stage)
            for stage in MarketplaceStage.objects.all()
            .order_by('stage_name')
            .values_list('stage_name', flat=True)
            .distinct()
        )

    def queryset(self, request, queryset):
        value = self.value()
        if value:
            return queryset.filter(stage__stage_name=value)
        return queryset


class MarketplaceStageFilter(admin.SimpleListFilter):
    title = 'marketplace stage'
    parameter_name = 'marketplace stage'

    def lookups(self, request, model_admin):
        return (True, 'Yes'), (False, 'No')

    def queryset(self, request, queryset):
        value = self.value()
        if value is None:
            return queryset
        if value == 'True':
            return queryset.filter(stage__marketplace_stage=True)

        return queryset.exclude(stage__marketplace_stage=True)


class MarketplaceStageStatusAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    hide_keyword_field = True
    list_display = fields = readonly_fields = (
        'id',
        'business_id',
        'date',
        'stage_id',
        'stage_name',
        'marketplace_stage',
        'created',
        'updated',
        'deleted',
    )
    search_fields = ('business_id',)
    list_filter = (StageNameFilter, MarketplaceStageFilter)

    @staticmethod
    def stage_name(instance):
        return instance.stage.stage_name

    @staticmethod
    def stage_id(instance):
        return instance.stage.id

    @staticmethod
    def marketplace_stage(instance):
        return instance.stage.marketplace_stage


class SeoRegionHomepageCategoryTemplateAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    list_display = ('id', 'name', 'categories_internal_names')
    fields = ('name', 'business_category')
    raw_id_fields = ('business_category',)

    @staticmethod
    def categories_internal_names(instance):
        return ', '.join(
            instance.business_category.values_list('internal_name', flat=True),
        )


def enable_region_homepage(modeladmin, request, queryset):
    queryset.update(active=True)
    bump_document(River.SEO_REGION_HOMEPAGE, list(queryset.values_list('id', flat=True)))


def disable_region_homepage(modeladmin, request, queryset):
    queryset.update(active=False)
    bump_document(River.SEO_REGION_HOMEPAGE, list(queryset.values_list('id', flat=True)))


class SeoRegionHomepageForm(forms.ModelForm):
    class Meta:
        model = SeoRegionHomepage
        fields = '__all__'

    def clean(self):
        cleaned_data = super().clean()
        if self.changed_data:
            region = cleaned_data.get('region')
            active = cleaned_data.get('active')
            if (
                region
                and active
                and SeoRegionHomepage.objects.filter(
                    region=region,
                    active=active,
                ).exists()
            ):
                raise forms.ValidationError('Region already active')

        return cleaned_data


class SeoRegionHomepageAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    list_display = ('id', 'template', 'region', 'active')
    fields = ('template', 'region', 'active')
    list_filter = ('template', RegionFilter, 'active')
    raw_id_fields = ('region',)
    form = SeoRegionHomepageForm
    actions = [enable_region_homepage, disable_region_homepage]

    def delete_queryset(self, request, queryset):
        super().delete_queryset(request, queryset)
        bump_document(River.SEO_REGION_HOMEPAGE, list(queryset.values_list('id', flat=True)))

    def delete_model(self, request, obj):
        obj.soft_delete()


class SeoRegionCategoryListingTemplateAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    list_display = ('name', 'region_display_names')
    raw_id_fields = ('regions',)
    fields = ('name', 'regions')

    @staticmethod
    def region_display_names(instance):
        return list(instance.regions.values_list('display_name', flat=True))


def enable_regions_category(modeladmin, request, queryset):
    queryset.update(active=True)
    bump_document(River.SEO_REGION_CATEGORY, list(queryset.values_list('id', flat=True)))


def disable_regions_category(modeladmin, request, queryset):
    queryset.update(active=False)
    bump_document(River.SEO_REGION_CATEGORY, list(queryset.values_list('id', flat=True)))


enable_regions_category.short_description = 'Enable Regions cat&treat'
disable_regions_category.short_description = 'Disable Regions cat&treat'


class SeoRegionCategoryListingAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    list_display = ('id', 'template', 'category', 'max_regions', 'active')
    raw_id_fields = ('category',)
    fields = ('template', 'category', 'max_regions', 'active')
    list_filter = ('template', 'category', 'active')
    actions = [
        enable_regions_category,
        disable_regions_category,
    ]


def enable_feature_flags(modeladmin, request, queryset):
    queryset.update(active=True)
    bump_document(River.SEO_FEATURE_FLAG, list(queryset.values_list('id', flat=True)))


def disable_feature_flags(modeladmin, request, queryset):
    queryset.update(active=False)
    bump_document(River.SEO_FEATURE_FLAG, list(queryset.values_list('id', flat=True)))


class SeoFeatureFlagAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    list_display = ('key', 'value', 'value_type', 'active')
    fields = ('key', 'value', 'value_type', 'active')

    actions = [enable_feature_flags, disable_feature_flags]


class SeoContentDataFormMixin:
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance.pk:
            self.initial |= copy.deepcopy(self.instance.data)

    def clean(self):
        super().clean()
        model_fields = {field.name for field in self._meta.model._meta.fields}
        fields_to_move = set(self.declared_fields) - model_fields
        self.cleaned_data['data'] = {key: self.cleaned_data.pop(key, '') for key in fields_to_move}
        self.changed_data = [field for field in self.changed_data if field not in fields_to_move]


class SeoContentDataForm(SeoContentDataFormMixin, forms.ModelForm):
    class Meta:
        model = SeoContentData
        json_fields = ('title', 'body', 'image_alt', 'image_title')
        fields = json_fields + ('language', 'image', 'active')

    title = forms.CharField(label='Title', max_length=128, required=True)
    body = forms.CharField(label='Body', widget=CKEditorWidget, required=True)
    data = forms.CharField(required=False, widget=forms.HiddenInput)
    image = forms.ImageField(required=True)
    image_alt = forms.CharField(required=True)
    image_title = forms.CharField(required=True)


class SeoRecommended4UDataForm(SeoContentDataFormMixin, forms.ModelForm):
    class Meta:
        model = SeoContentData
        json_fields = ('title', 'target_url', 'image_alt', 'image_title')
        fields = json_fields + ('language', 'image', 'active')

    title = forms.CharField(label='Title', max_length=128, required=True)
    target_url = forms.URLField(label='Target URL', required=True)
    data = forms.CharField(required=False, widget=forms.HiddenInput)
    image = forms.ImageField(required=True)
    image_alt = forms.CharField(required=True)
    image_title = forms.CharField(required=True)


class SeoContentDataFormSet(BaseInlineFormSet):
    def clean(self):
        if any(self.errors):
            return

        from collections import Counter

        lang_counter = Counter(
            form.cleaned_data['language'] for form in self.forms if form.cleaned_data['active']
        )

        if most_common_language := lang_counter.most_common(1):
            language, lang_counter = most_common_language[0]
            if lang_counter > 1:
                for form in self.forms:
                    if form.cleaned_data['language'] == language:
                        form.add_error('language', 'Duplicated active language')
                raise forms.ValidationError('Duplicated language')

        for form in self.forms:
            language = form.cleaned_data['language']
            if form.instance.id:
                if form.instance.language == language:
                    continue

            language_in_db = SeoContentData.objects.filter(
                seo_content=self.forms[0].instance.seo_content,
                language=language,
                active=True,
            )

            if not language_in_db.exists():
                continue

            raise forms.ValidationError('Language already active')


class SeoContentFormMixin:
    def clean(self):
        super().clean()
        position = self.cleaned_data['position']
        if self.instance.id and self.instance.position == position:
            return self.cleaned_data

        if SeoContent.objects.filter(
            category=self.cleaned_data['category'],
            content_type=self.cleaned_data['content_type'],
            position=position,
            active=True,
        ).exists():
            raise forms.ValidationError('Duplicated position')
        return self.cleaned_data


class SeoContentForm(SeoContentFormMixin, forms.ModelForm):
    class Meta:
        model = SeoContent
        fields = '__all__'

    content_type = forms.CharField(
        widget=forms.HiddenInput,
        initial=SeoCmsContentType.GENERIC.value,
    )


class SeoRecommended4UForm(SeoContentFormMixin, forms.ModelForm):
    class Meta:
        model = SeoRecommended4U
        fields = '__all__'

    content_type = forms.CharField(
        widget=forms.HiddenInput,
        initial=SeoCmsContentType.RECOMMENDED4U,
    )
    category = forms.NullBooleanField(widget=forms.HiddenInput, initial=None, required=False)


class SeoContentDataInline(admin.StackedInline):
    model = SeoContentData
    form = SeoContentDataForm
    formset = SeoContentDataFormSet
    extra = 0
    fieldsets = (
        (
            None,
            {
                'fields': (
                    ('language', 'active'),
                    ('title',),
                    ('body',),
                    ('image', 'image_alt', 'image_title'),
                    ('data',),
                ),
            },
        ),
    )


class SeoRecommended4UDataInline(admin.StackedInline):
    model = SeoContentData
    form = SeoRecommended4UDataForm
    formset = SeoContentDataFormSet
    extra = 0
    fieldsets = (
        (
            None,
            {
                'fields': (
                    ('language', 'active'),
                    ('title',),
                    ('image', 'image_alt', 'image_title'),
                    ('target_url',),
                    ('data',),
                ),
            },
        ),
    )


def disable_seo_content(modeladmin, request, queryset):
    ids_to_delete = list(queryset.values_list('content_data__id', flat=True))

    with transaction.atomic():
        queryset.update(active=False)
        SeoContentData.objects.filter(seo_content__in=queryset).update(active=False)
        bump_document(River.SEO_CMS_CONTENT_DATA, ids_to_delete)


def enable_seo_content(modeladmin, request, queryset):
    ids_to_delete = list(queryset.values_list('content_data__id', flat=True))

    with transaction.atomic():
        queryset.update(active=True)
        SeoContentData.objects.filter(seo_content__in=queryset).update(active=True)
        bump_document(River.SEO_CMS_CONTENT_DATA, ids_to_delete)


def delete_seo_content(modeladmin, request, queryset):
    ids_to_delete = list(queryset.values_list('content_data__id', flat=True))

    with transaction.atomic():
        SeoContentData.objects.filter(seo_content__in=queryset).soft_delete()
        queryset.soft_delete()
        bump_document(River.SEO_CMS_CONTENT_DATA, ids_to_delete)


disable_seo_content.short_description = 'Disable SEO Content'
enable_seo_content.short_description = 'Enable SEO Content'


class SeoContentAdmin(GroupPermissionMixin, admin.ModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    list_display = ('id', 'name', 'category', 'position', 'active')
    search_fields = ('name', 'category__id', 'position', 'active')
    list_filter = ('category', 'position', 'active')
    fields = ('name', 'position', 'category', 'content_type')
    form = SeoContentForm
    inlines = [
        SeoContentDataInline,
    ]
    actions = [
        disable_seo_content,
        enable_seo_content,
    ]

    def get_actions(self, request):
        actions = super().get_actions(request)
        actions['delete_selected'] = (
            delete_seo_content,
            'delete_selected',
            actions['delete_selected'][2],
        )
        return actions


class SeoRecommended4UAdmin(GroupPermissionMixin, admin.ModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    list_display = ('id', 'name', 'position', 'active')
    search_fields = ('name',)
    list_filter = ('position', 'active')
    fields = ('name', 'category', 'position', 'active', 'content_type')
    form = SeoRecommended4UForm
    inlines = [
        SeoRecommended4UDataInline,
    ]
    actions = [
        disable_seo_content,
        enable_seo_content,
    ]

    def get_actions(self, request):
        actions = super().get_actions(request)
        actions['delete_selected'] = (
            delete_seo_content,
            'delete_selected',
            actions['delete_selected'][2],
        )
        return actions


admin.site.register(CmsContent, CmsContentAdmin)
admin.site.register(SeoRegionHomepageCategoryTemplate, SeoRegionHomepageCategoryTemplateAdmin)
admin.site.register(SeoRegionHomepage, SeoRegionHomepageAdmin)
admin.site.register(SeoFeatureFlag, SeoFeatureFlagAdmin)
admin.site.register(SeoContent, SeoContentAdmin)
admin.site.register(SeoRecommended4U, SeoRecommended4UAdmin)
admin.site.register(SeoRegionCategoryListingTemplate, SeoRegionCategoryListingTemplateAdmin)
admin.site.register(SeoRegionCategoryListing, SeoRegionCategoryListingAdmin)
admin.site.register(SeoMetadata, SeoMetadataAdmin)
admin.site.register(BusinessMarketPlaceSlot, BusinessMarketPlaceSlotAdmin)
admin.site.register(BusinessNetwork, BusinessNetworkAdmin)
admin.site.register(MarketplaceCommission, CommissionAdmin)
admin.site.register(MatchTreatmentBatch, MatchTreatmentBatchAdmin)
admin.site.register(MarketplaceTransaction, MarketplaceTransactionAdmin)
admin.site.register(MarketplaceTransactionRow, MarketplaceTransactionRowAdmin)
admin.site.register(BoostClientCard, BoostClientCardAdmin)
admin.site.register(MarketplaceBListingAcquisition, MarketplaceBListingAcquisitionAdmin)
admin.site.register(MarketplaceClaimDenyReason, MarketplaceClaimDenyReasonAdmin)
admin.site.register(MarketplaceStage, MarketplaceStageAdmin)
admin.site.register(MarketplaceStageStatus, MarketplaceStageStatusAdmin)
