from django.contrib import admin

from lib.admin_helpers import BaseModelAdmin, NoAddMixin, NoDelMixin, RemovalCandidateMixin

from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin
from webapps.best_of_booksy.models import BestOfBooksyBusinessAward, BestOfBooksyAwardPeriod
from webapps.user.groups import GroupNameV2


class BestOfBooksyBusinessAwardAdmin(
    RemovalCandidateMixin, NoAddMixin, GroupPermissionMixin, BaseModelAdmin
):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    model = BestOfBooksyBusinessAward

    list_display = (
        'id',
        'business',
        'business_category_id',
        'business_category_name',
        'region_name',
        'certificate',
        'badge_name',
    )

    readonly_fields = (
        'id',
        'business',
        'business_category_id',
        'business_category_name',
        'region_name',
        'certificate',
        'created',
        'updated',
        'deleted',
        'badge_name',
    )

    search_fields = (
        '=business_id',
        '=business_category_id',
        '=region_name',
        '=award__name',
        '=award__award_period__period_name',
    )

    hide_keyword_field = True

    def get_queryset(self, request):
        return super().get_queryset(request).prefetch_related('award', 'award__award_period')

    @staticmethod
    def badge_name(obj):
        return obj.award.badge_name


class BestOfBooksyAwardPeriodAdmin(
    RemovalCandidateMixin, NoAddMixin, NoDelMixin, GroupPermissionMixin, BaseModelAdmin
):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    model = BestOfBooksyAwardPeriod

    list_display = (
        'id',
        'visible_from',
        'visible_till',
        'period_name',
    )

    readonly_fields = (
        'id',
        'period_name',
    )

    search_fields = ('=period_name',)

    hide_keyword_field = True


admin.site.register(BestOfBooksyBusinessAward, BestOfBooksyBusinessAwardAdmin)
admin.site.register(BestOfBooksyAwardPeriod, BestOfBooksyAwardPeriodAdmin)
