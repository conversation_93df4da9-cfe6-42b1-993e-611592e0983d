from dataclasses import dataclass


@dataclass(frozen=True)
class Coordinate:
    longitude: float
    latitude: float


@dataclass(frozen=True)
class Location:
    coordinate: Coordinate
    address: str
    city: str


@dataclass(frozen=True)
class VenueLocation:
    zipcode: str
    coordinate: Coordinate
    address: str
    city: str
    address2: str


@dataclass(frozen=True)
class VenueLastUpdated:
    contractor_name: str
    contractor_id: int
    updated: str


@dataclass(frozen=True)
class VenueMeta:
    score: float
    id: str
    item_no: int


@dataclass(frozen=True)
class VenueDetails:
    id: int
    name: str
    cover_photo: str
    thumbnail_photo: str
    location: Location
    meta: VenueMeta
    venue_location: VenueLocation
    last_updated: VenueLastUpdated
