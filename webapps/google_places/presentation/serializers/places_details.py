from rest_framework import serializers


class GooglePlacesDetailsRequestSerializer(serializers.Serializer):
    """Serializer for validating place_id input."""

    place_id = serializers.CharField(
        max_length=255, min_length=10, help_text="Google Places API place identifier"
    )


class GooglePlacesAddressComponentsSerializer(serializers.Serializer):
    street_number = serializers.CharField()
    street = serializers.CharField()
    city = serializers.CharField()
    country = serializers.CharField()
    zipcode = serializers.CharField()
    subpremise = serializers.CharField()


class GooglePlacesLocationSerializer(serializers.Serializer):
    latitude = serializers.FloatField()
    longitude = serializers.FloatField()


class VenueCoordinateSerializer(serializers.Serializer):
    latitude = serializers.FloatField()
    longitude = serializers.FloatField()


class VenueMetaSerializer(serializers.Serializer):
    score = serializers.FloatField()
    id = serializers.Cha<PERSON><PERSON><PERSON>()
    item_no = serializers.IntegerField()


class LocationVSerializer(serializers.Serializer):
    coordinate = VenueCoordinateSerializer()
    address = serializers.CharField()
    city = serializers.CharField()


class VenueLocationSerializer(serializers.Serializer):
    zipcode = serializers.CharField()
    coordinate = VenueCoordinateSerializer()
    address = serializers.CharField()
    city = serializers.CharField()
    address2 = serializers.CharField()


class VenueLastUpdatedSerializer(serializers.Serializer):
    contractor_name = serializers.CharField()
    contractor_id = serializers.IntegerField()
    updated = serializers.CharField()


class VenueSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    name = serializers.CharField()
    cover_photo = serializers.CharField()
    thumbnail_photo = serializers.CharField()
    location = LocationVSerializer()
    meta = VenueMetaSerializer()
    venue_location = VenueLocationSerializer()
    last_updated = VenueLastUpdatedSerializer()


class DetailsResponseSerializer(serializers.Serializer):
    """
    V1 Response serializer - DEPRECATED.
    This serializer is used by the deprecated V1 endpoint and will be removed after
    conclusion of Google Address Matchability V1&V2 experiment.
    New integrations should use DetailsResponseSerializerV2.
    """

    place_id = serializers.CharField()
    name = serializers.CharField()
    formatted_address = serializers.CharField()
    location = GooglePlacesLocationSerializer()
    address_components = GooglePlacesAddressComponentsSerializer()
    venue = (
        VenueSerializer()
    )  # DEPRECATED: Will be removed after conclusion of Google Address V1&V2 experiment


class DetailsResponseSerializerV2(serializers.Serializer):
    """V2 serializer without venue field."""

    place_id = serializers.CharField()
    name = serializers.CharField()
    formatted_address = serializers.CharField()
    location = GooglePlacesLocationSerializer()
    address_components = GooglePlacesAddressComponentsSerializer()
    address_lines = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        allow_empty=True,
        help_text="Address lines from postal address",
    )
    administrative_area = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Administrative area (state/province) from postal address",
    )
