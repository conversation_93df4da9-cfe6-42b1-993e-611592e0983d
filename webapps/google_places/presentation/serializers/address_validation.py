from rest_framework import serializers

from webapps.google_places.application.dtos.address_validation import (
    PossibleNextAction,
    PinLocationStatus,
)


class LocationSerializer(serializers.Serializer):
    latitude = serializers.FloatField()
    longitude = serializers.FloatField()


class AddressComponentsSerializer(serializers.Serializer):
    city = serializers.CharField(required=False, allow_blank=True)
    country = serializers.CharField(required=False, allow_blank=True)
    zipcode = serializers.CharField(required=False, allow_blank=True)
    subpremise = serializers.CharField(required=False, allow_blank=True, allow_null=True)


class AddressValidationRequestSerializer(serializers.Serializer):
    address_lines = serializers.ListField(
        child=serializers.CharField(max_length=255), min_length=1, help_text="List of address lines"
    )
    administrative_area = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
        help_text="Administrative area (e.g., state, province)",
    )
    location = LocationSerializer(help_text="Latitude and longitude coordinates")
    address_components = AddressComponentsSerializer(help_text="Address component details")
    previous_response_id = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
        help_text="Response ID from previous validation request",
    )


class CorrectedAddressSerializer(serializers.Serializer):
    postal_code = serializers.CharField()
    administrative_area = serializers.CharField(allow_null=True)
    locality = serializers.CharField()
    address_lines = serializers.ListField(child=serializers.CharField())


class PinLocationSerializer(serializers.Serializer):
    status = serializers.ChoiceField(choices=[status.value for status in PinLocationStatus])
    text = serializers.CharField()

    def to_representation(self, instance):
        """Convert enum objects to their string values for JSON serialization."""
        data = super().to_representation(instance)

        # Convert enum object to its string value
        if hasattr(instance.status, 'value'):
            data['status'] = instance.status.value

        return data


class AddressValidationResponseSerializer(serializers.Serializer):
    possible_next_action = serializers.ChoiceField(
        choices=[action.value for action in PossibleNextAction]
    )
    formatted_address = serializers.CharField()
    address_complete = serializers.BooleanField()
    has_replaced_components = serializers.BooleanField()
    missing_components = serializers.ListField(child=serializers.CharField(), default=list)
    unconfirmed_components = serializers.ListField(child=serializers.CharField(), default=list)
    replaced_components = serializers.ListField(child=serializers.CharField(), default=list)
    corrected_address = CorrectedAddressSerializer()
    pin_location = PinLocationSerializer()
    response_id = serializers.CharField()

    def to_representation(self, instance):
        """Convert enum objects to their string values for JSON serialization."""
        data = super().to_representation(instance)

        # Convert enum objects to their string values
        if hasattr(instance.possible_next_action, 'value'):
            data['possible_next_action'] = instance.possible_next_action.value

        return data
