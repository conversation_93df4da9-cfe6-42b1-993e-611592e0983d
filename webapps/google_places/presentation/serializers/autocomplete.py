from rest_framework import serializers


class GooglePlacesAddressSuggestionsResponseSerializer(serializers.Serializer):
    place_id = serializers.CharField()
    main_text = serializers.CharField()
    secondary_text = serializers.Char<PERSON>ield()


class GooglePlacesSuggestionsResponseSerializer(serializers.Serializer):
    place_id = serializers.CharField()
    main_text = serializers.CharField()
    secondary_text = serializers.CharField()


class GooglePlacesAutocompleteSerializer(serializers.Serializer):
    search_input = serializers.Char<PERSON>ield(required=False, allow_blank=True)
    longitude = serializers.FloatField(required=False, allow_null=True)
    latitude = serializers.FloatField(required=False, allow_null=True)


class GooglePlacesAddressAutocompleteResponseSerializer(serializers.Serializer):
    suggestions = GooglePlacesSuggestionsResponseSerializer(many=True, default=[])


class GooglePlacesAutocompleteResponseSerializer(serializers.Serializer):
    suggestions = GooglePlacesAddressSuggestionsResponseSerializer(many=True, default=[])


class GooglePlacesBusinessSuggestionsResponseSerializer(serializers.Serializer):
    place_id = serializers.CharField()
    main_text = serializers.CharField()
    secondary_text = serializers.CharField()


class GooglePlacesBusinessAutocompleteSerializer(serializers.Serializer):
    search_input = serializers.CharField(required=True, allow_blank=False)


class GooglePlacesBusinessAutocompleteResponseSerializer(serializers.Serializer):
    suggestions = GooglePlacesAddressSuggestionsResponseSerializer(many=True, default=[])
