from django.urls import path

from webapps.google_places.presentation.views.google_places import (
    GooglePlacesDetailsView,
    GooglePlacesDetailsViewV2,
    GooglePlacesAddressAutocompleteView,
    GooglePlacesAddressValidationView,
    GooglePlacesBusinessAutocompleteView,
    GooglePlacesAutocompleteView,
)

urlpatterns = [
    # V1 endpoint - DEPRECATED: Use V2 endpoint instead
    path(
        'details/<str:place_id>/', GooglePlacesDetailsView.as_view(), name='google_places_details'
    ),
    # V2 endpoint - Recommended: Returns place details without venue information
    path(
        'v2/details/<str:place_id>/',
        GooglePlacesDetailsViewV2.as_view(),
        name='google_places_details_v2',
    ),
    path(
        'autocomplete/address/',
        GooglePlacesAddressAutocompleteView.as_view(),
        name='google_places_address_autocomplete',
    ),
    path(
        'autocomplete/business/',
        GooglePlacesBusinessAutocompleteView.as_view(),
        name='google_places_business_autocomplete',
    ),
    path(
        'autocomplete/',
        GooglePlacesAutocompleteView.as_view(),
        name='google_places_autocomplete',
    ),
    path(
        'validate_address/',
        GooglePlacesAddressValidationView.as_view(),
        name='google_places_validate_address',
    ),
]
