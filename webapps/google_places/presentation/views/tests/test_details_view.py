from unittest.mock import patch, PropertyMock
from abc import ABC, abstractmethod

import pytest
from django.urls import reverse
from rest_framework import status

from drf_api.lib.base_drf_test_case import BaseBusinessApiTestCase
from webapps.business.baker_recipes import business_recipe, resource_recipe
from webapps.business.models import Resource

from webapps.user.baker_recipes import user_recipe


class BaseDetailsViewTest(BaseBusinessApiTestCase, ABC):
    """Base test class for Google Places details endpoints."""

    def setUp(self):
        self.user = user_recipe.make()
        self.business = business_recipe.make()
        self.resource = resource_recipe.make(
            business=self.business,
            staff_user=self.user,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_MANAGER,
        )
        super().setUp()
        self.valid_place_id = "ChIJN1t_tDeuEmsRUsoyG83frY4"
        self.mock_response_data = {
            "id": "ChIJEfoXPH01GUcRpFGQ0u5rU10",
            "formattedAddress": "Starodęby 10, 02-497 Warszawa, Poland",
            "location": {"latitude": 52.186305399999995, "longitude": 20.8947561},
            "displayName": {"text": "APX GP Barbershop"},
            "addressComponents": [
                {"longText": "10", "types": ["street_number"]},
                {"longText": "Starodęby", "types": ["route"]},
                {"longText": "Warszawa", "types": ["locality", "political"]},
                {"longText": "Poland", "types": ["country", "political"]},
                {"longText": "02-497", "types": ["postal_code"]},
                {"longText": "32", "types": ["subpremise"]},
            ],
        }

    def get_base_expected_response(self):
        """Get base expected response without venue or postal address fields."""
        return {
            'place_id': 'ChIJEfoXPH01GUcRpFGQ0u5rU10',
            'name': 'APX GP Barbershop',
            'formatted_address': 'Starodęby 10, 02-497 Warszawa, Poland',
            'location': {'latitude': 52.186305399999995, 'longitude': 20.8947561},
            'address_components': {
                'street_number': '10',
                'street': 'Starodęby',
                'city': 'Warszawa',
                'country': 'Poland',
                'zipcode': '02-497',
                'subpremise': '32',
            },
        }

    @abstractmethod
    def get_url_name(self) -> str:
        """Return the URL name for this endpoint."""

    def _test_get_success(self):
        """Test successful GET request to Google Places details endpoint."""
        with patch('v2.shared.http.http_client.HttpClient.get') as mock_get:
            mock_get.return_value = {'status_code': 200, 'data': self.mock_response_data}

            url = reverse(
                self.get_url_name(),
                kwargs={"business_pk": self.business.id, "place_id": self.valid_place_id},
            )
            response = self.client.get(url)

            self.assertEqual(status.HTTP_200_OK, response.status_code)
            mock_get.assert_called_once()

    def _test_session_token_included_in_params_when_session_id_provided(self):
        """Test that session token is included in the URL when session_id is provided."""
        with patch('v2.shared.http.http_client.HttpClient.get') as mock_get:
            mock_get.return_value = {'status_code': 200, 'data': self.mock_response_data}

            test_session_key = "session_key_mock"
            expected_token = "mock-session-token"

            url = reverse(
                self.get_url_name(),
                kwargs={"business_pk": self.business.id, "place_id": self.valid_place_id},
            )

            with patch.object(
                type(self.client.session), 'session_key', new_callable=PropertyMock
            ) as mock_session_key:
                mock_session_key.return_value = test_session_key

                # Mock the token cache to return expected token
                with patch(
                    'webapps.google_places.infrastructure.cache.SessionTokenCache.'
                    'get_or_create_token'
                ) as mock_token_cache:
                    mock_token_cache.return_value = expected_token
                    response = self.client.get(url)

            self.assertEqual(status.HTTP_200_OK, response.status_code)
            mock_get.assert_called_once()
            _, called_kwargs = mock_get.call_args
            self.assertEqual(expected_token, called_kwargs["params"]["sessionToken"])
            mock_token_cache.assert_called_once_with(test_session_key)


@pytest.mark.django_db
class TestDetailsView(BaseDetailsViewTest):
    """Test class for V1 Google Places details endpoint."""

    def get_url_name(self) -> str:
        return "google_places_details"

    def test_get_success(self):
        """Test successful GET request to Google Places details endpoint."""
        self._test_get_success()

    @patch('v2.shared.http.http_client.HttpClient.get')
    def test_get_response(self, mock_get):
        """Test GET request response format."""
        mock_get.return_value = {'status_code': 200, 'data': self.mock_response_data}

        url = reverse(
            self.get_url_name(),
            kwargs={"business_pk": self.business.id, "place_id": self.valid_place_id},
        )
        response = self.client.get(url)
        expected_response = self.get_base_expected_response()
        expected_response['venue'] = None

        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response = response.json()
        self.assertIsInstance(response, dict)
        self.assertEqual(expected_response, response)
        mock_get.assert_called_once()

    def test_session_token_included_in_params_when_session_id_provided(self):
        """Test that session token is included in the URL when session_id is provided."""
        self._test_session_token_included_in_params_when_session_id_provided()


@pytest.mark.django_db
class TestDetailsViewV2(BaseDetailsViewTest):
    """Test class for V2 Google Places details endpoint."""

    def setUp(self):
        super().setUp()
        # Add postalAddress data for V2 tests
        self.mock_response_data["postalAddress"] = {
            "addressLines": ["Starodęby 10"],
            "administrativeArea": "Mazowieckie",
            "locality": "Warszawa",
            "postalCode": "02-497",
            "country": "Poland",
            "regionCode": "PL",
        }

    def get_url_name(self) -> str:
        return "google_places_details_v2"

    def test_get_success(self):
        """Test successful GET request to Google Places V2 details endpoint."""
        self._test_get_success()

    @patch('v2.shared.http.http_client.HttpClient.get')
    def test_get_response(self, mock_get):
        """Test GET request response format for V2 endpoint."""
        mock_get.return_value = {'status_code': 200, 'data': self.mock_response_data}

        url = reverse(
            self.get_url_name(),
            kwargs={"business_pk": self.business.id, "place_id": self.valid_place_id},
        )
        response = self.client.get(url)
        expected_response = self.get_base_expected_response()
        expected_response.update(
            {
                'address_lines': ['Starodęby 10'],
                'administrative_area': 'Mazowieckie',
            }
        )

        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response = response.json()
        self.assertIsInstance(response, dict)
        self.assertEqual(expected_response, response)
        mock_get.assert_called_once()

    @patch('v2.shared.http.http_client.HttpClient.get')
    def test_get_response_with_postal_address(self, mock_get):
        """Test GET request response when postalAddress is present in Google API response.

        This test validates the V2 endpoint's ability to properly extract and return
        postal address information including address lines and administrative area.
        """
        mock_response_data = {
            "id": "ChIJEfoXPH01GUcRpFGQ0u5rU10",
            "formattedAddress": "Starodęby 10, 02-497 Warszawa, Poland",
            "location": {"latitude": 52.186305399999995, "longitude": 20.8947561},
            "displayName": {"text": "APX GP Barbershop"},
            "addressComponents": [
                {"longText": "10", "types": ["street_number"]},
                {"longText": "Starodęby", "types": ["route"]},
                {"longText": "Warszawa", "types": ["locality", "political"]},
                {"longText": "Poland", "types": ["country", "political"]},
                {"longText": "02-497", "types": ["postal_code"]},
                {"longText": "32", "types": ["subpremise"]},
            ],
            "postalAddress": {
                "addressLines": ["Starodęby 10"],
                "administrativeArea": "Mazowieckie",
                "locality": "Warszawa",
                "postalCode": "02-497",
                "country": "Poland",
                "regionCode": "PL",
            },
        }
        mock_get.return_value = {'status_code': 200, 'data': mock_response_data}

        url = reverse(
            self.get_url_name(),
            kwargs={"business_pk": self.business.id, "place_id": self.valid_place_id},
        )
        response = self.client.get(url)

        expected_response = self.get_base_expected_response()
        expected_response.update(
            {
                'address_lines': ['Starodęby 10'],
                'administrative_area': 'Mazowieckie',
            }
        )

        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response = response.json()
        self.assertIsInstance(response, dict)
        self.assertEqual(expected_response, response)
        mock_get.assert_called_once()

    def test_session_token_included_in_params_when_session_id_provided(self):
        """Test that session token is included in the URL when session_id is provided."""
        self._test_session_token_included_in_params_when_session_id_provided()

    @patch('v2.shared.http.http_client.HttpClient.get')
    def test_get_response_with_missing_route_and_doubled_locality(self, mock_get):
        """Test GET request response when route is missing and multiple locality components exist.
        Currently, we don't support this scenario in any specific way.

        This tests a real-world Polish address scenario where a village name (Wygoda)
        serves as both the street name and locality, and there are multiple locality
        components (Wygoda, Wola Krzysztoporska).
        """
        mock_response_data_polish_village = {
            "id": "ChIJEfoXPH01GUcRpFGQ0u5rU10",
            "formattedAddress": "Wygoda 55, 97-300 Wola Krzysztoporska, Poland",
            "location": {"latitude": 51.123456, "longitude": 19.654321},
            "displayName": {"text": "Some random name"},
            "addressComponents": [
                {"longText": "55", "types": ["street_number"]},
                {"longText": "Wygoda", "types": ["locality", "political"]},
                {"longText": "Wola Krzysztoporska", "types": ["locality", "political"]},
                {"longText": "Poland", "types": ["country", "political"]},
                {"longText": "97-300", "types": ["postal_code"]},
            ],
            "postalAddress": {
                "addressLines": ["Wygoda 55"],
                "administrativeArea": "Łódzkie",
                "locality": "Wola Krzysztoporska",
                "postalCode": "97-300",
                "country": "Poland",
                "regionCode": "PL",
            },
        }
        mock_get.return_value = {'status_code': 200, 'data': mock_response_data_polish_village}

        url = reverse(
            self.get_url_name(),
            kwargs={"business_pk": self.business.id, "place_id": self.valid_place_id},
        )
        response = self.client.get(url)
        expected_response = self.get_base_expected_response()
        expected_response.update(
            {
                'formatted_address': 'Wygoda 55, 97-300 Wola Krzysztoporska, Poland',
                'name': 'Some random name',
                'location': {'latitude': 51.123456, 'longitude': 19.654321},
                'address_components': {
                    'street_number': '55',
                    'street': '',
                    'city': 'Wola Krzysztoporska',
                    'country': 'Poland',
                    'zipcode': '97-300',
                    'subpremise': '',
                },
                'address_lines': ['Wygoda 55'],
                'administrative_area': 'Łódzkie',
            }
        )

        self.assertEqual(status.HTTP_200_OK, response.status_code)
        response = response.json()
        self.assertIsInstance(response, dict)
        self.assertEqual(expected_response, response)
        mock_get.assert_called_once()
