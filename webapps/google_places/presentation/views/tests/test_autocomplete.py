# pylint: disable=protected-access, line-too-long
from unittest.mock import patch, PropertyMock
import pytest
from django.urls import reverse
from rest_framework import status

from drf_api.lib.base_drf_test_case import BaseBusinessApiTestCase
from webapps.business.baker_recipes import business_recipe, resource_recipe
from webapps.business.models import Resource
from webapps.google_places.application.dtos.autocomplete import AutocompleteSuggestions, Suggestion

from webapps.user.baker_recipes import user_recipe


@pytest.mark.django_db
class TestAutocompleteView(BaseBusinessApiTestCase):
    def setUp(self):
        self.user = user_recipe.make()
        self.business = business_recipe.make()
        self.resource = resource_recipe.make(
            business=self.business,
            staff_user=self.user,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_MANAGER,
        )
        self.mock_get_suggestions = AutocompleteSuggestions(
            suggestions=[
                Suggestion(
                    place_id="ChIJ8zVuZV8tPUcRI_KDe8IR1Vc",
                    main_text="Gentlemen Barber Shop Stalowa Wola",
                    secondary_text="Generała Leopold<PERSON>ego, Stalowa Wola, Polska",
                ),
                Suggestion(
                    place_id="ChIJN1t_tDeuEmsRUsoyG83frY4",
                    main_text="Teodora Zielińskiego 4",
                    secondary_text="Radom, Polska",
                ),
            ]
        )
        super().setUp()

    @patch(
        'webapps.google_places.application.services.autocomplete.AutocompleteService.get_suggestions'
    )
    def test_get_success(self, mock_get_suggestions):
        """Test successful GET request to Google Places details endpoint."""
        mock_get_suggestions.return_value = self.mock_get_suggestions
        url = reverse(
            "google_places_autocomplete",
            kwargs={
                "business_pk": self.business.id,
            },
        )
        query_params = {
            'search_input': "Gentelman",
            'longitude': 10.02310,
            'latitude': 132.3123,
        }
        response = self.client.get(url, query_params)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

    @patch(
        'webapps.google_places.application.services.autocomplete.AutocompleteService.get_suggestions'
    )
    def test_get_response(self, mock_get_suggestions):
        mock_get_suggestions.return_value = self.mock_get_suggestions
        url = reverse(
            "google_places_autocomplete",
            kwargs={
                "business_pk": self.business.id,
            },
        )
        query_params = {
            'search_input': "Gentelman",
            'longitude': 10.02310,
            'latitude': 132.3123,
        }

        response = self.client.get(url, query_params)
        expected_response = {
            'suggestions': [
                {
                    'place_id': 'ChIJ8zVuZV8tPUcRI_KDe8IR1Vc',
                    'main_text': 'Gentlemen Barber Shop Stalowa Wola',
                    'secondary_text': 'Generała Leopolda Okulickiego, Stalowa Wola, Polska',
                },
                {
                    'place_id': 'ChIJN1t_tDeuEmsRUsoyG83frY4',
                    'main_text': 'Teodora Zielińskiego 4',
                    'secondary_text': 'Radom, Polska',
                },
            ]
        }
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        response = response.json()
        self.assertIsInstance(response, dict)
        self.assertEqual(expected_response, response)

    def test_empty_search_input(self):
        """Test GET request with empty/blank input returns empty suggestions."""
        empty_url = reverse(
            "google_places_autocomplete",
            kwargs={"business_pk": self.business.id},
        )

        response = self.client.get(empty_url, {})

        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertEqual({'suggestions': []}, response.json())

    @patch(
        'webapps.google_places.application.services.autocomplete.AutocompleteService.get_suggestions'
    )
    def test_session_id_passed_from_request(self, mock_get_suggestions):
        """Test that request.session.session_key is passed as session_id to service."""
        mock_get_suggestions.return_value = self.mock_get_suggestions

        # Mock the session to have a specific session_key
        test_session_key = "test_session_key_12345"

        url = reverse(
            "google_places_autocomplete",
            kwargs={"business_pk": self.business.id},
        )
        query_params = {
            'search_input': "Coffee Shop",
            'longitude': 50.0647,
            'latitude': 19.9450,
        }

        # Make request with mocked session
        with patch.object(
            type(self.client.session), 'session_key', new_callable=PropertyMock
        ) as mock_session_key:
            mock_session_key.return_value = test_session_key
            response = self.client.get(url, query_params)

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Verify that the service was called with the session_key
        mock_get_suggestions.assert_called_once()
        call_args = mock_get_suggestions.call_args

        # Check that session_id was passed (should be the 3rd argument or named parameter)
        self.assertEqual(call_args.kwargs.get('session_id'), test_session_key)

    @patch(
        'webapps.google_places.application.services.autocomplete.'
        'AutocompleteService.get_suggestions'
    )
    def test_session_id_empty_when_no_session_key(self, mock_get_suggestions):
        """Test that session_id is empty string when request has no meaningful session_key."""
        mock_get_suggestions.return_value = self.mock_get_suggestions

        url = reverse(
            "google_places_autocomplete",
            kwargs={"business_pk": self.business.id},
        )
        query_params = {
            'search_input': "Restaurant",
        }
        with patch.object(
            type(self.client.session), 'session_key', new_callable=PropertyMock
        ) as mock_session_key:
            mock_session_key.return_value = ""
            response = self.client.get(url, query_params)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        mock_get_suggestions.assert_called_once()
        call_args = mock_get_suggestions.call_args
        self.assertEqual(call_args.kwargs.get('session_id'), "")

    @patch(
        'webapps.google_places.application.services.autocomplete.'
        'AutocompleteService.get_suggestions'
    )
    def test_multiple_requests_same_session_use_same_session_id(self, mock_get_suggestions):
        """Test that multiple requests in the same session use the same session_id."""
        mock_get_suggestions.return_value = self.mock_get_suggestions

        test_session_key = "persistent_session_789"

        url = reverse(
            "google_places_autocomplete",
            kwargs={"business_pk": self.business.id},
        )

        with patch.object(
            type(self.client.session), 'session_key', new_callable=PropertyMock
        ) as mock_session_key:
            mock_session_key.return_value = test_session_key

            # First request
            query_params_1 = {'search_input': "First Search"}
            response_1 = self.client.get(url, query_params_1)

            # Second request in same session
            query_params_2 = {'search_input': "Second Search"}
            response_2 = self.client.get(url, query_params_2)

        self.assertEqual(response_1.status_code, status.HTTP_200_OK)
        self.assertEqual(response_2.status_code, status.HTTP_200_OK)

        # Verify both calls used the same session_id
        self.assertEqual(2, mock_get_suggestions.call_count)

        first_call_session_id = mock_get_suggestions.call_args_list[0].kwargs.get('session_id')
        second_call_session_id = mock_get_suggestions.call_args_list[1].kwargs.get('session_id')

        self.assertEqual(first_call_session_id, test_session_key)
        self.assertEqual(second_call_session_id, test_session_key)
        self.assertEqual(first_call_session_id, second_call_session_id)
