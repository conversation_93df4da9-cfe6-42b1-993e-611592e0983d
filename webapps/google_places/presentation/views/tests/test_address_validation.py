# pylint: disable=line-too-long
from unittest.mock import patch
import pytest
from django.urls import reverse
from rest_framework import status

from drf_api.lib.base_drf_test_case import BaseBusinessApiTestCase
from webapps.business.baker_recipes import business_recipe, resource_recipe
from webapps.business.models import Resource
from webapps.google_places.application.dtos.address_validation import (
    AddressValidationResponse,
    PossibleNextAction,
    CorrectedAddress,
    PinLocation,
    PinLocationStatus,
)

from webapps.user.baker_recipes import user_recipe


@pytest.mark.django_db
class TestAddressValidationView(BaseBusinessApiTestCase):
    def setUp(self):
        self.user = user_recipe.make()
        self.business = business_recipe.make()
        self.resource = resource_recipe.make(
            business=self.business,
            staff_user=self.user,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_MANAGER,
        )
        self.mock_validation_response = AddressValidationResponse(
            possible_next_action=PossibleNextAction.ACCEPT,
            formatted_address="123 Main Street, Test City, 12345, Test Country",
            address_complete=True,
            has_replaced_components=False,
            missing_components=[],
            unconfirmed_components=[],
            replaced_components=[],
            corrected_address=CorrectedAddress(
                postal_code="12345",
                administrative_area="Test State",
                locality="Test City",
                address_lines=["123 Main Street"],
            ),
            pin_location=PinLocation(
                status=PinLocationStatus.OK, text="Address location confirmed"
            ),
            response_id="test-response-id-12345",
        )
        super().setUp()

    @patch(
        'webapps.google_places.application.services.address_validation.AddressValidationService.validate_address'
    )
    def test_post_success(self, mock_address_validation):
        """Test successful POST request to Google Places
        address validation endpoint."""
        mock_address_validation.return_value = self.mock_validation_response
        url = reverse(
            "google_places_validate_address",
            kwargs={
                "business_pk": self.business.id,
            },
        )
        data = {
            'address_lines': ['123 Main Street', 'Suite 100'],
            'administrative_area': 'CA',
            'location': {'latitude': 52.539822699999995, 'longitude': 19.721941299999997},
            'address_components': {
                'city': 'Płock',
                'country': 'Poland',
                'zipcode': '09-407',
                'subpremise': '',
            },
            'previous_response_id': '9721c868-031f-435e-99bc-1a42625fada6',
        }
        response = self.client.post(url, data=data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('possible_next_action', response.data)
        self.assertEqual(response.data['possible_next_action'], 'ACCEPT')
        mock_address_validation.assert_called_once()
