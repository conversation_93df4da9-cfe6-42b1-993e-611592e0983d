from bo_obs.datadog.enums import BooksyTeams
from lagom import magic_bind_to_container
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.views import Response

from drf_api.base_views import BaseBooksySessionGenericAPIView
from drf_api.mixins import BusinessViewValidatorMixin, QuerySerializerMixin, ResponseSerializerMixin
from webapps.business.models import Resource
from webapps.google_places.application.dtos.address_validation import AddressValidationRequest
from webapps.google_places.application.services.autocomplete import (
    AbstractAutocompleteService,
)
from webapps.google_places.application.services.details import (
    AbstractDetailsService,
    AbstractDetailsServiceV2,
)
from webapps.google_places.application.services.address_validation import (
    AbstractAddressValidationService,
)
from webapps.google_places.containers import container
from webapps.google_places.application.dtos.place_details import (
    Location,
)
from webapps.google_places.presentation.serializers.address_validation import (
    AddressValidationResponseSerializer,
    AddressValidationRequestSerializer,
)
from webapps.google_places.presentation.serializers.autocomplete import (
    GooglePlacesAutocompleteSerializer,
    GooglePlacesAddressAutocompleteResponseSerializer,
    GooglePlacesAutocompleteResponseSerializer,
)
from webapps.google_places.presentation.serializers.places_details import (
    DetailsResponseSerializer,
    DetailsResponseSerializerV2,
)


class GooglePlacesDetailsView(BusinessViewValidatorMixin, BaseBooksySessionGenericAPIView):
    """
    DEPRECATED: This V1 endpoint is deprecated and will be removed after conclusion
    of Google Address Matchability V1&V2 experiment.
    Please use the V2 endpoint.
    The V2 endpoint provides the same functionality but without venue information.
    """

    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING, BooksyTeams.PROVIDER_CONVERSION)
    permission_classes = (IsAuthenticated,)
    required_minimum_access_level = Resource.STAFF_ACCESS_LEVEL_MANAGER
    serializer_class = DetailsResponseSerializer

    """
    Response body attributes:
    `place_id` (str): Google Place ID used to get details about a place.
    `name` (str): Name of a place taken from Google Place Details.
    `formatted_address` (str): Address oneliner taken from Google Place Details.
    `location` (dict): Longitude and Latitude taken from Google Place Details.
    `address_components` (dict): More detailed location data taken from Google Place Details.
    `venue` (dict): DEPRECATED - Venue information (will be removed in V2).
    """

    @magic_bind_to_container(container)
    def get(
        self,
        request: Request,
        business_pk: int,
        place_id: str,
        detail_service: AbstractDetailsService,
        *args,
        **kwargs,
    ):
        """
        Get detailed information about a specific place.

        DEPRECATED: This endpoint is deprecated and will be removed after conclusion
        of Google Address Matchability V1&V2 experiment.
        Use v2.

        Args:
            detail_service (AbstractDetailsService): The service to get place details.
            place_id (str): The Google Places API place identifier starting with ChIJ
        """
        self.get_business(business_pk, check_region=False)
        place_details_dto = detail_service.get_details(
            place_id=place_id, session_id=request.session.session_key
        )
        response_serializer = self.get_serializer(place_details_dto)

        return Response(status=status.HTTP_200_OK, data=response_serializer.data)


class GooglePlacesDetailsViewV2(BusinessViewValidatorMixin, BaseBooksySessionGenericAPIView):
    """V2 endpoint without venue information."""

    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING, BooksyTeams.PROVIDER_CONVERSION)
    permission_classes = (IsAuthenticated,)
    required_minimum_access_level = Resource.STAFF_ACCESS_LEVEL_MANAGER
    serializer_class = DetailsResponseSerializerV2

    """
    Response body attributes:
    `place_id` (str): Google Place ID used to get details about a place.
    `name` (str): Name of a place taken from Google Place Details.
    `formatted_address` (str): Address oneliner taken from Google Place Details.
    `location` (dict): Longitude and Latitude taken from Google Place Details.
    `address_components` (dict): More detailed location data taken from Google Place Details.
    `address_lines` (list): Address lines from postal address (e.g., ["1600 Amphitheatre Pkwy"]).
    `administrative_area` (str): Administrative area/state from postal address (e.g., "California").
    """

    @magic_bind_to_container(container)
    def get(
        self,
        request: Request,
        business_pk: int,
        place_id: str,
        detail_service: AbstractDetailsServiceV2,
        *args,
        **kwargs,
    ):
        """
        Get detailed information about a specific place without venue information.

        Args:
            detail_service (AbstractDetailsServiceV2): The V2 service to get place details.
            place_id (str): The Google Places API place identifier
        """
        self.get_business(business_pk, check_region=False)
        place_details_dto = detail_service.get_details(
            place_id=place_id, session_id=request.session.session_key
        )
        response_serializer = self.get_serializer(place_details_dto)

        return Response(status=status.HTTP_200_OK, data=response_serializer.data)


class GooglePlacesAddressAutocompleteView(
    BusinessViewValidatorMixin, QuerySerializerMixin, BaseBooksySessionGenericAPIView
):
    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING, BooksyTeams.PROVIDER_CONVERSION)
    permission_classes = (IsAuthenticated,)
    required_minimum_access_level = Resource.STAFF_ACCESS_LEVEL_MANAGER
    serializer_class = GooglePlacesAddressAutocompleteResponseSerializer
    query_serializer_class = GooglePlacesAutocompleteSerializer

    @magic_bind_to_container(container)
    def get(
        self,
        request: Request,
        business_pk: int,
        autocomplete_service: AbstractAutocompleteService,
        *args,
        **kwargs,
    ):
        self.get_business(business_pk, check_region=False)
        request_serializer = self.get_query_serializer(data=request.query_params)
        if not request_serializer.is_valid():
            return Response(
                {"errors": request_serializer.errors}, status=status.HTTP_400_BAD_REQUEST
            )
        if not request_serializer.validated_data.get("search_input"):
            return Response(status=status.HTTP_200_OK, data={"suggestions": []})
        location = None
        if (
            "longitude" in request_serializer.validated_data
            and "latitude" in request_serializer.validated_data
        ):
            location = Location(
                request_serializer.validated_data.get("longitude"),
                request_serializer.validated_data.get("latitude"),
            )
        address_autocomplete_dto = autocomplete_service.get_address_suggestions(
            request_serializer.validated_data.get("search_input"),
            location,
            request.session.session_key,
        )
        response_serializer = self.get_serializer(address_autocomplete_dto)
        return Response(status=status.HTTP_200_OK, data=response_serializer.data)


class GooglePlacesBusinessAutocompleteView(
    BusinessViewValidatorMixin, QuerySerializerMixin, BaseBooksySessionGenericAPIView
):
    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING, BooksyTeams.PROVIDER_CONVERSION)
    permission_classes = (IsAuthenticated,)
    required_minimum_access_level = Resource.STAFF_ACCESS_LEVEL_MANAGER
    serializer_class = GooglePlacesAddressAutocompleteResponseSerializer
    query_serializer_class = GooglePlacesAutocompleteSerializer

    @magic_bind_to_container(container)
    def get(
        self,
        request: Request,
        business_pk: int,
        autocomplete_service: AbstractAutocompleteService,
        *args,
        **kwargs,
    ):
        self.get_business(business_pk, check_region=False)
        request_serializer = self.get_query_serializer(data=request.query_params)

        if not request_serializer.is_valid():
            return Response(
                {"errors": request_serializer.errors}, status=status.HTTP_400_BAD_REQUEST
            )
        if not request_serializer.validated_data.get("search_input"):
            return Response(status=status.HTTP_200_OK, data={"suggestions": []})
        location = None
        if (
            "longitude" in request_serializer.validated_data
            and "latitude" in request_serializer.validated_data
        ):
            location = Location(
                request_serializer.validated_data.get("longitude"),
                request_serializer.validated_data.get("latitude"),
            )
        business_autocomplete_dto = autocomplete_service.get_business_suggestions(
            search_input=request_serializer.validated_data.get("search_input"),
            location=location,
            session_id=request.session.session_key,
        )
        response_serializer = self.get_serializer(business_autocomplete_dto)
        return Response(status=status.HTTP_200_OK, data=response_serializer.data)


class GooglePlacesAutocompleteView(
    BusinessViewValidatorMixin, QuerySerializerMixin, BaseBooksySessionGenericAPIView
):
    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING, BooksyTeams.PROVIDER_CONVERSION)
    permission_classes = (IsAuthenticated,)
    required_minimum_access_level = Resource.STAFF_ACCESS_LEVEL_MANAGER
    serializer_class = GooglePlacesAutocompleteResponseSerializer
    query_serializer_class = GooglePlacesAutocompleteSerializer

    @magic_bind_to_container(container)
    def get(
        self,
        request: Request,
        business_pk: int,
        autocomplete_service: AbstractAutocompleteService,
        *args,
        **kwargs,
    ):
        self.get_business(business_pk, check_region=False)
        request_serializer = self.get_query_serializer(data=request.query_params)
        if not request_serializer.is_valid():
            return Response(
                {"errors": request_serializer.errors}, status=status.HTTP_400_BAD_REQUEST
            )
        if not request_serializer.validated_data.get("search_input"):
            return Response(status=status.HTTP_200_OK, data={"suggestions": []})
        location = None
        if (
            "longitude" in request_serializer.validated_data
            and "latitude" in request_serializer.validated_data
        ):
            location = Location(
                request_serializer.validated_data.get("longitude"),
                request_serializer.validated_data.get("latitude"),
            )
        autocomplete_dto = autocomplete_service.get_suggestions(
            request_serializer.validated_data.get("search_input"),
            location,
            session_id=request.session.session_key,
        )
        response_serializer = self.get_serializer(autocomplete_dto)
        return Response(status=status.HTTP_200_OK, data=response_serializer.data)


class GooglePlacesAddressValidationView(
    BusinessViewValidatorMixin, ResponseSerializerMixin, BaseBooksySessionGenericAPIView
):
    booksy_teams = (BooksyTeams.PROVIDER_ONBOARDING, BooksyTeams.PROVIDER_CONVERSION)
    permission_classes = (IsAuthenticated,)
    required_minimum_access_level = Resource.STAFF_ACCESS_LEVEL_MANAGER
    serializer_class = AddressValidationRequestSerializer
    response_serializer_class = AddressValidationResponseSerializer

    @magic_bind_to_container(container)
    def post(
        self,
        request: Request,
        business_pk: int,
        address_validation_service: AbstractAddressValidationService,
        *args,
        **kwargs,
    ):
        """
        Validate an address using Google's Address Validation API.

        Request body should contain an 'address' object with:
        - address_lines: List of address lines
        - administrative_area: Optional administrative area (state/province)
        - location: Object with latitude and longitude
        - address_components: Object with city, country, zipcode, subpremise
        - previous_response_id: Optional response ID from previous request
        """

        self.get_business(business_pk, check_region=False)
        request_serializer = self.get_serializer(data=request.data)
        if not request_serializer.is_valid():
            return Response(
                {"errors": request_serializer.errors}, status=status.HTTP_400_BAD_REQUEST
            )
        validated_data = request_serializer.validated_data
        location_data = validated_data['location']

        address_request = AddressValidationRequest(
            address_lines=validated_data.get('address_lines'),
            administrative_area=validated_data.get('administrative_area'),
            location=Location(
                longitude=location_data.get('longitude'), latitude=location_data.get('latitude')
            ),
            address_components=validated_data.get('address_components'),
            previous_response_id=validated_data.get('previous_response_id'),
        )
        validation_result = address_validation_service.validate_address(
            address_request, session_id=request.session.session_key
        )
        response_serializer = self.get_response_serializer(validation_result)
        # return Response(status=status.HTTP_200_OK, data={'address': response_serializer.data})
        return Response(status=status.HTTP_200_OK, data=response_serializer.data)
