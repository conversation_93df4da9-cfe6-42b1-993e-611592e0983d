from django.apps import AppConfig


class GooglePlacesConfig(AppConfig):
    name = 'webapps.google_places.infrastructure'
    label = 'google_places'
    verbose_name = 'Google Places'

    def ready(self):
        from webapps.google_places.application.services.details import (
            AbstractDetailsService,
            AbstractDetailsServiceV2,
            DetailsService,
            DetailsServiceV2,
        )
        from webapps.google_places.application.services.autocomplete import (
            AbstractAutocompleteService,
            AutocompleteService,
        )
        from webapps.google_places.application.services.address_validation import (
            AbstractAddressValidationService,
            AddressValidationService,
        )
        from webapps.google_places.domain.ports.venue_port import VenuePort
        from webapps.google_places.infrastructure.adapters.venue_adapter import VenueAdapter
        from webapps.google_places.containers import container
        from service.search.v2.application.venue_search_service import VenueSearchService
        from service.search.v2.domain.ports import VenueFinderP<PERSON>, VenueRepository
        from service.search.v2.infrastructure.repository import ElasticsearchVenueRepository
        from webapps.google_places.application.interfaces.google_places_gateway import (
            GooglePlacesGateway,
        )
        from webapps.google_places.infrastructure.gateways.google_places_gateway import (
            GooglePlacesAPIGateway,
        )

        from webapps.google_places.infrastructure.apis.google_places_api import GooglePlacesClient
        from webapps.google_places.application.interfaces.google_places_client import (
            GooglePlacesAbstractClient,
        )

        # Register services
        container[VenueRepository] = lambda c: ElasticsearchVenueRepository()
        container[VenueFinderPort] = lambda c: VenueSearchService(repository=c[VenueRepository])
        container[VenuePort] = lambda c: VenueAdapter(c[VenueFinderPort])
        container[GooglePlacesGateway] = GooglePlacesAPIGateway
        container[GooglePlacesAbstractClient] = GooglePlacesClient
        container[AbstractDetailsService] = DetailsService
        container[AbstractDetailsServiceV2] = DetailsServiceV2
        container[AbstractAutocompleteService] = AutocompleteService
        container[AbstractAddressValidationService] = AddressValidationService
