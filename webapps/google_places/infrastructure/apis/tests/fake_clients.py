import json
import os

from webapps.google_places.application.interfaces.google_places_client import (
    GooglePlacesAbstractClient,
    ApiResponse,
)
from webapps.google_places.application.dtos.place_details import Location


class FakeGooglePlacesClientRepository:
    @staticmethod
    def get_details():
        current_dir = os.path.dirname(__file__)
        file_path = os.path.join(current_dir, '_data', 'place_details_api_reponse_example.json')
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)

    @staticmethod
    def get_business_autocomplete_suggestions():
        return {
            'suggestions': [
                {
                    'placePrediction': {
                        'placeId': 'ChIJ8zVuZV8tPUcRI_KDe8IR1Vc',
                        'structuredFormat': {
                            'mainText': {'text': 'Test Business'},
                            'secondaryText': {'text': 'Secondary Text'},
                        },
                    }
                },
                {
                    'placePrediction': {
                        'placeId': 'ChIJ8zVuZV8tPUcRI_KXXXXX',
                        'structuredFormat': {
                            'mainText': {'text': 'Test Business2'},
                            'secondaryText': {'text': 'Secondary Text2'},
                        },
                    }
                },
            ]
        }

    @staticmethod
    def get_business_autocomplete_single_suggestion():
        return {
            'suggestions': [
                {
                    'placePrediction': {
                        'placeId': 'ChIJ8zVuZV8tPUcRI_KDe8IR1Vc',
                        'structuredFormat': {
                            'mainText': {'text': 'Test Business'},
                            'secondaryText': {'text': 'Secondary Text'},
                        },
                    }
                },
            ]
        }


class FakeGooglePlacesClient(GooglePlacesAbstractClient):
    """Simple fake client that returns hardcoded responses for testing."""

    def __init__(self):
        self.details_data = FakeGooglePlacesClientRepository.get_details()
        self.repo = FakeGooglePlacesClientRepository()
        self.address_autocomplete_data = self.repo.get_business_autocomplete_single_suggestion()
        self.business_autocomplete_data = self.repo.get_business_autocomplete_single_suggestion()
        self.status_code = 200

    def get_business_autocomplete(
        self, search_input: str, location_bias: Location | None, session_id: str | None = None
    ) -> ApiResponse:
        """Return simple business autocomplete response."""
        return ApiResponse(status_code=self.status_code, data=self.business_autocomplete_data)

    def get_address_autocomplete(
        self, search_input: str, location_bias: Location | None, session_id: str | None = None
    ) -> ApiResponse:
        """Return simple address autocomplete response."""
        return ApiResponse(status_code=self.status_code, data=self.address_autocomplete_data)

    def get_autocomplete(
        self, search_input: str, location_bias: Location | None, session_id: str | None = None
    ) -> ApiResponse:
        """Return simple address/business autocomplete response."""
        return ApiResponse(status_code=self.status_code, data=self.address_autocomplete_data)

    def get_details(self, place_id: str, session_id: str | None = None) -> ApiResponse:
        """Return simple place details response."""
        return ApiResponse(status_code=self.status_code, data=self.details_data)
