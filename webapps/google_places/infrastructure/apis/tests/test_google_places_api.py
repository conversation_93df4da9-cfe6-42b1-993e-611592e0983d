# pylint: disable=protected-access
import json
import os
from unittest import TestCase
from unittest.mock import patch, call

from webapps.google_places.infrastructure.apis.google_places_api import GooglePlacesClient
from webapps.google_places.application.dtos.place_details import Location
from webapps.google_places.application.interfaces.google_places_client import ApiResponse


class GooglePlacesClientTest(TestCase):
    PLACE_ID = "ChIJEfoXPH01GUcRpFGQ0u5rU10"

    def setUp(self):
        self.client = GooglePlacesClient()
        self.valid_location = Location(latitude=12.12312, longitude=123.41231)
        self.expected_types = ['skin_care_clinic', 'health', 'hair_salon', 'hair_care']
        self.places_api_autocomplete_response_single = {
            'suggestions': [
                {
                    'placePrediction': {
                        'placeId': 'ChIJ8zVuZV8tPUcRI_KDe8IR1Vc',
                        'structuredFormat': {
                            'mainText': {'text': 'Test Business'},
                            'secondaryText': {'text': 'Secondary Text'},
                        },
                    }
                },
            ]
        }
        self.places_api_autocomplete_response_multiple = {
            'suggestions': [
                {
                    'placePrediction': {
                        'placeId': 'ChIJ8zVuZV8tPUcRI_KDe8IR1Vc',
                        'structuredFormat': {
                            'mainText': {'text': 'Test Business'},
                            'secondaryText': {'text': 'Secondary Text'},
                        },
                    }
                },
                {
                    'placePrediction': {
                        'placeId': 'ChIJ8zVuZV8tPUcRI_KXXXXX',
                        'structuredFormat': {
                            'mainText': {'text': 'Test Business2'},
                            'secondaryText': {'text': 'Secondary Text2'},
                        },
                    }
                },
            ]
        }

    @staticmethod
    def _load_mock_response_data():
        """Load mock response data from JSON file."""
        current_dir = os.path.dirname(__file__)
        file_path = os.path.join(current_dir, '_data', 'place_details_api_reponse_example.json')
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)

    def test__build_location_bias(self):
        expected_value = {
            "circle": {
                "center": {
                    "latitude": self.valid_location.latitude,
                    "longitude": self.valid_location.longitude,
                },
                "radius": 50000,
            }
        }

        location_bias = self.client._build_location_bias(self.valid_location)
        self.assertEqual(expected_value, location_bias)

    @patch(
        'webapps.google_places.infrastructure.apis.google_places_api.settings.API_COUNTRY', new='us'
    )
    def test__build_autocomplete_request_business_with_location(self):
        expected_value = {
            "input": "Some_Input",
            "locationBias": {
                "circle": {
                    "center": {
                        "latitude": self.valid_location.latitude,
                        "longitude": self.valid_location.longitude,
                    },
                    "radius": 50000,
                }
            },
            "includedRegionCodes": ['us'],
            "includedPrimaryTypes": ["establishment"],
        }

        result = self.client._build_autocomplete_request(
            search_input="Some_Input",
            location_bias=self.valid_location,
            included_primary_types=['establishment'],
        )
        self.assertEqual(expected_value, result)

    def test__build_autocomplete_request_business_without_location_uses_api_cc(self):
        expected_value = {
            "input": "Some_Input",
            'includedRegionCodes': ["us"],
            "includedPrimaryTypes": ["establishment"],
        }

        result = self.client._build_autocomplete_request(
            search_input="Some_Input", location_bias=None, included_primary_types=['establishment']
        )
        self.assertEqual(expected_value, result)

    @patch(
        'webapps.google_places.infrastructure.apis.google_places_api.settings.API_COUNTRY', new='us'
    )
    def test__build_autocomplete_request_business_without_included_primary_types_uses_api_cc(self):
        expected_value = {
            'input': 'Some_Input',
            'locationBias': {
                'circle': {
                    'center': {'latitude': 12.12312, 'longitude': 123.41231},
                    'radius': 50000,
                }
            },
            "includedRegionCodes": ['us'],
        }

        result = self.client._build_autocomplete_request(
            search_input="Some_Input", location_bias=self.valid_location
        )
        self.assertEqual(expected_value, result)

    @patch('v2.shared.http.http_client.HttpClient.post')
    def test_get_business_autocomplete_without_location_input(self, mock_post):
        """Test business autocomplete API input values for a call without location bias."""
        mock_post.return_value = {
            'status_code': 200,
            'data': self.places_api_autocomplete_response_single,
        }

        response = self.client.get_business_autocomplete("SomeBusiness", location_bias=None)

        self.assertEqual(200, response.status_code)
        self.assertEqual(self.places_api_autocomplete_response_single, response.data)

        mock_post.assert_called_once()
        called_args, called_kwargs = mock_post.call_args
        self.assertEqual("https://places.googleapis.com/v1/places:autocomplete", called_args[0])
        self.assertIn('headers', called_kwargs)
        self.assertIn('data', called_kwargs)
        self.assertEqual(self.expected_types, called_kwargs['data']['includedPrimaryTypes'])
        self.assertEqual(["us"], called_kwargs['data']['includedRegionCodes'])
        self.assertEqual("SomeBusiness", called_kwargs['data']['input'])
        self.assertNotIn('locationBias', called_kwargs['data'])
        self.assertEqual(
            ApiResponse(status_code=200, data=self.places_api_autocomplete_response_single),
            response,
        )

    @patch('v2.shared.http.http_client.HttpClient.post')
    def test_get_business_autocomplete_headers(self, mock_post):
        expected_headers = {
            'Content-Type': 'application/json',
            'X-Goog-Api-Key': '',
            'X-Goog-FieldMask': 'suggestions.placePrediction.placeId,'
            'suggestions.placePrediction.structuredFormat.'
            'mainText.text,suggestions.placePrediction.'
            'structuredFormat.secondaryText.text',
        }
        self.client.get_business_autocomplete("SomeBusiness", location_bias=None)
        mock_post.assert_called_once()
        _, called_kwargs = mock_post.call_args
        self.assertEqual(expected_headers, called_kwargs['headers'])

    @patch('v2.shared.http.http_client.HttpClient.post')
    def test_get_business_autocomplete_with_location_input(self, mock_post):
        """Test business autocomplete API input values for a call with location bias."""
        mock_post.return_value = {
            'status_code': 200,
            'data': self.places_api_autocomplete_response_single,
        }
        response = self.client.get_business_autocomplete(
            "SomeBusiness", location_bias=self.valid_location
        )

        self.assertIsInstance(response, ApiResponse)
        self.assertEqual(200, response.status_code)
        mock_post.assert_called_once()
        _, called_kwargs = mock_post.call_args
        self.assertIn('includedRegionCodes', called_kwargs['data'])
        self.assertEqual("SomeBusiness", called_kwargs['data']['input'])
        self.assertEqual(self.expected_types, called_kwargs['data']['includedPrimaryTypes'])
        self.assertIn('locationBias', called_kwargs['data'])

        self.assertEqual(
            self.valid_location.latitude,
            called_kwargs['data']['locationBias']['circle']['center']['latitude'],
        )
        self.assertEqual(
            self.valid_location.longitude,
            called_kwargs['data']['locationBias']['circle']['center']['longitude'],
        )

    @patch('v2.shared.http.http_client.HttpClient.post')
    def test_get_business_autocomplete_with_location_output_single_suggestion(self, mock_post):
        mock_post.return_value = {
            'status_code': 200,
            'data': self.places_api_autocomplete_response_single,
        }
        response = self.client.get_business_autocomplete(
            "SomeBusiness", location_bias=self.valid_location
        )
        mock_post.assert_called_once()
        self.assertIsInstance(response, ApiResponse)
        self.assertEqual(200, response.status_code)
        self.assertEqual(
            {
                'suggestions': [
                    {
                        'placePrediction': {
                            'placeId': 'ChIJ8zVuZV8tPUcRI_KDe8IR1Vc',
                            'structuredFormat': {
                                'mainText': {'text': 'Test Business'},
                                'secondaryText': {'text': 'Secondary Text'},
                            },
                        }
                    }
                ]
            },
            response.data,
        )

    @patch('v2.shared.http.http_client.HttpClient.post')
    def test_get_business_autocomplete_without_location_output_single_suggestion(self, mock_post):
        mock_post.return_value = {
            'status_code': 200,
            'data': self.places_api_autocomplete_response_single,
        }
        response = self.client.get_business_autocomplete("SomeBusiness", location_bias=None)
        mock_post.assert_called_once()
        self.assertIsInstance(response, ApiResponse)
        self.assertEqual(200, response.status_code)
        self.assertEqual(
            {
                'suggestions': [
                    {
                        'placePrediction': {
                            'placeId': 'ChIJ8zVuZV8tPUcRI_KDe8IR1Vc',
                            'structuredFormat': {
                                'mainText': {'text': 'Test Business'},
                                'secondaryText': {'text': 'Secondary Text'},
                            },
                        }
                    }
                ]
            },
            response.data,
        )

    @patch('v2.shared.http.http_client.HttpClient.post')
    def test_get_business_autocomplete_with_location_output_multiple_suggestions(self, mock_post):
        mock_post.return_value = {
            'status_code': 200,
            'data': self.places_api_autocomplete_response_multiple,
        }
        response = self.client.get_business_autocomplete(
            "SomeBusiness", location_bias=self.valid_location
        )
        mock_post.assert_called_once()
        self.assertIsInstance(response, ApiResponse)
        self.assertEqual(200, response.status_code)
        self.assertEqual(
            {
                'suggestions': [
                    {
                        'placePrediction': {
                            'placeId': 'ChIJ8zVuZV8tPUcRI_KDe8IR1Vc',
                            'structuredFormat': {
                                'mainText': {'text': 'Test Business'},
                                'secondaryText': {'text': 'Secondary Text'},
                            },
                        }
                    },
                    {
                        'placePrediction': {
                            'placeId': 'ChIJ8zVuZV8tPUcRI_KXXXXX',
                            'structuredFormat': {
                                'mainText': {'text': 'Test Business2'},
                                'secondaryText': {'text': 'Secondary Text2'},
                            },
                        }
                    },
                ]
            },
            response.data,
        )

    @patch('v2.shared.http.http_client.HttpClient.post')
    def test_get_business_autocomplete_without_location_output_multiple_suggestions(
        self, mock_post
    ):
        mock_post.return_value = {
            'status_code': 200,
            'data': self.places_api_autocomplete_response_multiple,
        }
        response = self.client.get_business_autocomplete("SomeBusiness", location_bias=None)
        mock_post.assert_called_once()
        self.assertIsInstance(response, ApiResponse)
        self.assertEqual(200, response.status_code)
        self.assertEqual(
            {
                'suggestions': [
                    {
                        'placePrediction': {
                            'placeId': 'ChIJ8zVuZV8tPUcRI_KDe8IR1Vc',
                            'structuredFormat': {
                                'mainText': {'text': 'Test Business'},
                                'secondaryText': {'text': 'Secondary Text'},
                            },
                        }
                    },
                    {
                        'placePrediction': {
                            'placeId': 'ChIJ8zVuZV8tPUcRI_KXXXXX',
                            'structuredFormat': {
                                'mainText': {'text': 'Test Business2'},
                                'secondaryText': {'text': 'Secondary Text2'},
                            },
                        }
                    },
                ]
            },
            response.data,
        )

    @patch('v2.shared.http.http_client.HttpClient.get')
    def test_get_details_success(self, mock_get):
        """Test get_details API call with successful response."""
        mock_response_data = self._load_mock_response_data()
        mock_get.return_value = {'status_code': 200, 'data': mock_response_data}

        place_id = "ChIJEfoXPH01GUcRpFGQ0u5rU10"
        response = self.client.get_details(place_id)

        self.assertIsInstance(response, ApiResponse)
        self.assertEqual(200, response.status_code)
        self.assertEqual(mock_response_data, response.data)

        mock_get.assert_called_once()
        called_args, called_kwargs = mock_get.call_args
        expected_url = f"https://places.googleapis.com/v1/places/{place_id}"
        self.assertEqual(expected_url, called_args[0])
        self.assertIn('headers', called_kwargs)
        self.assertIn('params', called_kwargs)
        self.assertIn('fields', called_kwargs['params'])
        self.assertEqual(
            'id,displayName.text,formattedAddress,location,addressComponents,postalAddress',
            called_kwargs['params']['fields'],
        )
        self.assertIn('X-Goog-Api-Key', called_kwargs['headers'])
        self.assertIn('Content-Type', called_kwargs['headers'])

    @patch('v2.shared.http.http_client.HttpClient.get')
    def test_get_details_error_response(self, mock_get):
        """Test get_details API call with error response."""
        mock_response_data = {"error": {"code": 400, "message": "Invalid place ID"}}
        mock_get.return_value = {'status_code': 400, 'data': mock_response_data}

        place_id = "invalid_place_id"
        response = self.client.get_details(place_id)

        self.assertIsInstance(response, ApiResponse)
        self.assertEqual(400, response.status_code)
        self.assertEqual(mock_response_data, response.data)

        mock_get.assert_called_once()
        called_args, called_kwargs = mock_get.call_args
        expected_url = f"https://places.googleapis.com/v1/places/{place_id}"
        self.assertEqual(expected_url, called_args[0])
        self.assertEqual(
            'id,displayName.text,formattedAddress,location,addressComponents,postalAddress',
            called_kwargs['params']['fields'],
        )

    @patch('v2.shared.http.http_client.HttpClient.post')
    def test_get_business_autocomplete_empty_response(self, mock_post):
        """Test business autocomplete API call when Google API returns empty dictionary."""
        mock_post.return_value = {
            'status_code': 200,
            'data': {},
        }

        response = self.client.get_business_autocomplete("SomeBusiness", location_bias=None)

        self.assertIsInstance(response, ApiResponse)
        self.assertEqual(200, response.status_code)
        self.assertEqual({}, response.data)

        mock_post.assert_called_once()
        called_args, _ = mock_post.call_args
        self.assertEqual("https://places.googleapis.com/v1/places:autocomplete", called_args[0])

    @patch('v2.shared.http.http_client.HttpClient.post')
    def test_get_business_autocomplete_error_response(self, mock_post):
        """Test business autocomplete API call with error response."""
        mock_error_response = {
            "error": {"code": 400, "message": "Invalid request", "status": "INVALID_REQUEST"}
        }
        mock_post.return_value = {
            'status_code': 400,
            'data': mock_error_response,
        }

        response = self.client.get_business_autocomplete("", location_bias=None)

        self.assertIsInstance(response, ApiResponse)
        self.assertEqual(400, response.status_code)
        self.assertEqual(mock_error_response, response.data)

        mock_post.assert_called_once()

    @patch('v2.shared.http.http_client.HttpClient.post')
    def test_get_address_autocomplete_empty_response(self, mock_post):
        """Test address autocomplete API call when Google API returns empty dictionary."""
        mock_post.return_value = {
            'status_code': 200,
            'data': {},  # Empty response from Google API
        }

        response = self.client.get_address_autocomplete("Some Address", location_bias=None)

        self.assertIsInstance(response, ApiResponse)
        self.assertEqual(200, response.status_code)
        self.assertEqual({}, response.data)

        mock_post.assert_called_once()
        called_args, called_kwargs = mock_post.call_args
        self.assertEqual("https://places.googleapis.com/v1/places:autocomplete", called_args[0])
        self.assertEqual(["premise", "route"], called_kwargs['data']['includedPrimaryTypes'])

    @patch('v2.shared.http.http_client.HttpClient.post')
    def test_get_address_autocomplete_error_response(self, mock_post):
        """Test address autocomplete API call with error response."""
        mock_error_response = {
            "error": {"code": 403, "message": "API key not valid", "status": "PERMISSION_DENIED"}
        }
        mock_post.return_value = {
            'status_code': 403,
            'data': mock_error_response,
        }

        response = self.client.get_address_autocomplete("Some Address", location_bias=None)

        self.assertIsInstance(response, ApiResponse)
        self.assertEqual(403, response.status_code)
        self.assertEqual(mock_error_response, response.data)

        mock_post.assert_called_once()

    def test_session_token_included_in_request_when_session_id_provided(self):
        """Test that session token is included in request data when session_id is provided."""
        session_id = "test_session_123"
        expected_token = "mock-session-token-456"
        with patch.object(
            self.client._token_cache, 'get_or_create_token', return_value=expected_token
        ):
            request_data = self.client._build_autocomplete_request(
                search_input="Test Business",
                location_bias=None,
                included_primary_types=["health"],
                session_id=session_id,
            )

        self.assertIn('sessionToken', request_data)
        self.assertEqual(expected_token, request_data['sessionToken'])

    def test_session_token_not_included_when_session_id_is_none(self):
        """Test that session token is not included when session_id is None."""
        request_data = self.client._build_autocomplete_request(
            search_input="Test Business",
            location_bias=None,
            included_primary_types=["health"],
            session_id=None,
        )

        self.assertNotIn('sessionToken', request_data)

    def test_session_token_not_included_when_session_id_is_empty(self):
        """Test that session token is not included when session_id is empty string."""
        request_data = self.client._build_autocomplete_request(
            search_input="Test Business",
            location_bias=None,
            included_primary_types=["health"],
            session_id="",
        )

        self.assertNotIn('sessionToken', request_data)

    @patch('v2.shared.http.http_client.HttpClient.post')
    def test_get_business_autocomplete_with_session_id(self, mock_post):
        """Test business autocomplete with session_id includes session token."""
        session_id = "business_session_789"
        expected_token = "business-session-token-123"

        mock_post.return_value = {
            'status_code': 200,
            'data': self.places_api_autocomplete_response_single,
        }

        with patch.object(
            self.client._token_cache, 'get_or_create_token', return_value=expected_token
        ) as mock_cache:
            response = self.client.get_business_autocomplete(
                "Test Business", location_bias=None, session_id=session_id
            )

        mock_cache.assert_called_once_with(session_id)

        mock_post.assert_called_once()
        _, called_kwargs = mock_post.call_args
        self.assertIn('data', called_kwargs)
        self.assertIn('sessionToken', called_kwargs['data'])
        self.assertEqual(expected_token, called_kwargs['data']['sessionToken'])
        self.assertEqual(
            ApiResponse(status_code=200, data=self.places_api_autocomplete_response_single),
            response,
        )

    @patch('v2.shared.http.http_client.HttpClient.post')
    def test_get_address_autocomplete_with_session_id(self, mock_post):
        """Test address autocomplete with session_id includes session token."""
        session_id = "address_session_456"
        expected_token = "address-session-token-789"

        mock_post.return_value = {
            'status_code': 200,
            'data': self.places_api_autocomplete_response_single,
        }

        with patch.object(
            self.client._token_cache, 'get_or_create_token', return_value=expected_token
        ) as mock_cache:
            response = self.client.get_address_autocomplete(
                "123 Main St", location_bias=self.valid_location, session_id=session_id
            )

        mock_cache.assert_called_once_with(session_id)

        # Verify request includes session token
        mock_post.assert_called_once()
        _, called_kwargs = mock_post.call_args
        self.assertIn('data', called_kwargs)
        self.assertIn('sessionToken', called_kwargs['data'])
        self.assertEqual(expected_token, called_kwargs['data']['sessionToken'])
        self.assertEqual(
            ApiResponse(status_code=200, data=self.places_api_autocomplete_response_single),
            response,
        )

    @patch('v2.shared.http.http_client.HttpClient.post')
    def test_same_session_id_reuses_cached_token(self, mock_post):
        """Test that same session_id reuses cached token across multiple requests."""
        session_id = "persistent_session"
        expected_token = "persistent-token-123"

        mock_post.return_value = {
            'status_code': 200,
            'data': self.places_api_autocomplete_response_single,
        }

        with patch.object(
            self.client._token_cache, 'get_or_create_token', return_value=expected_token
        ) as mock_cache:
            self.client.get_business_autocomplete(
                "First Search", location_bias=None, session_id=session_id
            )

            self.client.get_address_autocomplete(
                "Second Search", location_bias=None, session_id=session_id
            )

        self.assertEqual(2, mock_cache.call_count)
        mock_cache.assert_has_calls(
            [
                call(session_id),
                call(session_id),
            ]
        )
        self.assertEqual(expected_token, mock_post.call_args_list[0][1]['data']['sessionToken'])
        self.assertEqual(expected_token, mock_post.call_args_list[1][1]['data']['sessionToken'])

    @patch('v2.shared.http.http_client.HttpClient.post')
    def test_different_session_ids_get_different_tokens(self, mock_post):
        """Test that different session_ids get different tokens."""
        session_id_1 = "session_one"
        session_id_2 = "session_two"
        token_1 = "token-for-session-one"
        token_2 = "token-for-session-two"

        mock_post.return_value = {
            'status_code': 200,
            'data': self.places_api_autocomplete_response_single,
        }

        def mock_get_or_create(session_id):
            if session_id == session_id_1:
                return token_1
            if session_id == session_id_2:
                return token_2
            return "default-token"

        with patch.object(
            self.client._token_cache, 'get_or_create_token', side_effect=mock_get_or_create
        ):
            self.client.get_business_autocomplete(
                "Search 1", location_bias=None, session_id=session_id_1
            )
            self.client.get_business_autocomplete(
                "Search 2", location_bias=None, session_id=session_id_2
            )

        calls = mock_post.call_args_list
        self.assertEqual(2, len(calls))

        first_call_data = calls[0][1]['data']
        second_call_data = calls[1][1]['data']

        self.assertEqual(token_1, first_call_data['sessionToken'])
        self.assertEqual(token_2, second_call_data['sessionToken'])
        self.assertNotEqual(first_call_data['sessionToken'], second_call_data['sessionToken'])

    def test_session_token_cache_initialization(self):
        """Test that SessionTokenCache is properly initialized."""
        from webapps.google_places.infrastructure.cache import SessionTokenCache

        self.assertIsInstance(self.client._token_cache, SessionTokenCache)

    @patch('v2.shared.http.http_client.HttpClient.post')
    def test_session_token_with_location_bias(self, mock_post):
        """Test that session token works correctly with location bias."""
        session_id = "location_session"
        expected_token = "location-token-456"
        mock_post.return_value = {
            'status_code': 200,
            'data': self.places_api_autocomplete_response_single,
        }

        with patch.object(
            self.client._token_cache, 'get_or_create_token', return_value=expected_token
        ):
            response = self.client.get_business_autocomplete(
                "Restaurant", location_bias=self.valid_location, session_id=session_id
            )

        mock_post.assert_called_once()
        _, called_kwargs = mock_post.call_args
        request_data = called_kwargs['data']

        self.assertIn('sessionToken', request_data)
        self.assertIn('locationBias', request_data)
        self.assertEqual(expected_token, request_data['sessionToken'])
        self.assertIn('circle', request_data['locationBias'])
        self.assertEqual(
            ApiResponse(status_code=200, data=self.places_api_autocomplete_response_single),
            response,
        )

    @patch('v2.shared.http.http_client.HttpClient.get')
    def test_get_details_with_session_id(self, mock_get):
        """Test get_details with session_id includes session token in URL."""
        session_id = "details_session_123"
        expected_token = "details-session-token-456"
        mock_response_data = self._load_mock_response_data()

        mock_get.return_value = {'status_code': 200, 'data': mock_response_data}

        with patch.object(
            self.client._token_cache, 'get_or_create_token', return_value=expected_token
        ) as mock_cache:
            response = self.client.get_details(self.PLACE_ID, session_id=session_id)

        mock_cache.assert_called_once_with(session_id)

        mock_get.assert_called_once()
        called_args, called_kwargs = mock_get.call_args
        expected_url = f"https://places.googleapis.com/v1/places/{self.PLACE_ID}"
        self.assertEqual(expected_url, called_args[0])
        self.assertIn(expected_token, called_kwargs['params']['sessionToken'])
        self.assertEqual(
            'id,displayName.text,formattedAddress,location,addressComponents,postalAddress',
            called_kwargs['params']['fields'],
        )
        self.assertEqual(
            ApiResponse(status_code=200, data=mock_response_data),
            response,
        )

    @patch('v2.shared.http.http_client.HttpClient.get')
    def test_get_details_without_session_id(self, mock_get):
        """Test get_details without session_id does not include session token."""
        mock_response_data = self._load_mock_response_data()

        mock_get.return_value = {'status_code': 200, 'data': mock_response_data}

        response = self.client.get_details(self.PLACE_ID, session_id=None)

        mock_get.assert_called_once()
        called_args, called_kwargs = mock_get.call_args
        expected_url = f"https://places.googleapis.com/v1/places/{self.PLACE_ID}"
        self.assertEqual(expected_url, called_args[0])
        self.assertNotIn('sessionToken', called_args[0])
        self.assertNotIn('sessionToken', called_kwargs['params'])
        self.assertEqual(
            'id,displayName.text,formattedAddress,location,addressComponents,postalAddress',
            called_kwargs['params']['fields'],
        )
        self.assertEqual(
            ApiResponse(status_code=200, data=mock_response_data),
            response,
        )

    @patch('v2.shared.http.http_client.HttpClient.get')
    def test_get_details_same_session_id_reuses_cached_token(self, mock_get):
        """Test that same session_id reuses cached token for get_details."""
        session_id = "persistent_details_session"
        expected_token = "persistent-details-token-123"
        mock_response_data = self._load_mock_response_data()

        mock_get.return_value = {'status_code': 200, 'data': mock_response_data}

        with patch.object(
            self.client._token_cache, 'get_or_create_token', return_value=expected_token
        ) as mock_cache:
            self.client.get_details(self.PLACE_ID, session_id=session_id)
            self.client.get_details(self.PLACE_ID, session_id=session_id)

        self.assertEqual(2, mock_cache.call_count)
        mock_cache.assert_has_calls(
            [
                call(session_id),
                call(session_id),
            ]
        )

    @patch('v2.shared.http.http_client.HttpClient.get')
    def test_get_details_different_session_ids_get_different_tokens(self, mock_get):
        """Test that different session_ids get different tokens for get_details."""
        session_id_1 = "details_session_one"
        session_id_2 = "details_session_two"
        token_1 = "details-token-for-session-one"
        token_2 = "details-token-for-session-two"
        mock_response_data = self._load_mock_response_data()

        mock_get.return_value = {'status_code': 200, 'data': mock_response_data}

        def mock_get_or_create(session_id):
            if session_id == session_id_1:
                return token_1
            if session_id == session_id_2:
                return token_2
            return "default-token"

        with patch.object(
            self.client._token_cache, 'get_or_create_token', side_effect=mock_get_or_create
        ):
            self.client.get_details(self.PLACE_ID, session_id=session_id_1)
            self.client.get_details(self.PLACE_ID, session_id=session_id_2)

        calls = mock_get.call_args_list
        self.assertEqual(mock_get.call_count, 2)

        first_call_token = calls[0].kwargs['params']['sessionToken']
        second_call_token = calls[1].kwargs['params']['sessionToken']

        self.assertEqual(token_1, first_call_token)
        self.assertEqual(token_2, second_call_token)
        self.assertNotEqual(second_call_token, first_call_token)
