from typing import Any

from django.conf import settings

from v2.shared.http.http_client import HttpClient
from webapps.google_places.application.interfaces.google_places_client import (
    GooglePlacesAbstractClient,
    ApiResponse,
)
from webapps.google_places.domain.dtos.shared import Location
from webapps.google_places.infrastructure.cache import SessionTokenCache

RADIUS = 50000  # meters
ADDRESS_AUTOCOMPLETE_TYPES = ["premise", "route"]
BUSINESS_AUTOCOMPLETE_TYPES = ['skin_care_clinic', 'health', 'hair_salon', 'hair_care']
AUTOCOMPLETE_FIELDS = [
    'suggestions.placePrediction.placeId',
    'suggestions.placePrediction.structuredFormat.mainText.text',
    'suggestions.placePrediction.structuredFormat.secondaryText.text',
]
DETAILS_FIELDS = [
    'id',
    'displayName.text',
    'formattedAddress',
    'location',
    'addressComponents',
    'postalAddress',
]


class GooglePlacesClient(GooglePlacesAbstractClient):
    AUTOCOMPLETE_URL = "https://places.googleapis.com/v1/places:autocomplete"
    DETAILS_URL = "https://places.googleapis.com/v1/places/{place_id}"

    def __init__(self, http_client: HttpClient = None):
        self._http_client = http_client or HttpClient()
        self._token_cache = SessionTokenCache()

    @staticmethod
    def _build_location_bias(location: Location) -> dict[str, dict]:
        return {
            "circle": {
                "center": {"latitude": location.latitude, "longitude": location.longitude},
                "radius": RADIUS,
            }
        }

    def _build_autocomplete_request(
        self,
        search_input: str,
        location_bias: Location | None,
        included_primary_types: list[str] | None = None,
        session_id: str | None = None,
    ) -> dict[str, Any]:
        request = {"input": search_input, "includedRegionCodes": [settings.API_COUNTRY]}
        if included_primary_types:
            request["includedPrimaryTypes"] = included_primary_types
        if location_bias:
            request["locationBias"] = self._build_location_bias(location_bias)

        if session_id:
            request["sessionToken"] = self._token_cache.get_or_create_token(session_id)

        return request

    @staticmethod
    def _get_header() -> dict[str, str]:
        return {
            "Content-Type": "application/json",
            "X-Goog-Api-Key": settings.GOOGLE_PLACES_API_KEY_V2,
        }

    @staticmethod
    def _append_autocomplete_output_fields(header: dict[str, str]) -> dict[str, str]:
        header.update({"X-Goog-FieldMask": ",".join(AUTOCOMPLETE_FIELDS)})
        return header

    def get_address_autocomplete(
        self, search_input: str, location_bias: Location | None, session_id: str | None = None
    ) -> ApiResponse:
        request_data = self._build_autocomplete_request(
            search_input=search_input,
            location_bias=location_bias,
            included_primary_types=ADDRESS_AUTOCOMPLETE_TYPES,
            session_id=session_id,
        )
        headers = self._get_header()
        headers = self._append_autocomplete_output_fields(headers)
        response = self._http_client.post(self.AUTOCOMPLETE_URL, headers=headers, data=request_data)
        return ApiResponse(status_code=response['status_code'], data=response['data'])

    def get_business_autocomplete(
        self, search_input: str, location_bias: Location | None, session_id: str | None = None
    ) -> ApiResponse:
        request_data = self._build_autocomplete_request(
            search_input=search_input,
            location_bias=location_bias,
            included_primary_types=BUSINESS_AUTOCOMPLETE_TYPES,
            session_id=session_id,
        )
        headers = self._get_header()
        headers = self._append_autocomplete_output_fields(headers)
        response = self._http_client.post(self.AUTOCOMPLETE_URL, headers=headers, data=request_data)
        return ApiResponse(status_code=response['status_code'], data=response['data'])

    def get_autocomplete(
        self, search_input: str, location_bias: Location | None, session_id: str | None = None
    ) -> ApiResponse:
        request_data = self._build_autocomplete_request(
            search_input=search_input,
            location_bias=location_bias,
            session_id=session_id,
        )
        headers = self._get_header()
        headers = self._append_autocomplete_output_fields(headers)
        response = self._http_client.post(self.AUTOCOMPLETE_URL, headers=headers, data=request_data)
        return ApiResponse(status_code=response['status_code'], data=response['data'])

    def get_details(self, place_id: str, session_id: str | None = None) -> ApiResponse:
        payload = {"fields": ",".join(DETAILS_FIELDS)}
        if session_id:
            payload["sessionToken"] = self._token_cache.get_or_create_token(session_id)
        url = f"{self.DETAILS_URL.format(place_id=place_id)}"
        response = self._http_client.get(url, headers=self._get_header(), params=payload)
        return ApiResponse(status_code=response['status_code'], data=response['data'])
