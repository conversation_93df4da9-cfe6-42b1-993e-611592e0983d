from service.search.v2.domain.ports import VenueFinderPort
from service.search.v2.infrastructure.dtos import (
    VenueSearchParams,
    VenueSearchResult,
)
from webapps.google_places.domain.ports.venue_port import VenuePort
from webapps.google_places.domain.dtos.shared import AddressComponents
from webapps.google_places.domain.dtos.venue_details import (
    VenueDetails,
    Location,
    Coordinate,
    VenueMeta,
    VenueLocation,
    VenueLastUpdated,
)


class VenueAdapter(VenuePort):
    """Adapter that uses search context's venue search functionality."""

    def __init__(self, venue_search_service: VenueFinderPort):
        self.venue_search_service = venue_search_service

    @staticmethod
    def _convert_es_venue_hit_to_dto(es_venue_hit: VenueSearchResult) -> VenueDetails | None:
        return VenueDetails(
            id=es_venue_hit.id,
            name=es_venue_hit.name,
            cover_photo=es_venue_hit.cover_photo or "",
            thumbnail_photo=es_venue_hit.thumbnail_photo or "",
            location=Location(
                coordinate=Coordinate(
                    latitude=es_venue_hit.latitude, longitude=es_venue_hit.longitude
                ),
                address=es_venue_hit.address,
                city=es_venue_hit.city,
            ),
            meta=VenueMeta(
                score=es_venue_hit.meta_score or 0.0,
                id=es_venue_hit.meta_id or "",
                item_no=es_venue_hit.meta_item_no or 0,
            ),
            venue_location=VenueLocation(
                zipcode=es_venue_hit.zipcode or "",
                coordinate=Coordinate(
                    latitude=es_venue_hit.latitude, longitude=es_venue_hit.longitude
                ),
                address=es_venue_hit.address,
                address2=es_venue_hit.address2 or "",
                city=es_venue_hit.city,
            ),
            last_updated=VenueLastUpdated(
                contractor_id=es_venue_hit.last_updated_contractor_id or 0,
                contractor_name=es_venue_hit.last_updated_contractor_name or "",
                updated=es_venue_hit.last_updated_updated or "",
            ),
        )

    def get_venue_details(self, address_components: AddressComponents) -> VenueDetails | None:
        """Use search context's service to find venue details."""

        search_params = VenueSearchParams(
            street=f"{address_components.street} {address_components.street_number}",
            city=address_components.city,
            zipcode=address_components.zipcode,
        )
        if venue_details := self.venue_search_service.execute(search_params):
            return self._convert_es_venue_hit_to_dto(venue_details)
