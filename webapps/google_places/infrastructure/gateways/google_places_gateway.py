from webapps.google_places.application.interfaces.google_places_client import (
    ApiResponse,
    GooglePlacesAbstractClient,
)
from webapps.google_places.application.dtos.autocomplete import (
    AutocompleteSuggestions,
    Suggestion,
)
from webapps.google_places.domain.dtos.shared import Location, AddressComponents
from webapps.google_places.application.interfaces.google_places_gateway import GooglePlacesGateway
from webapps.google_places.application.dtos.google_place_details import GooglePlaceDetails


class GooglePlacesAPIGateway(GooglePlacesGateway):
    def __init__(self, google_places_api: GooglePlacesAbstractClient):
        self._api = google_places_api

    def _autocomplete_response_to_dto(self, response: ApiResponse) -> AutocompleteSuggestions:
        parsed_response = self._parse_autocomplete_response(response)
        return AutocompleteSuggestions(
            suggestions=[
                Suggestion(
                    place_id=suggestion.get('place_id'),
                    main_text=suggestion.get('main_text'),
                    secondary_text=suggestion.get('secondary_text'),
                )
                for suggestion in parsed_response
            ]
        )

    @staticmethod
    def _parse_autocomplete_response(response: ApiResponse) -> list[dict[str, str]]:
        """
        Simplify GOOGLE PLACES API response to return only needed fields.
        Args:
            response (ApiResponse): ApiResponse object containing GOOGLE PLACES API response
        Returns:
            [{'placeId': 'some_place_id', 'mainText': 'some_text', 'secondaryText': 'some_text'},]
        """
        parsed_response = []
        for suggestion in response.data.get("suggestions", []):
            place_prediction = suggestion.get("placePrediction", {})
            structured_format = place_prediction.get("structuredFormat", {})
            place_id = place_prediction.get("placeId")
            main_text = structured_format.get("mainText", {}).get("text")
            secondary_text = structured_format.get("secondaryText", {}).get("text")
            parsed_response.append(
                {"place_id": place_id, "main_text": main_text, "secondary_text": secondary_text}
            )
        return parsed_response

    def _parse_details_response(self, response: ApiResponse) -> GooglePlaceDetails:
        """Parse Google Places API details response into GooglePlaceDetails object."""
        data = response.data
        address_components = self._extract_address_components(data.get("addressComponents", []))

        location_data = data.get("location", {})
        location = Location(
            latitude=location_data.get("latitude", 0.0),
            longitude=location_data.get("longitude", 0.0),
        )

        postal_address = data.get("postalAddress") or {}
        address_lines = postal_address.get("addressLines")
        administrative_area = postal_address.get("administrativeArea")

        return GooglePlaceDetails(
            place_id=data.get("id", ""),
            name=data.get("displayName", {}).get("text", ""),
            formatted_address=data.get("formattedAddress", ""),
            location=location,
            address_components=address_components,
            address_lines=address_lines,
            administrative_area=administrative_area,
        )

    @staticmethod
    def _extract_address_components(address_components_data: list) -> AddressComponents:
        """Extract address components from API response."""
        component_map = {
            "street_number": "",
            "route": "",
            "locality": "",
            "country": "",
            "postal_code": "",
            "subpremise": "",
        }

        for component in address_components_data:
            long_text = component.get("longText", "")
            for component_type in component.get("types", []):
                if component_type in component_map:
                    component_map[component_type] = long_text
                    break

        return AddressComponents(
            street_number=component_map["street_number"],
            street=component_map["route"],
            city=component_map["locality"],
            country=component_map["country"],
            zipcode=component_map["postal_code"],
            subpremise=component_map["subpremise"],
        )

    def get_business_autocomplete(
        self, search_input: str, location: Location | None, session_id: str | None = None
    ) -> AutocompleteSuggestions:
        """
        Args:
            search_input (str): Input string from user
            location (Location): value object containing latitude and longitude
            session_id (str): Optional session identifier for token caching

        Returns:

        """
        response = self._api.get_business_autocomplete(
            search_input=search_input, location_bias=location, session_id=session_id
        )
        return self._autocomplete_response_to_dto(response)

    def get_address_autocomplete(
        self, search_input: str, location: Location | None, session_id: str | None = None
    ) -> AutocompleteSuggestions:
        """
        Args:
            search_input (str): Input string from user
            location (Location): value object containing latitude and longitude
            session_id (str): Optional session identifier for token caching

        Returns:

        """
        response = self._api.get_address_autocomplete(
            search_input=search_input,
            location_bias=location,
            session_id=session_id,
        )
        return self._autocomplete_response_to_dto(response)

    def get_autocomplete(
        self, search_input: str, location: Location | None, session_id: str | None = None
    ) -> AutocompleteSuggestions:
        """
        Method that returns both businesses and address suggestions
        Args:
            search_input (str): Input string from user
            location (Location): value object containing latitude and longitude
            session_id (str): Optional session identifier for token caching

        Returns:

        """
        response = self._api.get_address_autocomplete(
            search_input=search_input,
            location_bias=location,
            session_id=session_id,
        )
        return self._autocomplete_response_to_dto(response)

    def get_details(self, place_id: str, session_id: str | None = None) -> GooglePlaceDetails:
        """
        Args:
            place_id: Google Places API place identifier
            session_id: Optional session identifier for token caching

        Returns:
            GooglePlaceDetails object with place information
        """
        response = self._api.get_details(place_id, session_id)
        return self._parse_details_response(response)
