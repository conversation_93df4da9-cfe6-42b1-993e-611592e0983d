import abc

from webapps.google_places.application.dtos.autocomplete import AutocompleteSuggestions

from webapps.google_places.application.dtos.place_details import Location
from webapps.google_places.application.dtos.google_place_details import GooglePlaceDetails


class GooglePlacesGateway(abc.ABC):

    @abc.abstractmethod
    def get_business_autocomplete(
        self, search_input: str, location: Location | None, session_id: str | None = None
    ) -> AutocompleteSuggestions: ...

    @abc.abstractmethod
    def get_address_autocomplete(
        self, search_input: str, location: Location | None, session_id: str | None = None
    ) -> AutocompleteSuggestions: ...

    @abc.abstractmethod
    def get_autocomplete(
        self, search_input: str, location: Location | None, session_id: str | None = None
    ) -> AutocompleteSuggestions: ...

    @abc.abstractmethod
    def get_details(self, place_id: str, session_id: str | None = None) -> GooglePlaceDetails: ...
