import abc
from dataclasses import dataclass
from typing import Any

from webapps.google_places.domain.dtos.shared import Location


@dataclass(frozen=True)
class ApiResponse:
    status_code: int
    data: dict[str, Any]


class GooglePlacesAbstractClient(abc.ABC):

    @abc.abstractmethod
    def get_address_autocomplete(
        self, search_input: str, location_bias: Location | None, session_id: str | None = None
    ) -> ApiResponse: ...

    @abc.abstractmethod
    def get_business_autocomplete(
        self, search_input: str, location_bias: Location | None, session_id: str | None = None
    ) -> ApiResponse: ...

    @abc.abstractmethod
    def get_autocomplete(
        self, search_input: str, location_bias: Location | None, session_id: str | None = None
    ) -> ApiResponse: ...

    @abc.abstractmethod
    def get_details(self, place_id: str, session_id: str | None = None) -> ApiResponse: ...
