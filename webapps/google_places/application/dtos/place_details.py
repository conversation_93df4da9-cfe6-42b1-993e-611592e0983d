from dataclasses import dataclass

from webapps.google_places.domain.dtos.shared import AddressComponents, Location
from webapps.google_places.application.dtos.google_place_details import GooglePlaceDetails
from webapps.google_places.domain.dtos.venue_details import VenueDetails


@dataclass(frozen=True)
class PlaceDetails:
    """
    DEPRECATED: This V1 DTO includes venue field which is being phased out.
    Will be removed after conclusion of Google Address Matchability V1&V2 experiment.
    For new implementations, use PlaceDetailsV2 instead.
    """

    place_id: str
    name: str
    formatted_address: str
    location: Location
    address_components: AddressComponents
    venue: VenueDetails = (
        None  # DEPRECATED: Will be removed after conclusion of Google Address V1&V2 experiment
    )

    @classmethod
    def from_google_place_details(
        cls, google_place_details: GooglePlaceDetails, venue_details: VenueDetails | None = None
    ) -> 'PlaceDetails':
        return cls(
            place_id=google_place_details.place_id,
            name=google_place_details.name,
            formatted_address=google_place_details.formatted_address,
            location=google_place_details.location,
            address_components=google_place_details.address_components,
            venue=venue_details,
        )


@dataclass(frozen=True)
class PlaceDetailsV2:
    """V2 DTO without venue information."""

    place_id: str
    name: str
    formatted_address: str
    location: Location
    address_components: AddressComponents
    address_lines: list[str] | None = None
    administrative_area: str | None = None

    @classmethod
    def from_google_place_details(
        cls, google_place_details: GooglePlaceDetails
    ) -> 'PlaceDetailsV2':
        return cls(
            place_id=google_place_details.place_id,
            name=google_place_details.name,
            formatted_address=google_place_details.formatted_address,
            location=google_place_details.location,
            address_components=google_place_details.address_components,
            address_lines=google_place_details.address_lines,
            administrative_area=google_place_details.administrative_area,
        )
