from dataclasses import dataclass
from enum import Enum
from typing import Optional

from webapps.google_places.domain.dtos.shared import Location


class PossibleNextAction(Enum):
    FIX = "FIX"
    CONFIRM_ADD_SUBPREMISES = "CONFIRM_ADD_SUBPREMISES"
    CONFIRM = "CONFIRM"
    ACCEPT = "ACCEPT"


class PinLocationStatus(Enum):
    OK = "OK"
    WARN = "WARN"
    ERROR = "ERROR"


@dataclass(frozen=True)
class AddressComponents:
    city: str
    country: str
    zipcode: str
    subpremise: Optional[str]


@dataclass(frozen=True)
class AddressValidationRequest:
    address_lines: list[str]
    administrative_area: Optional[str]
    location: Location
    address_components: AddressComponents
    previous_response_id: Optional[str]


@dataclass(frozen=True)
class CorrectedAddress:
    postal_code: str
    administrative_area: Optional[str]
    locality: str
    address_lines: list[str]


@dataclass(frozen=True)
class PinLocation:
    status: PinLocationStatus
    text: str


@dataclass(frozen=True)
class AddressValidationResponse:
    possible_next_action: PossibleNextAction
    formatted_address: str
    address_complete: bool
    has_replaced_components: bool
    missing_components: list[str]
    unconfirmed_components: list[str]
    replaced_components: list[str]
    corrected_address: CorrectedAddress
    pin_location: PinLocation
    response_id: str
