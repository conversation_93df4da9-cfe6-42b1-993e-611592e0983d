from abc import ABC, abstractmethod

from webapps.google_places.application.dtos.place_details import PlaceDetails, PlaceDetailsV2
from webapps.google_places.application.interfaces.google_places_gateway import GooglePlacesGateway
from webapps.google_places.domain.ports.venue_port import VenuePort


class AbstractDetailsService(ABC):
    """
    DEPRECATED: This V1 service includes venue information which is being phased out.
    Will be removed after conclusion of Google Address Matchability V1&V2 experiment.
    For new implementations, use AbstractDetailsServiceV2 instead.
    """

    @abstractmethod
    def get_details(self, place_id: str, session_id: str | None = None) -> PlaceDetails:
        """Get place details by place ID."""


class DetailsService(AbstractDetailsService):
    """
    DEPRECATED: V1 implementation that includes venue fetching.
    This service will be removed after conclusion of Google Address Matchability V1&V2 experiment.
    Use DetailsServiceV2 for new implementations.
    """

    def __init__(self, gateway: GooglePlacesGateway, venue_port: VenuePort):
        self.gateway = gateway
        self.venue_port = venue_port

    def get_details(self, place_id: str, session_id: str | None = None) -> PlaceDetails:
        google_place = self.gateway.get_details(place_id, session_id)
        venue_details = self.venue_port.get_venue_details(google_place.address_components)
        return PlaceDetails.from_google_place_details(
            google_place_details=google_place,
            venue_details=venue_details,
        )


class AbstractDetailsServiceV2(ABC):
    @abstractmethod
    def get_details(self, place_id: str, session_id: str | None = None) -> PlaceDetailsV2:
        """Get place details by place ID without venue information."""


class DetailsServiceV2(AbstractDetailsServiceV2):
    """V2 service that returns place details without venue information."""

    def __init__(self, gateway: GooglePlacesGateway):
        self.gateway = gateway

    def get_details(self, place_id: str, session_id: str | None = None) -> PlaceDetailsV2:
        google_place = self.gateway.get_details(place_id, session_id)
        return PlaceDetailsV2.from_google_place_details(google_place_details=google_place)
