import uuid
from abc import ABC, abstractmethod

from webapps.google_places.application.dtos.address_validation import (
    AddressValidationRequest,
    AddressValidationResponse,
    PossibleNextAction,
    CorrectedAddress,
    PinLocation,
    PinLocationStatus,
)


class AbstractAddressValidationService(ABC):
    @abstractmethod
    def validate_address(
        self, request: AddressValidationRequest, session_id: str | None = None
    ) -> AddressValidationResponse:
        pass


class AddressValidationService(AbstractAddressValidationService):
    def validate_address(
        self, request: AddressValidationRequest, session_id: str | None = None
    ) -> AddressValidationResponse:
        response_id = request.previous_response_id or str(uuid.uuid4())
        address_complete = len(request.address_lines) > 0 and bool(
            request.address_components.get("city")
        )

        missing_components = []
        unconfirmed_components = []
        replaced_components = []

        if not request.address_components.get("zipcode"):
            missing_components.append("postal_code")

        if not request.address_components.get("city"):
            missing_components.append("locality")

        if request.address_components.get("subpremise"):
            unconfirmed_components.append("subpremise")

        corrected_address = CorrectedAddress(
            postal_code=request.address_components.get("zipcode", "09-407"),
            administrative_area=request.administrative_area,
            locality=request.address_components.get("city", "Płock"),
            address_lines=request.address_lines or ["123 Main Street"],
        )
        pin_location = PinLocation(
            status=PinLocationStatus.OK if address_complete else PinLocationStatus.WARN,
            text="Address location confirmed" if address_complete else "Address needs verification",
        )
        if missing_components:
            possible_next_action = PossibleNextAction.FIX
        elif unconfirmed_components:
            possible_next_action = PossibleNextAction.CONFIRM_ADD_SUBPREMISES
        elif not address_complete:
            possible_next_action = PossibleNextAction.CONFIRM
        else:
            possible_next_action = PossibleNextAction.ACCEPT

        return AddressValidationResponse(
            possible_next_action=possible_next_action,
            formatted_address=", ".join(
                request.address_lines
                + [
                    request.address_components.get("city", ""),
                    request.address_components.get("zipcode", ""),
                    request.address_components.get("country", ""),
                ]
            ).strip(", "),
            address_complete=address_complete,
            has_replaced_components=bool(replaced_components),
            missing_components=missing_components,
            unconfirmed_components=unconfirmed_components,
            replaced_components=replaced_components,
            corrected_address=corrected_address,
            pin_location=pin_location,
            response_id=response_id,
        )
