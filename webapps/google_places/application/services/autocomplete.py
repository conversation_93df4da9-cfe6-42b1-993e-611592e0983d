from abc import ABC, abstractmethod

from webapps.google_places.application.dtos.autocomplete import AutocompleteSuggestions
from webapps.google_places.domain.dtos.shared import Location
from webapps.google_places.application.interfaces.google_places_gateway import GooglePlacesGateway
from webapps.google_places.domain.services.address_formatting_service import (
    AddressFormattingService,
)


class AbstractAutocompleteService(ABC):
    @abstractmethod
    def get_address_suggestions(
        self, search_input: str, location: Location | None, session_id: str | None = None
    ) -> AutocompleteSuggestions:
        pass

    @abstractmethod
    def get_business_suggestions(
        self, search_input: str, location: Location | None, session_id: str | None = None
    ) -> AutocompleteSuggestions:
        pass

    @abstractmethod
    def get_suggestions(
        self, search_input: str, location: Location | None, session_id: str | None = None
    ) -> AutocompleteSuggestions:
        pass


class AutocompleteService(AbstractAutocompleteService):
    def __init__(self, gateway: GooglePlacesGateway):
        self.gateway = gateway

    def get_address_suggestions(
        self, search_input: str, location: Location | None, session_id: str | None = None
    ) -> AutocompleteSuggestions:
        if not search_input:
            return AutocompleteSuggestions(suggestions=[])
        return self.gateway.get_address_autocomplete(
            AddressFormattingService.format_for_search(search_input), location, session_id
        )

    def get_business_suggestions(
        self, search_input: str, location: Location | None, session_id: str | None = None
    ) -> AutocompleteSuggestions:
        if not search_input:
            return AutocompleteSuggestions(suggestions=[])

        return self.gateway.get_business_autocomplete(search_input, location, session_id)

    def get_suggestions(
        self, search_input: str, location: Location | None, session_id: str | None = None
    ) -> AutocompleteSuggestions:
        if not search_input:
            return AutocompleteSuggestions(suggestions=[])

        return self.gateway.get_autocomplete(search_input, location, session_id)
