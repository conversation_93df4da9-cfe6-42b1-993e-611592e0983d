from decimal import Decimal

from dateutil.relativedelta import relativedelta
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers

from lib.feature_flag.feature.public_api import (
    PublicAPIServiceVariantStartsAtZeroFix,
    PublicAPIServiceVariantFreePriceValidation,
)
from lib.interval.tools import cmp_relativedeltas
from webapps.business.enums import PriceType


class UnsupportedFeatureValidator:
    requires_context = True
    message = _("Unsupported feature")

    def __init__(self, message=None):
        self.message = message or self.message

    def __call__(self, attrs, serializer):
        pass


class MinDurationValidator:
    requires_context = True
    message = _('Can not be shorter than {minutes} minutes.')

    def __init__(self, field, minutes=5, message=None):
        self.field = field
        self.minutes = minutes
        self.message = message or self.message

    def __call__(self, attrs, serializer):
        minutes = self.minutes
        value = attrs.get(self.field)
        if value is not None and cmp_relativedeltas(value, relativedelta(minutes=minutes)) < 0:
            raise serializers.ValidationError({self.field: self.message.format(minutes=minutes)})


class PriceValidator:
    requires_context = True

    def __init__(self, price_field='price', type_field='type'):
        self.price_field = price_field
        self.type_field = type_field

    def __call__(self, attrs, serializer):
        price_field = self.price_field
        type_field = self.type_field

        if PublicAPIServiceVariantFreePriceValidation():
            price = (
                attrs.get(price_field)
                if price_field in attrs
                else serializer.get_from_instance(price_field)
            )
            price_type = (
                attrs.get(type_field)
                if type_field in attrs
                else serializer.get_from_instance(type_field)
            )
        else:
            price = attrs.get(price_field)
            price_type = attrs.get(type_field) or serializer.get_from_instance(type_field)
            if not price:
                if (
                    price_type == PriceType.STARTS_AT
                    and price == Decimal(0.0)
                    and PublicAPIServiceVariantStartsAtZeroFix()
                ):
                    pass
                else:
                    price = serializer.get_from_instance(price_field)

        empty_values = (
            None,
            '',
        )
        if price_type in PriceType.has_price():
            if price in empty_values:
                raise serializers.ValidationError({price_field: _('Price is required')})
            if PublicAPIServiceVariantStartsAtZeroFix():
                if (price == 0 and price_type != PriceType.STARTS_AT) or price < 0:
                    raise serializers.ValidationError({price_field: _('Invalid price')})
            else:
                if price <= 0:
                    raise serializers.ValidationError({price_field: _('Invalid price')})
        else:
            if price not in empty_values:
                raise serializers.ValidationError({price_field: _('Price is not allowed')})
            attrs[price_field] = None
