from collections import defaultdict
from typing import NamedTuple, Optional
from django.db import transaction
from lib.celery_tools import celery_task


class BusinessImportRow(NamedTuple):
    business_id: int
    import_uid: str
    importer_name: Optional[str] = None


@celery_task()
def create_business_partner_data_task(businesses_list: list) -> None:
    from webapps.public_partners.models import BusinessPartnerData

    with transaction.atomic():
        # Convert to NamedTuple for cleaner access while building business_ids list
        business_rows = []
        for raw_row in businesses_list:
            business_id, import_uid, *rest = raw_row
            importer_name = rest[0] if rest and rest[0] else 'Versum'
            business_rows.append(BusinessImportRow(business_id, import_uid, importer_name))

        business_ids = [row.business_id for row in business_rows]
        existing_businesses = set(
            BusinessPartnerData.objects.filter(business_id__in=business_ids).values_list(
                'business_id', flat=True
            )
        )
        bulk_create_list = []
        for row in business_rows:
            import_uid = row.import_uid.split('.')[0]
            if row.business_id in existing_businesses:
                bpd = BusinessPartnerData.objects.get(business_id=row.business_id)
                bpd.import_uid = import_uid
                bpd.save()
            else:
                bulk_create_list.append(
                    BusinessPartnerData(business_id=row.business_id, import_uid=import_uid)
                )

        BusinessPartnerData.objects.bulk_create(bulk_create_list)

        # Group businesses by importer name for efficient bulk updates
        importer_groups = defaultdict(list)
        for row in business_rows:
            importer_groups[row.importer_name].append(row.business_id)

        # Update each group with their respective importer name
        for importer_name, business_ids_group in importer_groups.items():
            set_importer_name_for_businesses(business_ids_group, importer_name)


def set_importer_name_for_businesses(business_ids, importer_name='Versum'):
    from webapps.business.models import Business

    update_queries = []
    for business in Business.objects.filter(pk__in=business_ids).select_for_update():
        business.integrations['importer'] = importer_name
        update_queries.append(business)
    Business.objects.bulk_update(update_queries, ['integrations'])
