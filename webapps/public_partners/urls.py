from django.conf import settings
from django.urls import include, path, register_converter
from django.urls.converters import StringConverter
from oauth2_provider.views import (
    ConnectDiscoveryInfoView,
    JwksInfoView,
    UserInfoView,
)

from webapps.public_partners.enum import AppointmentStatusAction
from webapps.public_partners.router import SmartRouter, NestedSmartRouter
from webapps.public_partners.views import (
    OAuth2AuthorizationView,
    OAuth2TokenView,
    OAuth2RevokeTokenView,
    OAuth2IntrospectTokenView,
    OAuth2ApplicationViewSet,
    OAuth2InstallationViewSet,
    MeViewSet,
    AmenitiesViewSet,
    AppointmentViewSet,
    PartnerTokenView,
    PartnerRefreshTokenView,
    BusinessCustomerCountViewSet,
    BusinessCustomerViewSet,
    BusinessSubdomainViewSet,
    BusinessViewSet,
    ConsentFormViewSet,
    ConsentViewSet,
    CustomBusinessScheduleViewSet,
    CustomResourceScheduleViewSet,
    DefaultBusinessScheduleViewSet,
    DefaultResourceScheduleViewSet,
    HealthCheckView,
    WarmupView,
    ImageViewSet,
    LivenessCheckView,
    RegionReverseGeocodingViewSet,
    ResourceViewSet,
    DeprecatedReviewImportViewSet,
    ReviewStatisticsViewSet,
    ReviewViewSet,
    AppointmentReviewViewSet,
    ServiceCategoryViewSet,
    ServiceSuggestionViewSet,
    ServiceVariantViewSet,
    ServiceViewSet,
    DeprecatedResourceTimeOffGroupViewSet,
    ResourceTimeOffViewSet,
    TravelingToCustomerViewSet,
    UserViewSet,
    WebhookViewSet,
    WebhookLogViewSet,
    NotificationReceiverViewSet,
    NotificationDeliveryViewSet,
    BusinessCategoriesViewSet,
    BusinessCategoryViewSet,
    BusinessPrimaryCategoryViewSet,
    ResourcePhotoViewSet,
    ServicePhotoViewSet,
    SubdomainCandidateViewSet,
    BillingSubscriptionViewSet,
    InvoiceDetailsViewSet,
    CustomerClaimLogViewSet,
    BillingDiscountCodeViewSet,
    # Deprecated
    DeprecatedResourceViewSet,
    DeprecatedAppointmentViewSet,
    UpdateMetadataViewSet,
)


class AppointmentActionConverter(StringConverter):
    regex = "|".join(AppointmentStatusAction.values())


register_converter(AppointmentActionConverter, 'appointment_action')

base_urlpatterns = [
    path('health_check/', HealthCheckView.as_view(), name='health_check'),
    path('liveness_check/', LivenessCheckView.as_view(), name='liveness_check'),
    path('warmup/', WarmupView.as_view(), name='public-warmup'),
    path(
        'region_reverse_geocoding/',
        RegionReverseGeocodingViewSet.as_view({'get': 'retrieve'}),
        name='region_reverse_geocoding',
    ),
    path(
        'service_suggestion/',
        ServiceSuggestionViewSet.as_view({'get': 'list'}),
        name='service_suggestion-list',
    ),
    path(
        'business_category/',
        BusinessCategoriesViewSet.as_view({'get': 'list'}),
        name='business_category-list',
    ),
    path(
        'subdomain_candidate/<slug:name>/',
        SubdomainCandidateViewSet.as_view({'get': 'retrieve'}),
        name='subdomain_candidate',
    ),
]

partner_auth_urlpatterns = [
    path(
        'token/',
        PartnerTokenView.as_view({'post': 'create'}),
        name='partner_token',
    ),
    path(
        'refresh/',
        PartnerRefreshTokenView.as_view({'post': 'create'}),
        name='partner_refresh_token',
    ),
]

oauth2_urlpatterns = [
    path('authorize/', OAuth2AuthorizationView.as_view(), name='authorize'),
    path('token/', OAuth2TokenView.as_view(), name='token'),
    path('revoke_token/', OAuth2RevokeTokenView.as_view(), name='revoke-token'),
    path('introspect/', OAuth2IntrospectTokenView.as_view(), name='introspect'),
    path(
        '.well-known/openid-configuration/',
        ConnectDiscoveryInfoView.as_view(),
        name='oidc-connect-discovery-info',
    ),
    path('.well-known/jwks.json', JwksInfoView.as_view(), name='jwks-info'),
    path('userinfo/', UserInfoView.as_view(), name='user-info'),
]

appointment_urlpatterns = [
    path(
        'business/<int:business_pk>/appointment/',
        AppointmentViewSet.as_view(
            {
                'get': 'list',
                'post': 'create',
            }
        ),
        name='appointments',
    ),
    path(
        'business/<int:business_pk>/appointment/bulk_import/',
        AppointmentViewSet.as_view({'post': 'bulk_import'}),
        name='appointment_bulk_import',
    ),
    path(
        'business/<int:business_pk>/appointment/mapping/',
        AppointmentViewSet.as_view({'post': 'mapping'}),
        name='appointment_mapping',
    ),
    path(
        'business/<int:business_pk>/appointment/<int:pk>/',
        AppointmentViewSet.as_view(
            {
                'get': 'retrieve',
                'put': 'update',
                'post': 'confirm',  # deprecated
                'delete': 'destroy',  # deprecated
            }
        ),
        name='appointment',
    ),
    path(
        'business/<int:business_pk>/appointment/<int:pk>/status/<appointment_action:transition>/',
        AppointmentViewSet.as_view({'patch': 'update_status'}),
        name='appointment_status_transition',
    ),
    path(
        'business/<int:business_pk>/appointment/<int:appointment_pk>/review/',
        AppointmentReviewViewSet.as_view(
            {
                'get': 'retrieve',
                'post': 'create',  # deprecated
                'put': 'update',
                'patch': 'partial_update',
                'delete': 'destroy',
            }
        ),
        name='appointment_review',
    ),
    path(
        'business/<int:business_pk>/appointment/<int:appointment_pk>/consent/',
        ConsentViewSet.as_view({'get': 'list'}),
        name='appointment_consent',
    ),
    path(
        'business/<int:business_pk>/appointment/<int:appointment_pk>/metadata/',
        UpdateMetadataViewSet.as_view({'patch': 'partial_update'}),
        name='metadata_update',
    ),
]

# DEPRECATED
deprecated_urlpatterns = [
    path(
        'business_categories/',
        BusinessCategoriesViewSet.as_view({'get': 'list'}),
        name='business_categories_deprecated',
    ),
    path(
        'business/<int:business_pk>/categories/',
        BusinessCategoryViewSet.as_view({'get': 'list'}),
        name='business_categories_deprecated',
    ),
    path(
        'business/<int:business_pk>/categories/<int:pk>/',
        BusinessCategoryViewSet.as_view({'put': 'update', 'delete': 'destroy'}),
        name='business_category_details_deprecated',
    ),
    path(
        'business_listing/',
        BusinessViewSet.as_view({'get': 'list'}),
        name='business_listing_deprecated',
    ),
    path(
        'business_mapping/',
        BusinessViewSet.as_view({'post': 'mapping'}),
        name='business_mapping_deprecated',
    ),
    path(
        'business_details/<int:pk>/',
        BusinessViewSet.as_view(
            {
                'get': 'retrieve',
                'put': 'update',
                'patch': 'partial_update',
            },
        ),
        name='business_details_deprecated',
    ),
    path(
        'business_details/<int:pk>/activate/',
        BusinessViewSet.as_view({'put': 'activate'}),
        name='business_activate_deprecated',
    ),
    path(
        'business_details/<int:pk>/deactivate/',
        BusinessViewSet.as_view({'put': 'deactivate'}),
        name='business_deactivate_deprecated',
    ),
    path(
        'business/<int:business_pk>/customers/',
        BusinessCustomerViewSet.as_view({'get': 'list'}),
        name='business_customers_deprecated',
    ),
    path(
        'resource/<int:pk>/',
        DeprecatedResourceViewSet.as_view({'get': 'retrieve'}),
        name='resource_details_deprecated',
    ),
    path(
        'business/<int:business_pk>/staff_email/',
        ResourceViewSet.as_view({'post': 'staff_email'}),
        name='resource_mapping_deprecated',
    ),
    path(
        'business/<int:business_pk>/appointments/',
        AppointmentViewSet.as_view({'get': 'list'}),
        name='appointment_listing_deprecated',
    ),
    path(
        # TODO remove in future versions
        # this endpoint should accept business_id in url
        'appointment/<int:pk>/',
        DeprecatedAppointmentViewSet.as_view(
            {
                'put': 'update',
                'delete': 'destroy',
                'post': 'confirm',
            }
        ),
        name='appointment_action_deprecated',
    ),
]

router = SmartRouter()
router.register('oauth2/application', OAuth2ApplicationViewSet, basename='oauth2_application')
router.register('oauth2/application/webhook', WebhookViewSet, basename='oauth2_webhook')
router.register('oauth2/application/webhook_log', WebhookLogViewSet, basename='oauth2_webhook_log')
router.register(
    'oauth2/application/installation',
    OAuth2InstallationViewSet,
    basename='oauth2_installation',
)
router.register('me', MeViewSet, basename='me')
router.register('business', BusinessViewSet)

business_router = NestedSmartRouter(router, 'business', lookup='business')
business_router.register(
    'billing/subscription',
    BillingSubscriptionViewSet,
    basename='billing_subscription',
)
business_router.register(
    'billing/discount_code', BillingDiscountCodeViewSet, basename='billing_discount_code'
)
business_router.register('subdomain', BusinessSubdomainViewSet, basename='subdomain')
business_router.register('amenities', AmenitiesViewSet, basename='amenities')
business_router.register('category', BusinessCategoryViewSet, basename='category')
business_router.register(
    'primary_category',
    BusinessPrimaryCategoryViewSet,
    basename='primary_category',
)
business_router.register('consent_form', ConsentFormViewSet, basename='consent_form')
business_router.register(
    'traveling_to_customer',
    TravelingToCustomerViewSet,
    basename='traveling_to_customer',
)
business_router.register(
    'schedule/default',
    DefaultBusinessScheduleViewSet,
    basename='default_business_schedule',
)
business_router.register(
    'schedule/custom',
    CustomBusinessScheduleViewSet,
    basename='custom_business_schedule',
)
business_router.register(
    r'schedule/resource/(?P<resource_pk>[^/.]+)/default',
    DefaultResourceScheduleViewSet,
    basename='default_resource_schedule',
)
business_router.register(
    r'schedule/resource/(?P<resource_pk>[^/.]+)/custom',
    CustomResourceScheduleViewSet,
    basename='custom_resource_schedule',
)
business_router.register(
    r'schedule/resource/(?P<resource_pk>[^/.]+)/time_off',
    ResourceTimeOffViewSet,
    basename='resource_time_off',
)
business_router.register(
    r'schedule/resource/(?P<resource_pk>[^/.]+)/time_off_group',
    DeprecatedResourceTimeOffGroupViewSet,
    basename='resource_time_off_group',
)
business_router.register(
    r'notification/(?P<type>popup)/delivery',
    NotificationDeliveryViewSet,
    basename='notification_delivery',
)
business_router.register(
    r'notification/(?P<type>email)/receiver',
    NotificationReceiverViewSet,
    basename='notification_receiver',
)
business_router.register('image', ImageViewSet, basename='image')
business_router.register('images', ImageViewSet, basename='images')  # plural
business_router.register('resource', ResourceViewSet, basename='resource')
business_router.register('service', ServiceViewSet, basename='service')
business_router.register('service_category', ServiceCategoryViewSet, basename='service_categories')
business_router.register(r'user/(?P<type>owner|staffer)', UserViewSet, basename='user')
business_router.register('review_statistics', ReviewStatisticsViewSet, basename='review_statistics')
business_router.register('review_import', DeprecatedReviewImportViewSet, basename='review_import')
business_router.register('review', ReviewViewSet, basename='review')
business_router.register('customer', BusinessCustomerViewSet, basename='customer')
business_router.register(
    'customers_count',
    BusinessCustomerCountViewSet,
    basename='customers_count',
)
business_router.register('invoice_details', InvoiceDetailsViewSet, basename='invoice_details')
business_router.register(
    'customer_claim_log', CustomerClaimLogViewSet, basename='customer_claim_log'
)

service_router = NestedSmartRouter(business_router, 'service', lookup='service')
service_router.register('variant', ServiceVariantViewSet, basename='service_variant')
service_router.register('photo', ServicePhotoViewSet, basename='service_photo')

resource_router = NestedSmartRouter(business_router, 'resource', lookup='resource')
resource_router.register('photo', ResourcePhotoViewSet, basename='resource_photo')

urlpatterns = [
    path(
        r'oauth2/',
        include(
            (
                oauth2_urlpatterns,
                'oauth2_provider',
            ),
            namespace='oauth2_provider',
        ),
    ),
    path(r'', include(partner_auth_urlpatterns)),
    path(r'', include(base_urlpatterns)),
    path(r'', include(router.urls)),
    path(r'', include(business_router.urls)),
    path(r'', include(service_router.urls)),
    path(r'', include(resource_router.urls)),
    path(r'', include(appointment_urlpatterns)),
    path(r'', include(deprecated_urlpatterns)),
]

urlpatterns = [path(f"public-api/{settings.API_COUNTRY}/", include(urlpatterns))]

handler403 = 'webapps.public_partners.views.errors.permission_denied_handler'
handler404 = 'webapps.public_partners.views.errors.not_found_handler'
handler500 = 'webapps.public_partners.views.errors.internal_server_error_handler'

if settings.LOCAL_DEPLOYMENT:
    from webapps.public_partners.views.errors import (
        internal_server_error_view,
        permission_denied_view,
    )

    urlpatterns.append(path('403/', permission_denied_view, name='403'))
    urlpatterns.append(path('500/', internal_server_error_view, name='500'))
