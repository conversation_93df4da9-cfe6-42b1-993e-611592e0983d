from django.utils import timezone
from django.utils.dateparse import parse_datetime
from model_bakery import baker

from webapps.booking.enums import AppointmentType, AppointmentStatus
from webapps.booking.models import BookingSources, SubBooking, Appointment
from webapps.public_partners.models.metadata import AppointmentMetadata, SubbookingMetadata


def parse_datetime_with_tz(value, tz):
    dt = parse_datetime(value)
    return dt.astimezone(tz) if timezone.is_aware(dt) else timezone.make_aware(dt, tz)


def iso_datetime(value):
    value = value.isoformat()
    if value.endswith('+00:00'):
        value = value[:-6] + 'Z'
    return value


def create_appointment(business, subbookings, **kwargs):
    kwargs['type'] = kwargs.get('type', AppointmentType.BUSINESS)
    kwargs['status'] = kwargs.get('status', AppointmentStatus.ACCEPTED)
    kwargs['source'] = kwargs.get(
        'source', BookingSources.get_cached(app_type=BookingSources.PUBLIC_API_APP)
    )
    kwargs['updated_by'] = kwargs.get('updated_by', business.owner)
    subbookings = subbookings or []

    staffers = [booking_kwargs.pop('staffer', None) for booking_kwargs in subbookings]
    appliances = [booking_kwargs.pop('appliance', None) for booking_kwargs in subbookings]

    bookings = []
    for booking_kwargs in subbookings:
        if 'service_variant' in booking_kwargs:
            booking_kwargs['booked_from'] = parse_datetime_with_tz(
                booking_kwargs.pop('booked_from'),
                business.get_timezone(),
            )
            booking_kwargs['booked_till'] = (
                booking_kwargs['booked_from'] + booking_kwargs['service_variant'].duration
            )
            booking_kwargs['service_name'] = booking_kwargs['service_variant'].service.name
        else:
            booking_kwargs['booked_from'] = parse_datetime_with_tz(
                booking_kwargs.pop('booked_from'),
                business.get_timezone(),
            )
            booking_kwargs['booked_till'] = parse_datetime_with_tz(
                booking_kwargs.pop('booked_till'),
                business.get_timezone(),
            )
        bookings.append(baker.prepare(SubBooking, **booking_kwargs))

    appointment = baker.make(
        Appointment,
        business=business,
        booked_from=min(b.booked_from for b in bookings),
        booked_till=max(b.booked_till for b in bookings),
        **kwargs,
    )

    for booking in bookings:
        booking.appointment = appointment
        booking.created = appointment.created
        booking.save(override=True)

    if any(staffers) or any(appliances):
        for staffer, appliance, booking in zip(staffers, appliances, bookings):
            resources = list(filter(None, [staffer, appliance]))
            if resources:
                booking.resources.set(resources)

    return appointment


def add_appointment_metadata(appointment, subbookings):
    for subbooking in subbookings:
        baker.make(SubbookingMetadata, subbooking=subbooking, metadata={})

    baker.make(
        AppointmentMetadata,
        appointment=appointment,
        metadata={},
    )
