from webapps.public_partners.models import AppointmentMetadata, SubbookingMetadata


# TODO: We are not implementing a full blown Repo/application layer design here yet
def get_appointment_metadata(appointment_id: int):
    return AppointmentMetadata.objects.filter(appointment_id=appointment_id)


def get_subbooking_metadata(subbookings_ids: list[int]):
    return SubbookingMetadata.objects.filter(subbooking_id__in=subbookings_ids)
