# pylint: skip-file
from bo_obs.datadog.enums import BooksyTeams
from django.conf import settings
from django.db import transaction
from django.http import Http404
from rest_framework.mixins import UpdateModelMixin
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from lib.db import READ_ONLY_DB
from webapps.booking.models import SubBooking
from webapps.business.context import business_context
from webapps.public_partners.consts import STAFF_ACCESS_LEVELS_MIN_RECEPTION
from webapps.public_partners.enum import PublicAPIVersionEnum
from webapps.public_partners.permissions import (
    BusinessAccessPermission,
    OAuth2TokenMayHaveScopePermission,
    PartnerFirewallPermission,
    ResourceAccessPermission,
)
from webapps.public_partners.serializers.appointment_metadata import (
    PAAppointmentMetadataSerializerV05,
    PASubbookingMetadataSerializerV05,
)
from webapps.public_partners.versioning import VersionSpec
from webapps.public_partners.views.base import (
    BusinessRelatedViewMixin,
    GenericVersionPublicAPIViewSet,
)
from webapps.public_partners.services.appointment.appointment_metadata import (
    get_appointment_metadata,
    get_subbooking_metadata,
)


class UpdateMetadataViewSet(
    BusinessRelatedViewMixin,
    GenericVersionPublicAPIViewSet,
):  # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.PROVIDER_CALENDAR,)

    permission_classes = [
        IsAuthenticated,
        PartnerFirewallPermission,
        OAuth2TokenMayHaveScopePermission,
        BusinessAccessPermission,
        ResourceAccessPermission,
    ]
    versioned_oauth2_token_scopes = {
        VersionSpec(min_version=PublicAPIVersionEnum.V05): dict(
            partial_update=[[settings.OAUTH2_BUSINESS_WRITE_SCOPE]],
            default=[[settings.OAUTH2_BUSINESS_WRITE_SCOPE]],
        ),
    }
    versioned_resource_access_levels = {
        VersionSpec(min_version=PublicAPIVersionEnum.V05): dict(
            partial_update=STAFF_ACCESS_LEVELS_MIN_RECEPTION,
            default=STAFF_ACCESS_LEVELS_MIN_RECEPTION,
        ),
    }
    action_versions_map = {
        'partial_update': VersionSpec(min_version=PublicAPIVersionEnum.V05),
    }

    def get_object(self, queryset):
        instance = queryset.first()
        if not instance:
            raise Http404
        self.check_object_permissions(self.request, instance)
        return instance

    def get_objects(self, queryset):
        instances = list(queryset)
        if not instances:
            raise Http404
        for instance in instances:
            self.check_object_permissions(self.request, instance)
        return instances

    @transaction.atomic()
    def partial_update(self, request, *args, **kwargs):
        with business_context(self.business):
            appointment_metadata = self.get_object(
                get_appointment_metadata(self.kwargs['appointment_pk'])
            )

            subbooking_ids = [subbooking['id'] for subbooking in self.request.data['subbookings']]
            subbooking_metadata = self.get_objects(get_subbooking_metadata(subbooking_ids))

            appointment_metadata_serializer = PAAppointmentMetadataSerializerV05(
                instance=appointment_metadata, data=request.data['metadata']
            )
            subbooking_metadata_serializer = PASubbookingMetadataSerializerV05(
                instance=subbooking_metadata,
                data=request.data['subbookings'],
                many=True,
                partial=True,
            )

            appointment_metadata_serializer.is_valid()
            subbooking_metadata_serializer.is_valid(raise_exception=True)

            appointment_metadata_serializer.partial_update(
                appointment_metadata, appointment_metadata_serializer.validated_data
            )
            subbooking_metadata_serializer.update(
                subbooking_metadata, subbooking_metadata_serializer.initial_data
            )

            return Response(
                {
                    "appointment": appointment_metadata_serializer.data,
                    "subbookings": subbooking_metadata_serializer.data,
                }
            )
