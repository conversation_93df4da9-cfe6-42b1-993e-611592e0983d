from dateutil.relativedelta import relativedelta
from model_bakery import baker
from rest_framework import status

from lib.feature_flag.feature.public_api import (
    PublicAPIServiceVariantFreePriceValidation,
    PublicAPIServiceVariantStartsAtZeroFix,
)
from lib.test_utils import compare_expected_fields
from lib.tests.utils import override_eppo_feature_flag
from webapps.business.baker_recipes import (
    service_recipe,
    service_variant_recipe,
)
from webapps.business.enums import ComboType
from webapps.public_partners.tests import (
    FirewallTestCaseMixin,
    PublicApiBaseTestCase,
)


class ServiceVariantViewSetTestMixin:
    _url = '/public-api/us/business/{}/service/{}/variant/'
    _details_url = '/public-api/us/business/{}/service/{}/variant/{}/'
    _VERSION = '0.2'
    SERVICE_VARIANT_FIELDS = {
        'id',
        'type',
        'price',
        'duration',
        'label',
        'time_slot_interval',
        'gap_hole_duration',
        'gap_hole_start_after',
    }

    def get_url(self, business_id=None, service_id=None):
        return self._url.format(business_id or self.business.id, service_id or self.service.id)

    def get_details_url(self, variant_id, business_id=None, service_id=None):
        return self._details_url.format(
            business_id or self.business.id, service_id or self.service.id, variant_id
        )

    def get_sample_data(self, **kwargs):
        result = {
            'type': 'X',
            'price': '99.99',
            'duration': 60,
            'label': 'Some variant name',
        }
        return {**result, **kwargs}

    def test_check_not_allowed_methods(self):
        self.check_not_allowed_methods(
            self.get_url(), {'post', 'get', 'patch', 'delete', 'options'}
        )

    def test_create_success(self):
        data = self.get_sample_data()
        self.assertEqual(self.service.active_variants.all().count(), 0)
        resp = self.post(self.get_url(), data=data)
        self.assertEqual(resp.status_code, status.HTTP_201_CREATED)
        self.service.refresh_from_db()
        self.assertEqual(self.service.active_variants.all().count(), 1)

        variant = resp.json()
        compare_expected_fields(variant.keys(), self.SERVICE_VARIANT_FIELDS)
        self.assertIsNotNone(variant['id'])
        self.assertEqual(variant['type'], data['type'])
        self.assertEqual(variant['price'], data['price'])
        self.assertEqual(variant['duration'], data['duration'])
        self.assertEqual(variant['label'], data['label'])

        return variant, data

    def test_create_success_another_variant(self):
        service_variant_recipe.make(
            service=service_recipe.make(business=self.business)
        )  # second service
        service_variant_recipe.make(service=self.service)

        data = self.get_sample_data()
        self.assertEqual(self.service.active_variants.all().count(), 1)
        resp = self.post(self.get_url(), data=data)
        self.assertEqual(resp.status_code, status.HTTP_201_CREATED)
        self.service.refresh_from_db()
        self.assertEqual(self.service.active_variants.all().count(), 2)

        variant = resp.json()
        compare_expected_fields(variant.keys(), self.SERVICE_VARIANT_FIELDS)
        self.assertIsNotNone(variant['id'])
        self.assertEqual(variant['type'], data['type'])
        self.assertEqual(variant['price'], data['price'])
        self.assertEqual(variant['duration'], data['duration'])
        self.assertEqual(variant['label'], data['label'])

    def test_create_failure_with_invalid_payload(self):
        data = self.get_sample_data(duration=0)
        self.assertEqual(self.service.active_variants.all().count(), 0)
        resp = self.post(self.get_url(), data=data)
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        compare_expected_fields(resp.json().keys(), ['duration'])

        # Invalid gap hole duration
        data = self.get_sample_data(
            duration=60,
            gap_hole_duration=30,
            gap_hole_start_after=40,
        )
        resp = self.post(self.get_url(), data=data)
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        compare_expected_fields(resp.json().keys(), ['duration'])

        # Invalid gap hole duration
        data = self.get_sample_data(
            duration=60,
            gap_hole_duration=30,
        )
        resp = self.post(self.get_url(), data=data)
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        compare_expected_fields(resp.json().keys(), ['gap_hole_duration'])

        # Invalid gap hole data
        data = self.get_sample_data(
            duration=60,
            gap_hole_start_after=40,
        )
        resp = self.post(self.get_url(), data=data)
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        compare_expected_fields(resp.json().keys(), ['gap_hole_start_after'])
        self.assertEqual(self.service.active_variants.all().count(), 0)

    def test_create_failure_with_duplicated_payload(self):
        data = self.get_sample_data()
        service_variant_recipe.make(
            service=service_recipe.make(business=self.business),
            type=data['type'],
            duration=relativedelta(minutes=data['duration']),
            label=data['label'],
        )  # second service
        service_variant_recipe.make(
            service=self.service,
            type=data['type'],
            duration=relativedelta(minutes=data['duration']),
            label=data['label'],
        )
        self.assertEqual(self.service.active_variants.all().count(), 1)
        resp = self.post(self.get_url(), data=data)
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        self.service.refresh_from_db()
        self.assertEqual(self.service.active_variants.all().count(), 1)
        compare_expected_fields(resp.json().keys(), ['non_field_errors'])

    def test_update_success(self):
        expected_variant = service_variant_recipe.make(service=self.service)

        data = self.get_sample_data()
        self.assertEqual(self.service.active_variants.all().count(), 1)
        resp = self.patch(self.get_details_url(expected_variant.pk), data=data)
        self.assertEqual(resp.status_code, status.HTTP_200_OK)
        self.assertEqual(self.service.active_variants.all().count(), 1)

        variant = resp.json()
        compare_expected_fields(variant.keys(), self.SERVICE_VARIANT_FIELDS)
        self.assertEqual(variant['id'], expected_variant.id)
        self.assertEqual(variant['type'], data['type'])
        self.assertEqual(variant['price'], data['price'])
        self.assertEqual(variant['duration'], data['duration'])
        self.assertEqual(variant['label'], data['label'])

    @override_eppo_feature_flag({PublicAPIServiceVariantFreePriceValidation.flag_name: True})
    def test_update_success_for_free_type(self):
        expected_variant = service_variant_recipe.make(price='50', type='X', service=self.service)

        data = self.get_sample_data()
        data['type'] = 'F'
        data['price'] = ''
        self.assertEqual(self.service.active_variants.all().count(), 1)
        resp = self.put(self.get_details_url(expected_variant.pk), data=data)
        self.assertEqual(resp.status_code, status.HTTP_200_OK)
        self.assertEqual(self.service.active_variants.all().count(), 1)

        variant = resp.json()
        compare_expected_fields(variant.keys(), self.SERVICE_VARIANT_FIELDS)
        self.assertEqual(variant['id'], expected_variant.id)
        self.assertEqual(variant['type'], data['type'])
        self.assertEqual(variant['price'], None)

    @override_eppo_feature_flag({PublicAPIServiceVariantFreePriceValidation.flag_name: False})
    @override_eppo_feature_flag({PublicAPIServiceVariantStartsAtZeroFix.flag_name: True})
    def test_update_fail_for_free_type(self):
        expected_variant = service_variant_recipe.make(price='50', type='X', service=self.service)

        data = self.get_sample_data()
        data['type'] = 'F'
        data['price'] = ''

        self.assertEqual(self.service.active_variants.all().count(), 1)
        resp = self.put(self.get_details_url(expected_variant.pk), data=data)
        error = resp.data['price'][0]
        self.assertEqual(error.title(), 'Price Is Not Allowed')
        self.assertEqual(error.code, 'invalid')
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)

    def test_update_failure_with_combo_service(self):
        self.service = service_recipe.make(business=self.business, combo_type=ComboType.SEQUENCE)
        expected_variant1 = service_variant_recipe.make(service=self.service, type='X', label='L')

        self.assertEqual(self.service.active_variants.all().count(), 1)
        resp = self.patch(
            self.get_details_url(expected_variant1.pk),
            data=dict(type=expected_variant1.type),
        )
        self.assertEqual(resp.status_code, status.HTTP_422_UNPROCESSABLE_ENTITY)
        self.service.refresh_from_db()
        self.assertEqual(self.service.active_variants.all().count(), 1)
        body = resp.json()
        assert body == {'detail': "Can't modify variant related to combo service"}

    def test_update_failure_with_duplicated_payload(self):
        expected_variant1 = service_variant_recipe.make(service=self.service, type='X', label='L')
        expected_variant2 = service_variant_recipe.make(service=self.service, type='F', label='L')

        self.assertEqual(self.service.active_variants.all().count(), 2)
        resp = self.patch(
            self.get_details_url(expected_variant2.pk),
            data=dict(type=expected_variant1.type),
        )
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        self.service.refresh_from_db()
        self.assertEqual(self.service.active_variants.all().count(), 2)
        compare_expected_fields(resp.json().keys(), ['non_field_errors'])

    def test_get_inactive_variant(self):
        expected_variant = service_variant_recipe.make(service=self.service, active=False)
        resp = self.get(
            self.get_details_url(expected_variant.pk), data=self.get_sample_data(active=False)
        )
        self.assertEqual(resp.status_code, status.HTTP_200_OK)
        variant = resp.json()
        compare_expected_fields(variant.keys(), self.SERVICE_VARIANT_FIELDS)

    def test_get_variant(self):
        expected_variant = service_variant_recipe.make(service=self.service)

        self.assertEqual(self.service.active_variants.all().count(), 1)
        resp = self.get(self.get_details_url(expected_variant.pk))
        self.assertEqual(resp.status_code, status.HTTP_200_OK)
        self.assertEqual(self.service.active_variants.all().count(), 1)

        variant = resp.json()
        compare_expected_fields(variant.keys(), self.SERVICE_VARIANT_FIELDS)

    def test_list_variants(self):
        expected_variants = service_variant_recipe.make(service=self.service, _quantity=4)

        variants_count = self.service.active_variants.all().count()
        assert variants_count == len(expected_variants)
        resp = self.get(self.get_url())
        self.assertEqual(resp.status_code, status.HTTP_200_OK)

        variants = resp.json()
        assert len(variants) == len(expected_variants)
        service = resp.json()[0]
        compare_expected_fields(service.keys(), self.SERVICE_VARIANT_FIELDS)

    def test_list_inactive_variants(self):
        expected_variants = service_variant_recipe.make(
            service=self.service, _quantity=4, active=False
        )
        resp = self.get(self.get_url(), data={"active": False})
        self.assertEqual(resp.status_code, status.HTTP_200_OK)

        variants = resp.json()
        assert len(variants) == len(expected_variants)
        service = resp.json()[0]
        compare_expected_fields(service.keys(), self.SERVICE_VARIANT_FIELDS)

    def test_delete_variant(self):
        expected_variant = service_variant_recipe.make(service=self.service)

        self.assertEqual(self.service.active_variants.all().count(), 1)
        resp = self.delete(self.get_details_url(expected_variant.pk))
        self.assertEqual(resp.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(self.service.active_variants.all().count(), 0)

    def test_get_variant_incorrect_service(self):
        variant = service_variant_recipe.make(service=self.service)
        other_service = service_recipe.make(business=self.business)
        resp = self.get(self.get_details_url(variant.id, service_id=other_service.id))
        self.assertEqual(resp.status_code, status.HTTP_404_NOT_FOUND)

    def test_get_variant_incorrect_business(self):
        other_business = baker.make_recipe('webapps.business.business_recipe')
        service = service_recipe.make(business=other_business)
        variant = service_variant_recipe.make(service=service)
        resp = self.get(
            self.get_details_url(variant.id, service_id=service.id, business_id=other_business.id)
        )
        self.assertEqual(resp.status_code, status.HTTP_403_FORBIDDEN)

    def test_get_deleted_variant(self):
        variant = service_variant_recipe.make(service=self.service)
        self.assertEqual(self.service.active_variants.all().count(), 1)
        self.delete(self.get_details_url(variant.pk))
        self.assertEqual(self.service.active_variants.all().count(), 0)

        resp = self.get(self.get_details_url(variant.pk))
        self.assertEqual(resp.status_code, status.HTTP_404_NOT_FOUND)


class ServiceVariantViewSetV02TestCase(
    ServiceVariantViewSetTestMixin, FirewallTestCaseMixin, PublicApiBaseTestCase
):
    _VERSION = '0.2'

    def setUp(self):
        super().setUp()
        self.service = service_recipe.make(business=self.business)

    def basic_response_for_firewall_testing(self):
        expected_variant = service_variant_recipe.make(service=self.service)
        return self.get(self.get_details_url(expected_variant.pk))


class ServiceVariantViewSetV03TestCase(
    ServiceVariantViewSetTestMixin, FirewallTestCaseMixin, PublicApiBaseTestCase
):
    _VERSION = '0.3'
    SERVICE_VARIANT_FIELDS = {
        'id',
        'type',
        'price',
        'duration',
        'label',
        'time_slot_interval',
        'gap_hole_duration',
        'gap_hole_start_after',
        'active',
    }

    def setUp(self):
        super().setUp()
        self.service = service_recipe.make(business=self.business)

    def get_sample_data(self, **kwargs):
        result = super().get_sample_data()
        result['active'] = True
        return {**result, **kwargs}

    def test_create_success(self):
        variant, data = super().test_create_success()
        self.assertEqual(variant['label'], data['label'])

    def basic_response_for_firewall_testing(self):
        expected_variant = service_variant_recipe.make(service=self.service)
        return self.get(self.get_details_url(expected_variant.pk))
