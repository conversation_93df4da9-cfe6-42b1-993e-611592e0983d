from unittest.mock import patch

from rest_framework import status

from lib.feature_flag.feature.public_api import (
    PublicAPIServiceVariantStartsAtZeroFix,
    PublicAPIServiceVariantFreePriceValidation,
)
from lib.rivers import River
from lib.test_utils import compare_expected_fields
from lib.tests.utils import override_eppo_feature_flag
from webapps.business.enums import StaffAccessLevels
from webapps.business.models import Service, ServiceVariant
from webapps.public_partners.models import ServiceMetadata
from webapps.public_partners.tests import (
    PublicApiBaseTestCase,
    ResourcePermissionTestCaseMixin,
    TokenScopePermissionTestCaseMixin,
)


class ServiceBulkImportViewSetV04TestCase(
    TokenScopePermissionTestCaseMixin,
    ResourcePermissionTestCaseMixin,
    PublicApiBaseTestCase,
):
    _url = '/public-api/us/business/{}/service/bulk_import/'
    _VERSION = '0.4'
    SERVICE_FIELDS = {
        'id',
        'business_id',
        'name',
        'description',
        'note',
        'order',
        'is_online_service',
        'is_available_for_customer_booking',
        'padding_time',
        'padding_type',
        'tax_rate',
        'parallel_clients',
        'service_category_id',
        'treatment_id',
        'variants',
        'active',
        'questions',
        'import_uid',
        'is_traveling_service',
        'color',
        'combo_type',
        'metadata',
        'created',
        'updated',
    }
    _FORBIDDEN_STAFF_ACCESS_LEVELS = [
        StaffAccessLevels.STAFF,
        StaffAccessLevels.ADVANCED,
        StaffAccessLevels.RECEPTION,
    ]
    _REQUIRED_TOKEN_SCOPES = ['business:write']

    # pylint: disable=line-too-long
    def setUp(self):
        super().setUp()
        self.assign_treatments_task_patcher = patch(
            'webapps.public_partners.services.bulk_import.service_bulk_import.assign_treatments_task.delay'
        )
        self.assign_treatments_task_mock = self.assign_treatments_task_patcher.start()
        self.bump_document_patcher = patch(
            'webapps.public_partners.services.bulk_import.service_bulk_import.bump_document'
        )
        self.bump_document_mock = self.bump_document_patcher.start()

    def tearDown(self) -> None:
        self.assign_treatments_task_patcher.stop()
        self.bump_document_patcher.stop()
        super().tearDown()

    def get_bulk_import_url(self, business_id=None):
        return self._url.format(business_id or self.business.id)

    def basic_response_for_resource_permission_testing(self):
        return self.post(self.get_bulk_import_url(), data=[dict(name='Simple service 1')])

    def basic_response_for_token_scope_permission_testing(self):
        return self.post(self.get_bulk_import_url(), data=[dict(name='Simple service 1')])

    def test_bulk_import_success(self):
        with self.captureOnCommitCallbacks(execute=True):
            data = [
                dict(name='Simple service 1'),
                dict(name='Simple service 2'),
                dict(name='Simple service 3'),
            ]
            resp = self.post(self.get_bulk_import_url(), data)
            services = resp.json()

        self.assertEqual(resp.status_code, status.HTTP_201_CREATED)
        self.assertEqual(self.business.services.all().count(), 3)
        self.assertEqual(ServiceMetadata.objects.count(), 0)
        self.assertEqual(len(services), 3)

        self.assign_treatments_task_mock.assert_called_once_with([s['id'] for s in services], False)
        self.bump_document_mock.assert_called_once_with(River.SERVICE, [s['id'] for s in services])

        for idx, service in enumerate(services):
            compare_expected_fields(service.keys(), self.SERVICE_FIELDS)
            self.assertIsNotNone(service['id'])
            self.assertEqual(service['name'], data[idx]['name'])
            self.assertEqual(service['variants'], [])
            self.assertTrue(service['active'])
            self.assertIsNotNone(service['color'])
            self.assertEqual(len(service['color']), 6)

    def test_bulk_import_success_with_basic_variants(self):
        with self.captureOnCommitCallbacks(execute=True):
            data = [
                dict(
                    name='Simple service 1',
                    variants=[
                        dict(type='X', price=10, duration=10),
                        dict(type='X', price=20, duration=20),
                    ],
                ),
                dict(name='Simple service 2'),
                dict(name='Simple service 3', variants=[dict(type='X', price=30, duration=30)]),
            ]
            resp = self.post(self.get_bulk_import_url(), data)
            services = resp.json()

        self.assertEqual(resp.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Service.objects.filter(business_id=self.business.id).count(), 3)
        self.assertEqual(ServiceVariant.objects.filter(service_id=services[0]['id']).count(), 2)
        self.assertEqual(ServiceVariant.objects.filter(service_id=services[1]['id']).count(), 0)
        self.assertEqual(ServiceVariant.objects.filter(service_id=services[2]['id']).count(), 1)
        self.assertEqual(ServiceMetadata.objects.count(), 0)
        self.assertEqual(len(services), 3)

        self.assign_treatments_task_mock.assert_called_once_with([s['id'] for s in services], False)
        self.bump_document_mock.assert_called_once_with(River.SERVICE, [s['id'] for s in services])

        for idx, service in enumerate(services):
            compare_expected_fields(service.keys(), self.SERVICE_FIELDS)
            self.assertIsNotNone(service['id'])
            self.assertEqual(service['name'], data[idx]['name'])
            self.assertEqual(len(service['variants']), len(data[idx].get('variants', [])))
            self.assertTrue(service['active'])
            self.assertIsNotNone(service['color'])
            self.assertEqual(len(service['color']), 6)

    def test_bulk_import_success_with_metadata(self):
        with self.captureOnCommitCallbacks(execute=True):
            data = [
                dict(name='Simple service 1', metadata=dict(extra=dict(foo=1))),
                dict(name='Simple service 2'),
                dict(name='Simple service 3', metadata=dict(extra=dict(bar=2))),
            ]
            resp = self.post(self.get_bulk_import_url(), data)
            services = resp.json()

        self.assertEqual(resp.status_code, status.HTTP_201_CREATED)
        self.assertEqual(self.business.services.all().count(), 3)
        self.assertEqual(ServiceMetadata.objects.count(), 2)
        self.assertEqual(ServiceMetadata.objects.filter(service_id=services[0]['id']).count(), 1)
        self.assertEqual(ServiceMetadata.objects.filter(service_id=services[1]['id']).count(), 0)
        self.assertEqual(ServiceMetadata.objects.filter(service_id=services[2]['id']).count(), 1)
        self.assertEqual(len(services), 3)

        self.assign_treatments_task_mock.assert_called_once_with([s['id'] for s in services], False)
        self.bump_document_mock.assert_called_once_with(River.SERVICE, [s['id'] for s in services])

        for idx, service in enumerate(services):
            compare_expected_fields(service.keys(), self.SERVICE_FIELDS)
            self.assertIsNotNone(service['id'])
            self.assertEqual(service['name'], data[idx]['name'])
            self.assertEqual(
                (service['metadata'] or {}).get('extra'), data[idx].get('metadata', {}).get('extra')
            )
            self.assertEqual(service['variants'], [])
            self.assertTrue(service['active'])

    def test_bulk_import_success_with_duplicated_name(self):
        with self.captureOnCommitCallbacks(execute=True):
            data = [
                dict(name='Simple service 1'),
                dict(name='Simple service 1'),
                dict(name='Simple service 3'),
            ]
            resp = self.post(self.get_bulk_import_url(), data)
            services = resp.json()

        self.assertEqual(resp.status_code, status.HTTP_201_CREATED)
        self.assertEqual(self.business.services.all().count(), 3)
        self.assertEqual(ServiceMetadata.objects.count(), 0)
        self.assertEqual(len(services), 3)

        self.assign_treatments_task_mock.assert_called_once_with([s['id'] for s in services], False)
        self.bump_document_mock.assert_called_once_with(River.SERVICE, [s['id'] for s in services])

        for idx, service in enumerate(services):
            compare_expected_fields(service.keys(), self.SERVICE_FIELDS)
            self.assertIsNotNone(service['id'])
            self.assertEqual(service['name'], data[idx]['name'])
            self.assertEqual(service['variants'], [])
            self.assertTrue(service['active'])

    def test_bulk_import_failure_with_empty_name(self):
        with self.captureOnCommitCallbacks(execute=True):
            data = [
                dict(name='Simple service 1'),
                dict(name=None),
                dict(name='Simple service 3'),
            ]
            resp = self.post(self.get_bulk_import_url(), data)
            body = resp.json()

        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(self.business.services.all().count(), 0)
        self.assertEqual(ServiceMetadata.objects.filter(deleted__isnull=True).count(), 0)
        self.assertEqual(body, [{}, {'name': ['This field may not be null.']}, {}])

        self.assign_treatments_task_mock.assert_not_called()
        self.bump_document_mock.assert_not_called()

    @override_eppo_feature_flag({PublicAPIServiceVariantStartsAtZeroFix.flag_name: True})
    @override_eppo_feature_flag({PublicAPIServiceVariantFreePriceValidation.flag_name: True})
    def test_bulk_import_success_with_zero_starts_at_price(self):
        with self.captureOnCommitCallbacks(execute=True):
            data = [
                dict(name='Simple service', variants=[dict(type='S', price=0, duration=30)]),
            ]
            resp = self.post(self.get_bulk_import_url(), data)

        self.assertEqual(resp.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Service.objects.filter(business_id=self.business.id).count(), 1)
