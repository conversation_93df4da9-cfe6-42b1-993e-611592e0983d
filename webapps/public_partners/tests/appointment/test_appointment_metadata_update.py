# pylint: disable=line-too-long
from unittest.mock import patch

from dateutil.relativedelta import relativedelta
from django.db import connection
from django.test.utils import CaptureQueriesContext
from model_bakery import baker

from lib.test_utils import increase_appointment_next_id
from webapps.business.models import Resource, Service, ServiceVariant
from webapps.public_partners.models.metadata import SubbookingMetadata
from webapps.public_partners.test_utils import add_appointment_metadata, create_appointment
from webapps.public_partners.tests import PublicApiBaseTestCase, TokenScopePermissionTestCaseMixin


class BaseAppointmentMetadataViewV05TestCase(PublicApiBaseTestCase):
    _url_fmt = '/public-api/us/business/{}/appointment/{}/metadata/'.format
    _VERSION = '0.5'
    _APPOINTMENT_FIELDS = {
        'id',
        'business_id',
        'business_timezone',
        'status',
        'status_changed',
        'type',
        'booked_from',
        'booked_till',
        'subbookings',
        'booked_for_id',
        'customer_name',
        'customer_phone',
        'customer_email',
        'customer_note',
        'business_note',
        'business_secret_note',
        'traveling',
        'metadata',
        'import_uid',
        'version',
        'created',
        'updated',
    }
    _SUBBOOKING_FIELDS = {
        'id',
        'booked_from',
        'booked_till',
        'service_variant_id',
        'service_id',
        'service_name',
        'staffer_id',
        'appliance_id',
        'combo_type',
        'combo_children',
        'metadata',
    }

    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.staffer = baker.make(
            Resource,
            type=Resource.STAFF,
            business=cls.business,
        )
        cls.service = baker.make(
            Service,
            business=cls.business,
            name='service_name',
        )
        cls.service_variant = baker.make(
            ServiceVariant,
            duration=relativedelta(minutes=15),
            service=cls.service,
        )
        cls.service.add_staffers([cls.staffer])

    def setUp(self):
        super().setUp()
        increase_appointment_next_id()

    def url_fmt(self, appointment_pk, business_pk=None):
        return self._url_fmt(business_pk or self.business.pk, appointment_pk)


class SingleAppointmentUpdateViewV05TestCase(
    BaseAppointmentMetadataViewV05TestCase, TokenScopePermissionTestCaseMixin
):  # pylint: disable=too-many-public-methods
    def setUp(self):
        super().setUp()
        self.appointment = create_appointment(
            business=self.business,
            subbookings=[
                {
                    "booked_from": "2037-07-13T13:00",
                    "service_variant": self.service_variant,
                    "staffer": self.staffer,
                },
                {
                    "booked_from": "2038-07-13T13:00",
                    "service_variant": self.service_variant,
                    "staffer": self.staffer,
                },
                {
                    "booked_from": "2039-07-13T13:00",
                    "service_variant": self.service_variant,
                    "staffer": self.staffer,
                },
                {
                    "booked_from": '2039-07-13T13:00',
                    "service_variant": self.service_variant,
                    "staffer": self.staffer,
                },
                {
                    "booked_from": '2039-07-13T13:00',
                    "service_variant": self.service_variant,
                    "staffer": self.staffer,
                },
            ],
            updated_by=self.business.owner,
        )
        add_appointment_metadata(self.appointment, self.appointment.subbookings)
        target = 'webapps.public_partners.notification_dispatchers.notify_client_task'
        self.notification_patcher = patch(target=target)
        self.patched_notification_task = self.notification_patcher.start()

    def tearDown(self):
        self.notification_patcher.stop()
        super().tearDown()

    def basic_response_for_resource_permission_testing(self):
        self.patch(self.url_fmt(appointment_pk=self.appointment.pk), data={})

    def basic_response_for_token_scope_permission_testing(self):
        self.patch(self.url_fmt(appointment_pk=self.appointment.pk), data={})

    def test_update_success_with_valid_request(self):
        data = {
            "metadata": {"extra": "test"},
            "subbookings": [
                {
                    "id": self.appointment.subbookings[0].id,
                    "metadata": {"kind": "medical", "sync_status": "finished"},
                },
                {
                    "id": self.appointment.subbookings[1].id,
                    "metadata": {"kind": "medical", "sync_status": "pending"},
                },
            ],
        }

        resp = self.patch(self.url_fmt(appointment_pk=self.appointment.pk), data=data)
        assert resp.status_code == 200
        assert (
            SubbookingMetadata.objects.filter(subbooking_id=self.appointment.subbookings[0].id)
            .first()
            .metadata
            == data['subbookings'][0]['metadata']
        )
        assert (
            SubbookingMetadata.objects.filter(subbooking_id=self.appointment.subbookings[1].id)
            .first()
            .metadata
            == data['subbookings'][1]['metadata']
        )
        assert (
            SubbookingMetadata.objects.filter(subbooking_id=self.appointment.subbookings[2].id)
            .first()
            .metadata
            == {}
        )
        assert resp.json() == {
            'appointment': {
                'extra': 'test',
            },
            'subbookings': [
                {
                    'kind': 'medical',
                    'sync_status': 'finished',
                },
                {
                    'kind': 'medical',
                    'sync_status': 'pending',
                },
            ],
        }

    def test_update_does_not_overwrite(self):
        data = {
            "metadata": {"extra": "test"},
            "subbookings": [
                {"id": self.appointment.subbookings[0].id, "metadata": {"kind": "medical"}},
            ],
        }
        clean_subbooking = SubbookingMetadata.objects.filter(
            subbooking_id=self.appointment.subbookings[0].id
        ).first()
        clean_subbooking.metadata = {"sync_status": "pending"}
        clean_subbooking.save()

        expected_metadata = {"sync_status": "pending", "kind": "medical"}

        self.patch(self.url_fmt(appointment_pk=self.appointment.pk), data=data)
        assert (
            SubbookingMetadata.objects.filter(subbooking_id=self.appointment.subbookings[0].id)
            .first()
            .metadata
            == expected_metadata
        )

    def test_update_returns_404(self):
        data = {"random": "data"}
        resp = self.patch(self.url_fmt(appointment_pk=self.appointment.pk + 99), data=data)
        assert resp.status_code == 404

    def test_update_does_not_query_n_plus_1_query(self):
        large_request = {
            "metadata": {"extra": "test"},
            "subbookings": [
                {
                    "id": self.appointment.subbookings[0].id,
                    "metadata": {"kind": "medical", "sync_status": "finished"},
                },
                {
                    "id": self.appointment.subbookings[1].id,
                    "metadata": {"kind": "medical", "sync_status": "finished"},
                },
                {
                    "id": self.appointment.subbookings[2].id,
                    "metadata": {"kind": "medical", "sync_status": "finished"},
                },
                {
                    "id": self.appointment.subbookings[3].id,
                    "metadata": {"kind": "medical", "sync_status": "finished"},
                },
                {
                    "id": self.appointment.subbookings[4].id,
                    "metadata": {"kind": "medical", "sync_status": "finished"},
                },
            ],
        }

        small_request = {
            "metadata": {"extra": "test"},
            "subbookings": [
                {
                    "id": self.appointment.subbookings[0].id,
                    "metadata": {"kind": "medical", "sync_status": "finished"},
                },
                {
                    "id": self.appointment.subbookings[1].id,
                    "metadata": {"kind": "medical", "sync_status": "finished"},
                },
            ],
        }

        large_request_query_count = 0
        small_request_query_count = 0
        with CaptureQueriesContext(connection) as ctx_large:
            resp_large = self.patch(
                self.url_fmt(appointment_pk=self.appointment.pk), data=large_request
            )
            large_request_query_count = len(ctx_large)

        with CaptureQueriesContext(connection) as ctx_small:
            resp_small = self.patch(
                self.url_fmt(appointment_pk=self.appointment.pk), data=small_request
            )
            small_request_query_count = len(ctx_small)

        assert large_request_query_count == small_request_query_count
        assert resp_small.status_code == 200
        assert resp_large.status_code == 200
