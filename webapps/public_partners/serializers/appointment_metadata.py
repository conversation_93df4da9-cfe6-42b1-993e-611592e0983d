from rest_framework import serializers

from webapps.public_partners.enum import SubbookingKindEnum, SyncStatusEnum
from webapps.public_partners.models import AppointmentMetadata, SubbookingMetadata
from webapps.public_partners.serializers.metadata import MetadataSerializer


class PASubbookingMetadataSerializerV04(MetadataSerializer):
    class Meta(MetadataSerializer.Meta):
        model = SubbookingMetadata
        fields = (
            'application',
            'kind',
            'sync_status',
            'extra',
        )

    kind = serializers.ChoiceField(
        choices=SubbookingKindEnum,
        source='metadata.kind',
        required=False,
        allow_null=True,
    )
    sync_status = serializers.ChoiceField(
        choices=SyncStatusEnum,
        source='metadata.sync_status',
        required=False,
        allow_null=True,
    )


class PAAppointmentMetadataSerializerV04(MetadataSerializer):
    class Meta(MetadataSerializer.Meta):
        model = AppointmentMetadata
        fields = ('application', 'extra')


class PASubbookingMetadataListSerializerV05(serializers.ListSerializer):
    def update(self, instance, validated_data):
        updated_subbookings = []
        data_by_id = {item['id']: item for item in validated_data}
        for subbooking in instance:
            subbooking_data = data_by_id[subbooking.subbooking_id]
            if subbooking_data:
                new_metadata = subbooking_data['metadata']
                subbooking.metadata.update(new_metadata)
                updated_subbookings.append(subbooking)

        SubbookingMetadata.objects.bulk_update(updated_subbookings, ['metadata'])
        return instance


class PASubbookingMetadataSerializerV05(MetadataSerializer):
    class Meta(MetadataSerializer.Meta):
        model = SubbookingMetadata
        list_serializer_class = PASubbookingMetadataListSerializerV05
        fields = (
            'application',
            'kind',
            'sync_status',
            'extra',
        )

    kind = serializers.ChoiceField(
        choices=SubbookingKindEnum,
        source='metadata.kind',
        required=False,
        allow_null=True,
    )
    sync_status = serializers.ChoiceField(
        choices=SyncStatusEnum,
        source='metadata.sync_status',
        required=False,
        allow_null=True,
    )


class PAAppointmentMetadataSerializerV05(MetadataSerializer):
    class Meta(MetadataSerializer.Meta):
        model = AppointmentMetadata
        fields = (
            'application',
            'extra',
        )

    def partial_update(self, instance, validated_data):
        instance.metadata.update(validated_data['metadata'])
        instance.save()
        return instance
