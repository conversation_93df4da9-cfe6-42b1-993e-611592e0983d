from lib.point_of_sale.entities import ConvenienceFeeSettingsEntity
from lib.point_of_sale.enums import ConvenienceFeeMode
from webapps.point_of_sale.models import ConvenienceFeeSettings, ConvenienceFeePricingPlan


class ConvenienceFeeService:

    @staticmethod
    def get_convenience_fee_settings(business_id: int) -> ConvenienceFeeSettingsEntity:
        """
        Resolve the convenience fee mode for the business (falls back to default mode if doesnt exist),
        then pick the appropriate pricing plan and return its fees.
        Raises RuntimeError only when a required GLOBAL default plan is missing.

        Selection rules:
        - mode = row in ConvenienceFeeSettings for business_id, else default (PX_FULL_COST).
        - if mode in {SPLIT, CX_FULL_COST}:
            1) try business-specific plan,
            2) else fall back to GLOBAL default plan (default=True, business_id IS NULL),
            3) if no global default exists -> RuntimeError (env misconfiguration).
        - if mode == PX_FULL_COST: plan/fees are None (fees come from POS plans).
        """
        # 1) Determine mode for business (or fall back to the model's default)
        settings = ConvenienceFeeSettings.objects.filter(business_id=business_id).first()
        mode = settings.fee_mode if settings else ConvenienceFeeMode.PX_FULL_COST

        plan = None

        if mode in (ConvenienceFeeMode.SPLIT, ConvenienceFeeMode.CX_FULL_COST):
            # 2) Prefer business-specific plan
            plan = ConvenienceFeePricingPlan.objects.filter(
                business_id=business_id,
                fee_mode=mode,
            ).first()

            # 3) Otherwise use global default plan
            if plan is None:
                plan = ConvenienceFeePricingPlan.objects.filter(
                    default=True,
                    business_id__isnull=True,
                    fee_mode=mode,
                ).first()

                if plan is None:
                    # Only error case: required global default plan is missing
                    raise RuntimeError(  # pylint: disable=broad-exception-raised
                        "Environment misconfiguration, default ConvenienceFeePricingPlan "
                        f"for fee mode {mode} doesn't exist! Add it in admin to fix this error."
                    )

        # For PX_FULL_COST plan/fees are None (POS plans carry fees)
        return ConvenienceFeeSettingsEntity(
            mode=mode,
            payment_provision_percentage_merchant=(
                plan.payment_provision_percentage_merchant if plan else None
            ),
            payment_provision_fee_merchant=(
                plan.payment_provision_fee_merchant if plan else None
            ),
            payment_provision_percentage_customer=(
                plan.payment_provision_percentage_customer if plan else None
            ),
            payment_provision_fee_customer=(
                plan.payment_provision_fee_customer if plan else None
            ),
        )
