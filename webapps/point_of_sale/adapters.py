import uuid
from decimal import Decimal
from datetime import datetime

from lib.payment_gateway.entities import (
    DisputeSplitsEntity,
    RefundSplitsEntity,
    PaymentSplitsEntity,
)
from lib.payment_gateway.enums import PaymentMethodType as PaymentGatewayPaymentMethodType
from lib.payment_providers.entities import (
    AuthAdditionalDataEntity,
    AuthorizePaymentMethodDataEntity,
)
from lib.payments.enums import (
    PaymentProviderCode,
    RefundError,
)
from lib.point_of_sale.entities import BasketPaymentEntity
from lib.point_of_sale.enums import PaymentMethodType as PointOfSalePaymentMethodType
from lib.pos.entities import PaymentRowEntity, ReceiptDetailsEntity
from webapps.business.ports.business import get_business_statement_name
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_providers.ports.account_holder_ports import PaymentProvidersAccountHolderPort


def get_customer_wallet_id_adapter(user_id: int) -> uuid.UUID:
    return PaymentGatewayPort.get_customer_wallet(user_id).id


def get_anonymous_wallet_id_adapter() -> uuid.UUID:
    return PaymentGatewayPort.get_anonymous_wallet().id


def get_business_wallet_id_adapter(business_id: int) -> uuid.UUID | None:
    business_wallet = PaymentGatewayPort.get_business_wallet(business_id)
    return business_wallet.id if business_wallet else None


def initialize_payment_adapter(  # pylint: disable=too-many-arguments, too-many-positional-arguments
    amount: int,
    sender_id: uuid.UUID,
    receiver_id: uuid.UUID,
    payment_method: PointOfSalePaymentMethodType,
    payment_provider_code: PaymentProviderCode,
    auto_capture: bool,
    payment_splits: PaymentSplitsEntity,
    refund_splits: RefundSplitsEntity,
    dispute_splits: DisputeSplitsEntity,
    payment_token: str = None,
) -> uuid.UUID:
    return PaymentGatewayPort.initialize_payment(
        amount=amount,
        payment_splits=payment_splits,
        refund_splits=refund_splits,
        dispute_splits=dispute_splits,
        sender_id=sender_id,
        receiver_id=receiver_id,
        payment_method=PaymentGatewayPaymentMethodType(payment_method.value),
        payment_provider_code=payment_provider_code,
        capture_automatically=auto_capture,
        payment_token=payment_token,
    ).id


def initialize_refund_adapter(
    payment_balance_transaction_id: uuid.UUID,
    wallet_id: uuid.UUID,
    amount: int | None,
) -> uuid.UUID:
    return PaymentGatewayPort.initialize_refund(
        payment_balance_transaction_id=payment_balance_transaction_id,
        wallet_id=wallet_id,
        amount=amount,
    ).id


def is_refund_possible_adapter(
    balance_transaction_id: uuid.UUID,
    wallet_id: uuid.UUID,
    amount: int,
) -> tuple[bool, RefundError | None]:
    return PaymentGatewayPort.is_refund_possible(
        balance_transaction_id=balance_transaction_id,
        wallet_id=wallet_id,
        amount=amount,
    )


def authorize_payment_adapter(
    balance_transaction_id: uuid.UUID,
    wallet_id: uuid.UUID,
    payment_method_data: AuthorizePaymentMethodDataEntity,
    additional_data: AuthAdditionalDataEntity | None,
    off_session: bool | None = None,
) -> None:
    return PaymentGatewayPort.authorize_payment(
        balance_transaction_id=balance_transaction_id,
        wallet_id=wallet_id,
        additional_data=additional_data,
        payment_method_data=payment_method_data,
        off_session=off_session,
    )


def capture_payment_adapter(
    balance_transaction_id: uuid.UUID,
    wallet_id: uuid.UUID,
) -> None:
    return PaymentGatewayPort.capture_payment(
        balance_transaction_id=balance_transaction_id,
        wallet_id=wallet_id,
    )


def cancel_balance_transaction_adapter(
    balance_transaction_id: uuid.UUID,
    wallet_id: uuid.UUID,
) -> bool:
    return PaymentGatewayPort.cancel_balance_transaction(
        balance_transaction_id=balance_transaction_id,
        wallet_id=wallet_id,
    )


def get_business_statement_name_adapter(business_id: int) -> str | None:
    return get_business_statement_name(business_id=business_id)


def get_last_payment_row_entity_adapter(basket_payment_id: uuid.UUID) -> PaymentRowEntity:
    from webapps.pos.ports import PaymentRowPort  # pylint: disable=cyclic-import

    return PaymentRowPort.get_last_payment_row_entity(
        basket_payment_id=basket_payment_id,
    )


def mark_sent_for_refund_adapter(
    basket_payment_id: uuid.UUID, operator_id: int, amount: int = None
) -> PaymentRowEntity:
    from webapps.pos.ports import PaymentRowPort  # pylint: disable=cyclic-import

    return PaymentRowPort.mark_sent_for_refund(
        basket_payment_id=basket_payment_id,
        operator_id=operator_id,
        amount=Decimal(amount) / 100 if amount else None,
    )


def update_basket_payment_id_adapter(payment_row_id: int, basket_payment_id: uuid.UUID) -> None:
    from webapps.pos.ports import PaymentRowPort  # pylint: disable=cyclic-import

    return PaymentRowPort.update_basket_payment_id(
        payment_row_id=payment_row_id,
        basket_payment_id=basket_payment_id,
    )


def calculate_fees_adapter(
    payment_row_id: int,
    payment_provider_code: PaymentProviderCode,
) -> tuple[PaymentSplitsEntity, RefundSplitsEntity, DisputeSplitsEntity]:
    from webapps.pos.ports import TransactionPort

    return TransactionPort.calculate_fees(
        payment_row_id=payment_row_id,
        payment_provider_code=payment_provider_code,
    )


def synchronize_stripe_payment_intent_adapter(basket_payment_entity: BasketPaymentEntity):
    from webapps.stripe_integration.ports import StripeIntegrationPort

    StripeIntegrationPort.synchronize_stripe_payment_intent(
        basket_payment_entity=basket_payment_entity,
    )


def is_off_session_transaction_adapter(receipt_id: int) -> bool:
    from webapps.pos.ports import TransactionPort

    return TransactionPort.is_off_session_transaction(
        receipt_id=receipt_id,
    )


def get_minimal_pba_amount_adapter() -> Decimal:
    from webapps.pos.ports import PosSettingsPort

    return PosSettingsPort.get_minimal_pba_amount()


def get_tap_to_pay_promo_start_adapter(business_id: int) -> datetime | None:
    # based on fees acceptance date
    if wallet_entity := PaymentGatewayPort.get_business_wallet(business_id=business_id):
        resp = PaymentProvidersAccountHolderPort.get_account_holder_settings(
            account_holder_id=wallet_entity.account_holder_id,
            payment_provider_code=PaymentProviderCode.STRIPE,
        )
        if resp.entity and resp.entity.stripe:
            return resp.entity.stripe.tap_to_pay_fees_accepted_at


def get_receipt_details_adapter(basket_id: uuid.UUID) -> ReceiptDetailsEntity | None:
    """
    Returns receipt details (id and receipt_number) for a given basket_id.

    :param basket_id: UUID of the basket
    :return: ReceiptDetailsEntity with receipt id and receipt_number or None if not found
    """
    from webapps.pos.ports import TransactionPort

    return TransactionPort.get_receipt_details(basket_id=basket_id)


def is_voucher_egift_card_adapter(voucher_id: int) -> bool:
    from webapps.voucher.ports import EgiftCardPort

    return EgiftCardPort.is_voucher_egift_card(voucher_id=voucher_id)


def is_appointment_in_inactive_status_adapter(appointment_id: int) -> bool:
    from webapps.booking.ports import AppointmentPort

    return AppointmentPort.is_appointment_in_inactive_status(appointment_id)
