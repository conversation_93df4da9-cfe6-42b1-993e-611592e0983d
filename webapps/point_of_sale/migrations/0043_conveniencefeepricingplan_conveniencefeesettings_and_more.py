# Generated by Django 4.2.23 on 2025-08-08 13:45

from django.db import migrations, models
import lib.models
import lib.point_of_sale.enums
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ("point_of_sale", "0042_alter_basketpayment_payment_method_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="ConvenienceFeePricingPlan",
            fields=[
                (
                    "created",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="Created (UTC)"
                    ),
                ),
                (
                    "updated",
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name="Updated (UTC)"
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Deleted (UTC)"
                    ),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("business_id", models.IntegerField(null=True)),
                ("default", models.BooleanField(default=False)),
                (
                    "fee_mode",
                    models.CharField(
                        choices=[
                            ("px_full_cost", "Merchant covers all the fees (default)"),
                            (
                                "split",
                                "Fees are split between merchant and customer (convenience fee)",
                            ),
                            (
                                "cx_full_cost",
                                "Customer covers all the fees (convenience fee)",
                            ),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "payment_provision_percentage_merchant",
                    models.DecimalField(decimal_places=2, max_digits=10),
                ),
                ("payment_provision_fee_merchant", models.IntegerField()),
                (
                    "payment_provision_percentage_customer",
                    models.DecimalField(decimal_places=2, max_digits=10),
                ),
                ("payment_provision_fee_customer", models.IntegerField()),
            ],
            managers=[
                ("objects", lib.models.AutoUpdateManager()),
            ],
        ),
        migrations.CreateModel(
            name="ConvenienceFeeSettings",
            fields=[
                (
                    "created",
                    models.DateTimeField(
                        auto_now_add=True, verbose_name="Created (UTC)"
                    ),
                ),
                (
                    "updated",
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name="Updated (UTC)"
                    ),
                ),
                (
                    "deleted",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="Deleted (UTC)"
                    ),
                ),
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("business_id", models.IntegerField(null=True, unique=True)),
                (
                    "fee_mode",
                    models.CharField(
                        choices=[
                            ("px_full_cost", "Merchant covers all the fees (default)"),
                            (
                                "split",
                                "Fees are split between merchant and customer (convenience fee)",
                            ),
                            (
                                "cx_full_cost",
                                "Customer covers all the fees (convenience fee)",
                            ),
                        ],
                        default=lib.point_of_sale.enums.ConvenienceFeeMode[
                            "PX_FULL_COST"
                        ],
                        max_length=20,
                    ),
                ),
            ],
            options={
                "get_latest_by": "updated",
                "abstract": False,
            },
            managers=[
                ("objects", lib.models.AutoUpdateManager()),
            ],
        ),
        migrations.AddConstraint(
            model_name="conveniencefeepricingplan",
            constraint=models.UniqueConstraint(
                condition=models.Q(("business_id__isnull", True), ("default", True)),
                fields=("fee_mode",),
                name="cfpp_unique_global_default_per_fee_mode",
            ),
        ),
        migrations.AddConstraint(
            model_name="conveniencefeepricingplan",
            constraint=models.UniqueConstraint(
                condition=models.Q(("business_id__isnull", False)),
                fields=("business_id", "fee_mode"),
                name="cfpp_unique_per_business_per_fee_mode",
            ),
        ),
        migrations.AddConstraint(
            model_name="conveniencefeepricingplan",
            constraint=models.CheckConstraint(
                check=models.Q(
                    ("default", True), ("business_id__isnull", False), _negated=True
                ),
                name="cfpp_default_requires_global",
            ),
        ),
    ]
