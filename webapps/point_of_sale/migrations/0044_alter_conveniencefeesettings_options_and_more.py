# Generated by Django 4.2.23 on 2025-08-13 08:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('point_of_sale', '0043_conveniencefeepricingplan_conveniencefeesettings_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='conveniencefeesettings',
            options={
                'verbose_name': 'Convenience Fee Settings',
                'verbose_name_plural': 'Convenience Fee Settings',
            },
        ),
        migrations.AlterField(
            model_name='conveniencefeepricingplan',
            name='business_id',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='conveniencefeepricingplan',
            name='payment_provision_fee_customer',
            field=models.IntegerField(help_text='86 = $0.86 (or other currency)'),
        ),
        migrations.AlterField(
            model_name='conveniencefeepricingplan',
            name='payment_provision_fee_merchant',
            field=models.IntegerField(help_text='86 = $0.86 (or other currency)'),
        ),
        migrations.AlterField(
            model_name='conveniencefeepricingplan',
            name='payment_provision_percentage_customer',
            field=models.DecimalField(decimal_places=2, help_text='13.69 = 13.69%', max_digits=10),
        ),
        migrations.AlterField(
            model_name='conveniencefeepricingplan',
            name='payment_provision_percentage_merchant',
            field=models.DecimalField(decimal_places=2, help_text='13.69 = 13.69%', max_digits=10),
        ),
    ]
