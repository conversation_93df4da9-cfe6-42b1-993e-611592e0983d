import mock
import pytest
from django.conf import settings
from django.test import TestCase, override_settings
from model_bakery import baker
from parameterized import parameterized
from rest_framework.exceptions import ErrorDetail

from country_config import CountryConfig
from country_config.enums import Country
from lib.tools import tznow
from service.exceptions import ServiceError
from webapps.business.baker_recipes import business_recipe
from webapps.navision.baker_recipes import navision_settings_recipe
from webapps.navision.invoice_details_forms_serializers import (
    TaxIfVATRegisteredInvoiceDetailsSerializer,
    MandatoryTaxInvoiceDetailsSerializer,
    OptionalTaxInvoiceDetailsSerializer,
    InvoiceAddressSerializer,
    EditAndConfirmaInvoiceDetailsSerializer,
    OneTimeEditOptionalTaxInvoiceDetailsSerializer,
    TwoFieldsTaxIdInvoiceDetailsSerializer,
)
from webapps.navision.models.invoice_details import InvoiceDetailsBusinessSettings
from webapps.navision.models.merchant import Merchant
from webapps.navision.models.tax_rate import TaxGroup
from webapps.purchase.models import SubscriptionBuyer, InvoiceAddress
from webapps.structure.baker_recipes import bake_region_graphs, usa_recipe
from webapps.structure.models import Region


class InvoiceAddressTestCase(TestCase):
    zipcode_invalid_test_parameters = [
        # CHECK_ZIPCODE_IN_REGION_TABLE = False
        (False, '', Country.PL),
        (False, '31', Country.PL),
        (False, '31 999', Country.PL),
        (False, None, Country.PL),
        (False, '', Country.US),
        (False, '54', Country.US),
        (False, '123524', Country.US),
        (False, None, Country.US),
        # CHECK_ZIPCODE_IN_REGION_TABLE = True
        (True, '', Country.PL),
        (True, '31', Country.PL),
        (True, '31 999', Country.PL),
        (True, None, Country.PL),
        (True, '', Country.US),
        (True, '54', Country.US),
        (True, '123524', Country.US),
        (True, None, Country.US),
    ]

    def invoice_address_body(self):
        if settings.CHECK_ZIPCODE_IN_REGION_TABLE:
            self.state = baker.make(Region, name='Texas', type=Region.Type.STATE)
            self.county = baker.make(Region, type=Region.Type.COUNTY)
            self.city = baker.make(Region, type=Region.Type.CITY)
            self.zipcode = baker.make(Region, name='90111', type=Region.Type.ZIP)
            bake_region_graphs(self.state, self.county, self.city, self.zipcode)

            return dict(
                address_details1='Address 1',
                city='City',
                zipcode=self.zipcode.name,
            )

        return dict(
            address_details1='Address 1',
            city='City',
            zipcode='90123',
        )

    def test_non_alpha_city_names_are_supported(self):
        address = self.invoice_address_body()
        for city in ['6th of October', 'SomeCity', 'ㅙㅚㅝㅞㅟㅢ']:
            address['city'] = city
            serializer = InvoiceAddressSerializer(data=address)
            self.assertTrue(serializer.is_valid())

    @parameterized.expand(zipcode_invalid_test_parameters)
    def test_invalid_zipcode_create(self, zipcode_in_region_table, zipcode, country):
        with override_settings(
            CHECK_ZIPCODE_IN_REGION_TABLE=zipcode_in_region_table,
            COUNTRY_CONFIG=CountryConfig(country),
        ):
            address = self.invoice_address_body()
            address['zipcode'] = zipcode
            serializer = InvoiceAddressSerializer(data=address)
            self.assertFalse(serializer.is_valid())
            self.assertIsNotNone(serializer.errors.get('zipcode'))

    @parameterized.expand(zipcode_invalid_test_parameters)
    def test_invalid_zipcode_update(self, zipcode_in_region_table, zipcode, country):
        invoice_address = baker.make(InvoiceAddress)
        with override_settings(
            CHECK_ZIPCODE_IN_REGION_TABLE=zipcode_in_region_table,
            COUNTRY_CONFIG=CountryConfig(country),
        ):
            invoice_address_data = {'zipcode': zipcode}
            serializer = InvoiceAddressSerializer(
                instance=invoice_address,
                data=invoice_address_data,
                partial=True,
            )
            self.assertFalse(serializer.is_valid())
            self.assertIsNotNone(serializer.errors.get('zipcode'))

    @parameterized.expand(
        [
            ('wymyslony state',),
            (None,),
        ]
    )
    def test_assign_state_based_on_zipcode__create(self, new_state):
        address = self.invoice_address_body()
        address['state'] = new_state
        address['state_abbrev'] = new_state
        serializer = InvoiceAddressSerializer(data=address)
        self.assertTrue(serializer.is_valid())
        invoice_address = serializer.save()
        self.assertEqual(self.state, invoice_address.state)

    def test_assign_state_based_on_zipcode__update(self):
        state = baker.make(Region, type=Region.Type.STATE)
        county = baker.make(Region, type=Region.Type.COUNTY)
        city = baker.make(Region, type=Region.Type.CITY)
        zipcode = baker.make(Region, name='55555', type=Region.Type.ZIP)
        bake_region_graphs(state, county, city, zipcode)

        invoice_address = baker.make(InvoiceAddress)

        serializer = InvoiceAddressSerializer(
            instance=invoice_address,
            data={'zipcode': zipcode.name},
            partial=True,
        )
        self.assertTrue(serializer.is_valid())
        serializer.save()
        invoice_address.refresh_from_db()
        self.assertEqual(invoice_address.state, state)
        self.assertEqual(invoice_address.zipcode, zipcode)


class InvoiceDetailsTestMixin(TestCase):
    def setUp(self) -> None:
        super().setUp()
        self.region = usa_recipe.make()
        self.nav_settings = navision_settings_recipe.make(
            region=self.region,
            enable_invoice_details_editing=True,
        )

        self.business = business_recipe.make()

        self.state = baker.make(Region, name='Texas', type=Region.Type.STATE)
        self.county = baker.make(Region, type=Region.Type.COUNTY)
        self.city = baker.make(Region, type=Region.Type.CITY)
        self.zipcode = baker.make(Region, name='90210', type=Region.Type.ZIP)

        bake_region_graphs(self.state, self.county, self.city, self.zipcode)

        self.invoice_address = baker.make(
            InvoiceAddress,
            zipcode=self.zipcode,
            address_details1='jakis adres 10/12',
            city='Gotham City',
        )
        self.buyer_common_data = dict(
            businesses=[self.business],
            invoice_address=self.invoice_address,
            invoice_email='<EMAIL>',
            entity_name='Example Co.',
        )

        self.common_data = dict(
            zipcode=self.zipcode.name,
            address_details='jakis adres 10/12',
            city='Gotham City',
            invoice_email='<EMAIL>',
            entity_name='Example Co.',
        )


class MandatoryTaxInvoiceDetailsSerializerTestCase(InvoiceDetailsTestMixin):
    def setUp(self) -> None:
        super().setUp()
        self.data = dict(
            **self.common_data,
            tax_id='123',
        )

    @property
    def buyer(self):
        return baker.make(
            SubscriptionBuyer,
            **self.buyer_common_data,
            vat_registered=None,
            tax_id='123',
        )

    @property
    def buyer_sent_to_prod(self):
        return baker.make(
            SubscriptionBuyer,
            **self.buyer_common_data,
            vat_registered=None,
            tax_id='123',
            is_verified=True,
            merchant=baker.make(
                Merchant,
                synced_at=tznow(),
                sent_to_production=True,
            ),
        )

    def test_get_empty_buyer(self):
        for idx in range(3):
            baker.make(
                TaxGroup,
                name=f'Some group {idx + 1}',
                accounting_group='domestic',
                region=self.region if idx == 1 else None,
                _fill_optional=True,
            )

        serializer = MandatoryTaxInvoiceDetailsSerializer(instance=None)
        self.assertDictEqual(
            {
                'invoice_details_id': None,
                'data': {
                    'tax_id': '',
                    'entity_name': '',
                    'address_details': '',
                    'city': '',
                    'zipcode': '',
                    'tax_group': None,
                    'invoice_email': '',
                },
                'form': {
                    'id': 'MandatoryTaxID',
                    'properties': {
                        'tax_id': {
                            'label': 'TAX ID (Employer Identification Number)',
                            'required': True,
                            'editable': True,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'tax_group': {
                            'label': 'Registered tax territory',
                            'required': True,
                            'editable': True,
                            'available_values': [
                                {
                                    'label': 'Some group 1',
                                    'value': 'Some group 1',
                                    'default': False,
                                },
                                {
                                    'label': 'Some group 2',
                                    'value': 'Some group 2',
                                    'default': True,
                                },
                                {
                                    'label': 'Some group 3',
                                    'value': 'Some group 3',
                                    'default': False,
                                },
                            ],
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            serializer.data,
        )

    def test_get_buyer(self):
        for idx in range(3):
            tax_group = baker.make(
                TaxGroup,
                name=f'Some group {idx + 1}',
                accounting_group='domestic',
                region=self.region if idx == 1 else None,
                _fill_optional=True,
            )

        self.buyer_common_data.update({'tax_group': tax_group})

        serializer = MandatoryTaxInvoiceDetailsSerializer(
            instance=self.buyer_sent_to_prod,
            context={'business': self.business},
        )
        self.assertDictEqual(
            {
                'invoice_details_id': mock.ANY,
                'data': {
                    'tax_id': '123',
                    'entity_name': 'Example Co.',
                    'address_details': 'jakis adres 10/12',
                    'city': 'Gotham City',
                    'zipcode': '90210',
                    'tax_group': 'Some group 3',
                    'invoice_email': '<EMAIL>',
                },
                'form': {
                    'id': 'MandatoryTaxID',
                    'properties': {
                        'tax_id': {
                            'label': 'TAX ID (Employer Identification Number)',
                            'required': True,
                            'editable': False,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'tax_group': {
                            'label': 'Registered tax territory',
                            'required': True,
                            'editable': True,
                            'available_values': [
                                {
                                    'label': 'Some group 1',
                                    'value': 'Some group 1',
                                    'default': False,
                                },
                                {
                                    'label': 'Some group 2',
                                    'value': 'Some group 2',
                                    'default': True,
                                },
                                {
                                    'label': 'Some group 3',
                                    'value': 'Some group 3',
                                    'default': False,
                                },
                            ],
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            serializer.data,
        )

    def test_create_buyer(self):
        serializer = MandatoryTaxInvoiceDetailsSerializer(
            data=self.data,
            context={'business': self.business},
        )
        self.assertTrue(serializer.is_valid())
        instance = serializer.save()
        self.assertEqual(self.data['tax_id'], instance.tax_id)
        self.assertIsNone(instance.vat_registered)

        serializer = MandatoryTaxInvoiceDetailsSerializer(instance=instance)
        self.assertDictEqual(
            {
                'invoice_details_id': mock.ANY,
                'data': {
                    'tax_id': '123',
                    'entity_name': 'Example Co.',
                    'address_details': 'jakis adres 10/12',
                    'city': 'Gotham City',
                    'zipcode': '90210',
                    'invoice_email': '<EMAIL>',
                },
                'form': {
                    'id': 'MandatoryTaxID',
                    'properties': {
                        'tax_id': {
                            'label': 'TAX ID (Employer Identification Number)',
                            'required': True,
                            'editable': True,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            serializer.data,
        )


class TaxIfVATRegisteredWithVATInvoiceDetailsSerializerTestCase(InvoiceDetailsTestMixin):
    def setUp(self) -> None:
        super().setUp()
        self.data = dict(
            **self.common_data,
            vat_registered=True,
            tax_id='123',
        )

    @property
    def buyer(self):
        return baker.make(
            SubscriptionBuyer,
            **self.buyer_common_data,
            vat_registered=True,
            tax_id='123',
        )

    @property
    def buyer_sent_to_prod(self):
        return baker.make(
            SubscriptionBuyer,
            **self.buyer_common_data,
            vat_registered=True,
            tax_id='123',
            is_verified=True,
            merchant=baker.make(
                Merchant,
                synced_at=tznow(),
                sent_to_production=True,
            ),
        )

    def test_tax_registered_field_initially_true(self):
        serializer = TaxIfVATRegisteredInvoiceDetailsSerializer(instance=None)
        self.assertTrue(serializer.fields['vat_registered'].initial)

    def test_get_empty_buyer(self):
        for idx in range(3):
            baker.make(
                TaxGroup,
                name=f'Some group {idx+1}',
                accounting_group='domestic',
                region=self.region if idx == 1 else None,
                _fill_optional=True,
            )

        serializer = TaxIfVATRegisteredInvoiceDetailsSerializer(instance=None)
        self.assertDictEqual(
            {
                'invoice_details_id': None,
                'data': {
                    'vat_registered': True,
                    'tax_id': '',
                    'entity_name': '',
                    'address_details': '',
                    'city': '',
                    'zipcode': '',
                    'tax_group': None,
                    'invoice_email': '',
                },
                'form': {
                    'id': 'TaxIDIfVatRegistered',
                    'properties': {
                        'vat_registered': {
                            'label': "I'm registered for VAT",
                            'required': True,
                            'editable': True,
                        },
                        'tax_id': {
                            'label': 'TAX ID (Employer Identification Number)',
                            'required': True,
                            'editable': True,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'tax_group': {
                            'label': 'Registered tax territory',
                            'required': True,
                            'editable': True,
                            'available_values': [
                                {
                                    'label': 'Some group 1',
                                    'value': 'Some group 1',
                                    'default': False,
                                },
                                {
                                    'label': 'Some group 2',
                                    'value': 'Some group 2',
                                    'default': True,
                                },
                                {
                                    'label': 'Some group 3',
                                    'value': 'Some group 3',
                                    'default': False,
                                },
                            ],
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            serializer.data,
        )

    def test_get_buyer(self):
        for idx in range(3):
            tax_group = baker.make(
                TaxGroup,
                name=f'Some group {idx+1}',
                accounting_group='domestic',
                region=self.region if idx == 1 else None,
                _fill_optional=True,
            )

        self.buyer_common_data.update({'tax_group': tax_group})

        serializer = TaxIfVATRegisteredInvoiceDetailsSerializer(
            instance=self.buyer_sent_to_prod,
            context={'business': self.business},
        )

        self.assertDictEqual(
            {
                'invoice_details_id': mock.ANY,
                'data': {
                    'vat_registered': True,
                    'tax_id': '123',
                    'entity_name': 'Example Co.',
                    'address_details': 'jakis adres 10/12',
                    'city': 'Gotham City',
                    'zipcode': '90210',
                    'tax_group': 'Some group 3',
                    'invoice_email': '<EMAIL>',
                },
                'form': {
                    'id': 'TaxIDIfVatRegistered',
                    'properties': {
                        'vat_registered': {
                            'label': "I'm registered for VAT",
                            'required': True,
                            'editable': False,
                        },
                        'tax_id': {
                            'label': 'TAX ID (Employer Identification Number)',
                            'required': True,
                            'editable': False,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'tax_group': {
                            'label': 'Registered tax territory',
                            'required': True,
                            'editable': True,
                            'available_values': [
                                {
                                    'label': 'Some group 1',
                                    'value': 'Some group 1',
                                    'default': False,
                                },
                                {
                                    'label': 'Some group 2',
                                    'value': 'Some group 2',
                                    'default': True,
                                },
                                {
                                    'label': 'Some group 3',
                                    'value': 'Some group 3',
                                    'default': False,
                                },
                            ],
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            serializer.data,
        )

    @override_settings(
        NAVISION_USE_TAX_GROUPS=True,
    )
    def test_create_buyer_one_tax_group(self):
        tax_group = baker.make(
            TaxGroup,
            name='Some group',
            accounting_group='domestic',
            _fill_optional=True,
        )
        self.data['tax_group'] = tax_group.name
        serializer = TaxIfVATRegisteredInvoiceDetailsSerializer(
            data=self.data,
            context={'business': self.business},
        )
        self.assertTrue(serializer.is_valid())
        instance = serializer.save()
        self.assertEqual(self.data['tax_id'], instance.tax_id)
        self.assertEqual(tax_group, instance.tax_group)

        serializer = TaxIfVATRegisteredInvoiceDetailsSerializer(instance=instance)
        self.assertDictEqual(
            {
                'invoice_details_id': mock.ANY,
                'data': {
                    'vat_registered': True,
                    'tax_id': '123',
                    'entity_name': 'Example Co.',
                    'address_details': 'jakis adres 10/12',
                    'city': 'Gotham City',
                    'zipcode': '90210',
                    'invoice_email': '<EMAIL>',
                },
                'form': {
                    'id': 'TaxIDIfVatRegistered',
                    'properties': {
                        'vat_registered': {
                            'label': "I'm registered for VAT",
                            'required': True,
                            'editable': True,
                        },
                        'tax_id': {
                            'label': 'TAX ID (Employer Identification Number)',
                            'required': True,
                            'editable': True,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            serializer.data,
        )

    @override_settings(
        NAVISION_USE_TAX_GROUPS=True,
    )
    def test_create_buyer_one_tax_group_tax_groups(self):
        for idx in range(3):
            tax_group = baker.make(
                TaxGroup,
                name=f'Some group {idx+1}',
                accounting_group='domestic',
                region=self.region if idx == 1 else None,
                _fill_optional=True,
            )

        self.data['tax_group'] = tax_group.name
        serializer = TaxIfVATRegisteredInvoiceDetailsSerializer(
            data=self.data,
            context={'business': self.business},
        )
        self.assertTrue(serializer.is_valid())
        instance = serializer.save()
        self.assertEqual(tax_group, instance.tax_group)

        serializer = TaxIfVATRegisteredInvoiceDetailsSerializer(instance=instance)
        self.assertDictEqual(
            {
                'label': 'Registered tax territory',
                'required': True,
                'editable': True,
                'available_values': [
                    {
                        'label': 'Some group 1',
                        'value': 'Some group 1',
                        'default': False,
                    },
                    {
                        'label': 'Some group 2',
                        'value': 'Some group 2',
                        'default': True,
                    },
                    {
                        'label': 'Some group 3',
                        'value': 'Some group 3',
                        'default': False,
                    },
                ],
            },
            serializer.data['form']['properties']['tax_group'],
        )

    def test_create_buyer_no_tax_id(self):
        self.data.pop('tax_id')
        serializer = TaxIfVATRegisteredInvoiceDetailsSerializer(
            data=self.data,
            context={'business': self.business},
        )
        self.assertFalse(serializer.is_valid())
        self.assertEqual(
            {'tax_id': [ErrorDetail(string='This field is required.', code='required')]},
            serializer.errors,
        )

    def test_create_buyer_invalid_zipcode(self):
        self.data['zipcode'] = 'zipcode'
        serializer = TaxIfVATRegisteredInvoiceDetailsSerializer(
            data=self.data,
            context={'business': self.business},
        )
        with pytest.raises(ServiceError) as exc:
            serializer.is_valid()
            assert exc.value.args[1] == [
                {
                    'field': 'zipcode',
                    'description': ErrorDetail(string='Invalid zip code', code='invalid'),
                    'code': 'invalid',
                }
            ]

    @parameterized.expand(
        [
            (
                Country.PL,
                '1234122312',
                [ErrorDetail(string='Invalid NIP number', code='invalid')],
            ),
            (
                Country.PL,
                'not too valid',
                [ErrorDetail(string='NIP must consist of exactly 10 digits', code='invalid')],
            ),
            (
                Country.PL,
                '',
                [ErrorDetail(string='This field may not be blank.', code='blank')],
            ),
            (
                Country.PL,
                None,
                [ErrorDetail(string='This field may not be null.', code='null')],
            ),
            (
                Country.PL,
                '123412',
                [ErrorDetail(string='NIP must consist of exactly 10 digits', code='invalid')],
            ),
            (
                Country.PL,
                '3434324324224',
                [ErrorDetail(string='NIP must consist of exactly 10 digits', code='invalid')],
            ),
            (
                Country.ES,
                '112233',
                [ErrorDetail(string='Invalid TAX ID number', code='invalid')],
            ),
        ]
    )
    def test_create_buyer_invalid_tax_id(self, api_country, tax_id, expected_error):
        with override_settings(API_COUNTRY=api_country):
            self.data['tax_id'] = tax_id
            serializer = TaxIfVATRegisteredInvoiceDetailsSerializer(
                data=self.data,
                context={'business': self.business},
            )
            self.assertFalse(serializer.is_valid())
            self.assertEqual({'tax_id'}, serializer.errors.keys())
            self.assertEqual(expected_error, serializer.errors.get('tax_id'))

    def test_check_if_invoice_address_is_saved(self):
        serializer = TaxIfVATRegisteredInvoiceDetailsSerializer(
            data=self.data,
            context={'business': self.business},
        )
        self.assertTrue(serializer.is_valid())
        self.assertEqual(1, InvoiceAddress.objects.count())
        serializer.save()
        self.assertEqual(2, InvoiceAddress.objects.count())

    def test_update_tax_id_buyer_not_sent_to_nav(self):
        data_to_update = {'tax_id': '00997', 'entity_name': 'Majkrosoft'}
        serializer = TaxIfVATRegisteredInvoiceDetailsSerializer(
            instance=self.buyer,
            data=data_to_update,
            context={'business': self.business},
            partial=True,
        )
        self.assertTrue(serializer.is_valid())
        instance = serializer.save()
        self.assertEqual('00997', instance.tax_id)
        self.assertEqual('Majkrosoft', instance.entity_name)

    def test_update_tax_id_buyer_sent_to_nav(self):
        data_to_update = {'tax_id': '00997', 'vat_registered': False, 'entity_name': 'Majkrosoft'}
        serializer = TaxIfVATRegisteredInvoiceDetailsSerializer(
            instance=self.buyer_sent_to_prod,
            data=data_to_update,
            context={'business': self.business},
            partial=True,
        )
        self.assertFalse(serializer.is_valid())
        self.assertEqual(
            {
                'vat_registered': [
                    ErrorDetail(
                        string='This field cannot be edited',
                        code='buyer_sent_to_production',
                    )
                ],
                'tax_id': [
                    ErrorDetail(
                        string='This field cannot be edited',
                        code='buyer_sent_to_production',
                    )
                ],
            },
            serializer.errors,
        )

    def test_update_entity_name_buyer_sent_to_nav(self):
        data = {'entity_name': 'Majkrosoft'}
        serializer = TaxIfVATRegisteredInvoiceDetailsSerializer(
            instance=self.buyer_sent_to_prod,
            data=data,
            context={'business': self.business},
            partial=True,
        )
        self.assertTrue(serializer.is_valid())
        insta = serializer.save()
        self.assertEqual(data['entity_name'], insta.entity_name)
        self.assertEqual('123', insta.tax_id)
        self.assertDictEqual(
            {
                'invoice_details_id': mock.ANY,
                'data': {
                    'vat_registered': True,
                    'tax_id': '123',
                    'entity_name': 'Majkrosoft',
                    'address_details': 'jakis adres 10/12',
                    'city': 'Gotham City',
                    'zipcode': '90210',
                    'invoice_email': '<EMAIL>',
                },
                'form': {
                    'id': 'TaxIDIfVatRegistered',
                    'properties': {
                        'vat_registered': {
                            'label': "I'm registered for VAT",
                            'required': True,
                            'editable': False,
                        },
                        'tax_id': {
                            'label': 'TAX ID (Employer Identification Number)',
                            'required': True,
                            'editable': False,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            serializer.data,
        )
        self.assertIsNone(insta.invoice_address.state)

    def test_remove_invoice_address(self):
        data = {'address_details': None, 'city': None, 'zipcode': None}
        serializer = TaxIfVATRegisteredInvoiceDetailsSerializer(
            instance=self.buyer_sent_to_prod,
            data=data,
            context={'business': self.business},
            partial=True,
        )
        self.assertFalse(serializer.is_valid())
        self.assertEqual(
            {
                'address_details': [
                    ErrorDetail(string='This field may not be null.', code='null'),
                ],
                'city': [
                    ErrorDetail(string='This field may not be null.', code='null'),
                ],
                'zipcode': [
                    ErrorDetail(string='This field may not be null.', code='null'),
                ],
            },
            serializer.errors,
        )


class TaxIfVATRegisteredNoVATInvoiceDetailsSerializerTestCase(InvoiceDetailsTestMixin):
    def setUp(self) -> None:
        super().setUp()
        self.data = dict(
            **self.common_data,
            vat_registered=False,
        )

    @property
    def buyer(self):
        return baker.make(
            SubscriptionBuyer,
            **self.buyer_common_data,
            vat_registered=False,
            tax_id=None,
        )

    @property
    def buyer_sent_to_prod(self):
        return baker.make(
            SubscriptionBuyer,
            **self.buyer_common_data,
            vat_registered=False,
            tax_id=None,
            is_verified=True,
            merchant=baker.make(
                Merchant,
                synced_at=tznow(),
                sent_to_production=True,
            ),
        )

    def test_get_buyer(self):
        serializer = TaxIfVATRegisteredInvoiceDetailsSerializer(instance=self.buyer)
        self.assertDictEqual(
            {
                'invoice_details_id': mock.ANY,
                'data': {
                    'vat_registered': False,
                    'tax_id': None,
                    'entity_name': 'Example Co.',
                    'address_details': 'jakis adres 10/12',
                    'city': 'Gotham City',
                    'zipcode': '90210',
                    'invoice_email': '<EMAIL>',
                },
                'form': {
                    'id': 'TaxIDIfVatRegistered',
                    'properties': {
                        'vat_registered': {
                            'label': "I'm registered for VAT",
                            'required': True,
                            'editable': True,
                        },
                        'tax_id': {
                            'label': 'TAX ID (Employer Identification Number)',
                            'required': True,
                            'editable': True,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            serializer.data,
        )

    def test_create_buyer_with_tax_id(self):
        self.data['tax_id'] = '123'
        serializer = TaxIfVATRegisteredInvoiceDetailsSerializer(
            data=self.data,
            context={'business': self.business},
        )
        self.assertTrue(serializer.is_valid())
        instance = serializer.save()
        self.assertIsNone(instance.tax_id)

    def test_create_buyer_no_tax_id(self):
        serializer = TaxIfVATRegisteredInvoiceDetailsSerializer(
            data=self.data,
            context={'business': self.business},
        )
        self.assertTrue(serializer.is_valid())
        insta = serializer.save()

        self.assertIsNone(insta.tax_id)
        self.assertDictEqual(
            {
                'invoice_details_id': mock.ANY,
                'data': {
                    'vat_registered': False,
                    'tax_id': None,
                    'entity_name': 'Example Co.',
                    'address_details': 'jakis adres 10/12',
                    'city': 'Gotham City',
                    'zipcode': '90210',
                    'invoice_email': '<EMAIL>',
                },
                'form': {
                    'id': 'TaxIDIfVatRegistered',
                    'properties': {
                        'vat_registered': {
                            'label': "I'm registered for VAT",
                            'required': True,
                            'editable': True,
                        },
                        'tax_id': {
                            'label': 'TAX ID (Employer Identification Number)',
                            'required': True,
                            'editable': True,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            serializer.data,
        )

    def test_update_entity_name_buyer_not_sent_to_nav(self):
        buyer = self.buyer
        data_to_update = {
            'entity_name': 'świetna nowa nazwa firmy',
            'address_details': 'nowy adres',
        }
        serializer = TaxIfVATRegisteredInvoiceDetailsSerializer(
            instance=buyer,
            data=data_to_update,
            context={'business': self.business},
            partial=True,
        )
        self.assertTrue(serializer.is_valid())
        instance = serializer.save()

        self.assertEqual('świetna nowa nazwa firmy', instance.entity_name)
        self.assertEqual('nowy adres', instance.invoice_address.address_details1)
        self.assertIsNone(instance.tax_id)

        self.assertDictEqual(
            {
                'invoice_details_id': mock.ANY,
                'data': {
                    'vat_registered': False,
                    'tax_id': None,
                    'entity_name': 'świetna nowa nazwa firmy',
                    'address_details': 'nowy adres',
                    'city': 'Gotham City',
                    'zipcode': '90210',
                    'invoice_email': '<EMAIL>',
                },
                'form': {
                    'id': 'TaxIDIfVatRegistered',
                    'properties': {
                        'vat_registered': {
                            'label': "I'm registered for VAT",
                            'required': True,
                            'editable': True,
                        },
                        'tax_id': {
                            'label': 'TAX ID (Employer Identification Number)',
                            'required': True,
                            'editable': True,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            serializer.data,
        )

    def test_update_entity_name_buyer_sent_to_nav(self):
        buyer = self.buyer_sent_to_prod
        data_to_update = {
            'entity_name': 'świetna nowa nazwa firmy',
            'address_details': 'nowy adres',
        }
        serializer = TaxIfVATRegisteredInvoiceDetailsSerializer(
            instance=buyer,
            data=data_to_update,
            context={'business': self.business},
            partial=True,
        )
        self.assertTrue(serializer.is_valid())
        instance = serializer.save()

        self.assertEqual('świetna nowa nazwa firmy', instance.entity_name)
        self.assertEqual('nowy adres', instance.invoice_address.address_details1)
        self.assertIsNone(instance.tax_id)

        self.assertDictEqual(
            {
                'invoice_details_id': mock.ANY,
                'data': {
                    'vat_registered': False,
                    'entity_name': 'świetna nowa nazwa firmy',
                    'address_details': 'nowy adres',
                    'city': 'Gotham City',
                    'zipcode': '90210',
                    'invoice_email': '<EMAIL>',
                },
                'form': {
                    'id': 'TaxIDIfVatRegistered',
                    'properties': {
                        'vat_registered': {
                            'label': "I'm registered for VAT",
                            'required': True,
                            'editable': False,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            serializer.data,
        )


class OptionalTaxInvoiceDetailsSerializerTestCase(InvoiceDetailsTestMixin):
    def setUp(self) -> None:
        super().setUp()
        self.data = dict(
            **self.common_data,
        )

    def test_tax_id_label__usa(self):
        buyer = baker.make(SubscriptionBuyer, **self.buyer_common_data, tax_id='123')
        serializer = OptionalTaxInvoiceDetailsSerializer(instance=buyer)
        expected_label = 'Federal Tax ID - optional'
        self.assertEqual(expected_label, serializer.data['form']['properties']['tax_id']['label'])

    @override_settings(API_COUNTRY=Country.MX)
    def test_tax_id_label__mexico(self):
        """
        Field label is computed at import time,
        so overriding settings won’t affect classes already imported.
        The module has been reloaded, so the serializer is re-evaluated with the Mexico settings.
        """
        import importlib
        import webapps.navision.invoice_details_forms_serializers as invoice_serializers

        importlib.reload(invoice_serializers)
        region = baker.make(Region, type=Region.Type.COUNTRY, name='México')
        self.nav_settings.region = region
        self.nav_settings.save()
        buyer = baker.make(SubscriptionBuyer, **self.buyer_common_data, tax_id='123')

        serializer = invoice_serializers.OptionalTaxInvoiceDetailsSerializer(instance=buyer)

        expected_label = 'TAX ID (Employer Identification Number) - optional'
        self.assertEqual(expected_label, serializer.data['form']['properties']['tax_id']['label'])

    @parameterized.expand(
        [
            ('no_tax_id', False),
            ('with_tax_id', True),
        ]
    )
    def test_get_buyer(self, name, tax_id_exists):
        buyer = baker.make(
            SubscriptionBuyer,
            **self.buyer_common_data,
            tax_id='123' if tax_id_exists else None,
        )
        serializer = OptionalTaxInvoiceDetailsSerializer(instance=buyer)
        self.assertDictEqual(
            {
                'invoice_details_id': mock.ANY,
                'data': {
                    'tax_id': '123' if tax_id_exists else None,
                    'entity_name': 'Example Co.',
                    'address_details': 'jakis adres 10/12',
                    'city': 'Gotham City',
                    'zipcode': '90210',
                    'invoice_email': '<EMAIL>',
                },
                'form': {
                    'id': 'OptionalTaxID',
                    'properties': {
                        'tax_id': {
                            'label': 'Federal Tax ID - optional',
                            'required': False,
                            'editable': True,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            serializer.data,
        )

    def test_create_buyer_with_tax_id(self):
        self.data['tax_id'] = '123'
        serializer = OptionalTaxInvoiceDetailsSerializer(
            data=self.data,
            context={'business': self.business},
        )
        self.assertTrue(serializer.is_valid())
        instance = serializer.save()
        self.assertEqual(self.data['tax_id'], instance.tax_id)
        self.assertEqual(self.state, instance.invoice_address.state)

        self.assertDictEqual(
            {
                'invoice_details_id': mock.ANY,
                'data': {
                    'tax_id': '123',
                    'entity_name': 'Example Co.',
                    'address_details': 'jakis adres 10/12',
                    'city': 'Gotham City',
                    'zipcode': '90210',
                    'invoice_email': '<EMAIL>',
                },
                'form': {
                    'id': 'OptionalTaxID',
                    'properties': {
                        'tax_id': {
                            'label': 'Federal Tax ID - optional',
                            'required': False,
                            'editable': True,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            serializer.data,
        )

    def test_create_buyer_no_tax_id(self):
        self.data['tax_id'] = None
        serializer = OptionalTaxInvoiceDetailsSerializer(
            data=self.data,
            context={'business': self.business},
        )
        self.assertTrue(serializer.is_valid())
        instance = serializer.save()
        self.assertEqual(self.data['tax_id'], instance.tax_id)
        self.assertEqual(self.state, instance.invoice_address.state)

        self.assertDictEqual(
            {
                'invoice_details_id': mock.ANY,
                'data': {
                    'tax_id': None,
                    'entity_name': 'Example Co.',
                    'address_details': 'jakis adres 10/12',
                    'city': 'Gotham City',
                    'zipcode': '90210',
                    'invoice_email': '<EMAIL>',
                },
                'form': {
                    'id': 'OptionalTaxID',
                    'properties': {
                        'tax_id': {
                            'label': 'Federal Tax ID - optional',
                            'required': False,
                            'editable': True,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            serializer.data,
        )

    @parameterized.expand(
        [
            ('no_tax_id', False, None),
            ('with_tax_id', True, '997'),
        ]
    )
    def test_update_buyer(self, name, tax_id_exists, tax_id):
        buyer = baker.make(
            SubscriptionBuyer,
            **self.buyer_common_data,
            tax_id='123' if tax_id_exists else None,
        )
        data_to_update = {
            'entity_name': 'świetna nowa nazwa firmy',
            'address_details': 'nowy adres',
            'tax_id': tax_id,
        }
        serializer = OptionalTaxInvoiceDetailsSerializer(
            instance=buyer,
            data=data_to_update,
            context={'business': self.business},
            partial=True,
        )
        self.assertTrue(serializer.is_valid())
        instance = serializer.save()

        self.assertEqual('świetna nowa nazwa firmy', instance.entity_name)
        self.assertEqual('nowy adres', instance.invoice_address.address_details1)
        self.assertEqual(tax_id, instance.tax_id)

        self.assertDictEqual(
            {
                'invoice_details_id': mock.ANY,
                'data': {
                    'tax_id': tax_id,
                    'entity_name': 'świetna nowa nazwa firmy',
                    'address_details': 'nowy adres',
                    'city': 'Gotham City',
                    'zipcode': '90210',
                    'invoice_email': '<EMAIL>',
                },
                'form': {
                    'id': 'OptionalTaxID',
                    'properties': {
                        'tax_id': {
                            'label': 'Federal Tax ID - optional',
                            'required': False,
                            'editable': True,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            serializer.data,
        )


class OneTimeEditOptionalTaxInvoiceDetailsSerializerTestCase(InvoiceDetailsTestMixin):
    def setUp(self) -> None:
        super().setUp()
        self.data = dict(
            **self.common_data,
        )

    @property
    def buyer(self):
        return baker.make(
            SubscriptionBuyer,
            **self.buyer_common_data,
            tax_id='123',
        )

    def test_create_buyer(self):
        self.data['tax_id'] = '123'
        # create buyer
        serializer = OneTimeEditOptionalTaxInvoiceDetailsSerializer(
            data=self.data,
            context={'business': self.business},
        )
        self.assertTrue(serializer.is_valid())
        instance = serializer.save()

        # and then - get buyer
        serializer = OneTimeEditOptionalTaxInvoiceDetailsSerializer(
            instance=instance,
            context={'business': self.business},
        )
        self.assertDictEqual(
            {
                'invoice_details_id': mock.ANY,
                'data': {
                    'tax_id': '123',
                    'entity_name': 'Example Co.',
                    'address_details': 'jakis adres 10/12',
                    'city': 'Gotham City',
                    'zipcode': '90210',
                    'invoice_email': '<EMAIL>',
                },
                'form': {
                    'id': 'OneTimeEditOptionalTaxID',
                    'properties': {
                        'tax_id': {
                            'label': 'Federal Tax ID - optional',
                            'required': False,
                            'editable': False,
                        },
                        'entity_name': {
                            'label': 'First Name & Last name OR Company Name',
                            'required': True,
                            'editable': True,
                        },
                        'address_details': {
                            'label': 'Address',
                            'required': True,
                            'editable': True,
                        },
                        'city': {
                            'label': 'City',
                            'required': True,
                            'editable': True,
                        },
                        'zipcode': {
                            'label': 'Zip Code',
                            'required': True,
                            'editable': True,
                        },
                        'invoice_email': {
                            'label': 'Invoice E-mail Address',
                            'required': True,
                            'editable': True,
                        },
                    },
                },
            },
            serializer.data,
        )

    def test_tax_id_editable_when_not_confirmed(self):
        serializer = OneTimeEditOptionalTaxInvoiceDetailsSerializer(
            instance=self.buyer,
            context={'business': self.business},
        )
        self.assertTrue(serializer.tax_id_editable)
        self.assertTrue(serializer.data['form']['properties']['tax_id']['editable'])

    def test_tax_id_not_editable_when_confirmed(self):
        InvoiceDetailsBusinessSettings.objects.create(
            business_id=self.business.id,
            invoice_details_last_confirmed_dt=tznow(),
        )
        serializer = OneTimeEditOptionalTaxInvoiceDetailsSerializer(
            instance=self.buyer,
            context={'business': self.business},
        )
        self.assertFalse(serializer.tax_id_editable)
        self.assertFalse(serializer.data['form']['properties']['tax_id']['editable'])

    def test_update_tax_id_even_if_buyer_is_sent_to_prod(self):
        existing_buyer = baker.make(
            SubscriptionBuyer,
            **self.buyer_common_data,
            tax_id='12345',
            is_verified=True,
            merchant=baker.make(
                Merchant,
                synced_at=tznow(),
                sent_to_production=True,
            ),
        )
        new_buyer_data = {
            'entity_name': 'Updated Company Name',
            'tax_id': '456XXX123',
        }
        serializer = OneTimeEditOptionalTaxInvoiceDetailsSerializer(
            instance=existing_buyer,
            data=new_buyer_data,
            context={'business': self.business},
            partial=True,
        )

        self.assertTrue(serializer.is_valid())

        instance = serializer.save()

        self.assertEqual('Updated Company Name', instance.entity_name)
        self.assertEqual('456XXX123', instance.tax_id)

    @parameterized.expand(['456', None])
    def test_update_buyer_various_tax_id_values(self, tax_id):
        data_to_update = {
            'entity_name': 'Updated Company Name',
            'tax_id': tax_id,
            'address_details': 'New Address',
        }
        serializer = OneTimeEditOptionalTaxInvoiceDetailsSerializer(
            instance=self.buyer,
            data=data_to_update,
            context={'business': self.business},
            partial=True,
        )
        self.assertTrue(serializer.is_valid())
        instance = serializer.save()
        self.assertEqual('Updated Company Name', instance.entity_name)
        self.assertEqual(tax_id, instance.tax_id)
        self.assertEqual('New Address', instance.invoice_address.address_details1)

    def test_check_if_invoice_address_is_saved(self):
        self.data['tax_id'] = '123'
        serializer = OneTimeEditOptionalTaxInvoiceDetailsSerializer(
            data=self.data,
            context={'business': self.business},
        )
        self.assertTrue(serializer.is_valid())
        self.assertEqual(1, InvoiceAddress.objects.count())
        serializer.save()
        self.assertEqual(2, InvoiceAddress.objects.count())


@pytest.mark.django_db
@pytest.mark.parametrize(
    ('buyer_exists', 'tax_id', 'exp_value'),
    [
        (False, None, 'GB'),
        (True, None, None),
        (True, '*********', '*********'),
    ],
)
@override_settings(INVOICE_DETAILS_TAX_ID_PREFIX='GB')
def test_initial_prefix(buyer_exists, tax_id, exp_value):
    navision_settings_recipe.make(enable_invoice_details_editing=True)
    serializers_with_initial_prefix = [
        TaxIfVATRegisteredInvoiceDetailsSerializer,
        EditAndConfirmaInvoiceDetailsSerializer,
        TwoFieldsTaxIdInvoiceDetailsSerializer,
    ]
    business = baker.make('business.Business')
    buyer = baker.make(
        SubscriptionBuyer,
        businesses=[business],
        vat_registered=True,
        tax_id=tax_id,
    )
    for serializer_cls in serializers_with_initial_prefix:
        if buyer_exists:
            serializer = serializer_cls(instance=buyer, context={'business': business})
        else:
            serializer = serializer_cls(instance=None, context={'business': business})
        assert serializer.data['data']['tax_id'] == exp_value
