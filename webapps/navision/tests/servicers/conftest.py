from typing import Generator

import pytest
from booksy_proto_invoicing.grpc.v1.merchant_pb2_grpc import MerchantServiceStub
from django_socio_grpc.tests.grpc_test_utils.fake_grpc import FakeChannel, FakeGRPC
from model_bakery import baker

from grpc_api.urls import grpc_handlers
from webapps.structure.baker_recipes import bake_region_graphs
from webapps.structure.models import Region


@pytest.fixture(name='test_channel')
def make_test_channel() -> FakeChannel:
    fake_grpc = FakeGRPC(lambda servicer, server: grpc_handlers(server), None)
    fake_channel = fake_grpc.get_fake_channel()
    yield fake_channel


@pytest.fixture(name='merchant_stub')
def make_merchant_stub(test_channel) -> Generator[MerchantServiceStub, None, None]:
    yield MerchantServiceStub(test_channel)


@pytest.fixture(name='tax_group')
def make_tax_group():
    return baker.make('navision.TaxGroup', name='domestic-us', accounting_group='domestic')


@pytest.fixture(name='invoice_address')
def make_invoice_address():
    buyer_zipcode = baker.make(
        Region,
        type=Region.Type.ZIP,
        name='90210',
    )
    buyer_state = baker.make(
        Region,
        type=Region.Type.STATE,
        name='California',
        abbrev='CA',
    )
    bake_region_graphs(buyer_state, buyer_zipcode)
    return baker.make(
        'purchase.InvoiceAddress',
        address_details1='Some address details 12',
        city='Beverly Hills',
        state=buyer_state,
        zipcode=buyer_zipcode,
    )


@pytest.fixture
def business_with_buyer(tax_group, invoice_address):
    buyer = baker.make(
        'purchase.SubscriptionBuyer',
        merchant=None,
        active=True,
        tax_group=tax_group,
        invoicing_allowed=True,
        invoicing_exclusion_reason='test buyer',
        is_verified=True,
        tax_id='PL-12345',
        vat_registered=True,
        invoice_email='<EMAIL>',
        entity_name='test name',
        invoice_address=invoice_address,
    )

    return baker.make(
        'business.Business', buyer=buyer, include_in_analysis=False, name='super test business name'
    )
