from unittest.mock import patch

import pytest

# pylint: disable=no-name-in-module
from booksy_proto_invoicing.grpc.v1.merchant_pb2 import GetMerchantRequest
from django_socio_grpc.tests.grpc_test_utils.fake_grpc import FakeRp<PERSON><PERSON>rror
from model_bakery import baker

from lib.feature_flag.feature.navision import NavisionGetAndSyncMerchantFlag
from lib.tests.utils import override_eppo_feature_flag


@pytest.mark.grpc_api
@pytest.mark.django_db
@patch('webapps.navision.servicers.merchant.create_or_update_merchant_in_navision.run')
@override_eppo_feature_flag({NavisionGetAndSyncMerchantFlag.flag_name: True})
def test_get_merchant_success(mock_navision_task, merchant_stub, business_with_buyer):
    buyer = business_with_buyer.buyer
    request_data = GetMerchantRequest(country_code='us', business_id=str(business_with_buyer.id))
    response = merchant_stub.GetMerchant(request=request_data)
    mock_navision_task.assert_called_once_with(buyer.merchant.id)

    assert response.merchant_id == buyer.merchant.merchant_id
    assert response.business_name == 'super test business name'
    assert response.buyer_name == 'test name'
    assert response.invoicing_allowed is True
    assert response.is_active is True
    assert response.is_verified is True
    assert response.vat_registered is True
    assert response.invoicing_exclusion_reason == 'test buyer'
    assert response.is_test_business is True
    assert response.emails == '<EMAIL>'
    assert response.tax_id == 'PL-12345'
    assert response.tax_group_name == 'domestic-us'
    assert response.accounting_group == 'domestic'
    assert response.address_details == 'Some address details 12'
    assert response.zip_code == '90210'
    assert response.city == 'Beverly Hills'
    assert response.state == 'CA'


@pytest.mark.grpc_api
@override_eppo_feature_flag({NavisionGetAndSyncMerchantFlag.flag_name: True})
@patch('webapps.navision.servicers.merchant.create_or_update_merchant_in_navision.run')
@pytest.mark.django_db
def test_get_merchant_business_not_found(mock_navision_task, merchant_stub):
    request_data = GetMerchantRequest(country_code='us', business_id='12345')

    with pytest.raises(FakeRpcError) as err:
        merchant_stub.GetMerchant(request=request_data)

    assert err.value.details() == 'Business does not exist'
    mock_navision_task.assert_not_called()


@pytest.mark.grpc_api
@override_eppo_feature_flag({NavisionGetAndSyncMerchantFlag.flag_name: True})
@patch('webapps.navision.servicers.merchant.create_or_update_merchant_in_navision.run')
@pytest.mark.django_db
def test_get_merchant_buyer_not_found(mock_navision_task, merchant_stub):
    business = baker.make('business.Business')
    request_data = GetMerchantRequest(country_code='us', business_id=str(business.id))

    with pytest.raises(FakeRpcError) as err:
        merchant_stub.GetMerchant(request=request_data)

    assert err.value.details() == 'Buyer does not exist'
    mock_navision_task.assert_not_called()


@pytest.mark.grpc_api
@override_eppo_feature_flag({NavisionGetAndSyncMerchantFlag.flag_name: True})
@pytest.mark.django_db
@patch('webapps.navision.servicers.merchant.create_or_update_merchant_in_navision.run')
@pytest.mark.parametrize(
    'business_email, buyer_email, buyer_extra_emails, expected_emails',
    [
        (
            None,
            '<EMAIL>',
            ['<EMAIL>', '<EMAIL>'],
            '<EMAIL>, <EMAIL>, <EMAIL>',
        ),
        (
            None,
            None,
            ['<EMAIL>', '<EMAIL>'],
            '<EMAIL>, <EMAIL>',
        ),
        (
            '<EMAIL>',
            None,
            ['<EMAIL>'],
            '<EMAIL>',
        ),
        (
            None,
            '<EMAIL>',
            None,
            '<EMAIL>',
        ),
        (
            None,
            None,
            None,
            '',
        ),
    ],
)
def test_get_merchant_emails(
    _, merchant_stub, business_email, buyer_email, buyer_extra_emails, expected_emails
):
    buyer = baker.make(
        'purchase.SubscriptionBuyer',
        invoice_email=buyer_email,
        extra_invoice_emails=buyer_extra_emails,
    )
    business = baker.make(
        'business.Business',
        buyer=buyer,
        invoice_email=business_email,
    )

    request_data = GetMerchantRequest(country_code='us', business_id=str(business.id))
    response = merchant_stub.GetMerchant(request=request_data)

    assert response.emails == expected_emails
