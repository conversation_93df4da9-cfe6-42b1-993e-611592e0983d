from enum import Enum

from django.conf import settings

from country_config.enums import Country
from lib.enums import StrChoicesEnum, StrEnum
from webapps.boost.enums import TransactionPaymentSource


class TargetAPI(
    int, Enum
):  # int to make it JSON serializable for the MerchantErrorResponse objects
    SANDBOX = 0
    PRODUCTION = 1


class TaxRateService(StrChoicesEnum):
    SAAS = 'SAAS', 'SaaS'
    BOOST = 'BOOST', 'Boost'
    STAFFERS = 'STAFFERS', 'Staffers'
    SMS = 'SMS', 'SMS'


class InvoiceDetailsFormsEnum(StrEnum):
    CONFIRM = 'path2_update_and_confirm'
    PATH_1 = 'tax_id_mandatory'
    PATH_2 = 'tax_id_mandatory_if_vat_registered'
    PATH_3 = 'tax_id_optional'
    TWO_TAX_ID_FIELDS = 'two_fields_for_tax_id'
    CONFIRM_OPTIONAL_TAX_ID = 'confirm_optional_tax_id'


class InvoiceService(StrChoicesEnum):
    @classmethod
    def to_tax_service(cls, service) -> TaxRateService:
        return {
            InvoiceService.SAAS: TaxRateService.SAAS,
            InvoiceService.SAAS_PER_BC: TaxRateService.SAAS,
            InvoiceService.BOOST: TaxRateService.BOOST,
            InvoiceService.STAFFERS: TaxRateService.STAFFERS,
            InvoiceService.SMS: TaxRateService.SMS,
        }[service]

    SAAS = 'SaaS', 'SaaS'
    BOOST = 'Boost', 'Boost'
    STAFFERS = 'Staffers', 'Staffers'
    SMS = 'SMS', 'SMS'
    DISCOUNT = 'Discount', 'Discount'
    SAAS_PER_BC = 'SaaS_per_BC', 'SaaS per BC'  # only for InvoicingSummary


class InvoicePaymentSource(StrChoicesEnum):
    OFFLINE = 'O', 'Offline'
    BRAINTREE = 'B', 'Braintree'
    STRIPE = 'S', 'Stripe'
    HOF = 'H', 'HoF'

    @staticmethod
    def online():
        return {
            InvoicePaymentSource.BRAINTREE,
            InvoicePaymentSource.STRIPE,
            InvoicePaymentSource.HOF,
        }

    @staticmethod
    def is_online(payment_source: 'InvoicePaymentSource'):
        return payment_source in InvoicePaymentSource.online()

    @classmethod
    def from_boost_payment_source(cls, transaction_source: TransactionPaymentSource):
        return {
            TransactionPaymentSource.OFFLINE: cls.OFFLINE,
            TransactionPaymentSource.STRIPE: cls.STRIPE,
            TransactionPaymentSource.BRAINTREE: cls.BRAINTREE,
        }.get(transaction_source)

    @classmethod
    def from_billing_payment_processor(cls, payment_processor: 'PaymentProcessorType'):
        from webapps.billing.enums import PaymentProcessorType

        return {
            PaymentProcessorType.STRIPE: cls.STRIPE,
            PaymentProcessorType.BRAINTREE: cls.BRAINTREE,
        }.get(payment_processor)


class InvoicePaymentSourceType(StrChoicesEnum):
    OFFLINE = 'offline', 'Offline'
    ONLINE = 'online', 'Online'


class TransactionStatus(StrEnum):
    SUCCESS = 'Success'
    FAILED = 'Failed'


class TransactionType(StrEnum):
    CHARGE = 'Charge'
    REFUND = 'Refund'
    DISPUTE = 'Dispute'  # NOT SUPPORTED


class NavisionSource(StrEnum):
    BAT = 'Bat'
    BRT = 'Brt'
    STR = 'Str'
    HOF = 'HoF'
    HOFIE = 'HoFIE'

    @classmethod
    def from_invoice_payment_source(cls, source: 'InvoicePaymentSource') -> 'NavisionSource':
        if settings.API_COUNTRY == Country.IE and source == InvoicePaymentSource.HOF:
            return cls.HOFIE
        return {
            InvoicePaymentSource.OFFLINE: cls.BAT,
            InvoicePaymentSource.BRAINTREE: cls.BRT,
            InvoicePaymentSource.STRIPE: cls.STR,
            InvoicePaymentSource.HOF: cls.HOF,
        }.get(source, '')


class InvoicingErrorCategory(StrChoicesEnum):
    BUSINESS = 'business', 'Business'
    BUYER = 'buyer', 'Subscription Buyer'
    MERCHANT = 'merchant', 'Merchant'
    ALREADY_INVOICED = 'invoiced', 'Already Invoiced'
    OTHER = 'other', 'Other'


class InvoicingErrorStatus(StrChoicesEnum):
    CREATED = 'created', 'Created'
    REJECTED = 'rejected', 'Rejected'
    PROCESSED = 'processed', 'Processed'
