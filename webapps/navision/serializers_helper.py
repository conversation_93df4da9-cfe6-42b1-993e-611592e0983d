from typing import Optional

from django.conf import settings
from django.utils.translation import gettext as _
from rest_framework import status

from lib.feature_flag.feature.billing import (
    ForceConfirmBillingInvoiceDataFlag,
    NewInvoicingProcessIEFlag,
)
from lib.feature_flag.feature.navision import NavisionConfirmTaxIdUSInvoiceDetailsFlag
from service.exceptions import ServiceError
from webapps.navision.enums import InvoiceDetailsFormsEnum
from webapps.navision.invoice_details_forms_serializers import (
    MandatoryTaxInvoiceDetailsSerializer,
    OptionalTaxInvoiceDetailsSerializer,
    TaxIfVATRegisteredInvoiceDetailsSerializer,
    EditAndConfirmaInvoiceDetailsSerializer,
    TwoFieldsTaxIdInvoiceDetailsSerializer,
    OneTimeEditOptionalTaxInvoiceDetailsSerializer,
)
from webapps.purchase.models import SubscriptionBuyer


def get_invoice_details_form_serializer(form_id: Optional[str], is_getter=False):
    error_desc = [
        {
            'code': 'invalid_form',
            'description': _('Invalid invoice details form'),
        }
    ]

    match settings.SUPPORTED_INVOICE_FORM:
        case InvoiceDetailsFormsEnum.PATH_1:
            serializer = MandatoryTaxInvoiceDetailsSerializer
        case InvoiceDetailsFormsEnum.PATH_2:
            if ForceConfirmBillingInvoiceDataFlag():
                serializer = EditAndConfirmaInvoiceDetailsSerializer
            else:
                serializer = TaxIfVATRegisteredInvoiceDetailsSerializer
        case InvoiceDetailsFormsEnum.PATH_3:
            serializer = OptionalTaxInvoiceDetailsSerializer
        case InvoiceDetailsFormsEnum.TWO_TAX_ID_FIELDS:
            if NewInvoicingProcessIEFlag():
                serializer = TwoFieldsTaxIdInvoiceDetailsSerializer
            else:
                serializer = TaxIfVATRegisteredInvoiceDetailsSerializer
        case InvoiceDetailsFormsEnum.CONFIRM_OPTIONAL_TAX_ID:
            if NavisionConfirmTaxIdUSInvoiceDetailsFlag():
                serializer = OneTimeEditOptionalTaxInvoiceDetailsSerializer
            else:
                serializer = OptionalTaxInvoiceDetailsSerializer
        case _:
            raise ServiceError(status.HTTP_400_BAD_REQUEST, error_desc)

    if is_getter or form_id == serializer.FORM_ID:
        return serializer

    raise ServiceError(status.HTTP_400_BAD_REQUEST, error_desc)


def get_invoice_details_for_billing_history(buyer: SubscriptionBuyer):
    match settings.SUPPORTED_INVOICE_FORM:
        case InvoiceDetailsFormsEnum.PATH_1:
            serializer = MandatoryTaxInvoiceDetailsSerializer
        case InvoiceDetailsFormsEnum.PATH_2:
            serializer = TaxIfVATRegisteredInvoiceDetailsSerializer
        case InvoiceDetailsFormsEnum.TWO_TAX_ID_FIELDS:
            if NewInvoicingProcessIEFlag():
                serializer = TwoFieldsTaxIdInvoiceDetailsSerializer
            else:
                serializer = TaxIfVATRegisteredInvoiceDetailsSerializer
        case _:
            serializer = OptionalTaxInvoiceDetailsSerializer
    return serializer(instance=buyer).data[
        'data'
    ]  # TODO improvement add separate serializer to get just invoice data
