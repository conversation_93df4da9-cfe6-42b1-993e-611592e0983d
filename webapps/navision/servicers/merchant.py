import json

import grpc

# pylint: disable=no-name-in-module
from booksy_proto_invoicing.grpc.v1.merchant_pb2 import GetMerchantRequest, GetMerchantResponse
from booksy_proto_invoicing.grpc.v1 import merchant_pb2_grpc
from django_socio_grpc.proto_serializers import ProtoSerializer
from rest_framework import serializers

from lib.feature_flag.feature.navision import NavisionGetAndSyncMerchantFlag
from lib.serializers import safe_get
from webapps.business.models import Business
from webapps.navision.tasks import create_or_update_merchant_in_navision


class NavisionMerchantRequestSerializer(ProtoSerializer):
    class Meta:
        proto_class = GetMerchantRequest

    business_id = serializers.IntegerField()
    country_code = serializers.CharField()


class NavisionMerchantResponseSerializer(ProtoSerializer):
    class Meta:
        proto_class = GetMerchantResponse

    merchant_id = serializers.CharField(source='buyer.merchant.merchant_id')
    emails = serializers.SerializerMethodField()
    business_name = serializers.CharField(source='name')
    buyer_name = serializers.CharField(source='buyer.merchant.entity_name')
    is_test_business = serializers.BooleanField()
    invoicing_allowed = serializers.BooleanField(source='buyer.invoicing_allowed')
    is_active = serializers.BooleanField(source='buyer.active')
    is_verified = serializers.BooleanField(source='buyer.is_verified')
    owns_enterprise = serializers.BooleanField(source='buyer.owns_enterprise')
    invoicing_exclusion_reason = serializers.CharField(source='buyer.invoicing_exclusion_reason')
    tax_id = serializers.CharField(source='buyer.tax_id')
    vat_registered = serializers.BooleanField(source='buyer.vat_registered')
    tax_group_name = serializers.CharField(source='buyer.tax_group.name')
    accounting_group = serializers.CharField(source='buyer.merchant.accounting_group')
    address_details = serializers.CharField(source='buyer.merchant.address_details1')
    zip_code = serializers.CharField(source='buyer.merchant.zip_code')
    state = serializers.CharField(source='buyer.merchant.state')
    city = serializers.CharField(source='buyer.merchant.city')

    @staticmethod
    def get_emails(instance):
        if email := instance.invoice_email:
            return email
        emails = instance.buyer.extra_invoice_emails or []
        if buyer_email := instance.buyer.invoice_email:
            emails.append(buyer_email)
        return ', '.join(emails)


class MerchantServiceServicer(merchant_pb2_grpc.MerchantServiceServicer):
    def GetMerchant(self, request, context):
        serializer = NavisionMerchantRequestSerializer(message=request)
        if not serializer.is_valid():
            details = json.dumps(serializer.errors)
            context.abort(code=grpc.StatusCode.NOT_FOUND, details=details)

        business_id = serializer.validated_data['business_id']
        business = Business.objects.filter(id=business_id).select_related('buyer__merchant').first()
        if not business:
            context.abort(code=grpc.StatusCode.NOT_FOUND, details='Business does not exist')
        if not (buyer := safe_get(business, ['buyer'])):
            context.abort(code=grpc.StatusCode.NOT_FOUND, details='Buyer does not exist')
        if NavisionGetAndSyncMerchantFlag() and buyer.merchant and buyer.merchant.synced_at is None:
            create_or_update_merchant_in_navision.run(buyer.merchant.id)
        return NavisionMerchantResponseSerializer(instance=business).message
