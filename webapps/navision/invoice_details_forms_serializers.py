from collections import defaultdict

from django.conf import settings
from django.db import transaction
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers
from rest_framework.fields import empty

from country_config import Country
from drf_api.base_serializers import CustomValidationErrorModelSerializer
from lib.feature_flag.bug import DoNotRemoveEmptyTaxGroupInInvoiceDetailsForm
from lib.fields.tax_number_field import TaxNumberFieldSerializer
from lib.fields.zipcode import Zipcode<PERSON>ield
from lib.serializers import BooksyEmailField, RequiredContextMixin
from webapps.navision.models.invoice_details import InvoiceDetailsBusinessSettings
from webapps.navision.models.settings import NavisionSettings
from webapps.navision.models.tax_rate import TaxGroup
from webapps.navision.tasks.invoice_details_business_settings import invoice_details_confirmed_task
from webapps.navision.utils import available_tax_groups
from webapps.purchase.models import SubscriptionBuyer, InvoiceAddress
from webapps.structure.models import Region


class InvoiceAddressSerializer(CustomValidationErrorModelSerializer):
    class Meta:
        model = InvoiceAddress
        fields = (
            "address_details1",
            "city",
            "zipcode",
        )

    address_details1 = serializers.CharField(
        max_length=100,
    )
    city = serializers.CharField(
        max_length=100,
    )
    zipcode = ZipcodeField(
        label=defaultdict(
            us=_('Zip Code'),
            gb='Postal Code',
            pl=_('Zip Code'),
            es=_('Zip Code'),
            ie='Eircode',
        ),
    )

    @staticmethod
    def _check_zipcode(validated_data):
        if not settings.CHECK_ZIPCODE_IN_REGION_TABLE and 'zipcode' in validated_data:
            validated_data['zipcode_textual'] = validated_data.pop('zipcode')

    @staticmethod
    def _add_state_to_data(validated_data):
        if not validated_data.get('state') and (zipcode := validated_data.get('zipcode')):
            state = zipcode.get_parent_by_type(types=[Region.Type.STATE])
            if state:
                validated_data['state'] = state

    def create(self, validated_data):
        self._check_zipcode(validated_data)
        self._add_state_to_data(validated_data)
        instance = super().create(validated_data)
        return instance

    def update(self, instance, validated_data):
        self._check_zipcode(validated_data)
        self._add_state_to_data(validated_data)
        instance = super().update(instance, validated_data)
        return instance

    @property
    def data(self):
        data = super().data
        data['address_details'] = data.pop('address_details1', '')
        return data

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data['zipcode'] = instance.zipcode_str
        return data


class InvoiceDetailsAddressMethodsMixin:
    def validate_invoice_address(self, data: dict):
        if invoice_address_data := data.pop('invoice_address', None):
            if self.instance and self.instance.invoice_address:
                invoice_address_serializer = InvoiceAddressSerializer(
                    instance=self.instance.invoice_address,
                    data=invoice_address_data,
                    partial=True,
                )
            else:
                invoice_address_serializer = InvoiceAddressSerializer(
                    data=invoice_address_data,
                )

            invoice_address_serializer.is_valid(raise_exception=True)
            self.context['invoice_address_serializer'] = invoice_address_serializer

    def save_invoice_address_obj(self):
        if invoice_address_serializer := self.context.get('invoice_address_serializer'):
            return invoice_address_serializer.save()

    def add_address_to_buyer(self, buyer: SubscriptionBuyer):
        if invoice_address := self.save_invoice_address_obj():
            buyer.invoice_address = invoice_address
            buyer.save(update_fields=['invoice_address'])


class ToRepresentationMixin:
    VAT_REGISTERED_FIELD = 'vat_registered'
    TAX_ID_FIELD = 'tax_id'
    TAX_GROUP_FIELD = 'tax_group'
    NOT_EDITABLE_FIELDS_AFTER_SYNC = (VAT_REGISTERED_FIELD, TAX_ID_FIELD)

    @staticmethod
    def get_tax_groups() -> list:
        tax_group_names = available_tax_groups()

        if default_tax_group := TaxGroup.countrywide_tax_group():
            default_tax_group = default_tax_group.name

        return [
            {
                'label': _(tax_group),
                'value': tax_group,
                'default': default_tax_group == tax_group,
            }
            for tax_group in tax_group_names
        ]

    def prepare_representation_data(self, data: dict) -> dict:
        mx_sent_to_production = (
            self.instance and not self.instance.provider_can_edit_tax_related_fields
        )

        # do not show tax_group field in the form if there is no tax_group or only one group
        tax_groups = self.get_tax_groups()
        if len(tax_groups) < 2:
            data.pop(self.TAX_GROUP_FIELD, None)

        if 'tax_id' in data and mx_sent_to_production and data['tax_id'] is None:
            data.pop('tax_id')

        response = {
            'invoice_details_id': data.pop('id', None),
            'data': data,
            'form': {
                'id': self.FORM_ID,
                'properties': {},
            },
        }

        field_properties = {}
        buyer_editing_disabled = self.instance and not (
            NavisionSettings.get_current_settings().enable_invoice_details_editing
            and self.instance.invoice_details_editable
        )

        for field_name in data:
            is_required = self.fields[field_name].required
            if field_name == self.TAX_ID_FIELD and self.VAT_REGISTERED_FIELD in data:
                is_required = True

            field_editing_disabled = (
                field_name in self.NOT_EDITABLE_FIELDS_AFTER_SYNC and mx_sent_to_production
            )

            field_data = {
                'label': self.fields[field_name].label,
                'required': is_required,
                'editable': not (buyer_editing_disabled or field_editing_disabled),
            }

            if field_name == self.TAX_GROUP_FIELD:
                field_data.update(
                    {
                        'available_values': tax_groups,
                        'required': True,
                    }
                )

            field_properties[field_name] = field_data

        response['form']['properties'] = field_properties

        return response

    @property
    def data(self):
        return self.prepare_representation_data(super().data)


class InvoiceDetailCommonFieldsSerializerMixin(
    ToRepresentationMixin,
    CustomValidationErrorModelSerializer,
    InvoiceDetailsAddressMethodsMixin,
    RequiredContextMixin,
):
    required_context = ('business',)
    default_error_messages = {
        'buyer_sent_to_production': _('This field cannot be edited'),
        'tax_region_not_supported': _('Tax regions are not supported'),
        'tax_id_not_supported': _('Tax ID field is not supported'),
    }

    class Meta:
        model = SubscriptionBuyer
        fields = (
            'id',
            'entity_name',
            'address_details',
            'city',
            'zipcode',
            'tax_group',
            'invoice_email',
        )

    entity_name = serializers.CharField(
        max_length=255,
        label=_('First Name & Last name OR Company Name'),
    )
    address_details = serializers.CharField(
        max_length=100,
        source='invoice_address.address_details1',
        label=_('Address'),
    )
    city = serializers.CharField(
        max_length=100,
        source='invoice_address.city',
        label=_('City'),
    )
    zipcode = serializers.CharField(
        source='invoice_address.zipcode',
        label=_('Zip Code'),
    )
    tax_group = serializers.SlugRelatedField(
        required=False,
        allow_null=True,
        queryset=TaxGroup.objects.all(),
        slug_field='name',
        label=_('Registered tax territory'),
    )
    invoice_email = BooksyEmailField(
        required=True,
        label=_('Invoice E-mail Address'),
    )

    def _validate_sync_with_prod(self):
        if self.instance and not self.instance.provider_can_edit_tax_related_fields:
            self.fail('buyer_sent_to_production')

    def validate_tax_group(self, value: str) -> str:
        if value and not settings.NAVISION_USE_TAX_GROUPS:
            self.fail('tax_region_not_supported')
        return value

    def validate(self, attrs: dict) -> dict:
        data = super().validate(attrs)
        self.validate_invoice_address(data)
        return data

    @transaction.atomic
    def create(self, validated_data: dict) -> SubscriptionBuyer:
        instance = super().create(validated_data)
        instance.businesses.add(self.context.get('business'), bulk=False)
        self.add_address_to_buyer(instance)
        return instance

    @transaction.atomic
    def update(self, instance: SubscriptionBuyer, validated_data: dict) -> SubscriptionBuyer:
        instance = super().update(instance, validated_data)
        self.add_address_to_buyer(instance)
        return instance

    def to_representation(self, instance: SubscriptionBuyer) -> dict:
        data = super().to_representation(instance)
        data.update(InvoiceAddressSerializer(instance.invoice_address).data)
        if not data.get('tax_group'):
            if not DoNotRemoveEmptyTaxGroupInInvoiceDetailsForm():
                data.pop('tax_group', None)
        return data


class VatRegisteredMixin:
    def __init__(self, instance=None, data=empty, **kwargs):
        if data is not empty and data.get('vat_registered'):
            self.fields['tax_id'].required = True
            self.fields['tax_id'].allow_null = False
            self.fields['tax_id'].allow_blank = False
        if settings.INVOICE_DETAILS_TAX_ID_PREFIX is not None:
            if not getattr(instance, 'tax_id', None):
                self.fields['tax_id'].initial = settings.INVOICE_DETAILS_TAX_ID_PREFIX
        super().__init__(instance, data, **kwargs)


class ConfirmationSerializerMixin:
    def create(self, validated_data: dict) -> SubscriptionBuyer:
        is_confirmed = validated_data.pop('is_confirmed', None)
        instance = super().create(validated_data)
        if is_confirmed:
            invoice_details_confirmed_task.delay(business_id=self.context['business'].id)
        return instance

    def update(self, instance: SubscriptionBuyer, validated_data: dict) -> SubscriptionBuyer:
        is_confirmed = validated_data.pop('is_confirmed', None)
        instance = super().update(instance, validated_data)
        if is_confirmed:
            invoice_details_confirmed_task.delay(business_id=self.context['business'].id)
        return instance

    def prepare_representation_data(self, data: dict) -> dict:
        representation = super().prepare_representation_data(data)
        representation['data']['is_confirmed'] = (
            InvoiceDetailsBusinessSettings.has_confirmed_invoice_details(
                self.context['business'].id
            )
        )
        representation['form']['properties'].pop('is_confirmed', None)
        return representation


class MandatoryTaxInvoiceDetailsSerializer(InvoiceDetailCommonFieldsSerializerMixin):
    FORM_ID = 'MandatoryTaxID'

    class Meta(InvoiceDetailCommonFieldsSerializerMixin.Meta):
        fields = ('tax_id',) + InvoiceDetailCommonFieldsSerializerMixin.Meta.fields

    tax_id = TaxNumberFieldSerializer(
        max_length=20,
        required=True,
        allow_null=False,
        allow_blank=False,
        label=_('TAX ID (Employer Identification Number)'),
    )

    def validate_tax_id(self, value: str) -> str:
        self._validate_sync_with_prod()
        return value


class TaxIfVATRegisteredInvoiceDetailsSerializer(
    VatRegisteredMixin,
    InvoiceDetailCommonFieldsSerializerMixin,
):
    FORM_ID = 'TaxIDIfVatRegistered'

    class Meta(InvoiceDetailCommonFieldsSerializerMixin.Meta):
        fields = ('vat_registered', 'tax_id') + InvoiceDetailCommonFieldsSerializerMixin.Meta.fields

    vat_registered = serializers.BooleanField(
        initial=True,
        required=True,
        label=_("I'm registered for VAT"),
    )
    tax_id = TaxNumberFieldSerializer(
        max_length=20,
        required=False,
        allow_null=True,
        allow_blank=True,
        label=_('TAX ID (Employer Identification Number)'),
    )

    def validate(self, attrs: dict) -> dict:
        if attrs.get('vat_registered') is False:
            attrs['tax_id'] = None  # if vat_registered is False, tax_id should be empty
        return super().validate(attrs)

    def validate_vat_registered(self, value: str) -> str:
        self._validate_sync_with_prod()
        return value

    def validate_tax_id(self, value: str) -> str:
        self._validate_sync_with_prod()
        return value


class OptionalTaxInvoiceDetailsSerializer(InvoiceDetailCommonFieldsSerializerMixin):
    FORM_ID = 'OptionalTaxID'

    class Meta(InvoiceDetailCommonFieldsSerializerMixin.Meta):
        fields = ('tax_id',) + InvoiceDetailCommonFieldsSerializerMixin.Meta.fields

    tax_id = TaxNumberFieldSerializer(
        max_length=20,
        required=False,
        allow_null=True,
        allow_blank=True,
        label=(
            _('Federal Tax ID - optional')
            if settings.API_COUNTRY == Country.US
            else _('TAX ID (Employer Identification Number) - optional')
        ),
    )

    def validate_tax_id(self, value: str) -> str:
        self._validate_sync_with_prod()
        return value


class OneTimeEditOptionalTaxInvoiceDetailsSerializer(
    OptionalTaxInvoiceDetailsSerializer,
):
    FORM_ID = 'OneTimeEditOptionalTaxID'

    def validate_tax_id(self, value: str) -> str:
        # Skip sync validation
        return value

    def create(self, validated_data: dict) -> SubscriptionBuyer:
        instance = super().create(validated_data)
        invoice_details_confirmed_task.delay(business_id=self.context['business'].id)
        return instance

    def update(self, instance: SubscriptionBuyer, validated_data: dict) -> SubscriptionBuyer:
        instance = super().update(instance, validated_data)
        invoice_details_confirmed_task.delay(business_id=self.context['business'].id)
        return instance

    @property
    def tax_id_editable(self) -> bool:
        if InvoiceDetailsBusinessSettings.has_confirmed_invoice_details(
            self.context['business'].id
        ):
            return False
        return True

    def prepare_representation_data(self, data: dict) -> dict:
        representation = super().prepare_representation_data(data)
        representation['form']['properties'][self.TAX_ID_FIELD]['editable'] = self.tax_id_editable
        return representation


class EditAndConfirmaInvoiceDetailsSerializer(
    VatRegisteredMixin,
    ConfirmationSerializerMixin,
    InvoiceDetailCommonFieldsSerializerMixin,
):
    FORM_ID = 'EditAndConfirm'

    class Meta(InvoiceDetailCommonFieldsSerializerMixin.Meta):
        fields = (
            'vat_registered',
            'tax_id',
            'is_confirmed',
        ) + InvoiceDetailCommonFieldsSerializerMixin.Meta.fields

    is_confirmed = serializers.BooleanField(
        write_only=True,
    )

    vat_registered = serializers.BooleanField(
        initial=True,
        required=True,
        label=_("I'm registered for VAT"),
    )

    tax_id = TaxNumberFieldSerializer(
        max_length=20,
        required=False,
        allow_null=True,
        allow_blank=True,
        label=(
            _('VAT number')
            if settings.API_COUNTRY == Country.GB
            else _('TAX ID (Employer Identification Number)')
        ),
    )

    def validate(self, attrs: dict) -> dict:
        if attrs.get('vat_registered') is False:
            attrs['tax_id'] = None  # if vat_registered is False, tax_id should be empty
        return super().validate(attrs)


class TwoFieldsTaxIdInvoiceDetailsSerializer(
    VatRegisteredMixin,
    ConfirmationSerializerMixin,
    InvoiceDetailCommonFieldsSerializerMixin,
):
    FORM_ID = 'TwoTaxIdFields'

    class Meta(InvoiceDetailCommonFieldsSerializerMixin.Meta):
        fields = (
            'vat_registered',
            'tax_id',
            'tax_id_non_vat_registered',
            'is_confirmed',
        ) + InvoiceDetailCommonFieldsSerializerMixin.Meta.fields

    is_confirmed = serializers.BooleanField(
        write_only=True,
    )

    vat_registered = serializers.BooleanField(
        initial=True,
        required=True,
        label=_("I'm registered for VAT"),
    )

    tax_id = TaxNumberFieldSerializer(
        max_length=20,
        required=False,
        allow_null=True,
        allow_blank=True,
        label=_('VAT number'),
    )

    tax_id_non_vat_registered = serializers.CharField(
        max_length=20,
        required=False,
        allow_null=True,
        allow_blank=True,
        label=_('Tax Registration Number (TRN)'),
    )

    def to_internal_value(self, data):
        if data.get('vat_registered') is False:
            data['tax_id'] = data.get('tax_id_non_vat_registered')
        data.pop('tax_id_non_vat_registered', None)
        return super().to_internal_value(data)

    def prepare_representation_data(self, data: dict) -> dict:
        if data.get('vat_registered') is False:
            data['tax_id_non_vat_registered'] = data['tax_id']
            data['tax_id'] = None
        representation = super().prepare_representation_data(data)
        return representation
