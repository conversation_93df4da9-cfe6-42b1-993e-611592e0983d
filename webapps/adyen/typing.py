from typing import NotRequired
from typing_extensions import TypedDict


class DeviceDataDict(TypedDict):
    """
    Typed dict structure holding information about device used for performing
    Adyen operations.

    Providing device fingerprint and phone number is a great way of detecting
    fraudulent operations, not authorized by cardholder.
    """

    fingerprint: str
    phone_number: str
    user_agent: str
    ip: NotRequired[str]


class AmountDict(TypedDict):
    """
    Dict for providing transaction amount in Adyen request.
    """

    value: int
    currency: str
