#!/usr/bin/env python
from math import asin, cos, sin, sqrt, radians

from django import forms
from django.contrib import admin, messages
from django.contrib.admin.widgets import ManyToManyRawIdWidget
from django.urls import re_path as url
from django.urls.base import reverse
from django.utils.html import format_html
from django.shortcuts import redirect
from rest_framework import serializers

from lib.admin_helpers import BaseModelAdmin, NoDelMixin
from lib.geocoding.base import GeocodingError
from lib.geocoding.here_maps import Geocoder
from lib.serializers import zip_code_validator
from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin
from webapps.user.groups import GroupNameV2
from .models import Region, RegionToCategories


class AddRegionAdminForm(forms.ModelForm):
    DISTANCE_TOLERANCE = 150  # KM

    immediate_parents = forms.ModelMultipleChoiceField(
        Region.objects.exclude(type=Region.Type.ZIP),
        widget=ManyToManyRawIdWidget(
            Region.immediate_parents.field.remote_field,
            admin.site,
        ),
        error_messages={
            'invalid_choice': '%(value)s is zipcode and thous it should be lowest in hierarchy'
        },
    )

    class Meta:
        model = Region
        fields = '__all__'
        widgets = {'name': forms.TextInput()}

    def clean_name(self):
        zipcode = self.cleaned_data['name']

        try:
            zip_code_validator(zipcode)
        except serializers.ValidationError as validation_error:
            self.add_error('name', validation_error.detail)

        if Region.objects.find_zipcode(zipcode).exists():
            self.add_error('name', 'Zipcode already exists')

        return zipcode

    def _closest_parent(self):
        if parents := self.cleaned_data.get('immediate_parents'):
            return list(Region.sort_regions(parents))[-1]

    def _distance(self, region1, lat, long):
        if region1.latitude is None or region1.longitude is None:
            self.add_error(
                'immediate_parents',
                f'Region (ID: {region1.id}) does not have geo info assigned',
            )
            return

        approx_earth_radius = 6356.752  # KM
        source_lat, source_long = radians(region1.latitude), radians(region1.longitude)
        lat, long = radians(lat), radians(long)

        distance = (
            2
            * approx_earth_radius
            * asin(
                sqrt(
                    sin((lat - source_lat) / 2) ** 2
                    + cos(source_lat) * cos(lat) * sin(long - source_long) ** 2
                )
            )
        )

        return distance

    def clean(self):
        cleaned_data = super().clean()

        postal_code_name = cleaned_data['name']

        try:
            here_maps_data = Geocoder.geocode(zipcode=postal_code_name)
            lat, long = here_maps_data.get('latitude'), here_maps_data.get('longitude')

            if not (lat and long):
                self.add_error('name', 'Here Maps data is incomplete for this zipcode')
            else:
                cleaned_data['latitude'] = lat
                cleaned_data['longitude'] = long

                if parents := cleaned_data.get('immediate_parents'):
                    parent_distances = [
                        (parent, self._distance(parent, lat, long)) for parent in parents
                    ]

                    for parent, distance in parent_distances:
                        if distance and distance > self.DISTANCE_TOLERANCE:
                            self.add_error(
                                'immediate_parents',
                                f'{parent.name} is {distance}km away from {postal_code_name} '
                                f'when maximal accepted distance is {self.DISTANCE_TOLERANCE}km',
                            )

            cleaned_data['city'] = here_maps_data.get('city')
        except GeocodingError:
            self.add_error('name', f'Haven\'t found geocoding for {postal_code_name}')

        return cleaned_data

    def save(self, commit=True):
        self.instance.type = Region.Type.ZIP
        self.instance.latitude = self.cleaned_data['latitude']
        self.instance.longitude = self.cleaned_data['longitude']

        parents = list(Region.sort_regions(self.cleaned_data['immediate_parents']))
        self.instance.time_zone_name = parents[-1].time_zone_name

        return super().save(commit=commit)

    def _save_m2m(self):
        immediate_parents = self.cleaned_data.get('immediate_parents')
        self.instance.immediate_parents.set(immediate_parents)

        return super()._save_m2m()


class ChangeRegionSuperAdminForm(forms.ModelForm):
    immediate_parents = forms.ModelMultipleChoiceField(
        Region.objects.exclude(type=Region.Type.ZIP),
        widget=ManyToManyRawIdWidget(
            Region.immediate_parents.field.remote_field,
            admin.site,
        ),
    )

    class Meta:
        model = Region
        fields = (
            'abbrev',
            'boost_phrase',
            'type',
            'longitude',
            'latitude',
            'time_zone_name',
            'show_county',
            'immediate_parents',
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        if self.instance:
            self.fields['immediate_parents'].initial = self.instance.immediate_parents.all()

    def _save_m2m(self):
        self.instance.immediate_parents.set(self.cleaned_data['immediate_parents'])
        return super()._save_m2m()


class ChangeRegionAdminForm(forms.ModelForm):
    class Meta:
        model = Region
        fields = (
            'boost_phrase',
            'show_county',
        )


class RegionAdmin(NoDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = ()
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    list_display = ['id', 'name', 'full_name', 'type', 'parent_names', 'immediate_child_names']
    list_filter = ['type', 'time_zone_name']
    search_fields = ['id', 'name']
    fields = (
        'name',
        'abbrev',
        'boost_phrase',
        'type',
        'longitude',
        'latitude',
        'time_zone_name',
        'show_county',
        'immediate_parents',
        'immediate_child_names',
    )
    readonly_fields = list(
        set(fields) - {'boost_phrase', 'show_county', 'name', 'immediate_parents'}
    )

    change_form_template = 'admin/change_forms/change_form__region.html'

    def add_view(self, request, form_url='', extra_context=None):
        self.form = AddRegionAdminForm
        self.fields = (
            'name',
            'boost_phrase',
            'show_county',
            'immediate_parents',
        )
        self.readonly_fields = list(
            set(self.fields) - {'boost_phrase', 'show_county', 'name', 'immediate_parents'}
        )

        return super().add_view(request, form_url=form_url, extra_context=extra_context)

    def change_view(self, request, object_id, form_url='', extra_context=None):
        self.fields = [
            'name',
            'abbrev',
            'boost_phrase',
            'type',
            'longitude',
            'latitude',
            'time_zone_name',
            'show_county',
            'immediate_child_names',
        ]
        if self.has_add_permission(request=request):
            self.form = ChangeRegionSuperAdminForm
            self.fields.append('immediate_parents')
            self.readonly_fields = (
                'name',
                'parent_names',
                'immediate_child_names',
            )
        else:
            self.form = ChangeRegionAdminForm
            self.fields.append('parent_names')
            self.readonly_fields = list(
                set(self.fields)
                - {
                    'boost_phrase',
                    'show_county',
                }
            )

        return super().change_view(
            request, object_id, form_url=form_url, extra_context=extra_context
        )

    def get_search_results(self, request, queryset, search_term):
        queryset, may_have_duplicates = super().get_search_results(
            request,
            queryset,
            search_term,
        )

        if request.GET.get('field_name') == 'zipcode':
            queryset = queryset.filter(type=Region.Type.ZIP)
        elif request.GET.get('field_name') == 'state':
            queryset = queryset.filter(type=Region.Type.STATE)

        return queryset.order_by('name'), may_have_duplicates

    def parent_names(self, obj):
        return format_html(
            ', '.join(self._get_admin_link(r) for r in Region.sort_regions(obj.get_parents()))
        )

    def immediate_child_names(self, obj):
        return format_html(', '.join(self._get_admin_link(r) for r in obj.immediate_children.all()))

    @staticmethod
    def _get_admin_link(region):
        """Return link to change view for given region."""
        return format_html(
            '<a href="{}" class="viewsitelink">{}</a>',
            reverse('admin:structure_region_change', args=(region.id,)),
            region.name,
        )

    @staticmethod
    def get_reindex_region(obj=None):
        if obj is None:
            return ''

        return format_html(
            """
            <a href="{}" class="btn ">
                Schedule Region Reindex - it may take a few minutes so be patient
            </a>
            """,
            reverse('admin:reindex_region', args=(obj.id,)),
        )

    # pylint: disable=method-hidden
    def get_form(self, request, obj=None, change=False, **kwargs):
        self.reindex_region = self.get_reindex_region(obj)

        form = super().get_form(request, obj, **kwargs)
        return form

    @staticmethod
    def reindex_region(request, region_id=None):  # pylint disable=method-hidden
        from webapps.structure.tasks import region_index_task  # pylint: disable=unused-import

        messages.success(
            request,
            'Region reindex has been scheduled - it may take a few minutes',
        )
        region_index_task.delay([region_id])

        return redirect(
            reverse(
                'admin:structure_region_change',
                args=(region_id,),
            )
        )

    def get_urls(self):
        urls = super().get_urls()
        additional_urls = [
            url(
                r'^(?P<region_id>\d+)/reindex_region/$',
                self.admin_site.admin_view(self.reindex_region),
                name='reindex_region',
            ),
        ]

        return additional_urls + urls

    def get_extra_region_actions(self, request, obj) -> dict:
        # pylint: disable=no-value-for-parameter
        links = {
            'reindex_region': self.get_reindex_region(obj),
        }
        return links


admin.site.register(Region, RegionAdmin)


class RegionToCategoriesAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = ()
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    list_display = ['id', 'region_id', 'categories']
    list_filter = ['id', 'region_id']
    search_fields = ['region_id']


admin.site.register(RegionToCategories, RegionToCategoriesAdmin)
