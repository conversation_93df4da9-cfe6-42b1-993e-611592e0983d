import time
import traceback
import logging

from dacite import from_dict
from django.conf import settings
from django.db.models.signals import post_save
from django.dispatch import receiver

from lib.celery_tools import post_transaction_task
from lib.events import (
    event_receiver,
    lazy_event_receiver,
)
from lib.feature_flag.feature.payment import (
    ExtendedWaitForPaymentRowTransactionFlag,
    BasketPaymentCompletedAnalyticsFlag,
)
from lib.payment_gateway.enums import (
    PaymentStatus,
    WalletOwnerType,
)
from lib.payments.enums import PaymentError, PaymentProviderCode
from lib.point_of_sale.entities import (
    BasketPaymentEntity,
    CancellationFeeAuthEntity,
)
from lib.point_of_sale.enums import (
    BasketPaymentSource,
    BasketPaymentStatus,
    BasketPaymentType,
    CancellationFeeAuthStatus,
    PaymentMethodType,
)
from lib.point_of_sale.events import (
    basket_payment_added_event,
    basket_payment_amount_updated_event,
    basket_payment_details_updated_event,
    cancellation_fee_auth_status_updated_event,
)
from lib.pos.utils import txn_refactor_stage2_enabled
from lib.serializers import safe_get
from lib.tools import major_unit, sget_v2
from webapps.adyen.helpers import (
    cancel_appointment_after_failed_payment,
    confirm_appointment,
)
from webapps.booking.enums import WhoMakesChange
from webapps.payment_gateway.models import (
    BalanceTransaction,
    Wallet,
)
from webapps.payment_providers.models import (
    Payment,
    TokenizedPaymentMethod,
)
from webapps.pos.calculations import recalculate_tips
from webapps.pos.enums import (
    CARD_TYPE__OTHER,
    PAYMENT_METHOD_TYPE__CREDIT_CARD,
    PaymentProviderEnum,
    PaymentTypeEnum,
    receipt_status,
)
from webapps.pos.events import (
    basket_payment_completed_event,
    deposit_charged_event,
    payment_completed_event,
)
from webapps.pos.models import (
    PaymentMethod,
    PaymentRow,
    PaymentRowChange,
    Transaction,
)
from webapps.pos.notifications import (
    CancellationFeeChargedNotification,
    MobilePaymentNotification,
)
from webapps.pos.services import PaymentRowService
from webapps.pos.tip_calculations import SimpleTip
from webapps.stripe_integration.provider import StripeProvider
from webapps.user.tools import get_system_user

logger_basket_payment_updated = logging.getLogger(
    'booksy.basket_payment_details_updated_event_handler'
)

# pylint: disable=unused-argument
if settings.POPUP_NOTIFICATIONS_ENABLED:

    @lazy_event_receiver(deposit_charged_event)
    def deposit_charged_handler(instance, **kwargs):
        """Note that this event does not imply that payment was successful"""
        transaction: Transaction = instance
        if transaction.latest_receipt.status_code == receipt_status.DEPOSIT_CHARGE_SUCCESS:
            CancellationFeeChargedNotification(transaction).send()

    @lazy_event_receiver(payment_completed_event)
    def payment_completed_handler(instance, **kwargs):
        """Note that this event does not imply that payment was successful"""
        transaction: Transaction = instance
        if (
            transaction.appointment
            and transaction.latest_receipt.status_code == receipt_status.PAYMENT_SUCCESS
        ):
            MobilePaymentNotification(transaction).send()


def run_basket_payment_completed_analytics(basket_payment: BasketPaymentEntity, business_id: int):
    if (
        basket_payment.status == BasketPaymentStatus.SUCCESS
        and basket_payment.type == BasketPaymentType.PAYMENT
        and BasketPaymentCompletedAnalyticsFlag()
    ):
        basket_payment_completed_event.send(
            _instance=None, basket_payment_id=basket_payment.id, business_id=business_id
        )


@event_receiver(basket_payment_added_event)
def basket_payment_added_event_handler(basket_payment: dict, **kwargs):
    """
    Updates PaymentRow with created BasketPayment. Used in reversed flow -> triggered by webhook.
    """
    basket_payment = from_dict(
        data_class=BasketPaymentEntity,
        data=basket_payment,
    )

    txn = Transaction.objects.filter(basket_id=basket_payment.basket_id).last()

    if not txn:
        return

    # There should be only 1 found
    payment_rows = txn.latest_receipt.payment_rows.filter(
        basket_payment_id=None,
    )

    if len(payment_rows) == 1:
        # Flow for phase 1
        payment_rows[0].basket_payment_id = basket_payment.id
        payment_rows[0].save(update_fields=['basket_payment_id'])
    elif not payment_rows:
        # FOr chargeback reversed and second chargeback it won't be last state
        original_payment_row = PaymentRow.objects.filter(
            receipt__transaction=txn, basket_payment_id=basket_payment.parent_basket_payment_id
        ).first()

        # HACKS >>>>>>>>>>
        # now we need to get last child:
        while True:
            new_row = original_payment_row.children.first()
            if not new_row:
                break

            original_payment_row = new_row

        # <<<<<<<<<<<<<<
        payment_splits = None
        if basket_payment.type == BasketPaymentType.CHARGEBACK:
            new_status = receipt_status.CHARGEBACK
            if basket_payment.payment_provider_code == PaymentProviderCode.STRIPE:
                payment_splits = StripeProvider.calculate_chargeback_splits(original_payment_row)
        elif basket_payment.type == BasketPaymentType.CHARGEBACK_REVERSED:
            new_status = receipt_status.CHARGEBACK_REVERSED
            if basket_payment.payment_provider_code == PaymentProviderCode.STRIPE:
                payment_splits = StripeProvider.calculate_splits(original_payment_row)
        elif basket_payment.type == BasketPaymentType.SECOND_CHARGEBACK:
            new_status = receipt_status.SECOND_CHARGEBACK
        else:
            return

        dispute_payment_row = original_payment_row.update_status(
            status=new_status,
            provider=PaymentRowService.map_payment_provider_code_into_payment_provider_enum(
                basket_payment.payment_provider_code
            ),
            settled=True,
            payment_splits=payment_splits,
            log_action=PaymentRowChange.SINGLE_ROW_UPDATE,
            log_note='basket_payment_added_event_handler handle chargeback',
        )

        dispute_payment_row.basket_payment_id = basket_payment.id
        dispute_payment_row.save(update_fields=['basket_payment_id'])

    run_basket_payment_completed_analytics(basket_payment, business_id=txn.pos.business_id)


@event_receiver(basket_payment_amount_updated_event)
def basket_payment_amount_updated_event_handler(basket_payment: dict, **kwargs):
    """
    Updates PaymentRow, Tip, TipRows and creates BasketTips.
    Used in reversed fow -> triggered by webhook by Stripe Terminal.
    """
    basket_payment = from_dict(
        data_class=BasketPaymentEntity,
        data=basket_payment,
    )

    txn = Transaction.objects.filter(
        basket_id=basket_payment.basket_id,
    ).last()

    if not txn or not txn_refactor_stage2_enabled(txn):
        return

    payment_row = PaymentRow.objects.filter(
        basket_payment_id=basket_payment.id,
    ).last()

    summed_tip_amount = major_unit(basket_payment.metadata['summed_tip_amount'])

    data = {
        'tip': {
            'rate': summed_tip_amount,
            'type': SimpleTip.TIP_TYPE__HAND,
        },
    }
    recalculate_tips(
        txn,
        request_data=data,
        update_instance=True,
        force_percent=True,
    )
    txn.save(update_fields=['total'])

    if payment_row.provider == PaymentProviderEnum.STRIPE_PROVIDER:
        splits = StripeProvider.calculate_splits(payment_row, txn.pos.stripe_account)
        payment_row.payment_splits = splits

    payment_row.amount = major_unit(basket_payment.amount)
    payment_row.tip_amount = summed_tip_amount
    payment_row.save(update_fields=['amount', 'tip_amount', 'payment_splits'])

    txn.tip.basket_tip_id = basket_payment.metadata['basket_tip_id']
    txn.tip.save(update_fields=['basket_tip_id'])


@event_receiver(basket_payment_details_updated_event)
def basket_payment_details_updated_event_handler(
    basket_payment: dict, **kwargs
):  # pylint: disable=too-many-branches, too-many-return-statements, too-many-statements
    _basket_payment = basket_payment
    basket_payment = from_dict(
        data_class=BasketPaymentEntity,
        data=basket_payment,
    )

    payment_row = PaymentRow.objects.filter(
        basket_payment_id=basket_payment.id,
    ).last()

    if not payment_row:
        if basket_payment.status == BasketPaymentStatus.SUCCESS:
            logger_basket_payment_updated.warning(
                'no_payment_row_for_success_basket_payment: %s, ',
                str(basket_payment.id),
            )
            basket_payment_details_updated_event_handler_task.apply_async(
                args=[_basket_payment],
            )
        return

    return _basket_payment_details_updated_event_handler(
        payment_row=payment_row,
        basket_payment=basket_payment,
    )


@post_transaction_task(time_limit=200, soft_time_limit=180)
def basket_payment_details_updated_event_handler_task(basket_payment: dict, **kwargs):
    type_mapping = {
        "payment_method": PaymentMethodType,
        "status": BasketPaymentStatus,
        "type": BasketPaymentType,
        "source": BasketPaymentSource,
        "payment_provider_code": PaymentProviderCode,
        "error_code": PaymentError,
    }

    for field, enum_type in type_mapping.items():
        if isinstance(basket_payment.get(field), str):
            basket_payment[field] = enum_type(basket_payment[field])

    basket_payment = from_dict(
        data_class=BasketPaymentEntity,
        data=basket_payment,
    )

    payment_row = PaymentRow.objects.filter(
        basket_payment_id=basket_payment.id,
    ).last()

    max_waiting_time: int = ExtendedWaitForPaymentRowTransactionFlag()
    if (
        not payment_row
        and max_waiting_time
        and basket_payment.status == BasketPaymentStatus.SUCCESS
    ):
        t1 = time.time()
        while time.time() < t1 + max_waiting_time:
            payment_row = PaymentRow.objects.filter(
                basket_payment_id=basket_payment.id,
            ).last()

            if payment_row:
                t2 = time.time()
                logger_basket_payment_updated.warning(
                    'basket_payment_details_updated_event_handler_task success - payment row appeared after %s for basket payment id: %s',  # pylint: disable=line-too-long
                    str(t2 - t1),
                    str(basket_payment.id),
                )
                break
            time.sleep(1)

    if not payment_row:
        if basket_payment.status == BasketPaymentStatus.SUCCESS:
            logger_basket_payment_updated.warning(
                'basket_payment_details_updated_event_handler_task no_payment_row_for_success_basket_payment: %s',  # pylint: disable=line-too-long
                str(basket_payment.id),
            )
        return

    return _basket_payment_details_updated_event_handler(
        payment_row=payment_row,
        basket_payment=basket_payment,
    )


def _basket_payment_details_updated_event_handler(  # pylint: disable=too-many-return-statements, too-many-branches, too-many-statements
    payment_row: PaymentRow,
    basket_payment: BasketPaymentEntity,
):

    txn = payment_row.receipt.transaction
    if not txn_refactor_stage2_enabled(txn):
        return

    balance_transaction = BalanceTransaction.objects.filter(
        id=basket_payment.balance_transaction_id
    ).last()

    if basket_payment.type == BasketPaymentType.PAYMENT:
        if txn.transaction_type == Transaction.TRANSACTION_TYPE__CANCELLATION_FEE:
            new_status = {
                BasketPaymentStatus.PENDING: None,
                BasketPaymentStatus.SUCCESS: receipt_status.DEPOSIT_CHARGE_SUCCESS,
                BasketPaymentStatus.FAILED: receipt_status.DEPOSIT_CHARGE_FAILED,
                BasketPaymentStatus.CANCELED: receipt_status.DEPOSIT_CHARGE_CANCELED,
            }[basket_payment.status]

        elif payment_row.payment_type.code == PaymentTypeEnum.PREPAYMENT:
            if not balance_transaction:
                return

            if basket_payment.status == BasketPaymentStatus.CANCELED:
                # Status change of BasketPayment is triggered before change of BalanceTransaction
                new_status = receipt_status.PAYMENT_CANCELED
            if (
                balance_transaction.payment_method == PaymentMethodType.KEYED_IN_PAYMENT
                and balance_transaction.payment.status == PaymentStatus.AUTHORIZATION_FAILED
            ):
                # KEYED_IN_PAYMENT fails authorization and capture at the same time
                new_status = receipt_status.PREPAYMENT_FAILED
            else:
                new_status = {
                    PaymentStatus.SENT_FOR_AUTHORIZATION: None,
                    PaymentStatus.ACTION_REQUIRED: receipt_status.CALL_FOR_PREPAYMENT_3DS,
                    PaymentStatus.AUTHORIZED: receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS,
                    PaymentStatus.AUTHORIZATION_FAILED: receipt_status.PREPAYMENT_AUTHORISATION_FAILED,  # pylint: disable=line-too-long
                    PaymentStatus.SENT_FOR_CAPTURE: None,
                    PaymentStatus.CAPTURED: receipt_status.PREPAYMENT_SUCCESS,
                    PaymentStatus.CAPTURE_FAILED: receipt_status.PREPAYMENT_FAILED,
                    PaymentStatus.CANCELED: receipt_status.PAYMENT_CANCELED,
                }[balance_transaction.payment.status]
                if basket_payment.payment_method == PaymentMethodType.BLIK:
                    if balance_transaction.payment.status == PaymentStatus.ACTION_REQUIRED:
                        new_status = receipt_status.CALL_FOR_PREPAYMENT

        elif payment_row.payment_type.code == PaymentTypeEnum.BOOKSY_PAY:
            if not balance_transaction:
                return

            if basket_payment.status == BasketPaymentStatus.CANCELED:
                # Status change of BasketPayment is triggered before change of BalanceTransaction
                new_status = receipt_status.PAYMENT_CANCELED
            else:
                # Note:
                # In the 3DS flow, the initial internal payment status is set to
                # `PaymentStatus.ACTION_REQUIRED`. Because of that, when a transaction fails,
                # the Stripe's `payment_intent.payment_failed` event is mapped to the internal
                # `PaymentStatus.AUTHORIZATION_FAILED` (see `payment_intent__failed` method in
                # StripeWebhookEvent). Therefore, this status needs to be mapped accordingly here,
                # even though Booksy Pay doesn't utilize the flow with the semi-auto booking mode.
                new_status = {
                    PaymentStatus.SENT_FOR_AUTHORIZATION: None,
                    PaymentStatus.ACTION_REQUIRED: (
                        receipt_status.CALL_FOR_BOOKSY_PAY
                        if basket_payment.payment_method
                        in [PaymentMethodType.BLIK, PaymentMethodType.KLARNA]
                        else receipt_status.CALL_FOR_BOOKSY_PAY_3DS
                    ),
                    PaymentStatus.SENT_FOR_CAPTURE: None,
                    PaymentStatus.AUTHORIZATION_FAILED: receipt_status.BOOKSY_PAY_FAILED,
                    PaymentStatus.CAPTURED: receipt_status.BOOKSY_PAY_SUCCESS,
                    PaymentStatus.CAPTURE_FAILED: receipt_status.BOOKSY_PAY_FAILED,
                    PaymentStatus.CANCELED: receipt_status.PAYMENT_CANCELED,
                    #
                }[balance_transaction.payment.status]

        else:
            new_status = {
                BasketPaymentStatus.PENDING: None,
                BasketPaymentStatus.ACTION_REQUIRED: receipt_status.CALL_FOR_PAYMENT_3DS,
                BasketPaymentStatus.SUCCESS: receipt_status.PAYMENT_SUCCESS,
                BasketPaymentStatus.CANCELED: receipt_status.PAYMENT_CANCELED,
                BasketPaymentStatus.FAILED: receipt_status.PAYMENT_FAILED,
            }[basket_payment.status]
            if basket_payment.payment_method == PaymentMethodType.BLIK:
                if (
                    sget_v2(balance_transaction, ['payment', 'status'])
                    == PaymentStatus.ACTION_REQUIRED
                ):  # pylint: disable=line-too-long
                    new_status = receipt_status.CALL_FOR_PAYMENT
    elif basket_payment.type == BasketPaymentType.REFUND:
        payment_row.process_refund(success=basket_payment.status == BasketPaymentStatus.SUCCESS)
        return
    elif basket_payment.type == BasketPaymentType.CHARGEBACK:
        new_status = receipt_status.CHARGEBACK
    elif basket_payment.type == BasketPaymentType.CHARGEBACK_REVERSED:
        new_status = receipt_status.CHARGEBACK_REVERSED
    elif basket_payment.type == BasketPaymentType.SECOND_CHARGEBACK:
        new_status = receipt_status.SECOND_CHARGEBACK
    else:
        return

    if new_status == receipt_status.PAYMENT_CANCELED:
        payment_row.receipt.transaction.update_payment_rows(
            receipt_status.PAYMENT_CANCELED,
            log_action=PaymentRowChange.MULTI_ROW_UPDATE,
            log_note='basket_payment_details_updated_event_handler Cancel Payment',
        )

    else:
        if (
            not new_status
            or new_status == payment_row.status
            # Users are permitted to retry the deposit charge,
            # even if it might result in the same 'FAILED' status.
            and new_status != receipt_status.DEPOSIT_CHARGE_FAILED
        ):
            return

        card_last_digits = None
        payment = None
        if balance_transaction and balance_transaction.external_id:
            payment = Payment.objects.get(id=balance_transaction.external_id)
            card_last_digits = safe_get(
                payment, ['tokenized_payment_method', 'details', 'last_digits']
            )

        previous_status = payment_row.status
        payment_row.update_status(
            status=new_status,
            card_last_digits=card_last_digits,
            provider=PaymentRowService.map_payment_provider_code_into_payment_provider_enum(
                basket_payment.payment_provider_code
            ),
            log_action=PaymentRowChange.SINGLE_ROW_UPDATE,
            log_note='BasketPayment details update',
        )
        txn.refresh_from_db()

        is_apple_pay_native = (
            payment
            and basket_payment.payment_method == PaymentMethodType.APPLE_PAY
            and payment.status == PaymentStatus.NEW
            and previous_status == receipt_status.CALL_FOR_DEPOSIT
        )
        if (
            previous_status == receipt_status.CALL_FOR_PREPAYMENT_3DS
            or basket_payment.payment_method == PaymentMethodType.BLIK
            or is_apple_pay_native
        ):
            if new_status == receipt_status.PREPAYMENT_SUCCESS:
                confirm_appointment(transaction=txn)
            elif new_status in [
                receipt_status.PREPAYMENT_FAILED,
                receipt_status.PREPAYMENT_AUTHORISATION_FAILED,
            ]:
                if basket_payment.payment_method == PaymentMethodType.BLIK:
                    reason = 'Failed Blik attempt'
                else:
                    reason = 'Failed 3DSecure attempt'
                cancel_appointment_after_failed_payment(
                    transaction=txn,
                    reason=reason,
                )
        if (
            new_status == receipt_status.PREPAYMENT_FAILED
            and basket_payment.payment_method == PaymentMethodType.KEYED_IN_PAYMENT
        ):
            appointment = txn.appointment
            status_after = appointment.STATUS.ACCEPTED

            appointment.update_appointment(
                updated_by=get_system_user(),
                status=status_after,
                who_makes_change=WhoMakesChange.BUSINESS,
            )

    run_basket_payment_completed_analytics(basket_payment, business_id=txn.pos.business_id)


@event_receiver(cancellation_fee_auth_status_updated_event)
def cancellation_fee_auth_updated_event_handler(cf_auth: dict, **kwargs):
    cf_auth = from_dict(
        data_class=CancellationFeeAuthEntity,
        data=cf_auth,
    )

    txn = Transaction.objects.filter(
        cancellation_fee_auth_id=cf_auth.id,
    ).last()

    if not (txn and txn_refactor_stage2_enabled(txn)):
        return

    card_last_digits = None
    if cf_auth.status == CancellationFeeAuthStatus.CANCELED:
        # TODO comment - offline cancel scenario
        new_status = receipt_status.DEPOSIT_CHARGE_CANCELED
        provider = None
    else:
        balance_transaction = BalanceTransaction.objects.get(id=cf_auth.balance_transaction_id)
        new_status = {
            PaymentStatus.SENT_FOR_AUTHORIZATION: None,
            PaymentStatus.ACTION_REQUIRED: receipt_status.CALL_FOR_DEPOSIT_3DS,
            PaymentStatus.AUTHORIZED: receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
            PaymentStatus.AUTHORIZATION_FAILED: receipt_status.DEPOSIT_AUTHORISATION_FAILED,
            PaymentStatus.CANCELED: receipt_status.DEPOSIT_CHARGE_CANCELED,
        }[balance_transaction.payment.status]

        provider = PaymentRowService.map_payment_provider_code_into_payment_provider_enum(
            balance_transaction.payment_provider_code
        )
        payment = Payment.objects.get(id=balance_transaction.external_id)
        card_last_digits = safe_get(payment, ['tokenized_payment_method', 'details', 'last_digits'])

    payment_row = txn.latest_receipt.payment_rows.first()  # There should be only one PR

    if not new_status or new_status == payment_row.status:
        return

    payment_row.update_status(
        status=new_status,
        card_last_digits=card_last_digits,
        provider=provider,
        log_action=PaymentRowChange.SINGLE_ROW_UPDATE,
        log_note=traceback.format_stack(),
    )


def synchronize_tokenized_payment_method_for_stripe(instance, created):
    if instance.provider_code == PaymentProviderCode.ADYEN:
        return

    user_id = Wallet.objects.get(
        customer_id=instance.customer_id,
        owner_type=WalletOwnerType.CUSTOMER,
    ).user_id

    payment_method, _ = PaymentMethod.objects.get_or_create(
        tokenized_pm_id=instance.id,
        defaults={
            'user_id': user_id,
            'provider': PaymentProviderEnum.STRIPE_PROVIDER,
        },
    )
    payment_method.default = instance.default
    payment_method.method_type = PAYMENT_METHOD_TYPE__CREDIT_CARD
    card_type = instance.details['brand']
    if card_type == 'unknown':
        card_type = CARD_TYPE__OTHER
    payment_method.card_type = card_type
    payment_method.card_last_digits = instance.details['last_digits']
    payment_method.active = not bool(instance.deleted)
    payment_method.deleted = instance.deleted
    payment_method.expiry_month = instance.details['expiry_month']
    payment_method.expiry_year = instance.details['expiry_year']
    payment_method.cardholder_name = instance.details.get('cardholder_name', '')[:128]
    payment_method.save()


# calling function for test mocks purpose
@receiver(post_save, sender=TokenizedPaymentMethod)
def synchronize_tokenized_payment_method_for_stripe_receiver(instance, created, **kwargs):
    synchronize_tokenized_payment_method_for_stripe(instance, created)
