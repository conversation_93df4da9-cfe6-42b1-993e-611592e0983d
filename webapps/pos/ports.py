import uuid
from collections import namedtuple
from decimal import Decimal

from django.conf import settings
from django.db.transaction import atomic
from django.utils.encoding import force_str

from lib.payment_gateway.entities import (
    DisputeSplitsEntity,
    PaymentSplitsEntity,
    RefundSplitsEntity,
)
from lib.payments.enums import PaymentProviderCode
from lib.point_of_sale.enums import BasketTipType, MakePaymentPaymentMethodType
from lib.pos.entities import (
    PaymentRowEntity,
    ReceiptDetailsEntity,
    TransactionEntity,
    POSEntity,
    MakePaymentRequestEntity,
    MakePaymentResponseEntity,
    CalculatePaymentAmountRequestEntity,
    CalculatePaymentAmountResponseEntity,
    TransactionRegisterEntity,
)
from lib.tools import sget_v2
from service.booking.doers.create_customer_appointment import (
    CreateCustomerAppointment,
    CreateCustomerAppointmentCommand,
)
from service.booking.tools import AppointmentData
from webapps.booking.appointment_wrapper import AppointmentWrapper
from webapps.booking.enums import SubbookingServiceVariantMode
from webapps.booking.models import BookingSources
from webapps.booking.ports import DraftPort
from webapps.pos.enums import PaymentTypeEnum, receipt_status, TapToPayStatus
from webapps.pos.models import PaymentMethod, PaymentRow, POS, Receipt
from webapps.pos.prepayment.serializers import CustomerMakePaymentSerializer
from webapps.pos.refund import mark_sent_for_refund
from webapps.pos.serializers import CustomerInfoSerializer
from webapps.pos.services import PaymentRowService, TransactionService
from webapps.pos.tip_calculations import SimpleTip
from webapps.pos.tools import get_minimal_pba_amount
from webapps.user.models import User


CustomerInvoiceItemData = namedtuple(
    'CustomerInvoiceItemData',
    [
        'id',
        'name',
        'quantity',  # integer
        'unit_symbol',
        'net_unit_price',  # minor units
        'gross_unit_price',  # minor units
        'tax_rate',  # Optional[Decimal]
    ],
)

CustomerInvoiceData = namedtuple(
    'CustomerInvoiceData',
    [
        'id',
        'buyer_customer_id',
        'payment_method',
        'total_gross_value',
        'items',
    ],
)


def get_customer_info_data(bci_instance):
    return CustomerInfoSerializer(instance=bci_instance).data


def enable_stripe_terminal_payments(business_pk: int):
    pos = POS.objects.filter(business_id=business_pk).first()
    if pos:
        pos.enable_stripe_terminal_payments(set_as_default=False)


def set_tap_to_pay_status(business_pk: int, status: TapToPayStatus):
    POS.objects.filter(business_id=business_pk).update(tap_to_pay_status=status)


class PosSettingsPort:
    @staticmethod
    def get_minimal_pba_amount() -> Decimal:
        return get_minimal_pba_amount()


class POSPort:
    @staticmethod
    def blik_in_prepayment_promo_enabled(business_id: int) -> bool:
        return POS.objects.filter(
            business_id=business_id,
            blik_in_prepayment_promo_enabled=True,
        ).exists()

    @staticmethod
    def get_pos_by_business_id(business_id: int) -> POSEntity:
        pos = POS.objects.filter(business_id=business_id).first()
        return POSEntity(
            id=pos.id,
            receipt_footer_line_1=pos.receipt_footer_line_1,
            receipt_footer_line_2=pos.receipt_footer_line_2,
            commissions_enabled=pos.commissions_enabled,
            registers_enabled=pos.registers_enabled,
        )

    @staticmethod
    def make_payment_transaction(
        data: MakePaymentRequestEntity,
    ) -> MakePaymentResponseEntity:
        appointment: AppointmentWrapper = AppointmentWrapper.get_by_appointment_id(
            data.appointment_id, customer_user_id=data.user_id
        )
        business = appointment.business
        bci = appointment.booked_by if appointment.is_family_and_friends else appointment.booked_for
        user = bci.user
        extra_data = data.extra_data

        ### prepare data in old format for serializer
        old_payload = {'dry_run': False}
        payment_method = data.payment_data.payment_method
        if payment_method == MakePaymentPaymentMethodType.CARD:
            pm = PaymentMethod.objects.filter(
                tokenized_pm_id=data.payment_data.tokenized_pm_id
            ).first()
            old_payload['payment_method'] = pm.id
        elif payment_method in (
            MakePaymentPaymentMethodType.APPLE_PAY,
            MakePaymentPaymentMethodType.GOOGLE_PAY,
            MakePaymentPaymentMethodType.BLIK,
        ):
            old_payload['external_payment_method'] = {
                'partner': data.payment_data.payment_method,
                'token': data.payment_data.token,
            }
        else:
            old_payload['gift_cards_ids'] = [data.payment_data.token]

        if data.payment_data.tip:
            tip_type = {
                BasketTipType.PERCENT: SimpleTip.TIP_TYPE__PERCENT,
                BasketTipType.AMOUNT: SimpleTip.TIP_TYPE__HAND,
            }.get(data.payment_data.tip.type)
            old_payload['tip'] = {
                'type': tip_type,
                'rate': data.payment_data.tip.value / 100,
            }

        serializer = CustomerMakePaymentSerializer(
            data=old_payload,
            context={
                'currency_symbol': settings.CURRENCY_CODE,
                'valid_currency': True,
                'user': user,
                'business': business,
                'pos': business.pos,
                'appointment_checkout': appointment.checkout,
                'appointment_data': AppointmentData.build(appointment),
                'already_prepaid': appointment.is_prepaid,
                'extra_data': {
                    'cardholder_ip': extra_data.forwarded_ip,
                    'browser_language': extra_data.language,
                    'user': user,
                },
                'compatibilities': {},
                'device_fingerprint': extra_data.fingerprint,
                'cell_phone': user.cell_phone if user else '',
                'user_agent': extra_data.user_agent,
                'show_payment_summary': True,
            },
        )

        if not serializer.is_valid():
            return MakePaymentResponseEntity(errors=serializer.errors, basket_id=None)

        with atomic():
            trx = serializer.save()

        # todo for CF: basket_id is None, CancellationFeeAuth is created in POS v2 instead
        return MakePaymentResponseEntity(basket_id=trx.basket_id)

    @staticmethod
    def calculate_payment_amount(
        data: CalculatePaymentAmountRequestEntity,
    ) -> CalculatePaymentAmountResponseEntity:
        """
        Runs calculator for draft appointment to calculate amounts/fees/taxes/tips:
        1. Fetch draft appointment data
        2. Convert draft into a temporary appointment (without commit in DB - hence dry_run = True)
        Calculator needs data generated from
        the AppointmentWrapper object (appointment.checkout etc.)
        3. Use old serializers to run the calculator logic
        4. Map old data from serializers into the response (CalculatePaymentAmountResponseEntity)
        """

        draft = DraftPort.get_draft_by_id(data.draft_id, user_id=data.user_id)

        payload = {
            'dry_run': True,
            'business_id': draft.business_id,
            'booked_from': draft.booked_from.replace(tzinfo=None).isoformat(),
            'recurring': False,
            'customer_note': draft.customer_note,
            'subbookings': [
                {
                    'booked_from': item.booked_from.replace(tzinfo=None).isoformat(),
                    'service_variant': {
                        'id': item.service.variant.id,
                        'mode': SubbookingServiceVariantMode.VARIANT,
                    },
                    'staffer_id': item.requested_staffer_id if item.requested_staffer_id else -1,
                }
                for item in draft.items
            ],
            'compatibilities': {'prepayment': True},
        }

        if data.tip:
            tip_type = {
                BasketTipType.PERCENT: SimpleTip.TIP_TYPE__PERCENT,
                BasketTipType.AMOUNT: SimpleTip.TIP_TYPE__HAND,
            }.get(data.tip.type)
            payload['tip'] = {
                'type': tip_type,
                'rate': data.tip.value / 100,
            }

        CreateCustomerAppointment().do(
            CreateCustomerAppointmentCommand(
                user=User.objects.get(id=data.user_id),
                data=payload,
                payment_extra={},
                business_id=draft.business_id,
                user_agent=data.extra_data.user_agent,
                fingerprint=data.extra_data.fingerprint,
                booking_source=(
                    BookingSources.objects.filter(id=data.booking_source_id).first()
                    if data.booking_source_id
                    else None
                ),
            )
        )
        # todo to be determined what data is actually needed
        return CalculatePaymentAmountResponseEntity(draft_id=data.draft_id)


class PaymentRowPort:
    @staticmethod
    def get_last_payment_row_entity(basket_payment_id: uuid.UUID) -> PaymentRowEntity:
        return PaymentRow.objects.filter(basket_payment_id=basket_payment_id).last().entity

    @staticmethod
    def mark_sent_for_refund(
        basket_payment_id: uuid.UUID, operator_id: int, amount: Decimal = None
    ) -> PaymentRowEntity:

        return mark_sent_for_refund(basket_payment_id, operator_id, amount=amount).entity

    @staticmethod
    def update_basket_payment_id(payment_row_id: int, basket_payment_id: uuid.UUID) -> None:
        return PaymentRowService.update_basket_payment_id(
            payment_row_id=payment_row_id,
            basket_payment_id=basket_payment_id,
        )


class TransactionPort:
    @staticmethod
    def is_off_session_transaction(receipt_id: int) -> bool:
        return TransactionService.is_off_session_payment(
            txn=Receipt.objects.get(id=receipt_id).transaction,
        )

    @staticmethod
    def get_receipt_details(basket_id: uuid.UUID) -> ReceiptDetailsEntity | None:
        """
        Returns receipt details (id and receipt_number) for a given basket_id.

        :param basket_id: UUID of the basket
        :return: ReceiptDetailsEntity with receipt id and receipt_number or None if not found
        """
        from webapps.pos.models import Transaction

        transaction = (
            Transaction.objects.filter(
                basket_id=basket_id,
            )
            .select_related('latest_receipt', 'customer')
            .first()
        )

        if not transaction or not transaction.latest_receipt:
            return None

        receipt = transaction.latest_receipt
        return ReceiptDetailsEntity(
            id=receipt.id,
            receipt_number=receipt.receipt_number,
            assigned_number=receipt.assigned_number,
            transaction_id=transaction.id,
            customer_data=transaction.customer_data,
            customer_id=transaction.customer_id,
            customer_name=transaction.customer and transaction.customer.get_full_name(),
            customer_email=transaction.customer and transaction.customer.email,
        )

    @staticmethod
    def calculate_fees(
        payment_row_id: int,
        payment_provider_code: PaymentProviderCode,
    ) -> tuple[PaymentSplitsEntity, RefundSplitsEntity, DisputeSplitsEntity]:
        payment_row = PaymentRow.objects.get(id=payment_row_id)
        return TransactionService.calculate_fees(
            pos=payment_row.receipt.transaction.pos,
            payment_provider_code=(
                PaymentRowService.map_payment_provider_code_into_payment_provider_enum(
                    payment_provider_code,
                )
            ),
            payment_type_code=PaymentTypeEnum(payment_row.payment_type.code),
        )

    @staticmethod
    def get_transaction(basket_payment_id: uuid.UUID) -> TransactionEntity | None:
        transaction = TransactionService.get_transaction(basket_payment_id=basket_payment_id)
        return transaction.entity if transaction else None

    @staticmethod
    def get_transaction_commission_staffers_id(transaction_id: id) -> list[int]:
        return TransactionService.get_transaction_commission_staffers_id(transaction_id)

    @staticmethod
    def get_register_details(basket_id: uuid.UUID) -> TransactionRegisterEntity:
        from webapps.pos.models import Transaction

        transaction = Transaction.objects.filter(basket_id=basket_id)
        return TransactionRegisterEntity(
            id=sget_v2(transaction, ['register', 'id'], None),
            is_open=sget_v2(transaction, ['register', 'is_open'], None),
        )


class RefundPort:
    @staticmethod
    def is_refund_possible(
        basket_payment_id: uuid.UUID,
        check_requested: bool,
        check_balance: bool,
        refresh_transaction: bool,
    ) -> tuple[bool, None]:
        from webapps.pos.refund import is_refund_possible

        payment_row = PaymentRow.objects.filter(basket_payment_id=basket_payment_id).last()
        if not payment_row:
            return False, None

        return is_refund_possible(
            payment_row,
            check_requested=check_requested,
            check_balance=check_balance,
            refresh_transaction=refresh_transaction,
        )

    @staticmethod
    def basket_payment_refund_status(basket_payment_id: uuid.UUID) -> dict | None:
        payment_row = PaymentRow.objects.filter(basket_payment_id=basket_payment_id).last()
        if payment_row.status not in receipt_status.REFUND_STATUSES:
            return None
        short_status = receipt_status.STATUS_TYPES_REVERSE.get(payment_row.status, '')
        return {
            'short_status': short_status,
            'short_status_label': force_str(
                receipt_status.STATUS_TYPES_DISPLAY.get(
                    short_status,
                )
            ),
        }

    @staticmethod
    def payment_row_id_from_basket_payment(basket_payment_id: uuid.UUID) -> int | None:
        payment_row = PaymentRow.objects.filter(basket_payment_id=basket_payment_id).last()
        return payment_row.id if payment_row else None
