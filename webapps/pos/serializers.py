import copy
import logging
import re
import typing as t
import uuid
from abc import abstractmethod
from collections import defaultdict, OrderedDict
from datetime import (
    datetime,
    time,
    timedelta,
    timezone,
)
import settings
from webapps.no_show_protection.defaults import MAX_KEYED_IN_PAYMENT_TRANSACTION_AMOUNT
from decimal import (
    Decimal,
    InvalidOperation,
)
from functools import cached_property
from functools import partial
from itertools import chain

from bo_obs.datadog.enums import DatadogOperationNames, DatadogCustomServices
from bo_obs.datadog.mixins import set_apm_tag_in_current_span, MANUAL_KEEP_KEY

from dateutil.relativedelta import relativedelta
from ddtrace import tracer
from django.conf import settings
from django.contrib.postgres.aggregates import ArrayAgg
from django.core.cache import cache
from django.db.models import (
    CharField,
    F,
    Manager,
    Prefetch,
    Q,
    Subquery,
    query,
)
from django.db.models.aggregates import Count
from django.db.models.functions import Lower
from django.db.transaction import atomic
from django.template import defaultfilters
from django.utils.encoding import force_str
from django.utils.translation import gettext as _
from django.utils.translation import gettext_lazy
from rest_framework import (
    fields,
    serializers,
    status,
)
from rest_framework.exceptions import ValidationError
from rest_framework.fields import (
    empty,
    get_attribute,
)

import lib
from country_config.enums import Country
from drf_api_utils.serializers import SchemaTypeSerializerMethodField
from lib.db import (
    READ_ONLY_DB,
    retry_on_sync_error,
    using_db_for_reads,
)
from lib.feature_flag.adapter import UserData
from lib.feature_flag.feature import (
    BooksyGiftcardsCheckoutTimeValidationFlag,
    BooksyGiftcardsEnabledFlag,
    DisableBGCStatusChangeIfFailedPayment,
)
from lib.feature_flag.feature.booksy_pay import (
    BooksyPayRefundShortStatusFlag,
)
from lib.feature_flag.feature.business import (
    RemoveNoneShortStatusLabelFlag,
)
from lib.feature_flag.feature.payment import (
    CheckIfBGCAlreadyAssignedToAppointment,
    ReorganizePaymentTypeTilesEnabled,
    ShowBooksyWalletForTrialKYCPx,
    NSPFixedPrize,
)
from lib.feature_flag.feature.payment import ShowNewFinancialCenterFlag
from lib.french_certification.utils import french_certification_enabled
from lib.payment_providers.enums import ProviderAccountHolderStatus
from lib.payments.enums import (
    TokenizedPaymentMethodInternalStatus,
    PaymentSummaryRowsEnum,
    PaymentProviderCode,
)
from lib.point_of_sale.enums import (
    BasketPaymentAnalyticsTrigger,
    BasketTipSource,
)
from lib.pos.utils import txn_refactor_stage2_enabled
from lib.rivers import (
    River,
    bump_document,
)
from lib.serializers import (
    AllRelatedListSerializer,
    DurationField,
    PaginatorSerializer,
    RelativedeltaField,
    RequiredContextMixin,
    safe_get,
    validate_10_digits,
)
from lib.tools import (
    duration_formatter,
    firstof,
    format_currency,
    major_unit,
    minor_unit,
    service_color,
    sget_v2,
    tznow,
)
from service.booking.tools import (
    AppointmentData,
    check_tokenized_payments_v2,
)
from service.business.serializers import ItemOrListSerializerField
from service.pos.enums import NoShowType
from webapps.adyen.models import (
    Card,
    Cardholder,
)
from webapps.adyen.typing import DeviceDataDict
from webapps.b2b_referral.models import Prize
from webapps.booking.appointment_checkout import AppointmentCheckout
from webapps.booking.appointment_wrapper import AppointmentWrapper
from webapps.booking.enums import (
    AppointmentStatus,
    AppointmentTypeSMChoices,
)
from webapps.booking.factory.addons import addon_use_make
from webapps.booking.models import (
    Appointment,
    SubBooking,
)
from webapps.booking.serializers.utils import (
    has_traveling_services,
)
from webapps.booking.tools.tools import iter_leaf_services
from webapps.business.enums import (
    ComboPricing,
    NoShowProtectionType,
    PriceType,
)
from webapps.business.models import (
    Resource,
    Service,
    ServiceAddOn,
    ServiceAddOnUse,
    ServiceCategory,
    ServiceVariant,
    ServiceVariantChangelog,
    ServiceVariantPayment,
)
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business.serializer_fields import (
    NoShowProtectionServiceVariantField,
    validate_and_convert_percent_to_amount,
    validate_no_show_percent_and_get_amount,
    validate_no_show_amount,
)
from webapps.business.serializers import ServiceVariantSerializer
from webapps.business.service_promotions import PaymentTransactionServicePromotionMixin
from webapps.family_and_friends.helpers.bci import get_proper_bci_for_member
from webapps.family_and_friends.models import MemberProfile
from webapps.family_and_friends.pos_helpers import (
    initial_family_and_friends_payment_row_validation,
)
from webapps.feeds.enums import EventType
from webapps.feeds.utils import update_to_external_partners
from webapps.french_certification.utils import is_subbooking_editable
from webapps.market_pay.enums import TransferFundsType
from webapps.market_pay.models import CURRENCY_FACTOR
from webapps.no_show_protection.defaults import MAX_NSP_FIXED_AMOUNT
from webapps.payment_gateway.consts import MINIMAL_PAYMENT_AMOUNT
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_providers.ports.account_holder_ports import PaymentProvidersAccountHolderPort
from webapps.payment_providers.ports.payment_ports import PaymentProvidersPaymentPort
from webapps.payments.utils import get_tokenized_pm_internal_status
from webapps.point_of_sale.ports import BasketPaymentPort
from webapps.point_of_sale.services.basket import BasketService
from webapps.point_of_sale.services.cancellation_fee_auth import CancellationFeeAuthService
from webapps.pos import (
    calculations,
    enums,
)
from webapps.pos import serializer_fields as pos_fields
from webapps.pos import validators
from webapps.pos.adapters import (
    fc_validate_transaction_rows,
    fc_validate_transaction_rows_for_prepayment,
)
from webapps.pos.booksy_gift_cards.ports import validate_booksy_gift_card
from webapps.pos.bsx.models import BsxSettings
from webapps.pos.bsx.serializers import BsxSettingsSerializer
from webapps.pos.calculations import (
    RowCalculationMode,
    calculate_proportional_gross_prices,
    get_pos_settings,
    round_currency,
    rounded_decimal,
)
from webapps.pos.domain.commission.serializers import CommissionBaseSerializer
from webapps.pos.deposit import (
    create_cancellation_fee_transaction,
    create_gift_card_transaction,
    create_prepayment_transaction,
)
from webapps.pos.enums import (
    CARD_TYPE__APPLE_PAY,
    CARD_TYPE__BOOKSY_GIFT_CARD,
    compatibilities,
    PaymentProviderEnum,
    PaymentRowsSummaryScopes,
    PaymentTypeEnum,
    POSPlanPaymentTypeEnum,
    receipt_status,
)
from webapps.pos.enums.compatibilities import (
    STRIPE_TERMINAL,
    TAX_SUMMARIES_V2,
)
from webapps.pos.enums.payment_types import ONLINE_PAYMENT_TYPE_CODES
from webapps.pos.enums.splash import SplashType
from webapps.pos.enums.tip_mode import PosTipMode
from webapps.pos.models import (
    POS,
    BankAccount,
    BooksyGiftCard,
    PaymentMethod,
    PaymentRow,
    PaymentRowChange,
    PaymentType,
    Receipt,
    StaffCommissionDefault,
    TaxRate,
    Tip,
    Transaction,
    TransactionRow,
    TransactionTaxSubtotal,
    TransactionTip,
    TransactionTipRow,
    TippingAfterAppointmentAnswer,
)
from webapps.pos.provider import get_payment_provider
from webapps.pos.provider.adyen_ee import AdyenEEPaymentProvider
from webapps.pos.refund import (
    get_refund_request_status_settings,
    is_refund_possible,
)
from webapps.pos.serializer_fields import (
    POSBusinessRelatedField,
    PosDateTimeField,
    PriceFieldUnformatted,
)
from webapps.pos.services import (
    PaymentRowService,
    TransactionService,
    TransactionTipService,
)
from webapps.pos.tip_calculations import (
    SimpleTip,
    TipCalculator,
)
from webapps.pos.tools import (
    CommissionRater,
    check_pay_by_app_status,
    check_tax_rates,
    check_tips,
    detect_card_type,
    get_bank_account_choices,
    get_minimal_pba_amount,
)
from webapps.pos.utils import (
    is_no_show_protection_active,
    name_lines_from_booking,
)
from webapps.profile_completeness.events import step_turn_on_noshow_protection
from webapps.register.serializers import ResourceFromUserField
from webapps.segment.tasks import analytics_protection_service_enabled_task
from webapps.segment.utils import analytics_business_any_no_show_protection_on
from webapps.stripe_integration.models import StripeAccount
from webapps.stripe_integration.provider import StripeProvider
from webapps.user.models import User
from webapps.voucher.enums import VoucherType
from webapps.voucher.models import (
    Voucher,
    VoucherServiceVariant,
    VoucherTemplateServiceVariant,
)
from webapps.voucher.serializers import (
    SimpleCustomerSerializer,
    VoucherTransactionPartSerializer,
)
from webapps.voucher.utils import validate_package_with_prepayment
from webapps.warehouse.models import (
    Commodity,
    CommodityStockLevel,
    Warehouse,
    WarehouseDocument,
    WarehouseDocumentRow,
    WarehouseDocumentType,
    WarehouseFormula,
    WarehouseFormulaRow,
)
from webapps.warehouse.serializers.other import (
    TransactionCommoditySerializer,
    TransactionWarehouseSerializer,
)
from webapps.warehouse.serializers.recipe import (
    RecipesSerializer,
    WarehouseRecipesRowSerializer,
)

log = logging.getLogger('booksy.pos')


class _AllRelatedListOnlyUpdateSerializer(serializers.ListSerializer):
    def update(self, instance, validated_data):
        related_manager = getattr(instance, self.field_name)
        if validated_data is None:
            # do not modify
            return instance

        for data in validated_data:
            # lookup by id, rest as defaults
            lookup = {'id': data.pop('id', None)}
            related_manager.update_or_create(defaults=data, **lookup)
        return instance

    def to_representation(self, data):
        if self.context.get('pos_view'):
            data = data.filter(available=True)
        return super().to_representation(data)


class UpdateWithRemovalListSerializer(serializers.ListSerializer):
    def update(self, instance, validated_data):
        related_manager = getattr(instance, self.field_name)
        if validated_data is None:
            # do not modify
            return instance

        related_manager.all().delete()

        for data in validated_data:
            related_manager.create(**data)


class ItemPriceValidationMixin:

    def validate_item_price(self, value):
        # we need this kind validation
        # due to the #54213
        all_digits = len(value.as_tuple().digits)
        exponent = value.as_tuple().exponent
        # exponent has negative sign
        if all_digits + exponent > 6:
            raise serializers.ValidationError(_('Invalid price. Price can\'t exceed 8 digits.'))
        return value


"""
POS Settings Serializers
"""


class TipSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(read_only=False, required=False)

    class Meta:
        model = Tip
        exclude = ('pos', 'created', 'updated', 'deleted')
        list_serializer_class = UpdateWithRemovalListSerializer


class TaxRateSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(read_only=False, required=False)

    class Meta:
        model = TaxRate
        exclude = ('pos', 'created', 'updated', 'deleted')
        list_serializer_class = UpdateWithRemovalListSerializer


class PaymentTypeSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(read_only=False, required=False)
    label = serializers.CharField(read_only=True, required=False)
    code = serializers.CharField(read_only=False, required=False)
    is_offline = serializers.SerializerMethodField(read_only=True)
    editable = serializers.BooleanField(read_only=True)
    default = serializers.BooleanField(read_only=True)

    class Meta:
        model = PaymentType
        fields = (
            'id',
            'label',
            'code',
            'is_offline',
            'editable',
            'default',
            'order',
            'enabled',
        )
        list_serializer_class = _AllRelatedListOnlyUpdateSerializer

    def validate(self, attrs):
        if (
            safe_get(attrs, ['code']) in PaymentType.PERMANENTLY_ENABLED_TYPES
            and safe_get(attrs, ['enabled']) is False
        ):
            raise serializers.ValidationError(
                "This payment method cannot be disabled", code='cannot_be_disabled'
            )

        return attrs

    def get_is_offline(self, instance: PaymentType) -> bool:
        return instance.code not in ONLINE_PAYMENT_TYPE_CODES


class BankAccountSerializer(serializers.ModelSerializer):

    class Meta:
        model = BankAccount
        fields = ('routing_number', 'account_number', 'type')

    @staticmethod
    def validate_type(value):
        choices = get_bank_account_choices()
        if choices and not value:
            raise serializers.ValidationError(_('Bank Account type is required'))
        return value

    @retry_on_sync_error
    @atomic
    def update(self, instance, validated_data):
        validated_data = validated_data or {}

        try:
            bank_acc = instance.bank_account
        except AttributeError:
            # create bank account
            bank_acc = BankAccount(pos=instance, **validated_data)
        else:
            # update bank account
            for field, value in list(validated_data.items()):
                setattr(bank_acc, field, value)

        # Trigger handle_dwolla_stuff_on_bank_account_change_old
        bank_acc.save()

        return instance


class POSSerializer(serializers.ModelSerializer):
    tips = TipSerializer(many=True, required=False)
    tax_rates = TaxRateSerializer(many=True, required=False)
    payment_types = PaymentTypeSerializer(
        many=True,
        required=False,
    )
    deposit_cancel_time = RelativedeltaField(required=False)
    bank_account = BankAccountSerializer(required=False, allow_null=True)
    auto_charge_cancellation_fee = serializers.BooleanField(required=False)
    bsx_settings = BsxSettingsSerializer(required=False, allow_null=True)

    # Read-only
    business_id = serializers.PrimaryKeyRelatedField(read_only=True)
    no_show_cancel_time_options = serializers.SerializerMethodField()
    online_payment_status = serializers.SerializerMethodField()
    has_marketpay_account = serializers.SerializerMethodField()
    marketpay_payouts_enabled = serializers.SerializerMethodField()
    stripe_account_tokens_enabled = serializers.SerializerMethodField()
    show_new_financial_center = serializers.SerializerMethodField()
    booksy_pay_enabled = serializers.SerializerMethodField()

    class Meta:
        model = POS
        read_only_fields = (
            # Read-only model fields
            'active',
            'service_tax_mode',
            'product_tax_mode',
            'voucher_tax_mode',
            'tip_calculation_mode',
            'deprecated_tip_rounding_mode',
            'tax_in_receipt_visible',
            'commissions_enabled',
            'payment_auto_accept',
            'force_pba_for_cf',
            'item_discount_enabled',
            'global_discount_enabled',
            'deposit_policy',
            'deposit_cancel_time',
            'auto_charge_cancellation_fee',
            'pay_by_app_request_date',
            'prepayment_enabled',
            'refund_enabled',
            'service_fee',
            'deprecated_pos_plan',
            'deprecated_pos_plan_locked',
            'pos_plans',
            'receipt_footer_line_2',
            'marketpay_enabled',
            'stripe_terminal_enabled',
            'tap_to_pay_enabled',
            # Additional read-only
            'business_id',
            'no_show_cancel_time_options',
            'online_payment_status',
            'has_marketpay_account',
            'force_stripe_pba',
            'force_stripe_kyc',
            'marketpay_payouts_enabled',
            'has_stripe_account',
            'stripe_account_tokens_enabled',
            'show_new_financial_center',
            'booksy_pay_enabled',
        )
        fields = (
            'tax_rates',
            'payment_types',
            'deposit_cancel_time',
            'bank_account',
            'auto_charge_cancellation_fee',
            'products_stock_enabled',
            'registers_enabled',
            'registers_max_opened',
            'registers_reopening',
            'registers_shared_enabled',
            'waive_amount',
            'pay_by_app_status',  # WTF
            'receipt_footer_line_1',
            'tips_enabled',
            'tips',
            'donations_enabled',  # Not sure, if should be here, but used in analytics_business_pos_updated_task
            'bsx_settings',
        ) + read_only_fields

    @retry_on_sync_error
    @atomic
    def update(self, instance, validated_data):
        validated_data = copy.deepcopy(validated_data)  # retry_on_sync
        tips = validated_data.pop('tips', None)
        tax_rates = validated_data.pop('tax_rates', None)
        bank_account = validated_data.pop('bank_account', None)
        pay_by_app_status = validated_data.get('pay_by_app_status', False)
        payment_types = validated_data.pop('payment_types', None)
        bsx_settings = validated_data.pop('bsx_settings', None)

        # will raise on failed dwolla request, so leave it on top
        if bank_account:
            self.fields['bank_account'].update(instance, bank_account)

        super().update(instance, validated_data)
        self.fields['tips'].update(instance, tips)
        if tips and instance.stripe_account:
            StripeProvider.set_tip_settings(instance.stripe_account)
        self.fields['tax_rates'].update(instance, tax_rates)
        self.fields['payment_types'].update(instance, payment_types)

        if bsx_settings:
            instance.bsx_settings, _created = BsxSettings.objects.update_or_create(
                pos=instance, defaults=bsx_settings
            )

        if pay_by_app_status and pay_by_app_status == POS.PAY_BY_APP_DISABLED:
            instance.disable_pay_by_app()

        return instance

    @staticmethod
    def get_marketpay_payouts_enabled(instance):
        if not instance.account_holder:
            return False

        return bool(instance.account_holder.payout_allowed)

    @staticmethod
    def get_no_show_cancel_time_options(instance):
        return instance.no_show_cancel_time_options

    @staticmethod
    def get_online_payment_status(instance):
        if (
            settings.API_COUNTRY == Country.US
            and instance.pay_by_app_status == POS.PAY_BY_APP_ENABLED
            and not hasattr(instance, 'bank_account')
        ):
            return enums.NO_BANK_ACCOUNT_STATUS
        else:
            return instance.pay_by_app_status

    @staticmethod
    def get_has_marketpay_account(instance):
        return bool(instance.account_holder)

    @staticmethod
    def get_stripe_account_tokens_enabled(_instance):
        return settings.STRIPE_ACCOUNT_TOKENS_ENABLED

    @staticmethod
    def get_show_new_financial_center(instance: POS):
        if not instance.business:
            return False
        if ShowBooksyWalletForTrialKYCPx():
            if not (
                ShowNewFinancialCenterFlag(UserData(subject_key=instance.business_id))
                and instance.force_stripe_pba
            ):
                return False
            if settings.API_COUNTRY == Country.US:
                wallet_entity = PaymentGatewayPort.get_business_wallet(
                    business_id=instance.business_id,
                )
                details = PaymentProvidersAccountHolderPort.get_provider_account_status(
                    account_holder_id=wallet_entity.account_holder_id,
                    payment_provider_code=PaymentProviderCode.STRIPE,
                ).entity
                stripe_account_verified = (
                    sget_v2(details, ['status']) == ProviderAccountHolderStatus.VERIFIED
                )
                return instance.business.has_subscription or stripe_account_verified
            else:
                return instance.business.has_subscription
        else:
            return (
                ShowNewFinancialCenterFlag(UserData(subject_key=instance.business_id))
                and instance.force_stripe_pba
                and instance.business.has_subscription
            )

    @staticmethod
    def get_booksy_pay_enabled(instance: POS) -> bool:
        from webapps.booksy_pay.services.settings import BooksyPaySettingsService

        if not settings.POS__BOOKSY_PAY:
            return False

        bp_settings = BooksyPaySettingsService().get_settings(pos_id=instance.id)
        return sget_v2(bp_settings, ['enabled'], False)

    def _validate_registers(self, attrs):
        if not self.instance:
            return

        fields_to_check = [
            'registers_enabled',
            'registers_max_opened',
            'registers_reopening',
            'registers_shared_enabled',
        ]

        change_in_registers = any(
            safe_get(attrs, [field]) != safe_get(self.instance, [field])
            for field in fields_to_check
            if safe_get(attrs, [field]) is not None
        )

        open_register = self.instance.registers.filter(is_open=True).exists()

        if change_in_registers and open_register:
            raise serializers.ValidationError(
                _('Before making any change in cash registers settings close all of them.'),
                code='open_registers',
            )

    def validate(self, attrs):
        self._validate_registers(attrs)

        if self.instance:
            status = attrs.get('pay_by_app_status', self.instance.pay_by_app_status)
        else:
            status = attrs.get('pay_by_app_status')
        if status == POS.PAY_BY_APP_PENDING and not self.instance.pay_by_app_request_date:
            attrs['pay_by_app_request_date'] = tznow()

        payment_types = attrs.get('payment_types', None)
        if payment_types:
            # 69983 Start ugly fix
            if 'enabled' not in payment_types[0]:
                attrs.pop('payment_types')
            # 69983 End ugly fix
            elif not any(
                (
                    x
                    for x in payment_types
                    if x['enabled'] and x['code'] not in ONLINE_PAYMENT_TYPE_CODES
                )
            ):
                raise serializers.ValidationError(
                    _('At least one payment type is required'), code='at_least_one_pt_required'
                )

        return attrs

    @staticmethod
    def validate_tips(value):
        errors = check_tips(value)
        if errors:
            raise serializers.ValidationError(errors)
        return value

    @staticmethod
    def validate_tax_rates(value):
        errors = check_tax_rates(value)
        if errors:
            raise serializers.ValidationError(errors)
        return value

    def validate_pay_by_app_status(self, value):
        error = check_pay_by_app_status(self.instance, value)
        if error is not None:
            raise serializers.ValidationError(error)

        return value

    @atomic
    def save(self, operator_id=None):
        dirty_registers_enabled = self.instance.registers_enabled
        instance = super(POSSerializer, self).save()
        # if cash registers were previously opened and now someone
        # disabled this functionality
        if dirty_registers_enabled and not instance.registers_enabled:
            instance.clear_cash_registers(operator_id=operator_id)
        # use data serializer with fraud status for log changes
        data = POSChangeLogSerializer(instance=instance).data
        instance.log_changes(operator_id, data)
        return instance


class FCPOSSerializer(POSSerializer):
    def validate(self, attrs):
        attrs = super().validate(attrs)
        if self.instance and 'tax_rates' in attrs:
            new_tax_rates = set(tax_rate['rate'] for tax_rate in attrs['tax_rates'])
            invalid_tax_rates = {
                tax_rate
                for tax_rate in new_tax_rates
                if (tax_rate is not None and tax_rate >= Decimal('100.00'))
            }
            if invalid_tax_rates:
                raise serializers.ValidationError('Tax rate cannot be higher than or equal to 100%')
            old_tax_rates = set(self.instance.tax_rates.values_list('rate', flat=True))
            deleted_tax_rates = old_tax_rates - new_tax_rates
            if deleted_tax_rates:
                raise serializers.ValidationError('Cannot delete tax rates')
        return attrs


def get_pos_serializer_class(business_id):
    if french_certification_enabled(business_id):
        return FCPOSSerializer
    return POSSerializer


class POSChangeLogSerializer(POSSerializer):
    """
    Serializer to save changes in POSChange model
    """

    online_payment_status = None  # exclude from parent class

    class Meta:
        model = POS
        read_only_fields = ('id',)
        exclude = (
            'created',
            'updated',
            'deleted',
            'business',
            # 'online_payment_status',  # drf==3.7 compat
        )


"""
Transaction Write Serializers
"""


class BookingItemListSerializer(serializers.ListSerializer):

    def validate(self, attrs):
        """
        Extract combo children, so that each child booking will be represented as
        separate transaction rows.
        """
        validated_attrs = []
        for _attrs in attrs:
            booking = _attrs['subbooking']
            service_variant = _attrs['service_variant']

            if booking and booking.combo_children:
                validated_attrs.extend(
                    self.child.validate({'subbooking_id': combo_child})
                    for combo_child in booking.combo_children
                )
                continue
            elif service_variant and service_variant.service.is_combo:
                validated_attrs.extend(
                    self.child.validate(
                        {
                            'service_variant_combo_parent_id': service_variant,
                            'service_variant_id': combo_membership.child,
                            '_combo_membership': combo_membership,
                        }
                    )
                    for combo_membership in service_variant.combo_children_through.select_related(
                        'child'
                    )
                )
                continue

            validated_attrs.append(_attrs)
        return validated_attrs


class BookingItemSerializer(serializers.Serializer, ItemPriceValidationMixin):
    """Create Transaction Row serializer for bookings/services"""

    class Meta:
        list_serializer_class = BookingItemListSerializer

    booking_id = pos_fields.POSRelatedField(
        queryset=SubBooking.objects.all(),
        queryset_filter='appointment__business__pos',
        required=False,
        source='subbooking_id',
    )
    item_price = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)
    voucher_redeem_amount = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
        required=False,
    )
    discount_rate = serializers.IntegerField(
        required=False,
        default=None,
        allow_null=True,
        min_value=0,
        max_value=100,
    )
    commission_staffer_id = serializers.IntegerField(
        write_only=True,
        required=False,
        allow_null=True,
    )

    # walk-in booking
    service_variant_id = pos_fields.POSBusinessRelatedField(
        required=False, queryset=ServiceVariant.objects.all(), business_lookup='service__business'
    )
    service_variant_combo_parent_id = pos_fields.POSBusinessRelatedField(
        required=False,
        allow_null=True,
        queryset=ServiceVariant.objects.all(),
        business_lookup='service__business',
    )
    service_name = serializers.CharField(
        allow_blank=True,
        allow_null=True,
        required=False,
    )
    row_hash_uuid = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
    )

    @staticmethod
    def validate_service_name(value) -> str:
        return value or _('Custom Amount')

    def is_simple_checkout(
        self,
        service_variant: ServiceVariant,
    ) -> bool:
        """Conditions for simple_checkout valid_price test."""
        return safe_get(self.context, ['compatibilities', 'simple_checkout'])

    def _is_valid_price(self, amount: t.Optional[Decimal], price_type: PriceType):
        return (
            (
                amount is not None
                and price_type
                in (
                    PriceType.FIXED,
                    PriceType.DONT_SHOW,
                )
                or self.is_simple_checkout(None)
            )
            or price_type == PriceType.FREE
            or (
                # amount is not None and
                self.context.get('prepayment')
            )
            or (
                # amount is not None and
                self.context.get('cached_payment_rows')
            )
            or (
                # amount is not None and
                self.context.get('booksy_pay')
            )
            or (
                # amount is not None and
                self.context.get('is_bgc')
            )
        )

    def _make_service_variant_view(
        self,
        service_variant,
        booking=None,
        service_variant_combo_parent=None,
        combo_membership=None,
    ):
        if booking:
            service_variant.make_view(booking.service_data)
            return

        if combo_membership:
            service_variant.make_view(combo_membership)
            return

        if service_variant_combo_parent:
            self.context.setdefault('_pending_combo_members', {})
            pending_combo_members_by_parent_id = self.context['_pending_combo_members']

            parent_id = service_variant_combo_parent.id
            pending_combo_members = pending_combo_members_by_parent_id.get(parent_id)
            if not pending_combo_members:
                pending_combo_members = list(
                    service_variant_combo_parent.combo_children_through.all()
                )
                pending_combo_members_by_parent_id[parent_id] = pending_combo_members

            for index, combo_membership in enumerate(pending_combo_members):
                if combo_membership.child_id == service_variant.id:
                    service_variant.make_view(combo_membership)
                    del pending_combo_members[index]
                    return

    def validate(self, data):
        """final validate and create TransactionRow data from validated data"""

        if (
            not data.get('subbooking_id')
            and not data.get('service_variant_id')
            and not data.get('service_name')
        ):
            raise serializers.ValidationError(
                _('You need to provide either booking_id or ' 'service_variant_id or service_name')
            )

        pos = self.context['pos']

        booking = data.pop('subbooking_id', None)
        service_variant = data.pop('service_variant_id', None)
        service_variant_combo_parent = data.pop('service_variant_combo_parent_id', None)
        combo_membership = data.pop('_combo_membership', None)

        if not service_variant and booking:
            service_variant = booking.service_variant

        if booking and booking.combo_parent:
            service_variant_combo_parent = booking.combo_parent.service_variant

        if booking and not self.context.get('edit'):
            self._validate_if_payable(booking)

        item_price = data.get('item_price')
        valid_price = True

        if service_variant:
            self._make_service_variant_view(
                service_variant,
                booking=booking,
                service_variant_combo_parent=service_variant_combo_parent,
                combo_membership=combo_membership,
            )

        if item_price is None:
            if service_variant:
                item_price = service_variant.price
                valid_price = self._is_valid_price(item_price, service_variant.type)
            elif self.context.get('cached_payment_rows') and self.context.get('old_txn'):
                # Mode used only in
                # TransactionSerializer.update_transaction_with_booking
                # Here we need some small hack. When old transaction row had
                # no ServiceVariant and PriceStartsAt we were not able to set
                # price. Now we are rewriting it from old transaction, but only
                # if there is one TransactionRow.
                old_transaction_rows = self.context['old_txn'].rows.all()[:2]

                if len(old_transaction_rows) == 1:
                    item_price = old_transaction_rows[0].item_price
                else:
                    # This price is for sure wrong, but we don't want to see
                    # transaction error during editing appointment
                    item_price = 0
            else:
                valid_price = False
        item_price = item_price or Decimal(0)
        name_line_1, name_line_2 = name_lines_from_booking(
            booking=booking,
            service_variant=service_variant,
            service_name=data.get('service_name'),
        )

        if item_price is not None and item_price < Decimal(0):
            raise serializers.ValidationError(
                {'item_price': _('The price cannot be negative.')}, code='negative_price'
            )

        row = TransactionRow.get_empty_row()
        data.update(row)

        data['subbooking'] = booking
        data['service_variant'] = service_variant
        data['service_variant_combo_parent'] = service_variant_combo_parent
        if service_variant:
            data['service_variant_version'] = service_variant.version
            data['service_variant_duration'] = service_variant.duration
            data['service_variant_price'] = service_variant.price
            data['service_variant_type'] = service_variant.type

        data['type'] = TransactionRow.TRANSACTION_ROW_TYPE__SERVICE
        data['name_line_1'] = name_line_1
        data['name_line_2'] = name_line_2
        data['service_name'] = data['name_line_1']
        data['quantity'] = 1
        data['discount_rate'] = data.get('discount_rate', None)
        tax_rate = (
            service_variant.service.tax_rate if service_variant else pos.default_service_tax.rate
        )
        data['tax_rate'] = tax_rate
        data['item_price'] = item_price
        data['warehouse'] = None
        if not valid_price:
            data['incomplete_price'] = True
            data['price_value'] = item_price
            data['price_type'] = service_variant.type if service_variant else PriceType.FIXED
        self.context['bookings'] = [data['subbooking']]
        return data

    def _validate_if_payable(self, booking):
        """Modified logic from BusinessBookingPaymentInfoField.is_payable()."""
        appointment = booking.appointment
        payable = (
            # Prepayment can create transaction on unconfirmed SubBooking
            (
                (self.context.get('prepayment', False) or self.context.get('is_bgc', False))
                and appointment.status
                in [
                    # Creating booking
                    Appointment.STATUS.UNCONFIRMED,
                    # Adding prepayment to existing booking
                    Appointment.STATUS.MODIFIED,
                    # Creating business appointment with prepayment request
                    Appointment.STATUS.PENDING_PAYMENT,
                ]
            )
            or
            # must not be a reservation or time-off
            (appointment.type in Appointment.TYPES_BOOKABLE)
            and (
                appointment.status == Appointment.STATUS.FINISHED
                or appointment.status == Appointment.STATUS.ACCEPTED
            )
        )

        if not payable:
            raise serializers.ValidationError(_('You can not charge for this appointment.'))

        # check if there is a payment already
        payment = (
            Transaction.objects.by_appointment_id(
                booking.appointment_id,
            )
            .select_related(
                'latest_receipt',
            )
            .only(
                'id',
                'latest_receipt__status_code',
            )
            .filter(
                transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
                children__isnull=True,
            )
            .exclude(
                latest_receipt__status_code=receipt_status.ARCHIVED,
            )
            .last()
        )

        if payable:
            payable = payable and (
                payment is None
                or (
                    payment.latest_receipt.status_code in receipt_status.PAYABLE_STATUSES
                    and self.context.get('old_txn') == payment
                )
                and not (
                    # Check if old found transaction is also old_txn
                    # Prevent form making two parallel park_sales
                    self.context.get('old_txn', None)
                    and self.context.get('old_txn').id == payment.id
                    and payment.latest_receipt.status_code not in receipt_status.FAKE_EDIT_STATUSES
                )
                or (
                    # Allows to pay by Booksy Pay in case when previous PBA transaction
                    # was failed or canceled
                    (
                        self.context.get('booksy_pay')
                        or payment.latest_receipt.payment_type.code
                        == PaymentTypeEnum.KEYED_IN_PAYMENT
                    )
                    and payment.latest_receipt.status_code
                    in (
                        receipt_status.PAYMENT_CANCELED,
                        receipt_status.PAYMENT_FAILED,
                    )
                )
            )

        if not payable:
            raise serializers.ValidationError(_('This appointment has already been charged.'))

        # Don't allow app without required compatibilities to open transaction
        compatibilities = [
            key for key, value in list(self.context.get('compatibilities', {}).items()) if value
        ]
        if payable and not (
            payment is None
            or payment.latest_receipt
            and not payment.latest_receipt.payment_type.compatibility_requiring
            or payment.latest_receipt.payment_type.compatibility_requiring
            and payment.latest_receipt.payment_type.code in compatibilities
        ):
            # Check if payment has compatibility_requiring and
            # its code is in dict
            raise serializers.ValidationError(_('You need new app!'))

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        if instance.subbooking_id is None and instance.service_variant_id is None:
            ret['service_name'] = instance.name_line_1
        elif 'service_name' in ret:
            del ret['service_name']
        if instance.subbooking_id is not None and 'service_variant_id' in ret:
            del ret['service_variant_id']
        if instance.subbooking_id is None and 'booking_id' in ret:
            del ret['booking_id']
        return ret


class TravelFeeSerializer(serializers.Serializer, ItemPriceValidationMixin):
    discount_rate = serializers.IntegerField(
        required=False,
        min_value=0,
        max_value=100,
    )
    item_price = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
        required=False,
    )
    commission_staffer_id = serializers.IntegerField(
        write_only=True,
        required=False,
        allow_null=True,
    )

    def default_travel_fee_price(self):
        return safe_get(
            self.context['pos'].business,
            ['traveling', 'price'],
        ) or Decimal(0)

    def validate(self, data):
        data = super().validate(data)
        data['type'] = TransactionRow.TRANSACTION_ROW_TYPE__TRAVEL_FEE
        data['tax_rate'] = self.context['pos'].default_service_tax.rate
        data['quantity'] = 1
        data['name_line_1'] = _('Travel Fee')
        data['name_line_2'] = ''
        if 'item_price' not in data:
            data['item_price'] = self.default_travel_fee_price()
        return data

    def create_for_bookings(self, bookings):
        if has_traveling_services(dict(subbookings=bookings))[0]:
            item_price = safe_get(bookings[0], ['subbooking', 'appointment', 'traveling', 'price'])
            if item_price is None:
                item_price = self.default_travel_fee_price()
            return dict(
                type=TransactionRow.TRANSACTION_ROW_TYPE__TRAVEL_FEE,
                item_price=item_price,
                tax_rate=self.context['pos'].default_service_tax.rate,
                quantity=1,
                name_line_1=_('Travel Fee'),
                name_line_2='',
            )
        return None


class ProductItemSerializer(serializers.Serializer, ItemPriceValidationMixin):
    """Create Transaction Row serializer for products"""

    product_id = TransactionCommoditySerializer()
    quantity = serializers.IntegerField(required=False)
    discount_rate = serializers.IntegerField(
        required=False,
        min_value=0,
        max_value=100,
    )
    item_price = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
        required=False,
    )
    gross_total = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
        required=False,
    )
    commission_staffer_id = serializers.IntegerField(
        write_only=True,
        required=False,
        allow_null=True,
    )
    warehouse = TransactionWarehouseSerializer(required=False)
    row_hash_uuid = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
    )

    def run_validation(self, data=empty):
        default_warehouse = Warehouse.objects.filter(
            business=self.context['business'], is_default=True
        ).first()
        if not default_warehouse:
            raise serializers.ValidationError(
                {
                    'non_field_errors': [
                        'No warehouse specified, no default warehouse',
                    ],
                }
            )
        data = super(ProductItemSerializer, self).run_validation(data)
        return data

    def validate(self, data):
        """final validate and create TransactionRow data from validated data"""
        product = data.pop('product_id')
        row = TransactionRow.get_empty_row()
        row['product'] = product
        data['type'] = TransactionRow.TRANSACTION_ROW_TYPE__PRODUCT
        data['quantity'] = data.get('quantity', 1)
        data['discount_rate'] = data.get('discount_rate', Decimal(0))
        data['tax_rate'] = product.tax_rate.rate if product.tax_rate else 0

        warehouse = data.get('warehouse')
        if not warehouse:
            business = self.context['business']
            if not Warehouse.objects.filter(
                business=business,
                is_default=True,
            ).exists():
                raise serializers.ValidationError(
                    "No warehouse specified, no default warehouse",
                )

        item_price = data.get('item_price')
        if item_price is not None:
            data['item_price'] = item_price
        else:
            data['item_price'] = product.item_price

        if data['item_price'] is not None and data['item_price'] < Decimal(0):
            raise serializers.ValidationError(
                {'item_price': _('The price cannot be negative.')}, code='negative_price'
            )

        data['name_line_1'] = product.name
        data['name_line_2'] = product.category.name if product.category else u''
        row.update(data)
        return row


class DepositItemSerializer(serializers.Serializer):
    """Create Transaction Row serializer with deposit for bookings/services."""

    booking_id = pos_fields.POSRelatedField(
        queryset=SubBooking.objects.all(),
        queryset_filter='appointment__business__pos',
        required=False,
        allow_null=True,
        source='subbooking_id',
    )
    row = serializers.DictField(required=False)
    addon = serializers.PrimaryKeyRelatedField(
        queryset=ServiceAddOnUse.objects.all(),
        source='addon_use_id',
        required=False,
        allow_null=True,
    )

    def _prepare_addon_row(self, data, addon_use):
        data['subbooking'] = data.pop('subbooking_id', None)
        data['addon_use'] = addon_use
        data['service_variant'] = None
        data['product'] = None
        data['type'] = TransactionRow.TRANSACTION_ROW_TYPE__DEPOSIT
        data['name_line_1'] = _('Deposit for %(addon_use_name)s') % {
            'addon_use_name': addon_use.name,
        }
        data['name_line_2'] = _('Add-ons')
        data['quantity'] = addon_use.quantity
        data['discount_rate'] = data.get('discount_rate')
        data['tax_rate'] = addon_use.tax_rate
        if last_transaction := addon_use.transaction_rows.filter(
            type=TransactionRow.TRANSACTION_ROW_TYPE__DEPOSIT,
        ).last():
            data['item_price'] = last_transaction.item_price
        else:
            data['item_price'] = 0

        return data

    def _prepare_subbooking_row(self, data):
        pos = self.context['pos']

        booking = data.pop('subbooking_id', None)
        service_variant = booking.service_variant
        service_variant_combo_parent = None
        item_price = (
            booking.service_data.payment_amount
            if booking.service_data.payment_type == NoShowProtectionType.CANCELLATION_FEE
            else 0
        )

        if booking and booking.combo_parent:
            service_variant_combo_parent = booking.combo_parent.service_variant

        data['subbooking'] = booking
        data['service_variant'] = service_variant
        data['service_variant_combo_parent'] = service_variant_combo_parent
        data['service_variant_version'] = booking.service_data.service_variant_version
        data['service_variant_price'] = booking.service_data.service_variant_price
        data['service_variant_type'] = booking.service_data.service_variant_type
        data['service_variant_duration'] = booking.service_data.service_variant_duration
        data['product'] = None
        data['type'] = TransactionRow.TRANSACTION_ROW_TYPE__DEPOSIT
        data['name_line_1'], data['name_line_2'] = name_lines_from_booking(
            booking=booking,
            service_variant=service_variant,
            service_name=(service_variant.service.name if service_variant else ''),
        )
        data['name_line_1'] = _('Cancellation fee for %(service_name)s') % {
            'service_name': data['name_line_1'],
        }
        data['quantity'] = 1
        data['discount_rate'] = data.get('discount_rate')
        tax_rate = (
            service_variant.service.tax_rate if service_variant else pos.default_service_tax.rate
        )
        data['tax_rate'] = tax_rate
        data['item_price'] = item_price

        return data

    def validate(self, data):
        """Final validate and create TransactionRow data from validated data"""
        if 'row' in data:
            # inject already prepared deposit row
            return data['row']

        if (
            'subbooking_id' not in data
            and 'service_variant_id' not in data
            and 'service_name' not in data
            and 'addon_use_id' not in data
        ):
            raise serializers.ValidationError(
                _(
                    'You need to provide either booking_id or '
                    'service_variant_id or service_name or addon'
                )
            )

        if addon_use := data.pop('addon_use_id', None):
            data = self._prepare_addon_row(data, addon_use)
        else:
            data = self._prepare_subbooking_row(data)

        return data


"""
Transaction Read Serializers
"""


class SubbookingServiceSerializer(serializers.ModelSerializer):
    color = serializers.SerializerMethodField()

    class Meta:
        model = SubBooking
        fields = ('service_name', 'color')

    def get_color(self, instance: SubBooking):
        business = self.context.get('business')
        service = safe_get(instance, ['service_variant', 'service'])
        return service and service.get_color(business)


class PaymentTypeInfoSerializer(serializers.ModelSerializer):

    class Meta:
        model = PaymentType
        fields = ('code', 'label')


class PaymentTypeChoicesSerializer(serializers.Serializer):
    selected = serializers.SerializerMethodField()
    code = serializers.CharField()
    label = serializers.CharField()
    default = serializers.BooleanField()
    status = serializers.CharField(allow_null=True)

    def get_selected(self, instance):
        transaction = self.context.get('__current_transaction')
        payment_type_code = partial(safe_get, attrs=['payment_type', 'code'])

        if isinstance(transaction, Transaction):
            # existing Transaction serialization
            # needed for enums.BUSINESS_ACTION__SET_PAYMENT_TYPE action
            # we get payment_type from latest_receipt
            prs = transaction.latest_receipt and transaction.latest_receipt.payment_rows.all()
        else:
            # dry_run for Transaction creation - transaction is dict
            # payment_type is needed for payment type select button
            prs = transaction.get('payment_rows', [])

        unlocked_prs = [pr for pr in prs if not safe_get(pr, ['locked'])]
        selected_type_codes = [payment_type_code(row) for row in unlocked_prs]

        # Single prepayment shouldn't act as split
        single_prepayment_bool = (
            len(prs) == 1
            and payment_type_code(prs[0]) == PaymentTypeEnum.PREPAYMENT
            and not transaction.get('split_payment_remaining', Decimal(0))
        )

        single_booksy_gift_card_bool = (
            len(prs) == 1
            and payment_type_code(prs[0]) == PaymentTypeEnum.BOOKSY_GIFT_CARD
            and not transaction.get('split_payment_remaining', Decimal(0))
        )

        single_unlocked_row = (
            len(unlocked_prs) == 1
            and safe_get(unlocked_prs[0], ['mode']) == PaymentRow.PAYMENT_ROW_MODE__COMPLETE
        )

        if single_prepayment_bool:
            # If we have prepayment for 100% of transaction, we want to select PREPAYMENT code
            selected_type_codes = [PaymentTypeEnum.PREPAYMENT]

        elif single_booksy_gift_card_bool:
            selected_type_codes = [PaymentTypeEnum.BOOKSY_GIFT_CARD]

        elif not single_unlocked_row:
            # if we have single row, which is in mode C, we want to process it in normal way,
            # any other case are assumed as SPLIT
            selected_type_codes = [PaymentTypeEnum.SPLIT]

        return (
            safe_get(instance, ['code']) in selected_type_codes
            if selected_type_codes
            else instance.default
        )


class TipSerializerMixin(serializers.Serializer):
    rate = serializers.DecimalField(required=True, max_digits=8, decimal_places=2)
    type = serializers.ChoiceField(required=True, choices=SimpleTip.TIP_TYPES)

    label = serializers.SerializerMethodField(read_only=True)
    amount = pos_fields.PriceField(read_only=True)
    amount_unformatted = serializers.DecimalField(
        source='amount', read_only=True, max_digits=8, decimal_places=2
    )

    def get_label(self, instance):
        rate = get_attribute(instance, ['rate'])
        type_ = get_attribute(instance, ['type'])

        if rate == 0:
            return _('No Tip')

        if type_ == SimpleTip.TIP_TYPE__HAND:
            return u'{}'.format(format_currency(rate))

        if type_ == SimpleTip.TIP_TYPE__PERCENT:
            return u'{}%'.format(rate)


class TransactionTipRowListSerializer(serializers.ListSerializer):

    default_error_messages = {
        'rates_not_valid': _('Sum of tip_rows rates must be equal to 100'),
        'missing_tip_rows': _('Please provide tip_rows information'),
    }

    def validate(self, attrs):
        result = super().validate(attrs)
        if not result:
            self.fail('missing_tip_rows')

        self.validate_rates_sum_to_hundred(result)

        return result

    def validate_rates_sum_to_hundred(self, data: list) -> None:
        total_tip_rates = sum((get_attribute(tip_row, ['rate']) for tip_row in data))
        if total_tip_rates != 100:
            self.fail('rates_not_valid')


class TransactionTipRowSerializer(serializers.ModelSerializer):

    staffer_id = pos_fields.PrimaryKeyRelatedAsInt(
        queryset=Resource.objects.filter(
            type=Resource.STAFF,
        ).values_list('id', flat=True),
    )
    amount = pos_fields.PriceFieldUnformatted(read_only=True)
    rate = serializers.IntegerField(min_value=0, max_value=100)

    class Meta:
        model = TransactionTipRow
        list_serializer_class = TransactionTipRowListSerializer
        fields = ('staffer_id', 'rate', 'amount')


class TransactionTipSerializer(TipSerializerMixin, serializers.ModelSerializer):
    already_paid = pos_fields.PriceField(read_only=True)
    already_paid_unformatted = serializers.DecimalField(
        source='already_paid',
        read_only=True,
        max_digits=10,
        decimal_places=2,
    )
    amount_remaining = serializers.SerializerMethodField()
    tip_rows = TransactionTipRowSerializer(many=True, required=False)

    class Meta:
        model = TransactionTip
        fields = (
            'already_paid',
            'already_paid_unformatted',
            'amount_remaining',
            'tip_rows',
            # TipSerializerMixin
            'rate',
            'type',
            'label',
            'amount',
            'amount_unformatted',
        )

    @staticmethod
    def get_amount_remaining(instance: Transaction) -> Decimal:
        """Remaining amount for tip."""
        amount = safe_get(instance, ['amount']) or Decimal('0.00')
        already_paid = safe_get(instance, ['already_paid']) or Decimal('0.00')
        remaining = rounded_decimal(amount - already_paid)
        return remaining if remaining > 0 else Decimal('0.00')

    def to_representation(self, instance):
        result = super().to_representation(instance)
        self.strip_empty_tip_rows_for_default_tips(result)
        return result

    def strip_empty_tip_rows_for_default_tips(self, result: dict) -> None:
        """Avoid returning empty tip rows when not tip rows was requested."""
        has_initial_data = hasattr(self.parent, 'initial_data')
        if (
            has_initial_data and not safe_get(self.parent.initial_data, ['tip', 'tip_rows'])
        ) or not result.get('tip_rows'):
            result.pop('tip_rows', None)


class TipChoicesSerializer(TipSerializerMixin, serializers.Serializer):
    selected = serializers.SerializerMethodField()
    default = serializers.SerializerMethodField()
    disabled = serializers.BooleanField(read_only=True)
    main = serializers.SerializerMethodField(read_only=True)

    def get_default(self, instance):
        if self.context['pos'].default_tip:
            return instance['rate'] == self.context['pos'].default_tip.rate
        return False

    def get_selected(self, instance):
        if 'tip' in self.context:
            # standalone use - requires tip in context
            tip = self.context['tip']
        elif self.parent.parent == self.root:
            # TransactionSerializer dry_run=True usage
            # get tip from TransactionSerializer's validated_data
            tip = self.root.validated_data.get('tip')
        else:
            tip = None
        return bool(
            get_attribute(tip, ['rate']) is not None
            and get_attribute(tip, ['rate']) == instance['rate']
            and get_attribute(tip, ['type']) == instance['type']
        )

    def get_main(self, instance):
        """
        This field is filled in TransactionSerializer.to_representation()

        Only 3 tips can have main flag set to True. The logic is following:
        1. Get zero tip, default tip and selected tip
        2. If you still don't have 3 tips take as many you need but from the top

        Ticket #48841
        """
        return False

    @staticmethod
    def mark_as_main_tip(data):
        """
        Set some of tip_choices as main #48841
        More details in TipChoicesSerializer.get_main()

        Should be called in:
         - TransactionSerializer.to_representation()
         - CustomerAppointmentTransactionSerializer.to_representation()
        :param data: serialized data which will be modified
        """
        if 'tip_choices' in data:
            for tip in data['tip_choices']:
                if tip.get('selected') or tip.get('default') or tip.get('rate') == '0.00':
                    tip['main'] = True

            # Check how many tips have main
            how_many_main = len([tip for tip in data['tip_choices'] if tip.get('main')])

            for tip in data['tip_choices'][::-1]:
                if how_many_main >= 3:
                    break

                if not tip.get('main'):
                    tip['main'] = True
                    how_many_main += 1


class StafferTipChoicesListSerializer(serializers.ListSerializer):
    def to_representation(self, data):
        items = super().to_representation(data)
        return {item['staffer_id']: item['tip_choices'] for item in items}


class StafferTipChoicesSerializer(serializers.Serializer):
    staffer_id = pos_fields.PrimaryKeyRelatedAsInt(
        queryset=Resource.objects.filter(
            type=Resource.STAFF,
        ).values_list('id', flat=True),
    )
    tip_choices = TipChoicesSerializer(many=True)

    class Meta:
        list_serializer_class = StafferTipChoicesListSerializer


class TransactionRowDetailsSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(read_only=True, required=False)
    booking_id = serializers.PrimaryKeyRelatedField(
        read_only=True, required=False, source='subbooking'
    )
    product_id = serializers.PrimaryKeyRelatedField(
        read_only=True, required=False, source='product'
    )
    service_variant_id = serializers.PrimaryKeyRelatedField(
        read_only=True, required=False, source='service_variant'
    )
    service_variant_series_id = serializers.IntegerField(
        read_only=True, source='service_variant.series'
    )
    service_variant_combo_parent_id = serializers.PrimaryKeyRelatedField(
        read_only=True, required=False, source='service_variant_combo_parent'
    )
    service_variant_label = serializers.CharField(read_only=True, source='service_variant.label')
    service_variant_combo_parent_name = serializers.SerializerMethodField()
    service_color = serializers.SerializerMethodField()
    quantity = serializers.IntegerField()
    item_price = serializers.FloatField()
    formatted_item_price = pos_fields.TypedPriceField(
        source='item_price',
        read_only=True,
    )

    incomplete_price = serializers.BooleanField(required=False)
    price_type = serializers.CharField(required=False)
    price_value = serializers.DecimalField(required=False, max_digits=10, decimal_places=2)

    tax_amount = pos_fields.TypedPriceField()
    tax_rate = pos_fields.TaxRateReadOnlyField()
    total = serializers.FloatField()
    formatted_total = pos_fields.SomewhatTypedPriceField(
        source='total',
        read_only=True,
    )
    formatted_total_wo_discount = pos_fields.SomewhatTypedPriceField(
        source='total_wo_discount',
        read_only=True,
    )
    commission_staffer_id = serializers.IntegerField(
        read_only=True,
        required=False,
        allow_null=True,
    )
    commission_amount = pos_fields.PriceFieldUnformatted(
        read_only=True,
        required=False,
        allow_null=True,
        source='commission.amount',
    )
    commissions_last_edit = pos_fields.PosDateTimeField(
        read_only=True,
        required=False,
        allow_null=True,
    )
    commissions_last_editor_name = serializers.CharField(
        read_only=True,
        required=False,
        allow_null=True,
    )
    discount_rate = serializers.IntegerField(
        required=False,
        default=None,
        allow_null=True,
        min_value=0,
        max_value=100,
    )

    name_line_2 = serializers.SerializerMethodField()

    # Voucher fields
    voucher_id = serializers.PrimaryKeyRelatedField(
        read_only=True, required=False, source='voucher'
    )
    voucher_template_id = serializers.SerializerMethodField(
        read_only=True, required=False, allow_null=True
    )
    valid_from = serializers.DateField(read_only=True)
    voucher_customer = SimpleCustomerSerializer(read_only=True)
    voucher_paid = serializers.BooleanField(read_only=True)
    warehouse_name = serializers.CharField(
        read_only=True,
        allow_null=True,
        source='warehouse.name',
    )
    addon = serializers.SerializerMethodField(required=False, allow_null=True)
    row_hash_uuid = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    editable = serializers.SerializerMethodField()  # field for FRCert usage only

    class Meta:
        model = TransactionRow
        exclude = (
            'created',
            'updated',
            'deleted',
            'transaction',
            'subbooking',
            'product',
            'voucher',
            'service_variant',
            'service_variant_version',
            'service_variant_combo_parent',
            'service_variant_price',
            'service_variant_type',
            'service_variant_duration',
            'commission_staffer',
            'commissions_last_editor',
        )
        extra_kwargs = {'warehouse': {'read_only': True}}
        list_serializer_class = AllRelatedListSerializer

    def get_addon(self, row):
        # needed to return Addon id during `dry_run`
        return safe_get(row, ['addon', 'id'])

    def get_fields(self):
        fields = super(TransactionRowDetailsSerializer, self).get_fields()
        pos = self.context['pos']

        if not pos.tax_in_receipt_visible:
            fields['tax_rate'].write_only = True
            fields['tax_amount'].write_only = True
        return fields

    @staticmethod
    def get_name_line_2(instance):

        if get_attribute(instance, ['type']) == TransactionRow.TRANSACTION_ROW_TYPE__SERVICE:
            booking = get_attribute(instance, ['subbooking'])
            service_variant = get_attribute(instance, ['service_variant'])
            service_name = get_attribute(instance, ['service_variant']) and get_attribute(
                instance, ['service_variant', 'service', 'name']
            )

            return name_lines_from_booking(
                booking=booking,
                service_variant=service_variant,
                service_name=service_name,
            )[1]

        if get_attribute(instance, ['type']) == TransactionRow.TRANSACTION_ROW_TYPE__VOUCHER:
            voucher = get_attribute(instance, ['voucher'])
            voucher_cust = get_attribute(instance, ['voucher_customer'])
            if voucher:
                name = None
                if voucher_cust:
                    name = voucher_cust.full_name
                    if not name and voucher_cust.user:
                        name = voucher_cust.user.full_name
                if name:
                    return _('{name}, code: {code}').format(name=name, code=voucher.code)
                else:
                    return _('code: {code}').format(code=voucher.code)

        return get_attribute(instance, ['name_line_2'])

    def get_editable(self, instance):
        if french_certification_enabled(self.context['pos'].business_id):
            if subbooking := safe_get(instance, ['subbooking']):
                return is_subbooking_editable(subbooking.id)
        return True

    def to_representation(self, instance):
        ret = super(TransactionRowDetailsSerializer, self).to_representation(instance)

        if not ret.get('row_hash_uuid'):
            row_hash_uuid = uuid.uuid4().hex
            ret['row_hash_uuid'] = row_hash_uuid

        if not ret.get('service_name'):
            try:
                service_name = get_attribute(instance, ['name_line_1'])
            except (AttributeError, KeyError):
                service_name = ''

            if service_name:
                ret['service_name'] = service_name

        try:
            txn = instance.transaction
            instance_id = instance.id
        except AttributeError:
            txn = instance.get('transaction')
            instance_id = instance.get('id')

        if self.context.get('customer_api'):
            # do not return commissions in customer API
            return ret

        # mark first staffer for other rows
        first_staffer_id = self.context.get('first_staffer_id')
        if first_staffer_id is None:
            first_staffer_id = self.get_staffer_id(instance)
            self.context['first_staffer_id'] = first_staffer_id

        if ret.get('commission_staffer_id') is None:
            pos = self.context['pos']
            if txn and instance_id or self.context.get('dry_run'):
                # TODO
                rater = CommissionRater(pos=pos, transaction=txn)
                ret['commission_staffer_id'] = rater.get_commission_staffer_id(
                    instance, first_staffer_id, self.context.get('operator')
                )

            if ret.get('commission_staffer_id') is None:
                ret['commission_staffer_id'] = -1

        if 'commissions_last_editor_name' not in ret:
            if instance_id:
                ret['commissions_last_editor_name'] = instance.commissions_last_editor_name
            else:
                ret['commissions_last_editor_name'] = ''

        access_level = self.context.get('access_level')
        voucher_customer = ret.get('voucher_customer')
        if voucher_customer is not None and access_level in Resource.STAFF_ACCESS_LEVELS_LOCKED:
            for field in ['cell_phone', 'email']:
                if field in voucher_customer:
                    voucher_customer[field] = voucher_customer.get(field) and 'LOCKED'

        return ret

    def get_staffer_id(self, row):
        booking = safe_get(row, ['subbooking'])
        return (
            booking.resources.filter(
                type=Resource.STAFF,
            )
            .values_list(
                'id',
                flat=True,
            )
            .first()
            if booking
            else None
        )

    def get_voucher_template_id(self, row):
        # Voucher Template is property, so source is not possible to use
        # We are not able to exclude voucher_template from serializers fields
        voucher_template = safe_get(row, ['voucher_template']) or None
        return voucher_template and voucher_template.id

    @staticmethod
    def get_service_color(instance: TransactionRow):
        service_variant = safe_get(instance, ['service_variant'])
        if not service_variant:
            return None

        if service_variant.service.color:
            return service_variant.service.color

        return service_color(
            service_id=service_variant.service_id,
            category_id=service_variant.service.service_category_id,
            single_category=service_variant.service.business.is_single_category,
        )

    @staticmethod
    def get_service_variant_combo_parent_name(row):
        service_variant_combo_parent = safe_get(row, ['service_variant_combo_parent'])
        if not service_variant_combo_parent:
            return ''

        return service_variant_combo_parent.label or service_variant_combo_parent.service.name


class TransactionTaxSubtotalSerializer(serializers.ModelSerializer):

    class Meta:
        model = TransactionTaxSubtotal
        list_serializer_class = AllRelatedListSerializer
        fields = '__all__'


class TaxListSerializer(serializers.ListSerializer):
    def to_representation(self, instance):
        transaction_rows = safe_get(instance, ['rows'])

        iterable = (
            transaction_rows.all()
            if isinstance(transaction_rows, (Manager, query.QuerySet))
            else transaction_rows
        )

        merged_tax_summaries = defaultdict(
            lambda: {
                'total_tax_amount': Decimal(0),
                'total_net_value': Decimal(0),
                'total_gross_value': Decimal(0),
            }
        )

        for row in iterable:
            rate = safe_get(row, ['tax_rate'])
            tax_amount = safe_get(row, ['tax_amount']) or Decimal(0)
            net_total = safe_get(row, ['net_total']) or Decimal(0)
            gross_total = safe_get(row, ['gross_total']) or Decimal(0)

            merged_tax_summaries[rate]['total_tax_amount'] += tax_amount
            merged_tax_summaries[rate]['total_net_value'] += net_total
            merged_tax_summaries[rate]['total_gross_value'] += gross_total

        data = [{'tax_rate': rate, **values} for rate, values in merged_tax_summaries.items()]

        return super().to_representation(sorted(data, key=lambda x: x['tax_rate'] or Decimal('0')))


class TaxSerializer(serializers.Serializer):
    tax_rate = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_tax_amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_net_value = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_gross_value = serializers.DecimalField(max_digits=10, decimal_places=2)

    class Meta:
        list_serializer_class = TaxListSerializer


class SummaryTaxListSerializer(pos_fields.PriceField):

    def to_representation(self, transaction):
        data = get_attribute(transaction, ['tax_subtotals'])
        iterable = data.all() if isinstance(data, (Manager, query.QuerySet)) else data
        summary_by_tax = defaultdict(Decimal)
        for value in iterable:
            amount = get_attribute(value, ['tax_amount'])
            subtotal_type = get_attribute(value, ['subtotal_type'])
            tax_mode = self.get_tax_mode(transaction, subtotal_type)
            summary_by_tax[tax_mode] += Decimal(amount)

        return [
            {
                'type': 'taxes',
                'label': _('Taxes & Fees'),
                'text': super(SummaryTaxListSerializer, self).to_representation(amount),
                'value_float': round_currency(amount),
            }
            for tax_mode, amount in sorted(list(summary_by_tax.items()), reverse=True)
            if tax_mode == POS.POS_TAX_MODE__EXCLUDED
        ]

    def get_tax_mode(self, transaction, subtotal_type):
        tax_mode_attr = {
            TransactionTaxSubtotal.SUBTOTAL_TYPE__SERVICES: 'service_tax_mode',
            TransactionTaxSubtotal.SUBTOTAL_TYPE__PRODUCTS: 'product_tax_mode',
        }[subtotal_type]
        if self.context.get('dry_run'):
            # Transaction does not exist yet - use POS settings
            tax_mode = getattr(self.context['pos'], tax_mode_attr)
        else:
            # use tax mode settings from existing Transaction
            tax_mode = get_attribute(transaction, [tax_mode_attr])
        return tax_mode


class SummaryTipSerializer(pos_fields.PriceField):

    def get_tip_base(self, instance):
        """Returns amount calculation base."""
        tip_mode = safe_get(self.context, ['pos', 'tip_calculation_mode'])
        tip_base = None

        if tip_mode == PosTipMode.SERVICES:
            tip_base = safe_get(instance, ['subtotal_services'])

        elif tip_mode == PosTipMode.PRODUCTS:
            tip_base = safe_get(instance, ['subtotal_products'])

        elif tip_mode == PosTipMode.SUBTOTAL:
            tip_base = safe_get(instance, ['subtotal'])

        return tip_base

    def to_representation(self, instance):
        tip = safe_get(instance, ['tip'])

        amount = safe_get(tip, ['amount']) or Decimal(0)
        ret = [
            {
                'type': 'tip',
                'label': _('Tip'),
                'text': super(SummaryTipSerializer, self).to_representation(amount),
                'value_float': round_currency(amount),
                'tip_base': self.get_tip_base(instance),
            }
        ]

        return ret


class SummaryDiscountSerializer(pos_fields.PriceField):

    def to_representation(self, transaction):
        rate = get_attribute(transaction, ['discount_rate'])
        amount = get_attribute(transaction, ['discount_amount'])

        text = super(SummaryDiscountSerializer, self).to_representation(amount)

        ret = {
            'type': 'discount',
            'label': _('Discount'),
            'text': text if amount == 0.0 else u'-{}'.format(text),
        }
        if self.context.get('dry_run'):
            ret['value'] = rate

        return ret


class SummarySubtotalSerializer(pos_fields.PriceField):
    def to_representation(self, subtotal):
        return {
            'type': 'subtotal',
            'label': _('Subtotal'),
            'text': super(SummarySubtotalSerializer, self).to_representation(
                subtotal,
            ),
            'value_float': round_currency(subtotal),
        }


class TransactionSummarySerializer(serializers.Serializer):
    subtotal = SummarySubtotalSerializer()
    discount = pos_fields.SummaryRateField(
        'discount',
        label=gettext_lazy('Discount'),
        source='*',
        editable=True,
        show_zero_amount=False,
    )
    tax_subtotals = SummaryTaxListSerializer(source='*')
    tip = SummaryTipSerializer(source='*')

    def to_representation(self, instance):
        data = super(TransactionSummarySerializer, self).to_representation(instance)
        list_data = []
        for key, val in list(data.items()):
            if isinstance(val, list):
                list_data.extend(val)
            elif isinstance(val, dict):
                list_data.append(val)
            else:
                list_data.append(
                    {
                        'type': 'label',
                        'label': force_str(self.fields[key].label),
                        'text': val,
                    }
                )
        return list_data

    @staticmethod
    def show_zero_amount(pos: POS, fields: dict):
        if not pos.global_discount_enabled:
            del fields['discount']
        else:
            fields['discount'].show_zero_amount = True

    def get_fields(self):
        fields = super(TransactionSummarySerializer, self).get_fields()
        pos = self.context['pos']
        try:
            already_paid = get_attribute(self.context['__current_transaction'], ['tip', 'amount'])
        except AttributeError:
            already_paid = Decimal(0)

        tax_summaries_v2 = safe_get(self.context, ['compatibilities', TAX_SUMMARIES_V2])
        if not pos.tax_in_receipt_visible or tax_summaries_v2:
            del fields['tax_subtotals']
        if not pos.tips_enabled and not already_paid:
            del fields['tip']

        self.show_zero_amount(pos, fields)

        if self.context.get('dry_run'):
            if 'discount' in fields:
                fields['discount'].show_zero_amount = True
            if 'tip' in fields:
                fields['tip'].show_zero_amount = True
        return fields


class CustomerInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = BusinessCustomerInfo
        fields = '__all__'

    @staticmethod
    def handle_locked_fields(doc: dict, access_level=None):
        """Obfuscate some fields if a user shouldn't see them."""
        if access_level in Resource.STAFF_ACCESS_LEVELS_LOCKED:
            for field in ['phone', 'email']:
                if field in doc:
                    doc[field] = doc.get(field) and 'LOCKED'
        return doc

    def to_representation(self, instance):
        if self.context.get('customer_api'):
            # customer shouldn't see anything from his BCI
            # we can only use user data
            customer_profile = instance.get_customer_profile()
            return customer_profile and {
                'id': instance.id,
                'email': customer_profile.user.email,
                'phone': customer_profile.user.cell_phone,
                'full_name': u"{} {}".format(
                    customer_profile.user.first_name or '',
                    customer_profile.user.last_name or '',
                ).strip(),
                'photo_url': self.get_photo_url(customer_profile),
            }

        # business see BCI as in appointment
        customer_data = instance.as_customer_data_with_relation()
        email = customer_data.email if instance.visible_in_business else ''
        doc = {
            'id': instance.id,
            'email': email,
            'phone': customer_data.cell_phone,
            'full_name': customer_data.full_name,
            'photo_url': self.get_photo_url(customer_data),
            'has_pay_by_app': customer_data.has_pay_by_app,
            'autopay_enabled': safe_get(instance, ['user', 'payment_auto_accept']),
        }

        access_level = self.context.get('access_level')
        doc = self.handle_locked_fields(doc, access_level)
        return doc

    @staticmethod
    def get_photo_url(obj):
        if obj.photo_id:
            return obj.photo.full_url
        return safe_get(obj, ['customer_profile', 'photo', 'full_url'])


class RefundOperatorSerializer(serializers.ModelSerializer):
    label = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ('label',)

    @staticmethod
    def get_label(instance):
        """
        If needed, add different logic for business and customer contexts.
        """
        if instance.is_staff:
            return _('Booksy Support Team')

        return instance.full_name


class ReceiptPaymentRowsSerializer(serializers.ModelSerializer):
    # # Read only
    label = serializers.SerializerMethodField(read_only=True)
    amount = serializers.FloatField(source='amount_to_display', required=True)
    amount_text = pos_fields.PriceField(source='amount_to_display', read_only=True)
    created = pos_fields.PosDateTimeField(read_only=True)
    created_iso = serializers.DateTimeField(read_only=True, source='created')
    status = serializers.SerializerMethodField(read_only=True)
    payment_type_code = serializers.CharField(
        source='payment_type.code',
        read_only=True,
    )
    refundable = serializers.SerializerMethodField()
    payment_intent = serializers.SerializerMethodField()
    refund_operator = RefundOperatorSerializer(read_only=True)

    class Meta:
        model = PaymentRow
        fields = [
            'id',
            'created',
            'created_iso',
            'card_type',
            'card_last_digits',
            'status',
            'label',
            'amount_text',
            'locked',
            'payment_type_code',
            'amount',
            'refundable',
            'payment_intent',
            'refund_operator',
            'basket_payment_id',
        ]

    def get_label(self, instance):
        return force_str(get_attribute(instance, ['payment_type']).get_code_display())

    def get_status(self, instance):
        if isinstance(instance, PaymentRow):
            code = get_attribute(instance, ['status'])
            return receipt_status.STATUS_TYPES_REVERSE.get(code, '')

    def get_refundable(self, instance):
        if isinstance(instance, PaymentRow):
            possible, error = is_refund_possible(
                instance, check_requested=True, check_balance=False, refresh_transaction=False
            )
            return possible
        return False

    @staticmethod
    def get_payment_intent(instance):
        try:
            # not using .first() on purpose, it results in not using values from .prefetch_all()
            return instance.intents.all()[0].id
        except IndexError:
            return None


class ReceiptNoteSerializer(serializers.Serializer):
    type = serializers.CharField(read_only=True)
    msg = serializers.CharField(read_only=True)

    def to_representation(self, instance):
        data = {}
        # Rewrite failed message from payment_rows
        failed_row = firstof(
            pr for pr in instance.payment_rows.all() if pr.status in receipt_status.FAILED_STATUSES
        )

        if failed_row and failed_row.provider:
            provider = get_payment_provider(
                codename=failed_row.provider,
                txn=instance.transaction,
            )
            message = provider.add_payment_method_err_msg(failed_row)

            data['type'] = force_str(message.get('type', ''))
            data['msg'] = force_str(message.get('msg', ''))

        return data


class ReceiptShortStatusMixin:
    """Mixing providing short_status info for ReceiptSerializer.

    Requires context: 'customer_api' - True if customer API is requesting
    """

    def _get_refund_request_status(self, instance):
        """
        Determines the specific refund status to display to the customer (only customer). Since we
        don't store customer refund requests in the database, this function heuristically
        determines the refund request status based on appointment cancellation details and a time
        window.
        For now, it's supported only by Booksy Pay.

        Returns REFUND_REQUESTED or REFUND_OVERDUE if specific conditions are met. Otherwise,
        returns None.
        """
        business_id = sget_v2(self.context, ['pos', 'business_id'])
        if not BooksyPayRefundShortStatusFlag(UserData(subject_key=business_id)):
            return

        refund_request_status_settings = get_refund_request_status_settings(
            payment_type=get_attribute(instance, ['payment_type', 'code']),
            pos=self.context.get('pos'),
        )
        if (
            self.context.get('customer_api', False)
            and refund_request_status_settings
            and get_attribute(instance, ['status_code']) in receipt_status.REFUNDABLE_STATUSES
            and get_attribute(instance, ['payment_type', 'code']) in (PaymentTypeEnum.BOOKSY_PAY,)
            and (appointment := sget_v2(instance, ['transaction', 'appointment']))
            and appointment.status == AppointmentStatus.CANCELED
            and appointment.updated_by.id == sget_v2(appointment, ['booked_for', 'user', 'id'])
            and (appointment.booked_from - refund_request_status_settings.auto_refund_delta)
            < appointment.status_changed
        ):
            if appointment.status_changed < (
                tznow() - refund_request_status_settings.refund_overdue_delta
            ):
                return receipt_status.STATUS_TYPE__REFUND_OVERDUE

            return receipt_status.STATUS_TYPE__REFUND_REQUESTED

    def _get_short_status(self, instance):
        code = get_attribute(instance, ['status_code'])
        short_status = receipt_status.SHORT_STATUS_TYPES_REVERSE.get(code, '')

        payment_rows = get_attribute(instance, ['payment_rows']).all() or []
        pr_locked_count = len([x for x in payment_rows if x.locked])

        if pr_locked_count and code == receipt_status.PAYMENT_CANCELED:
            return receipt_status.STATUS_TYPE__IN_PROGRESS

        if refund_request_status := self._get_refund_request_status(instance):
            return refund_request_status

        return short_status

    def _get_short_status_description(self, short_status):
        # Set description for status
        descript = receipt_status.SHORT_STATUS_TYPES_DESCRIPTION.get(short_status, '')

        if short_status == receipt_status.STATUS_TYPE__CALL_FOR_PAYMENT:
            if self.context.get('customer_api', False):
                descript = descript.get('customer')
            else:
                descript = descript.get('business')

        return force_str(descript)

    def _get_short_status_label(self, receipt, short_status):
        if short_status == receipt_status.STATUS_TYPE__IN_PROGRESS:
            return force_str(
                gettext_lazy('Paid {}').format(
                    format_currency(get_attribute(receipt, ['already_paid']))
                )
            )
        else:
            label = receipt_status.STATUS_TYPES_DISPLAY.get(
                short_status,
            )
            if label is None and RemoveNoneShortStatusLabelFlag():
                label = ""
            return force_str(label)

    def _generate_short_status(self, receipt):
        short_status = self._get_short_status(receipt)

        return {
            'short_status': short_status,
            'short_status_label': self._get_short_status_label(receipt, short_status),
            'short_status_description': self._get_short_status_description(short_status),
        }


class ReceiptDetailsSerializer(
    ReceiptShortStatusMixin,
    serializers.ModelSerializer,
):
    status_type = serializers.SerializerMethodField()
    created = pos_fields.PosDateTimeField()
    created_iso = serializers.DateTimeField(source='created')
    # Deprecated >>
    payment_type = PaymentTypeInfoSerializer(read_only=True)
    # <<
    payment_rows = ReceiptPaymentRowsSerializer(read_only=True, many=True)

    remaining = pos_fields.PriceField(read_only=True)
    remaining_unformatted = serializers.DecimalField(
        source='remaining', read_only=True, max_digits=10, decimal_places=2
    )

    total = pos_fields.PriceField(read_only=True)
    already_paid = pos_fields.PriceField(read_only=True)
    note = ReceiptNoteSerializer(read_only=True, source='*')
    receipt_number = serializers.SerializerMethodField()

    class Meta:
        model = Receipt
        exclude = ('updated', 'deleted', 'transaction')

    def get_receipt_number(self, instance):
        return instance.assigned_number

    def get_status_type(self, instance):
        code = get_attribute(instance, ['status_code'])
        status_type = receipt_status.STATUS_TYPES_REVERSE.get(code, '')

        # Business owners should not see 3DSecure-related statuses.
        # Those statuses should be treated as payment-awaiting.
        if (
            self.context.get('serialized_for_business')
            and status_type == receipt_status.STATUS_TYPE__CALL_FOR_3DS
        ):
            status_type = receipt_status.STATUS_TYPE__CALL_FOR_PAYMENT
        return status_type

    def to_representation(self, instance):
        short_status = self._generate_short_status(instance)
        data = super(ReceiptDetailsSerializer, self).to_representation(instance)

        data.update(short_status)

        # For old apps. Rewrite payment data from payment_rows
        if get_attribute(instance, ['payment_type', 'code']) == PaymentTypeEnum.PAY_BY_APP:
            pba_row = firstof(
                pr
                for pr in instance.payment_rows.all()
                if pr.payment_type.code == PaymentTypeEnum.PAY_BY_APP
            )

            data['pnref'] = pba_row.pnref
            data['provider'] = pba_row.provider
            data['card_type'] = pba_row.card_type
            data['card_last_digits'] = pba_row.card_last_digits

        if (
            not instance.remaining
            or
            # We want to show CFP receipts with only 1 row in old way
            (
                len(instance.payment_rows.all()) == 1
                and instance.status_code == receipt_status.CALL_FOR_PAYMENT
            )
        ):
            del data['remaining']

        if len(instance.payment_rows.all()) == 1:
            data['payment_rows'][0]['mode'] = PaymentRow.PAYMENT_ROW_MODE__COMPLETE

        return data


class FrenchCertificationReceiptDetailsSerializer(ReceiptDetailsSerializer):
    def to_representation(self, instance: Receipt):
        from webapps.pos.adapters import FiscalReceiptAdapter

        representation = super().to_representation(instance)
        representation['id'] = None
        fiscal_receipt_entity = FiscalReceiptAdapter.last_fiscal_receipt_by_basket_id(
            instance.transaction.basket_id
        )
        if fiscal_receipt_entity:
            representation['receipt_number'] = str(fiscal_receipt_entity.number)
        else:
            representation['receipt_number'] = None
        representation['disclaimer'] = _("The above confirmation of sale is not a fiscal receipt.")

        representation['is_send_email_hidden'] = french_certification_enabled(
            instance.transaction.pos.business_id
        )
        return representation


def get_receipt_details_serializer(*args, **kwargs):
    business_id = kwargs['context']['pos'].business_id
    if french_certification_enabled(business_id):
        return FrenchCertificationReceiptDetailsSerializer(*args, **kwargs)
    return ReceiptDetailsSerializer(*args, **kwargs)


def get_transaction_serializer(*args, **kwargs):
    business_id = kwargs['context']['pos'].business_id
    if french_certification_enabled(business_id):
        return FrenchCertificationTransactionSerializer(*args, **kwargs)
    return TransactionSerializer(*args, **kwargs)


"""
Main Transaction Serializer
"""


class _TransactionCurrencyMixin:
    """Provide transaction currency for nested serializers and fields"""

    def to_representation(self, instance):
        currency_symbol = get_attribute(instance, ['currency_symbol'])
        self.context['valid_currency'] = currency_symbol == settings.CURRENCY_CODE
        self.context['currency_symbol'] = currency_symbol

        return super().to_representation(instance)


class TransactionInfoSerializer(
    ReceiptShortStatusMixin,
    _TransactionCurrencyMixin,
    serializers.ModelSerializer,
):
    """Simple Transaction serializer to provide short info.

    It's used in booking's payment info and in transaction deposit info.
    """

    # Transaction fields
    # id = <auto from Meta>
    # transaction_type = <auto from Meta>
    total = pos_fields.PriceField(
        read_only=True,
        source='latest_receipt.total',
    )

    already_paid = pos_fields.PriceField(read_only=True, source='latest_receipt.already_paid')

    remaining = pos_fields.PriceField(read_only=True, source='latest_receipt.remaining')

    # Receipt fields
    created = pos_fields.PosDateTimeField(
        read_only=True,
        source='latest_receipt.created',
    )
    created_iso = serializers.DateTimeField(
        read_only=True,
        source='latest_receipt.created',
    )
    card_type = serializers.CharField(
        read_only=True,
        source='latest_receipt.card_type',
    )
    card_last_digits = serializers.CharField(
        read_only=True,
        source='latest_receipt.card_last_digits',
    )
    status_code = serializers.CharField(
        read_only=True,
        source='latest_receipt.status_code',
    )
    payment_type = serializers.CharField(
        read_only=True,
        source='latest_receipt.payment_type.label',
    )
    payment_type_code = serializers.CharField(
        read_only=True,
        source='latest_receipt.payment_type.code',
    )
    receipt_number = serializers.CharField(
        read_only=True,
        source='latest_receipt.receipt_number',
    )
    payment_rows = ReceiptPaymentRowsSerializer(read_only=True, many=True)

    # calculated Receipt fields
    status_type = serializers.SerializerMethodField()
    details_line_1 = serializers.SerializerMethodField()
    details_line_2 = serializers.SerializerMethodField()

    def get_status_type(self, instance):
        code = get_attribute(
            instance,
            ['latest_receipt', 'status_code'],
        )
        return receipt_status.STATUS_TYPES_REVERSE.get(code, '')

    def get_details_line_1(self, instance):
        tz = self.context['pos'].business.get_timezone()
        created = get_attribute(
            instance,
            ['latest_receipt', 'created'],
        ).astimezone(tz)
        ret = u', '.join(
            [
                instance.get_transaction_type_display(),
                defaultfilters.date(created, 'DATETIME_FORMAT'),
            ]
        )
        return ret

    def get_details_line_2(self, instance):
        payment_type = get_attribute(instance, ['latest_receipt', 'payment_type'])

        transaction_type = get_attribute(instance, ['transaction_type'])

        # Override label for cancellation fee
        if (
            payment_type.pay_by_app
            and transaction_type == Transaction.TRANSACTION_TYPE__CANCELLATION_FEE
        ):
            return _('Cancellation Fee')

        return payment_type.label

    def to_representation(self, instance):
        # 52409. Stardust is alias for new Android customer app version.
        # It requires some changes in
        stardust = self.context.get(compatibilities.COMPATIBILITIES, {}).get(
            compatibilities.STARDUST, False
        )

        short_status = self._generate_short_status(get_attribute(instance, ['latest_receipt']))

        # If transaction is not full paid, but we want to show it
        # Better to show already paid amount
        if isinstance(instance, Transaction) and round_currency(
            instance.latest_receipt.already_paid
        ) != round_currency(instance.total):
            if not stardust:
                # Hack for apps before stardust. Statuses_without_money changed.
                # It shows always true values now.
                # If iOS will get stardust we can remove it!
                if instance.latest_receipt.status_code in receipt_status.STATUS_TO_SHOW_FULL_AMOUNT:
                    instance.latest_receipt.already_paid = instance.payment_rows.first().amount

                # Stardust shows both values. Total and already_paid
                instance.total = instance.latest_receipt.already_paid

        data = super(TransactionInfoSerializer, self).to_representation(instance)
        data.update(short_status)

        # For old apps. Rewrite payment data from payment_rows
        if (
            get_attribute(instance, ['latest_receipt', 'payment_type', 'code'])
            == PaymentTypeEnum.PAY_BY_APP
        ):
            pba_row = firstof(
                pr
                for pr in instance.payment_rows
                if pr.payment_type.code == PaymentTypeEnum.PAY_BY_APP
            )

            data['pnref'] = pba_row.pnref
            data['provider'] = pba_row.provider
            data['card_type'] = pba_row.card_type
            data['card_last_digits'] = pba_row.card_last_digits

        return data

    class Meta:
        model = Transaction
        fields = (
            'already_paid',
            'card_last_digits',
            'card_type',
            'created',
            'created_iso',
            'details_line_1',
            'details_line_2',
            'id',
            'payment_rows',
            'payment_type',
            'payment_type_code',
            'receipt_number',
            'remaining',
            'status_code',
            'status_type',
            'total',
            'transaction_type',
        )


class TransactionInfoForPendingPaymentSerializer(TransactionInfoSerializer):
    # calculated payment link fields
    prepayment_deadline = serializers.SerializerMethodField()

    def get_prepayment_deadline(self, instance: Transaction):
        if not instance or not instance.appointment:
            return None
        return instance.appointment.prepayment_deadline

    class Meta:
        model = Transaction
        fields = TransactionInfoSerializer.Meta.fields + ('prepayment_deadline',)


class PaymentRowsSerializer(serializers.ModelSerializer):
    amount = serializers.DecimalField(
        required=False, allow_null=True, max_digits=10, decimal_places=2
    )
    service_amount = serializers.DecimalField(
        required=False, allow_null=True, max_digits=10, decimal_places=2
    )
    tip_amount = serializers.DecimalField(
        required=False, allow_null=True, max_digits=10, decimal_places=2
    )

    payment_type_code = pos_fields.PaymentTypeCodeRelatedField(
        queryset_attr=['payment_types'],
        source='payment_type',
        required=False,  # True for dry_run=False
        allow_null=True,
    )
    locked = serializers.BooleanField(required=False)
    id = serializers.IntegerField(required=False)
    mode = serializers.CharField(required=False)

    voucher_id = pos_fields.VoucherPOSRelatedField(
        queryset_attr=['vouchers'],
        source='voucher',
        required=False,
        default=None,
        allow_null=True,
    )

    voucher_service_id = serializers.PrimaryKeyRelatedField(
        queryset=ServiceVariant.objects.all(),
        source='voucher_service',
        required=False,
        default=None,
        allow_null=True,
    )

    # Read only
    created = pos_fields.PosDateTimeField(required=False)
    created_iso = serializers.DateTimeField(required=False)
    label = serializers.SerializerMethodField(read_only=True)
    amount_text = pos_fields.PriceField(source='amount', read_only=True)
    service_amount_text = pos_fields.PriceField(source='service_amount', read_only=True)
    tip_amount_text = pos_fields.PriceField(source='tip_amount', read_only=True)
    voucher_service_price = serializers.SerializerMethodField()
    voucher_code = serializers.CharField(source='voucher.code', read_only=True)

    class Meta:
        model = PaymentRow
        fields = [
            'id',
            'basket_payment_id',
            'created',
            'created_iso',
            'amount',
            'payment_type_code',
            'locked',
            'label',
            'amount_text',
            'mode',
            'tip_amount',
            'tip_amount_text',
            'service_amount',
            'service_amount_text',
            'voucher_id',
            'voucher_service_id',
            'voucher_service_price',
            'voucher_code',
        ]

    def get_label(self, instance):
        return force_str(get_attribute(instance, ['payment_type']).get_code_display())

    def get_status(self, instance):
        if isinstance(instance, PaymentRow):
            code = get_attribute(instance, ['status'])
            return receipt_status.STATUS_TYPES_REVERSE.get(code, '')

    def to_internal_value(self, data):
        if 'id' in data:
            pr = PaymentRow.objects.get(id=data['id'])
            data['tip_amount'] = pr.tip_amount

        return super(PaymentRowsSerializer, self).to_internal_value(data)

    def validate(self, data):

        payment_type_code = data.get('payment_type') and data.get('payment_type').code

        # Android apps do not send 'amount' in payment rows when changing
        # payment type to split payment and therefore this validation made all
        # split payments to raise an error message on Android apps (Web-biz and
        # iOS are ok). We add additional compatibilities check to run validation
        # only for requests coming from Web-biz app.
        compatibilities_dict = self.context.get('compatibilities', {})
        if (
            data.get('mode') != PaymentRow.PAYMENT_ROW_MODE__COMPLETE
            and payment_type_code != PaymentTypeEnum.MEMBERSHIP
            and compatibilities_dict.get(PaymentTypeEnum.EGIFT_CARD)
            and compatibilities_dict.get(PaymentTypeEnum.MEMBERSHIP)
            and compatibilities_dict.get(PaymentTypeEnum.PACKAGE)
        ):
            if 'amount' not in data:
                raise serializers.ValidationError(
                    {
                        'voucher': 'Amount is required for not complete mode rows.',
                    },
                    code='amount_required',
                )

        self.block_memberships_with_custom_amount(data, payment_type_code)

        return data

    @staticmethod
    def block_memberships_with_custom_amount(
        data: dict,
        payment_type_code: str,
    ):
        amount = data.get('amount') or Decimal(0)
        if (
            payment_type_code == PaymentTypeEnum.MEMBERSHIP
            and 'voucher_service' in data
            and data.get('voucher_service') is not None
            and amount > 0
        ):
            raise serializers.ValidationError(
                {
                    'voucher': 'Amount cannot be greater than 0 for membership',
                },
                code='invalid_amount',
            )

    @staticmethod
    def get_voucher_service_price(instance):
        if isinstance(instance, PaymentRow):
            voucher: Voucher = instance.voucher
            voucher_service: ServiceVariant = instance.voucher_service
        else:
            voucher: Voucher = instance.get('voucher')
            voucher_service: ServiceVariant = instance.get('voucher_service')
        if not (voucher and voucher_service):
            return None
        if hasattr(voucher, 'prefetched_services'):
            # noinspection PyTypeChecker
            prefetched_services: t.List[VoucherServiceVariant] = voucher.prefetched_services
            service_price = next(
                (
                    vsv.item_price
                    for vsv in prefetched_services
                    if vsv.service_variant_id == voucher_service.id
                ),
                None,
            )
        else:
            service_price = (
                voucher.services.filter(
                    service_variant=voucher_service,
                )
                .values_list('item_price', flat=True)
                .first()
            )
        if not service_price:
            if hasattr(voucher.voucher_template, 'prefetched_services'):
                # noinspection PyTypeChecker
                prefetched_services: t.List[VoucherTemplateServiceVariant] = (
                    voucher.voucher_template.prefetched_services
                )
                service_price = next(
                    (
                        vtsv.item_price
                        for vtsv in prefetched_services
                        if vtsv.service_variant_id == voucher_service.id
                    ),
                    None,
                )
            else:
                service_price = (
                    voucher.voucher_template.services.filter(
                        service_variant=voucher_service,
                    )
                    .values_list('item_price', flat=True)
                    .first()
                )
        if not service_price:
            # Membership VSV and VTS do not have item_price set, we use SV.price
            service_price = voucher_service.price

        if service_price:
            service_price = serializers.DecimalField(
                max_digits=10,
                decimal_places=2,
            ).to_representation(service_price)
        return service_price


class SplitPaymentChoicesSerializer(serializers.Serializer):
    code = serializers.CharField()
    label = serializers.CharField()


class RemovingAppointmentWithBGCBlockedError(serializers.ValidationError):

    def __init__(self):
        detail = _(
            'It is not possible to remove a service which has a Booksy Gift Card attached. '
            'To replace service, add a new service and then remove the original one.'
        )
        code = 'removing_appt_with_bgc_blocked'
        super().__init__(detail=detail, code=code)


class BookingTransactionPartSerializer(serializers.Serializer):
    # READ/WRITE fields
    booking = serializers.PrimaryKeyRelatedField(
        required=False,
        allow_null=True,
        queryset=SubBooking.objects.all(),
        help_text=(
            'Directly specify which appointment is going to be charged, '
            'use ir if no bookings are present in the basket.'
        ),
        source='subbooking',
    )
    appointment_uid = serializers.IntegerField(
        read_only=True,
        source='appointment_id',
    )
    multibooking = serializers.PrimaryKeyRelatedField(
        required=False,
        allow_null=True,
        queryset=Appointment.objects.all(),
        help_text=(
            'Directly specify which appointment is going to be charged, '
            'use it if no bookings are present in the basket.'
        ),
        source='appointment',
    )
    customer_card_id = pos_fields.POSBusinessRelatedField(
        queryset=BusinessCustomerInfo.objects.all(),
        required=False,
        source='customer_card',
        allow_null=True,
        default=None,
        business_lookup='business',
    )
    customer_data = serializers.CharField(required=False, allow_blank=True)

    # WRITE ONLY fields
    bookings = BookingItemSerializer(
        many=True,
        write_only=True,
        required=False,
    )
    travel_fee = TravelFeeSerializer(
        write_only=True,
        required=False,
        allow_null=True,
    )
    deposits = DepositItemSerializer(
        many=True,
        write_only=True,
        required=False,  # True for deposits
    )
    saved_bookings = serializers.PrimaryKeyRelatedField(
        queryset=SubBooking.objects.all(),
        required=False,
        many=True,
        write_only=True,
    )

    # READ ONLY fields
    appointment_status = serializers.SerializerMethodField()
    appointment_datetime = PosDateTimeField(
        read_only=True,
        source="appointment.booked_from",
        default=None,
    )
    appointment_customer_name = serializers.SerializerMethodField()
    customer_info = CustomerInfoSerializer(
        source='customer_card',
        read_only=True,
        required=False,
    )
    appointment_services = serializers.SerializerMethodField()

    def validate(self, data):
        data = super(BookingTransactionPartSerializer, self).validate(data)
        bookings_part, any_booking = self._validate_bookings_and_deposit(data)
        self._validate_bookings_changed_if_booksy_gift_card_used(data)
        data.update(bookings_part)

        customer_part = self._fillup_customer(data, any_booking)
        data.update(customer_part)

        bookings = data.pop('bookings', [])
        data['rows'] = data.get('rows', []) + bookings + data.pop('deposits', [])

        travel_fee = data.pop('travel_fee', None)
        if travel_fee is None:
            travel_fee = self.fields['travel_fee'].create_for_bookings(bookings)
        if travel_fee is not None:
            data['rows'].append(travel_fee)

        self._validate_customer_card_id(data)
        return data

    def _validate_bookings_changed_if_booksy_gift_card_used(self, data):
        if not self.context.get('old_txn'):
            return
        if not self._is_booksy_gift_card_txn(self.context['old_txn']):
            return
        appointment = data.get('appointment', None)
        if (not appointment and self.context['old_txn'].appointment) or not data.get(
            'bookings', []
        ):
            raise RemovingAppointmentWithBGCBlockedError()

    def _get_appointment(self, obj):
        if self.parent is None:
            # do not calculate on listings
            return safe_get(obj, ['appointment']) or safe_get(obj, ['multibooking'])

    def get_appointment_status(self, obj):
        appointment = self._get_appointment(obj)
        return appointment and appointment.status

    def get_appointment_customer_name(self, obj):
        appointment = self._get_appointment(obj)
        if appointment:
            return (
                # full_name from customer card
                (appointment.booked_for and appointment.booked_for.as_customer_data().full_name)
                or
                # full_name from appointment
                appointment.customer_name
            )

    def get_appointment_services(self, instance):
        return SubbookingServiceSerializer(
            instance=list(get_attribute(instance, ['appointment', 'subbookings']) or []),
            many=True,
            context=self.context,
        ).data

    def _is_cancellation_fee_appointment(self, appointment):
        return appointment.transactions.filter(
            transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
        ).exists()

    @staticmethod
    def _is_prepayment_txn(txn):
        return txn.latest_receipt.payment_rows.filter(
            payment_type__code=PaymentTypeEnum.PREPAYMENT,
        ).exists()

    @staticmethod
    def _is_booksy_gift_card_txn(txn):
        return txn.latest_receipt.payment_rows.filter(
            payment_type__code=PaymentTypeEnum.BOOKSY_GIFT_CARD,
        ).exists()

    @staticmethod
    def _is_booksy_pay_txn(txn):
        return txn.latest_receipt.payment_rows.filter(
            payment_type__code=PaymentTypeEnum.BOOKSY_PAY,
        ).exists()

    def _validate_customer_card_id(self, data):
        customer_card = data.get('customer_card')
        appointment = data.get('appointment')
        if not (customer_card or appointment):
            return
        customer_card_id = getattr(customer_card, 'id', None)
        old_txn = self.context.get('old_txn')
        if (
            old_txn
            and old_txn.customer_card_id != customer_card_id
            and self._is_prepayment_txn(old_txn)
            and not self._can_use_different_customer_after_prepayment(appointment, old_txn)
        ) or (
            appointment
            and appointment.booked_for_id != customer_card_id
            and self._is_cancellation_fee_appointment(appointment)
            and not self._can_use_different_customer(appointment, customer_card_id)
        ):
            raise serializers.ValidationError(
                _('Clients with cancellation fee and deposit cannot be changed')
            )

        if (
            old_txn
            and old_txn.customer_card_id != customer_card_id
            and self._is_booksy_pay_txn(old_txn)
            and not self._can_use_different_customer_after_booksy_pay(appointment, old_txn)
        ):
            raise serializers.ValidationError(_('You cannot change customer on Booksy Pay!'))

    def _can_use_different_customer(self, appointment, customer_card_id):
        return (
            appointment.is_family_and_friends
            and appointment.booked_by
            and appointment.booked_by.id == customer_card_id
        )

    @staticmethod
    def _can_use_different_customer_by_txn(appointment, old_txn):
        return (
            appointment
            and appointment.is_family_and_friends
            and appointment.booked_by
            and appointment.booked_by.id == old_txn.customer_card_id
        )

    def _can_use_different_customer_after_prepayment(self, appointment, old_txn):
        return self._can_use_different_customer_by_txn(appointment, old_txn)

    def _can_use_different_customer_after_booksy_pay(self, appointment, old_txn):
        return self._can_use_different_customer_by_txn(appointment, old_txn)

    @staticmethod
    def _validate_bookings_and_deposit(data):
        """Validate and return associated (multi-)bookings and deposit.

        It should be run in .validate()
        It ensures that:
          - every booking in bookings field is present only once.
          - there is one booking or all bookings belong to one Appointment.
          - if there are no bookings, but (multi-)booking is specified
            directly in data it will be used
          - ensures that all bookings from bookings field belong
            to (multi-)booking specified directly
          - .create() saves associated (multi-)booking.
          - .create() saves associated deposit if available

        :param data: Transaction data during validating
        :param is_deposit: Boolean, True if Transaction is for deposit
        :return: Dict with: Booking, Appointment, and deposit associated
                 with Transaction. Any_booking used in _validate_customer
        """
        bookings = data.get('bookings', [])
        deposits = data.get('deposits', [])
        direct_booking = data.get('subbooking')
        direct_appointment = data.get('appointment')
        is_deposit = data['transaction_type'] == (Transaction.TRANSACTION_TYPE__CANCELLATION_FEE)

        saved_bookings = []
        for booking in data.pop('saved_bookings', None) or [
            item['subbooking'] for item in chain(bookings, deposits) if item.get('subbooking')
        ]:
            saved_bookings.extend(iter_leaf_services([booking]))

        booking_ids = []
        bookings = []  # list of single bookings (not-multibooking)
        appointments = set()  # set of unique appointments
        for booking in saved_bookings:
            appointments.add(booking.appointment)
            booking_ids.append(booking.id)

        # handle direct_booking and direct_appointment
        if direct_booking and not direct_appointment:
            direct_appointment = direct_booking.appointment
        if direct_appointment and direct_appointment not in appointments:
            appointments.add(direct_appointment)

        # check if bookings are not duplicated
        if len(booking_ids) != len(set(booking_ids)):
            raise serializers.ValidationError(_('All bookings must be unique'))

        # check if a single booking or all belong to the same appointment
        if (
            len(bookings) > 1  # more than one single booking
            or len(appointments) > 1  # more than one appointment
            or (bookings and appointments)  # both appointments and single bookings
        ):
            raise serializers.ValidationError(
                _('All bookings should be from the same multibooking')
            )

        # check if all bookings belong to the same customer
        if len({b.appointment.booked_for_id for b in saved_bookings}) > 1:
            raise serializers.ValidationError(
                _('All bookings should be booked for the same customer')
            )

        # match deposit related to this payment
        booking = bookings[0] if bookings else None
        appointment = appointments.pop() if appointments else None
        deposit = None
        if not is_deposit and (appointment or booking):
            deposit = (
                Transaction.objects.by_appointment_id(
                    appointment.id if appointment else booking.appointment_id
                )
                .filter(
                    transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
                    latest_receipt__status_code__in=[
                        receipt_status.DEPOSIT_AUTHORISATION_SUCCESS,
                        receipt_status.DEPOSIT_CHARGE_FAILED,
                    ],
                )
                .last()
            )

        # return booking or appointment associated with the Transaction
        any_booking = saved_bookings[0] if saved_bookings else None

        part_data = {'subbooking': booking, 'appointment': appointment, 'deposit': deposit}
        return part_data, any_booking

    def _fillup_customer(self, data, any_booking):
        """
        Fill up data with customers details.
        It should be run in .validate()
        """
        part_data = {}
        force_customer = data.pop('force_customer', False)
        part_data['customer_card'] = data.get('customer_card')
        part_data['customer_data'] = data.get('customer_data')

        if (
            self.instance is None
            and any_booking
            and any_booking.appointment.booked_for_id
            and not part_data['customer_card']
            and not force_customer
        ):
            # If it's a family and friends appointment we changed the customer connected
            # to the transaction to parent if:
            # (1) appointment is for inactive member (no user profile, no contact data,  eg. pet)
            # (2) parent is triggering transaction (it's prepayment or deposit)
            if (
                any_booking.appointment.is_family_and_friends
                and any_booking.appointment.booked_by
                and (
                    any_booking.appointment.booked_for.user is None  # (1)
                    or any_booking.appointment.booked_by.user == self.context.get('user')  # (2)
                )
            ):
                part_data['customer_card'] = any_booking.appointment.booked_by
            elif not self.context.get('edit'):
                part_data['customer_card'] = any_booking.appointment.booked_for
        customer_card = part_data['customer_card']
        if not part_data['customer_data'] or customer_card or force_customer:
            customer_data = (
                customer_card.as_customer_data_with_relation() if customer_card else None
            )
            # get "<name>, <email>, <phone>" from customer card or booking
            part_data['customer_data'] = u', '.join(
                [
                    _f
                    for _f in [
                        (
                            (customer_data and customer_data.full_name)
                            or (any_booking and any_booking.appointment.customer_name)
                        ),
                        (
                            (customer_data and customer_data.cell_phone)
                            or (any_booking and any_booking.appointment.customer_phone)
                        ),
                        (
                            (customer_data and customer_data.email)
                            or (any_booking and any_booking.appointment.customer_email)
                        ),
                    ]
                    if _f
                ]
            )

        # fill customer from customer card
        part_data['customer_id'] = (
            part_data['customer_card'].user_id if part_data['customer_card'] else None
        )

        return part_data


class ProductTransactionPartSerializer(serializers.Serializer):
    products = ProductItemSerializer(
        many=True,
        write_only=True,
        required=False,
    )

    def validate(self, data):
        data = super(ProductTransactionPartSerializer, self).validate(data)

        self._validate_commodity_counts(data)
        self._validate_commodity_type(data)
        data['rows'] = data.get('rows', []) + data.pop('products', [])

        return data

    def _get_total_product_quantity(self, product_rows, commodity, warehouse):
        total_quantity = 0
        for product_row in product_rows:
            row_warehouse = product_row.get('warehouse') or Warehouse.get_default_warehouse(
                self.context['business']
            )
            if product_row['product'] == commodity and row_warehouse == warehouse:
                total_quantity += product_row['quantity']
        return total_quantity

    def _validate_commodity_counts(self, data):
        """
        Validating product (commodity) data.
        It should be run in .validate()
        """
        if not self.context['pos'].products_stock_enabled:
            return

        # if it's edit screen and dry_run we don't to return 400 error code
        # as first response
        dry_run = data.get('dry_run')
        edit_screen = self.context.get('edit_screen')
        if edit_screen and dry_run:
            return

        parent_txn = data.get('parent_txn')
        product_rows = data.get('products', [])
        default_warehouse = Warehouse.get_default_warehouse(
            self.context['business'],
        )
        for product_row in product_rows:
            commodity = product_row['product']  # Commodity instance
            warehouse = product_row.get('warehouse') or default_warehouse
            if warehouse.is_during_stocktaking:
                raise serializers.ValidationError(
                    (
                        _(
                            "Can't sell product from warehouse {}. "
                            "This warehouse is during stocktaking."
                        )
                    ).format(warehouse.name)
                )
            stock = commodity.get_stock_level(warehouse)
            packages_needed = self._get_total_product_quantity(product_rows, commodity, warehouse)
            if parent_txn:
                old_txn_product_row = parent_txn.rows.filter(
                    product_id__isnull=False,
                    product=commodity,
                ).first()
                if old_txn_product_row:
                    old_txn_product_quantity = old_txn_product_row.quantity
                else:
                    old_txn_product_quantity = 0
                packages_needed = packages_needed - old_txn_product_quantity
            if commodity.enable_stock_control and packages_needed > stock.full_packages_left:
                raise serializers.ValidationError(
                    (
                        _(
                            '{} is not available in the quantity of {} packages. '
                            'Only {} full packages left.'
                        )
                    ).format(
                        commodity.name,
                        packages_needed,
                        stock.full_packages_left,
                    )
                )

    def _validate_commodity_type(self, data):
        product_rows = data.get('products', [])
        for product_row in product_rows:
            commodity = product_row['product']  # Commodity instance
            if commodity.product_type == Commodity.TYPE_PRO:
                raise serializers.ValidationError(
                    (
                        (
                            _(
                                "Can't sell professional {} product. Only retail "
                                "products can be sold."
                            )
                        ).format(commodity.name)
                    )
                )


class AddonItemSerializer(serializers.Serializer):
    service_addon_id = POSBusinessRelatedField(
        required=False, queryset=ServiceAddOn.objects.all(), business_lookup='business'
    )
    service_addon_use_id = POSBusinessRelatedField(
        required=False, queryset=ServiceAddOnUse.objects.all(), business_lookup='business'
    )
    quantity = serializers.IntegerField(required=False)
    item_price = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
        required=False,
    )
    discount_rate = serializers.IntegerField(
        required=False,
        min_value=0,
        max_value=100,
    )
    commission_staffer_id = serializers.IntegerField(
        write_only=True,
        required=False,
        allow_null=True,
    )
    row_hash_uuid = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
    )

    def validate(self, data):
        service_addon = data.pop('service_addon_id', None)
        service_addon_use = data.pop('service_addon_use_id', None)

        item_price = data.get('item_price')
        quantity = data.get('quantity')

        if item_price is not None and item_price < Decimal(0):
            raise serializers.ValidationError(
                {'item_price': _('The price cannot be negative.')}, code='negative_price'
            )
        if service_addon and service_addon_use:
            raise serializers.ValidationError(
                {
                    'non_field_errors': _('You cant pass both service_addon and service_addon_use'),
                }
            )

        tax_rate = None
        if service_addon:
            service_addon.quantity = quantity
            item_price = item_price or service_addon.price
            tax_rate = service_addon.tax_rate
        elif service_addon_use:
            if quantity is None:
                quantity = service_addon_use.quantity
            if item_price is None:
                item_price = service_addon_use.price
            tax_rate = service_addon_use.tax_rate

        row = TransactionRow.get_empty_row()
        data.update(row)
        extra_data = self._fill_transaction_row(service_addon or service_addon_use, data)
        data.update(extra_data)

        data['addon'] = service_addon
        data['addon_use'] = service_addon_use
        data['subbooking'] = getattr(service_addon_use, 'subbooking', None)
        data['quantity'] = quantity
        data['tax_rate'] = tax_rate
        data['item_price'] = item_price or Decimal(0)
        data['total_price'] = data['item_price'] * quantity if quantity is not None else Decimal(0)

        return data

    def to_representation(self, instance: ServiceAddOnUse) -> dict:
        data = TransactionRow.get_empty_row()
        extra_data = self._fill_transaction_row(instance, data)
        data.update(extra_data)

        data['addon_use'] = instance
        data['quantity'] = instance.quantity
        data['tax_rate'] = instance.tax_rate
        data['item_price'] = instance.price or Decimal(0)
        data['subbooking'] = instance.subbooking
        data['service_variant'] = instance.subbooking.service_variant

        return data

    def _fill_transaction_row(self, addon, data):
        data['type'] = TransactionRow.TRANSACTION_ROW_TYPE__ADDON
        data['name_line_1'], data['name_line_2'] = self._name_lines(addon)
        data['service_name'] = addon.name
        data['discount_rate'] = data.get('discount_rate', Decimal(0))
        return data

    @staticmethod
    def _name_lines(service_addon):
        duration = getattr(service_addon, 'duration', relativedelta(minutes=0))
        duration = (
            ' ({})'.format(
                duration_formatter(duration),
            )
            if duration
            else ''
        )

        formatted_date = ''
        if booking := getattr(service_addon, 'subbooking', None):
            extra_info = _('Add-ons')
            tz = booking.appointment.business.get_timezone()
            booked_from = booking.booked_from.astimezone(tz)
            formatted_date = '{}, {}, {}'.format(
                defaultfilters.date(booked_from, 'D'),
                defaultfilters.date(booked_from, 'DATETIME_FORMAT'),
                extra_info,
            )

        name_line_1 = f'{service_addon.name} {duration}'
        name_line_2 = formatted_date

        return name_line_1, name_line_2


class AddonTransactionPartSerializer(serializers.Serializer):
    addons = AddonItemSerializer(
        many=True,
        write_only=True,
        required=False,
    )

    def validate(self, data):
        data = super().validate(data)
        recived_addons = data.pop('addons', [])
        recived_extra_addons = [addon for addon in recived_addons if addon['addon']]

        recived_corrected_addons = [addon for addon in recived_addons if addon['addon_use']]
        subbookings_addons = self._get_addons_from_subbookings(data.get('rows', []))
        addons = self._merge_addons_list(recived_corrected_addons, subbookings_addons)
        # addons with 0 quantity shouldn't be represented into Tr. rows:
        addons = [addon for addon in addons if addon['quantity']]
        data['rows'] = self._sort_subbookings_addons(data.get('rows', []), addons)
        data['rows'] = data['rows'] + recived_extra_addons

        return data

    @staticmethod
    def _merge_addons_list(recived_addons, subbookings_addons):
        res = []
        recived_addons_ids = [
            recived_addon['addon_use'].id
            for recived_addon in recived_addons
            if recived_addon['addon_use']
        ]

        for subbookings_addon in subbookings_addons:
            if subbookings_addon['addon_use'].id not in recived_addons_ids:
                res.append(subbookings_addon)
        res.extend(recived_addons)
        return res

    @staticmethod
    def _sort_subbookings_addons(subbooking_rows, addons):
        res = []
        for row in subbooking_rows:
            res.append(row)
            if row['type'] != TransactionRow.TRANSACTION_ROW_TYPE__TRAVEL_FEE:
                for addon in addons:
                    if addon['subbooking'] and addon['subbooking'] == row['subbooking']:
                        res.append(addon)

        return res

    @staticmethod
    def _get_addons_from_subbookings(rows):
        addons = []
        for row in rows:
            # skip Deposit row - for Addons it was generated previously in appointment_checkout.py
            if (
                row.get('subbooking')
                and row['type'] != TransactionRow.TRANSACTION_ROW_TYPE__DEPOSIT
            ):
                qs = row.get('subbooking').addons_set.filter(deleted__isnull=True)
                res = list(AddonItemSerializer(qs, many=True).data)
                addons.extend(res)
        return addons


class SharedRegistersNotSupportedError(serializers.ValidationError):

    def __init__(self, serializer=None):
        """
        This exception is a hack for changing code in Booksy3.0

        Old apps use code='old_version' to indicate that shared registers
        are not supported. With Booksy3.0 SimpleTransaction we use code
        'shared_register_not_supported'.

        :param serializer: TransactionSerializer
        """
        detail = _(
            'Your app does not support shared registers feature. '
            'Leave only one register opened by you '
            'and close the rest.'
        )
        code = 'shared_register_not_supported'

        super().__init__(detail=detail, code=code)


class SharedRegistersSelectOrOpenError(serializers.ValidationError):

    def __init__(self):
        detail = _('To make a sale, please select register or open the new one.')
        code = 'register'
        super().__init__(detail=detail, code=code)


class SharedRegistersNotOpenedError(serializers.ValidationError):

    def __init__(self):
        detail = _('To make a sale, please open the register first.')
        code = 'register'
        super().__init__(detail=detail, code=code)


class TransactionCashInfoSerializer(serializers.Serializer):

    cash_received = pos_fields.PriceFieldUnformatted(default=None)
    cash_change = pos_fields.PriceFieldUnformatted(read_only=True)

    @staticmethod
    def calculate_cash_change(cash_info: dict, data: dict) -> dict:
        if not cash_info:
            return cash_info

        not_locked_rows = [row for row in data.get('payment_rows') if not row.get('locked')]

        cash_payment_rows = [
            row for row in not_locked_rows if row['payment_type_code'] == PaymentTypeEnum.CASH
        ]

        if len(not_locked_rows) != 1 or not cash_payment_rows:
            return {}

        cash_payment_sum = sum(Decimal(row['amount'] or 0) for row in cash_payment_rows)

        cash_change = cash_info['cash_received'] - cash_payment_sum
        cash_change = cash_change if cash_change > 0 else 0
        cash_info['cash_change'] = rounded_decimal(cash_change)

        return cash_info


class PaymentTransactionPartSerializer(
    _TransactionCurrencyMixin,
    PaymentTransactionServicePromotionMixin,
    serializers.Serializer,
):
    # read write fields
    transaction_type = serializers.ChoiceField(choices=Transaction.TRANSACTION_TYPES)
    customer_action_required = serializers.SerializerMethodField()
    tip = TransactionTipSerializer(required=False, allow_null=True)
    discount_rate = serializers.IntegerField(
        default=0,
        min_value=0,
        max_value=100,
    )
    # Commodities
    commodity_usage = WarehouseRecipesRowSerializer(required=False, many=True)
    issuing_staffer = pos_fields.POSBusinessRelatedField(
        queryset=Resource.objects.filter(type=Resource.STAFF),
        business_lookup='business',
        default=None,
        required=False,
        allow_null=True,
    )

    confirming_staffer = pos_fields.POSBusinessRelatedField(
        queryset=Resource.objects.filter(type=Resource.STAFF),
        business_lookup='business',
        default=None,
        required=False,
        allow_null=True,
    )
    note = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
        max_length=500,
    )

    # write only fields
    dry_run = serializers.BooleanField(write_only=True)
    # prevent auto select customer from booking
    force_customer = serializers.BooleanField(write_only=True, required=False)

    payment_type_code = pos_fields.PaymentTypeCodeRelatedField(
        queryset_attr=['payment_types'],
        source='payment_type',
        write_only=True,
        required=False,  # True for dry_run=False
    )

    # read only fields (created in validation or read from instance)
    id = serializers.IntegerField(read_only=True, required=False)
    customer_id = serializers.IntegerField(read_only=True, required=False)
    created = pos_fields.PosDateTimeField(read_only=True, required=False)
    created_iso = serializers.DateTimeField(read_only=True, required=False, source='created')
    updated = pos_fields.PosDateTimeField(read_only=True, required=False)
    updated_iso = serializers.DateTimeField(read_only=True, required=False, source='updated')
    payment_type_choices = PaymentTypeChoicesSerializer(
        many=True,
        required=False,
        read_only=True,
    )
    payment_rows = PaymentRowsSerializer(
        many=True,
        required=False,
    )
    split_payment_choices = SplitPaymentChoicesSerializer(
        many=True,
        required=False,
        read_only=True,
    )
    split_payment_remaining = pos_fields.PriceField(read_only=True)
    split_payment_remaining_unformatted = serializers.DecimalField(
        source='split_payment_remaining', read_only=True, max_digits=10, decimal_places=2
    )

    tip_choices = TipChoicesSerializer(
        many=True,
        required=False,
        read_only=True,
    )
    staffer_tip_choices = StafferTipChoicesSerializer(
        many=True,
        required=False,
        read_only=True,
    )

    rows = TransactionRowDetailsSerializer(many=True, read_only=True)
    tax_summary = TaxSerializer(source='*', read_only=True, many=True)
    summaries = TransactionSummarySerializer(read_only=True, source='*')
    total = pos_fields.PriceField(read_only=True)
    total_unformatted = serializers.DecimalField(
        source='total', read_only=True, max_digits=10, decimal_places=2
    )
    receipts = ReceiptDetailsSerializer(
        many=True,
        read_only=True,
        required=False,
    )
    actions = serializers.SerializerMethodField()

    business_name = serializers.CharField(read_only=True, required=False)
    business_address = serializers.CharField(read_only=True, required=False)
    parent_txn = serializers.PrimaryKeyRelatedField(
        queryset=Transaction.objects.all(),
        required=False,
        allow_null=True,
        default=None,
    )
    deposit_info = TransactionInfoSerializer(
        read_only=True,
        required=False,
        source='deposit',
    )
    charge_date = pos_fields.PosDateTimeField(read_only=True)
    commissions_enabled = serializers.BooleanField(
        read_only=True,
        required=False,
    )

    operator = ResourceFromUserField(
        read_only=True,
        required=False,
    )

    lock = serializers.BooleanField(
        read_only=True,
    )

    receipt_footer_line_1 = serializers.CharField(
        read_only=True,
        required=False,
    )
    receipt_footer_line_2 = serializers.CharField(
        read_only=True,
        required=False,
    )
    tax_id = serializers.CharField(
        source='customer_card.tax_id',
        read_only=True,
        required=False,
    )
    total_appointment_without_tip = pos_fields.PriceFieldUnformatted(
        read_only=True,
    )
    total_wo_prepayment = serializers.SerializerMethodField()
    total_wo_prepayment_w_remaining_tip = serializers.SerializerMethodField()
    default_commodity_usage = serializers.SerializerMethodField()
    three_d_data = serializers.SerializerMethodField()
    txn_hash = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    cash_info = TransactionCashInfoSerializer(required=False)
    customer_invoice = serializers.SerializerMethodField()
    customer_invoice_number = serializers.SerializerMethodField()
    can_generate_invoice = serializers.SerializerMethodField()
    fast_pay_by_app_available = serializers.SerializerMethodField()
    is_family_and_friends = serializers.SerializerMethodField()
    force_stripe_pba = serializers.SerializerMethodField()
    deposit_policy = serializers.CharField(source='pos.deposit_policy', read_only=True)
    payment_token = serializers.CharField(write_only=True, required=False)

    def get_customer_action_required(self, instance):
        business_id = safe_get(self.context, ['pos', 'business_id'])
        if not settings.POS__BLIK:
            return False

        if isinstance(instance, Transaction) and instance.payment_rows and instance.latest_receipt:
            status = get_attribute(instance, ['latest_receipt', 'status_code'])
            if status in [
                receipt_status.CALL_FOR_PAYMENT,
            ]:
                if instance.payment_rows.filter(payment_type__code=PaymentTypeEnum.BLIK).exists():
                    return True

        return False

    @property
    def data(self):
        data = super().data
        if 'txn_hash' in data and data['txn_hash'] is None:
            del data['txn_hash']

        return data

    def get_fast_pay_by_app_available(self, instance) -> bool:
        pos = self.context['pos']
        bci = safe_get(instance, ['customer_card'])
        is_family_and_friends = self.get_is_family_and_friends(instance)

        return all(
            (
                pos.is_pay_by_app_active,
                not is_family_and_friends,
                (
                    pos.stripe_kyc_completed
                    and bci
                    and bci.has_card(provider=PaymentProviderEnum.STRIPE_PROVIDER)
                ),
            )
        )

    def get_is_family_and_friends(self, instance) -> bool:
        return bool(safe_get(instance, ['appointment', 'is_family_and_friends']))

    def get_force_stripe_pba(self, instance) -> bool:
        """Method is modified to handle online migration - even if pos has force_stripe_pba, but
        transaction is saved before pos.migration_date False will be returned -> this transaction
        should be handled through Adyen Provider"""

        if isinstance(instance, Transaction):
            return txn_refactor_stage2_enabled(
                instance
            ) and bool(  # LaunchDarkly flag is checked here
                safe_get(self.context, ['pos', '_force_stripe_pba'])
            )
        else:
            return bool(safe_get(self.context, ['pos', 'force_stripe_pba']))

    @staticmethod
    def _get_prefetched_customer_invoice(instance):
        # return CustomerInvoice if prefetched otherwise raises AttributeError
        return instance._prefetched_customer_invoices and instance._prefetched_customer_invoices[0]

    def get_customer_invoice(self, instance):
        if isinstance(instance, Transaction):
            # Invoices may get soft deleted that's why they can't be in
            # one-to-one relation with transaction and we must pick first.
            try:
                invoice = self._get_prefetched_customer_invoice(instance)
            except AttributeError:
                invoice = instance.customer_invoices.first()
            if invoice:
                return invoice.id
        return None

    def get_customer_invoice_number(self, instance):
        # see get_customer_invoice()
        if isinstance(instance, Transaction):
            try:
                invoice = self._get_prefetched_customer_invoice(instance)
            except AttributeError:
                invoice = instance.customer_invoices.only('number').first()
            if invoice:
                return invoice.number

    @staticmethod
    def _get_stripe_3ds_data(instance: Transaction):
        if not instance.latest_receipt:
            return
        if instance.latest_receipt.status_code not in receipt_status.PENDING_3DS_STATUSES:
            return

        basket_payment_id = (
            instance.latest_receipt.payment_rows.values_list('basket_payment_id', flat=True)
            .filter(
                status__in=receipt_status.PENDING_3DS_STATUSES,
            )
            .first()
        )

        basket_payment_entity = BasketPaymentPort.get_basket_payment_entity(
            basket_payment_id=basket_payment_id,
        )
        if basket_payment_entity:
            return basket_payment_entity.action_required_details
        else:
            return None

    def get_three_d_data(self, instance: Transaction) -> t.Optional[dict]:
        if self.check_stage_2() and getattr(instance, 'basket_id', None):
            return self._get_stripe_3ds_data(instance)

    def get_can_generate_invoice(self, instance) -> bool:
        # If any transaction in transaction series had an invoice generated
        # it is then impossible to generate another invoice
        if isinstance(instance, Transaction):
            is_possible, _msg = instance.can_generate_invoice()
            return is_possible
        return True

    def get_default_commodity_usage(self, instance):
        """
        Method calculate and return default commodity usage.
        As "default commodity usage" we understand a commodity usage resulting
        from predefined commodity usage for service variants defnied
        by merchant at business settings panel - at database it will be
        WarehouseFormula object attached to service variant.
        If service variant used at checkout have some commodity usage defined
        then it will be used during calculation.

        Returned object is a list of WarehouseFormula rows.
        Single row fields:
            - id - row id
            - commodity - commodity id
            - count - how much pieces is used
            - warehouse - warehouse from which we get used commodities

        :returns: [{
            'id': 18,
            'commodity': 1525,
            'count': 5,
            'warehouse': 367,
        }]
        """
        initial_data = getattr(self, 'initial_data', None)
        if not initial_data or 'bookings' not in initial_data:
            return []

        bookings_ids = [x['booking_id'] for x in initial_data['bookings'] if x.get('booking_id')]
        booking_service_variant_ids = SubBooking.objects.filter(
            id__in=bookings_ids,
            service_variant_id__isnull=False,
        ).values_list('service_variant_id', flat=True)
        service_variant_formula_cache = {}
        whole_usage = []
        for service_variant_id in booking_service_variant_ids:
            if service_variant_id in service_variant_formula_cache:
                recipe = service_variant_formula_cache[service_variant_id]
            else:
                recipe = WarehouseFormula.get_formula(service_variant_id)
                service_variant_formula_cache[service_variant_id] = recipe
            if not recipe:
                continue
            serializer = RecipesSerializer(instance=recipe)
            recipe_rows = serializer.data['rows']
            for recipe_row in recipe_rows:
                if not recipe_row['commodity']['archived']:
                    whole_usage.append(recipe_row)

        service_variant_ids = [
            x['service_variant_id']
            for x in initial_data['bookings']
            if x.get('service_variant_id') and not x.get('booking_id')
        ]
        for service_variant_id in service_variant_ids:
            if service_variant_id in service_variant_formula_cache:
                recipe = service_variant_formula_cache[service_variant_id]
            else:
                recipe = WarehouseFormula.get_formula(service_variant_id)
                service_variant_formula_cache[service_variant_id] = recipe
            if not recipe:
                continue
            serializer = RecipesSerializer(instance=recipe)
            recipe_rows = serializer.data['rows']
            for recipe_row in recipe_rows:
                if not recipe_row['commodity']['archived']:
                    whole_usage.append(recipe_row)
        return whole_usage

    def validate_commodity_usage(self, value):
        txn_hash = self.initial_data.get('txn_hash')
        if txn_hash:
            value = self.recalculate_commodity_usage(value, txn_hash)
        return value

    def validate_txn_hash(self, value):
        if not self.context.get('business'):
            return value
        cache_key = self._get_cache_key(value)
        if not cache.get(cache_key):
            raise serializers.ValidationError('Checkout timeout')
        return value

    def _get_cache_key(self, txn_hash):
        business = self.context.get('business')
        if not business:
            return None
        cache_key = 'checkout/{business_id}/{txn_hash}'.format(
            business_id=business.id,
            txn_hash=txn_hash,
        )
        return cache_key

    def recalculate_commodity_usage(self, current_value, txn_hash):
        cached_values = cache.get(self._get_cache_key(txn_hash))

        if not cached_values:
            return current_value

        cached_bookings = cached_values.get('bookings')
        current_bookings = self.initial_data['bookings']
        if cached_bookings == current_bookings:
            return current_value
        current_default_usage = self.get_default_commodity_usage(None)
        cached_default_usage = cached_values['default_commodity_usage']
        new_usage, usage_to_delete = self._difference_at_usage(
            current_default_usage,
            cached_default_usage,
        )

        for usage_row in new_usage:
            same_commodity_row = self._get_same_commodity_row(
                current_value,
                usage_row,
            )
            if same_commodity_row:
                same_commodity_row['count'] += usage_row['count']
            else:
                commodity = Commodity.objects.get(
                    id=usage_row['commodity']['id'],
                )
                if not commodity.archived:
                    current_value.append(
                        {
                            'commodity': Commodity.objects.get(
                                id=usage_row['commodity']['id'],
                            ),
                            'count': usage_row['count'],
                            'warehouse': Warehouse.objects.get(
                                id=usage_row['warehouse'],
                            ),
                        }
                    )

        for usage_row in usage_to_delete:
            same_commodity_row = self._get_same_commodity_row(
                current_value,
                usage_row,
            )
            if same_commodity_row:
                deleted_count = usage_row['count']
                current_count = same_commodity_row['count']
                if deleted_count >= current_count:
                    current_value.remove(same_commodity_row)
                else:
                    same_commodity_row['count'] -= usage_row['count']
        return current_value

    def _difference_at_usage(
        self,
        current_usage: t.List[t.OrderedDict],
        cached_usage: t.List[t.OrderedDict],
    ):
        new_usage = []

        # OrderedDict is unhashable, creating unique list manually
        unique_current_usage = []
        for x in current_usage:
            if x not in unique_current_usage:
                unique_current_usage.append(x)

        for usage_row in unique_current_usage:
            how_many_cached = cached_usage.count(usage_row)
            how_many_used = current_usage.count(usage_row)
            diff = how_many_used - how_many_cached
            if diff <= 0:
                continue
            for x in range(diff):
                new_usage.append(usage_row)

        # OrderedDict is unhashable, creating unique list manually
        unique_cached_usage = []
        for x in cached_usage:
            if x not in unique_cached_usage:
                unique_cached_usage.append(x)

        usage_to_remove = []
        for usage_row in unique_cached_usage:
            how_many_cached = cached_usage.count(usage_row)
            how_many_used = current_usage.count(usage_row)
            diff = how_many_cached - how_many_used
            if diff <= 0:
                continue
            for x in range(diff):
                usage_to_remove.append(usage_row)
        return new_usage, usage_to_remove

    def _get_same_commodity_row(self, current_value, usage_row):
        for current_row in current_value:
            current_commodity_id = current_row['commodity'].id
            current_warehouse_id = current_row['warehouse'].id
            if usage_row['commodity']['id'] != current_commodity_id:
                continue
            if usage_row['warehouse'] != current_warehouse_id:
                continue
            return current_row
        return None

    def get_fields(self):
        fields = super(PaymentTransactionPartSerializer, self).get_fields()
        pos = self.context['pos']

        if not pos.tips_enabled:
            del fields['tip']

        if self.context.get('customer_api'):
            del fields['operator']

        if self.context.get('deposit_mode'):
            fields['bookings'].required = False
            fields['deposits'].required = True

        return fields

    @staticmethod
    def _sum_rows_by_key(rows: t.List[t.Dict], key: str) -> Decimal:
        return round_currency(
            sum((row.get(key) or Decimal(0) for row in rows or [])),
        )

    def validate_issuing_staffer(self, issuing_staffer):
        if issuing_staffer:
            return issuing_staffer

        business = self.context['pos'].business
        operator = self.context.get('operator')

        return Resource.objects.filter(
            staff_user=operator if operator else business.owner,
            type=Resource.STAFF,
            business=business,
        ).first()

    def _validate_old_txn(self, data):
        edit = self.context.get('edit', None)
        park_sale = self.context.get('park_sale', None)
        old_txn = self.context.get('old_txn', None)

        if french_certification_enabled(self.context['pos'].business_id) and old_txn:
            fc_validate_transaction_rows_for_prepayment(data['rows'], old_txn)

        if not edit:  # it's not edit transaction mode
            return

        # HACK. If transaction is only cloned, we don't need to validate
        # >>>>
        if self.context.get('cached_payment_rows'):
            return
        # <<<<

        if park_sale and edit:
            raise serializers.ValidationError(_('You cannot park already paid transaction.'))

        if not old_txn:
            raise serializers.ValidationError(
                _('To edit transaction parent transaction must be provided.'), code='no_parent'
            )

        compatibilities = [
            key for key, value in list(self.context.get('compatibilities', {}).items()) if value
        ]
        payment_type = data.get('payment_type')

        if self.context.get('old_txn'):
            if (
                payment_type
                and payment_type.compatibility_requiring
                and payment_type.code not in compatibilities
            ):
                raise serializers.ValidationError(_('UPGRADE!'))

        status_code = old_txn.latest_receipt.status_code

        if status_code == receipt_status.ARCHIVED:
            raise serializers.ValidationError(
                {
                    'latest_receipt.status_code': _('Can\'t edit already archived transaction'),
                },
                code='archived',
            )

        if old_txn.lock:
            raise serializers.ValidationError(
                {'payment_type_code': _('Can\'t edit this transaction')}, code='locked'
            )

    def _validate_register(self, data, is_deposit):
        """
        Validating register for transaction.
        It should be run in .validate()

        :return Empty dict or Dict with register object
        """
        if (
            self.context.get('prepayment', None)
            or self.context.get('import', False)
            or self.context.get('direct_payment', False)
            or self.context.get('skip_register', False)
            or self.context.get('booksy_pay', None)
            or self.context.get('is_bgc', None)
        ):
            return {}
        if is_deposit:
            return {}
        if not self.context['pos'].registers_enabled:
            return {}

        if data.get('parent_txn'):  # User the same register for child
            parent_txn = data.get('parent_txn')
            if parent_txn.register:
                if not parent_txn.register.is_open:
                    raise serializers.ValidationError(
                        _('You can not edit transaction in closed register'),
                        code='closed_register',
                    )

                return {'register': parent_txn and parent_txn.register}

        if self.context.get('cached_payment_rows', None):
            return {}

        selected_register_id = self.context.get('selected_register_id')

        register = self.context['pos'].get_open_register(
            selected_register_id=selected_register_id,
            operator=self.context.get('operator', None),
            takeover_when_only_one_is_open=(
                'simple_checkout' in self.compatibilities or self.context.get('is_frontdesk')
            ),
        )

        filters = [Q(is_open=True)]
        if not self.context['pos'].registers_shared_enabled:
            filters.append(Q(opened_by=self.context.get('operator', None)))

        register_count = (
            self.context['pos']
            .registers.filter(
                *filters,
            )
            .count()
        )

        if register is None:
            if self.context['pos'].registers_shared_enabled:
                if (
                    # Register is not selected, and API doesn't know how to
                    # select. Only simple_checkout is not supporting
                    # shared_registers directly.
                    selected_register_id is None
                    and register_count > 0
                    and self.is_simple_checkout
                ):
                    raise SharedRegistersNotSupportedError()
                else:
                    raise SharedRegistersSelectOrOpenError()
            else:
                raise SharedRegistersNotOpenedError()

        return {'register': register}

    def _validate_tip(self, data, is_deposit):
        """
        Validating tip. Creating tip object if old format provided.
        It should be run in .validate()

        :return dict with tip object
        """
        part_data = {}

        if (
            self.context.get('is_bgc')
            or sget_v2(data, ['payment_type', 'code']) == PaymentTypeEnum.BOOKSY_GIFT_CARD
        ) and not data.get('tip'):
            #  prevent default tip auto-attaching during creation/edition appointment with
            #  Booksy Gift Card, tip selection possible only during checkout
            part_data['tip'] = {'rate': 0, 'type': 'P'}
            return part_data

        if (
            self.context['pos'].tips_enabled
            and (
                not self.context.get('prepayment', None)
                or (self.context.get('prepayment', None) and data.get('tip'))
            )
            and (
                not self.context.get('booksy_pay', None)
                or (self.context.get('booksy_pay', None) and data.get('tip'))
            )
            and not is_deposit
        ):
            tip = data.get('tip')

            if not tip:
                default_tip = self.context['pos'].default_tip
                part_data['tip'] = {
                    'rate': default_tip.rate if default_tip else 0,
                    'type': 'P',
                }
        else:
            part_data['tip'] = {'rate': 0, 'type': 'P'}

        return part_data

    def _validate_payment_rows(self, data):
        """
        Validating payment methods. Run only when dry_run = False.
        It should be run in .validate()
        """
        payment_rows = data.get('payment_rows', [])
        payment_type = data.get('payment_type')
        is_booksy_gift_card_transaction = PaymentTypeEnum.BOOKSY_GIFT_CARD in [
            sget_v2(payment_row, ['payment_type', 'code']) for payment_row in payment_rows
        ]
        if (not payment_type and 'new_checkout' not in self.context.get('compatibilities', {})) or (
            not any(pr.get('payment_type') for pr in payment_rows)
            and 'new_checkout' in self.context.get('compatibilities', {})
        ):
            raise serializers.ValidationError(
                {
                    'non_field_errors': _('Payment type is required'),
                },
                code='missing_payment_type',
            )

        payment_rows_unlocked = [row for row in payment_rows if not row.get('locked', False)]
        payment_rows_codes = {row['payment_type'].code for row in payment_rows_unlocked}

        if (
            self.is_voucher_partial_redeem
            and PaymentTypeEnum.BLIK in payment_rows_codes
            and not self.context.get('prepayment')
        ):
            self._validate_online_payment_minimal_amount(payment_rows_unlocked)

        self._validate_bcr_minimal_amount(payment_rows_unlocked)

        # if it is booking transaction with prepayment validation is not required
        if self.context.get('prepayment') or self.is_voucher_partial_redeem:
            return {'payment_rows': payment_rows}

        # HACK. If transaction is only cloned, we don't need to validate
        # >>>>
        if self.context.get('cached_payment_rows'):
            return {'payment_rows': payment_rows}
        # <<<<<<

        # Support old aps
        if not payment_rows and payment_type and not payment_type.split:
            payment_rows = [
                {
                    'payment_type': payment_type,
                    'amount': data.get('total'),
                    'voucher': None,
                    'voucher_service': None,
                }
            ]
            payment_rows_codes = {row['payment_type'].code for row in payment_rows_unlocked}

        for row in payment_rows:
            if 'amount' not in row:
                raise serializers.ValidationError(
                    {
                        'payment_rows': _(
                            'Amount to be charged must be greater than 0 to complete payment.',
                        ),
                    },
                    code='missing_payment_row_amount',
                )
            if row.get('amount', Decimal(0)) < 0 and not self.context.get('import'):
                raise serializers.ValidationError(
                    {
                        'payment_rows': _(
                            'Amount to be charged must be greater than 0 to complete payment.'
                        ),
                    },
                    code='negative_amount',
                )

        payment_rows_amount = sum(row.get('amount', Decimal(0)) for row in payment_rows)

        if (
            round_currency(data['total']) != round_currency(payment_rows_amount)
            and not is_booksy_gift_card_transaction
        ):
            log.error(
                'Split payment rows are not summing up.\n'
                'data: {} \ncontext: {} \nprs: {}\n'.format(
                    force_str(data),
                    force_str(self.context),
                    payment_rows_amount,
                )
            )
            raise serializers.ValidationError(
                {
                    'non_field_errors': _(
                        'Split payment rows are not summing up to transaction total'
                    )
                },
                code='wrong_sum_payment_rows',
            )

        if PaymentTypeEnum.SPLIT in payment_rows_codes:
            raise serializers.ValidationError(
                {
                    'non_field_errors': _(
                        'You can not use split payment method in split payment rows.'
                    )
                },
                code='nested_split',
            )

        no_multi_usage = [
            row['payment_type']
            for row in payment_rows_unlocked
            if not row['payment_type'].multi_usage
        ]

        no_multi_usage_set = {
            row['payment_type']
            for row in payment_rows_unlocked
            if not row['payment_type'].multi_usage
        }

        if len(no_multi_usage) != len(no_multi_usage_set):  # doubled code
            raise serializers.ValidationError(
                {'non_field_errors': _('Each payment method can be used only once')},
                code='doubled_method',
            )

        # during editing transaction payment_type with callback_requiring
        # shouldn't be allowed
        if any(row['payment_type'].should_lock for row in payment_rows) and self.context.get(
            'edit', None
        ):
            raise serializers.ValidationError(
                {
                    'non_field_errors': _(
                        'You can not use this payment method ' 'during editing transaction.'
                    )
                },
                code='locked',
            )

        if any(row['payment_type'].forbidden_in_free_transaction for row in payment_rows) and data[
            'total'
        ] <= Decimal(0):
            raise serializers.ValidationError(
                {
                    'non_field_errors': _(  # not 'payment_type' for legacy reasons
                        'You can not pay for free transactions.'
                    ),
                },
                code='free_transaction',
            )

        if any(row['payment_type'].pay_by_app for row in payment_rows) and not data['customer_id']:
            if safe_get(
                data, ['transaction_type']
            ) != Transaction.TRANSACTION_TYPE__CANCELLATION_FEE or not self.get_is_family_and_friends(
                data
            ):
                raise serializers.ValidationError(
                    {
                        'payment_type': _('To pay by app the customer must be a Booksy user'),
                    },
                    code='no_booksy_user',
                )

        if any(
            row
            for row in payment_rows
            if row['payment_type'].code
            in [
                PaymentTypeEnum.EGIFT_CARD,
                PaymentTypeEnum.PACKAGE,
                PaymentTypeEnum.MEMBERSHIP,
            ]
            and not row.get('voucher')
        ):
            raise serializers.ValidationError(
                {'payment_rows': _('Voucher ID is required')}, code='voucher_id_required'
            )

        if any(
            [
                row
                for row in payment_rows
                if row['payment_type'].code
                in [
                    PaymentTypeEnum.PACKAGE,
                    PaymentTypeEnum.MEMBERSHIP,
                ]
                and row.get('voucher')
                and not row.get('voucher_service')
            ]
        ):
            raise serializers.ValidationError(
                {'payment_rows': _('Voucher Service is required in Package')},
                code='voucher_service_required',
            )

        # Check if Locked PaymentRow
        if self.context.get('edit') is not None:  # Fake and Real Edit
            old_txn = self.context.get('old_txn')
            old_payment_rows = old_txn.latest_receipt.payment_rows.all()
            old_locked_rows = [row.id for row in old_payment_rows if row.locked]

            locked_payment_rows = [row.get('id') for row in payment_rows if row.get('locked')]

            if any(row_id not in locked_payment_rows for row_id in old_locked_rows):
                raise serializers.ValidationError(
                    {
                        'payment_rows': _('Locked row is missing!'),
                    },
                    code='missing_locked_row',
                )

            # Check if tip is bigger than already paid
            tip_already_paid = sum(
                row.get('tip_amount') or Decimal(0) for row in payment_rows if row.get('locked')
            )
            old_tip_already_paid = sum(
                row.tip_amount or Decimal(0) for row in old_payment_rows if row.locked
            )
            if round_currency(old_tip_already_paid) != round_currency(tip_already_paid):
                raise serializers.ValidationError(
                    {
                        'tip': _('Missing part of tip!'),
                    },
                    code='missing_tip_part',
                )

            # Check if customer was changed
            if old_txn.customer_id != data.get('customer_id') and locked_payment_rows:
                raise serializers.ValidationError(
                    {
                        'customer_card_id': _(
                            'You cannot change customer when locked payment ' 'row is present!'
                        )
                    },
                    code='changed_customer',
                )

        self._validate_online_payment_minimal_amount(payment_rows_unlocked)
        self._validate_bcr_minimal_amount(payment_rows_unlocked)

        for pr in payment_rows:
            pr.pop('mode', None)

        return {'payment_rows': payment_rows}

    @staticmethod
    def _validate_online_payment_minimal_amount(unlocked_payment_rows: list) -> None:
        amount = sum(Decimal(safe_get(pr, ['amount']) or 0) for pr in unlocked_payment_rows)
        payment_type_codes = [
            safe_get(pr, ['payment_type', 'code']) for pr in unlocked_payment_rows
        ]

        is_mobile_payment = (
            enums.PAY_BY_APP in payment_type_codes or PaymentTypeEnum.BLIK in payment_type_codes
        )
        if not is_mobile_payment:
            return

        minimal_payment = get_minimal_pba_amount()
        if amount < minimal_payment:
            raise serializers.ValidationError(
                {
                    'payment_rows': _('Amount must be greater or equal to {}').format(
                        format_currency(minimal_payment),
                    )
                },
                code='pay_by_app_minimal_amount',
            )

    @staticmethod
    def _validate_bcr_minimal_amount(unlocked_payment_rows: list) -> None:
        minimal_payment = major_unit(MINIMAL_PAYMENT_AMOUNT)

        bcr_rows = [
            pr
            for pr in unlocked_payment_rows
            if safe_get(pr, ['payment_type', 'code']) in PaymentTypeEnum.terminal_methods()
        ]

        if any(
            [
                bcr_row
                for bcr_row in bcr_rows
                if (safe_get(bcr_row, ['amount']) or 0) <= minimal_payment
            ]
        ):
            raise serializers.ValidationError(
                {
                    'payment_rows': _('Amount must be greater or equal to {}').format(
                        format_currency(minimal_payment)
                    )
                },
                code='bcr_minimal_amount',
            )

    def _inject_txn_hash_and_cache(self, data):
        if not self.context.get('business'):
            return data
        txn_hash = data.get('txn_hash')
        if not txn_hash:
            txn_hash = uuid.uuid4().hex
            data['txn_hash'] = txn_hash
        cache_key = self._get_cache_key(txn_hash)
        cache_content = self._get_cache_content(data)
        cache.set(cache_key, cache_content, 10800)
        return data

    def _get_usage_from_default(self):
        default_usage = self.get_default_commodity_usage(None)
        usage_rows = []
        for default_row in default_usage:
            usage_rows.append(
                {
                    'commodity': Commodity.objects.get(
                        id=default_row['commodity']['id'],
                    ),
                    'count': default_row['count'],
                    'warehouse': Warehouse.objects.get(
                        id=default_row['warehouse'],
                    ),
                }
            )
        return usage_rows

    def _get_cache_content(self, data):
        content = {
            'commodity_usage': data.get('commodity_usage') or [],
            'bookings': self.initial_data.get('bookings') or [],
            'default_commodity_usage': self.get_default_commodity_usage(None),
        }
        return content

    def get_payment_types_for_transaction(
        self,
        pos: POS,
        compatibilities: t.List[str],
        is_edit: bool,
        data: dict,
    ) -> t.List:
        payment_type_filters = [Q(enabled=True), Q(deleted__isnull=True), Q(available=True)]
        if 'simple_checkout' in compatibilities:
            payment_type_filters.append(~Q(code=PaymentTypeEnum.SPLIT))
        if STRIPE_TERMINAL not in compatibilities:
            payment_type_filters.append(~Q(code=PaymentTypeEnum.STRIPE_TERMINAL))

        appointment = safe_get(data, ['appointment'])
        exclude_pay_by_app = self.get_is_family_and_friends(data)
        # 72435. Force pay by app payment if the booking contains service with
        # cancellation fee, and client has active payment card, and there was
        # no previous failed pay_by_app attempt
        if pos.force_pba_for_cf and appointment and not exclude_pay_by_app:
            is_deposit = (
                data.get('deposit')
                and data['deposit'].transaction_type
                == Transaction.TRANSACTION_TYPE__CANCELLATION_FEE
            )
            client_has_card_query = PaymentMethod.objects.filter(
                user_id=data['customer_id'],
                active=True,
                default=True,
            )
            last_payment_failed_query = data['appointment'].transactions.filter(
                latest_receipt__status_code__in=(
                    receipt_status.FAILED_STATUSES + receipt_status.CANCELED_STATUSES
                )
            )
            should_force_pba = (
                is_deposit
                and client_has_card_query.exists()
                and not last_payment_failed_query.exists()
            )
            if should_force_pba:
                payment_type_filters.append(Q(code=PaymentTypeEnum.PAY_BY_APP))

        # Filter supported payment types
        payment_types = pos.payment_types.filter(
            *payment_type_filters,
        ).exclude(code=PaymentTypeEnum.BOOKSY_GIFT_CARD)
        if ReorganizePaymentTypeTilesEnabled():
            payment_types.order_by('order')
        else:
            payment_types.order_by('-default', 'order')  # return default always first

        return [
            x
            for x in payment_types
            if not (
                # Don't add payment_type requiring callback in edit screen
                x.should_lock
                and is_edit
            )
            and not (
                # Don't add payment_type which app doesn't support
                x.compatibility_requiring
                and x.code not in compatibilities
            )
            and not x.forbidden_in_checkout
        ]

    @property
    def compatibilities(self) -> list:
        return [
            key for key, value in list(self.context.get('compatibilities', {}).items()) if value
        ]

    @property
    def is_voucher_partial_redeem(self) -> bool:
        return 'voucher_partial_redeem' in self.compatibilities

    @staticmethod
    def total_(instance):
        return safe_get(instance, ['total']) or Decimal(0)

    @staticmethod
    def _already_paid(instance: t.Union[Transaction, dict]) -> Decimal:
        path = ['latest_receipt', 'already_paid']
        if isinstance(instance, dict):
            path = ['already_paid']

        return safe_get(instance, path) or Decimal(0)

    def get_total_wo_prepayment(
        self,
        instance: t.Union[Transaction, dict],
    ) -> str:
        return str(self._total_wo_prepayment(instance))

    def _total_wo_prepayment(
        self,
        instance: t.Union[Transaction, dict],
    ) -> Decimal:
        total = self.total_(instance)
        already_paid = self._already_paid(instance)
        total_wo_prepayment = total - already_paid

        return total_wo_prepayment

    def get_total_wo_prepayment_w_remaining_tip(
        self,
        instance: t.Union[Transaction, dict],
    ) -> str:
        total_wo_prepayment = self._total_wo_prepayment(instance)

        tip_amount = safe_get(instance, ['tip', 'amount']) or Decimal(0)
        if isinstance(instance, Transaction) and instance.has_tip:
            tip_amount_remaining = TransactionTipSerializer(
                instance=instance.tip,
                context=self.context,
            ).data.get('amount_remaining') or Decimal(0)
        else:
            tip_amount_remaining = safe_get(
                instance,
                ['tip', 'amount_remaining'],
            ) or Decimal(0)

        if tip_amount_remaining:
            total_wo_prepayment -= tip_amount - tip_amount_remaining

        return str(total_wo_prepayment)

    @staticmethod
    def filter_draft_to_complete_payment_rows(data: dict) -> list:
        return [
            row
            for row in data.get('payment_rows', [])
            if row.get('mode') == PaymentRow.PAYMENT_ROW_MODE__COMPLETE
        ]

    @staticmethod
    def filter_all_payment_rows(data: dict) -> list:
        return [row for row in data.get('payment_rows', []) if row.get('payment_type')]

    @staticmethod
    def filter_draft_payment_rows(data: dict) -> list:
        return [
            row
            for row in data.get('payment_rows', [])
            if row.get('mode') == PaymentRow.PAYMENT_ROW_MODE__KEEP
        ]

    @staticmethod
    def filter_non_draft_payment_rows(data: dict) -> list:
        return [row for row in data.get('payment_rows', []) if not row.get('mode', False)]

    @staticmethod
    def filter_used_codes(data: dict) -> set:
        return {
            safe_get(row, ['payment_type', 'code'])
            for row in data.get('payment_rows', [])
            if not row.get('locked', False)
        }

    @staticmethod
    def get_default_payment_type(
        payment_type_choices: list, used_codes: set, excluded_pay_by_app: bool
    ) -> PaymentType:
        excluded_codes = copy.copy(used_codes)
        if excluded_pay_by_app:
            excluded_codes.add(PaymentTypeEnum.PAY_BY_APP)
        available_payment_types = [
            payment_type
            for payment_type in payment_type_choices
            if payment_type.code not in excluded_codes
        ]

        return firstof(
            payment_type for payment_type in available_payment_types if payment_type.default
        ) or firstof(available_payment_types)

    @staticmethod
    def filter_locked_payment_rows(data: dict) -> list:
        return [row for row in data.get('payment_rows', []) if row.get('locked', False)]

    @staticmethod
    def _initial_payment_row_validation(data):
        payment_rows = data.get('payment_rows') or []

        for pr in payment_rows:
            if not (pr.get('payment_type') or pr.get('mode')):
                raise serializers.ValidationError(
                    {
                        'payment_rows': _('Either payment type or mode is required'),
                    },
                    code='payment_type_or_mode_required',
                )
        mode_c_rows = [
            pr for pr in payment_rows if pr.get('mode') == PaymentRow.PAYMENT_ROW_MODE__COMPLETE
        ]

        if len(mode_c_rows) > 1:
            raise serializers.ValidationError(
                {
                    'payment_rows': _('There can be only one Payment Row in Complete mode'),
                },
                code='only_one_mode_c',
            )

    @staticmethod
    def _initial_family_and_friends_payment_row_validation(data):
        initial_family_and_friends_payment_row_validation(data, data.get('payment_type'))
        for pr in data.get('payment_rows') or []:
            initial_family_and_friends_payment_row_validation(data, pr.get('payment_type'))

    def _inject_payment_type(self, data):
        used_codes = self.filter_used_codes(data)
        draft_to_complete = self.filter_draft_to_complete_payment_rows(data)
        draft = self.filter_draft_payment_rows(data)
        data['payment_rows'] = self.filter_non_draft_payment_rows(data)
        payment_rows = data.get('payment_rows') or []
        locked_rows = self.filter_locked_payment_rows(data)

        data['payment_type_choices'] = self.get_payment_types_for_transaction(
            pos=self.context['pos'],
            compatibilities=self.compatibilities,
            is_edit=self.context.get('edit', False),
            data=data,
        )

        already_paid = self._sum_rows_by_key(locked_rows, 'amount')
        split_payment_amount = self._sum_rows_by_key(payment_rows, 'amount')
        split_payment_remaining = data['total'] - split_payment_amount

        total_appointment_without_tip = round_currency(
            sum(
                (
                    data.get('taxed_subtotal_services') or Decimal(0),
                    data.get('taxed_subtotal_products') or Decimal(0),
                )
            )
        )

        data.update(
            {
                'already_paid': already_paid,
                'split_payment_remaining': split_payment_remaining,
                'total_appointment_without_tip': total_appointment_without_tip,
            }
        )

        if draft_to_complete:
            row_to_complete = draft_to_complete[0]
            row_to_complete['amount'] = data['split_payment_remaining']

            payment_type = row_to_complete.get('payment_type')
            if payment_type and payment_type.code in [
                PaymentTypeEnum.MEMBERSHIP,
                PaymentTypeEnum.PACKAGE,
            ]:
                # If there is remaining amount to be paid and payment_row is
                # posted with mode C (complete) it would fill the amount into
                # package/membership payment row, which shouldn't happen
                row_to_complete['amount'] = 0
                row_to_complete.pop('mode', None)
            if 'payment_type' not in draft_to_complete[0]:
                exclude_pay_by_app = self.get_is_family_and_friends(data)
                row_to_complete['payment_type'] = self.get_default_payment_type(
                    data['payment_type_choices'], used_codes, exclude_pay_by_app
                )
                row_to_complete['voucher'] = None

            # Hack for Giftcards.
            # If rowC exceeds voucher value change to split
            if (
                row_to_complete['payment_type'].code == PaymentTypeEnum.EGIFT_CARD
                and row_to_complete.get('voucher')
                and row_to_complete['amount'] > row_to_complete['voucher'].current_balance
            ):
                row_to_complete['amount'] = row_to_complete['voucher'].current_balance
                row_to_complete.pop('mode', None)

            if not self.is_simple_checkout:
                # For full checkout in Frontdesk, modeC may be also used in split payment.
                data['split_payment_remaining'] = (
                    data['split_payment_remaining'] - row_to_complete['amount']
                )

            # Hack for Memberships and Packages
            # if there is more than 1 service or any merchandise rows
            # change to split
            service_rows = [
                row for row in data['rows'] if row.get('service_variant') or row.get('subbooking')
            ]

            merchandise_rows = [
                row for row in data['rows'] if row.get('voucher') or row.get('product')
            ]

            if (
                row_to_complete['payment_type'].code
                in [PaymentTypeEnum.PACKAGE, PaymentTypeEnum.MEMBERSHIP]
                and row_to_complete.get('voucher')
                and row_to_complete.get('voucher_service')
                and (merchandise_rows or len(service_rows) > 1)
            ):
                row_to_complete['amount'] = 0
                row_to_complete.pop('mode', None)

            # hack for tip_amount when appointment is paid but not whole tip
            if split_payment_remaining and already_paid > total_appointment_without_tip:
                row_to_complete['tip_amount'] = split_payment_remaining

        # inject split_payments related fields
        excluded_codes = used_codes | {
            PaymentTypeEnum.PREPAYMENT,
            PaymentTypeEnum.BOOKSY_PAY,
            PaymentTypeEnum.SPLIT,
            PaymentTypeEnum.KEYED_IN_PAYMENT,
        }
        data['split_payment_choices'] = [
            x
            for x in data['payment_type_choices']
            if x.code not in excluded_codes
            and not (
                (x.code == PaymentTypeEnum.PAY_BY_APP and PaymentTypeEnum.SQUARE in used_codes)
                or (x.code == PaymentTypeEnum.SQUARE and PaymentTypeEnum.PAY_BY_APP in used_codes)
                or (x.code == PaymentTypeEnum.PAY_BY_APP and PaymentTypeEnum.BLIK in used_codes)
                or (x.code == PaymentTypeEnum.BLIK and PaymentTypeEnum.PAY_BY_APP in used_codes)
            )
        ]

        if draft_to_complete:
            data['payment_rows'].extend(draft_to_complete)
        if draft:
            data['payment_rows'].extend(draft)

        payment_amount = self._sum_rows_by_key(data['payment_rows'], 'amount')
        data['payment_remaining'] = data['total'] - payment_amount

    @staticmethod
    def _change_from_cash_received(
        cached_received: Decimal,
        amount_to_pay: Decimal,
    ) -> Decimal:
        return cached_received - amount_to_pay

    @staticmethod
    def _get_bci_for_promotions(data):
        appointment = safe_get(data, ['appointment'])
        if appointment and appointment.is_family_and_friends:
            return appointment.booked_by

    def validate(self, data):
        """Final validate and create Transaction data."""
        data = super(PaymentTransactionPartSerializer, self).validate(data)
        # ServicePromotions
        data = self.apply_promotions(data, self._get_bci_for_promotions(data))

        # For editable summaries
        self.context['dry_run'] = data.get('dry_run')
        # while creating Transaction currency always valid
        self.context['valid_currency'] = True

        # Temporary variables - shorting code
        pos = self.context['pos']
        is_deposit = data['transaction_type'] == Transaction.TRANSACTION_TYPE__CANCELLATION_FEE

        # Setting basic
        data['currency_symbol'] = settings.CURRENCY_CODE
        data['operator'] = self.context.get('operator')

        # Validate old_txn
        self._validate_old_txn(data)

        # Validate taxes and vouchers for France
        if french_certification_enabled(pos.business_id):
            fc_validate_transaction_rows(data=data)

        # Register
        register_part = self._validate_register(data, is_deposit)
        data.update(register_part)

        # Tip
        tip_part = self._validate_tip(data, is_deposit)
        data.update(tip_part)

        data['discount_rate'] = data.get('discount_rate', Decimal(0))

        # Only validates here
        self.common_voucher_validation(data)
        # Mark bookings paid via packages
        # It can modify data['rows']
        # package_part = self._validate_package_rows(data)
        # data.update(package_part)

        # Mark bookings paid via memberships
        # It can modify data['rows']
        membership_part = self.validate_memberships_rows(data)
        data.update(membership_part)

        # Calculating
        calculated, error = calculations.calculate(
            data, calculations.get_pos_settings(pos), self.context
        )
        if error:
            raise serializers.ValidationError(**error)
        data.update(calculated)
        data.update(self.receipt_footer_lines)

        for usage in data.get('commodity_usage', []):
            if usage['commodity'].archived:
                raise serializers.ValidationError(
                    _('Attempt to use an archived commodity in ' 'product consumption')
                )

        self._initial_payment_row_validation(data)
        self._initial_family_and_friends_payment_row_validation(data)

        if data['dry_run']:
            # Injecting data connected to payment_type
            self._inject_payment_type(data)

            # validate voucher payment
            egift_card_part = self.validate_egift_card_rows(data)
            data.update(egift_card_part)
        else:
            # Validating payments
            payment_part = self._validate_payment_rows(data)
            data.update(payment_part)

            # eGift card Row was filled up with 'dry_run' mode. No only validate
            self.validate_egift_card_rows(data)

            # Check if after summing total field still has less then 10 digits
            validate_10_digits('total', _('Total amount exceeds 10 digits'), data['total'])

            # Check if after summing register
            # counted_total still has less 10 digits
            if 'register' in data:
                validate_10_digits(
                    'counted_total',
                    _('This amount exceeds 10 digits in cash register'),
                    data['register'].counted_total + data['total'],
                )

            # For saving: prices are required
            if len(data['rows']) == 0 or any([b.get('incomplete_price') for b in data['rows']]):
                raise serializers.ValidationError(
                    {
                        'non_field_errors': _('Service prices are missing'),
                    }
                )

            # Setting params required for saving
            data.update(
                {
                    'business_name': pos.business.name,
                    'business_address': pos.business.get_location_name(with_city=True)[:250],
                    # 44950. Deposit should be always TAX_MODE_INCLUDED
                    'service_tax_mode': pos.service_tax_mode,
                    'product_tax_mode': pos.product_tax_mode,
                }
            )

            self.validate_bgc_appointment_can_be_checked_out(data)
        self._inject_txn_hash_and_cache(data)
        self._validate_kip_max_amount(rows=data['payment_rows'])
        return data

    def validate_payment_token(self, value):
        """
        This method will only be called if payment_token is included in the request.
        If the field is not in the request, this validation method won't be triggered.
        """
        if not re.match(r'^pm_[a-zA-Z0-9]{24,}$', value):
            raise serializers.ValidationError("Invalid Stripe payment method ID format")

        return value

    @staticmethod
    def _validate_kip_max_amount(rows: OrderedDict) -> None:
        for row in rows:
            if row['payment_type'].code == PaymentTypeEnum.KEYED_IN_PAYMENT.value and row[
                'amount'
            ] > MAX_KEYED_IN_PAYMENT_TRANSACTION_AMOUNT.get(
                settings.API_COUNTRY, MAX_KEYED_IN_PAYMENT_TRANSACTION_AMOUNT.get('default')
            ):
                raise ValidationError(
                    _(
                        "Keyed In Payment is not available for this transaction. Try another Booksy Payment method."
                    )
                )

    def validate_bgc_appointment_can_be_checked_out(self, data) -> None:
        if not BooksyGiftcardsCheckoutTimeValidationFlag():
            return
        if not self.context.get('biz_endpoint'):
            return

        if appointment := safe_get(data, ['appointment']):
            current_time = datetime.now(timezone.utc)
            appointment_date = appointment.booked_from

            if appointment.is_booksy_gift_card_appointment and appointment_date.replace(
                tzinfo=timezone.utc
            ) - current_time > timedelta(hours=1):
                raise serializers.ValidationError(
                    _(
                        'Appointments with Booksy Gift Cards cannot be checked out earlier than 1 hour before appointment. Please move the appointment to appropriate time.'
                    )
                )

    @property
    def receipt_footer_lines(self) -> dict:
        pos = self.context['pos']
        old_txn = self.context.get('old_txn', None)

        if not old_txn:
            return {
                'receipt_footer_line_1': pos.receipt_footer_line_1,
                'receipt_footer_line_2': pos.receipt_footer_line_2,
            }
        return {
            'receipt_footer_line_1': old_txn.receipt_footer_line_1,
            'receipt_footer_line_2': old_txn.receipt_footer_line_2,
        }

    def create_cancellation_fee_auth(self, data, payment_rows):
        # TODO DELETE AFTER POS REFACTOR WILL BE COMPLETED

        if (appointment := data.get('appointment')) is None and (
            appointment_id := data.get('appointment_id')
        ):
            appointment = Appointment.objects.get(id=appointment_id)

        pr = payment_rows[0]
        payment_type_code = pr['payment_type'].code

        payment_provider_code = TransactionService.get_payment_provider_code(
            pos=self.context['pos'], payment_type_code=payment_type_code
        )

        payment_splits, refund_splits, dispute_splits = TransactionService.calculate_fees(
            pos=self.context['pos'],
            payment_provider_code=payment_provider_code,
            payment_type_code=PaymentTypeEnum(payment_type_code),
        )

        # Get operator_id from context
        operator_id = None
        if 'user' in self.context:
            operator_id = self.context['user'].id
        elif 'request' in self.context and hasattr(self.context['request'], 'user'):
            operator_id = self.context['request'].user.id

        cf_auth = CancellationFeeAuthService.create_auth(
            amount=minor_unit(data['total']),
            appointment_id=appointment.id,
            sender_user_id=data['customer_card'].user.id,
            business_id=self.context['pos'].business_id,
            payment_provider_code=payment_provider_code,
            payment_method_type=PaymentRowService.map_payment_type_code(payment_type_code),
            payment_splits=payment_splits,
            refund_splits=refund_splits,
            dispute_splits=dispute_splits,
            operator_id=operator_id,
            basket_id=data.get('basket_id'),
        )

        data['cancellation_fee_auth_id'] = cf_auth.id

    def check_stage_2(self) -> bool:
        editing_process = self.context.get('editing_appointment_process')
        if editing_process:
            return txn_refactor_stage2_enabled(self.context.get('old_txn'))
        return True

    def get_or_create_basket(self, data):
        # TODO DELETE AFTER POS REFACTOR WILL BE COMPLETED

        if self.context.get('edit') is False and (
            basket_id := safe_get(self.context, ['old_txn', 'basket_id'])
        ):
            # Its fake edit
            basket = BasketService.get_basket(basket_id)
        else:
            customer_card = data.get('customer_card')
            basket = BasketService.create_basket(
                business_id=self.context['pos'].business_id,
                customer_card_id=customer_card and customer_card.id or None,
            )

        data['basket_id'] = basket.id

        return basket

    @atomic
    def create(self, validated_data):
        data = copy.deepcopy(validated_data)

        if data.pop('dry_run'):
            raise RuntimeError('You can not save when dry_run is enabled.')

        data.pop('payment_type', None)

        payment_rows = data.pop('payment_rows')
        tip = data.pop('tip')
        if tip:
            tip.pop('already_paid')
            tip.pop('amount_remaining', None)

        data['pos'] = self.context['pos']
        data.pop('dry_run', None)

        ignore_analytics = self.context.pop('ignore_analytics', False)
        # Transaction object doesn't have these fields
        data.pop('tip_choices', None)
        data.pop('staffer_tip_choices', None)
        data.pop('voucher_limits', None)
        data.pop('voucher_services', None)
        data.pop('tax_excluded', None)
        payment_token = data.pop('payment_token', None)

        appointment = data.pop('multibooking', None)
        appointment_id = data.pop('multibooking_id', None)
        booking = data.pop('subbooking', None)
        if appointment:
            data['appointment'] = appointment
        elif booking:
            data['appointment_id'] = booking.appointment_id
        elif appointment_id:
            data['appointment_id'] = appointment_id

        rows = data.pop('rows')
        for i, row in enumerate(rows, 0):
            row['order'] = i
            row.pop('service_name', None)
            row.pop('voucher_paid', None)

            if 'override_item_price' in row:
                # We cannot override before, because in case of changing payment
                # method we need to know basic price
                row['item_price'] = row['override_item_price']
                row.pop('override_item_price')

        tax_subtotals = data.pop('tax_subtotals')

        rows = self._handle_commissions(rows)

        for row in payment_rows:
            row.pop('service_amount', None)
            row.pop('mode', None)

        if 'commodity_usage' in data:
            commodity_usage_rows = data.pop('commodity_usage', []) or []
        else:
            commodity_usage_rows = self._get_usage_from_default()
        data.pop('txn_hash', None)
        data.pop('cash_info', None)
        issuing_staffer = data.pop('issuing_staffer')
        confirming_staffer = data.pop('confirming_staffer', None)
        note = data.pop('note', None)

        # TODO DELETE AFTER POS REFACTOR WILL BE COMPLETED
        basket = None
        if self.check_stage_2():
            if not self.context.get('editing_appointment_process'):
                if self.context.get('deposit_mode') is True:
                    # TODO::FLS-3976
                    self.create_cancellation_fee_auth(data, payment_rows)
                else:
                    basket = self.get_or_create_basket(data)
            else:
                parent_txn = self.context.get('old_txn')
                data['cancellation_fee_auth_id'] = parent_txn.cancellation_fee_auth_id
                data['basket_id'] = parent_txn.basket_id

                if data['basket_id']:
                    basket = BasketService.get_basket(basket_id=data['basket_id'])
        # <<

        transaction = Transaction.objects.create(**data)

        rows = self.create_voucher(rows)
        rows = self._create_addons_use(rows)
        rows = self._exclude_row_hashes(rows)
        rows = self._exclude_total_wo_discount(rows)

        # TODO DELETE AFTER POS REFACTOR WILL BE COMPLETED
        if self.check_stage_2() and basket:
            TransactionService.create_basket_items(basket=basket, rows=rows)
        # <<<

        TransactionRow.objects.bulk_create(
            [TransactionRow(transaction=transaction, **row) for row in rows]
        )

        TransactionTaxSubtotal.objects.bulk_create(
            [TransactionTaxSubtotal(transaction=transaction, **tax) for tax in tax_subtotals]
        )

        self._create_rw_documents(
            commodity_usage_rows,
            transaction,
            issuing_staffer,
            confirming_staffer,
            note,
        )
        self._create_wz_documents(
            rows,
            transaction,
            issuing_staffer,
            confirming_staffer,
            note,
        )

        tip_rows = tip.pop('tip_rows', [])
        # TODO DELETE AFTER POS REFACTOR WILL BE COMPLETED
        if self.check_stage_2() and basket:
            if self.context.get('prepayment'):
                basket_tip_source = BasketTipSource.PREPAYMENT
            elif self.context.get('booksy_pay'):
                basket_tip_source = BasketTipSource.BOOKSY_PAY
            else:
                basket_tip_source = BasketTipSource.CHECKOUT

            TransactionTipService.create_basket_tips(
                basket=basket,
                txn=transaction,
                tip=tip,
                tip_rows=tip_rows,
                source=basket_tip_source,
                tip_already_paid=PaymentRowService.get_tip_already_paid(payment_rows),
            )
        # <<<

        transaction_tip = TransactionTip.objects.create(transaction=transaction, **tip)
        TransactionTipRow.objects.bulk_create(
            [TransactionTipRow(transaction_tip=transaction_tip, **tip_row) for tip_row in tip_rows]
        )

        if self.context.get('cached_payment_rows', None):
            payment_rows = self.context.get('old_txn').clone_payment_rows(
                self.context.get('cached_payment_rows')
            )
        else:
            last_payment_row = payment_rows[-1]
            self._calculate_tip_amount_for_payment_row(
                last_payment_row, payment_rows, transaction, validated_data
            )

            payment_rows = self._update_payment_rows_statuses(
                transaction,
                validated_data,
                payment_rows,
            )
            payment_rows = [PaymentRow.create_with_status(**row) for row in payment_rows]

        if payment_token:
            with tracer.trace(
                DatadogOperationNames.KIP_CHECKOUT, service=DatadogCustomServices.KIP
            ):
                set_apm_tag_in_current_span(MANUAL_KEEP_KEY)
                transaction.update_status(
                    payment_rows,
                    ignore_analytics=ignore_analytics,
                    update_basket_payments=not self.context.get('editing_appointment_process'),
                    device_data=self.context.get('device_data'),
                    payment_token=payment_token,
                )
        else:
            transaction.update_status(
                payment_rows,
                ignore_analytics=ignore_analytics,
                update_basket_payments=not self.context.get('editing_appointment_process'),
                device_data=self.context.get('device_data'),
                payment_token=payment_token,
            )

        self._block_booksy_gift_cards_in_deposit_until_transaction_succeeds(
            transaction, payment_rows
        )
        self.update_resolved_client_discount(validated_data)

        return transaction

    def _block_booksy_gift_cards_in_deposit_until_transaction_succeeds(
        self, transaction: Transaction, payment_rows: t.List[PaymentRow]
    ) -> None:
        """
        Due to logic of the locked property in payment row, we need to assure that for BGC transfers
        status will stay in Deposit for split payments until the other payment finishes successfully
        """
        if DisableBGCStatusChangeIfFailedPayment():
            return

        if transaction.latest_receipt.status_code == receipt_status.PAYMENT_SUCCESS:
            return

        bgc_payments = list(
            filter(
                lambda payment: payment.payment_type.code == PaymentTypeEnum.BOOKSY_GIFT_CARD,
                payment_rows,
            )
        )
        for bgc_payment in bgc_payments:
            bgc_payment.status = receipt_status.GIFT_CARD_DEPOSIT
            bgc_payment.save(update_fields=["status"])

    def _create_addons_use(self, transaction_rows):
        for row in transaction_rows:
            if addon := row.pop('addon', None):
                addon_use = addon_use_make(addon=addon, quantity=row.get('quantity', 1))
                addon_use.save()
                row['addon_use'] = addon_use

        return transaction_rows

    @staticmethod
    def _exclude_row_hashes(transaction_rows):
        for row in transaction_rows:
            row.pop('row_hash_uuid', None)

        return transaction_rows

    @staticmethod
    def _exclude_total_wo_discount(transaction_rows):
        for row in transaction_rows:
            row.pop('total_price', None)
            row.pop('total_wo_discount', None)

        return transaction_rows

    def _update_payment_rows_statuses(
        self,
        transaction: Transaction,
        validated_data: dict,
        payment_rows: list,
    ) -> list:
        is_deposit = transaction.transaction_type == Transaction.TRANSACTION_TYPE__CANCELLATION_FEE

        payment_rows_sum = sum(pr.get('amount', 0) for pr in payment_rows)
        is_payment_with_egift_card = PaymentTypeEnum.EGIFT_CARD in (
            safe_get(pr, ['payment_type', 'code']) for pr in payment_rows if not pr.get('lock')
        )
        maybe_voucher_partial_redeem = (
            not is_deposit
            and is_payment_with_egift_card
            and payment_rows_sum < validated_data['total']
        )

        if maybe_voucher_partial_redeem:
            payment_rows = [
                {
                    'status': (
                        receipt_status.VOUCHER_PARTIAL_REDEEM
                        if self.is_giftcard_payment_row(row)
                        else None
                    ),
                    **row,
                }
                for row in payment_rows
            ]
        else:
            is_park_sale = self.context.get('park_sale', False)
            status = receipt_status.PARK_SALE if is_park_sale else None
            payment_rows = [
                {
                    'status': (
                        receipt_status.CALL_FOR_PAYMENT
                        if safe_get(row, ['payment_type', 'code']) == PaymentTypeEnum.BLIK
                        else status
                    ),
                    'is_deposit': is_deposit,
                    **row,
                }
                for row in payment_rows
            ]

        return payment_rows

    def _calculate_tip_amount_for_payment_row(
        self,
        last_payment_row: t.Dict[str, t.Any],
        payment_rows: t.List[t.Dict[str, t.Any]],
        transaction: Transaction,
        validated_data: t.Dict[str, t.Any],
    ) -> None:
        """Calculate tip amount for the last payment row based on business rules."""
        if last_payment_row.get('tip_amount'):
            return  # Tip amount already set, no need to calculate

        business_id: int = self.context['pos'].business_id
        if not NSPFixedPrize(UserData(subject_key=business_id)):
            self._calculate_tip_for_legacy_rules(
                last_payment_row, payment_rows, transaction, validated_data
            )
        else:
            self._calculate_tip_for_fixed_price_rules(
                last_payment_row, payment_rows, transaction, validated_data
            )

    def _calculate_tip_for_legacy_rules(
        self,
        last_payment_row: t.Dict[str, t.Any],
        payment_rows: t.List[t.Dict[str, t.Any]],
        transaction: Transaction,
        validated_data: t.Dict[str, t.Any],
    ) -> None:
        """Calculate tip amount using legacy rules (simple difference calculation)."""
        payment_rows_sum = sum(pr['amount'] for pr in payment_rows)
        tip_already_paid = safe_get(
            validated_data,
            ['tip', 'already_paid'],
        ) or Decimal(0)
        tip_amount = sum(
            [
                payment_rows_sum,
                -self.total_amount_without_tip(transaction),
                -tip_already_paid,
            ]
        )
        last_payment_row['tip_amount'] = tip_amount if tip_amount > 0 else Decimal(0)

    def _calculate_tip_for_fixed_price_rules(
        self,
        last_payment_row: dict,
        payment_rows: list,
        transaction: Transaction,
        validated_data: dict,
    ) -> None:
        """
        Calculate tip amount using fixed price rules
        with special handling for cancellation fees/prepayments."""
        if self._is_cancellation_fee_or_prepayment_transaction(transaction, payment_rows):
            """Set tip amount from validated data for cancellation fees/prepayments."""
            tip_amount = safe_get(validated_data, ['tip', 'amount']) or Decimal(0)
            last_payment_row['tip_amount'] = tip_amount
        else:
            self._calculate_tip_for_legacy_rules(
                last_payment_row, payment_rows, transaction, validated_data
            )

    @staticmethod
    def _is_cancellation_fee_or_prepayment_transaction(
        transaction: Transaction, payment_rows: list
    ) -> bool:
        """Check if this is a cancellation fee or prepayment transaction."""
        is_cancellation_fee = (
            transaction.transaction_type == Transaction.TRANSACTION_TYPE__CANCELLATION_FEE
        )
        is_prepayment = any(
            pr.get('payment_type', {}).code == PaymentTypeEnum.PREPAYMENT for pr in payment_rows
        )
        return is_cancellation_fee or is_prepayment

    def _create_rw_documents(
        self,
        commodity_usage_rows,
        transaction: Transaction,
        issuing_staffer: Resource,
        confirming_staffer: Resource = None,
        note: str = None,
    ):
        rw_documents_data = {}

        for usage_row in commodity_usage_rows:
            row_warehouse = usage_row['warehouse']
            usage_row.pop('id', None)
            usage_row_object = WarehouseFormulaRow(**usage_row)
            usage_row_object.save()
            usage_row_object.transactions.set([transaction])
            rw_documents_data.setdefault((row_warehouse, transaction), []).append(usage_row)
        for (warehouse, transaction), warehouse_documents in rw_documents_data.items():
            rw_document = WarehouseDocument(
                warehouse=warehouse,
                transaction=transaction,
                type=WarehouseDocumentType.RW,
                issuing_staffer=issuing_staffer,
                confirming_staffer=confirming_staffer,
                note=note,
            )
            rw_document.save()
            rw_document_rows = []
            for document_data in warehouse_documents:
                commodity = document_data['commodity']
                net_price, gross_price, tax = commodity.get_last_financial_info()
                row = WarehouseDocumentRow(
                    commodity_name=commodity.name,
                    commodity=commodity,
                    quantity=document_data['count'],
                    net_price=net_price,
                    gross_price=gross_price,
                    tax=tax,
                    document=rw_document,
                    is_full_package_expenditure=False,
                )
                rw_document_rows.append(row)
                commodity.decrease_volume(
                    warehouse,
                    row.quantity,
                    document_id=rw_document.id,
                    document_type=rw_document.type,
                )
            WarehouseDocumentRow.objects.bulk_create(rw_document_rows)

    def _create_wz_documents(
        self,
        transaction_rows,
        transaction: Transaction,
        issuing_staffer: Resource,
        confirming_staffer: Resource = None,
        note: str = None,
    ):
        products_data = [row for row in transaction_rows if row.get('product')]
        if not products_data:
            return

        default_warehouse = Warehouse.objects.filter(
            business=transaction.pos.business,
            is_default=True,
        ).first()

        wz_documents_data = {}
        for product_data_row in products_data:
            commodity = product_data_row['product']
            warehouse = product_data_row.get('warehouse', default_warehouse)
            quantity = product_data_row['quantity']
            if not commodity or not warehouse:
                raise serializers.ValidationError("Incorrect commodity or warehouse")

            # When front passes 0 in quantity, used commodity should be returned to stock.
            # All calculations are made in pos.calculations in return we get net_total
            # and gross_total of transaction row. To get price of single item we need to
            # calculate it on our own here.

            # `quantity or 1` prevents us from dividing by 0. If quantity is 0, net_total and
            # gross_total are also 0.
            wz_row = WarehouseDocumentRow(
                commodity_name=commodity.name,
                commodity=commodity,
                quantity=quantity,
                net_price=round_currency(product_data_row['net_total'] / (quantity or 1)),
                gross_price=round_currency(product_data_row['gross_total'] / (quantity or 1)),
                tax=product_data_row['tax_amount'],
                is_full_package_expenditure=True,
            )
            wz_documents_data.setdefault((commodity, warehouse), []).append(wz_row)
        wz_document_rows = []
        for (commodity, warehouse), documents_row in wz_documents_data.items():
            wz_document = WarehouseDocument(
                warehouse=warehouse,
                transaction=transaction,
                type=WarehouseDocumentType.WZ,
                issuing_staffer=issuing_staffer,
                confirming_staffer=confirming_staffer,
                note=note,
            )
            wz_document.save()
            for document_row in documents_row:
                document_row.document = wz_document
                wz_document_rows.append(document_row)
                commodity.decrease_packages(
                    warehouse,
                    document_row.quantity,
                    document_id=wz_document.id,
                    document_type=wz_document.type,
                )
        WarehouseDocumentRow.objects.bulk_create(wz_document_rows)

    def _handle_commissions(self, rows, repr_only=False):
        """
        Args:
            repr_only: get data only for representation, not save.
        """
        pos = self.context['pos']
        tz = pos.business.get_timezone()
        now = tznow()
        operator = self.context.get('operator')
        dry_run = self.context.get('dry_run', True)  # True on GET

        def _handle(rows):
            # if new transaction set commissions_last_edit = now
            if not self.context.get('edit') and not dry_run:
                for row in rows:
                    row['commissions_last_edit'] = now
                    if not repr_only:
                        row['commissions_last_editor'] = operator
                return rows

            # for example on GET
            if self.context.get('old_txn') is None:
                return rows

            # handle for realz
            old_txn = self.context['old_txn']
            old_rows = list(old_txn.rows.select_related('commission_staffer'))

            for row in rows:
                # try to match row to one of the old_rows
                row_booking_id = (
                    row.get('subbooking').id if row.get('subbooking') else row.get('subbooking_id')
                )
                row_service_variant_id = (
                    row.get('service_variant').id
                    if row.get('service_variant')
                    else row.get('service_variant_id')
                )
                row_product_id = (
                    row.get('product').id if row.get('product') else row.get('product_id')
                )
                row_voucher_id = (
                    row.get('voucher').id if row.get('voucher') else row.get('voucher_id')
                )
                old_row = None
                if row_booking_id:
                    # match by SubBooking
                    old_row = firstof(r for r in old_rows if r.subbooking_id == row_booking_id)
                elif row_service_variant_id:
                    # match by ServiceVariant
                    old_row = firstof(
                        r for r in old_rows if r.service_variant_id == row_service_variant_id
                    )
                elif row_product_id:
                    # match by Product
                    old_row = firstof(r for r in old_rows if r.product_id == row_product_id)
                elif row_voucher_id:
                    # match by Voucher
                    olf_row = firstof(r for r in old_rows if r.voucher_id == row_voucher_id)
                if old_row is None:
                    # none matched
                    continue
                # remove old_row from old_rows
                # each can be matched only once
                old_rows.remove(old_row)

                new_staffer_id = row.get('commission_staffer_id')
                old_staffer_id = (
                    old_row.commission_staffer.id if old_row.commission_staffer else None
                )

                # no change - copy staffer from old row
                if new_staffer_id is None and old_staffer_id or new_staffer_id == old_staffer_id:
                    row['commission_staffer_id'] = old_staffer_id
                    row['commissions_last_edit'] = old_row.commissions_last_edit
                    if not repr_only:
                        row['commissions_last_editor'] = old_row.commissions_last_editor
                    else:
                        row['commissions_last_editor_name'] = TransactionRow.get_staffer_name(
                            pos.business,
                            operator,
                        )

                # change - update last edit
                elif new_staffer_id and new_staffer_id != old_staffer_id:
                    row['commissions_last_edit'] = old_row.commissions_last_edit if dry_run else now

                    if not repr_only:
                        row['commissions_last_editor'] = operator
                    else:
                        row['commissions_last_editor_name'] = TransactionRow.get_staffer_name(
                            pos.business,
                            operator,
                        )
            return rows

        rows = _handle(rows)
        for row in rows:
            if repr_only and type(row.get('commissions_last_edit')) is datetime:
                row['commissions_last_edit'] = (
                    row['commissions_last_edit']
                    .astimezone(
                        tz=tz,
                    )
                    .strftime('%Y-%m-%dT%H:%M')
                )

        return rows

    def to_representation(self, instance: Transaction):
        """Inject <action_specific>_choices data for existing Transactions.

        Data for tip_rate_choices and payment_type_choices is injected only
        when an action that needs them is allowed.

        """

        # 52409. Stardust is alias for new Android customer app version.
        # It requires some changes in
        stardust = self.context.get(compatibilities.COMPATIBILITIES, {}).get(
            compatibilities.STARDUST, False
        )

        # if pos is not in context, we must take it from current instance
        # this enables many=True for customer_api
        # (where each transaction might have different pos)
        if 'pos' not in self.context:
            self.context['__pos_per_instance'] = True
        if self.context.get('__pos_per_instance'):
            self.context['pos'] = instance.pos

        # Small hack ;)
        # To save transaction we need to set price of service.
        # For prepayment transaction we should show label "Please set the price"
        # Item without the price does the work
        if isinstance(instance, Transaction) and instance.payment_rows and instance.latest_receipt:
            status = get_attribute(instance, ['latest_receipt', 'status_code'])
            if status in [
                receipt_status.PREPAYMENT_SUCCESS,
                receipt_status.PREPAYMENT_FAILED,
            ]:
                for row in instance.rows.all():
                    if row.service_variant and row.service_variant.type == PriceType.STARTS_AT:
                        row.item_price = None

        # in many=True serialization we must remember current instance
        # for use in sub-serializers (PaymentTypeChoicesSerializer)
        self.context['__current_transaction'] = instance

        data = super(PaymentTransactionPartSerializer, self).to_representation(instance)

        # If transaction is not full paid, but we want to show it
        # Better to show already paid amount
        # Cannot be moved before to_representation, because if we set
        # `total` == `already_paid`, we lost `remaining` value
        if (
            isinstance(instance, Transaction)
            and data.get('receipts')
            and round_currency(instance.latest_receipt.already_paid)
            != round_currency(instance.total)
        ):
            data['split_payment_remaining'] = data['receipts'][0].get('remaining', None)
            data['split_payment_remaining_unformatted'] = data['receipts'][0].get(
                'remaining_unformatted', None
            )

            if not stardust:
                # Hack for apps before stardust. Statuses_without_money changed.
                # It shows always true values now.
                # If iOS will get stardust we can remove it!
                if instance.latest_receipt.status_code in receipt_status.STATUS_TO_SHOW_FULL_AMOUNT:
                    data['receipts'][0]['already_paid'] = data['payment_rows'][0]['amount_text']

                # Stardust shows both values. Total and already_paid
                _already_paid = data['receipts'][0]['already_paid']
                try:
                    data['total'] = format_currency(_already_paid)
                except InvalidOperation:
                    data['total'] = _already_paid

        # Filter canceled payment_rows
        # If transaction has one of status fro list below
        # We can erase rows which are not locked.
        if (
            isinstance(instance, Transaction)
            and instance.payment_rows
            and instance.latest_receipt
            and not self.context.get('force_full_payment_rows', False)
        ):
            status = get_attribute(instance, ['latest_receipt', 'status_code'])
            appt_status = get_attribute(instance, ['appointment', 'status'])
            if (
                status
                in [
                    receipt_status.PAYMENT_CANCELED,
                    # Case of checkout with refunded prepayment
                    receipt_status.PREPAYMENT_SUCCESS,
                    receipt_status.PREPAYMENT_FAILED,
                    # Show all rows for waiting_for_prepayment appointments
                ]
                and appt_status != Appointment.STATUS.PENDING_PAYMENT
            ):
                data['payment_rows'] = [
                    row for row in data.get('payment_rows', []) if row.get('locked')
                ]

                # During GET. If transaction has no locked payment_rows set
                # default payment_type
                if not (data['payment_rows']):
                    data['payment_type_code'] = PaymentTypeEnum.CASH

        # Hide call_for_deposit and call_for_payment
        ommiting_statuses = [
            receipt_status.CALL_FOR_DEPOSIT,
        ]
        if sget_v2(instance, ['appointment', 'status']) != Appointment.STATUS.PENDING_PAYMENT:
            ommiting_statuses.append(receipt_status.CALL_FOR_PREPAYMENT)

        if data.get('receipts'):
            data['receipts'] = [
                x
                for x in data.get('receipts', [])
                if get_attribute(x, ['status_code']) not in ommiting_statuses
            ] or []

        # show creation date of archived receipt as
        # creation date of original receipt
        receipts = data.get('receipts', [])

        if receipts:
            for key, receipt in enumerate(receipts):
                if get_attribute(receipt, ['status_code']) == receipt_status.ARCHIVED:
                    receipt['created'] = receipts[key - 1]['created']

                    # Rewrite payment_rows to have date of creation
                    archived_rows = receipt.get('payment_rows', [])
                    original_rows = receipts[key - 1].get('payment_rows', [])

                    for row_match in zip(archived_rows, original_rows):
                        row_match[0]['created'] = row_match[1]['created']

        # add tip_choices if CUSTOMER_ACTION__SET_TIP_RATE allowed
        if (
            self.context.get('customer_api')
            and data.get('tip') is not None  # present only if pos.tips_enabled
            and
            # check if action is allowed - result of .action_allowed(
            (data['actions'] or {}).get(enums.CUSTOMER_ACTION__SET_TIP_RATE)
        ) or self.context.get('force_tip_rate_choices'):
            # recalculate transaction with alternative tip_rates
            calculated = calculations.recalculate_tips(instance)
            # serialize tip_rate_choices
            subcontext = self.context.copy()
            subcontext['tip'] = instance.tip

            data['tip_choices'] = TipChoicesSerializer(
                calculated['tip_choices'],
                many=True,
                context=subcontext,
            ).data

            data['tip'] = TransactionTipSerializer(
                instance=calculated['tip'], context=self.context
            ).data

        if not self.context.get('customer_api'):
            data['rows'] = self._handle_commissions(
                data.get('rows') or [],
                repr_only=True,
            )

        if 'commissions_enabled' not in data:
            data['commissions_enabled'] = bool(
                self.context['pos'].commissions_enabled
                and self.context.get('operator')
                and self.context['operator'].get_staffer_access_level(
                    self.context['pos'].business,
                )
                in [
                    Resource.STAFF_ACCESS_LEVEL_OWNER,
                    Resource.STAFF_ACCESS_LEVEL_MANAGER,
                    Resource.STAFF_ACCESS_LEVEL_RECEPTION,
                ]
                and data['transaction_type'] != Transaction.TRANSACTION_TYPE__CANCELLATION_FEE
            )
        if (
            'tip_choices' in data
            and data['transaction_type'] == Transaction.TRANSACTION_TYPE__CANCELLATION_FEE
        ):
            # never show tip rates for deposits
            del data['tip_choices']
            if 'staffer_tip_choices' in data:
                del data['staffer_tip_choices']

        TipChoicesSerializer.mark_as_main_tip(data)
        if 'staffer_tip_choices' in data:
            for choices_data in data['staffer_tip_choices'].values():
                TipChoicesSerializer.mark_as_main_tip({'tip_choices': choices_data})

        self._calculate_cash_info(data)

        return data

    def _calculate_cash_info(self, data: dict) -> None:
        """Calculates cash change for cash transactions."""
        if not hasattr(self, 'initial_data'):
            return

        cash_info = TransactionCashInfoSerializer.calculate_cash_change(
            self.validated_data.get('cash_info'),
            data,
        )
        data.update({'cash_info': (TransactionCashInfoSerializer(instance=cash_info).data)})

    def get_actions(self, obj):
        if not isinstance(obj, Transaction):
            return None
        if obj.latest_receipt is None:
            return None
        if self.context.get('customer_api'):
            return self._get_customer_actions(obj)
        else:
            return self._get_business_actions(obj)

    @staticmethod
    def _get_customer_actions(obj):
        actions = {action: obj.action_allowed(action=action) for action in enums.CUSTOMER_ACTIONS}
        return actions

    @staticmethod
    def _get_business_actions(obj):
        actions = {action: obj.action_allowed(action=action) for action in enums.BUSINESS_ACTIONS}
        actions['send_receipt'] = obj.customer_id is not None or obj.customer_card_id is not None
        return actions

    @classmethod
    def get_data_for_renewal(cls, transaction, force=False):
        """Create a dry_run ready data for a new transaction from existing one.

        This initializes TransactionSerializer with fields modified,
        so the resultant serialized data is in the input format
        for TransactionSerializer.

        :param transaction: existing Transaction instance.
        :param force: flag if function should force returning data
        :return: data that can be used to create new dry_run transaction.
                 None if renewal is not possible.

        """
        if not force:
            # check if renewal possible
            if (
                transaction.appointment_id
                and not pos_fields.BusinessBookingPaymentInfoField.is_payable(
                    transaction.appointment,
                    transaction,
                )
            ):
                # transaction is not renewable
                return None

        # create transaction serializer
        serializer = cls(
            instance=transaction,
            context={
                'pos': transaction.pos,
                'force_full_payment_rows': force,
            },
        )

        # remove read_only fields
        for field_name, field in list(serializer.get_fields().items()):
            if field.read_only:
                del serializer.fields[field_name]

        # change write_only fields to two way fields
        serializer.fields['dry_run'] = serializers.BooleanField()
        serializer.fields['payment_type_code'] = serializers.CharField(
            source='latest_receipt.payment_type.code',
        )
        serializer.fields['bookings'] = BookingItemSerializer(many=True)
        serializer.fields['products'] = ProductItemSerializer(many=True)
        serializer.fields['deposits'] = DepositItemSerializer(many=True)
        serializer.fields['travel_fee'] = TravelFeeSerializer()

        # inject some data for newly added fields
        transaction.dry_run = True
        rows = list(transaction.rows.all())
        transaction.bookings = [
            r for r in rows if r.type == TransactionRow.TRANSACTION_ROW_TYPE__SERVICE
        ]
        transaction.products = [
            r for r in rows if r.type == TransactionRow.TRANSACTION_ROW_TYPE__PRODUCT
        ]

        deposits = [r for r in rows if r.type == TransactionRow.TRANSACTION_ROW_TYPE__DEPOSIT]
        transaction.deposits = [
            row
            for row in deposits
            if not row.addon_use or (row.addon_use and not row.addon_use.deleted)
        ]

        transaction.travel_fee = firstof(
            filter(lambda x: x.type == TransactionRow.TRANSACTION_ROW_TYPE__TRAVEL_FEE, rows)
        )

        # do the serialization
        data = serializer.data
        return data

    @classmethod
    def update_transaction_with_booking(cls, appointment, ignore_analytics=False):
        from webapps.booking.models import Appointment

        if isinstance(appointment, Appointment):
            booking_ids = [bk.id for bk in iter_leaf_services(appointment.subbookings)]
        else:
            raise TypeError('object should be an instance of "Appointment"')

        filters = {
            Transaction.TRANSACTION_TYPE__CANCELLATION_FEE: [
                Q(children__isnull=True),
                Q(latest_receipt__status_code__in=[receipt_status.DEPOSIT_AUTHORISATION_SUCCESS]),
                Q(transaction_type=Transaction.TRANSACTION_TYPE__CANCELLATION_FEE),
            ],
            Transaction.TRANSACTION_TYPE__PAYMENT: [
                Q(children__isnull=True),
                Q(
                    latest_receipt__status_code__in=[
                        receipt_status.PREPAYMENT_SUCCESS,
                        receipt_status.PREPAYMENT_AUTHORISATION_SUCCESS,
                        receipt_status.BOOKSY_PAY_SUCCESS,
                        receipt_status.GIFT_CARD_DEPOSIT,
                    ]
                ),
                Q(transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT),
            ],
        }

        for transaction_type in [
            Transaction.TRANSACTION_TYPE__PAYMENT,
            Transaction.TRANSACTION_TYPE__CANCELLATION_FEE,
        ]:
            transaction = (
                Transaction.objects.by_appointment_id(
                    appointment.id,
                )
                .filter(*filters[transaction_type])
                .last()
            )

            # If still there is no transaction we can safely exit ;)
            if not transaction:
                continue

            txn_data = cls.get_data_for_renewal(transaction=transaction, force=True)

            txn_data['parent_txn'] = transaction.id
            txn_data['dry_run'] = False

            txn_data['appointment'] = appointment.id

            # For payment transaction - switch items in TransactionRows
            if transaction_type == Transaction.TRANSACTION_TYPE__PAYMENT:
                txn_data['bookings'] = [{'booking_id': booking_id} for booking_id in booking_ids]
                if appointment.traveling:
                    travel_fee = txn_data.get('travel_fee') or {}
                    travel_fee['item_price'] = appointment.traveling.price
                    txn_data['travel_fee'] = travel_fee
                else:
                    txn_data['travel_fee'] = None
            # For cancellation_fee transaction - do nothing
            # TODO::FLS-3976
            serializer = cls(
                data=txn_data,
                context={
                    'pos': transaction.pos,
                    'business': appointment.business,
                    'old_txn': transaction,
                    'operator': transaction.operator,
                    'edit': True,
                    'cached_payment_rows': transaction.latest_receipt.payment_rows.all(),
                    'ignore_analytics': ignore_analytics,
                    compatibilities.COMPATIBILITIES: {
                        compatibilities.SQUARE: True,
                        compatibilities.SPLIT: True,
                        compatibilities.PREPAYMENT: True,
                    },
                    'deposit_mode': (
                        transaction_type == Transaction.TRANSACTION_TYPE__CANCELLATION_FEE
                    ),
                    'editing_appointment_process': True,
                },
            )

            lib.tools.sasrt(
                serializer.is_valid(),
                status.HTTP_400_BAD_REQUEST,
                [
                    {
                        'code': 'invalid',
                        'type': 'validation',
                        'description': f"{serializer.errors.get('non_field_errors', [''])[0]}",
                    }
                ],
            )

            # First update payment_rows to generate ARCHIVED receipt
            transaction.update_payment_rows(
                receipt_status.ARCHIVED,
                log_action=PaymentRowChange.MULTI_ROW_UPDATE,
                log_note='update_transaction_with_booking',
                update_basket_payments=False,
            )
            # Then save validated data to have valid receipt order
            serializer.save()

    @staticmethod
    def get_total_appointment_without_tip(instance):
        total_without_tip = sum(
            [
                safe_get(instance, ['taxed_subtotal_services']) or Decimal(0),
                safe_get(instance, ['taxed_subtotal_products']) or Decimal(0),
            ]
        )
        return round_currency(total_without_tip)

    @staticmethod
    def is_giftcard_payment_row(payment_row) -> bool:
        return (
            safe_get(
                payment_row,
                ['voucher', 'voucher_template', 'type'],
            )
            == VoucherType.EGIFT_CARD
        )


class SimpleTransactionPartSerializer(serializers.Serializer):
    """Serializer used for extracting bookings from appointments
    with item_price recalculation."""

    default_error_messages = {
        'amount_required': _(
            'amount is required for simple_checkout with dry_run=False',
        ),
        'appointment_not_found': _('Appointment not found'),
        'empty_checkout_amount_required': _('amount_without_tip is required for empty_checkout'),
        'invalid_appointment_type': _('Invalid appointment type'),
        'simple_checkout_amount_collision': _(
            'either amount_without_tip or remaining_amount_without_tip'
            ' can be used with simple transaction',
        ),
        'transaction_required': _('Parent transaction required'),
    }
    simple_checkout_keys = (
        'amount_without_tip',
        'appointment',
        'appointment_type',
        'empty_checkout',
        'remaining_amount_without_tip',
    )

    # OBSOLETE: unique as pair:
    # - id (booking_id/multibooking_id)
    # - type (single/multi)
    appointment = serializers.IntegerField(
        required=False,
        # different source, because 'appointment' is field in Transaction
        source='appointment_or_booking',
        write_only=True,
    )
    appointment_type = serializers.ChoiceField(
        choices=AppointmentTypeSMChoices,
        required=False,
    )
    amount_without_tip = PriceFieldUnformatted(required=False)
    remaining_amount_without_tip = PriceFieldUnformatted(required=False)
    empty_checkout = serializers.BooleanField(required=False, default=False)

    def to_internal_value(self, data):
        if not self.is_simple_checkout:
            # self.pop_non_transaction_items(data)
            return super().to_internal_value(data)

        old_txn = self.context.get('old_txn')
        if old_txn:
            data['payment_rows'] = self.merge_payment_rows_with_locked(
                old_txn=old_txn,
                payment_rows=data.get('payment_rows', []),
            )

            self.inject_old_txn_tip(old_txn=old_txn, data=data)

        if not self.has_bookings:
            data['bookings'] = data.pop('rows', [])

        return super().to_internal_value(data)

    def merge_payment_rows_with_locked(
        self,
        old_txn: Transaction,
        payment_rows: list,
    ) -> list:
        ignored_payment_status_codes = [
            receipt_status.ARCHIVED,
            receipt_status.PARK_SALE,
            receipt_status.PREPAYMENT_FAILED,
            receipt_status.BOOKSY_PAY_FAILED,
        ]
        if old_txn.latest_receipt.status_code in ignored_payment_status_codes:
            return payment_rows

        locked_payment_rows = filter(lambda r: r.locked, old_txn.payment_rows)
        unlocked_payment_rows = filter(
            lambda r: not r.get('locked'),
            payment_rows,
        )

        payment_rows_ = PaymentRowsSerializer(
            instance=locked_payment_rows,
            context={
                **self.context,
                'currency_symbol': settings.CURRENCY_CODE,
            },
            many=True,
        ).data

        payment_rows_.extend(unlocked_payment_rows)
        return payment_rows_

    @staticmethod
    def inject_old_txn_tip(
        old_txn: Transaction,
        data: dict,
    ) -> None:
        valid_statuses = (
            receipt_status.PREPAYMENT_SUCCESS,
            receipt_status.VOUCHER_PARTIAL_REDEEM,
            receipt_status.BOOKSY_PAY_SUCCESS,
        )
        tip_already_present = data.get('tip')
        has_previous_tip = (
            hasattr(old_txn, 'tip') and old_txn.latest_receipt.status_code in valid_statuses
        )

        if tip_already_present or not has_previous_tip:
            return

        if not old_txn.tip.rate or old_txn.tip.amount is None:
            return

        data['tip'] = {
            'rate': old_txn.tip.rate,
            'type': old_txn.tip.type,
        }

    def validate(self, attrs):
        amount_without_tip = attrs.pop('amount_without_tip', None)
        appointment_id = attrs.pop('appointment_or_booking', None)
        appointment_type = attrs.pop('appointment_type', None)
        attrs.pop('empty_checkout', None)
        remaining_amount_without_tip = attrs.pop(
            'remaining_amount_without_tip',
            None,
        )
        if not self.is_simple_checkout:
            return super().validate(attrs)

        is_appointment = appointment_id and appointment_type

        if is_appointment and not self.has_bookings:
            if appointment_type not in AppointmentTypeSMChoices:
                self.fail('invalid_appointment_type')

            if amount_without_tip and remaining_amount_without_tip:
                self.fail('simple_checkout_amount_collision')

            recalculated_rows = self.resolve_appointment(
                appointment_id,
                appointment_type,
                amount_without_tip,
                remaining_amount_without_tip,
            )

            booking_rows, travel_fee_row = self._split_bookings_from_travel_fee(
                transaction_rows=recalculated_rows,
            )
            if travel_fee_row:
                attrs['travel_fee'] = travel_fee_row

            attrs['bookings'] = booking_rows

        elif self.is_empty_checkout:
            amount_to_pay = self.get_amount_to_pay(
                amount_without_tip,
                remaining_amount_without_tip,
            )

            if not attrs['dry_run'] and not amount_to_pay:
                self.fail('empty_checkout_amount_required')
            attrs['bookings'] = self.resolve_empty_checkout(amount_to_pay)

        if (
            amount_without_tip is not None
            or remaining_amount_without_tip is not None
            or self.is_empty_checkout
        ):
            self.disable_service_promotions_for_custom_amount()
            attrs['bookings'] = self.zero_empty_discount_rate(attrs['bookings'])

        return super().validate(attrs)

    def _split_bookings_from_travel_fee(
        self,
        transaction_rows: t.List[dict],
    ) -> tuple:
        _travel_fee_type = TransactionRow.TRANSACTION_ROW_TYPE__TRAVEL_FEE

        bookings_rows = self._filter_non_travel_fee_rows(transaction_rows)
        travel_fee_row = self._filter_travel_fee_row(transaction_rows)

        return bookings_rows, travel_fee_row

    @staticmethod
    def _filter_travel_fee_row(rows: t.List[dict]) -> t.Optional[dict]:
        _travel_type = TransactionRow.TRANSACTION_ROW_TYPE__TRAVEL_FEE
        travel_fee_rows = [row for row in rows if row.get('type') == _travel_type]
        return travel_fee_rows[0] if travel_fee_rows else None

    @staticmethod
    def _filter_non_travel_fee_rows(rows: t.List[dict]) -> list:
        _travel_type = TransactionRow.TRANSACTION_ROW_TYPE__TRAVEL_FEE
        return [row for row in rows if row.get('type') != _travel_type]

    def to_representation(self, instance):
        result = super().to_representation(instance)

        if (
            hasattr(self, 'initial_data')
            and self.validated_data.get('amount_without_tip') is not None
        ):
            amount_without_tip = self.validated_data['amount_without_tip']
        else:
            amount_without_tip = self.total_amount_without_tip(instance)

        result.update(
            {
                'amount_without_tip': str(amount_without_tip),
                'remaining_amount_with_tip': str(
                    safe_get(instance, ['split_payment_remaining']) or self.zero,
                ),
                'remaining_amount_without_tip': str(
                    self._remaining_amount_without_tip(instance),
                ),
            }
        )

        access_level = self.context.get('access_level')
        if result.get('customer_data') and access_level in Resource.STAFF_ACCESS_LEVELS_LOCKED:
            # remove phone and email from customer data, keep only full name
            result['customer_data'] = result['customer_data'].partition(',')[0]

        return result

    @staticmethod
    def zero_amount_for_empty_checkout(
        amount_without_tip: t.Optional[Decimal],
    ):
        """Set amount_without tip to 0 for empty checkout with dry_run."""
        return amount_without_tip or Decimal(0)

    def pop_non_transaction_items(self, data: dict) -> dict:
        """Removes items that cannot be saved with transaction."""
        return {key: data.pop(key, None) for key in self.simple_checkout_keys}

    @staticmethod
    def _clear_invalid_price_flags(booking: dict) -> None:
        """Removes incomplete_price flag from booking"""
        incomplete_price_flags = (
            'incomplete_price',
            'price_value',
            'price_type',
        )
        non_fixed_price_type = (
            PriceType.STARTS_AT,
            PriceType.VARIES,
            PriceType.DONT_SHOW,
        )
        booking_with_service_variant = booking.get('service_variant')

        can_remove_flags = not booking_with_service_variant or (
            booking_with_service_variant
            and booking_with_service_variant.type in non_fixed_price_type
        )

        if can_remove_flags:
            _ = [booking.pop(flag, None) for flag in incomplete_price_flags]

    def get_amount_to_pay(
        self,
        amount_without_tip: t.Union[Decimal, None],
        remaining_amount_without_tip: t.Union[Decimal, None],
    ):
        """Calculate amount to pay for bookings."""
        amount_with_already_paid = self.zero

        if remaining_amount_without_tip is not None:
            old_txn = self.context.get('old_txn')
            if not old_txn:
                self.fail('transaction_required')
            amount_with_already_paid = sum(
                [
                    remaining_amount_without_tip,
                    old_txn.latest_receipt.already_paid or self.zero,
                ]
            )

            subtract_tip_already_paid = old_txn.latest_receipt.status_code in (
                receipt_status.PREPAYMENT_SUCCESS,
                receipt_status.PAYMENT_CANCELED,
                receipt_status.VOUCHER_PARTIAL_REDEEM,
                receipt_status.BOOKSY_PAY_SUCCESS,
            )
            if subtract_tip_already_paid:
                tip_already_paid = sum(
                    row.tip_amount
                    for row in old_txn.latest_receipt.payment_rows.all()
                    if row.tip_amount
                )
                amount_with_already_paid -= tip_already_paid

        return amount_without_tip or amount_with_already_paid

    def resolve_appointment(
        self,
        appointment_id: int,
        appointment_type: str,
        amount_without_tip: t.Union[Decimal, None],
        remaining_amount_without_tip: t.Union[Decimal, None],
    ) -> list:
        """Calculate prices for bookings from appointment."""
        appointment = AppointmentWrapper.get_appointment(
            appointment_type=appointment_type,
            appointment_id=appointment_id,
            business_id=self.context['pos'].business_id,
        )
        if not appointment:
            self.fail('appointment_not_found')

        subbookings = [
            {'booking_id': booking.id} for booking in iter_leaf_services(appointment.subbookings)
        ]

        bookings = self.serialize_booking_items(data=subbookings)

        should_recalculate_item_prices = (
            amount_without_tip is not None or remaining_amount_without_tip is not None
        )
        if should_recalculate_item_prices:
            amount_to_pay = self.get_amount_to_pay(
                amount_without_tip,
                remaining_amount_without_tip,
            )

            bookings = self.calculate_booking_prices_from_amount(
                bookings,
                amount_to_pay,
            )

            # self.disable_service_promotions_for_custom_amount()

        bookings = self.clean_incomplete_prices_for_subbookings(bookings)

        return bookings

    def clean_incomplete_prices_for_subbookings(self, bookings: list) -> list:
        key = 'item_price'

        if RowCalculationMode(self.context).gross_to_net:
            key = 'gross_total'

        item_price_sum = sum(booking.get(key) or self.zero for booking in bookings)

        if not item_price_sum:
            return bookings

        for booking in bookings:
            self._clear_invalid_price_flags(booking)

        return bookings

    def disable_service_promotions_for_custom_amount(self) -> None:
        self.context['disable_promotions'] = True

    def resolve_empty_checkout(self, amount_without_tip: Decimal) -> list:
        """Creates service_name with item_price for checkout.

        We must set ['row_calculation_mode']['from_gross_total'] to True
        in context for proper calculation of taxes.
        This is done via setter on RowCalculationMode.
        """
        booking_data = [
            {
                'service_name': _('Custom Amount'),
                'item_price': amount_without_tip,
            }
        ]
        bookings = self.serialize_booking_items(data=booking_data)

        bookings[0]['gross_total'] = amount_without_tip
        RowCalculationMode(self.context).gross_to_net = True
        return bookings

    def serialize_booking_items(self, data: list) -> list:
        serializer = BookingItemSerializer(
            data=data,
            many=True,
            context=self.context,
        )
        serializer.is_valid(raise_exception=True)
        return serializer.validated_data

    @staticmethod
    def zero_empty_discount_rate(bookings: t.List[dict]) -> list:
        return [
            {
                **booking,
                'discount_rate': booking.get('discount_rate') or 0,
            }
            for booking in bookings
        ]

    def calculate_booking_prices_from_amount(
        self,
        bookings: list,
        amount_to_pay: Decimal,
    ) -> list:
        context = copy.deepcopy(self.context)
        context.get('compatibilities', {}).pop('simple_checkout', None)

        data = copy.deepcopy(self.initial_data)
        data['bookings'] = [{'booking_id': row['subbooking'].id} for row in bookings]
        data.pop('appointment_or_booking', None)
        data.pop('appointment_type', None)

        locked_payment_rows = [pr for pr in data.get('payment_rows', []) if pr.get('locked')]
        data['payment_rows'] = locked_payment_rows + [
            {'mode': PaymentRow.PAYMENT_ROW_MODE__COMPLETE},
        ]
        data['dry_run'] = True

        serializer = TransactionSerializer(data=data, context=context)

        if serializer.is_valid():
            basic_amount_without_tip = serializer.total_amount_without_tip(
                serializer.validated_data,
            )
            validated_rows = serializer.validated_data.get('rows', [])
            discount_amount = sum(
                row.get('real_discount_amount', Decimal('0.00')) for row in validated_rows
            )
            total_diff = amount_to_pay - basic_amount_without_tip + discount_amount

            if total_diff == 0:
                return bookings

            travel_fee_row = self._filter_travel_fee_row(validated_rows)
            travel_fee_rows = [travel_fee_row] if travel_fee_row else []
            service_rows = self._filter_non_travel_fee_rows(validated_rows)

            new_gross_prices = calculate_proportional_gross_prices(
                amount_to_pay,
                basic_amount_without_tip,
                service_rows + travel_fee_rows,
            )

            bookings = [
                {**row, 'gross_total': gross_total}
                for row, gross_total in zip(
                    service_rows + travel_fee_rows,
                    new_gross_prices,
                )
            ]

            RowCalculationMode(self.context).gross_to_net = True

        return bookings

    @property
    def zero(self) -> Decimal:
        return Decimal('0.00')

    @property
    def is_simple_checkout(self) -> bool:
        return safe_get(self.context, ['compatibilities', 'simple_checkout'])

    @property
    def is_empty_checkout(self) -> bool:
        return self.initial_data.get('empty_checkout', False)

    @property
    def has_bookings(self) -> bool:
        return len(self.initial_data.get('bookings', [])) > 0

    def total_amount_without_tip(self, instance) -> Decimal:
        amount = sum(
            [
                safe_get(instance, ['taxed_subtotal_services']) or self.zero,
                safe_get(instance, ['taxed_subtotal_products']) or self.zero,
            ]
        )
        return rounded_decimal(amount)

    def _remaining_amount_without_tip(self, instance) -> Decimal:
        already_paid = (
            safe_get(
                instance,
                ['latest_receipt', 'already_paid'],
            )
            or self.zero
        )
        split_remaining = safe_get(instance, ['split_payment_remaining'])
        total_without_tip = self.total_amount_without_tip(instance)

        if split_remaining is not None:
            tip_remaining = safe_get(instance, ['tip', 'amount_remaining'])
            result = split_remaining - tip_remaining

        elif total_without_tip and already_paid:
            total = safe_get(instance, ['total'])
            tip_paid = safe_get(instance, ['tip', 'already_paid']) or self.zero
            result = total - already_paid - tip_paid

        else:
            result = total_without_tip

        return rounded_decimal(result)


class TransactionSerializer(
    SimpleTransactionPartSerializer,
    PaymentTransactionPartSerializer,
    VoucherTransactionPartSerializer,
    ProductTransactionPartSerializer,
    AddonTransactionPartSerializer,
    BookingTransactionPartSerializer,
):
    """
    Serializers used to create or edit transaction.

    Possible context:
    - edit:
        True - transaction is full edit mode. Only when editing transaction from
            PAYMENT_SUCCESS status.
        False - transaction is in fake edit mode.
        not exists - that key doesn't exist - creating new transaction.
    - cached_payment_rows:
        True - Dirty hack to update transaction_rows.
            Only Transaction_rows are updated. cached payment rows are cloned.
    - compatibilities: {
            'square': True,   # App supports square payments
            'split': True,    # App supports split payments
            'prepayment': True   # App supports prepayments
            'new_checkout': True  # App supports not using payment_type_code
        }
    - old_txn: object of parent_transaction.
        Basically the same as data['parent_txn']
    - pos: pos object
    - selected_register_id: Id of register where transaction should be put in.
        Used when shared registers feature enabled.
    - park_sale:
        True - front wanst to save transaction as a draft. Part of validation
            is disabled in this mode.
    - force_full_payment_rows:
        True - front wants to get all payment rows including
            refunded and cancelled. Used in update_transaction_with_booking.
    - import:
        True - validating registers is turned off,
    - disable_promotions:
        True - promotions won't be counted. Used in old endpoint (MP)
    - donations:
        True - handling donation. Register support is disabled.
    - simple_redeem:
        True - handling simple voucher redeem. Egift card service limits are
            disabled
    - user:
        User that is triggering transaction. Used for transactions connected to
        Family & Friends appointments, to check if they are being triggered by parent.
    """

    post_finish_tasks = []


class FrenchCertificationTransactionSerializer(TransactionSerializer):
    receipts = FrenchCertificationReceiptDetailsSerializer(
        many=True,
        read_only=True,
        required=False,
    )
    is_id_hidden = serializers.BooleanField(read_only=True, default=True)


class ExternalPartners(serializers.Serializer):
    google_pay = serializers.BooleanField(read_only=True)
    apple_pay = serializers.BooleanField(read_only=True)
    blik = serializers.BooleanField(read_only=True)
    klarna = serializers.BooleanField(read_only=True)

    def to_representation(self, instance: dict | POS):
        allow_blik = bool(
            sget_v2(instance, ['prepayment_total']) or sget_v2(instance, ['booksy_pay_total'])
        )
        appointment_data: AppointmentData = self.context.get('appointment_data')
        pos = self.context.get('pos')
        return check_tokenized_payments_v2(
            appointment_data=appointment_data,
            is_cancelation=bool(sget_v2(instance, ['cancellation_fee_total'])),
            stripe_enabled=pos.force_stripe_pba if pos else False,
            allow_blik=allow_blik,
            is_booksy_pay_available=self.context.get('is_booksy_pay_available'),
        )


class ExternalPaymentMethodSerializer(serializers.ModelSerializer):
    partner = serializers.CharField(required=True)
    token = serializers.CharField(required=True)

    class Meta:
        model = PaymentMethod
        fields = ('partner', 'token')

    def __init__(self, *args, **kwargs):
        self.allow_blik = kwargs.pop('allow_blik', False)
        super(ExternalPaymentMethodSerializer, self).__init__(*args, **kwargs)

    def validate(self, data):
        cancellation_fee = self.context.get('cancellation_fee', None)
        appointment_data: AppointmentData = self.context.get('appointment_data')
        pos = self.context.get('pos')

        payment_possibilities = check_tokenized_payments_v2(
            appointment_data=appointment_data,
            is_cancelation=bool(cancellation_fee),
            stripe_enabled=pos.force_stripe_pba if pos else False,
            allow_blik=self.allow_blik,
            is_booksy_pay_available=self.context.get('is_booksy_pay_available'),
        )

        card_type = get_attribute(data, ['card_type'])
        if not safe_get(payment_possibilities, [card_type]):
            raise serializers.ValidationError(
                _('Payment is not possible'), code='payment_not_possible'
            )

        return data

    def to_internal_value(self, instance):
        """Creates temp PaymentMethod instance with additional fields."""

        payment_method = PaymentMethod(
            provider=AdyenEEPaymentProvider.codename,
            card_type=instance.get('partner'),
            user=self.context['user'],
        )
        payment_method.token = instance.get('token')
        return payment_method


class BooksyGiftCardFieldMixin:

    def validate_if_bgc_available(self):
        if not (BooksyGiftcardsEnabledFlag()):
            raise serializers.ValidationError(_('Gift cards temporary disabled'))
        if not sget_v2(
            instance=self.context,
            path_list=['pos', 'business', 'booksy_gift_cards_settings', 'is_enabled'],
        ):
            raise serializers.ValidationError(
                _(
                    'Sorry, this business is not accepting Booksy Gift Cards yet,'
                    ' please select another business.'
                ),
            )
        appointment_data: AppointmentData | None = self.context.get('appointment_data')
        if appointment_data and not appointment_data.has_fixed_price:
            raise serializers.ValidationError(
                _('Gift cards are only available for fixed-price appointments.')
            )

    @property
    def user(self) -> User:
        return self.context['user']

    @property
    def appointment_value(self) -> Decimal:
        return self.context['appointment_checkout'].total.value


class BooksyGiftCardCodePaymentMethodField(serializers.CharField, BooksyGiftCardFieldMixin):
    def get_value(self, *args, **kwargs):
        value = super().get_value(*args, **kwargs)
        return value.replace('-', '').upper() if isinstance(value, str) else value

    def run_validation(self, gift_card_code: str | empty = empty):
        if gift_card_code not in ('', empty, None):
            self._validate_non_empty_gift_card_code(gift_card_code)
        return super().run_validation(data=gift_card_code)

    def _validate_non_empty_gift_card_code(self, gift_card_code: str):
        self.validate_if_bgc_available()
        validation_response = validate_booksy_gift_card(
            gift_card_code=gift_card_code,
            user=self.user,
            value=self.appointment_value,
        )
        if not validation_response.is_valid:
            raise serializers.ValidationError(
                detail=validation_response.error_message, code='terminated_gift_card_error'
            )
        self.context['available_giftcard_balance'] = validation_response.balance
        self.context['gift_cards_ids'] = validation_response.gift_cards_ids
        self.context['gift_card_code'] = gift_card_code

    def to_internal_value(self, gift_card_code: str) -> PaymentMethod:
        """Creates temp PaymentMethod instance with additional fields."""
        payment_method = PaymentMethod(
            provider=PaymentProviderEnum.BOOKSY_GIFT_CARDS,
            card_type=CARD_TYPE__BOOKSY_GIFT_CARD,
            user=self.user,
        )
        payment_method.gift_cards_ids = self.context['gift_cards_ids']
        return payment_method

    def to_representation(self, value):
        return self.context['gift_card_code'] if value else None


class BooksyGiftCardIdsPaymentMethodField(serializers.ListField, BooksyGiftCardFieldMixin):

    def run_validation(self, gift_cards_ids: list[str] | empty = empty):
        if gift_cards_ids not in ('', empty, None, []):
            self._validate_non_empty_gift_cards_ids(gift_cards_ids)
            return super().run_validation(data=gift_cards_ids)
        return super().run_validation(data=empty)

    def _validate_non_empty_gift_cards_ids(self, gift_cards_ids: list[str]):
        self.validate_if_bgc_available()
        validation_response = validate_booksy_gift_card(
            gift_cards_ids=gift_cards_ids,
            user=self.user,
            value=self.appointment_value,
        )
        if not validation_response.is_valid:
            raise serializers.ValidationError(
                detail=validation_response.error_message, code='terminated_gift_card_error'
            )
        self.context['available_giftcard_balance'] = validation_response.balance
        self.context['gift_cards_ids'] = validation_response.gift_cards_ids
        self.context['gift_cards_ids_to_display'] = gift_cards_ids

    def to_internal_value(self, gift_cards_ids: list[str]) -> PaymentMethod:
        """Creates temp PaymentMethod instance with additional fields."""
        payment_method = PaymentMethod(
            provider=PaymentProviderEnum.BOOKSY_GIFT_CARDS,
            card_type=CARD_TYPE__BOOKSY_GIFT_CARD,
            user=self.user,
        )
        payment_method.gift_cards_ids = self.context['gift_cards_ids']
        return payment_method

    def to_representation(self, value):
        return self.context['gift_cards_ids_to_display'] if value else []


class BaseCustomerAppointmentTransactionSerializer(serializers.Serializer):
    """
    NOTE: DO NOT USE THIS SERIALIZER DIRECTLY - INHERIT IN CHILD CLASSES.

    Common serializer fields & methods of processing transactions for a Customer Appointment.
    """

    dry_run = serializers.BooleanField(write_only=True)

    # Tip
    tip_choices = TipChoicesSerializer(
        many=True,
        required=False,
        read_only=True,
    )
    tip = TransactionTipSerializer(required=False, allow_null=True)

    # Payment method
    payment_method = pos_fields.UserRelatedField(  # limited to context['user']
        queryset=PaymentMethod.objects.all(),
        required=False,
        allow_null=True,
        write_only=True,
    )

    external_payment_method = ExternalPaymentMethodSerializer(
        required=False,
        allow_null=True,
        write_only=True,
        allow_blik=True,
    )

    # External partners
    external_partners = ExternalPartners(source='*', read_only=True)

    force_stripe_pba = serializers.SerializerMethodField()
    to_pay_later = serializers.SerializerMethodField()
    payment_summary = serializers.SerializerMethodField()

    def to_internal_value(self, data):
        if CARD_TYPE__APPLE_PAY == sget_v2(data, ['external_payment_method', 'partner']):
            data['payment_method'] = None

        return super().to_internal_value(data)

    def get_fields(self):
        fields = super().get_fields()
        if not (pos := self.context.get('pos')) or not pos.tips_enabled:
            del fields['tip']
        if not BooksyGiftcardsEnabledFlag() or not self.context.get('show_payment_summary'):
            del fields['payment_summary']
        return fields

    def get_payment_summary(self, obj):
        appointment_value = self.context['appointment_checkout'].total.value
        if not appointment_value:
            return {}
        prepayment_total = obj.get('prepayment_total', Decimal('0.00'))
        prepayment_tax = obj.get('prepayment_tax', Decimal('0.00'))
        tip_amount = sget_v2(obj, ['tip', 'amount'], Decimal('0.00'))
        appointment_total_value = appointment_value + prepayment_tax + tip_amount
        rows = []
        gift_card_value = Decimal('0.00')
        if sget_v2(obj, ['gift_card_code']) or sget_v2(obj, ['gift_cards_ids']):
            gift_card_value = self.context['available_giftcard_balance']
            rows.extend(
                [
                    {
                        'label': PaymentSummaryRowsEnum.TOTAL.label,
                        'value': str(appointment_total_value),
                        'key': PaymentSummaryRowsEnum.TOTAL,
                    },
                    {
                        'label': PaymentSummaryRowsEnum.GIFT_CARD.label,
                        'value': str(-1 * gift_card_value),
                        'key': PaymentSummaryRowsEnum.GIFT_CARD,
                    },
                ]
            )
        to_pay_now = {
            'label': PaymentSummaryRowsEnum.TO_PAY_NOW.label,
            'value': str(prepayment_total),
            'key': PaymentSummaryRowsEnum.TO_PAY_NOW,
        }
        to_pay_later = {
            'label': PaymentSummaryRowsEnum.TO_PAY_LATER.label,
            'value': str(appointment_total_value - prepayment_total - gift_card_value),
            'key': PaymentSummaryRowsEnum.TO_PAY_LATER,
        }

        return {
            'rows': rows,
            'summary': [to_pay_now, to_pay_later],
        }

    def get_to_pay_later(self, obj):
        raise NotImplementedError

    def _get_extra_input_data(self):
        return {}

    @property
    def no_fees(self) -> dict:
        return {
            'service_fee': Decimal(0),
            'tip_base': Decimal(0),
        }

    def _calculate_tip(self, attrs: dict) -> dict:
        """Validate tips..
        :param dict attrs: Partly validated data
        :return: update_attrs dict which should be merged with attrs
        :rtype: dict
        """
        from webapps.pos.calculations import get_pos_settings
        from webapps.pos.tip_calculations import TipCalculator

        pos = self.context.get('pos')
        if not pos:
            return {}

        pos_settings = get_pos_settings(pos)

        if not pos_settings.get('tips_enabled'):
            return {}

        tip = attrs.get('tip')

        if not tip:
            default_tip = self.context['pos'].default_tip
            tip = {
                'rate': default_tip.rate if default_tip else 0,
                'type': 'P',
            }

        tip_calculator = TipCalculator(
            transaction_data={
                'total_override': attrs['tip_base'],
                'already_paid': 0,
                'tip_already_paid': 0,
                'tip': tip,
            },
            pos_settings=pos_settings,
        )

        return tip_calculator.calculate_tips()

    @abstractmethod
    def _validate_payment_methods(self, attrs: dict) -> None:
        raise NotImplementedError

    @property
    def appointment_already_prepaid(self) -> bool:
        return self.context.get('already_prepaid') or False

    def _get_tip_by_hand(self) -> dict:
        tip = self.validated_data.get('tip')

        # Small hack - use tip always as BY_HAND format
        if tip:
            tip['rate'] = tip['amount']
            tip['type'] = SimpleTip.TIP_TYPE__HAND

        return tip

    def _get_payment_method(self) -> PaymentMethod:
        if self.validated_data.get('payment_method'):
            return self.validated_data['payment_method']
        return self.validated_data['external_payment_method']

    def get_force_stripe_pba(self, instance) -> bool:
        return bool(safe_get(self.context, ['pos', 'force_stripe_pba']))

    @property
    def context_pos(self):
        return self.context['pos']


class CustomerAppointmentTransactionSerializer(BaseCustomerAppointmentTransactionSerializer):
    """Creates cancellation fee / prepayment transaction during booking."""

    # Cancellation fee
    cancellation_fee = serializers.DecimalField(read_only=True, max_digits=10, decimal_places=2)
    cancellation_fee_tax = serializers.DecimalField(read_only=True, max_digits=10, decimal_places=2)
    cancellation_fee_total = serializers.DecimalField(
        read_only=True, max_digits=10, decimal_places=2
    )

    # Prepayment
    prepayment = serializers.DecimalField(read_only=True, max_digits=10, decimal_places=2)
    prepayment_tax = serializers.DecimalField(read_only=True, max_digits=10, decimal_places=2)
    prepayment_total = serializers.DecimalField(read_only=True, max_digits=10, decimal_places=2)

    gift_card_code = BooksyGiftCardCodePaymentMethodField(
        allow_null=True,
        required=False,
    )
    gift_cards_ids = BooksyGiftCardIdsPaymentMethodField(
        allow_null=True,
        required=False,
    )

    def get_to_pay_later(self, _) -> str | None:
        checkout = self.context['appointment_checkout']
        total_value = checkout.total.value

        if total_value is None:
            return None

        if not self.prepayment_applies:
            return str(total_value)

        return str(total_value - checkout.prepayment.value - self._get_prepayment_tax_value())

    @cached_property
    def prepayment_applies(self) -> bool:
        business = self.context['business']
        user = self.context['user']

        if not (business.pos_enabled and business.pos_pay_by_app_enabled):
            return False

        bci = user and user.business_customer_infos.filter(business_id=business.id).first()

        family_and_friends_member_id = sget_v2(
            self,
            ['initial_data', 'book_for_family_member', 'member'],
        )
        if bci and bci.trusted:
            if family_and_friends_member_id:
                member = MemberProfile.objects.get(id=family_and_friends_member_id)
                member_bci = get_proper_bci_for_member(member, business) if member else None

                if not member_bci or member_bci.trusted:
                    return False
            else:
                return False

        return True

    def _get_prepayment_tax_value(self) -> Decimal:
        business = self.context['business']

        if not business.pos_enabled:
            return Decimal(0)

        service_fee = business.pos.service_fee if business.pos.service_fee else Decimal(0)
        return self.context['appointment_checkout'].prepayment.excluded_tax + service_fee

    def _get_extra_input_data(self):
        update_attrs = super()._get_extra_input_data()

        if not self.prepayment_applies:
            return self.no_fees

        checkout: AppointmentCheckout = self.context['appointment_checkout']
        cancellation_fee = checkout.cancellation_fee

        update_attrs['cancellation_fee'] = cancellation_fee.value
        update_attrs['cancellation_fee_tax'] = cancellation_fee.excluded_tax

        update_attrs['cancellation_fee_total'] = round_currency(
            update_attrs['cancellation_fee_tax'] + update_attrs['cancellation_fee']
        )

        prepayment = checkout.prepayment
        update_attrs['prepayment'] = prepayment.value

        if update_attrs['prepayment'] == 0:
            update_attrs['prepayment_tax'] = 0
            update_attrs['tip_base'] = 0
        else:
            update_attrs['prepayment_tax'] = self._get_prepayment_tax_value()
            # update_attrs['prepayment_total'] - set later after calculating tip

            update_attrs['tip_base'] = checkout.prepayment_tip_base

        return update_attrs

    @property
    def no_fees(self) -> dict:
        no_fees = super().no_fees
        no_fees.update(
            {
                'cancellation_fee': Decimal(0),
                'cancellation_fee_tax': Decimal(0),
                'cancellation_fee_total': Decimal(0),
                'prepayment': Decimal(0),
                'prepayment_tax': Decimal(0),
                'prepayment_total': Decimal(0),
            }
        )

        return no_fees

    def _calculate_tip(self, attrs):
        """Validate tips..
        :param dict attrs: Partly validated data
        :return: update_attrs dict which should be merged with attrs
        :rtype: dict
        """
        tips_data = super()._calculate_tip(attrs)

        if tips_data and attrs['prepayment'] == 0:
            tips_data['tip']['rate'] = 0
            tips_data['tip']['amount'] = 0

        return tips_data

    def _validate_payment_methods(self, attrs: dict) -> None:
        if attrs['cancellation_fee_total'] or attrs['prepayment_total']:
            payment_method = attrs.get('payment_method')
            external_payment_method = attrs.get('external_payment_method')
            gift_card_code = attrs.get('gift_card_code')
            gift_cards_ids = attrs.get('gift_cards_ids')

            if (
                not bool(payment_method)
                and not bool(external_payment_method)
                and not bool(gift_card_code)
                and not bool(gift_cards_ids)
            ):  # XOR
                raise serializers.ValidationError(
                    'Payment_method xor external_payment_method ' 'should be provided',
                    code='missing_method',
                )

            if payment_method and payment_method.provider != PaymentProviderEnum.FAKE_PROVIDER:
                business_payment_provider_code = (
                    PaymentRowService.map_payment_provider_code_into_payment_provider_enum(
                        TransactionService.get_payment_provider_code(
                            pos=self.context['pos'], payment_type_code=PaymentTypeEnum.PAY_BY_APP
                        )
                    )
                )

                if payment_method.provider != business_payment_provider_code:
                    raise serializers.ValidationError(
                        'Wrong card is used.', code='wrong_payment_provider'
                    )
            if tokenized_pm_id := sget_v2(payment_method, ['tokenized_pm_id']):
                validation_response = PaymentProvidersPaymentPort.validate_tokenized_pm(
                    tokenized_pm_id=tokenized_pm_id,
                )
                if errors := [x for x in validation_response.errors if x is not None]:
                    error = errors[0]
                    raise serializers.ValidationError(
                        detail=error.label,
                        code=error.value,
                    )

        cancellation_fee = attrs['cancellation_fee_total']
        pos = self.context.get('pos')

        payment_possibilities = check_tokenized_payments_v2(
            appointment_data=self.context.get('appointment_data'),
            is_cancelation=bool(cancellation_fee),
            stripe_enabled=pos.force_stripe_pba if pos else False,
            allow_blik=bool(attrs['prepayment_total']),
            is_booksy_pay_available=self.context.get('is_booksy_pay_available'),
        )

        if (
            attrs.get('external_payment_method')
            and not payment_possibilities[
                get_attribute(attrs, ['external_payment_method', 'card_type'])
            ]
        ):
            raise serializers.ValidationError(
                _('Payment is not possible'), code='payment_not_possible'
            )

    def _validate_compatibilities(self, attrs):
        """Check if app supports prepayment."""

        compatibilities = [
            key for key, value in list(self.context.get('compatibilities', {}).items()) if value
        ]
        if attrs['prepayment'] and 'prepayment' not in compatibilities:
            raise serializers.ValidationError(
                {'prepayment_needed': _('Your app does not support services with deposits.')},
                code='prepayment_not_supported',
            )

    def validate(self, attrs):
        attrs = super().validate(attrs)
        # Set context for child serializers
        self.context['booking_process'] = True

        if self.appointment_already_prepaid:
            return self.no_fees

        attrs.update(self._get_extra_input_data())
        self._validate_compatibilities(attrs)

        tip_part = self._calculate_tip(attrs)
        attrs.update(tip_part)

        attrs['prepayment_total'] = self._calculate_prepayment_total(attrs)

        if self.context.get('edit'):  # it means that is "edit flow"
            appointment_data = self.context['appointment_data']
            txn = Transaction.objects.by_appointment_id(appointment_data.appointment_id)
            if not (attrs['prepayment_total'] and txn):
                raise serializers.ValidationError(
                    _('You cannot add a deposit to pre-existing transactions.'),
                    code='prepayment_already_exists',
                )
        if not attrs['dry_run']:
            self._validate_payment_methods(attrs)

        # MVP: if Booksy Gift Card Used - remove all no show protection types and tips
        if attrs.get('gift_card_code') or attrs.get('gift_cards_ids'):
            self._reset_nsp_and_tip_amount(attrs)
            if CheckIfBGCAlreadyAssignedToAppointment():
                self._validate_if_bgc_already_used(attrs)

        return attrs

    @staticmethod
    @using_db_for_reads(READ_ONLY_DB)
    def _validate_if_bgc_already_used(attrs):

        if attrs.get('gift_card_code'):
            gift_cards_ids = attrs.get('gift_card_code').gift_cards_ids
        elif attrs.get('gift_cards_ids'):
            gift_cards_ids = attrs.get('gift_cards_ids').gift_cards_ids
        else:
            return

        if BooksyGiftCard.objects.filter(external_id__in=gift_cards_ids).exists():
            raise serializers.ValidationError(
                _('Invalid Booksy Gift Card. Please contact our support'),
                code='gift_card_already_assigned',
            )

    @staticmethod
    def _reset_nsp_and_tip_amount(attrs: dict):
        attrs['cancellation_fee_total'] = Decimal('0.00')
        attrs['cancellation_fee'] = Decimal('0.00')
        attrs['cancellation_fee_tax'] = Decimal('0.00')
        attrs['prepayment_total'] = Decimal('0.00')
        attrs['prepayment_tax'] = Decimal('0.00')
        attrs['prepayment'] = Decimal('0.00')
        attrs['service_fee'] = Decimal('0.00')
        attrs['tip_base'] = Decimal('0.00')
        if attrs.get('tip'):
            attrs['tip']['rate'] = Decimal('0.00')
            attrs['tip']['amount'] = Decimal('0.00')

    @staticmethod
    def _calculate_prepayment_total(attrs: dict):
        return (
            attrs['prepayment']
            + attrs['prepayment_tax']
            + (attrs['tip']['amount'] if attrs.get('tip') else Decimal(0))
        )

    def save(self):
        """Creates transaction with prepayment / cancellation fee.
        Using TransactionSerializer.
        """
        from webapps.business.models import Business

        checkout: AppointmentCheckout = self.context['appointment_checkout']
        total_value: Decimal = checkout.total.value
        appointment_data: AppointmentData = self.context['appointment_data']

        device_data = DeviceDataDict(
            fingerprint=self.context.get('device_fingerprint', ''),
            phone_number=self.context.get('cell_phone', ''),
            user_agent=self.context.get('user_agent', ''),
        )
        extra_data = self.context.get('extra_data')

        if not (
            self.validated_data['prepayment_total'] or self.validated_data['cancellation_fee_total']
        ):
            if gift_card_data := self.validated_data.get(
                'gift_card_code'
            ) or self.validated_data.get('gift_cards_ids'):
                # add hold for max balance from Booksy Gift Card
                transaction = create_gift_card_transaction(
                    total_value=total_value,
                    subbookings_ids=appointment_data.subbookings_ids,
                    pos=self.context_pos,
                    user=self.context.get('user'),
                    available_balance=self.context.get('available_giftcard_balance', 0),
                )
                provider = get_payment_provider(
                    codename=gift_card_data.provider,
                    txn=transaction,
                )
                # Only 1 PR should exist
                prepayment_row = transaction.latest_receipt.payment_rows.get()

                provider.make_payment(
                    transaction=transaction,
                    payment_method=gift_card_data,
                    payment_row=prepayment_row,
                    device_data=device_data,
                    extra_data=extra_data,
                    trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
                    gift_cards_ids=self.context.get('gift_cards_ids'),
                )
                TransactionService.check_prepayment_auth_success(txn=transaction)
            return

        tip = self._get_tip_by_hand()
        payment_method = self._get_payment_method()

        if self.validated_data['cancellation_fee_total']:
            # TODO::FLS-3976
            transaction = create_cancellation_fee_transaction(
                checkout=checkout, pos=self.context_pos, user=self.context.get('user')
            )
            provider = get_payment_provider(
                codename=payment_method.provider,
                txn=transaction,
            )

            expiration_date_validation = appointment_data.booked_from_in_db
            transaction = provider.authorize_deposit(
                transaction,
                payment_method=payment_method,
                extra_data=extra_data,
                device_data=device_data,
                expiration_date_validation=expiration_date_validation,
            )
            TransactionService.check_cancellation_fee_auth_success(txn=transaction)

        if self.validated_data['prepayment_total']:
            transaction = create_prepayment_transaction(
                prepaid_total=checkout.prepayment.total,
                subbookings_ids=appointment_data.subbookings_ids,
                pos=self.context_pos,
                tip=tip,
                prepayment=self.validated_data['prepayment_total'],
                user=self.context.get('user'),
            )
            provider = get_payment_provider(
                codename=payment_method.provider,
                txn=transaction,
            )
            # Only 1 PR should exist
            prepayment_row = transaction.latest_receipt.payment_rows.get()

            provider.make_payment(
                transaction=transaction,
                payment_method=payment_method,
                payment_row=prepayment_row,
                device_data=device_data,
                extra_data=extra_data,
                trigger=BasketPaymentAnalyticsTrigger.CUSTOMER__PREPAYMENT_AUTH,
            )
            TransactionService.check_prepayment_auth_success(txn=transaction)

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        if not Decimal(ret.get('prepayment_total') or '0') and 'tip_choices' in ret:
            del ret['tip_choices']

        if not self.instance:  # it is NOT for GET http method
            TipChoicesSerializer.mark_as_main_tip(ret)

        return ret


class DecimalSchemaSerializerMethodField(SchemaTypeSerializerMethodField):

    def __init__(
        self,
        schema_field: t.Callable = fields.DecimalField,
        field_kwargs: t.Optional[dict] = None,
        **kwargs,
    ):
        if field_kwargs is None:
            field_kwargs = {}
        if schema_field == fields.DecimalField and 'coerce_to_string' not in field_kwargs:
            field_kwargs['coerce_to_string'] = True
        super().__init__(
            schema_field=schema_field,
            field_kwargs=field_kwargs,
            **kwargs,
        )

    def to_representation(self, value) -> str:
        return str(super().to_representation(value) or Decimal('0.00'))


class CustomerAppointmentCheckoutSerializer(CustomerAppointmentTransactionSerializer):
    """Creates cancellation fee / prepayment / Booksy Pay payment transaction during booking."""

    # Cancellation fee
    cancellation_fee = DecimalSchemaSerializerMethodField(
        field_kwargs={
            'max_digits': 10,
            'decimal_places': 2,
        },
    )
    cancellation_fee_tax = DecimalSchemaSerializerMethodField(
        field_kwargs={
            'max_digits': 10,
            'decimal_places': 2,
        },
    )
    cancellation_fee_total = DecimalSchemaSerializerMethodField(
        field_kwargs={
            'max_digits': 10,
            'decimal_places': 2,
        },
    )

    # Prepayment
    prepayment = DecimalSchemaSerializerMethodField(
        field_kwargs={
            'max_digits': 10,
            'decimal_places': 2,
        },
    )
    prepayment_tax = DecimalSchemaSerializerMethodField(
        field_kwargs={
            'max_digits': 10,
            'decimal_places': 2,
        },
    )
    prepayment_total = DecimalSchemaSerializerMethodField(
        field_kwargs={
            'max_digits': 10,
            'decimal_places': 2,
        },
    )

    # Booksy Pay
    booksy_pay = DecimalSchemaSerializerMethodField(
        field_kwargs={
            'max_digits': 10,
            'decimal_places': 2,
        },
    )
    booksy_pay_tax = DecimalSchemaSerializerMethodField(
        field_kwargs={
            'max_digits': 10,
            'decimal_places': 2,
        },
    )
    booksy_pay_total = DecimalSchemaSerializerMethodField(
        field_kwargs={
            'max_digits': 10,
            'decimal_places': 2,
        },
    )

    external_payment_method = None

    def get_to_pay_later(self, obj):
        to_pay_later = super().get_to_pay_later(obj)
        if to_pay_later and (
            self.context.get('booksy_pay_flow_enabled', False)
            or self.context.get('is_paid_by_booksy_pay', False)
        ):
            checkout = self.context['appointment_checkout']
            return str(
                checkout.total.value - checkout.booksy_pay.value - self._get_prepayment_tax_value()
            )

        return to_pay_later

    @staticmethod
    def get_cancellation_fee(instance) -> Decimal:
        return sget_v2(instance, ['checkout', 'cancellation_fee', 'value']) or Decimal('0.00')

    @staticmethod
    def get_cancellation_fee_tax(instance) -> Decimal:
        return sget_v2(instance, ['checkout', 'cancellation_fee', 'excluded_tax']) or Decimal(
            '0.00'
        )

    def get_cancellation_fee_total(self, instance) -> Decimal:
        return round_currency(
            self.get_cancellation_fee(instance) + self.get_cancellation_fee_tax(instance)
        )

    @staticmethod
    def get_prepayment(instance) -> Decimal:
        return sget_v2(instance, ['checkout', 'prepayment', 'value']) or Decimal('0.00')

    def get_prepayment_tax(self, instance) -> Decimal:
        business = self.context['business']
        service_fee = sget_v2(business, ['pos', 'service_fee']) or Decimal(0)
        if self.get_prepayment(instance) == 0:
            prepayment_tax = Decimal('0.00')
        else:
            prepayment_tax = sget_v2(
                instance, ['checkout', 'prepayment', 'excluded_tax']
            ) or Decimal('0.00')
            prepayment_tax += service_fee
        return prepayment_tax

    def get_prepayment_total(self, instance) -> Decimal:
        return self.get_prepayment(instance) + self.get_prepayment_tax(instance)

    @staticmethod
    def get_booksy_pay(instance) -> Decimal:
        return sget_v2(instance, ['checkout', 'booksy_pay', 'value']) or Decimal('0.00')

    def get_booksy_pay_tax(self, instance) -> Decimal:
        business = self.context['business']
        service_fee = sget_v2(business, ['pos', 'service_fee']) or Decimal(0)
        if self.get_booksy_pay(instance) == 0:
            booksy_pay_tax = Decimal('0.00')
        else:
            booksy_pay_tax = sget_v2(
                instance, ['checkout', 'booksy_pay', 'excluded_tax']
            ) or Decimal('0.00')
            booksy_pay_tax += service_fee

        return booksy_pay_tax

    def get_booksy_pay_total(self, instance) -> Decimal:
        return self.get_booksy_pay(instance) + self.get_booksy_pay_tax(instance)

    def to_representation(self, instance):
        ret = super().to_representation(instance)

        if (
            not (
                Decimal(ret.get('prepayment_total') or '0')
                or Decimal(ret.get('booksy_pay_total') or '0')
            )
            and 'tip_choices' in ret
        ):
            del ret['tip_choices']

        TipChoicesSerializer.mark_as_main_tip(ret)

        return ret

    def validate(self, attrs):
        if self.appointment_already_prepaid:
            return self.no_fees
        attrs = super().validate(attrs)
        self._validate_compatibilities(attrs)
        return attrs


"""
Transaction Actions Serializers
"""


class BusinessTransactionActionRequest(serializers.Serializer):
    """
    Perform an action on Transaction by Business.

    Context requirements:
        * pos - a POS instance
        * operator - a User instance - for register vs charge deposit action

    """

    action = serializers.ChoiceField(choices=enums.BUSINESS_ACTIONS)
    payment_type_code = pos_fields.PaymentTypeCodeRelatedField(
        queryset_attr=['payment_types'],
        source='payment_type',
        write_only=True,
        required=False,
    )
    payment_status = serializers.ChoiceField(
        choices=[receipt_status.PAYMENT_SUCCESS, receipt_status.PAYMENT_FAILED],
        write_only=True,
        required=False,
    )
    pnref = CharField(max_length=128, null=True, blank=True)
    row_id = serializers.IntegerField(required=False, write_only=True)
    payment_token = serializers.CharField(
        required=False, allow_blank=True, allow_null=True, write_only=True
    )

    def __init__(self, instance=None, data=serializers.empty, **kwargs):
        super().__init__(instance=instance, data=data, **kwargs)

        # conditionally required fields
        if self.initial_data.get('action') == enums.BUSINESS_ACTION__SET_PAYMENT_TYPE:
            self.fields['payment_type_code'].required = True

        if self.initial_data.get('action') == enums.BUSINESS_ACTION__SET_PAYMENT_STATUS:
            self.fields['payment_status'].required = True

        if self.initial_data.get('action') == enums.BUSINESS_ACTION__RETRY_KIP:
            self.fields['payment_token'].required = True

    def validate_action(self, action):
        if (
            action == enums.BUSINESS_ACTION__CHARGE_DEPOSIT
            and self.context['pos'].registers_enabled
            and not self.context['pos'].get_open_register(
                operator=self.context['operator'],
                selected_register_id=self.context.get('selected_register_id'),
                takeover_when_only_one_is_open=self.context.get('is_frontdesk'),
            )
        ):
            raise serializers.ValidationError(
                _('To charge a cancellation fee, ' 'please open the register first.'),
                code='register',
            )

        return action

    def _validate_row_id(self, data):
        payment_rows = self.instance.latest_receipt.payment_rows.all()
        if len(payment_rows) == 1 and 'row_id' not in data:
            data['row_id'] = payment_rows[0].id

        if not data.get('row_id'):
            raise serializers.ValidationError(
                {
                    'row_id': _('This field is required'),
                },
                code='required',
            )

        if data.get('row_id') not in [x.id for x in payment_rows]:
            raise serializers.ValidationError(
                {
                    'row_id': _('There is no Payment Row with this ID'),
                },
                code='not_included',
            )

    def validate(self, data):
        if data['action'] == enums.BUSINESS_ACTION__SET_PAYMENT_STATUS:
            self._validate_row_id(data)

        if not self.instance.action_allowed(data.get('action')):
            raise serializers.ValidationError(_('Action is not allowed'))

        return data


class CustomerTransactionActionRequest(serializers.Serializer):
    action = serializers.ChoiceField(choices=enums.CUSTOMER_ACTIONS)
    tip = TransactionTipSerializer(required=False)
    row_id = serializers.IntegerField(required=False)
    payment_method = pos_fields.UserRelatedField(  # limited to context['user']
        queryset=PaymentMethod.objects.all(),
        required=False,
    )
    external_payment_method = ExternalPaymentMethodSerializer(required=False)

    def __init__(self, instance=None, data=serializers.empty, **kwargs):
        super(CustomerTransactionActionRequest, self).__init__(
            instance=instance, data=data, **kwargs
        )

    def validate_action(self, action):
        if not self.instance.action_allowed(action):
            raise serializers.ValidationError(_('Action is not allowed'))
        return action

    def _validate_row_id(self, data):
        payment_rows = self.instance.latest_receipt.payment_rows.all()

        if data.get('row_id') not in {x.id for x in payment_rows}:
            raise serializers.ValidationError(
                {
                    'row_id': _('There is no Payment Row with this ID.'),
                },
                code='not_included',
            )
        return data

    def _validate_tip(self, data):
        if data.get('tip') is None:
            raise serializers.ValidationError(_('Tip should be provided.'))

        calculation_data = {
            'discounted_subtotal_services': self.instance.discounted_subtotal_services,
            'discounted_subtotal_products': self.instance.discounted_subtotal_products,
            'taxed_subtotal_services': self.instance.taxed_subtotal_services,
            'taxed_subtotal_products': self.instance.taxed_subtotal_products,
            'tip_base_services': (
                self.instance.subtotal_services
                + self.instance.discounted_subtotal_services
                - self.instance.discounted_subtotal_services
            ),
            'tip_base_products': (
                self.instance.subtotal_products
                + self.instance.discounted_subtotal_products
                - self.instance.discounted_subtotal_products
            ),
            'tip': data.get('tip'),
            # Temporary variable
            'already_paid': self.instance.latest_receipt.already_paid or Decimal('0'),
            'tip_already_paid': round_currency(
                sum(
                    row.tip_amount
                    for row in self.instance.payment_rows.all() or []
                    if row.locked and row.tip_amount
                )
            ),
        }

        tip_calculator = TipCalculator(
            calculation_data,
            get_pos_settings(self.instance.pos),
        )
        calculated = tip_calculator.calculate_tips()

        if calculated['tip']['amount'] < calculation_data['tip_already_paid']:
            raise serializers.ValidationError(
                _('You can not give less tip then you have already given.')
            )

    def _validate_amount(self, row_id):
        if not row_id:
            return
        minimal_payment = get_minimal_pba_amount()
        pr = PaymentRow.objects.get(id=row_id)
        if pr.amount < minimal_payment:
            raise serializers.ValidationError(
                {
                    'payment_rows': _('Amount must be greater or equal to {}').format(
                        format_currency(minimal_payment),
                    )
                },
                code='pay_by_app_minimal_amount',
            )

    def validate(self, data):
        self._validate_row_id(data)

        if data['action'] == enums.CUSTOMER_ACTION__SET_TIP_RATE:
            self._validate_tip(data)

        if data['action'] == enums.CUSTOMER_ACTION__MAKE_PAYMENT:
            payment_method = data.get('payment_method')
            external_payment_method = data.get('external_payment_method')

            if bool(payment_method) == bool(external_payment_method):  # XOR
                raise serializers.ValidationError(
                    'Payment_method xor external_payment_method ' 'should be provided',
                    code='missing_method',
                )

            if payment_method and payment_method.provider != PaymentProviderEnum.FAKE_PROVIDER:
                business_payment_provider_code = (
                    PaymentRowService.map_payment_provider_code_into_payment_provider_enum(
                        TransactionService.get_payment_provider_code(
                            pos=self.instance.pos,
                            payment_type_code=PaymentTypeEnum.PAY_BY_APP,
                            txn=self.instance,
                        )
                    )
                )

                if payment_method.provider != business_payment_provider_code:
                    raise serializers.ValidationError(
                        'Wrong card is used.', code='wrong_payment_provider'
                    )

            self._validate_amount(data.get('row_id'))

        return data


class PaymentMethodRequestSerializer(serializers.Serializer):
    payment_method = pos_fields.UserRelatedField(  # limited to context['user']
        queryset=PaymentMethod.objects.all(),
        required=False,
        allow_null=True,
    )

    external_payment_method = ExternalPaymentMethodSerializer(
        required=False,
        allow_null=True,
    )

    def validate(self, data):
        payment_method = data.get('payment_method')
        external_payment_method = data.get('external_payment_method')

        if bool(payment_method) == bool(external_payment_method):  # XOR
            raise serializers.ValidationError(
                'Payment_method xor external_payment_method should be provided',
                code='missing_method',
            )

        return data


"""
Products Serializers
"""


class BaseSearchRequestSerializer(serializers.Serializer):

    per_page = serializers.IntegerField(min_value=0, default=20, max_value=1000)
    page = serializers.IntegerField(min_value=1, default=1)

    def get_objects(self, data):
        raise NotImplementedError


class ProductsSearchRequestSerializer(RequiredContextMixin, BaseSearchRequestSerializer):
    required_context = ('pos',)

    query = serializers.CharField(required=False)
    # don't validate categories IDs
    category = serializers.IntegerField(required=False)
    resource_id = serializers.IntegerField(required=False)
    all_warehouses = serializers.BooleanField(required=False, default=False)
    sale = serializers.BooleanField(required=False, default=False)
    excluded = ItemOrListSerializerField(
        child=serializers.IntegerField(),
        required=False,
    )
    ids = ItemOrListSerializerField(
        child=serializers.IntegerField(),
        required=False,
    )

    def get_products(self, data):
        products = Commodity.objects.filter(
            business=self.context['pos'].business,
        ).order_by('order')

        category_id = data.get('category')
        if category_id is not None:
            if category_id == 0:
                products = products.filter(category_id__isnull=True)
            elif category_id > 0:
                products = products.filter(category_id=category_id)

            products = products.filter(archived=category_id == -1)
        else:
            products = products.filter(archived=False)

        query = data.get('query')
        if query:
            products = products.filter(
                Q(name__icontains=data['query'])
                | Q(category__name__icontains=data['query'])
                | Q(product_code__startswith=query)
            )
        if data.get('sale'):
            products = products.filter(
                product_type=Commodity.TYPE_RETAIL,
            )
        if data.get('excluded'):
            products = products.exclude(id__in=data.get('excluded'))
        if data.get('ids'):
            products = products.filter(id__in=data.get('ids'))

        return products

    def get_stock_levels(self, data):
        stock_levels = CommodityStockLevel.objects.filter(
            commodity__business=self.context['pos'].business,
            commodity__deleted__isnull=True,
            warehouse__deleted__isnull=True,
        ).prefetch_related('commodity', 'warehouse')

        category_id = data.get('category')
        if category_id is not None:
            if category_id == 0:
                stock_levels = stock_levels.filter(
                    commodity__category_id__isnull=True,
                )
            elif category_id > 0:
                stock_levels = stock_levels.filter(
                    commodity__category_id=category_id,
                )

            stock_levels = stock_levels.filter(
                commodity__archived=category_id == -1,
            )
        else:
            stock_levels = stock_levels.filter(commodity__archived=False)

        query = data.get('query')
        if query:
            stock_levels = stock_levels.filter(
                Q(commodity__name__icontains=query) | Q(commodity__product_code__startswith=query),
            )
        if data.get('sale'):
            stock_levels = stock_levels.filter(
                commodity__product_type=Commodity.TYPE_RETAIL,
            )
        return stock_levels.order_by(
            'commodity__order',
            Lower('warehouse__name'),
        )

    def get_objects(self, data):
        return self.get_products(data)


class ServiceVariantsSearchRequestSerializer(RequiredContextMixin, BaseSearchRequestSerializer):
    required_context = ('pos',)

    query = serializers.CharField(required=False)
    category_id = serializers.IntegerField(required=False)
    resource_id = serializers.IntegerField(required=False)
    excluded = ItemOrListSerializerField(
        child=serializers.IntegerField(),
        required=False,
    )
    ids = ItemOrListSerializerField(
        child=serializers.IntegerField(),
        required=False,
    )

    def get_service_variants(self, data):
        service_variants = (
            ServiceVariant.objects.annotate_is_combo()
            .filter(
                service__business=self.context['pos'].business,
                active=True,
                service__active=True,
                is_combo=False,
            )
            .order_by(
                F('service__service_category__order').asc(nulls_first=True),
                'service__order',
                'id',
            )
        )

        resource_id = data.get('resource_id')
        if resource_id is not None:
            service_variants = service_variants.filter(
                Q(service__resources=resource_id) | Q(resources=resource_id)
            )

        category_id = data.get('category_id')
        if category_id is not None:
            if category_id == 0:
                service_variants = service_variants.filter(service__service_category__isnull=True)
            else:
                service_variants = service_variants.filter(service__service_category=category_id)

        query = data.get('query')
        if query:
            service_variants = service_variants.filter(
                Q(service__name__icontains=query)
                | Q(service__service_category__name__icontains=query)
            )

        if data.get('excluded'):
            service_variants = service_variants.exclude(id__in=data.get('excluded'))
        if data.get('ids'):
            service_variants = service_variants.filter(id__in=data.get('ids'))

        return service_variants

    def get_objects(self, data):
        return self.get_service_variants(data).select_related('service')


class BusinessItemsSearchRequestSerializer(BaseSearchRequestSerializer):
    OBJECT_TYPES = {
        'product': Commodity,
        'service': Service,
        'service-variant': ServiceVariant,
        'addon': ServiceAddOn,
    }

    query = serializers.CharField(default='')
    object_type = serializers.ListField(
        child=serializers.ChoiceField(OBJECT_TYPES),
        default=lambda: list(BusinessItemsSearchRequestSerializer.OBJECT_TYPES.keys()),
    )
    customer_id = serializers.IntegerField(required=False)
    sort_order = serializers.ChoiceField(
        required=False,
        choices=[
            ('name', 'Name'),
            ('popularity', 'Popularity'),
            ('score', 'Relevancy'),
        ],
    )

    def validate(self, attrs):
        attrs = super().validate(attrs)

        if attrs.get('object_type'):
            models = [self.OBJECT_TYPES[value] for value in attrs['object_type']]
        else:
            models = list(self.OBJECT_TYPES.values())

        attrs['models'] = models
        return attrs


"""
Payment Methods
"""


class PaymentMethodSerializer(serializers.ModelSerializer):
    payment_method_id = serializers.IntegerField(read_only=True, source='id')
    zip_code = serializers.CharField(read_only=True)
    confirmed = serializers.SerializerMethodField(read_only=True)
    internal_status = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = PaymentMethod
        exclude = (
            'id',
            'provider_ref',
            'user',
            'created',
            'updated',
            'deleted',
        )

    @staticmethod
    def get_confirmed(instance: PaymentMethod) -> bool:
        return True

    @staticmethod
    def get_internal_status(instance: PaymentMethod) -> TokenizedPaymentMethodInternalStatus:
        internal_status = get_tokenized_pm_internal_status(instance)
        return (
            internal_status
            if internal_status is not None
            else TokenizedPaymentMethodInternalStatus.VALID
        )


class PaymentMethodCreditCardSerializer(serializers.Serializer):
    card_type = serializers.ChoiceField(required=False, allow_null=True, choices=enums.CARD_TYPES)
    card_number = pos_fields.CardNumberField()
    card_holder = serializers.CharField()
    expiration_month = serializers.IntegerField(min_value=1, max_value=12)
    expiration_year = serializers.IntegerField(
        min_value=tznow().year,
        max_value=2099,
    )
    cvc_code = serializers.CharField(
        min_length=3,
        max_length=4,
        validators=[validators.IsDigit(_('CVC code should contain only digits'))],
    )

    def validate(self, attrs):
        if attrs.get('expiration_month') and attrs.get('expiration_year'):
            now = tznow()
            if attrs['expiration_year'] < now.year or (
                attrs['expiration_year'] == now.year and attrs['expiration_month'] < now.month
            ):
                raise serializers.ValidationError(_('This card is expired'))

        # autodetect card_type
        if attrs.get('card_number') and not attrs.get('card_type'):
            attrs['card_type'] = detect_card_type(attrs['card_number'])
        return attrs


class PaymentMethodCreateSerializer(serializers.Serializer):
    credit_card = PaymentMethodCreditCardSerializer(required=False)
    encrypted_data = serializers.CharField(required=True)
    default = serializers.BooleanField(default=True)
    billing_address = serializers.DictField(required=False)

    def validate(self, attrs):
        if (
            settings.MARKET_PAY_AVS_ENABLED
            and attrs.get('billing_address')
            and attrs['billing_address'].get('postalCode', '')
            and not re.match(
                settings.AVS_ZIPCODE_REGEXP, attrs['billing_address'].get('postalCode', '')
            )
        ):
            raise serializers.ValidationError(_('Invalid zip code provided'))

        user = self.context.get('user')
        max_payment_limit = 10
        last_week = tznow() - timedelta(days=7)
        # always check all cards added by user
        cardholder_ids = Cardholder.objects.filter(
            email=user.email,
        ).values_list('id', flat=True)

        card_qs = (
            Card.all_objects.filter(
                cardholder_id__in=Subquery(cardholder_ids),
                # use updated to check if given card was modified
                updated__gte=last_week,
            )
            .order_by(
                # disable ordering
            )
            .values(
                'last_4_digits',
            )
            .annotate(number_authorized_cards=Count('id'))
            .filter(number_authorized_cards__gte=max_payment_limit)
        )

        if settings.LIVE_DEPLOYMENT and card_qs.exists():
            raise serializers.ValidationError(
                _('Something went wrong. Please contact our CS team.')
            )
        return attrs


"""
Commissions
"""


class StaffCommissionDefaultSerializer(CommissionBaseSerializer):

    class Meta:
        model = StaffCommissionDefault
        exclude = ('resource', 'id')


#####################################################################
# Those are unified serializers, made so returned data formats are
# the same for products and services


class UnifiedCommissionProductRatesSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    name = serializers.CharField()
    formatted_price = pos_fields.TypedPriceField(source='item_price')
    item_price = serializers.DecimalField(max_digits=10, decimal_places=2)
    description = serializers.CharField(source='product_code')
    commission_type = serializers.CharField()
    commission_rate = serializers.DecimalField(max_digits=10, decimal_places=2)
    commission_value = serializers.SerializerMethodField()

    alert = serializers.BooleanField()
    photo_url = serializers.CharField(read_only=True, source='photo.full_url')

    stock_quantity = serializers.IntegerField(
        source='full_packages_left_in_default_warehouse', read_only=True
    )

    def get_commission_value(self, obj):
        if obj.commission_type == '%':
            return round_currency((obj.item_price or 0) * obj.commission_rate / 100)
        else:
            return round_currency(obj.commission_rate)


class UnifiedCommissionServiceRatesSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    name = serializers.CharField(source='service.name')
    formatted_price = serializers.SerializerMethodField()
    item_price = serializers.DecimalField(max_digits=10, decimal_places=2, source='price')
    price_type = serializers.ChoiceField(
        choices=PriceType.choices(),
        source='type',
    )
    description = serializers.CharField(source='service.description')
    commission_type = serializers.CharField()
    commission_rate = serializers.DecimalField(max_digits=10, decimal_places=2)
    commission_value = serializers.SerializerMethodField()

    alert = serializers.BooleanField()
    color = serializers.SerializerMethodField()

    duration = DurationField()

    def get_formatted_price(self, instance):
        return instance.format_price()

    def get_color(self, obj):
        service = obj.service
        if service.color:
            return service.color

        color = service_color(
            service_id=get_attribute(service, ['id']),
            category_id=get_attribute(service, ['service_category_id']),
            single_category=self.context.get('single_category'),
        )
        return color

    def get_commission_value(self, obj):
        if obj.commission_type == '%':
            return round_currency((obj.price or 0) * obj.commission_rate / 100)
        else:
            return round_currency(obj.commission_rate)


class UnifiedCommissionVoucherRatesSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    name = serializers.CharField()
    formatted_price = pos_fields.TypedPriceField(source='item_price')
    item_price = serializers.DecimalField(max_digits=10, decimal_places=2)
    description = serializers.CharField()
    commission_type = serializers.CharField()
    commission_rate = serializers.DecimalField(max_digits=10, decimal_places=2)
    commission_value = serializers.SerializerMethodField()
    alert = serializers.BooleanField()

    def get_commission_value(self, obj):
        if obj.commission_type == '%':
            return round_currency(obj.item_price * obj.commission_rate / 100)
        else:
            return round_currency(obj.commission_rate)


class ServiceNoShowProtectionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Service
        fields = (
            'id',
            'name',
            'no_show_protection',
        )

    id = serializers.IntegerField(required=True)
    name = serializers.CharField(read_only=True)
    no_show_protection = NoShowProtectionServiceVariantField(
        required=True,
        allow_null=True,
    )

    def validate(self, attrs: dict) -> dict:
        """
        :raises ValidationError: if the data provided is not correct
        """
        validated_data = super().validate(attrs)
        if no_show_protection := validated_data.get('no_show_protection'):
            service_variants = ServiceVariant.objects.filter(
                service_id=validated_data['id'],
                active=True,
            ).annotate(
                _custom_prices=ArrayAgg(
                    'combo_parents_through__price',
                    filter=Q(
                        combo_parents_through__combo__combo_pricing=ComboPricing.CUSTOM,
                        combo_parents_through__combo__service__active=True,
                        combo_parents_through__combo__service__deleted__isnull=True,
                        combo_parents_through__deleted__isnull=True,
                    ),
                    distinct=True,
                ),
            )
            for service_variant in service_variants:
                if no_show_protection['payment_type'] == NoShowProtectionType.PREPAYMENT:
                    if validate_package_with_prepayment(service_variant.id):
                        service_name = service_variant.service.name
                        raise serializers.ValidationError(
                            _(
                                "Deposit can't be set, because {service_name} "
                                + "is a part of an existing package."
                            ).format(service_name=service_name),
                            code='package_with_pp',
                        )

                validate_and_convert_percent_to_amount(
                    no_show_protection,
                    service_variant.price,
                    validate_only=True,
                )
                for (
                    custom_price
                ) in service_variant._custom_prices:  # pylint: disable=protected-access
                    if custom_price is None:
                        continue
                    validate_and_convert_percent_to_amount(
                        no_show_protection,
                        custom_price,
                        validate_only=True,
                    )

        return validated_data


class ServicePaymentSerializer(serializers.ModelSerializer):
    no_show_protection = NoShowProtectionServiceVariantField(
        required=False,
        allow_null=True,
    )
    color = serializers.SerializerMethodField()
    variants = ServiceVariantSerializer(
        source='allowed_variants',
        read_only=True,
        many=True,
        fields=['id', 'type', 'price', 'duration'],
    )

    class Meta:
        model = Service
        fields = (
            'id',
            'name',
            'no_show_protection',
            'color',
            'variants',
        )
        extra_kwargs = {
            'id': {'read_only': False},
        }

    def to_representation(self, instance):
        instance.no_show_protection = self.get_no_show_protection(instance)
        return super().to_representation(instance)

    def get_no_show_protection(self, service):
        """
        Handler verifies that this business has service level no show
        protection enabled, so we are sure that all service variants
        have the same no show protection and we pick first.
        """
        return ServiceVariantPayment.objects.filter(
            service_variant__service=service,
            service_variant__deleted__isnull=True,
            service_variant__active=True,
        ).first()

    @staticmethod
    def get_color(instance):
        if instance.color:
            return instance.color
        return service_color(
            service_id=instance.id,
            category_id=instance.service_category_id,
            single_category=instance.business.is_single_category,
        )


class NoShowFeeSerializer(serializers.ModelSerializer):

    class Meta:
        model = POS
        fields = (
            'no_show_cancel_time_option',
            'no_show_protection_policy',
            'service_categories',
            'no_show_cancel_time_options',
            'minimal_pay_by_app_payment',
            'max_pay_by_app_payment',
            'auto_charge_cancellation_fee',
            'no_show_protection_active',
        )

    no_show_cancel_time_option = RelativedeltaField(source='deposit_cancel_time')
    no_show_protection_policy = serializers.CharField(
        source='deposit_policy',
        max_length=5000,
    )
    no_show_protection_active = serializers.SerializerMethodField()
    service_categories = serializers.SerializerMethodField(required=False)
    no_show_cancel_time_options = serializers.SerializerMethodField()

    minimal_pay_by_app_payment = serializers.SerializerMethodField()
    max_pay_by_app_payment = serializers.SerializerMethodField()

    def __init__(self, *args, **kwargs):
        super(NoShowFeeSerializer, self).__init__(*args, **kwargs)
        self.is_first_run_cf = True
        self.is_first_run_pp = True

    @staticmethod
    def get_service_prefetches(exclude_sv):
        return [
            Prefetch('service_category', queryset=ServiceCategory.objects.all().order_by('order')),
            Prefetch(
                'service_variants',
                queryset=ServiceVariant.objects.filter(
                    ~(Q(id__in=exclude_sv)),
                    active=True,
                    type__in=PriceType.has_price(),
                )
                .select_related('payment')
                .order_by('duration'),
                to_attr='allowed_variants',
            ),
        ]

    def get_service_categories(self, instance):
        business = self.context.get('business')
        # Default is PP. It ensure that old apps won't make something stupid
        no_show_type = self.context.get('no_show_type', NoShowType.PREPAYMENT)
        if not no_show_type or no_show_type == NoShowType.ALL:
            no_show_types = [
                ServiceVariantPayment.PRE_PAYMENT_TYPE,
                ServiceVariantPayment.CANCELLATION_FEE_TYPE,
            ]
        else:
            no_show_types = [no_show_type]

        if business:
            # take all active services
            # with
            #   1) categories
            #   2) service_variants
            #   3) service_variant_payments
            active_service_ids = business.services.filter(
                deleted__isnull=True,
                active=True,
            ).values_list('id', flat=True)

            # Only PP is restricted
            if ServiceVariantPayment.PRE_PAYMENT_TYPE in no_show_types:
                sv_with_vouchers = list(
                    VoucherTemplateServiceVariant.objects.filter(
                        service_variant__service__in=active_service_ids,
                    ).values_list('service_variant', flat=True)
                ) + list(
                    VoucherServiceVariant.objects.filter(
                        service_variant__service__in=active_service_ids,
                    ).values_list('service_variant', flat=True)
                )
            else:
                sv_with_vouchers = []

            services = (
                business.services.filter(
                    deleted__isnull=True,
                    active=True,
                )
                .filter(
                    # Prefetched service variants are filtered out inside
                    # get_service_prefetches(). We don't want to return services
                    # that don't have any allowed service variants so we must also
                    # filter services.
                    ~(Q(service_variants__id__in=sv_with_vouchers)),
                    service_variants__active=True,
                    service_variants__type__in=PriceType.has_price(),
                )
                .prefetch_related(*self.get_service_prefetches(sv_with_vouchers))
                .order_by('order')
                .distinct()
            )
            uncategorised_sv = {
                'category_name': _('Not categorised'),
            }
            uncategorised_sv['services'] = []

            categories = {}
            # iterate though all services and group it by category
            # do not sort it, variants is already sort by order and
            # service_variants by duration
            for service in services:
                service_categories, uncategorised_sv_services = (
                    self.categories_from_service_payment(service)
                )
                uncategorised_sv['services'].extend(uncategorised_sv_services)

                for category_id in service_categories:
                    if category_id in categories:
                        categories[category_id]['services'].extend(
                            service_categories[category_id]['services']
                        )
                    else:
                        categories[category_id] = service_categories[category_id]

                # get helpful fields that will be used in to_representation
                if self.is_first_run_cf:
                    self.is_first_run_cf = not service.active_variants.has_payment(
                        NoShowProtectionType.CANCELLATION_FEE,
                    )
                # if is_first_run is false it mean that we find at least one
                # service with cancellation fee or prepayment
                # so it is not the first time no_show_wizzard run
                if self.is_first_run_pp:
                    self.is_first_run_pp = not service.active_variants.has_payment(
                        NoShowProtectionType.PREPAYMENT,
                    )

            return [uncategorised_sv] + [
                category for category in list(categories.values()) if category.get('services')
            ]

    def categories_from_service_payment(self, service):
        service_categories = {}
        uncategorised_sv_services = []
        service_data = ServicePaymentSerializer(
            instance=service,
        ).data
        if service.service_category is None:
            uncategorised_sv_services.append(service_data)
        else:
            category_id = service.service_category.id
            if category_id in service_categories:
                service_categories[category_id]['services'].append(service_data)
            else:
                service_categories[category_id] = {
                    'category_name': service.service_category.name,
                    'services': [service_data],
                }
        return service_categories, uncategorised_sv_services

    @staticmethod
    def get_minimal_pay_by_app_payment(instance):
        max_amount = TransactionService.get_minimal_pay_by_app_payment()
        return {
            'amount': max_amount,
            'formatted_amount': format_currency(max_amount),
        }

    @staticmethod
    def get_max_pay_by_app_payment(instance):
        max_amount = TransactionService.get_max_pay_by_app_payment()
        return {
            'amount': max_amount,
            'formatted_amount': format_currency(max_amount),
        }

    @staticmethod
    def get_no_show_cancel_time_options(instance):
        return instance.no_show_cancel_time_options

    def get_no_show_protection_active(self, instance: POS) -> bool:
        business = self.context.get('business')
        return is_no_show_protection_active(business.id) if business else False

    def to_representation(self, instance):
        result = super().to_representation(instance)
        # super was fired now we sure we have
        # self.is_first_run_cf and self.is_first_run_pp
        # so we can add them to representation
        first_wizard_run = self.is_first_run_pp and self.is_first_run_cf
        result['is_first_run'] = first_wizard_run
        # result['is_first_run_cf'] = self.is_first_run_cf
        # result['is_first_run_pp'] = self.is_first_run_pp
        return result


class NoShowFeeWizardServiceSerializer(NoShowFeeSerializer):

    class Meta(object):
        model = POS
        fields = (
            'no_show_cancel_time_option',
            'no_show_protection_policy',
            'service_categories',
            'no_show_cancel_time_options',
            'minimal_pay_by_app_payment',
            'max_pay_by_app_payment',
            'auto_charge_cancellation_fee',
            'no_show_protection_active',
        )

    no_show_cancel_time_option = RelativedeltaField(source='deposit_cancel_time')
    no_show_protection_policy = serializers.CharField(
        source='deposit_policy',
        max_length=5000,
    )
    no_show_protection_active = serializers.SerializerMethodField()
    service_categories = serializers.SerializerMethodField(required=False)
    no_show_cancel_time_options = serializers.SerializerMethodField()

    minimal_pay_by_app_payment = serializers.SerializerMethodField()

    def get_service_categories(self, instance: POS) -> list:
        service_categories = []

        if business := self.context.get('business'):
            services = business.services.filter(
                deleted__isnull=True,
                active=True,
            )

            if not NSPFixedPrize((UserData(subject_key=str(business.id)))):
                # for fix amount NSP rules we allow services variants with no price to have NSP
                services = services.filter(Q(service_variants__type__in=PriceType.has_price()))

            services = (
                services.filter(
                    service_variants__active=True,
                )
                .prefetch_related(*self.get_service_prefetches([]))
                .order_by('order')
                .distinct()
            )

            services_per_category: t.Dict[int, t.List[dict]] = defaultdict(list)
            service_category_data: t.Dict[int, dict] = {}
            for service in services:
                service_data = ServicePaymentSerializer(
                    instance=service,
                ).data
                category = service.service_category
                category_id = category.id if category else None
                services_per_category[category_id].append(service_data)
                service_category_data[category_id] = {
                    'id': category_id,
                    'name': category.name if category else _('Not categorised'),
                }

                self.is_first_run_cf = (
                    self.is_first_run_cf
                    and not service.active_variants.has_payment(
                        NoShowProtectionType.CANCELLATION_FEE
                    )
                )
                self.is_first_run_pp = (
                    self.is_first_run_pp
                    and not service.active_variants.has_payment(NoShowProtectionType.PREPAYMENT)
                )

            sorted_category_ids = tuple(
                sorted([key for key in service_category_data.keys() if key is not None])
            )
            if None in service_category_data:
                sorted_category_ids = (None,) + sorted_category_ids

            service_categories = [
                dict(
                    **service_category_data[category_id],
                    services=services_per_category[category_id],
                )
                for category_id in sorted_category_ids
            ]
        return service_categories


class UpdateNoShowFeeWizardServicesSerializer(serializers.ModelSerializer):
    class Meta:
        model = POS
        fields = (
            'no_show_cancel_time_option',
            'no_show_protection_policy',
            'auto_charge_cancellation_fee',
            'services',
        )

    no_show_cancel_time_option = RelativedeltaField(
        source='deposit_cancel_time',
        required=False,
    )
    no_show_protection_policy = serializers.CharField(
        source='deposit_policy',
        max_length=5000,
        required=False,
    )
    auto_charge_cancellation_fee = serializers.BooleanField(required=False)
    services = ServiceNoShowProtectionSerializer(many=True, required=True)

    def save(self, operator_id: int) -> POS:
        instance = super().save()
        self._save_service_data()
        self.initial_data.pop('services', [])
        instance.log_changes(operator_id, self.initial_data)
        business = self.context.get('business')
        step_turn_on_noshow_protection.send(business)
        bump_document(River.BUSINESS, business.id)
        return instance

    def validate(self, attrs: dict) -> dict:
        if not self.instance.is_pay_by_app_active:
            raise serializers.ValidationError(
                _('No show protection fee can\'t be set, because ' 'pay by app payment disabled.')
            )

        return super().validate(attrs)

    @atomic
    def _save_service_data(self) -> None:
        now = tznow()
        business = self.context.get('business')
        user = self.context.get('user')
        service_ids = [service_data['id'] for service_data in self.validated_data['services']]
        services_by_id = Service.objects.in_bulk(service_ids)
        service_variants: [ServiceVariant] = ServiceVariant.objects.filter(
            service_id__in=service_ids,
            active=True,
            valid_till__isnull=True,
        )
        services_to_unset = []
        all_changed_ids = []
        changelogs = []

        # After saving, update no show protection for those variants
        for service_data in self.validated_data['services']:  # type: Service
            no_show_protection: NoShowProtectionServiceVariantField = service_data.get(
                'no_show_protection'
            )
            service: Service = services_by_id.get(service_data['id'])
            if not service:
                continue

            if not no_show_protection:
                services_to_unset.append(service)
                continue

            changed_ids = []

            for service_variant in [
                service_variant
                for service_variant in service_variants
                if service_variant.service_id == service.id
            ]:  # type: ServiceVariant
                no_show_protection_copy = no_show_protection.copy()
                percentage = no_show_protection_copy.get('percentage')
                payment_amount = no_show_protection_copy.get('payment_amount')
                if percentage:
                    no_show_protection_copy['payment_amount'] = (
                        validate_no_show_percent_and_get_amount(
                            no_show_protection_copy,
                            service_variant.price,
                        )
                    )
                if NSPFixedPrize(UserData(subject_key=str(service.business.id))):
                    if payment_amount:
                        no_show_protection_copy['payment_amount'] = validate_no_show_amount(
                            no_show_protection_copy['payment_amount'],
                        )
                UpdateNoShowFeeSerializer.update_or_create_payment(
                    service_variant, no_show_protection_copy, now
                )
                changed_ids.append(service_variant.id)

            all_changed_ids.extend(changed_ids)

            update_to_external_partners(
                EventType.SERVICE,
                business=business,
                service=service,
                ids_to_create=[],
                ids_to_update=changed_ids,
                ids_to_delete=[],
            )

        for service in services_to_unset:
            service_variant_ids = [
                service_variant.id
                for service_variant in service_variants
                if service_variant.service_id == service.id
            ]
            ServiceVariantPayment.objects.filter(
                service_variant_id__in=service_variant_ids,
            ).update(
                deleted=now,
            )
            all_changed_ids.extend(service_variant_ids)

            for service_variant in service_variants:
                if service_variant.service_id == service.id:
                    service_variant.payment = None

            update_to_external_partners(
                EventType.SERVICE,
                business=business,
                service=service,
                ids_to_create=[],
                ids_to_update=service_variant_ids,
                ids_to_delete=[],
            )

        for service_variant in service_variants:
            service_variant.version = (
                service_variant.version + 1 if service_variant.version is not None else 1
            )
            service_variant.updated = now
            changelogs.append(ServiceVariantChangelog.prepare_entry(user, service_variant))

        ServiceVariant.objects.bulk_update(service_variants, ['version', 'updated'])
        ServiceVariantChangelog.objects.bulk_create(changelogs)

        analytics_business_any_no_show_protection_on.cache_clear(business.id)


class UpdateNoShowFeeSerializer(serializers.ModelSerializer):

    THRESHOLD_TO_RUN_BULK = 0.8

    class Meta:
        model = POS
        fields = (
            'no_show_cancel_time_option',
            'no_show_protection_policy',
            'auto_charge_cancellation_fee',
            'services',
        )

    # one of three fields can be present
    no_show_cancel_time_option = RelativedeltaField(
        source='deposit_cancel_time',
        required=False,
    )
    no_show_protection_policy = serializers.CharField(
        source='deposit_policy',
        max_length=5000,
        required=False,
    )
    auto_charge_cancellation_fee = serializers.BooleanField(required=False)
    services = ServicePaymentSerializer(many=True, required=False)

    def __init__(self, *args, **kwargs):
        super(UpdateNoShowFeeSerializer, self).__init__(*args, **kwargs)
        self.transformed_service_variants = {}

    @staticmethod
    def update_or_create_payment(
        service_variant: ServiceVariant,
        no_show_protection: NoShowProtectionServiceVariantField,
        now,
    ):
        defaults = {
            'payment_amount': no_show_protection['payment_amount'],
            'payment_type': no_show_protection['payment_type'],
            'updated': now,
            'deleted': None,
        }

        if NSPFixedPrize((UserData(subject_key=str(service_variant.service.business.id)))):
            defaults['saving_type'] = no_show_protection['saving_type']
        payment, _created = ServiceVariantPayment.all_objects.update_or_create(
            service_variant_id=service_variant.id,
            defaults=defaults,
        )

        service_variant.payment = payment

    def update_service_variants(self, service_variants_data):
        """
        1) Will update all service_variant_payments in service_variants
           If no payment exist for service_variant will create it
        2) Will delete all service_variant with no no_show_protection: None

        :param service_variants_data: list of dicts.
                                Each dict represents service_variant
                                -keys: id, no_show_protection
                                - keys_no_show_protection: amount, type
        :return: None
        """
        now = tznow()
        business = self.context['business']
        variant_by_id = {
            variant.id: variant
            for variant in ServiceVariant.objects.filter(
                service__business_id=business.id,
                active=True,
            )
            .select_related(
                'service',
            )
            .only(
                'price',
                'type',
                'service__id',
            )
        }
        updated_variant_ids_by_service_id = defaultdict(list)
        services = {}

        for variant_data in service_variants_data:
            variant_id = variant_data['id']
            no_show_protection = variant_data.pop('no_show_protection', None)
            variant = variant_by_id.get(variant_id)

            if not variant or no_show_protection is None:
                continue

            self.update_or_create_payment(variant, no_show_protection, now)

            services[variant.service_id] = variant.service
            updated_variant_ids_by_service_id[variant.service_id].append(variant_id)

        ServiceVariant.bump_version(chain(updated_variant_ids_by_service_id.values()))

        for service_id, variant_ids in updated_variant_ids_by_service_id.items():
            update_to_external_partners(
                EventType.SERVICE,
                business=business,
                service=services[service_id],
                ids_to_create=[],
                ids_to_update=variant_ids,
                ids_to_delete=[],
            )

    def update_services(self, services):
        """Update all service variants for each service"""
        now = tznow()
        business = self.context['business']
        no_show_protection_for_service = {
            service_data['id']: service_data['no_show_protection'] for service_data in services
        }
        service_ids = no_show_protection_for_service.keys()
        service_variants_to_update = list(
            ServiceVariant.objects.filter(
                service__business_id=business.id,
                service_id__in=service_ids,
                type__in=PriceType.has_price(),
                active=True,
            )
            .select_related('service')
            .only(
                'type',
                'price',
                'service__id',
            )
        )
        updated = defaultdict(list)

        for service_variant in service_variants_to_update:
            no_show_protection = no_show_protection_for_service.get(service_variant.service_id)
            if no_show_protection is not None:
                no_show_protection = dict(**no_show_protection)
                # here will be updated no_show_protection['payment_amount']
                validate_and_convert_percent_to_amount(no_show_protection, service_variant.price)
                self.update_or_create_payment(
                    service_variant,
                    no_show_protection,
                    now,
                )
                updated[service_variant.service].append(service_variant.id)

        ServiceVariant.bump_version(
            service_variant.id for service_variant in service_variants_to_update
        )

        for service, updated_service_variant_ids in updated.items():
            update_to_external_partners(
                EventType.SERVICE,
                business=business,
                service=service,
                ids_to_create=[],
                ids_to_update=updated_service_variant_ids,
                ids_to_delete=[],
            )

    @retry_on_sync_error
    @atomic
    def _atomic_update(
        self,
        instance,
        validated_data,
    ):
        """
        Update POS fields:
            - deposit_cancel_time
            - deposit_policy
        Update or create service_variant_payments
        :param instance: instance of POS.
        :param validated_data: OrderedDict.
        :return: POS instance
        """
        validated_data = copy.deepcopy(validated_data)  # retry_on_sync
        self.update_services(validated_data.pop('services', []))
        # update pos
        updated = super(UpdateNoShowFeeSerializer, self).update(instance, validated_data)

        return updated

    def save(self, operator_id):
        instance = super(UpdateNoShowFeeSerializer, self).save()
        self.initial_data.pop('service_variants', [])
        self.initial_data.pop('services', [])
        # save history of change
        instance.log_changes(operator_id, self.initial_data)

        # profile completeness
        business = self.context.get('business')
        step_turn_on_noshow_protection.send(business)

        return instance

    def update(self, instance, validated_data):
        result = self._atomic_update(instance, validated_data)
        # force save business to reindex
        # cancellation_policy and deposit_cancel_time
        business = self.context.get('business')
        if business:
            business.bump_document()

            # profile completeness
            step_turn_on_noshow_protection.send(business)

            analytics_protection_service_enabled_task.delay(
                business_id=business.id,
                context={
                    'business_id': business.id,
                },
            )

        return result

    def validate(self, attrs):
        validate_data = super().validate(attrs)
        # Transform list of service_variants to aggregated dict.
        #
        # - key:
        #     '{}_{}'.format(no_show_type_payment, payment_amount)
        #
        # -   value is a two element tuple:
        #         1) first element is service_variant_ids
        #         2) arguments to update or create ServiceVariantPayment
        for sv in validate_data.get('service_variants', []):
            no_show_protection = sv.get('no_show_protection')
            if not no_show_protection:
                continue

            payment_type = no_show_protection['payment_type']
            payment_amount = no_show_protection['payment_amount']
            sv_key = '{}_{}'.format(
                payment_type,
                payment_amount,
            )
            if sv_key in self.transformed_service_variants:
                service_variant_ids, args = self.transformed_service_variants[sv_key]
                service_variant_ids.append(sv['id'])
            else:
                self.transformed_service_variants[sv_key] = (
                    [
                        sv['id'],
                    ],
                    {
                        'payment_type': payment_type,
                        'payment_amount': payment_amount,
                    },
                )

        # if pay_app_disabled
        # but some try to update services_variants
        if not self.instance.is_pay_by_app_active and len(self.transformed_service_variants):
            raise serializers.ValidationError(
                _('No show protection fee can\'t be set, because ' 'pay by app payment disabled.')
            )

        return validate_data


class TransactionPaymentRowSerializer(
    serializers.Serializer,
):
    id = serializers.IntegerField(read_only=True)
    amount = serializers.DecimalField(source='amount_to_display', max_digits=10, decimal_places=2)
    amount_text = pos_fields.PriceField(source='amount_to_display', read_only=True)
    created = pos_fields.PosDateTimeField(read_only=True)
    created_iso = serializers.DateTimeField(read_only=True, source="created")
    payment_type_code = serializers.CharField(
        source='payment_type.code',
        read_only=True,
    )
    customer_info = CustomerInfoSerializer(
        source='receipt.transaction.customer_card',
        read_only=True,
    )
    transaction_id = serializers.IntegerField(
        source='receipt.transaction_id',
        read_only=True,
    )
    appointment_id = serializers.IntegerField(
        source='receipt.transaction.appointment_id',
        read_only=True,
    )
    transaction_type = serializers.CharField(
        source='receipt.transaction.transaction_type',
        read_only=True,
    )

    def _generate_short_status(self, instance):
        short_status = receipt_status.STATUS_TYPES_REVERSE.get(instance.status, '')

        return {
            'short_status': short_status,
            'short_status_label': force_str(
                receipt_status.STATUS_TYPES_DISPLAY.get(
                    short_status,
                )
            ),
        }

    def to_representation(self, instance):
        short_status = self._generate_short_status(instance)
        data = super(TransactionPaymentRowSerializer, self).to_representation(instance)
        data.update(short_status)
        return data


class PaymentRowListingSerializer(TransactionPaymentRowSerializer):

    def to_representation(self, instance):
        if instance.status in [
            receipt_status.PAYMENT_CANCELED,
            receipt_status.PAYMENT_FAILED,
            receipt_status.DEPOSIT_CHARGE_FAILED,
        ]:
            instance.amount = 0

        return super().to_representation(instance)


class TransactionFundTransferSerializer(serializers.Serializer):
    created = pos_fields.PosDateTimeField(read_only=True)
    created_iso = serializers.DateTimeField(read_only=True, source='created')
    amount_text = serializers.SerializerMethodField(
        read_only=True,
    )
    amount = serializers.SerializerMethodField()
    salon_name = serializers.SerializerMethodField(read_only=True)
    type = serializers.SerializerMethodField(read_only=True)

    def get_salon_name(self, instance):
        salon_names = Prize.objects.filter(
            reference__in=instance.prize_refs,
        ).values_list('referral_reward__referral__invited__name', flat=True)
        return ', '.join(salon_names)

    def get_type(self, instance):
        if instance.prize_refs:
            return TransferFundsType.REFERRAL_REWARD.value
        return TransferFundsType.MANUAL_TRANSFER.value

    def get_amount_text(self, instance):
        if instance.source_account_code == self.context['account_holder'].account_code:
            return '-{}'.format(instance.formatted_amount())
        return instance.formatted_amount()

    def get_amount(self, instance):
        value = instance.amount / CURRENCY_FACTOR
        if instance.source_account_code == self.context['account_holder'].account_code:
            value = -value
        return serializers.DecimalField(
            max_digits=10,
            decimal_places=2,
        ).to_representation(value)


class BusinessCashFlowSerializer(serializers.Serializer):
    payment_row = TransactionPaymentRowSerializer(
        allow_null=True,
        read_only=True,
    )
    fund_transfer = TransactionFundTransferSerializer(
        allow_null=True,
        read_only=True,
    )

    def __init__(self, instance=None, data=empty, **kwargs):
        super().__init__(instance=instance, data=data, **kwargs)
        self.context['account_holder'] = self.context['business'].pos.account_holder
        self.context['valid_currency'] = True


class BusinessPosPlanInfoRequestSerializer(serializers.Serializer):
    pos_plan_type = serializers.ChoiceField(required=True, choices=POSPlanPaymentTypeEnum.choices())


class PaymentRowsSummarySerializer(PaginatorSerializer):
    scope = serializers.ChoiceField(
        choices=PaymentRowsSummaryScopes.choices(),
        required=True,
    )
    date_from = serializers.DateField(required=False, allow_null=True)
    date_till = serializers.DateField(required=False, allow_null=True)
    query = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    per_page = serializers.IntegerField(
        min_value=0,
        default=5,
        max_value=1000,
    )

    @staticmethod
    def validate_query(query):
        return None if query == '' else query

    def validate(self, attrs):
        tz = self.context['tz']
        for field in ['from', 'till']:
            if ('date_' + field) in attrs:
                diff = timedelta(days=int(field == 'till'))
                attrs['datetime_' + field] = datetime.combine(
                    attrs['date_' + field] + diff,
                    time(),
                ).replace(tzinfo=tz)
        if attrs['scope'] == PaymentRowsSummaryScopes.CUSTOM and (
            'date_from' not in attrs or 'date_till' not in attrs
        ):
            raise serializers.ValidationError(
                "'date_from' and 'date_till' required when 'scope' " "parameter is 'custom'"
            )
        return super(self.__class__, self).validate(attrs)


class BusinessFastPayoutLimitSerializer(serializers.ModelSerializer):
    fast_payout_merchant_max_limit = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
        required=True,
    )
    fast_payout_max_limit = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
        required=False,
    )
    fast_payout_min_limit = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
        required=False,
    )

    class Meta:
        model = StripeAccount
        fields = (
            'fast_payout_merchant_max_limit',
            'fast_payout_max_limit',
            'fast_payout_min_limit',
        )
        read_only_fields = (
            'fast_payout_max_limit',
            'fast_payout_min_limit',
        )

    def validate_fast_payout_merchant_max_limit(self, attrs):
        merch_max_lim = attrs
        if (
            merch_max_lim > self.instance.fast_payout_max_limit
            or merch_max_lim < self.instance.fast_payout_min_limit
        ):
            raise ValidationError(_('Limit is out of range'))
        return attrs


class SplashSerializer(serializers.Serializer):
    splash = serializers.ChoiceField(
        choices=[
            None,
            SplashType.SETTINGS_PROFILE_USE_TAP_TO_PAY,
            SplashType.SPLASH_USE_TAP_TO_PAY,
            SplashType.SPLASH_PX_BOOKSY_PAY_ONBOARDING,
            SplashType.SPLASH_BOOST_PRE_SUSPENSION_WARNING,
        ],
    )
    additional_data = serializers.DictField(required=False)


class AppointmentBooksyPaySerializer(serializers.Serializer):
    is_available = serializers.BooleanField(source='is_booksy_pay_available', default=False)
    is_payment_window_open = serializers.BooleanField(
        source='is_booksy_pay_payment_window_open',
        default=False,
    )
    is_paid = serializers.BooleanField(source='is_paid_by_booksy_pay', default=False)


class TipRateSerializer(serializers.Serializer):
    percent = serializers.CharField()
    value = serializers.DecimalField(max_digits=5, decimal_places=2)


class StafferSerializer(serializers.Serializer):
    photo_url = serializers.CharField()
    name = serializers.CharField()


class TippingAppetiteExperimentSplashSerializer(serializers.Serializer):
    staffer = StafferSerializer()
    service_name = serializers.CharField()
    tip_rate = TipRateSerializer(many=True)
    appointment_value = serializers.DecimalField(max_digits=10, decimal_places=2)


class TippingAfterAppointmentExperimentAnswerSerializer(serializers.Serializer):
    answer = serializers.ChoiceField(choices=TippingAfterAppointmentAnswer.Answer.choices())
