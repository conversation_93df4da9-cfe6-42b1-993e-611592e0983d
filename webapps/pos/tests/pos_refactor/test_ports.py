from itertools import cycle
from unittest.mock import patch, MagicMock
import uuid
import pytest
from django.test import TestCase
from model_bakery import baker

from webapps.business.models import Resource
from webapps.pos.models import PaymentRow, Receipt, Transaction, TransactionRow
from webapps.pos.ports import PaymentRowPort, TransactionPort
from webapps.pos.tests.pos_refactor.helpers import get_basket_payment_entity


@pytest.mark.django_db
class TestTransactionPort(TestCase):
    @patch('webapps.pos.services.TransactionService.is_off_session_payment')
    def test_is_off_session_transaction(self, is_off_session_payment_mock):
        is_off_session_payment_mock.return_value = True

        transaction = baker.make(Transaction)
        receipt = baker.make(
            Receipt,
            transaction=transaction,
        )

        self.assertTrue(
            TransactionPort.is_off_session_transaction(
                receipt_id=receipt.id,
            )
        )
        is_off_session_payment_mock.assert_called_once_with(
            txn=transaction,
        )

    def test_get_transaction(self):
        basket_payment_id = uuid.uuid4()
        transaction = baker.make(Transaction)
        receipt = baker.make(Receipt, transaction=transaction)
        baker.make(PaymentRow, basket_payment_id=basket_payment_id, receipt=receipt)

        transaction_entity = TransactionPort.get_transaction(basket_payment_id=basket_payment_id)

        self.assertEqual(transaction_entity.id, transaction_entity.id)

    def test_get_transaction_commission_staffers_ids(self):
        transaction = baker.make(Transaction)
        staffers = baker.make(Resource, type=Resource.STAFF, _quantity=2)
        baker.make(
            TransactionRow, transaction=transaction, commission_staffer=cycle(staffers), _quantity=2
        )

        commission_staffers_ids = TransactionPort.get_transaction_commission_staffers_id(
            transaction.id
        )

        self.assertEqual(commission_staffers_ids, [staffer.id for staffer in staffers])


@pytest.mark.django_db
class TestPaymentRowPort(TestCase):
    def test_get_last_payment_row_entity(self):
        basket_payment_entity = get_basket_payment_entity()
        baker.make(
            PaymentRow,
            basket_payment_id=basket_payment_entity.id,
        )
        payment_row2 = baker.make(
            PaymentRow,
            basket_payment_id=basket_payment_entity.id,
        )

        payment_row_entity = PaymentRowPort.get_last_payment_row_entity(
            basket_payment_id=basket_payment_entity.id
        )
        self.assertEqual(
            payment_row_entity.id,
            payment_row2.id,
        )

    @patch('webapps.pos.ports.mark_sent_for_refund')
    def test_mark_sent_for_refund(self, mock_payment_row_mark_sent_for_refund: MagicMock) -> None:
        payment_row = baker.make(PaymentRow)
        mock_payment_row_mark_sent_for_refund.return_value = MagicMock(entity=payment_row.entity)
        basket_payment_id = uuid.uuid4()
        operator_id = 789

        result = PaymentRowPort.mark_sent_for_refund(basket_payment_id, operator_id)

        self.assertEqual(result, payment_row.entity)
        mock_payment_row_mark_sent_for_refund.assert_called_once_with(
            basket_payment_id,
            operator_id,
            amount=None,
        )

    @patch('webapps.pos.ports.PaymentRowService.update_basket_payment_id')
    def test_update_basket_payment_id(self, mock_update_basket_payment_id: MagicMock) -> None:
        payment_row_id = 123
        basket_payment_id = uuid.uuid4()

        PaymentRowPort.update_basket_payment_id(payment_row_id, basket_payment_id)

        mock_update_basket_payment_id.assert_called_once_with(
            payment_row_id=payment_row_id,
            basket_payment_id=basket_payment_id,
        )
