from unittest.mock import patch
import stripe
from django.utils.crypto import get_random_string

from lib.point_of_sale.enums import PaymentMethodType
from lib.tools import minor_unit
from webapps.payment_gateway.models import BalanceTransaction
from webapps.payment_providers.consts.stripe import PaymentIntentStripeStatus, RefundStripeStatus
from webapps.payment_providers.consts.stripe_error_code import DeclineCode
from webapps.point_of_sale.models import BasketPayment
from webapps.pos.enums import PaymentTypeEnum, receipt_status
from webapps.pos.models import Transaction
from webapps.pos.serializers import TransactionSerializer
from webapps.pos.tests.pos_refactor import StripeTestEventBody
from webapps.pos.tests.pos_refactor.keyed_in_payment.base import (
    TestTransactionSerializerKeyedInPaymentBase,
)
from webapps.stripe_integration.enums import StripePaymentIntentActions
from webapps.stripe_integration.models import StripePaymentIntent
from webapps.stripe_integration.tests.mocks import (
    mock_stripe_payment_intent_create,
    mock_stripe_payment_intent_retrieve,
    mock_stripe_payment_intent_retrieve_with_tip,
    mock_stripe_payment_intent_modify,
    mock_stripe_payment_intent_cancel,
    mock_stripe_refund_create,
    mock_stripe_transfer_create,
)
from webapps.payment_providers.models import (
    PaymentOperation,
    StripePaymentIntent as StripePaymentIntentV2,
)


class TestTransactionSerializerKeyedInPaymentEntry(TestTransactionSerializerKeyedInPaymentBase):
    def edit_transaction(
        self,
        old_txn: Transaction,
        edit: bool,
        payment_typ_code: PaymentTypeEnum,
        tip: dict = None,
        amount=543.21,
    ):
        data = {
            'dry_run': False,
            'transaction_type': Transaction.TRANSACTION_TYPE__PAYMENT,
            'bookings': [
                {
                    'booking_id': booking.id,
                    'item_price': amount,
                }
                for booking in self.create_appointment().subbookings
            ],
            'payment_rows': [{'payment_type_code': payment_typ_code, 'amount': amount}],
            'customer_card_id': self.bci.id,
            'parent_txn': old_txn.id,
        }

        if tip:
            data['tip'] = tip

        serializer = TransactionSerializer(
            data=data,
            context={
                'pos': self.pos,
                'operator': self.user,
                'compatibilities': self.compatibilities,
                'old_txn': old_txn,
                'edit': edit,
                'device_data': self.device_data_entity,
            },
        )

        assert serializer.is_valid(), serializer.errors

        if edit is True:
            old_txn.update_payment_rows(
                status=receipt_status.ARCHIVED,
            )

        return serializer.save()

    @mock_stripe_payment_intent_create
    @mock_stripe_payment_intent_retrieve
    def test_stripe_pending(self, *args):
        txn = self.create_transaction(PaymentTypeEnum.KEYED_IN_PAYMENT)
        self._check_stripe_pending_with_intent(txn, PaymentMethodType.KEYED_IN_PAYMENT)
        self.assertEqual(StripePaymentIntent.objects.count(), 1)
        self.assertEqual(StripePaymentIntentV2.objects.count(), 1)

    @mock_stripe_payment_intent_create
    @mock_stripe_payment_intent_retrieve
    @patch('webapps.stripe_integration.provider.StripeProvider.get_or_create_stripe_customer')
    def test_stripe_pending_with_intent(self, customer_mock, *args):
        customer_mock.return_value = None

        txn = self.create_transaction(PaymentTypeEnum.KEYED_IN_PAYMENT)
        self._check_stripe_pending_with_intent(txn, PaymentMethodType.KEYED_IN_PAYMENT)
        self.assertEqual(StripePaymentIntent.objects.count(), 1)
        self.assertEqual(StripePaymentIntentV2.objects.count(), 1)

        _client_token, intent_object = self.get_provider().create_payment_intent(
            transaction=txn,
            payment_row=txn.payment_rows[0],
        )
        self._check_stripe_pending_with_intent(txn, PaymentMethodType.KEYED_IN_PAYMENT)
        self.assertEqual(StripePaymentIntent.objects.count(), 1)
        self.assertEqual(StripePaymentIntentV2.objects.count(), 1)
        self.assertEqual(intent_object, StripePaymentIntent.objects.first())

    @mock_stripe_payment_intent_create
    @mock_stripe_payment_intent_retrieve
    @patch('webapps.stripe_integration.provider.StripeProvider.get_or_create_stripe_customer')
    def test_stripe_success(self, customer_mock, *args):
        customer_mock.return_value = None

        txn = self.create_transaction(PaymentTypeEnum.KEYED_IN_PAYMENT)
        self.make_success(txn)

        self._check_stripe_success(txn, PaymentMethodType.KEYED_IN_PAYMENT)

        self.assertEqual(StripePaymentIntent.objects.count(), 1)
        self.assertEqual(StripePaymentIntentV2.objects.count(), 1)
        assert (
            StripePaymentIntent.objects.first().external_id
            == StripePaymentIntentV2.objects.first().external_id
        )

    @mock_stripe_payment_intent_create
    @mock_stripe_payment_intent_retrieve_with_tip(base_amount=12345, tip_amount=2200)
    @mock_stripe_payment_intent_modify
    @patch('webapps.stripe_integration.provider.StripeProvider.get_or_create_stripe_customer')
    def test_stripe_success_with_tip(self, customer_mock, *args):
        customer_mock.return_value = None
        self.transaction_data['tip'] = {
            "already_paid": "$0.00",
            "already_paid_unformatted": "0.00",
            "amount_remaining": 22.00,
            "rate": "22.00",
            "type": "P",
            "label": "22.00%",
            "amount": "$22.00",
            "amount_unformatted": "22.00",
        }
        self.transaction_data['payment_rows'][0]['amount'] = 122.00

        txn = self.create_transaction(PaymentTypeEnum.KEYED_IN_PAYMENT)
        self.make_success(txn)
        self._check_stripe_success_with_tip(txn, PaymentMethodType.KEYED_IN_PAYMENT)

    @mock_stripe_payment_intent_create
    @mock_stripe_payment_intent_retrieve
    @patch('webapps.stripe_integration.provider.StripeProvider.get_or_create_stripe_customer')
    def test_stripe_success_with_fees(self, customer_mock, *args):
        self.stripe_plan.provision = 0.10
        self.stripe_plan.txn_fee = 10
        self.stripe_plan.save()
        self.tap_to_pay_plan.save()

        customer_mock.return_value = None

        txn = self.create_transaction(PaymentTypeEnum.KEYED_IN_PAYMENT)
        self.make_success(txn)

        self._check_stripe_success_with_fees(txn, PaymentMethodType.KEYED_IN_PAYMENT)

    @mock_stripe_payment_intent_create
    @mock_stripe_payment_intent_retrieve
    @mock_stripe_payment_intent_cancel
    @patch('webapps.stripe_integration.provider.StripeProvider.get_or_create_stripe_customer')
    def test_stripe_cancel(self, customer_mock, *args):
        customer_mock.return_value = None

        txn = self.create_transaction(PaymentTypeEnum.KEYED_IN_PAYMENT)

        _client_token, intent_object = self.get_provider().create_payment_intent(
            transaction=txn, payment_row=txn.payment_rows[0]
        )

        self.get_provider().process_actions(
            action=StripePaymentIntentActions.CANCEL,
            intent_object=intent_object,
            operator=self.user,
        )
        self._check_stripe_cancel(txn, PaymentMethodType.KEYED_IN_PAYMENT)

    @mock_stripe_payment_intent_create
    @mock_stripe_payment_intent_retrieve
    @mock_stripe_payment_intent_cancel
    @patch('webapps.stripe_integration.provider.StripeProvider.get_or_create_stripe_customer')
    def test_stripe_cancel_retry_cash(self, customer_mock, *args):
        customer_mock.return_value = None

        txn = self.create_transaction(PaymentTypeEnum.KEYED_IN_PAYMENT)

        _client_token, intent_object = self.get_provider().create_payment_intent(
            transaction=txn, payment_row=txn.payment_rows[0]
        )

        self.get_provider().process_actions(
            action=StripePaymentIntentActions.CANCEL,
            intent_object=intent_object,
            operator=self.user,
        )
        txn.refresh_from_db()

        txn2 = self.edit_transaction(
            old_txn=txn,
            edit=False,
            payment_typ_code=PaymentTypeEnum.CASH,
        )

        self._check_stripe_cancel_retry_cash(txn, txn2, PaymentMethodType.KEYED_IN_PAYMENT)

    @mock_stripe_payment_intent_create
    @mock_stripe_payment_intent_retrieve
    @mock_stripe_payment_intent_cancel
    @patch('webapps.stripe_integration.provider.StripeProvider.get_or_create_stripe_customer')
    def test_stripe_cancel_retry_stripe(self, customer_mock, *args):
        customer_mock.return_value = None

        txn = self.create_transaction(PaymentTypeEnum.KEYED_IN_PAYMENT)

        _client_token, intent_object = self.get_provider().create_payment_intent(
            transaction=txn, payment_row=txn.payment_rows[0]
        )

        self.get_provider().process_actions(
            action=StripePaymentIntentActions.CANCEL,
            intent_object=intent_object,
            operator=self.user,
        )
        txn.refresh_from_db()

        txn2 = self.edit_transaction(
            old_txn=txn, edit=False, payment_typ_code=PaymentTypeEnum.KEYED_IN_PAYMENT, amount=392
        )

        self._check_stripe_cancel_retry_stripe(txn, txn2, PaymentMethodType.KEYED_IN_PAYMENT)

    @mock_stripe_payment_intent_create
    @mock_stripe_payment_intent_retrieve
    @mock_stripe_payment_intent_cancel
    @patch('webapps.stripe_integration.provider.StripeProvider.get_or_create_stripe_customer')
    def test_stripe_cancel_retry_stripe_with_intent(self, customer_mock, *args):
        customer_mock.return_value = None

        txn = self.create_transaction(PaymentTypeEnum.KEYED_IN_PAYMENT)

        _client_token, intent_object = self.get_provider().create_payment_intent(
            transaction=txn, payment_row=txn.payment_rows[0]
        )

        self.get_provider().process_actions(
            action=StripePaymentIntentActions.CANCEL,
            intent_object=intent_object,
            operator=self.user,
        )
        txn.refresh_from_db()

        txn2 = self.edit_transaction(
            old_txn=txn, edit=False, payment_typ_code=PaymentTypeEnum.KEYED_IN_PAYMENT, amount=261
        )

        self.get_provider().create_payment_intent(
            transaction=txn2, payment_row=txn2.payment_rows[0]
        )

        self._check_stripe_cancel_retry_stripe_with_intent(
            txn, txn2, PaymentMethodType.KEYED_IN_PAYMENT
        )

    @mock_stripe_payment_intent_create
    @mock_stripe_payment_intent_retrieve
    @mock_stripe_payment_intent_cancel
    @patch('webapps.stripe_integration.provider.StripeProvider.get_or_create_stripe_customer')
    def test_stripe_cancel_retry_stripe_success(self, customer_mock, *args):
        customer_mock.return_value = None

        txn = self.create_transaction(PaymentTypeEnum.KEYED_IN_PAYMENT)

        _client_token, intent_object = self.get_provider().create_payment_intent(
            transaction=txn, payment_row=txn.payment_rows[0]
        )

        self.get_provider().process_actions(
            action=StripePaymentIntentActions.CANCEL,
            intent_object=intent_object,
            operator=self.user,
        )
        txn.refresh_from_db()

        txn2 = self.edit_transaction(
            old_txn=txn, edit=False, payment_typ_code=PaymentTypeEnum.KEYED_IN_PAYMENT, amount=268
        )

        self.make_success(txn2)

        self._check_stripe_cancel_retry_stripe_success(
            txn, txn2, PaymentMethodType.KEYED_IN_PAYMENT
        )

    @mock_stripe_payment_intent_create
    @mock_stripe_payment_intent_retrieve
    @mock_stripe_payment_intent_cancel
    @patch('webapps.stripe_integration.provider.StripeProvider.get_or_create_stripe_customer')
    def test_stripe_fail(self, customer_mock, *args):
        customer_mock.return_value = None

        txn = self.create_transaction(PaymentTypeEnum.KEYED_IN_PAYMENT)

        _client_token, intent_object = self.get_provider().create_payment_intent(
            transaction=txn, payment_row=txn.payment_rows[0]
        )

        self._simulate_stripe_incoming_webhook(
            data=StripeTestEventBody.payment_intent_payment_failed(
                payment_intent_external_id=intent_object.external_id,
                decline_code=DeclineCode.LOST_CARD,
            ),
            expect_new_webhook_handling=True,
        )

        self._check_stripe_fail(txn, payment_method=PaymentMethodType.KEYED_IN_PAYMENT)

    @mock_stripe_payment_intent_create
    @mock_stripe_payment_intent_retrieve
    @mock_stripe_payment_intent_cancel
    @patch('webapps.stripe_integration.provider.StripeProvider.get_or_create_stripe_customer')
    def test_stripe_process_fail(self, customer_mock, *args):
        # manual fail (sent by a client app based on the terminal state)
        customer_mock.return_value = None

        txn = self.create_transaction(PaymentTypeEnum.KEYED_IN_PAYMENT)

        _, intent_object = self.get_provider().create_payment_intent(
            transaction=txn, payment_row=txn.payment_rows[0]
        )

        self.get_provider().process_actions(
            action=StripePaymentIntentActions.FAILED,
            intent_object=intent_object,
            operator=self.user,
            action_kwargs={'error_code': DeclineCode.LOST_CARD},
        )

        payment_intent_status = PaymentIntentStripeStatus.CANCELED

        self._check_stripe_fail(
            txn,
            payment_intent_status=payment_intent_status,
            payment_method=PaymentMethodType.KEYED_IN_PAYMENT,
        )

    @mock_stripe_payment_intent_create
    @mock_stripe_payment_intent_retrieve
    @mock_stripe_payment_intent_cancel
    @patch('webapps.stripe_integration.provider.StripeProvider.get_or_create_stripe_customer')
    def test_stripe_fail_cancel(self, customer_mock, *args):
        customer_mock.return_value = None

        txn = self.create_transaction(PaymentTypeEnum.KEYED_IN_PAYMENT)

        _, intent_object = self.get_provider().create_payment_intent(
            transaction=txn, payment_row=txn.payment_rows[0]
        )

        self._simulate_stripe_incoming_webhook(
            data=StripeTestEventBody.payment_intent_payment_failed(
                payment_intent_external_id=intent_object.external_id,
                decline_code=DeclineCode.LOST_CARD,
            ),
            expect_new_webhook_handling=True,
        )

        self.get_provider().process_actions(
            action=StripePaymentIntentActions.CANCEL,
            intent_object=intent_object,
            operator=self.user,
        )

        self._check_stripe_fail_cancel(txn, PaymentMethodType.KEYED_IN_PAYMENT)

    @mock_stripe_payment_intent_create
    @mock_stripe_payment_intent_retrieve
    @mock_stripe_refund_create
    @mock_stripe_transfer_create
    @patch('webapps.stripe_integration.provider.StripeProvider.get_or_create_stripe_customer')
    @patch('webapps.payment_providers.providers.stripe.StripeProvider.create_refund')
    def test_stripe_send_for_refund(self, refund_mock, customer_mock, *args):
        refund_mock.return_value = stripe.Refund(id=get_random_string(8))
        customer_mock.return_value = None

        txn = self.create_transaction(PaymentTypeEnum.KEYED_IN_PAYMENT)
        self.make_success(txn)

        txn.refresh_from_db()
        success_row = txn.payment_rows[0]

        # Send for refund
        success_row.send_for_refund(
            operator=self.user,
        )

        self._check_stripe_send_for_refund(txn, success_row, PaymentMethodType.KEYED_IN_PAYMENT)
        self.assertEqual(refund_mock.call_count, 1)

    @mock_stripe_payment_intent_create
    @mock_stripe_payment_intent_retrieve
    @mock_stripe_refund_create
    @mock_stripe_transfer_create
    @patch('webapps.stripe_integration.provider.StripeProvider.get_or_create_stripe_customer')
    def test_stripe_refund(self, customer_mock, *args):
        customer_mock.return_value = None

        txn = self.create_transaction(PaymentTypeEnum.KEYED_IN_PAYMENT)
        payment_intent = self.make_success(txn)

        txn.refresh_from_db()
        success_row = txn.payment_rows[0]

        # Send for refund
        send_for_refund_row = success_row.send_for_refund(
            operator=self.user,
        )
        send_for_refund_row.refresh_from_db()

        balance_transaction = BalanceTransaction.objects.get(
            id=BasketPayment.objects.get(
                id=send_for_refund_row.basket_payment_id
            ).balance_transaction_id
        )
        payment_operation = PaymentOperation.objects.get(id=balance_transaction.external_id)
        self._simulate_stripe_incoming_webhook(
            data=StripeTestEventBody.charge_refunded(
                refund_external_id=payment_operation.stripe_refund.external_id,
                payment_intent_external_id=payment_intent.external_id,
                status=RefundStripeStatus.SUCCEEDED,
            ),
            expect_new_webhook_handling=True,
        )
        txn.refresh_from_db()
        self._check_stripe_refund(txn, success_row, PaymentMethodType.KEYED_IN_PAYMENT)

    @mock_stripe_payment_intent_create
    @mock_stripe_payment_intent_retrieve
    @mock_stripe_refund_create
    @mock_stripe_transfer_create
    @patch('webapps.stripe_integration.provider.StripeProvider.get_or_create_stripe_customer')
    def test_stripe_refund_with_fees(self, customer_mock, *args):
        self.stripe_plan.provision = self.tap_to_pay_plan.provision = 0.10
        self.stripe_plan.txn_fee = self.tap_to_pay_plan.txn_fee = 10
        self.stripe_plan.refund_provision = self.tap_to_pay_plan.refund_provision = 0.1
        self.stripe_plan.refund_txn_fee = self.tap_to_pay_plan.refund_txn_fee = 10
        self.stripe_plan.save()
        self.tap_to_pay_plan.save()

        customer_mock.return_value = None

        txn = self.create_transaction(PaymentTypeEnum.KEYED_IN_PAYMENT)
        payment_intent = self.make_success(txn)

        txn.refresh_from_db()
        success_row = txn.payment_rows[0]

        # Send for refund
        send_for_refund_row = success_row.send_for_refund(
            operator=self.user,
        )
        send_for_refund_row.refresh_from_db()

        balance_transaction = BalanceTransaction.objects.get(
            id=BasketPayment.objects.get(
                id=send_for_refund_row.basket_payment_id
            ).balance_transaction_id
        )
        payment_operation = PaymentOperation.objects.get(id=balance_transaction.external_id)
        self._simulate_stripe_incoming_webhook(
            data=StripeTestEventBody.charge_refunded(
                refund_external_id=payment_operation.stripe_refund.external_id,
                payment_intent_external_id=payment_intent.external_id,
                status=RefundStripeStatus.SUCCEEDED,
            ),
            expect_new_webhook_handling=True,
        )
        txn.refresh_from_db()
        self._check_stripe_refund_with_fees(txn, success_row, PaymentMethodType.KEYED_IN_PAYMENT)

    @mock_stripe_payment_intent_create
    @mock_stripe_payment_intent_retrieve
    @mock_stripe_refund_create
    @mock_stripe_transfer_create
    @patch('webapps.stripe_integration.provider.StripeProvider.get_or_create_stripe_customer')
    def test_stripe_refund_fail(self, customer_mock, *args):
        customer_mock.return_value = None

        txn = self.create_transaction(PaymentTypeEnum.KEYED_IN_PAYMENT)
        payment_intent = self.make_success(txn)

        txn.refresh_from_db()
        success_row = txn.payment_rows[0]

        # Send for refund
        send_for_refund_row = success_row.send_for_refund(
            operator=self.user,
        )
        send_for_refund_row.refresh_from_db()

        balance_transaction = BalanceTransaction.objects.get(
            id=BasketPayment.objects.get(
                id=send_for_refund_row.basket_payment_id
            ).balance_transaction_id
        )
        payment_operation = PaymentOperation.objects.get(id=balance_transaction.external_id)
        self._simulate_stripe_incoming_webhook(
            data=StripeTestEventBody.charge_refunded(
                refund_external_id=payment_operation.stripe_refund.external_id,
                payment_intent_external_id=payment_intent.external_id,
                status=RefundStripeStatus.FAILED,
            ),
            expect_new_webhook_handling=True,
        )
        txn.refresh_from_db()
        self._check_stripe_refund_fail(txn, success_row, PaymentMethodType.KEYED_IN_PAYMENT)

        @mock_stripe_payment_intent_create
        @mock_stripe_payment_intent_retrieve
        @mock_stripe_transfer_create
        @patch('webapps.stripe_integration.provider.StripeProvider.get_or_create_stripe_customer')
        def test_stripe_chargeback(self, customer_mock):
            customer_mock.return_value = None

            txn = self.create_transaction(PaymentTypeEnum.KEYED_IN_PAYMENT)
            payment_intent = self.make_success(txn)

            txn.refresh_from_db()
            success_row = txn.payment_rows[0]

            self._simulate_stripe_incoming_webhook(
                data=StripeTestEventBody.charge_dispute_created(
                    payment_intent_external_id=payment_intent.external_id,
                    amount=minor_unit(success_row.amount),
                ),
                expect_new_webhook_handling=True,
            )

            self._check_stripe_chargeback(txn, success_row, PaymentMethodType.KEYED_IN_PAYMENT)

    @mock_stripe_payment_intent_create
    @mock_stripe_payment_intent_retrieve
    @mock_stripe_transfer_create
    @patch('webapps.stripe_integration.provider.StripeProvider.get_or_create_stripe_customer')
    def test_stripe_chargeback_with_fee(self, customer_mock):
        self.stripe_plan.provision = self.tap_to_pay_plan.provision = 0.10
        self.stripe_plan.txn_fee = self.tap_to_pay_plan.txn_fee = 10
        self.stripe_plan.chargeback_provision = self.tap_to_pay_plan.chargeback_provision = 0.1
        self.stripe_plan.chargeback_txn_fee = self.tap_to_pay_plan.chargeback_txn_fee = 10
        self.stripe_plan.save()
        self.tap_to_pay_plan.save()

        customer_mock.return_value = None

        txn = self.create_transaction(PaymentTypeEnum.KEYED_IN_PAYMENT)
        payment_intent = self.make_success(txn)

        txn.refresh_from_db()
        success_row = txn.payment_rows[0]

        self._simulate_stripe_incoming_webhook(
            data=StripeTestEventBody.charge_dispute_created(
                payment_intent_external_id=payment_intent.external_id,
                amount=minor_unit(success_row.amount),
            ),
            expect_new_webhook_handling=True,
        )

        self._check_stripe_chargeback_with_fee(txn, success_row, PaymentMethodType.KEYED_IN_PAYMENT)

    @mock_stripe_payment_intent_create
    @mock_stripe_payment_intent_retrieve
    @patch('webapps.stripe_integration.provider.StripeProvider.get_or_create_stripe_customer')
    def test_split_cash_stripe_pending(self, *args):
        txn = self.create_split_transaction(
            [
                {
                    'payment_type_code': PaymentTypeEnum.CASH,
                    'amount': 100,
                },
                {
                    'payment_type_code': PaymentTypeEnum.KEYED_IN_PAYMENT,
                    'amount': 23.45,
                },
            ]
        )

        self.get_provider().create_payment_intent(transaction=txn, payment_row=txn.payment_rows[1])

        self._check_split_cash_stripe_pending(txn, PaymentMethodType.KEYED_IN_PAYMENT)

    @mock_stripe_payment_intent_create
    @mock_stripe_payment_intent_retrieve
    @patch('webapps.stripe_integration.provider.StripeProvider.get_or_create_stripe_customer')
    def test_split_cash_stripe_success(self, customer_mock, *args):
        customer_mock.return_value = None

        txn = self.create_split_transaction(
            [
                {
                    'payment_type_code': PaymentTypeEnum.CASH,
                    'amount': 100,
                },
                {
                    'payment_type_code': PaymentTypeEnum.KEYED_IN_PAYMENT,
                    'amount': 23.45,
                },
            ]
        )

        self.make_success(txn, 1)

        self._check_split_cash_stripe_success(txn, PaymentMethodType.KEYED_IN_PAYMENT)

    @mock_stripe_payment_intent_create
    @mock_stripe_payment_intent_retrieve
    @mock_stripe_refund_create
    @mock_stripe_transfer_create
    @patch('webapps.stripe_integration.provider.StripeProvider.get_or_create_stripe_customer')
    def test_split_cash_stripe_send_for_refund(self, customer_mock, *args):
        customer_mock.return_value = None

        txn = self.create_split_transaction(
            [
                {
                    'payment_type_code': PaymentTypeEnum.CASH,
                    'amount': 100,
                },
                {
                    'payment_type_code': PaymentTypeEnum.KEYED_IN_PAYMENT,
                    'amount': 23.45,
                },
            ]
        )

        self.make_success(txn, 1)
        txn.refresh_from_db()

        success_row = txn.payment_rows[1]

        # Send for refund
        success_row.send_for_refund(
            operator=self.user,
        )

        self._check_split_cash_stripe_send_for_refund(
            txn, success_row, PaymentMethodType.KEYED_IN_PAYMENT
        )

    @mock_stripe_payment_intent_create
    @mock_stripe_payment_intent_retrieve
    @mock_stripe_refund_create
    @mock_stripe_transfer_create
    @patch('webapps.stripe_integration.provider.StripeProvider.get_or_create_stripe_customer')
    def test_split_cash_stripe_refund(self, customer_mock, *args):
        txn = self.create_split_transaction(
            [
                {
                    'payment_type_code': PaymentTypeEnum.CASH,
                    'amount': 100,
                },
                {
                    'payment_type_code': PaymentTypeEnum.KEYED_IN_PAYMENT,
                    'amount': 23.45,
                },
            ]
        )

        payment_intent = self.make_success(txn, 1)
        txn.refresh_from_db()

        success_row = txn.payment_rows[1]

        # Send for refund
        send_for_refund_row = success_row.send_for_refund(
            operator=self.user,
        )
        send_for_refund_row.refresh_from_db()

        balance_transaction = BalanceTransaction.objects.get(
            id=BasketPayment.objects.get(
                id=send_for_refund_row.basket_payment_id
            ).balance_transaction_id
        )
        payment_operation = PaymentOperation.objects.get(id=balance_transaction.external_id)
        self._simulate_stripe_incoming_webhook(
            data=StripeTestEventBody.charge_refunded(
                refund_external_id=payment_operation.stripe_refund.external_id,
                payment_intent_external_id=payment_intent.external_id,
                status=RefundStripeStatus.SUCCEEDED,
            ),
            expect_new_webhook_handling=True,
        )
        txn.refresh_from_db()
        self._check_split_cash_stripe_refund(txn, success_row, PaymentMethodType.KEYED_IN_PAYMENT)

    @mock_stripe_payment_intent_create
    @mock_stripe_payment_intent_retrieve
    @mock_stripe_refund_create
    @mock_stripe_transfer_create
    @patch('webapps.stripe_integration.provider.StripeProvider.get_or_create_stripe_customer')
    def test_split_cash_stripe_refund_fail(self, customer_mock, *args):
        txn = self.create_split_transaction(
            [
                {
                    'payment_type_code': PaymentTypeEnum.CASH,
                    'amount': 100,
                },
                {
                    'payment_type_code': PaymentTypeEnum.KEYED_IN_PAYMENT,
                    'amount': 23.45,
                },
            ]
        )

        payment_intent = self.make_success(txn, 1)
        txn.refresh_from_db()

        success_row = txn.payment_rows[1]

        # Send for refund
        send_for_refund_row = success_row.send_for_refund(
            operator=self.user,
        )
        send_for_refund_row.refresh_from_db()

        balance_transaction = BalanceTransaction.objects.get(
            id=BasketPayment.objects.get(
                id=send_for_refund_row.basket_payment_id
            ).balance_transaction_id
        )
        payment_operation = PaymentOperation.objects.get(id=balance_transaction.external_id)
        self._simulate_stripe_incoming_webhook(
            data=StripeTestEventBody.charge_refunded(
                refund_external_id=payment_operation.stripe_refund.external_id,
                payment_intent_external_id=payment_intent.external_id,
                status=RefundStripeStatus.FAILED,
            ),
            expect_new_webhook_handling=True,
        )
        txn.refresh_from_db()
        self._check_split_cash_stripe_refund_fail(
            txn, success_row, PaymentMethodType.KEYED_IN_PAYMENT
        )

    @mock_stripe_payment_intent_create
    @mock_stripe_payment_intent_retrieve
    @mock_stripe_transfer_create
    @patch(
        'lib.payment_providers.events.payment_providers_transfer_fund_updated_event.send',
    )
    @patch('webapps.stripe_integration.provider.StripeProvider.get_or_create_stripe_customer')
    def test_split_cash_stripe_chargeback(self, customer_mock, update_event_mock):
        customer_mock.return_value = None

        txn = self.create_split_transaction(
            [
                {
                    'payment_type_code': PaymentTypeEnum.CASH,
                    'amount': 100,
                },
                {
                    'payment_type_code': PaymentTypeEnum.KEYED_IN_PAYMENT,
                    'amount': 23.45,
                },
            ]
        )

        payment_intent = self.make_success(txn, 1)

        txn.refresh_from_db()
        success_row = txn.payment_rows[1]

        self._simulate_stripe_incoming_webhook(
            data=StripeTestEventBody.charge_dispute_created(
                payment_intent_external_id=payment_intent.external_id,
                amount=2345,
            ),
            expect_new_webhook_handling=True,
        )

        self._check_split_cash_stripe_chargeback(
            txn, success_row, PaymentMethodType.KEYED_IN_PAYMENT
        )
        self.assertEqual(update_event_mock.call_count, 2)  # new -> processing -> success
