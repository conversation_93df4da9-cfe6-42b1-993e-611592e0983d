from unittest import TestCase

import pytest
from dateutil.relativedelta import relativedelta
from mock import patch
from model_bakery import baker
from parameterized import parameterized

from lib.tools import tznow
from webapps.booking.tests.utils import create_appointment
from webapps.booksy_pay.models import BooksyPaySettings
from webapps.business.baker_recipes import (
    business_recipe,
    service_recipe,
    service_variant_recipe,
)
from webapps.business.enums import PriceType
from webapps.business.models import Business
from webapps.business.models.service import ServiceVariantPayment
from webapps.pos.baker_recipes import (
    payment_type_recipe,
    payment_row_recipe,
    receipt_recipe,
    transaction_recipe,
)
from webapps.pos.enums import PaymentTypeEnum, receipt_status
from webapps.pos.models import Transaction
from webapps.pos.utils import (
    get_booksy_pay_payment_info,
    is_no_show_protection_active,
)


@pytest.mark.django_db
@pytest.mark.parametrize(
    'service_variant_active, with_payment, expected',
    [
        (True, True, True),
        (False, True, False),
        (True, False, False),
        (<PERSON>alse, <PERSON>alse, False),
    ],
)
def test_is_no_show_protection_active(
    service_variant_active,
    with_payment,
    expected,
):
    business_1 = business_recipe.make()
    service_variant_1 = service_variant_recipe.make(
        service=service_recipe.make(
            business=business_1,
        ),
        active=service_variant_active,
    )
    if with_payment:
        baker.make(ServiceVariantPayment, service_variant=service_variant_1)

    assert is_no_show_protection_active(business_id=business_1.id) is expected


@pytest.mark.django_db
def test_is_no_show_protection_active__with_deleted_payment():
    business_1 = business_recipe.make()
    service_variant_1 = service_variant_recipe.make(
        service=service_recipe.make(
            business=business_1,
        ),
    )
    baker.make(ServiceVariantPayment, service_variant=service_variant_1, deleted=tznow())
    assert is_no_show_protection_active(business_id=business_1.id) is False


@pytest.mark.django_db
@pytest.mark.parametrize(
    'service_with_payment, expected',
    [
        (True, True),
        (False, False),
    ],
)
def test_is_no_show_protection_active__appointment(service_with_payment, expected):
    business_1 = business_recipe.make()
    service_variant_1 = service_variant_recipe.make(
        service=service_recipe.make(
            business=business_1,
        ),
    )
    baker.make(ServiceVariantPayment, service_variant=service_variant_1)

    service_variant_2 = service_variant_recipe.make(
        service=service_recipe.make(
            business=business_1,
        ),
    )

    appointment = create_appointment(
        [
            dict(
                service_variant=(
                    service_variant_1 if service_with_payment is True else service_variant_2
                )
            ),
        ],
        business=business_1,
    )
    assert (
        is_no_show_protection_active(
            business_id=business_1.id,
            service_variants_ids=[
                subbooking.service_variant.id for subbooking in appointment.subbookings
            ],
        )
        is expected
    )


booksy_pay_late_cancellation_window = relativedelta(days=+8)


@pytest.mark.django_db
class TestGetBooksyPayPaymentInfo(TestCase):
    def setUp(self):
        super().setUp()
        self.business = baker.make(
            Business,
            active=True,
            status=Business.Status.PAID,
        )
        self.pos = baker.make(
            'pos.POS',
            business=self.business,
        )
        self.bp_settings = baker.make(
            BooksyPaySettings,
            pos=self.pos,
            enabled=True,
            allowed=True,
            late_cancellation_window=booksy_pay_late_cancellation_window,
        )
        self.booksy_pay_payment_type = payment_type_recipe.make(
            code=PaymentTypeEnum.BOOKSY_PAY,
            pos=self.pos,
        )
        self.appointment = create_appointment(
            total_type=PriceType.FIXED,
            business=self.business,
        )
        self.transaction = transaction_recipe.make(
            appointment=self.appointment,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
        )

    @parameterized.expand(
        [
            (
                True,
                receipt_status.BOOKSY_PAY_SUCCESS,
                {
                    'is_auto_refund_possible': False,
                    'is_paid': True,
                    'late_cancellation_window': booksy_pay_late_cancellation_window,
                    'refundable': True,
                },
            ),
            (
                False,
                receipt_status.BOOKSY_PAY_SUCCESS,
                {
                    'is_auto_refund_possible': False,
                    'is_paid': True,
                    'late_cancellation_window': booksy_pay_late_cancellation_window,
                    'refundable': False,
                },
            ),
            (
                True,
                receipt_status.REFUNDED,
                {
                    'is_auto_refund_possible': False,
                    'is_paid': False,
                    'late_cancellation_window': booksy_pay_late_cancellation_window,
                    'refundable': False,
                },
            ),
        ]
    )
    @patch('webapps.pos.refund.is_refund_possible')
    def test_refundable(self, refund_possible, payment_status, expected, is_refund_possible_mock):
        is_refund_possible_mock.return_value = (refund_possible, None)
        receipt = receipt_recipe.make(
            transaction=self.transaction,
            status_code=payment_status,
            payment_type=self.booksy_pay_payment_type,
        )
        payment_row_recipe.make(
            receipt=receipt,
            payment_type=receipt.payment_type,
            status=payment_status,
        )
        self.transaction.latest_receipt = receipt
        self.transaction.save()

        booksy_pay_payment_info = get_booksy_pay_payment_info(appointment=self.appointment)
        self.assertDictEqual(booksy_pay_payment_info, expected)

    @parameterized.expand(
        [
            (
                relativedelta(hours=-1),
                {
                    'is_auto_refund_possible': False,
                    'is_paid': True,
                    'late_cancellation_window': booksy_pay_late_cancellation_window,
                    'refundable': True,
                },
            ),
            (
                relativedelta(hours=1),
                {
                    'is_auto_refund_possible': True,
                    'is_paid': True,
                    'late_cancellation_window': booksy_pay_late_cancellation_window,
                    'refundable': True,
                },
            ),
        ]
    )
    @patch('webapps.pos.refund.is_refund_possible')
    def test_is_auto_refund_possible(self, additional_time, expected, is_refund_possible_mock):
        is_refund_possible_mock.return_value = (True, None)
        self.appointment.booked_from = (
            tznow() + booksy_pay_late_cancellation_window + additional_time
        )
        self.appointment.save()

        receipt = receipt_recipe.make(
            transaction=self.transaction,
            status_code=receipt_status.BOOKSY_PAY_SUCCESS,
            payment_type=self.booksy_pay_payment_type,
        )
        payment_row_recipe.make(
            receipt=receipt,
            payment_type=receipt.payment_type,
            status=receipt_status.BOOKSY_PAY_SUCCESS,
        )
        self.transaction.latest_receipt = receipt
        self.transaction.save()

        booksy_pay_payment_info = get_booksy_pay_payment_info(appointment=self.appointment)
        self.assertDictEqual(booksy_pay_payment_info, expected)
