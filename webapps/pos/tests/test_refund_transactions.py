import uuid
from decimal import Decimal

import pytest
from django.test import override_settings
from mock import Mock, patch
from model_bakery import baker

from webapps.business.models import Resource
from webapps.pos.enums import (
    POSPlanPaymentTypeEnum,
    PaymentProviderEnum,
    receipt_status,
)
from webapps.pos.models import (
    POSPlan,
    PaymentRow,
    Receipt,
    Transaction,
)
from webapps.pos.refund import (
    mark_refund_requested,
    mark_sent_for_refund,
    is_refund_possible,
)
from webapps.pos.serializers import RefundOperatorSerializer
from webapps.pos.tests import TestCaseWithSetUp
from webapps.user.models import User


@pytest.mark.django_db
@patch('webapps.pos.refund.is_refund_possible', Mock(return_value=(True, None)))
class TestRefundTransition(TestCaseWithSetUp):
    """Test transitions for cash method."""

    def setUp(self):
        super().setUp()

        self.transaction = baker.make(
            Transaction,
            pos=self.pos,
        )

    @override_settings(POS__REFUNDS=True)
    @patch('webapps.point_of_sale.services.basket_payment.BasketPaymentService')
    @patch('webapps.point_of_sale.models.BasketPayment.objects')
    @patch('webapps.pos.refund.txn_refactor_stage2_enabled', return_value=True)
    def test_is_refund_possible_for_booksy_gift_card_is_false(
        self,
        mock_txn_stage2_enabled,
        mock_basket_payment_objects,
        mock_basket_payment_service,
    ):
        class InvalidBasketPayment(Exception):
            pass

        mock_basket_payment_objects.get.return_value = Mock()
        mock_basket_payment_service.is_refund_possible.side_effect = Mock(
            side_effect=InvalidBasketPayment("Booksy Gift Cards payment method is not refundable")
        )

        receipt = baker.make(
            Receipt,
            transaction=self.transaction,
            status_code=receipt_status.PAYMENT_SUCCESS,
            payment_type=self.booksy_gift_card,
        )
        pr = baker.make(
            PaymentRow,
            receipt=receipt,
            amount=Decimal(10),
            payment_type=self.booksy_gift_card,
            pnref=uuid.uuid4(),
            status=receipt_status.PAYMENT_SUCCESS,
        )
        self.transaction.latest_receipt = receipt
        self.transaction.save()
        with patch('webapps.point_of_sale.exceptions.InvalidBasketPayment', InvalidBasketPayment):
            state, error = is_refund_possible(
                pr, check_requested=True, check_balance=False, refresh_transaction=False
            )

        mock_basket_payment_service.is_refund_possible.assert_called_once_with(
            original_basket_payment=mock_basket_payment_objects.get.return_value,
        )
        assert state is False
        assert error.code.value == 'ROW_NOT_REFUNDABLE'
        assert error.message == 'Row has wrong status or type'

    @override_settings(POS__REFUNDS=True)
    @patch('webapps.pos.refund.txn_refactor_stage2_enabled', Mock(return_value=True))
    @patch('webapps.point_of_sale.models.BasketPayment.objects')
    @patch('webapps.point_of_sale.services.basket_payment.BasketPaymentService')
    def test_booksy_pay(
        self,
        mock_basket_payment_service,
        mock_basket_payment_objects,
    ):
        mock_basket_payment_objects.get.return_value = Mock()
        mock_basket_payment_service.is_refund_possible.return_value = (True, None)
        receipt = baker.make(
            Receipt,
            transaction=self.transaction,
            status_code=receipt_status.BOOKSY_PAY_SUCCESS,
            payment_type=self.booksy_pay,
        )
        pr = baker.make(
            PaymentRow,
            receipt=receipt,
            amount=Decimal(50),
            payment_type=self.booksy_pay,
            pnref=uuid.uuid4(),
            status=receipt_status.BOOKSY_PAY_SUCCESS,
            provider=PaymentProviderEnum.STRIPE_PROVIDER,
        )
        self.transaction.latest_receipt = receipt
        self.transaction.save()

        possible, _ = is_refund_possible(
            pr,
            check_requested=True,
            check_balance=False,
        )
        self.assertTrue(possible)

        mark_refund_requested(pr, self.user)
        self.assertIsNotNone(pr.refund_requested)
        self.assertIsNotNone(pr.refund_operator)

    def test_mark_sent_for_refund(self):
        staffer = baker.make(Resource, staff_user=self.user)
        basket_payment_id = uuid.uuid4()
        pos_plan = baker.make(
            POSPlan,
            min_txn_num=0,
            provision=0.0235,
            txn_fee=0.1,
            plan_type=POSPlanPaymentTypeEnum.STRIPE_MOBILE_PAYMENT,
        )
        self.pos.pos_plans.add(pos_plan)
        receipt = baker.make(
            Receipt,
            transaction=self.transaction,
            payment_type=self.prepayment,
            status_code=receipt_status.PREPAYMENT_SUCCESS,
        )
        payment_row = baker.make(
            PaymentRow,
            receipt=receipt,
            basket_payment_id=basket_payment_id,
            amount=Decimal(50),
            pnref=uuid.uuid4(),
            status=receipt_status.BOOKSY_PAY_SUCCESS,
            provider=PaymentProviderEnum.STRIPE_PROVIDER,
            payment_type=self.prepayment,
        )
        self.transaction.latest_receipt = receipt
        self.transaction.save()

        result = mark_sent_for_refund(basket_payment_id, staffer.id)
        payment_row.refresh_from_db()

        self.assertIsNotNone(payment_row.refund_requested)
        self.assertEqual(payment_row.refund_operator, staffer.staff_user)
        self.assertEqual(result.parent_payment_row, payment_row)
        self.assertEqual(result.basket_payment_id, basket_payment_id)
        self.assertEqual(result.status, receipt_status.SENT_FOR_REFUND)

    def test_mark_sent_for_partial_refund(self):
        staffer = baker.make(Resource, staff_user=self.user)
        basket_payment_id = uuid.uuid4()
        pos_plan = baker.make(
            POSPlan,
            min_txn_num=0,
            provision=0.0235,
            txn_fee=0.1,
            plan_type=POSPlanPaymentTypeEnum.STRIPE_MOBILE_PAYMENT,
        )
        self.pos.pos_plans.add(pos_plan)
        receipt = baker.make(
            Receipt,
            transaction=self.transaction,
            payment_type=self.prepayment,
            status_code=receipt_status.PREPAYMENT_SUCCESS,
        )
        payment_row = baker.make(
            PaymentRow,
            receipt=receipt,
            basket_payment_id=basket_payment_id,
            amount=Decimal(50),
            pnref=uuid.uuid4(),
            status=receipt_status.BOOKSY_PAY_SUCCESS,
            provider=PaymentProviderEnum.STRIPE_PROVIDER,
            payment_type=self.prepayment,
        )
        self.transaction.latest_receipt = receipt
        self.transaction.save()

        refund_amount = Decimal("25.1")
        result = mark_sent_for_refund(basket_payment_id, staffer.id, amount=refund_amount)
        payment_row.refresh_from_db()

        self.assertIsNotNone(payment_row.refund_requested)
        self.assertEqual(payment_row.refund_operator, staffer.staff_user)

        self.assertEqual(result.parent_payment_row, payment_row)
        self.assertEqual(result.amount, refund_amount)
        self.assertEqual(result.basket_payment_id, basket_payment_id)
        self.assertEqual(result.status, receipt_status.SENT_FOR_REFUND)

        self.assertEqual(
            PaymentRow.objects.filter(parent_payment_row=result.parent_payment_row).count(),
            2,
        )

        split_payment_row = (
            PaymentRow.objects.filter(parent_payment_row=result.parent_payment_row)
            .exclude(id=result.id)
            .get()
        )
        self.assertEqual(split_payment_row.amount, payment_row.amount - refund_amount)
        self.assertEqual(split_payment_row.status, receipt_status.BOOKSY_PAY_SUCCESS)

    def test_mark_sent_for_partial_refund_with_full_amount(self):
        """
        In this scenario we pass refund amount equal to payment_row amount
        so we should handle this refund as full refund not as partial
        """
        staffer = baker.make(Resource, staff_user=self.user)
        basket_payment_id = uuid.uuid4()
        pos_plan = baker.make(
            POSPlan,
            min_txn_num=0,
            provision=0.0235,
            txn_fee=0.1,
            plan_type=POSPlanPaymentTypeEnum.STRIPE_MOBILE_PAYMENT,
        )
        self.pos.pos_plans.add(pos_plan)
        receipt = baker.make(
            Receipt,
            transaction=self.transaction,
            payment_type=self.prepayment,
            status_code=receipt_status.PREPAYMENT_SUCCESS,
        )
        payment_amount = Decimal("50.13")
        payment_row = baker.make(
            PaymentRow,
            receipt=receipt,
            basket_payment_id=basket_payment_id,
            amount=payment_amount,
            pnref=uuid.uuid4(),
            status=receipt_status.BOOKSY_PAY_SUCCESS,
            provider=PaymentProviderEnum.STRIPE_PROVIDER,
            payment_type=self.prepayment,
        )
        self.transaction.latest_receipt = receipt
        self.transaction.save()

        result = mark_sent_for_refund(basket_payment_id, staffer.id, amount=payment_amount)
        payment_row.refresh_from_db()

        self.assertIsNotNone(payment_row.refund_requested)
        self.assertEqual(payment_row.refund_operator, staffer.staff_user)

        self.assertEqual(result.parent_payment_row, payment_row)
        self.assertEqual(result.amount, payment_amount)
        self.assertEqual(result.basket_payment_id, basket_payment_id)
        self.assertEqual(result.status, receipt_status.SENT_FOR_REFUND)

        self.assertEqual(
            PaymentRow.objects.filter(parent_payment_row=result.parent_payment_row).count(),
            1,
        )


@pytest.mark.django_db
@pytest.mark.parametrize(
    'user_data, expected_label',
    [
        ({'is_staff': True, 'first_name': 'Test', 'last_name': 'User'}, 'Booksy Support Team'),
        ({'is_staff': False, 'first_name': 'Test', 'last_name': 'User'}, 'Test User'),
    ],
)
def test_get_refund_operator_label(user_data, expected_label):
    user = baker.make(User, **user_data)
    assert RefundOperatorSerializer.get_label(user) == expected_label
