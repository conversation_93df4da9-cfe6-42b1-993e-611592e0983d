# pylint: disable=too-many-lines,too-many-statements
from datetime import datetime, timezone, timedelta
from http import HTTPStatus

import pytest
from dateutil.relativedelta import relativedelta
from django.utils.crypto import get_random_string
from model_bakery import baker
from rest_framework.test import APIClient

from service.tests import BaseAsyncHTTPTest
from webapps.booking.models import Appointment
from webapps.booking.tests.utils import create_appointment
from webapps.business.enums import PriceType
from webapps.business.models import (
    Resource,
    Service,
    ServiceVariant,
)
from webapps.market_pay.models import AccountHolder
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.pos.enums import (
    PaymentTypeEnum,
    POSPlanPaymentTypeEnum,
)
from webapps.pos.models import (
    POS,
    PaymentType,
    POSPlan,
)
from webapps.pos.tests.pos_refactor.helpers_stripe import StripeMixin
from webapps.user.models import User


@pytest.mark.django_db
class TestNoShowProtectionEndpoint(
    BaseAsyncHTTPTest, StripeMixin
):  # pylint: disable=too-many-instance-attributes
    def setUp(self):
        super().setUp()
        self.client = APIClient()
        self.client_secret = get_random_string(12)
        self.payment_intent_stripe_id = 'pi_123456789'

        self.pos = baker.make(
            POS,
            business=self.business,
            active=True,
            _force_stripe_pba=True,
            ba_deposit_enabled=True,
            pay_by_app_status=POS.PAY_BY_APP_ENABLED,
        )
        self.pos_plan = baker.make(
            POSPlan,
            min_txn_num=0,
            provision=0.0235,  # not random just to force non-negative value of a fee
            txn_fee=0.1,  # not random just to force non-negative value of a fee
            plan_type=POSPlanPaymentTypeEnum.STRIPE_MOBILE_PAYMENT,
        )
        self.business_wallet = PaymentGatewayPort.get_or_create_business_wallet(
            self.business.id,
            statement_name=self.business.name,
        )[0]
        baker.make(AccountHolder, pos=self.business.pos, ever_passed_kyc=True)

        self.service_1 = baker.make(Service, business=self.business)
        self.service_variant = baker.make(
            ServiceVariant,
            service=self.service_1,
            duration=relativedelta(minutes=15),
            price=125,
            type=PriceType.FIXED,
        )
        self.service_2 = baker.make(Service, business=self.business)
        self.service_variant_2 = baker.make(
            ServiceVariant,
            service=self.service_1,
            duration=relativedelta(minutes=15),
            type=PriceType.VARIES,
        )

        booked_from1 = datetime(2019, 1, 1, 10, 0, tzinfo=timezone.utc)
        self.booking_1 = create_appointment(
            [
                {
                    'booked_from': booked_from1,
                    'booked_till': booked_from1 + timedelta(minutes=15),
                    'service_variant': self.service_variant,
                },
            ],
            business=self.business,
            status=Appointment.STATUS.ACCEPTED,
        ).subbookings[0]
        self.payment_failed_body = {
            'id': 'evt_failed',
            'type': 'payment_intent.payment_failed',
            'data': {
                'object': {
                    'id': self.payment_intent_stripe_id,
                    'status': 'failed',
                    'last_payment_error': {
                        'code': 'card_declined',
                        'message': 'Your card was declined.',
                    },
                }
            },
        }
        self.business_wallet = PaymentGatewayPort.get_or_create_business_wallet(
            self.business.id,
            statement_name=self.business.name,
        )[0]
        self.staffers = [
            self.owner,
            baker.make(
                Resource,
                type=Resource.STAFF,
                business=self.business,
                staff_user=baker.make(User),
            ),
        ]
        baker.make(PaymentType, code=PaymentTypeEnum.PAY_BY_APP, pos=self.pos)

    def test_owner_changes_nsp_rules(self):
        # given
        nsp_url = (
            f'/business_api/me/businesses/{self.business.id}/pos/no_show_fee_service_protection/'
        )
        nsp_request_body = {
            "no_show_cancel_time_option": {"days": 4},
            "no_show_protection_policy": "cancellation policy",
            "auto_charge_cancellation_fee": True,
            "services": [
                {
                    "id": self.service_1.id,
                    "no_show_protection": {"type": "CF", "payment_amount": '60.00'},
                },
            ],
        }
        # when
        nsp_post_resp = self.fetch(nsp_url, method='POST', body=nsp_request_body)
        # then
        assert nsp_post_resp.code == HTTPStatus.OK

    def test_staffer_can_not_change_nsp_rules(self):
        # given
        user = baker.make(
            User,
            email="<EMAIL>",
        )
        self.change_user(user)
        baker.make(
            Resource,
            visible=True,
            type=Resource.STAFF,
            staff_user=user,
            business=self.business,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_STAFF,
        )

        nsp_url = (
            f'/business_api/me/businesses/{self.business.id}/pos/no_show_fee_service_protection/'
        )
        nsp_request_body = {
            "no_show_cancel_time_option": {"days": 4},
            "no_show_protection_policy": "cancellation policy",
            "auto_charge_cancellation_fee": True,
            "services": [
                {
                    "id": self.service_1.id,
                    "no_show_protection": {"type": "CF", "payment_amount": '60.00'},
                },
            ],
        }
        # when
        nsp_post_resp = self.fetch(nsp_url, method='POST', body=nsp_request_body)
        # then
        assert nsp_post_resp.code == HTTPStatus.NOT_FOUND
