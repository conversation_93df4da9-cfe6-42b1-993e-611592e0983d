from datetime import datetime, timed<PERSON><PERSON>
from logging import getLogger
from typing import TYPE_CHECKING, Union

from dateutil.relativedelta import relativedelta
from django.template import defaultfilters
from django.utils.translation import gettext as _

from lib.feature_flag.adapter import UserData
from lib.feature_flag.enums import CustomUserAttributes
from lib.feature_flag.feature import ServiceNameReplicationFlag, BlikForFreePromoFlag
from lib.feature_flag.feature.booksy_pay import (
    BooksyPayRefundOnCxCancellationFlag,
)
from lib.feature_flag.feature.payment import BlikDiscountFlag
from lib.tools import duration_formatter, sasrt, sget_v2, tznow
from webapps.business.models import (
    Business,
    Resource,
    Service,
    ServiceVariant,
    ServiceVariantPayment,
)
from webapps.pos.enums import PaymentTypeEnum, receipt_status, TapToPayStatus
from webapps.pos.typing import BooksyPayPaymentInfo, RefundEligibility

logger = getLogger('webapps.pos.utils')

if TYPE_CHECKING:
    from webapps.booking.models import Appointment, SubBooking


def check_seller(business: Business) -> None:
    sasrt(
        hasattr(business, 'invoice_seller_details'),
        400,
        [
            {
                'code': 'invoice_business_details',
                'type': 'validation',
                'description': _('Business does not have seller details configured'),
            }
        ],
    )


def name_lines_from_booking(
    booking: 'SubBooking', service_variant: ServiceVariant, service_name: str
) -> tuple[str, str]:
    if ServiceNameReplicationFlag(
        UserData(
            custom={
                CustomUserAttributes.BUSINESS_ID: sget_v2(booking, ['appointment', 'business_id'])
            }
        )
    ):
        service_name = _get_service_name(booking, service_variant, service_name)
        duration = _get_duration(booking, service_variant)
    else:
        if service_variant:
            service_name = service_variant.service.name
            duration = service_variant.duration
        elif booking:
            service_name = service_variant.service.name if service_variant else booking.service_name
            duration = booking.booked_till - booking.booked_from
        else:
            duration = None

    # date
    tz = booking.appointment.business.get_timezone() if booking else None
    booked_from = booking.booked_from.astimezone(tz) if booking else None
    formatted_date = (
        '{}, {}'.format(  # pylint:disable=consider-using-f-string
            defaultfilters.date(booked_from, 'D'),
            defaultfilters.date(booked_from, 'DATETIME_FORMAT'),
        )
        if booking
        else ''
    )

    # staffer name
    staffers = (
        [
            resource
            for resource in booking.resources.all()  # prefetched
            if resource.type == Resource.STAFF
        ]
        if booking
        else []
    )
    staffer_name = staffers[0].name if staffers else None

    # duration
    duration = f' ({duration_formatter(duration)})' if duration else ''

    # return transaction name lines
    name_line_1 = ((service_name or '') + duration).strip()
    name_line_2 = formatted_date
    if staffer_name is not None:
        name_line_2 = ', '.join([formatted_date, staffer_name])
    return name_line_1, name_line_2


def _get_service_name(
    booking: 'SubBooking', service_variant: ServiceVariant, service_name: str
) -> str:
    if internal_service_name := sget_v2(booking, ['service_data_internal', 'service_name']):
        return internal_service_name
    if service_name_from_variant := sget_v2(service_variant, ['service', 'name']):
        return service_name_from_variant
    if service_name_from_booking := sget_v2(booking, ['service_name']):
        return service_name_from_booking
    return service_name


def _get_duration(
    booking: 'SubBooking',
    service_variant: ServiceVariant,
) -> relativedelta | timedelta | None:
    if service_variant:
        return service_variant.duration
    if booking:
        return booking.booked_till - booking.booked_from
    return None


def is_no_show_protection_active(
    business_id: int,
    service_variants_ids: list[int] | None = None,
) -> bool:
    qs = Service.objects.filter(
        business_id=business_id,
        service_variants__deleted__isnull=True,
        service_variants__active=True,
        service_variants__payment__isnull=False,
        service_variants__payment__deleted__isnull=True,
    )
    if service_variants_ids is not None:
        qs = qs.filter(service_variants__in=service_variants_ids)

    return qs.exists()


def is_business_eligible_for_blik_promo(business_pk: int):
    return (
        BlikForFreePromoFlag(UserData(subject_key=business_pk))
        and _is_blik_promo_active()
        and _is_business_in_blik_promo(business_pk)
        and not _business_has_prepayment(business_pk)
    )


def _is_blik_promo_active():
    blik_discount_data = BlikDiscountFlag()
    if not blik_discount_data:
        # feature disabled
        return False
    try:
        prepayment_promo_start = int(blik_discount_data.get('prepayment_promo_start'))
        prepayment_promo_end = int(blik_discount_data.get('prepayment_promo_end'))
    except (ValueError, TypeError) as e:
        # pylint: disable=consider-using-f-string
        logger.warning("BlikDiscountFlag decoding error: {0}, defaulting to no discount".format(e))
        return False

    now = int(datetime.now().timestamp())

    return prepayment_promo_start <= now <= prepayment_promo_end


def _is_business_in_blik_promo(business_pk: int):
    from webapps.pos.models import POS

    return POS.objects.filter(
        business_id=business_pk,
        blik_in_prepayment_promo_enabled=True,
    ).exists()


def _business_has_prepayment(business_pk: int):
    return ServiceVariantPayment.objects.filter(
        service_variant__service__business_id=business_pk,
    ).exists()


def extract_appointment_id(booking: Union['SubBooking', 'Appointment']) -> int:
    from webapps.booking.models import Appointment, SubBooking

    if isinstance(booking, SubBooking):
        return booking.appointment_id
    if isinstance(booking, Appointment):
        return booking.id
    raise TypeError("booking parameter must be a SubBooking or Appointment instance")


# region booksy pay
def is_paid_by_booksy_pay(appointment_id: int) -> bool:
    # pylint: disable=cyclic-import
    from webapps.booksy_pay.utils import get_booksy_pay_transaction

    return get_booksy_pay_transaction(appointment_id).exists()


def get_booksy_pay_payment_info(appointment: 'Appointment') -> BooksyPayPaymentInfo:
    # pylint: disable=cyclic-import
    from webapps.booksy_pay.services.settings import BooksyPaySettingsService
    from webapps.booksy_pay.utils import get_booksy_pay_transaction
    from webapps.pos.refund import is_refund_possible

    transaction = get_booksy_pay_transaction(appointment.id).last()
    if transaction is not None:
        pr = transaction.latest_receipt.payment_rows.filter(
            payment_type__code=PaymentTypeEnum.BOOKSY_PAY,
            status__in=(
                receipt_status.BOOKSY_PAY_SUCCESS,
                receipt_status.PAYMENT_SUCCESS,
            ),
        ).last()
        refundable, _ = is_refund_possible(pr) if pr else (False, None)
    else:
        refundable = False

    pos = sget_v2(appointment.business, ['pos'])
    bp_settings = BooksyPaySettingsService().get_settings(pos_id=pos.id) if pos else None
    late_cancellation_window = sget_v2(bp_settings, ['late_cancellation_window'])

    return BooksyPayPaymentInfo(
        is_paid=bool(transaction),
        refundable=refundable,
        is_auto_refund_possible=(
            refundable
            and late_cancellation_window is not None
            and appointment.booked_from >= (tznow() + late_cancellation_window)
        ),
        late_cancellation_window=late_cancellation_window,
    )


def get_refund_eligibility(appointment: 'Appointment') -> RefundEligibility:
    """
    Checks if a refund is eligible for an appointment. Currently, it works only for Booksy Pay
    transactions. This logic may be expanded to support more payment types in the future.
    """
    if not BooksyPayRefundOnCxCancellationFlag(UserData(subject_key=appointment.business_id)):
        return RefundEligibility()

    booksy_pay_payment_info = get_booksy_pay_payment_info(appointment)
    return RefundEligibility(
        refundable=booksy_pay_payment_info['refundable'],
        is_auto_refund_possible=booksy_pay_payment_info['is_auto_refund_possible'],
    )


def is_call_for_status(appointment_id: int) -> bool:
    from webapps.pos.models import Transaction

    return bool(
        Transaction.objects.filter(
            appointment_id=appointment_id,
            transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
            latest_receipt__status_code__in=(
                receipt_status.CALL_FOR_PAYMENT_ACTIONS + receipt_status.PENDING_3DS_STATUSES
            ),
            latest_receipt__payment_type__code__in=(
                PaymentTypeEnum.BOOKSY_PAY,
                PaymentTypeEnum.PAY_BY_APP,
                PaymentTypeEnum.PREPAYMENT,
            ),
        ).exists()
    )


# endregion booksy pay


def has_payment_method_customer_reminder_notifications(business_id: int) -> bool:
    from webapps.pos.models import POS

    pos = POS.objects.filter(business_id=business_id).first()
    if not pos:
        return False

    return pos.promote_payment_method_availability_to_customers


def has_tap_to_pay_and_bcr_available(business_id: int) -> [bool, bool]:
    from webapps.pos.models import POS

    pos = POS.objects.filter(business_id=business_id).first()
    if not pos:
        return False, False

    tap_to_pay_enabled = pos.tap_to_pay_enabled and pos.tap_to_pay_status == TapToPayStatus.ENABLED
    bcr_enabled = pos.stripe_terminal_enabled and pos.is_stripe_terminal_payments_active

    return tap_to_pay_enabled, bcr_enabled
