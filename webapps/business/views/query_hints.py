import copy
import logging
from itertools import chain

import requests
from bo_obs.datadog.enums import BooksyTeams

from rest_framework import status
from rest_framework.response import Response

from drf_api.base_serializers import CustomValidationErrorSerializer
from drf_api.base_views import BaseBooksySessionGenericAPIView
from drf_api.mixins import QuerySerializerMixin
from drf_api.permissions.authentication import OptionalLogin
from lib.elasticsearch.consts import ESDocType, MAX_BUSINESS_SUGGESTION
from lib.feature_flag.adapter import UserData
from lib.feature_flag.enums import AppDomains, ExperimentVariants, SubjectType
from lib.feature_flag.metrics import FeatureFlagMetrics
from lib.feature_flag.experiment.customer import (
    SuggesterExperiments,
)
from lib.feature_flag.feature import EnableSearchServiceInModernQueryHints
from lib.search_service.client import (
    get_suggestions,
    get_treatment_suggestions,
)
from lib.searchables.response import SerializerResponse
from lib_drf.docs.schema import BooksyAutoSchema
from service.search.consts import MAX_NUMBER_OF_TREATMENT_HINTS
from service.search.serializers import (
    BusinessCategoryQueryHintSerializer,
    BusinessQueryHintSerializer,
    FocusOnQueryResponseSerializer,
    FocusOnQuerySerializer,
    ModernQueryHintsSerializer,
)
from service.tools import AnalyticsTokensMixin, ViewListItemEventMixin
from webapps.business.business_categories.cache import TreatmentCache
from webapps.business.models import BusinessCategory
from webapps.business.searchables.business.search_engine import BusinessCompletionSearchable
from webapps.business.searchables.business_category import (
    BusinessCategoryCompletionSearchable,
    BusinessTreatmentSearchable,
)
from webapps.business.searchables.serializers.business_category import BusinessCategoryHitSerializer
from webapps.kill_switch.models import KillSwitch

logger = logging.getLogger('booksy.query_hints')


class FocusOnQueryViewSchema(BooksyAutoSchema):
    def get_responses(self, path, method):
        serializer = self.get_response_serializer(path, method)
        return {
            status.HTTP_200_OK: {
                'content': {
                    'application/json': {
                        'schema': self._get_reference(serializer),
                    },
                },
                'description': 'Popular treatments based on customer bookings in the last 14 days.',
            },
        }


class FocusOnQueryView(  # nosemgrep: no-is-authenticated-permission-for-drf
    QuerySerializerMixin, BaseBooksySessionGenericAPIView
):
    booksy_teams = (BooksyTeams.CUSTOMER_SEARCH,)
    permission_classes = (OptionalLogin,)
    schema = FocusOnQueryViewSchema()
    query_serializer_class = FocusOnQuerySerializer
    serializer_class = FocusOnQueryResponseSerializer

    def get(self, request):
        """
        Return popular treatments based on customer bookings in the last 14 days.

        category - ID; allowed empty for all categories

        limit - the maximum number of returned treatments, default is 40

        selected_treatment_id - if passed and the selected treatment is not in the first n popular
        treatments (where n = limit), then the selected treatment is returned on top
        """
        serializer = self.query_serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data

        data['hidden_from_business_search_hints'] = False

        searchable = BusinessTreatmentSearchable(
            ESDocType.BUSINESS_CATEGORY,
            serializer=BusinessCategoryHitSerializer,
        )

        resp = searchable.search(data).params(size=data['limit']).execute()

        if data.get('selected_treatment_id'):
            treatments = self._get_treatments(
                selected_treatment_id=data.get('selected_treatment_id'),
                limit=data.get('limit'),
                es_query_response=resp,
            )
        else:
            treatments = resp

        serializer = self.get_serializer()
        return Response(
            serializer.to_representation({'treatments': treatments}), status=status.HTTP_200_OK
        )

    def _get_treatments(
        self,
        selected_treatment_id: int,
        limit: int,
        es_query_response: SerializerResponse,
    ):
        treatments = list(es_query_response)
        if selected_treatment_id in {treatment['id'] for treatment in treatments}:
            return treatments

        if selected_treatment := TreatmentCache.get_by_id(selected_treatment_id):
            treatments.insert(0, selected_treatment)

            if limit and len(treatments) > limit:
                treatments = treatments[:-1]

        return treatments


class RecommendedTreatmentsView(FocusOnQueryView):
    pass


class ModernQueryHintsViewSchema(BooksyAutoSchema):
    def get_responses(self, path, method):
        serializer = self.get_response_serializer(path, method)
        return {
            status.HTTP_200_OK: {
                'content': {
                    'application/json': {
                        'schema': self._get_reference(serializer),
                    },
                },
                'description': 'Auto-completion hints for query input',
            },
        }


class ModernQueryHintsView(  # nosemgrep: no-is-authenticated-permission-for-drf
    ViewListItemEventMixin,
    AnalyticsTokensMixin,
    QuerySerializerMixin,
    BaseBooksySessionGenericAPIView,
):
    # pylint: disable=too-many-ancestors
    booksy_teams = (BooksyTeams.CUSTOMER_SEARCH,)
    permission_classes = (OptionalLogin,)
    schema = ModernQueryHintsViewSchema()
    query_serializer_class = ModernQueryHintsSerializer
    serializer_class = CustomValidationErrorSerializer

    def get(self, request):
        """
        Get auto-completion hints for query input.

        text - query used to match categories and businesses

        location_id - Region id to search businesses

        location_geo - coordinates used to find nearest businesses

        category_id - Category id used to filter businesses
        """
        serializer = self.query_serializer_class(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data

        if EnableSearchServiceInModernQueryHints():
            response, businesses_for_event = self._get_suggestions_using_search_service(data)
        else:
            response, businesses_for_event = self._get_legacy_suggestions(data)

        self.trigger_view_item_list_event(businesses=businesses_for_event, query=data.get('text'))
        return response

    def _get_suggestions_using_search_service(self, params: dict) -> tuple[Response, list]:
        """
        Gets suggestions using the search service, handling A/B experiments.
        Falls back to core if the service fails or no variant is assigned.
        """
        experiment_variant = SuggesterExperiments(
            UserData(
                subject_key=self.fingerprint,
                subject_type=SubjectType.FINGERPRINT.value,
                app_domain=AppDomains.CUSTOMER.value,
                is_experiment=True,
            )
        )

        with FeatureFlagMetrics().emit_variation_duration_metric(
            flag_name=SuggesterExperiments.flag_name,
            flag_value=experiment_variant,
        ):
            try:
                if experiment_variant == ExperimentVariants.CONTROL:
                    return self._get_control_variant_suggestions(params)
                if experiment_variant:
                    return self._get_experiment_variant_suggestions(params, experiment_variant)
                logger.warning("No experiment variant assigned. Getting suggestions using core.")
            except requests.RequestException:
                logger.warning("Getting suggestions using core.")

            return self._get_legacy_suggestions(params)

    def _get_control_variant_suggestions(self, params: dict) -> tuple[Response, list]:
        search_service_response = get_treatment_suggestions(query=params.get('text'))
        businesses = self._suggest_businesses(params)
        response = self._format_response_v2(search_service_response, businesses)
        return response, businesses

    def _get_experiment_variant_suggestions(self, params: dict, variant) -> tuple[Response, list]:
        search_service_response = get_suggestions(
            query=params.get('text'),
            location_geo=params.get('location_geo'),
            experiment_variant=variant,
        )
        business_ids = self._format_business_data_for_event(search_service_response['suggestions'])
        response = Response(search_service_response, status=status.HTTP_200_OK)
        return response, business_ids

    def _get_legacy_suggestions(self, params: dict) -> tuple[Response, list]:
        """Gets suggestions using core"""
        businesses = self._suggest_businesses(params)
        categories = self._suggest_categories_and_treatments(params)
        response = self._format_response(categories, businesses)
        return response, businesses

    @staticmethod
    def _format_business_data_for_event(suggestions: list) -> list:
        return [
            {
                'id': suggestion['query_parameters']['id'],
                'is_b_listing': suggestion['query_parameters']['is_b_listing'],
            }
            for suggestion in suggestions
            if suggestion.get('object_type') == 'business'
        ]

    @staticmethod
    def _format_response(categories, businesses):
        number_categories = len(categories)
        treatment_counter = 0
        hints = []
        for _id, item in enumerate(chain(categories, businesses), 1):
            if _id <= number_categories:
                if item.get('type') == BusinessCategory.CATEGORY:
                    object_type = 'category'
                elif treatment_counter < MAX_NUMBER_OF_TREATMENT_HINTS:
                    object_type = 'treatment'
                    treatment_counter += 1
                else:
                    continue

            else:
                object_type = 'b_listing' if item['is_b_listing'] else 'business'
            hints.append(
                {
                    'object_type': object_type,
                    'label': item['name'],
                    'query_parameters': item,
                }
            )

        return Response(
            {
                'suggestions': hints,
                'number_category_suggestions': number_categories,
            },
            status=status.HTTP_200_OK,
        )

    @staticmethod
    def _format_response_v2(response, businesses):
        # Search service returns only categories and treatments for now
        number_categories = len(response['suggestions'])

        response['number_category_suggestions'] = number_categories
        for item in businesses:
            object_type = 'b_listing' if item['is_b_listing'] else 'business'
            response['suggestions'].append(
                {
                    'object_type': object_type,
                    'label': item['name'],
                    'query_parameters': item,
                }
            )
        return Response(
            response,
            status=status.HTTP_200_OK,
        )

    def _suggest_businesses(self, data):
        request_data = copy.deepcopy(data)
        request_data['business_text'] = request_data['text']

        suggestions = self.get_business_suggestions(
            query=request_data['text'],
            coordinates=request_data['coordinates'],
        )

        return BusinessQueryHintSerializer(instance=suggestions, many=True).data

    def _suggest_categories_and_treatments(self, data):
        query = data['text']

        suggestions = self.get_business_category_suggestions(query)

        return BusinessCategoryQueryHintSerializer(instance=suggestions, many=True).data

    def get_business_suggestions(self, query, coordinates=None):
        searchable = BusinessCompletionSearchable(ESDocType.BUSINESS)
        data = {
            'query': query,
            'coordinates': coordinates,
        }
        response = searchable.params(
            size=MAX_BUSINESS_SUGGESTION,
            track_total_hits=False,
            _source_includes=[
                'name',
                'id',
                'slug',
                'sitemap_url',
                'thumbnail_photo',
                'is_b_listing',
                'business_location',
            ],
        ).execute(data)

        return response.hits

    @classmethod
    def get_business_category_suggestions(cls, query, category_type=None, limit=40):
        searchable = BusinessCategoryCompletionSearchable(ESDocType.BUSINESS_CATEGORY)
        data = {
            'query': query,
            'type': category_type,
            'visible_for_customer': True,
        }
        response = searchable.params(
            size=limit,
            track_total_hits=False,
            _source_includes=[
                'id',
                'internal_name',
                'full_name',
                'slug',
                'type',
            ],
        ).execute(data)

        return response.hits

    def trigger_view_item_list_event(self, businesses, query):
        if not KillSwitch.exist_and_alive(KillSwitch.MarTech.VIEW_ITEM_LIST_IN_QUERY_HINTS):
            return

        businesses_without_b_listings = [b for b in businesses if not b['is_b_listing']]
        extra_parameters = {'search_query': query} if query else None

        self._trigger_view_item_list_event(
            item_list_id='Search_Hints',
            item_list_name=None,
            items=[
                {'item_id': business['id'], 'index': index}
                for index, business in enumerate(businesses_without_b_listings)
            ],
            extra_parameters=extra_parameters,
            allow_empty=True,
        )
