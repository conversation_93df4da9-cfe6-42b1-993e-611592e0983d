# pylint: disable=too-many-lines
# pylint: disable=cyclic-import
import copy
import logging
import operator
import re
import typing as t
from collections import Counter, defaultdict
from datetime import datetime, timedelta
from decimal import Decimal
from functools import partial, reduce

from itertools import chain
from unicodedata import normalize
from urllib.parse import quote, urljoin

from bo_obs.datadog.enums import DatadogOperationNames, DatadogCustomServices
from dateutil.relativedelta import relativedelta
from dateutil.tz import tz
from ddtrace import tracer
from django.conf import settings
from django.contrib.postgres.aggregates import ArrayAgg
from django.core.exceptions import ValidationError
from django.db import transaction
from django.db.models import Prefetch, Q, QuerySet, Exists, OuterRef
from django.utils.encoding import force_str
from django.utils.translation import gettext_lazy as _
from rest_framework import serializers
from rest_framework.fields import empty, get_attribute
from rest_framework.serializers import ListSerializer

from country_config.enums import Country
from lib.booksy_sms import parse_phone_number
from lib.db import retry_on_sync_error
from lib.feature_flag.bug import EnforceOwnerDemotionCheck
from lib.feature_flag.feature.security import SecurityBlacklistFlag
from lib.fields.phone_number import (
    BooksyCellPhoneCountrySpecificField,
    BooksyPhoneSerializerField,
)
from lib.french_certification.utils import french_certification_enabled
from lib.gdpr_descriptions import BUSINESS_AGREEMENTS
from lib.interval.tools import cmp_relativedeltas
from lib.serializers import (
    BooksyEmailField,
    BooksyTypedChoiceField,
    BooksyURLField,
    BooksyYesNoField,
    BooleanStrictField,
    BusinessAgreementMixin,
    ConstantValueField,
    DurationField,
    DynamicFieldsSerializerMixin,
    IdAndNameSerializer,
    RelativedeltaField,
    TimezoneField,
    safe_get,
    BooksyInstagramURLField,
    BooksyFacebookURLField,
)
from lib.tools import (
    format_currency,
    id_to_external_api,
    relativedelta_total_minutes,
    relativedelta_total_seconds,
    sasrt,
    sget,
    sget_v2,
    tznow,
)
from lib.validators import (
    BusinessInstagramLinkValidator,
    BusinessFacebookLinkValidator,
)
from webapps.b2b_referral.models import B2BReferralSetting
from webapps.b2b_referral.serializers import BusinessReferralCodeMixin
from webapps.billing.enums import TransactionStatus, TransactionSource
from webapps.billing.models import BillingSubscription
from webapps.billing.permissions import BillingUserPermission
from webapps.business.adapters import JETAdapter
from webapps.business.business_categories.cache import TreatmentCache
from webapps.business.business_visibility.validations import validate_business_visibility_change
from webapps.business.consts import (
    MODERATED_CATEGORIES,
    NOT_ASSIGNED,
)
from webapps.business.enums import (
    BusinessCategoryEnum,
    CancellationReasonType,
    ComboPricing,
    ComboType,
    CustomData,
    DAYS_OF_THE_WEEK,
    DiscountType,
    PriceType,
    ResourceType,
    StaffAccessLevels,
    NoShowProtectionType,
)
from webapps.business.models import (
    Business,
    BusinessPolicyAgreement,
    CancellationReason,
    ComboMembership,
    RentingVenue,
    Resource,
    SalonNetwork,
    TRIAL_STATUS_NOT_ACTIVE,
    TravelingToClients,
    get_default_annex_signed,
)
from webapps.business.models.business_change import BusinessChange
from webapps.business.models.category import BusinessCategory, ServiceSuggestion
from webapps.business.models.external import AppsFlyer
from webapps.business.models.service import (
    SERVICE_PROMOTION_DURATION,
    SERVICE_PROMOTION_LM_HOUR,
    SERVICE_VARIANT_FLASH_SALE,
    SERVICE_VARIANT_HAPPY_HOURS,
    SERVICE_VARIANT_LAST_MINUTE,
    Service,
    ServiceCategory,
    ServicePromotion,
    ServiceVariant,
    ServiceVariantChangelog,
    ServiceVariantPayment,
)
from webapps.business.online_booking_integrations import (
    IntegrationPartner,
    IntegrationStatus,
)
from webapps.business.renting_venue import (
    LOCATION_FIELDS,
    get_venue_last_updated,
)
from webapps.business.resources import AnyResource
from webapps.business.serializer_fields import (
    NoShowProtectionServiceField,
    NoShowProtectionServiceVariantField,
    validate_and_convert_percent_to_amount,
)
from webapps.business.serializers.fields import (
    ServicePriceField,
)
from webapps.business.service_price import ServicePrice
from webapps.business.service_promotions import ServicePromotions
from webapps.business.tools import booking_policy
from webapps.business.validators import (
    ResourceUniqueTogetherValidator,
    ResourceVisibilityWithServicesValidator,
    ResourceVisibilityWithServiceVariantsValidator,
)
from webapps.ecommerce.adapters import EcommercePermissionAdapter
from webapps.ecommerce.fields import HasStoreAvailableField
from webapps.feeds.enums import EventType
from webapps.feeds.utils import update_to_external_partners
from webapps.images.enums import ImageTypeEnum
from webapps.message_blast.models import CommonMessageBlastTemplate
from webapps.notification.push import notification_receivers_list
from webapps.photo.models import Photo
from webapps.photo.serializers import ServicePhotoSerializer
from webapps.pos.calculations import round_decimal
from webapps.pos.serializer_fields import PriceField
from webapps.pos.services import TransactionService
from webapps.profile_completeness.events import (
    step_launch_promotions,
    step_link_profile,
    step_turn_on_noshow_protection,
)
from webapps.public_partners.ports import PartnerAppDataMixin, PartnerAppMixin
from webapps.public_partners.utils import get_partner_names_from_business
from webapps.registrationcode.serializers import RegistrationCodeField
from webapps.schedule.base_serializers import (
    LegacyHoursSerializer,
    ResourceTimeOffSerializer,
    TimeOffReasonSerializer,
    WeekHoursSerializer,
)
from webapps.schedule.enums import (
    ALL_TIME_OFF_REASONS,
    APPLIANCE_TIME_OFF_REASONS,
    STAFF_TIME_OFF_REASONS,
)
from webapps.schedule.model_serializers import HoursSerializerMixin
from webapps.schedule.ports import deprecated_get_resource_time_offs, get_resource_time_offs
from webapps.schedule.signals import before_business_hours_update
from webapps.security_blacklist.enums import BlacklistType
from webapps.security_blacklist.validators import BlacklistValidator
from webapps.segment.tasks import analytics_protection_service_enabled_task
from webapps.structure.models import Region
from webapps.subdomain_grpc.serializers import SubdomainSerializerMixin
from webapps.user.models import User
from webapps.user.tools import get_umbrella_user
from webapps.voucher.models import (
    Voucher,
    VoucherServiceVariant,
    VoucherTemplateServiceVariant,
)
from webapps.voucher.utils import validate_package_with_prepayment
from webapps.warehouse.models import (
    Warehouse,
    WarehouseFormula,
    WarehouseFormulaRow,
)
from webapps.warehouse.serializers.recipe import (
    FastWarehouseRecipesRowSerializer,
    WarehouseRecipesRowSerializer,
)
from webapps.zoom.utils import has_zoom_credentials
from webapps.public_partners.models import (
    OAuth2ApplicationCategoryLink,
    OAuth2Application,
    OAuth2Installation,
)


log = logging.getLogger('booksy.service')


class BusinessDetailsLocationField(serializers.Field):
    """Read-only field for business location (BusinessDetailsLocation)."""

    def __init__(self, **kwargs):
        """Force read-only and whole instance as source."""
        kwargs['source'] = '*'
        kwargs['read_only'] = True
        super().__init__(**kwargs)

    def to_representation(self, value: Business):
        return value.get_formatted_address()


class TravelingToClientsSerializer(serializers.ModelSerializer):
    distance = serializers.IntegerField(
        min_value=1,
        max_value=50,
        default=15,
    )

    class Meta:
        model = TravelingToClients
        fields = [
            'price',
            'price_type',
            'distance',
            'distance_unit',
            'hide_address',
            'traveling_only',
            'policy',
        ]
        read_only_fields = [
            'distance_unit',
        ]

    def validate(self, attrs):
        if 'price_type' in attrs:
            attrs['price'] = ServiceVariantSerializerWriter.get_price_validated_with_type(
                attrs.get('price'), attrs['price_type']
            )
        return attrs


class TravelingToClientMixin(serializers.Serializer):
    traveling = TravelingToClientsSerializer(
        required=False,
        allow_null=True,
    )

    @transaction.atomic
    def create(self, validated_data):
        has_traveling_data = 'traveling' in validated_data
        traveling_data = validated_data.pop('traveling', None)
        instance = super().create(validated_data)
        if has_traveling_data:
            self._apply_traveling_data(instance, traveling_data)
        return instance

    @transaction.atomic
    def update(self, instance, validated_data):
        has_traveling_data = 'traveling' in validated_data
        traveling_data = validated_data.pop('traveling', None)
        instance = super().update(instance, validated_data)
        if has_traveling_data:
            self._apply_traveling_data(instance, traveling_data)
        return instance

    @staticmethod
    def _apply_traveling_data(instance, traveling_data):
        if traveling_data is not None:
            traveling_data['deleted'] = None
            traveling, __ = TravelingToClients.all_objects.update_or_create(
                business_id=instance.id,
                defaults=traveling_data,
            )
            instance.traveling = traveling
        elif traveling_data is None and instance.id is not None:
            if hasattr(instance, 'traveling'):
                instance.traveling.soft_delete()
            # clear cache
            # pylint: disable=protected-access
            if 'traveling' in instance._state.fields_cache:
                del instance._state.fields_cache
            # pylint: enable=protected-access


class SimpleBusinessClaimSerializer(serializers.ModelSerializer):
    """Really Simple Business serializer used for claim response list"""

    cover_photo = serializers.SerializerMethodField()
    thumbnail_photo = serializers.SerializerMethodField()
    location = BusinessDetailsLocationField()

    class Meta:
        model = Business
        fields = (
            'id',
            'name',
            'location',
            'thumbnail_photo',
            'cover_photo',
            'primary_category',
        )

    @staticmethod
    def get_thumbnail_photo(business):
        return business.get_photo(ImageTypeEnum.LOGO)

    @staticmethod
    def get_cover_photo(business):
        return business.get_cover_photo_url()


class BusinessInCustomerBookingSerializer(SimpleBusinessClaimSerializer):
    """Simple Business format as used in customer_api's booking responses."""

    url = serializers.SerializerMethodField()
    traveling = TravelingToClientsSerializer()
    partners = serializers.SerializerMethodField()

    class Meta:
        model = Business
        fields = (
            'id',
            'name',
            'slug',
            'phone',
            'thumbnail_photo',
            'location',
            'active',
            'visible',
            'cover_photo',
            'url',
            'traveling',
            'primary_category',
            'partners',
        )

    @staticmethod
    def get_url(instance):
        return instance.get_marketplace_sitemap_url()

    @staticmethod
    def get_partners(instance):
        return get_partner_names_from_business(instance)


class ServiceVariantRelatedField(serializers.PrimaryKeyRelatedField):
    """
    PrimaryKeyRelatedField for ServiceVariant

    Restrict variants to business if set on serializer (parent)
    """

    def __init__(self, **kwargs):
        if 'queryset' not in kwargs:
            kwargs['queryset'] = ServiceVariant.objects.filter(
                service__business__active=True
            ).select_related('service', 'service__service_category')
        super().__init__(**kwargs)

    def get_queryset(self):
        business = self.context.get('business')
        if business:
            return self.queryset.filter(service__business=business)
        return self.queryset.all()


class ResourceSimpleSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    type = serializers.ChoiceField(
        required=True,
        choices=Resource.RESOURCE_TYPES,
    )
    name = serializers.CharField(
        required=True,
        max_length=201,
        error_messages={'max_length': _('Resource name is too long')},
    )
    active = serializers.BooleanField(read_only=True)
    visible = serializers.BooleanField()
    description = serializers.CharField(allow_blank=True)
    position = serializers.CharField(allow_blank=True, max_length=64)


class ResourceSerializer(ResourceSimpleSerializer):
    staff_user_exists = serializers.SerializerMethodField()
    is_invited = serializers.SerializerMethodField()
    invited = serializers.DateTimeField(read_only=True)

    @staticmethod
    def get_is_invited(instance) -> bool:
        return bool(sget(instance, ['invited']))

    @staticmethod
    def get_staff_user_exists(obj):
        return bool(obj.staff_user_id)

    def to_representation(self, instance):
        if instance is AnyResource:
            # handle autoassign case - treat it as an empty value
            return None
        ret = super().to_representation(instance)
        ret.update(self.photo_to_representation(instance))
        return ret

    @staticmethod
    def photo_to_representation(instance):
        photo_data = (
            {"photo": instance.photo_id, "photo_url": instance.photo.full_url}
            if instance.photo
            else {}
        )
        return photo_data


class CurrentStafferSerializer(ResourceSerializer):
    staff_access_level = serializers.ChoiceField(choices=StaffAccessLevels.choices())
    staff_user_id = serializers.ReadOnlyField()
    has_store_available = HasStoreAvailableField()


class FullResourceSerializer(serializers.ModelSerializer):
    """
    Serializes a whole Resource object, with all of its fields.
    This serializer could be used when saving whole object's structure info.
    """

    class Meta:
        model = Resource
        fields = '__all__'


class ExtendedResourceSerializer(PartnerAppDataMixin, ResourceSerializer):
    pass


class ResourceAllDayTimeOffSerializer(serializers.Serializer):
    def to_representation(self, instance):
        lower = instance.date_range.lower
        upper = instance.date_range.upper - timedelta(days=1)
        return {
            'id': instance.id,
            'off_from': f'{lower.strftime(settings.DATE_FORMAT)}T00:00',
            'off_till': f'{upper.strftime(settings.DATE_FORMAT)}T23:59',
        }


class ResourceWorkingHoursMixin(HoursSerializerMixin):
    # for backward compatiblity: remove when not needed anymore...
    time_slots = LegacyHoursSerializer(
        many=True,
        source=HoursSerializerMixin.WEEK_HOURS_FIELD,
        required=False,
    )
    # new format
    working_hours = WeekHoursSerializer(
        source=HoursSerializerMixin.WEEK_HOURS_FIELD,
        many=True,
        required=False,
    )

    def run_validation(self, data=empty):
        if data is not empty:
            # time_slots has preference since
            # old app can post old data in working_hours
            if 'time_slots' in data and 'working_hours' in data:
                del data['working_hours']
        return super().run_validation(data)


class ResourceCreatorSerializer(ResourceWorkingHoursMixin, ResourceSerializer):
    staff_email = BooksyEmailField(allow_blank=True, max_length=75)
    staff_cell_phone = serializers.CharField(allow_blank=True)
    staff_access_level = serializers.CharField()
    reviews_rank_avg = serializers.FloatField(read_only=True)
    services = serializers.SerializerMethodField()
    unhandled_services = serializers.SerializerMethodField()
    is_current_user = serializers.SerializerMethodField()
    time_offs = serializers.SerializerMethodField()
    upcoming_time_offs = serializers.SerializerMethodField()
    time_off_reasons = serializers.SerializerMethodField()
    visible_on_calendar = serializers.BooleanField(required=False)
    has_store_available = HasStoreAvailableField()

    @staticmethod
    def get_services(obj):
        return obj.active_services_ids

    @staticmethod
    def get_unhandled_services(obj):
        return list(
            obj.unhandled_services.filter(
                active=True,
            ).values_list('id', flat=True)
        )

    def get_is_current_user(self, obj):
        if not self.context.get('user'):
            return False
        return self.context['user'].id == obj.staff_user_id

    @staticmethod
    def get_time_offs(obj):
        time_offs = deprecated_get_resource_time_offs(
            resource_id=obj.id,
            business=obj.business,
        )
        return ResourceAllDayTimeOffSerializer(
            time_offs,
            many=True,
        ).data

    @staticmethod
    def get_upcoming_time_offs(obj):
        time_offs = get_resource_time_offs(
            resource_id=obj.id,
            business=obj.business,
        )
        return ResourceTimeOffSerializer(
            time_offs,
            many=True,
        ).data

    @staticmethod
    def get_time_off_reasons(obj):  # pylint: disable=unused-argument
        return TimeOffReasonSerializer(
            ALL_TIME_OFF_REASONS,
            many=True,
        ).data

    def validate_staff_access_level(self, value):
        insufficient_permissions_field_error = serializers.ValidationError(
            {
                'staff_access_level': _('Insufficient permissions'),
            }
        )
        request_user = self.context['user']

        request_user_resource = Resource.objects.filter(
            staff_user=request_user,
            business=self.context['business'],
        ).first()
        if not request_user_resource:
            raise insufficient_permissions_field_error

        request_user_access_level = getattr(
            StaffAccessLevels,
            request_user_resource.staff_access_level.upper(),
            None,
        )

        # Check for demoting owner
        if self.instance and EnforceOwnerDemotionCheck():
            if (
                self.instance.staff_access_level == StaffAccessLevels.OWNER
                and value != StaffAccessLevels.OWNER
            ):
                raise insufficient_permissions_field_error

        if request_user_access_level == StaffAccessLevels.OWNER:
            if (
                value != StaffAccessLevels.OWNER
                and self.instance
                and self.instance.staff_user
                and self.instance.staff_user.id == self.context['business'].owner.id
            ):
                raise serializers.ValidationError(
                    f"As owner you can't set your access level to {value}"
                )
            return value

        if request_user_access_level == StaffAccessLevels.MANAGER:
            if value == StaffAccessLevels.OWNER:
                raise insufficient_permissions_field_error
            return value

        raise insufficient_permissions_field_error

    @transaction.atomic
    def create(self, validated_data):
        has_store_available = validated_data.pop('has_store_available', None)
        instance = super().create(validated_data)
        EcommercePermissionAdapter.update_store_permission(instance, has_store_available)
        return instance

    @transaction.atomic
    def update(self, instance, validated_data):
        has_store_available = validated_data.pop('has_store_available', None)
        instance = super().update(instance, validated_data)
        EcommercePermissionAdapter.update_store_permission(instance, has_store_available)
        return instance


class ExtendedResourceCreatorSerializer(PartnerAppDataMixin, ResourceCreatorSerializer):
    pass


class StaffResourceCreatorSerializer(ExtendedResourceCreatorSerializer):
    @staticmethod
    def get_time_off_reasons(obj):
        return TimeOffReasonSerializer(
            STAFF_TIME_OFF_REASONS,
            many=True,
        ).data


class ApplianceResourceCreatorSerializer(ExtendedResourceCreatorSerializer):
    @staticmethod
    def get_time_off_reasons(obj):
        return TimeOffReasonSerializer(
            APPLIANCE_TIME_OFF_REASONS,
            many=True,
        ).data


class ServiceRelatedField(serializers.PrimaryKeyRelatedField):
    def __init__(self, **kwargs):
        if 'queryset' not in kwargs:
            kwargs['queryset'] = Service.objects.filter(active=True)
        super().__init__(**kwargs)

    def get_queryset(self):
        return list(
            self.queryset.filter(
                business=self.context['business'],
            ).values_list('id', flat=True)
        )

    def to_internal_value(self, data):
        allowed_ids = set(self.get_queryset())
        for primary_key in data:
            if primary_key not in allowed_ids:
                self.fail('does_not_exist', pk_value=primary_key)
        return data

    def to_representation(self, value):
        return value.pk.instance.active_services_ids


class ResourceReadSerializer(PartnerAppDataMixin, serializers.ModelSerializer):
    photo = serializers.ReadOnlyField(source='photo_id')
    photo_url = serializers.ReadOnlyField(source='photo.full_url')
    is_current_user = serializers.SerializerMethodField()
    staff_has_push = serializers.SerializerMethodField()
    is_invited = BooleanStrictField(source='invited')
    staff_user_exists = BooleanStrictField(source='staff_user_id')
    has_store_available = HasStoreAvailableField()

    class Meta:
        model = Resource
        fields = (
            'id',
            'name',
            'type',
            'description',
            'visible',
            'staff_email',
            'staff_cell_phone',
            'staff_cell_phone_with_prefix',
            'staff_has_push',
            'staff_access_level',
            'position',
            'is_current_user',
            'invited',
            'is_invited',
            'photo',
            'photo_url',
            'staff_user_exists',
            'partner_app_data',
            'has_store_available',
        )
        read_only_fields = fields

    def to_representation(self, instance):
        result = super().to_representation(instance)

        if result['photo'] is None:
            del result['photo']
        if result['photo_url'] is None:
            del result['photo_url']

        return result

    def get_is_current_user(self, instance):
        if user_id := self.context.get('user_id'):
            return instance.staff_user_id == user_id
        return False

    @staticmethod
    def get_staff_has_push(instance):
        return bool(
            notification_receivers_list(
                user_id=instance.staff_user_id, customer=False, bump_badges=False
            )
        )


def get_resource_create_update_serializer_class(business_id):
    if french_certification_enabled(business_id):
        return FCResourceCreateUpdateSerializer
    return ResourceCreateUpdateSerializer


class ResourceCreateUpdateSerializer(
    ResourceCreatorSerializer,
    serializers.ModelSerializer,
):
    id = serializers.IntegerField(read_only=True)
    active = serializers.BooleanField()
    business = serializers.PrimaryKeyRelatedField(
        write_only=True,
        queryset=Business.objects.all(),
    )
    photo = serializers.PrimaryKeyRelatedField(queryset=Photo.objects.all())
    services = ServiceRelatedField(read_only=False)
    visible = serializers.BooleanField(required=False)
    staff_email = BooksyEmailField(allow_blank=True, max_length=75)
    position = serializers.CharField(required=False, allow_blank=True, max_length=64)
    visible_on_calendar = serializers.BooleanField(required=False)
    has_store_available = HasStoreAvailableField()

    required_field = ['type', 'name']

    class Meta:
        model = Resource
        fields = (
            'id',
            'active',
            'business',
            'description',
            'is_current_user',
            'name',
            'photo',
            'services',
            'staff_access_level',
            'staff_cell_phone',
            'staff_cell_phone_with_prefix',
            'staff_email',
            'staff_user_exists',
            'time_offs',
            'type',
            'unhandled_services',
            'visible',
            'position',
            'invited',
            'is_invited',
            'visible_on_calendar',
            'has_store_available',
        )
        read_only_fields = ('is_invited', 'invited', 'staff_cell_phone_with_prefix')
        validators = [
            ResourceUniqueTogetherValidator(),
            ResourceVisibilityWithServicesValidator(),
            ResourceVisibilityWithServiceVariantsValidator(),
        ]

    def __init__(self, *args, **kwargs):
        kwargs['data'] = {k: v for k, v in kwargs['data'].items() if v is not None}
        super().__init__(*args, **kwargs)

    def validate(self, attrs):
        self._validate_required_fields(attrs)
        self._validate_staff_email(attrs.get('staff_email'))
        self._validated_services(attrs.get('services'))
        self._validate_service_variants(attrs.get('services'))

        return attrs

    def to_representation(self, instance):
        result = super().to_representation(instance)
        result.pop('photo', None)
        result.update(self.photo_to_representation(instance))
        return result

    @staticmethod
    def _raise(field, message, code='validation'):
        raise serializers.ValidationError(detail={field: message}, code=code)

    def _get_staff_user_exists(self):
        resource_has_user = self.instance and self.instance.staff_user_id
        return self.initial_data.get('staff_user_exists') or resource_has_user

    def _validated_services(self, services_ids):
        if services_ids is not None:
            old_services = self.instance.active_services if self.instance else []
            new_services = dict(
                Service.objects.filter(id__in=services_ids).values_list('id', 'combo_type')
            )
            for idx, service_id in reversed(list(enumerate(services_ids))):
                if new_services.get(service_id):
                    services_ids.pop(idx)

            ids = set(services_ids)
            if len(services_ids) < len(old_services):
                for srv in old_services:
                    if (
                        not srv.is_combo
                        and srv.id not in ids
                        and srv.one_resource_perform_service()
                    ):
                        self._raise(
                            'services',
                            _(
                                'At least one staff or resource is required '
                                'to perform service {}'
                            ).format(srv.name),
                            'conflict',
                        )

    def _validate_service_variants(self, service_ids):
        if self.instance and service_ids is not None:
            new_services = Service.objects.filter(id__in=service_ids).values_list('id', flat=True)

            services_to_delete = (
                self.instance.services_query.annotate_is_combo()
                .filter(~Q(id__in=new_services), active=True, is_combo=False)
                .values_list('id', flat=True)
            )

            variants_to_delete = self.instance.service_variants.prefetch_related(
                'resources', 'service__resources'
            ).filter(service__in=services_to_delete, active=True)

            if orphan_variant := next(
                (variant for variant in variants_to_delete if variant.has_only_one_resource), None
            ):
                self._raise(
                    'services',
                    _(
                        'At least one staff or resource is required '
                        'to perform service {} variant'
                    ).format(orphan_variant.name_with_details),
                    'conflict',
                )

    def _validate_required_fields(self, attrs):
        errors = {}
        for field in self.required_field:
            if field not in attrs:
                errors[field] = _('{} is required.').format(field.capitalize())
        if errors:
            raise serializers.ValidationError(detail=errors, code='required')

    def _validate_staff_email(self, staff_email):
        if SecurityBlacklistFlag():
            BlacklistValidator.validate(staff_email, BlacklistType.EMAIL)
        field = 'staff_email'
        staff_user_exists = self._get_staff_user_exists()
        resource_user_id, resource_staff_email = (
            (self.instance.staff_user_id, self.instance.staff_email)
            if self.instance
            else (None, None)
        )

        if staff_email and staff_email != resource_staff_email:
            if self.context['business'].resources.filter(
                staff_email=staff_email,
                active=True,
            ):
                self._raise(
                    field,
                    _(
                        'This email is already used by a staff member on your team. '
                        'Try a different email or contact Booksy Support.'
                    ),
                    'conflict',
                )

        if staff_user_exists:
            if staff_email:
                if (
                    User.objects.filter(email__iexact=staff_email)
                    .exclude(
                        id=resource_user_id,
                    )
                    .exists()
                ):
                    self._raise(
                        field,
                        _(
                            'This email is already linked to another Booksy account. '
                            'Try a different email or contact Booksy Support.'
                        ),
                        'conflict',
                    )
            else:
                self._raise(
                    field,
                    _('Email is required for staff with account'),
                    'required',
                )

    @staticmethod
    def _instance_full_clean(instance):
        try:
            instance.full_clean()
        except ValidationError as e:
            sasrt(
                False,
                400,
                [
                    {'code': 'invalid', 'field': field, 'description': error.messages[0]}
                    for field, errors in e.error_dict.items()
                    for error in errors
                ],
            )

    @staticmethod
    def _attach_combos(validated_data):
        if services_ids := validated_data.get('services'):
            qs = (
                Service.objects.filter(id__in=services_ids, combo_type__isnull=True)
                .annotate(
                    ids=ArrayAgg(
                        'service_variants__combo_parents_through__combo__service_id',
                        filter=Q(
                            service_variants__combo_parents_through__combo__service__deleted__isnull=True  # pylint: disable=line-too-long
                        )
                        & Q(
                            service_variants__combo_parents_through__combo__service__combo_type__isnull=False  # pylint: disable=line-too-long
                        ),
                    )
                )
                .values_list('ids', flat=True)
            )

            combo_services_ids = list(chain(*qs))
            validated_data['services'] = list(set(services_ids + combo_services_ids))

        return validated_data

    @transaction.atomic
    def create(self, validated_data):

        validated_data = self._attach_combos(validated_data)
        received_services = validated_data.pop('services', None)
        create_fnc = partial(super().create, validated_data)
        instance = self._save(create_fnc, validated_data)

        if received_services:
            instance.add_services(received_services)

        return instance

    @transaction.atomic
    def update(self, instance, validated_data):

        validated_data = self._attach_combos(validated_data)
        if received_services := validated_data.pop('services', None):
            instance.set_services(received_services, reset_current_services=False)
        update_fnc = partial(super().update, instance, validated_data)
        updated_instance = self._save(update_fnc, validated_data, instance)

        return updated_instance

    @transaction.atomic
    def _save(self, save_fnc, validated_data, instance=None):
        staff_user_exists = self._get_staff_user_exists()
        preexisting_user = instance and instance.staff_user
        previous_access_level = (
            instance.staff_access_level if instance else validated_data.get('staff_access_level')
        )

        instance = save_fnc()  # call create or update

        self._instance_full_clean(instance)
        if 'photo' in validated_data:
            instance.migrate_photo_to_s3()
        self._create_or_update_user(
            instance,
            validated_data,
            preexisting_user,
            previous_access_level,
            staff_user_exists,
            self.context.get('send_invitation', False),
        )
        return instance

    @staticmethod
    def _create_or_update_user(  # pylint: disable=too-many-arguments, too-many-positional-arguments
        instance,
        validated_data,
        preexisting_user,
        previous_access_level,
        staff_user_exists,
        send_invitation,
    ):
        s_email = validated_data.get('staff_email')
        if instance.type == Resource.STAFF and staff_user_exists and s_email:
            if preexisting_user is None or preexisting_user.email != s_email:
                # delete previous user's sessions
                if preexisting_user:
                    preexisting_user.delete_all_user_sessions()
                # create and invite new user
                instance.staff_user_create()
                instance.staff_user_invite()
            else:
                # update user's cell phone
                cell_phone = validated_data.get('staff_cell_phone')
                if cell_phone:
                    preexisting_user.cell_phone = cell_phone
                    preexisting_user.save(update_fields=['cell_phone'])
                # delete user's sessions
                if instance.staff_access_level != previous_access_level:
                    preexisting_user.delete_all_user_sessions()
                if send_invitation:
                    instance.staff_user_invite()


class FCResourceCreateUpdateSerializer(ResourceCreateUpdateSerializer):
    @transaction.atomic
    def update(self, instance, validated_data):
        if instance.type == Resource.STAFF:
            new_access_level = validated_data['staff_access_level']
            if instance.staff_access_level != new_access_level:
                JETAdapter.user_rights_management_event(
                    business_id=instance.business_id,
                    resource_id=instance.id,
                    new_access_level=new_access_level,
                    operator_id=self.context.get('user').id,
                )
        return super().update(instance, validated_data)


class ReadonlyDynamicMixin:
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._dynamic_readable_fields = []

    @property
    def _readable_fields(self):
        # pylint: disable=use-yield-from
        readable_fields = self._dynamic_readable_fields
        if readable_fields:
            yield from (field for field in readable_fields)
            return
        return super()._readable_fields


class SerializerBusinessMixin:
    """
    Mixin for serializer which needs business instance

    Dynamically set context business from current instance.

    In this way the business is available for all child serializers/fields
    and serializer is many=True safe
    """

    def get_timezone_name(self, _obj):
        # injected in Region.gettz()
        return self.timezone._long_name  # pylint: disable=protected-access

    def to_representation(self, instance):
        if 'business' not in self.context:
            self._set_instance_business(instance)

        return super().to_representation(instance)

    def run_validation(self, data):
        if 'business' not in self.context:
            if self.instance is not None:
                self._set_instance_business(self.instance)
            else:
                self._set_instance_business(data)

        return super().run_validation(data)

    def _set_instance_business(self, data):
        business = self._get_attribute(data, ['business'])
        if not business:
            appointment = self._get_attribute(data, ['appointment'])
            if appointment:
                business = appointment.business
        if (
            not business
            and 'service_variant_id' in self.fields
            and hasattr(self, 'initial_data')
            and 'service_variant_id' in self.initial_data
        ):
            try:
                service_variant = self.fields['service_variant_id'].to_internal_value(
                    self.initial_data.get('service_variant_id')
                )
            except serializers.ValidationError:
                service_variant = None
            if service_variant:
                business = service_variant.service.business

        if business:
            self.context['instance_business'] = business

    @staticmethod
    def _get_attribute(data, attr, default=None):
        try:
            return get_attribute(data, attr)
        except (AttributeError, KeyError):
            return default

    @property
    def business(self):
        business = self.context.get('business', self.context.get('instance_business'))
        if not business:
            raise serializers.ValidationError(_('Business is required'))

        return business

    @property
    def timezone(self):
        return self.business.get_timezone()


class BusinessInReviewSerializer(serializers.ModelSerializer):
    """Serializer for BizIdNameAndThumb swagger model"""

    class Meta:
        model = Business
        fields = ('id', 'name', 'reviews_count', 'reviews_stars', 'url')

    url = serializers.SerializerMethodField()

    @staticmethod
    def get_url(instance):
        return instance.get_marketplace_sitemap_url()

    def to_representation(self, instance: Business):
        ret = super().to_representation(instance)
        logo = instance.get_photo(ImageTypeEnum.LOGO)
        if logo:
            ret['thumbnail_photo'] = logo

        ret['full_address'] = ', '.join(
            [
                _f
                for _f in [
                    instance.address,
                    ' '.join(_f for _f in [instance.zip, instance.city_or_region_city] if _f),
                ]
                if _f
            ]
        )
        return ret


class VoucherStatusSerializer(serializers.ModelSerializer):
    class Meta:
        model = Voucher
        fields = ('status',)


class ComboChildSerializer(serializers.ModelSerializer):
    duration = DurationField()
    service_color = serializers.IntegerField(source='service.get_color', read_only=True)
    service_name = serializers.CharField(source='service.name', read_only=True)

    class Meta:
        model = ServiceVariant
        fields = (
            'id',
            'duration',
            'price',
            'type',
            'service_color',
            'service_name',
            'label',
        )


class ComboChildThroughSerializer(serializers.ModelSerializer):
    service_variant = ComboChildSerializer(source='child')
    gap_time = DurationField(required=False)
    price = serializers.DecimalField(
        source='service_price.value',
        allow_null=True,
        decimal_places=2,
        max_digits=10,
        read_only=True,
    )
    type = serializers.ChoiceField(
        source='service_price.price_type',
        choices=PriceType.choices(),
        read_only=True,
    )
    service_price = ServicePriceField()

    class Meta:
        model = ComboMembership
        fields = ('service_variant', 'gap_time', 'price', 'type', 'service_price')


class ServiceVariantSerializer(
    DynamicFieldsSerializerMixin,
    serializers.ModelSerializer,
):
    duration = DurationField(source='service_duration')
    time_slot_interval = DurationField()
    gap_hole_duration = DurationField(required=False)
    gap_hole_start_after = DurationField(required=False)
    no_show_protection = NoShowProtectionServiceVariantField(
        source='payment',
        required=False,
    )
    formula = serializers.SerializerMethodField()
    label = serializers.CharField(
        allow_null=True,
        required=False,
    )
    combo_children = ComboChildThroughSerializer(
        many=True, required=False, source='combo_children_through'
    )
    price = serializers.DecimalField(
        source='service_price.value',
        allow_null=True,
        decimal_places=2,
        max_digits=10,
        read_only=True,
    )
    type = serializers.ChoiceField(
        source='service_price.price_type',
        choices=PriceType.choices(),
        read_only=True,
    )
    service_price = ServicePriceField()
    staffers = serializers.SerializerMethodField()

    class Meta:
        model = ServiceVariant
        fields = (
            'id',
            'type',
            'price',
            'duration',
            'time_slot_interval',
            'gap_hole_start_after',
            'gap_hole_duration',
            'no_show_protection',
            'formula',
            'label',
            'combo_children',
            'combo_pricing',
            'service_price',
            'staffers',
        )

    def get_formula(self, instance):
        if 'formula_objs_cache' in self.context:
            formula_rows = self.context['formula_objs_cache'].get(instance.id)
            rows_serializer = FastWarehouseRecipesRowSerializer(
                instance=formula_rows,
                many=True,
            )
        else:
            formula_rows = WarehouseFormula.get_formula_rows(
                service_variant_id=instance.id,
            )
            if not formula_rows:
                return []
            rows_serializer = WarehouseRecipesRowSerializer(
                instance=formula_rows,
                many=True,
            )
        return rows_serializer.data

    @staticmethod
    def get_staffers(instance) -> t.List[int]:
        if instance.service.is_combo:
            return list(
                set(
                    chain(
                        *[
                            through.child.active_staffers_ids
                            for through in instance.combo_children_through.all()
                        ]
                    )
                )
            )

        return instance.active_staffers_ids


class FullServiceVariantSerializer(serializers.ModelSerializer):
    """
    Serializes whole ServiceVariant object, with all of its fields.
    This serializer could be used when saving whole object's structure info,
    e.g. within ServiceVariantChangelog.create_entry() method.
    """

    class Meta:
        model = ServiceVariant
        fields = '__all__'

    service_name = serializers.CharField(source='service.name')
    service_category_name = serializers.CharField(source='service.service_category.name')
    service_tax_rate = serializers.FloatField(source='service.tax_rate')
    service_padding_type = serializers.CharField(source='service.padding_type')
    service_is_online_service = serializers.BooleanField(source='service.is_online_service')
    service_is_traveling_service = serializers.BooleanField(source='service.is_traveling_service')
    service_is_available_for_customer_booking = serializers.BooleanField(
        source='service.is_available_for_customer_booking',
    )
    service_parallel_clients = serializers.IntegerField(source='service.parallel_clients')
    service_note = serializers.CharField(source='service.note')
    service_description = serializers.CharField(source='service.description')
    service_padding_time = serializers.SerializerMethodField()
    service_gap_time = serializers.SerializerMethodField()
    service_questions = serializers.ListField(
        child=serializers.CharField(),
        source='service.questions',
    )
    service_resources = serializers.PrimaryKeyRelatedField(
        many=True,
        source='service.active_resources',
        read_only=True,
    )
    service_color = serializers.IntegerField(source='service.color')
    service_combo_type = serializers.CharField(source='service.combo_type')
    payment_amount = serializers.FloatField(source='active_payment.payment_amount')
    payment_type = serializers.CharField(source='active_payment.payment_type')
    payment_saving_type = serializers.CharField(source='active_payment.saving_type')
    duration = DurationField(source='service_duration')
    time_slot_interval = DurationField()
    gap_hole_duration = DurationField(required=False)
    gap_hole_start_after = DurationField(required=False)
    custom_combo_price = serializers.SerializerMethodField()

    @staticmethod
    def get_service_padding_time(instance):
        return relativedelta_total_minutes(instance.service.padding_time)

    @staticmethod
    def get_service_gap_time(instance):
        return relativedelta_total_minutes(instance.service.gap_time)

    @staticmethod
    def get_custom_combo_price(instance):
        if instance.combo_pricing == ComboPricing.CUSTOM and instance.service_price.should_display:
            return str(instance.service_price.decimal)
        return None


class ServiceVariantSimpleSerializer(serializers.ModelSerializer):
    duration = DurationField()
    price = serializers.DecimalField(
        max_digits=10,
        decimal_places=2,
        allow_null=True,
    )
    formatted_price = PriceField(source='price')
    service_id = serializers.IntegerField()
    service_name = serializers.CharField(source='service.name')
    service_color = serializers.IntegerField(source='service.get_color', read_only=True)
    is_combo = serializers.BooleanField(source='service.is_combo')

    class Meta:
        model = ServiceVariant
        fields = (
            'id',
            'duration',
            'price',
            'formatted_price',
            'type',
            'service_id',
            'service_color',
            'service_name',
            'is_combo',
            'label',
        )


class ServiceSerializer(PartnerAppDataMixin, serializers.ModelSerializer):
    padding_time = DurationField()
    gap_time = DurationField()
    note_to_customer = serializers.CharField(max_length=1500, source='note')
    variants = ServiceVariantSerializer(many=True, source='active_variants')
    parallel_clients = serializers.IntegerField(required=False)
    color = serializers.IntegerField(source='get_color', read_only=True)
    resources = serializers.SerializerMethodField()
    questions = serializers.ListField(child=serializers.CharField(), required=False)
    default_questions = serializers.SerializerMethodField()
    photos = ServicePhotoSerializer(many=True)
    is_available_for_customer_booking = serializers.BooleanField(required=False)
    is_online_service = serializers.BooleanField(required=False)
    is_traveling_service = serializers.SerializerMethodField()
    no_show_protection = NoShowProtectionServiceField(
        source='*',
        required=False,
    )
    treatment = serializers.SerializerMethodField()
    is_treatment_selected_by_user = serializers.BooleanField()
    treatment_name = serializers.SerializerMethodField()
    service_category_id = serializers.IntegerField(required=False)
    service_price = ServicePriceField()
    combo_parents = serializers.SerializerMethodField()

    class Meta:
        model = Service
        fields = (
            'id',
            'name',
            'order',
            'description',
            'description_type',
            'padding_type',
            'padding_time',
            'gap_time',
            'note_to_customer',
            'resources',
            'tax_rate',
            'parallel_clients',
            'wordcloud',
            'color',
            'variants',
            'questions',
            'default_questions',
            'is_available_for_customer_booking',
            'is_online_service',
            'is_traveling_service',
            'service_code',
            'photos',
            'no_show_protection',
            'treatment',
            'is_treatment_selected_by_user',
            'treatment_name',
            'service_category_id',
            'combo_type',
            'service_price',
            'combo_parents',
            'partner_app_data',
        )

    def to_representation(self, instance):
        if 'formula_objs_cache' not in self.context:
            self.context['formula_objs_cache'] = self._get_context_formula_objs(instance)
        return super().to_representation(instance)

    @classmethod
    def get_prefetches(cls):
        return [
            'photos__photo',
            Prefetch(
                'resources',
                queryset=Resource.objects.filter(
                    active=True,
                    deleted__isnull=True,
                    type=ResourceType.APPLIANCE,
                ).only('id'),
                to_attr='active_appliances',
            ),
            Prefetch(
                'service_variants',
                queryset=ServiceVariant.objects.filter(
                    active=True,
                )
                .select_related('payment')
                .select_related('service')
                .prefetch_related(
                    Prefetch(
                        'combo_children_through',
                        queryset=ComboMembership.objects.all(),
                    ),
                    Prefetch(
                        'resources',
                        queryset=Resource.objects.filter(
                            active=True,
                            type=ResourceType.STAFF,
                        ),
                        to_attr='active_staffers',  # overwrite cached_property
                    ),
                )
                .order_by('duration'),
                to_attr='active_variants',  # overwrite cached_property
            ),
        ]

    def get_default_questions(self, obj):  # pylint: disable=unused-argument
        return self.context.get('default_questions') or []

    @staticmethod
    def get_is_traveling_service(obj):
        if hasattr(obj, '_is_traveling_service'):
            return obj._is_traveling_service  # pylint: disable=protected-access

        if not obj.is_combo:
            return obj.is_traveling_service

        return ComboMembership.objects.filter(
            combo__service_id=obj.id,
            child__service__is_traveling_service=True,
        ).exists()

    @staticmethod
    def _get_context_formula_objs(service):
        from webapps.business.utils import get_sv_to_formula_rows_map

        formulas = WarehouseFormula.objects.filter(
            service_variants__service=service,
        )
        return get_sv_to_formula_rows_map(formulas, service.business)

    @staticmethod
    def get_combo_parents(service):
        if hasattr(service, '_combo_parents_count'):
            return service._combo_parents_count  # pylint: disable=protected-access

        return service.get_related_combo_services().count()

    @staticmethod
    def get_resources(service):
        if service.is_combo:
            appliances_ids = {
                appliance.id
                for service_variant in service.active_variants
                for through in service_variant.combo_children_through.all()
                for appliance in through.child.service.active_appliances
            }
            staffers_ids = {
                staffer.id
                for service_variant in service.active_variants
                for through in service_variant.combo_children_through.all()
                for staffer in through.child.active_staffers
            }
            return list(appliances_ids) + list(staffers_ids)

        appliances_ids = [appliance.id for appliance in service.active_appliances]
        staffers_ids = {*chain(*[v.active_staffers_ids for v in service.active_variants])}
        return appliances_ids + list(staffers_ids)

    @staticmethod
    def get_treatment(obj):
        return None if not obj.is_treatment_selected_by_user else obj.treatment_id

    @staticmethod
    def get_treatment_name(obj):
        if obj.is_treatment_selected_by_user and obj.treatment_id is None:
            return NOT_ASSIGNED
        return obj.treatment.name if obj.treatment and obj.is_treatment_selected_by_user else None


class ServiceSimpleSerializer(serializers.ModelSerializer):
    variants = ServiceVariantSimpleSerializer(many=True, source='active_variants')
    color = serializers.IntegerField(source='get_color', read_only=True)

    class Meta:
        model = Service
        fields = (
            'id',
            'name',
            'color',
            'variants',
            'is_combo',
        )

    @classmethod
    def get_prefetches(cls):
        return [
            Prefetch(
                'service_variants',
                queryset=ServiceVariant.objects.filter(
                    active=True,
                )
                .select_related('payment')
                .order_by('duration'),
                to_attr='active_variants',
            ),
        ]


class ServiceCategorySerializer(serializers.ModelSerializer):
    services = ServiceSerializer(many=True, source='active_services')

    class Meta:
        model = ServiceCategory
        fields = (
            'id',
            'name',
            'order',
            'show_first',
            'services',
        )

    def to_representation(self, instance):
        if 'category_payments_objs_cache' not in self.context:
            self.context['category_payments_objs_cache'] = self._get_context_category_payments_objs(
                instance
            )
        if 'formula_objs_cache' not in self.context:
            self.context['formula_objs_cache'] = self._get_context_formula_objs(instance)
        return super().to_representation(instance)

    def _get_context_category_payments_objs(self, instance):
        service_to_service_variants_map = self._get_service_to_service_variants_map(instance)
        if not service_to_service_variants_map:
            return {}
        service_variant_to_service_payment_map = {}
        for map_record in service_to_service_variants_map:
            service_id = map_record['id']
            if service_id in service_variant_to_service_payment_map:
                continue
            service_variant_id = map_record['service_variants']
            if not service_variant_id:
                continue
            service_variant_to_service_payment_map[service_variant_id] = service_id
        # unique service variant payments per service because from
        # B30 all service variants have same no show protection
        service_variant_payments = ServiceVariantPayment.objects.filter(
            service_variant__in=service_variant_to_service_payment_map.keys(),
        )
        cache_value = {}
        for payment in service_variant_payments:
            service_id = service_variant_to_service_payment_map[payment.service_variant_id]
            cache_value[service_id] = payment
        return cache_value

    def _get_service_to_service_variants_map(self, instance):
        if isinstance(self.parent, ListSerializer):
            return Service.objects.filter(
                service_category__in=self.instance,
                service_variants__payment__isnull=False,
                service_variants__active=True,
            ).values('id', 'service_variants')
        return instance.services.filter(
            service_variants__payment__isnull=False,
            service_variants__active=True,
        ).values(
            'id',
            'service_variants',
        )

    @staticmethod
    def _get_context_formula_objs(instance):
        from webapps.business.utils import get_sv_to_formula_rows_map

        category_formulas = WarehouseFormula.objects.filter(
            service_variants__service__service_category=instance,
        ).prefetch_related('service_variants')
        return get_sv_to_formula_rows_map(category_formulas, instance.business)


class ServiceSuggestionSerializer(serializers.ModelSerializer):
    class Meta:
        model = ServiceSuggestion
        fields = (
            'name',
            'duration',
            'price',
            'tax_rate',
            'treatment',
        )

    duration = DurationField(allow_null=True)

    def to_representation(self, instance):
        data = super().to_representation(instance)
        if data['duration'] == 0:
            data['duration'] = None

        return data


class AppsFlyerSerializer(serializers.ModelSerializer):
    class Meta:
        model = AppsFlyer
        fields = (
            'appsflyer_device_id',
            'advertising_id',
        )


class ComboChildSerializerWriter(serializers.Serializer):
    id = serializers.PrimaryKeyRelatedField(
        queryset=ServiceVariant.objects.filter(
            active=True,
            service__active=True,
            service__combo_type__isnull=True,
        ),
    )


class ComboChildThroughSerializerWriter(serializers.ModelSerializer):
    service_variant = ComboChildSerializerWriter(source='child')
    gap_time = DurationField(required=False)

    class Meta:
        model = ComboMembership
        fields = ('service_variant', 'gap_time', 'type', 'price')

    def validate(self, attrs):
        return ComboMembership(
            gap_time=attrs.get('gap_time', relativedelta()),
            child=attrs['child']['id'],
            type=attrs.get('type'),
            price=attrs.get('price'),
        )


class ServiceVariantSerializerWriter(serializers.ModelSerializer):
    id = serializers.IntegerField(required=False)  # for reuse
    duration = DurationField(required=False)
    time_slot_interval = DurationField(required=False)
    gap_hole_duration = DurationField(required=False)
    gap_hole_start_after = DurationField(required=False)
    type = serializers.ChoiceField(required=True, allow_null=True, choices=PriceType.choices())
    no_show_protection = NoShowProtectionServiceVariantField(
        source='payment',
        required=False,
        allow_null=True,
    )
    # overwrite price from modelserializer
    # and set max digits to 8
    price = serializers.DecimalField(
        max_digits=8, decimal_places=2, allow_null=True, required=False
    )
    formula = WarehouseRecipesRowSerializer(
        required=False,
        allow_null=True,
        many=True,
    )
    label = serializers.CharField(
        allow_null=True,
        allow_blank=True,
        required=False,
        default='',
        max_length=128,
        error_messages={
            'max_length': _(
                'Service variant description can contain up to 128 characters.',
            ),
        },
    )
    combo_pricing = serializers.ChoiceField(
        required=False,
        allow_null=True,
        choices=ComboPricing.choices(),
    )
    combo_children = ComboChildThroughSerializerWriter(
        many=True,
        required=False,
        source='combo_children_through',
    )
    service_price = serializers.CharField(read_only=True)
    staffers = serializers.PrimaryKeyRelatedField(
        many=True,
        required=False,
        queryset=Resource.objects.filter(active=True, type=Resource.STAFF),
    )

    class Meta:
        model = ServiceVariant
        fields = (
            'id',
            'type',
            'price',
            'duration',
            'time_slot_interval',
            'gap_hole_start_after',
            'gap_hole_duration',
            'no_show_protection',
            'formula',
            'label',
            'combo_children',
            'combo_pricing',
            'service_price',
            'staffers',
        )

    def get_fields(self):
        if isinstance(self.instance, QuerySet):
            return super().get_fields()

        is_combo = self.context.get(
            'is_combo', get_attribute(self.instance, ['service', 'is_combo']) or False
        )

        fields = super().get_fields()
        fields['type'].required = not is_combo

        if is_combo:
            del fields['price']
            del fields['type']
            del fields['duration']
            del fields['no_show_protection']
        else:
            if 'combo_pricing' in fields:
                del fields['combo_pricing']

            if 'combo_children' in fields:
                del fields['combo_children']

        return fields

    @staticmethod
    def validate_combo_children(value):
        if len(value) < 2:
            raise ValidationError(_('Add at least two services'))
        return value

    @staticmethod
    def validate_id(value):
        # Treat id=0 as empty value
        return value or None

    def validate(self, attrs):
        data = super().validate(attrs)
        gap_hole_duration = data.get('gap_hole_duration', None)
        gap_hole_start_after = data.get('gap_hole_start_after', None)
        price = data.get('price')
        price_type = data.get('type')
        duration = data.get('duration') or relativedelta()
        combo_pricing = data.get('combo_pricing')
        combo_children_through = data.get('combo_children_through') or []
        no_show_protection = data.get('payment')

        if not gap_hole_duration:
            data['gap_hole_duration'] = relativedelta()
        if not gap_hole_start_after:
            data['gap_hole_start_after'] = relativedelta()

        if len(list(filter(bool, (gap_hole_duration, gap_hole_start_after)))) == 1:
            raise serializers.ValidationError(
                {
                    'gap_hole_duration' if gap_hole_duration else 'gap_hole_start_after': _(
                        'Invalid values for processing time during the service'
                    )
                }
            )

        if (
            gap_hole_duration
            and gap_hole_start_after
            and cmp_relativedeltas(
                duration,
                gap_hole_start_after + gap_hole_duration,
            )
            != 1
        ):
            raise serializers.ValidationError(
                {
                    'duration': _(
                        'Duration of the service cannot be shorter '
                        'than total processing time during the service.'
                    ),
                }
            )

        time_slot_interval = data.get('time_slot_interval', None)
        if not time_slot_interval:
            data['time_slot_interval'] = relativedelta(
                minutes=settings.SERVICE_VARIANT_DEFAULT_INTERVAL,
            )

        if combo_pricing:
            no_show_protection_field = NoShowProtectionServiceVariantField()
            for combo_membership in combo_children_through:
                if combo_pricing != ComboPricing.CUSTOM:
                    combo_membership.price = None
                    combo_membership.type = None
                    continue

                try:
                    no_show_protection = no_show_protection_field.to_representation(
                        combo_membership.child.payment
                    )
                except ServiceVariantPayment.DoesNotExist:
                    pass
                else:
                    validate_and_convert_percent_to_amount(
                        no_show_protection,
                        combo_membership.price,
                        validate_only=True,
                    )
        else:
            price = self.get_price_validated_with_type(price, price_type)
            data['price'] = price

            validate_and_convert_percent_to_amount(
                no_show_protection,
                price,
            )

        return data

    @staticmethod
    def get_price_validated_with_type(price, type_):
        empty_values = (None, '')

        if type_ in PriceType.has_price():
            if price in empty_values:
                raise serializers.ValidationError({'price': _('Price is required')})
            if price <= 0:
                raise serializers.ValidationError({'price': _('Invalid price')})
        else:
            if price not in empty_values:
                raise serializers.ValidationError({'price': _('Price is not allowed')})
            price = None
        return price

    def validate_no_show_protection(self, value):
        if value and not self.context.get('pos_pay_by_app_enabled'):
            raise serializers.ValidationError(
                _('No show protection fee can\'t be set, because pay by app payment disabled.')
            )
        return value

    @staticmethod
    def validate_duration(value):
        if not value or cmp_relativedeltas(value, relativedelta(minutes=5)) < 0:
            raise serializers.ValidationError(
                _('Service duration can not be shorter than 5 minutes.')
            )
        return value

    @staticmethod
    def validate_label(value):
        """Historically we allowed both nulls and empty strings as label.
        Some service variants have nulls and some have empty strings and
        they therefore compare unequal which is unintended during validation
        """
        if value is None:
            return ''
        return value


class PhotosSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    url = serializers.URLField(allow_null=True)


class ServiceResourceSerializer(serializers.PrimaryKeyRelatedField):
    def get_queryset(self):
        business = self.context['business']
        if not business:
            return Resource.objects.none()

        return Resource.objects.filter(business=business)


class NoShowProtectionMixin:
    @staticmethod
    def get_service_no_show_protection(
        request_variants: t.Sequence[dict],
        existing_variants: t.Optional[QuerySet] = None,
    ):
        if existing_variants is None:
            existing_variants = ServiceVariant.objects.none()

        changed_variants = ServiceSerializerWriter.get_changed_variants(
            request_variants,
            existing_variants,
        )
        service_no_show_protection = None

        highest_percent = 0
        protection_changed = bool(changed_variants)
        for variant in changed_variants:
            no_show_protection = variant.pop('payment', None) or variant.pop(
                'no_show_protection', None
            )
            price = variant.get('price')
            if not (no_show_protection and price):
                continue
            price = Decimal(price)
            percent = 100 * round_decimal(
                (no_show_protection.get('payment_amount') or 0) / price, 2
            )
            if percent > highest_percent:
                highest_percent = percent
                service_no_show_protection = {
                    'payment_type': no_show_protection.get('payment_type'),
                    'percentage': highest_percent,
                }
        return service_no_show_protection, protection_changed

    @staticmethod
    def get_changed_variants(
        request_variants: t.Sequence[dict],
        existing_variants: QuerySet,
    ):
        changed_variants = []
        for variant in request_variants:
            duration = variant.get('duration', 0)
            if isinstance(duration, (int, float)):
                duration = relativedelta(minutes=duration)
            existing_variant = existing_variants.filter(
                price=variant.get('price'),
                duration=duration,
            ).first()
            if not existing_variant:
                changed_variants.append(variant)
                continue
            request_payment_amount = sget(variant, ['payment', 'payment_amount']) or sget(
                variant, ['no_show_protection', 'payment_amount']
            )
            existing_payment_amount = sget(existing_variant, ['payment', 'payment_amount'])
            if request_payment_amount != existing_payment_amount:
                changed_variants.append(variant)
        return changed_variants


class ServiceSerializerWriter(
    NoShowProtectionMixin,
    serializers.ModelSerializer,
):
    padding_time = DurationField(required=False)
    gap_time = DurationField(required=False)
    note_to_customer = serializers.CharField(
        source='note',
        max_length=1500,
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    resources = ServiceResourceSerializer(many=True, required=False)
    variants = ServiceVariantSerializerWriter(many=True, required=True)
    parallel_clients = serializers.IntegerField(required=False)
    color = serializers.IntegerField(required=False, allow_null=True)
    questions = serializers.ListField(child=serializers.CharField(max_length=256), required=False)
    is_available_for_customer_booking = serializers.BooleanField(required=False)
    is_online_service = serializers.BooleanField(required=False)
    no_show_protection = NoShowProtectionServiceField(
        source='payment',
        required=False,
        allow_null=True,
    )
    treatment = serializers.IntegerField(
        source='treatment_id',
        required=False,
        allow_null=True,
    )
    is_treatment_selected_by_user = serializers.BooleanField(required=False)
    tax_rate = serializers.DecimalField(
        decimal_places=2,
        max_digits=5,
        required=False,
        allow_null=True,
    )

    class Meta:
        model = Service
        fields = (
            'id',
            'name',
            'order',
            'description',
            'description_type',
            'padding_type',
            'padding_time',
            'gap_time',
            'note_to_customer',
            'resources',
            'tax_rate',
            'parallel_clients',
            'wordcloud',
            'color',
            'variants',
            'service_category',
            'questions',
            'is_available_for_customer_booking',
            'is_online_service',
            'is_traveling_service',
            'service_code',
            'no_show_protection',
            'treatment',
            'is_treatment_selected_by_user',
            'combo_type',
        )

    @property
    def business(self) -> Business:
        return self.context['business']

    @classmethod
    def get_prefetches(cls):
        return [
            'resources',
            Prefetch(
                'service_variants',
                queryset=ServiceVariant.objects.filter(
                    active=True,
                ).order_by('duration'),
                to_attr='active_variants',  # overwrite cached_property
            ),
        ]

    def _validate_formula_row(self, formula_row, variant_id):
        formula_row_id = formula_row.get('id')
        if not self.instance or not variant_id or not formula_row_id:
            raise serializers.ValidationError(_('Invalid commodity in service variant formula'))
        prev_formula_row = WarehouseFormulaRow.objects.filter(
            id=formula_row_id,
            formulas__service_variants__id=variant_id,
            formulas__service_variants__active=True,
            formulas__service_variants__service__id=self.instance.id,
        ).first()
        if (
            not prev_formula_row
            or (prev_formula_row.commodity.id != formula_row['commodity'].id)
            or (prev_formula_row.count != formula_row['count'])
            or (prev_formula_row.warehouse.id != formula_row['warehouse'].id)
        ):
            raise serializers.ValidationError(_('Invalid commodity in service variant formula'))

    def validate_is_online_service(self, is_online_service):
        if is_online_service and self.business.is_traveling_only:
            raise serializers.ValidationError(
                _("Business Traveling to Clients cannot have online services")
            )

        zoom_credentials_exists = has_zoom_credentials(self.business)
        if is_online_service and not zoom_credentials_exists:
            raise serializers.ValidationError(
                _(
                    "Can't mark service as online one. "
                    "You don't have Zoom account configured. "
                    "Please contact our support."
                )
            )
        return is_online_service

    def validate_is_traveling_service(self, is_traveling_service):
        if is_traveling_service:
            if not self.business.is_traveling:
                raise serializers.ValidationError(
                    _('Traveling to clients disabled'),
                    code='traveling_disabled',
                )
        else:
            if self.business.is_traveling_only:
                raise serializers.ValidationError(
                    _('Services without traveling to client disabled'), code='traveling_only'
                )

        if (
            self.instance is not None
            and self.instance.is_traveling_service != is_traveling_service
            and self.instance.get_related_combo_services().exists()
        ):
            raise serializers.ValidationError(
                _(
                    "Unable to change the service type to mobile. "
                    "It’s assigned to at least one combo service."
                ),
                code='traveling_combo_disabled',
            )

        return is_traveling_service

    def _get_default_tax_rate(self):
        pos = self.context['pos']
        if pos:
            return (
                pos.tax_rates.filter(
                    default_for_service=True,
                )
                .values_list('rate', flat=True)
                .first()
            )

        return None

    @staticmethod
    def _service_variant_unique_key(variant):
        duration = safe_get(variant, ['duration'])

        return (
            relativedelta_total_seconds(duration) if duration else None,
            str(safe_get(variant, ['type'])),
            safe_get(variant, ['label']) or '',
        )

    def validate_variants(self, variants):
        # pylint: disable=too-many-branches
        if not variants:
            raise serializers.ValidationError(
                _('At least one service variant is required'),
                code='required',
            )

        # check for duplicates
        counter = Counter(self._service_variant_unique_key(variant) for variant in variants)
        if any(count > 1 for count in counter.values()):
            raise serializers.ValidationError(
                _('Duplicated duration, type and description in service variant'),
                code='duplicated',
            )

        # preserve original ids
        if self.instance:
            existing_variants_by_unique_key = {
                self._service_variant_unique_key(variant): variant
                for variant in self.instance.active_variants
            }
            existing_variant_ids = {variant.id for variant in self.instance.active_variants}
            for variant_data in variants:
                unique_key = self._service_variant_unique_key(variant_data)
                if unique_key in existing_variants_by_unique_key:
                    variant_data['id'] = existing_variants_by_unique_key[unique_key].id
                elif variant_data.get('id') not in existing_variant_ids:
                    variant_data['id'] = None

        # check archived commodities in formulas
        for variant in variants:
            formula = variant.get('formula')
            if formula:
                for formula_row in formula:
                    if formula_row['commodity'].archived:
                        self._validate_formula_row(formula_row, variant.get('id'))

        # check for combo connections
        if self.instance is not None and not self.context.get('skip_combo_validation'):
            current_variant_ids = set(self.instance.active_variants.values_list('id', flat=True))
            new_variant_ids = {variant['id'] for variant in variants if 'id' in variant}
            variant_ids_to_delete = current_variant_ids - new_variant_ids
            for variant in ServiceVariant.objects.filter(id__in=variant_ids_to_delete):
                if variant.get_related_combo_services().exists():
                    raise serializers.ValidationError(
                        _('Deleting combo variants without skip_combo_validation is prohibited'),
                        code='variant_existing_dependent_combo',
                    )

        return variants

    def run_validation(self, data=empty):
        if isinstance(data, dict):
            combo_type = data.get('combo_type')
            combo_type_field = self.fields['combo_type']
            variants_field = self.fields['variants']
            try:
                variants_field.context['is_combo'] = bool(
                    combo_type and combo_type_field.to_internal_value(combo_type)
                )
            except ValidationError:
                pass

        return super().run_validation(data)

    def _validate_pp_with_packages(
        self,
        service_payment_data: t.Optional[dict],
        variants_data: t.Sequence[dict],
        service_name: str,
    ):
        if (
            service_payment_data is None
            or service_payment_data['payment_type'] != NoShowProtectionType.PREPAYMENT
        ):
            return
        for variant_data in variants_data:
            if validate_package_with_prepayment(sget_v2(variant_data, ['id'])):
                raise serializers.ValidationError(
                    _(
                        "Deposit can't be set, because {service_name} "
                        + "is a part of an existing package."
                    ).format(service_name=service_name),
                    code='package_with_pp',
                )

    @tracer.wrap(
        name=DatadogOperationNames.NSP_CRUD, service=DatadogCustomServices.NO_SHOW_PROTECTION
    )
    def _validate_payment(
        self, service_payment_data: t.Optional[dict], variants_data: t.Sequence[dict]
    ) -> None:
        """
        Service doesn't have no-show-protection settings directly, only at service variant level
        """
        use_service_payment = bool(service_payment_data)

        if service_payment_data is None:
            service_payment_data, use_service_payment = self.get_service_no_show_protection(
                variants_data,
                self.instance.active_variants.all() if self.instance else None,
            )

        for variant_data in variants_data:
            variant_payment_data = variant_data.get('payment', None)
            if use_service_payment:
                variant_payment_data = copy.deepcopy(service_payment_data)

            if variant_payment_data:
                variant_price = variant_data.get('price')
                skip_variant_if_no_price = variant_payment_data.pop(
                    'skip_variant_if_no_price', None
                )
                if not (not variant_price and skip_variant_if_no_price):
                    validate_and_convert_percent_to_amount(
                        variant_payment_data,
                        variant_price,
                    )
                else:
                    variant_payment_data.pop('percentage')

            if variant_payment_data and not variant_payment_data.get('payment_amount'):
                variant_payment_data = None

            variant_data['payment'] = variant_payment_data

    def validate(self, attrs):
        data = super().validate(attrs)
        padding_time = data.get('padding_time', None)
        padding_type = data.get('padding_type', None)
        gap_time = data.get('gap_time', None)
        color = data.get('color', None)
        combo_type = data.get('combo_type')
        variants_data = data.get('variants', [])
        resources_data = data.get('resources', [])
        tax_rate = data.get('tax_rate', empty)

        if color and color > len(next(iter(settings.SERVICE_COLOR_PALETTES.values()))):
            raise serializers.ValidationError({'color': _('Invalid color number')})
        data['color'] = color

        if not resources_data and not combo_type:
            raise serializers.ValidationError(
                {'resources': _('At least one staff or resource must be assigned to this service')}
            )

        assigned_appliances = any(res.is_appliance for res in resources_data)

        if not assigned_appliances and not combo_type:
            for variant in variants_data:
                if 'staffers' in variant and not variant['staffers']:
                    raise serializers.ValidationError(
                        {
                            'variants': _(
                                'Each service variant needs to be assigned '
                                'to at least one staffer.'
                            )
                        }
                    )

        if self.instance and gap_time is None:
            data['gap_time'] = self.instance.gap_time

        if self.instance and padding_time is None:
            data['padding_time'] = self.instance.padding_time
        if self.instance and padding_type is None:
            data['padding_type'] = self.instance.padding_type
        if tax_rate is empty:
            data['tax_rate'] = (
                self.instance.tax_rate if self.instance else self._get_default_tax_rate()
            )

        if padding_type and not padding_time:
            raise serializers.ValidationError(
                _('Padding time is required when padding type specified')
            )

        service_payment_data = data.pop('payment', None)
        self._validate_pp_with_packages(service_payment_data, variants_data, data['name'])
        self._validate_payment(service_payment_data, variants_data)
        self._validate_service_type(data)
        self._validate_treatment(data)
        self._validate_is_treatment_selected_by_user(data)
        self._validate_combo(data)
        return data

    def _validate_service_type(self, data):
        if self.context.get('use_service_type'):
            if (
                'treatment_id' not in data
                or 'is_treatment_selected_by_user' not in data
                or not data['is_treatment_selected_by_user']
            ):
                raise serializers.ValidationError({'treatment': _('Service type is required')})
            if (
                data.get('treatment_id') is not None
                and TreatmentCache.get_by_id(data['treatment_id']) is None
            ):
                raise serializers.ValidationError({'treatment': 'Treatment ID does not exist'})

    def _validate_treatment(self, data):
        # backward compatibility
        if (
            not self.context.get('use_service_type', False)
            and 'treatment_id' in data
            and TreatmentCache.get_by_id(data['treatment_id']) is None
        ):
            del data['treatment_id']

    def _validate_is_treatment_selected_by_user(self, data):
        if 'is_treatment_selected_by_user' not in data:
            return

        # corner case: ignore the old mobile app if another user has selected the treatment
        if not self.context.get('use_service_type', False):
            del data['is_treatment_selected_by_user']

    def _validate_combo(self, data):
        """Combo validation

        Most of these errors are not possible if fronted is working correctly
        """
        combo_type = data.get('combo_type')
        if not combo_type:
            return

        if len(data['variants']) > 1:
            raise serializers.ValidationError(_('Service Combo must have single service variant'))

        for variant_data in data['variants']:
            if not variant_data.get('combo_children_through'):
                raise serializers.ValidationError(
                    _('Service Combo must have at least one child service')
                )
            if not variant_data.get('combo_pricing'):
                raise serializers.ValidationError(_('Service Combo must have specified pricing'))

            if combo_type == ComboType.PARALLEL:
                number_of_staffers = self.business.number_of_staffers
                if len(variant_data.get('combo_children_through')) > number_of_staffers:
                    raise serializers.ValidationError(
                        _(
                            'Number of services in Parallel Booking is limited to the number of '
                            'staff members'
                        ),
                        code='not_enough_staffers',
                    )

    @staticmethod
    @tracer.wrap(
        name=DatadogOperationNames.NSP_CRUD, service=DatadogCustomServices.NO_SHOW_PROTECTION
    )
    def _update_payment(service_variant: ServiceVariant, payment_data: t.Optional[dict]) -> None:
        """
        Update no_show_protection fee for given service_variant
        :param service_variant_id: int. valid id of ServiceVariant
        :param payment: dict or None. Keys attributes of ServiceVariantPayment
                If None passed will check if payment exists and deleted if so
        :return: None
        """
        if payment_data:
            ServiceVariantPayment.all_objects.update_or_create(
                service_variant_id=service_variant.id,
                defaults={
                    **payment_data,
                    'deleted': None,
                },
            )
        else:
            ServiceVariantPayment.objects.filter(service_variant_id=service_variant.id).update(
                deleted=tznow()
            )

    @staticmethod
    def _update_resources(instance, resources, use_variants_staffers):

        if resources and not instance.is_combo:
            if not use_variants_staffers:
                instance.set_staffers(resources)
            instance.set_appliances(resources)

        try:
            del instance.active_resources
            del instance.active_staffers
        except AttributeError:
            pass

    @staticmethod
    def _update_formula(
        service_variant: ServiceVariant, formula_rows_data: t.Iterable[dict]
    ) -> None:
        formula = WarehouseFormula()
        formula_rows = []
        for formula_row_data in formula_rows_data:
            formula_row_data.pop('id', None)
            formula_row = WarehouseFormulaRow(**formula_row_data)
            formula_row.save()
            formula_rows.append(formula_row)
        formula.save()
        formula.service_variants.add(service_variant)
        formula.rows.set(formula_rows)

        # TODO: it doesn't clear previous formulas

    @staticmethod
    def _update_combo_children_through(
        service_variant: ServiceVariant, combo_children_through: t.Iterable[ComboMembership]
    ) -> None:
        now = tznow()
        update_fields = {
            field.name
            for field in ComboMembership._meta.concrete_fields  # pylint: disable=protected-access
            if field.name not in {'id', 'combo', 'child', 'created'}
        }
        to_create = []
        to_update = []

        existing_memberships = list(service_variant.combo_children_through.all())
        existing_memberships_by_child_id = defaultdict(list)

        # Try to reuse existing instances.
        # There can be several members with same child_id.
        for membership in existing_memberships:
            existing_memberships_by_child_id[membership.child_id].append(membership)

        for order, membership in enumerate(combo_children_through):
            try:
                existing_membership = existing_memberships[membership.child_id].pop(0)
            except (IndexError, KeyError):
                existing_membership = None

            if existing_membership:
                for attr in update_fields:
                    setattr(existing_membership, attr, getattr(membership, attr))
                membership = existing_membership
                to_update.append(membership)
            else:
                to_create.append(membership)

            membership.combo = service_variant
            membership.order = order
            membership.updated = now
            membership.deleted = None

        if to_update:
            ComboMembership.objects.bulk_update(to_update, update_fields)

        if to_create:
            ComboMembership.objects.bulk_create(to_create)

        to_delete_ids = [
            membership.id for membership in existing_memberships if membership not in to_update
        ]
        if to_delete_ids:
            ComboMembership.objects.filter(id__in=to_delete_ids).delete()

    def _update_variants_and_resources(
        self,
        instance: Service,
        variants_data: t.Sequence[dict],
        use_variants_staffers: bool,
        resources: list[Resource],
    ) -> None:
        # pylint: disable=too-many-branches, too-many-statements, too-many-locals

        user = self.context['user']
        superuser = self.context.get('superuser')

        now = tznow()

        existing_variants = list(instance.active_variants.all())
        existing_variant_by_id = {variant.id: variant for variant in existing_variants}
        created_variant_ids = set()
        updated_variant_ids = set()
        deleted_variant_ids = set()
        has_payment = False
        requested_by = superuser if superuser else user

        to_delete_ids = set(existing_variant_by_id.keys()) - {
            data.get('id') for data in variants_data
        }
        for variant_id in to_delete_ids:
            variant = existing_variant_by_id[variant_id]
            for combo_service in variant.get_related_combo_services():
                combo_service.safe_delete()

            variant.valid_till = now
            variant.safe_delete()
            deleted_variant_ids.add(variant.id)

            VoucherTemplateServiceVariant.handle_service_variant_post_save(variant)
            VoucherServiceVariant.handle_service_variant_post_save(variant)

        new_sv_changelog_entries = []
        for variant_data in variants_data:
            variant_id = variant_data.pop('id', None)
            formula_data = variant_data.pop('formula', None)
            combo_children_through = variant_data.pop('combo_children_through', None)
            variant_payment_data = variant_data.pop('payment')
            staffers = variant_data.pop('staffers', [])

            variant = existing_variant_by_id.get(variant_id, None)
            if not variant:
                variant = ServiceVariant(
                    service=instance,
                    valid_from=now,
                    active=True,
                )
                variant_created = True
            else:
                variant_created = False

            for attr, value in variant_data.items():
                setattr(variant, attr, value)

            variant.save()

            if use_variants_staffers:
                variant.set_staffers(staffers)

            if formula_data:
                self._update_formula(variant, formula_data)

            if combo_children_through:
                self._update_combo_children_through(variant, combo_children_through)

            self._update_payment(variant, variant_payment_data)
            has_payment |= bool(variant_payment_data)

            if variant_created:
                created_variant_ids.add(variant.id)
            else:
                updated_variant_ids.add(variant.id)

            new_sv_changelog_entries.append(variant)
            VoucherTemplateServiceVariant.handle_service_variant_post_save(variant)
            VoucherServiceVariant.handle_service_variant_post_save(variant)

        self._update_resources(instance, resources, use_variants_staffers)
        for serv_variant in new_sv_changelog_entries:
            ServiceVariantChangelog.create_entry(requested_by, serv_variant)

        try:
            del instance.active_variants
        except AttributeError:
            pass

        update_to_external_partners(
            EventType.SERVICE,
            business=instance.business,
            service=instance,
            ids_to_create=list(created_variant_ids),
            ids_to_update=list(updated_variant_ids),
            ids_to_delete=list(deleted_variant_ids),
        )
        if has_payment:
            analytics_protection_service_enabled_task.delay(
                business_id=self.business.id,
                context={
                    'business_id': self.business.id,
                },
            )

    @transaction.atomic
    def create(self, validated_data):
        # pylint: disable=used-before-assignment
        variants_data = validated_data.pop('variants')
        resources = validated_data.pop('resources', [])

        instance = Service(business=self.business, **validated_data)
        instance.save()

        use_variants_staffers = any(variant.get('staffers') for variant in variants_data)

        self._update_variants_and_resources(
            instance, variants_data, use_variants_staffers, resources
        )

        return instance

    @retry_on_sync_error
    @transaction.atomic
    def update(self, instance, validated_data):
        # pylint: disable=used-before-assignment
        validated_data = copy.deepcopy(validated_data)  # retry_on_sync_error!
        variants_data = validated_data.pop('variants')
        resources = validated_data.pop('resources', [])

        if instance.business.status == instance.business.Status.SETUP:
            service_variant = instance.service_variants.first()
            if any(
                (
                    validated_data.get('name', instance.name) != instance.name,
                    variants_data[0].get('duration', service_variant.duration)
                    != service_variant.duration,
                    variants_data[0].get('price', service_variant.price) != service_variant.price,
                )
            ):
                instance.is_suggested = False

        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        instance.save()

        staffer_present = any(res.type == ResourceType.STAFF for res in resources)
        use_variants_staffers = (
            any(variant.get('staffers') for variant in variants_data) or not staffer_present
        )

        self._update_variants_and_resources(
            instance, variants_data, use_variants_staffers, resources
        )

        with tracer.trace(
            name=DatadogOperationNames.NSP_CRUD, service=DatadogCustomServices.NO_SHOW_PROTECTION
        ):
            # profile completeness
            step_turn_on_noshow_protection.send(self.business)

        return instance


class UserSerializer(serializers.ModelSerializer):
    photo_url = serializers.URLField(source='customer_profile.photo.full_url')

    class Meta:
        model = User
        fields = (
            'id',
            'first_name',
            'last_name',
            'cell_phone',
            'email',
            'photo_url',
        )

    def to_representation(self, instance):
        ret = super().to_representation(instance)

        parsed = parse_phone_number(ret['cell_phone'])
        ret['cell_phone'] = parsed.global_short
        ret['cell_phone_local'] = parsed.local_short

        return ret


class ChangeDetailsSerializer(serializers.Serializer):
    """DEPRECATED"""

    change_request_text = serializers.CharField(
        max_length=5000,
    )
    renting_venue_id = serializers.IntegerField()

    @staticmethod
    def validate_renting_venue_id(venue_id):
        if not Business.objects.filter(
            id=venue_id,
            status=Business.Status.VENUE,
            active=True,
        ).exists():
            raise serializers.ValidationError(_('Umbrella Venue object does not exists'))
        return venue_id

    def create(self, validated_data):
        return validated_data


class InstagramBusinessSerializer(serializers.Serializer):
    id = serializers.IntegerField(read_only=True)
    name = serializers.CharField()
    latitude = serializers.FloatField()
    longitude = serializers.FloatField()
    address = serializers.CharField()
    phone = serializers.CharField()
    instagram_url = serializers.URLField(source='instagram_link')

    website_url = serializers.SerializerMethodField(read_only=True)
    facebook_url = serializers.SerializerMethodField(read_only=True)
    action_url = serializers.SerializerMethodField(read_only=True)

    @staticmethod
    def get_facebook_url(instance):
        if not instance.facebook_link:
            return ''
        expression = (
            r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\(\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
        )
        ex = re.findall(expression, instance.facebook_link)
        if ex:
            return ex[0]
        return ''

    def get_action_url(self, instance):
        merged_business = self.context.get('merged_businesses', {})
        merged_key = instance.instagram_link
        if merged_key in merged_business:
            business_ids = ','.join([str(b_id) for b_id in merged_business[merged_key]])
        else:
            business_ids = instance.id

        url = urljoin(
            settings.MARKETPLACE_URL,
            f'/{settings.MARKETPLACE_LANG_COUNTRY}/instant-experiences/widget/{business_ids}'
            f'?instant_experiences_enabled=true&ig_ix=true',
        )
        return quote(url)

    def get_website_url(self, instance):
        subdomains = self.context.get('subdomains')
        subdomain = subdomains.get(instance.id)
        if subdomain:
            url = instance.create_seo_subdomain(subdomain)
        else:
            url = instance.get_seo_url()
        return quote(url)

    def to_representation(self, instance):
        result = super().to_representation(instance)
        regions = self.context.get('regions')
        # update location details
        # city, state, zipcode, country
        if regions:
            business_regions = regions.get(instance.id, {})
            if not business_regions:
                # business with missing data
                # do not return any data at all
                # filter it in
                # feeds.instagram.tools
                # InstagramFeedsWrapper.get_serialized_businesses
                return
            result.update(business_regions)
        return result


class BusinessUpdateInstagramLink(serializers.Serializer):
    id = serializers.IntegerField()
    instagram_link = serializers.URLField()


class UpdateIGBusinessLinksSerializer(serializers.Serializer):
    businesses = BusinessUpdateInstagramLink(many=True)
    operator_email = serializers.EmailField()
    country_code = serializers.ChoiceField(choices=list(Country.supported()))


class BusinessAgreementsGetSerializer(serializers.ModelSerializer, BusinessAgreementMixin):
    class Meta:
        model = Business
        fields = ('business_agreements',)

    business_agreements = serializers.SerializerMethodField()

    def get_business_agreements(self, instance):
        return self._get_agreement_from_instance(
            instance=instance, is_mobile=self.context.get('is_mobile')
        )


class BusinessInspectorSerializer(serializers.ModelSerializer):
    class Meta:
        model = BusinessPolicyAgreement
        fields = (
            'business',
            'inspector_first_name',
            'inspector_last_name',
            'inspector_email',
        )

    business = serializers.PrimaryKeyRelatedField(write_only=True, queryset=Business.objects.all())


class BusinessAgreementSerializer(serializers.Serializer):
    name = serializers.ChoiceField(
        choices=list(BUSINESS_AGREEMENTS.keys()),
    )
    value = serializers.BooleanField()


class BusinessAgreementsUpdateSerializer(serializers.Serializer, BusinessAgreementMixin):
    business_agreements = BusinessAgreementSerializer(many=True)

    def validate(self, attrs):
        data = super().validate(attrs)
        return self._validate_request_update(data)

    def create(self, validated_data):
        business_id = self.context.get('business_id')
        packed_kwargs = validated_data.get('packed_kwargs', {})
        if business_id is None or not packed_kwargs:
            # fallback no user available
            # or nothing to update
            return

        BusinessPolicyAgreement.objects.update_or_create(
            business_id=business_id,
            defaults=packed_kwargs,
        )

        return validated_data


##################################
### GDPR BUSINESS DATA EXPORT ###
##################################


class BusinessExportSerializer(serializers.ModelSerializer):
    """Used to export businesses' data for their owners' request.
    Field names will become headings in the report sent to the customer,
    ex. "image_urls" -> "Image Urls" """

    regions = serializers.SerializerMethodField()
    categories = serializers.SerializerMethodField()
    treatments = serializers.SerializerMethodField()
    # Business.photos seems to be deprecated, now we use images.Image model lol
    image_urls = serializers.SerializerMethodField()
    registration_source = serializers.ReadOnlyField(source='registration_source.name')

    # GDPR
    privacy_policy_agreement = serializers.SerializerMethodField()
    marketing_agreement = serializers.SerializerMethodField()
    receiving_messages_consent = serializers.SerializerMethodField()

    @staticmethod
    def get_regions(instance):
        return instance.region.name

    @staticmethod
    def get_categories(instance):
        return ', '.join([c.name for c in instance.categories.all()])

    @staticmethod
    def get_treatments(instance):
        return ', '.join([t.name for t in instance.treatments.all()])

    @staticmethod
    def get_image_urls(instance):
        return ', '.join([i.full_url for i in instance.images.all()])

    # Agreements
    @staticmethod
    def get_privacy_policy_agreement(instance):
        if not instance.agreement_exist:
            return None
        return instance.agreement.privacy_policy_agreement

    @staticmethod
    def get_marketing_agreement(instance):
        if not instance.agreement_exist:
            return None
        return instance.agreement.marketing_agreement

    @staticmethod
    def get_receiving_messages_consent(instance):
        if not instance.agreement_exist:
            return None
        return instance.agreement.receiving_messages_consent

    class Meta:
        model = Business
        fields = (
            'regions',
            'categories',
            'treatments',
            'image_urls',
            'registration_source',
            'name',
            'name_short',
            'official_name',
            'umbrella_brand_name',
            'address',
            'address2',
            'city',
            'phone',
            'alert_phone',
            'longitude',
            'latitude',
            'website',
            'facebook_link',
            'instagram_link',
            'description',
            'contractor_description',
            'parking',
            'wheelchair_access',
            'opening_hours_note',
            'invoice_address',
            'invoice_email',
            'privacy_policy_agreement',
            'marketing_agreement',
            'receiving_messages_consent',
        )


class CoordinateSerializer(serializers.Serializer):
    default_error_messages = {
        'lat_out_of_range': 'Latitude must be between -90. and 90.',
        'lng_out_of_range': 'Longitude must be between -180. and 180.',
        'both_required': 'Both latitude and longitude are required.',
    }

    latitude = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    longitude = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
    )

    def to_representation(self, instance):
        res = super().to_representation(instance)

        res['latitude'] = instance.latitude or None
        res['longitude'] = instance.longitude or None

        return res

    @staticmethod
    def _to_float(value):
        try:
            value = float(value)
        except (TypeError, ValueError):
            value = None

        return value

    def validate(self, attrs):
        if attrs.get('latitude') in [None, ''] or attrs.get('longitude') in [None, '']:
            attrs.pop('latitude', None)
            attrs.pop('longitude', None)

        return attrs

    def validate_latitude(self, value):
        return self._to_float(value)

    def validate_longitude(self, value):
        return self._to_float(value)


class LocationSerializer(serializers.Serializer):
    default_error_messages = {
        'invalid_zipcode': _("Zip code is not valid."),
    }

    zipcode = serializers.CharField(
        required=False,
        allow_null=True,
        allow_blank=True,
    )
    address = serializers.CharField(
        required=False,
        max_length=100,
        allow_blank=True,
        allow_null=True,
    )
    address2 = serializers.CharField(
        required=False,
        max_length=100,
        allow_blank=True,
        allow_null=True,
    )
    address3 = serializers.CharField(
        required=False,
        max_length=100,
        allow_blank=True,
        allow_null=True,
    )
    city = serializers.CharField(
        required=False,
        max_length=100,
        allow_blank=True,
        allow_null=True,
    )
    coordinate = CoordinateSerializer(source="*", required=False)
    time_zone_name = serializers.CharField(read_only=True)

    @staticmethod
    def validate_city(value):
        return value or ''

    @staticmethod
    def validate_zipcode(value):
        return value.upper() if value not in [None, ''] else None

    def validate(self, attrs):
        validated_data = super().validate(attrs)

        if validated_data.get('zipcode') is not None:
            zipcode = validated_data['zipcode']
            latitude = validated_data.get('latitude')
            longitude = validated_data.get('longitude')

            if self.parent and self.parent.instance is not None:
                if latitude is None:
                    latitude = self.parent.instance.latitude

                if longitude is None:
                    longitude = self.parent.instance.longitude

            zipcode_region = Region.objects.find_zipcode(zipcode).first()
            zipcode_regexp = settings.COUNTRY_CONFIG.zipcode_regexp
            region = None

            if zipcode_region is None:
                if not zipcode_regexp or not re.match(zipcode_regexp, zipcode, re.IGNORECASE):
                    raise serializers.ValidationError(
                        {
                            'zipcode': self.error_messages['invalid_zipcode'],
                        },
                        code='invalid_zipcode',
                    )
            else:
                zipcode = zipcode_region.name

            if region is None:  # nosemgrep
                region = zipcode_region

            if region is None:
                from lib.geocoding import here_maps

                result = here_maps.Geocoder().reverse_geocode(latitude, longitude, raw=True)
                result = here_maps.ResolveGeocoder().get_region_by_latlon(
                    result,
                    query=result['address'].get('city') or result['address'].get('county'),
                    filter_types=[Region.Type.CITY, Region.Type.COUNTY],
                )

                if result:
                    region = Region.objects.filter(id=result.id).first()

            validated_data['zipcode'] = zipcode
            validated_data['region'] = region

        return validated_data

    def to_representation(self, instance):
        result = super().to_representation(instance)

        result['zipcode'] = instance.zip
        result['address'] = instance.address or ''
        result['address2'] = instance.address2 or ''
        result['city'] = instance.city or None
        result['state'] = (
            instance.region.get_parent_name_by_type(settings.ES_ADM_1_LVL, attr='abbrev')
            if instance.region
            else None
        ) or None
        result['address3'] = instance.custom_data.get(CustomData.VENUE_CONTRACTOR_ADDRESS_NO) or ''

        return result


class SegmentIdSerializerMixin(serializers.Serializer):
    segment_business_id = serializers.CharField(
        required=False, allow_blank=True, source='integrations_dict.segment_business_id'
    )

    def to_representation(self, instance):
        """Fallback value"""
        data = super().to_representation(instance)
        data['segment_business_id'] = self.get_segment_business_id(instance)
        return data

    @staticmethod
    def get_segment_business_id(instance):
        return instance.integrations.get('segment_business_id') or id_to_external_api(instance.id)


class RentingVenueSerializer(serializers.ModelSerializer):
    class Meta:
        model = Business
        fields = (
            'id',
            'name',
            'location',
            'last_updated',
            'cover_photo',
            'thumbnail_photo',
            'primary_category_id',
        )

    id = serializers.IntegerField(read_only=True)
    name = serializers.CharField()
    location = LocationSerializer(source='*')
    cover_photo = serializers.CharField(read_only=True, source='get_cover_photo_url')
    thumbnail_photo = serializers.SerializerMethodField()
    primary_category_id = serializers.IntegerField(read_only=True)
    last_updated = serializers.SerializerMethodField()

    @staticmethod
    def get_last_updated(instance):
        return get_venue_last_updated(instance)

    @staticmethod
    def get_thumbnail_photo(instance):
        return instance.get_photo(ImageTypeEnum.LOGO) or ''

    def validate(self, attrs):
        if self.instance:
            contractor = self.context['contractor']
            if not contractor or contractor.status != Business.Status.PAID:
                raise ValidationError(_('You are not allowed to update Venue'))
        elif not settings.CAN_CREATE_UMBRELLA:
            raise ValidationError(_('You are not allowed to create Venue'))
        else:
            attrs.update(
                status=Business.Status.VENUE,
                owner=get_umbrella_user(),
                active=True,
                visible=True,
            )

        return attrs

    def update(self, instance, validated_data):
        before_change = BusinessChange.extract_vars(instance)
        metadata = {"contractor_id": self.context['contractor'].id}

        instance = super().update(instance, validated_data)
        BusinessChange.add(instance, instance, before_change, metadata=metadata)
        return instance


class RentingVenueMixin(serializers.Serializer):
    """Aka LocationMixin"""

    renting_venue_id = serializers.PrimaryKeyRelatedField(
        queryset=RentingVenue.objects.all(),
        write_only=True,
        required=False,
        allow_null=True,
    )
    renting_venue = RentingVenueSerializer(required=False, allow_null=True)
    location = LocationSerializer(source='*', required=False)

    @transaction.atomic
    def create(self, validated_data):
        data = self._save_renting_venue(validated_data)
        return super().create(data)

    @transaction.atomic
    def update(self, instance, validated_data):
        self._save_renting_venue(validated_data, business=instance)
        return super().update(instance, validated_data)

    def _save_renting_venue(self, validated_data, business=None):
        renting_venue_id = validated_data.pop('renting_venue_id', False)
        renting_venue = validated_data.pop('renting_venue', False)
        if renting_venue:
            # ignore address3 in venue
            renting_venue.pop('address3', None)
            # inherit primary_category for new renting_venue
            renting_venue['primary_category'] = (
                business.primary_category if business else validated_data.get('primary_category')
            )
        address3 = validated_data.pop('address3', False)

        # exists in posted data
        if renting_venue_id is not False:
            # venue selected
            if renting_venue_id is not None:
                validated_data['renting_venue_id'] = renting_venue_id.id
                for field in LOCATION_FIELDS:
                    validated_data[field] = getattr(renting_venue_id, field)
            # venue deselected
            else:
                validated_data['renting_venue_id'] = None

        if not renting_venue_id and renting_venue:
            instance = self.fields['renting_venue'].create(renting_venue)
            validated_data['renting_venue_id'] = instance.id

        if address3 is not False:
            business.custom_data[CustomData.VENUE_CONTRACTOR_ADDRESS_NO] = address3

        return validated_data


class VenueContractorDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = Business
        fields = (
            'id',
            'name',
            'primary_category',
            'cover_photo',
            'renting_venue_invitation_status',
        )
        show_missing_as = {}

    cover_photo = serializers.CharField(source='get_cover_photo_url')
    primary_category = IdAndNameSerializer()
    renting_venue_invitation_status = serializers.CharField(
        read_only=True,
        allow_null=True,
    )


class BusinessSalonNetworkField(serializers.PrimaryKeyRelatedField):
    def get_queryset(self):
        return Business.objects.filter(active=True).all()


class BusinessSalonNetworkSerializer(serializers.ModelSerializer):
    """You can also book with us here SalonNetwork serializer."""

    class Meta:
        model = SalonNetwork
        fields = ('members',)

    members = BusinessSalonNetworkField(allow_empty=True, many=True)


class SimpleBusinessLocationInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = Business
        fields = ('address', 'city', 'zipcode')
        read_only_fields = fields

    address = serializers.CharField()
    city = serializers.CharField(source='city_or_region_city')
    zipcode = serializers.CharField(source='zip')


class SimpleBusinessInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = Business
        fields = read_only_fields = (
            'active',
            'id',
            'location',
            'name',
            'official_name',
            'phone',
            'pos_enabled',
            'status',
        )

    location = SimpleBusinessLocationInfoSerializer(read_only=True, source='*')


class NewTermsFlowField(serializers.BooleanField):
    def __init__(self, **kwargs):
        kwargs['required'] = False
        super().__init__(**kwargs)

    def get_attribute(self, instance):
        if instance.agreement_exist:
            return instance.agreement.new_terms_flow
        return True


class BusinessOpeningHoursMixin(HoursSerializerMixin):
    # for backward compatibility: remove when not needed anymore
    open_hours = LegacyHoursSerializer(
        many=True,
        source=HoursSerializerMixin.WEEK_HOURS_FIELD,
        required=False,
    )
    # new format
    business_opening_hours = WeekHoursSerializer(
        source=HoursSerializerMixin.WEEK_HOURS_FIELD,
        many=True,
        required=False,
    )

    # remove when open_hours removed
    def run_validation(self, data=empty):
        # old format has precedence, since the old app
        # can unconsciously post old data
        if data is not empty:
            if 'open_hours' in data and 'business_opening_hours' in data:
                del data['business_opening_hours']

        return super().run_validation(data)

    def create(self, validated_data):
        if not validated_data.get(HoursSerializerMixin.WEEK_HOURS_FIELD):
            validated_data[HoursSerializerMixin.WEEK_HOURS_FIELD] = self.get_default_hours()
        instance = super().create(validated_data)
        if hasattr(instance, HoursSerializerMixin.WEEK_HOURS_FIELD):
            delattr(instance, HoursSerializerMixin.WEEK_HOURS_FIELD)
        return instance

    def update(self, instance, validated_data):
        week_hours = validated_data.get(HoursSerializerMixin.WEEK_HOURS_FIELD)
        if week_hours is not None:
            before_business_hours_update.send(
                None,
                business=instance,
                business_hours=week_hours,
            )
        return super().update(instance, validated_data)


class BusinessSerializer(
    PartnerAppMixin,
    TravelingToClientMixin,
    BusinessOpeningHoursMixin,
    RentingVenueMixin,
    serializers.ModelSerializer,
    SegmentIdSerializerMixin,
    SubdomainSerializerMixin,
    BusinessReferralCodeMixin,
):
    default_error_messages = {
        'invalid_min_max_lead_time': _('Booking Window conflict. Check Booking Rules.'),
        'invalid_lead_modification_time': _('Max lead & max modification times are incorrect'),
    }
    _categories_query = BusinessCategory.objects.filter(
        deleted=None,
        type=BusinessCategory.CATEGORY,
    )

    class Meta:
        model = Business
        fields = (
            'active',
            'active_appliances_count',
            'active_from',
            'active_staffers_count',
            'active_till',
            'alert_phone',
            'auto_correct',
            'bank_account_number',
            'bank_account_type',
            'blocked_hours',
            'booking_max_lead_time',
            'booking_max_modification_time',
            'booking_min_lead_time',
            'booking_mode',
            'booking_policy',
            'boost_payment_source',
            'boost_status',
            'categories',
            'cover_photo',
            'credit_cards',
            'description',
            'facebook_link',
            'gdpr_annex_signed',
            'has_addons',
            'has_braintree',
            'has_meeting_provider',
            'id',
            'invited_reward',
            'instagram_link',
            'public_email',
            'ecommerce_link',
            'invoice_address',
            'invoice_email',
            'is_gdpr_first_run',
            'is_visible_in_marketplace',
            'location',  # RentingVenueMixin
            'locked_limit_hourly',
            'mp_deeplink',
            'name',
            'name_short',
            'new_terms_flow',
            'official_name',
            'open_hours',
            'opening_hours_note',
            'owner_id',
            'owner_email',
            'owner_full_name',
            'parking',
            'package',
            'suggested_package',
            'payment_source',
            'phone',
            'phone_with_prefix',
            'physiotherapy_enabled',
            'pos_commissions_enabled',
            'pos_enabled',
            'pos_minimal_pay_by_app_payment',
            'pos_pay_by_app_enabled',
            'pos_prepayment_enabled',
            'printer_config',
            'pricing_level',
            'primary_category',
            'primary_category_internal_name',
            'promoted_before',
            'promotion_status',
            'promotion_trial_status',
            'referred_by',
            'referrer_reward',
            'referral_code',
            'registration_code',
            'renting_venue_id',  # RentingVenueMixin
            'renting_venue',  # RentingVenueMixin
            'reviews_count',
            'reviews_rank',
            'reviews_stars',
            'salon_network',
            'segment_business_id',
            'show_adyen_to_stripe_consent',
            'sms_limit',
            'sms_notification_status',
            'sms_priority',
            'status',
            'subdomain',
            'time_slots_optimization',
            'timezone',
            'traveling',
            'trial_till',
            'turntracker_enabled',
            # visible is deprecated in here, should not be used by new frontends or new api endpints
            'visible',
            'website',
            'wheelchair_access',
            'boost_remind_later',
            'boost_contact_now',
            'can_use_frontdesk',
            'is_3d_secure_verified',
            'is_migrated_demo_account',
            'migrated_from',
            'is_migration_completed',
            'can_extend_trial',
            'visible_delay_till',
            'show_onboarding_walkthrough_banner',
            'has_new_billing',
            'printer_config',
            'hidden_in_search',
            'partner_apps_enabled',
            'partner_apps',
            'not_published',
            'was_published_after_profile_setup',
            'show_activate_booksy_med_banner',
        )
        # specify which model fields(not in declared fields) are read only
        read_only_fields = (
            'active',
            'active_from',
            'active_till',
            'credit_cards',
            'has_addons',
            'has_meeting_provider',
            'id',
            'invited_reward',
            'migrated_from',
            'is_migration_completed',
            'can_extend_trial',
            'new_terms_flow',
            'payment_source',
            'suggested_package',
            'phone_with_prefix',
            'pos_enabled',
            'pos_pay_by_app_enabled',
            'printer_config',
            'primary_category_internal_name',
            'promoted_before',
            'promotion_status',
            'referrer_reward',
            'reviews_count',
            'reviews_stars',
            'status',
            'trial_till',
            'turntracker_enabled',
            'visible_delay_till',
            'has_new_billing',
            'printer_config',
            'partner_apps',
        )
        do_not_clean_fields = (
            'booking_mode',
            'latitude',
            'longitude',
            'name',
            'phone',
            'primary_category',
            'region',
            'registration_code',
            'subdomain',
            'booking_max_lead_time',
            'booking_min_lead_time',
            'booking_max_modification_time',
            'segment_business_id',
        )
        show_missing_as = {
            'alert_phone': '',
            'bank_account_number': '',
            'bank_account_type': '',
            'credit_cards': '',
            'description': '',
            'facebook_link': '',
            'instagram_link': '',
            'ecommerce_link': '',
            'invoice_address': '',
            'name_short': '',
            'official_name': '',
            'opening_hours_note': '',
            'parking': '',
            'phone': '',
            'pos_commissions_enabled': False,
            'pos_prepayment_enabled': False,
            'website': '',
            'wheelchair_access': '',
        }

    active_appliances_count = serializers.IntegerField(
        source='number_of_appliances',
        read_only=True,
    )
    active_staffers_count = serializers.IntegerField(
        source='number_of_staffers',
        read_only=True,
    )

    alert_phone = BooksyPhoneSerializerField(
        allow_blank=True,
        allow_null=True,
        required=False,
    )

    bank_account_number = serializers.CharField(
        source='pos.bank_account.full_account_number',
        read_only=True,
    )
    bank_account_type = serializers.CharField(
        source='pos.bank_account.type',
        read_only=True,
    )
    blocked_hours = ConstantValueField(constant_value=[])

    booking_max_lead_time = RelativedeltaField(required=False)
    booking_max_modification_time = RelativedeltaField(required=False)
    booking_min_lead_time = RelativedeltaField(required=False)
    booking_mode = serializers.ChoiceField(
        allow_blank=True,
        allow_null=True,
        choices=Business.BookingMode.choices(),
        required=False,
    )
    booking_policy = serializers.SerializerMethodField()

    categories = serializers.PrimaryKeyRelatedField(
        many=True,
        required=False,
        queryset=_categories_query.all(),
    )

    cover_photo = serializers.SerializerMethodField()
    website = BooksyURLField(
        allow_blank=True,
        allow_null=True,
        required=False,
    )
    ecommerce_link = BooksyURLField(
        allow_blank=True,
        allow_null=True,
        required=False,
    )
    facebook_link = BooksyFacebookURLField(
        allow_blank=True,
        allow_null=True,
        required=False,
        validators=[BusinessFacebookLinkValidator()],
    )
    instagram_link = BooksyInstagramURLField(
        allow_blank=True,
        allow_null=True,
        required=False,
        validators=[BusinessInstagramLinkValidator()],
    )

    invoice_email = serializers.EmailField(
        allow_blank=True,
        allow_null=True,
        required=False,
    )

    gdpr_annex_signed = serializers.SerializerMethodField()
    is_gdpr_first_run = serializers.SerializerMethodField()
    new_terms_flow = NewTermsFlowField()

    locked_limit_hourly = serializers.IntegerField(
        allow_null=True,
        min_value=0,
        max_value=180,
        required=False,
    )
    mp_deeplink = serializers.SerializerMethodField()
    name = serializers.CharField(
        max_length=Business.NAME_MAX_LENGTH,
        error_messages={
            'max_length': _(
                f'Business name is too long. Max. number of characters: {Business.NAME_MAX_LENGTH}.'
            )
        },
    )
    name_short = serializers.CharField(
        max_length=Business.NAME_MAX_LENGTH, allow_null=True, allow_blank=True, required=False
    )
    suggested_package = serializers.SerializerMethodField()
    physiotherapy_enabled = serializers.BooleanField(
        source='patient_file_enabled',
        read_only=True,
    )
    owner_id = serializers.IntegerField(
        source='owner.id',
        read_only=True,
    )
    owner_email = serializers.CharField(source='owner.email', read_only=True)
    owner_full_name = serializers.CharField(
        source='owner.full_name',
        read_only=True,
    )

    phone = BooksyPhoneSerializerField(
        allow_blank=True,
        allow_null=True,
        required=False,
    )

    primary_category = serializers.PrimaryKeyRelatedField(
        allow_empty=True,
        allow_null=True,
        required=False,
        queryset=_categories_query.all(),
    )

    primary_category_internal_name = serializers.SerializerMethodField()

    promotion_trial_status = serializers.SerializerMethodField()

    pos_commissions_enabled = serializers.BooleanField(
        source='pos.commissions_enabled',
        read_only=True,
    )
    pos_minimal_pay_by_app_payment = serializers.SerializerMethodField()
    pos_prepayment_enabled = serializers.BooleanField(
        source='pos.prepayment_enabled',
        read_only=True,
    )
    registration_code = RegistrationCodeField(
        allow_blank=True,
        allow_null=True,
        required=False,
    )
    reviews_rank = serializers.FloatField(
        source='reviews_rank_avg',
        read_only=True,
    )
    sms_priority = serializers.ChoiceField(
        choices=Business.SMSPriority.choices(),
        required=False,
    )
    timezone = TimezoneField(source='get_timezone')
    boost_status = serializers.ChoiceField(
        choices=Business.BoostStatus.choices(),
        required=False,
    )
    time_slots_optimization = serializers.BooleanField(
        allow_null=True,
        required=False,
    )
    salon_network = BusinessSalonNetworkSerializer(
        required=False,
        allow_null=True,
    )
    has_addons = serializers.BooleanField(
        allow_null=True,
        required=False,
    )
    has_meeting_provider = serializers.SerializerMethodField()

    referrer_reward = serializers.SerializerMethodField()
    invited_reward = serializers.SerializerMethodField()

    can_use_frontdesk = serializers.BooleanField(read_only=True)
    is_migrated_demo_account = serializers.BooleanField(read_only=True)
    migrated_from = serializers.SerializerMethodField()
    is_migration_completed = serializers.SerializerMethodField()
    can_extend_trial = serializers.SerializerMethodField()

    is_3d_secure_verified = serializers.SerializerMethodField()
    printer_config = serializers.SerializerMethodField()

    hidden_in_search = serializers.BooleanField(required=False)
    not_published = serializers.BooleanField(required=False, read_only=True)
    was_published_after_profile_setup = serializers.BooleanField(required=False, read_only=True)
    show_adyen_to_stripe_consent = serializers.SerializerMethodField()
    show_onboarding_walkthrough_banner = serializers.SerializerMethodField()
    show_activate_booksy_med_banner = serializers.SerializerMethodField()

    @staticmethod
    def get_mp_deeplink(instance):
        return instance.get_marketplace_deeplink()

    @staticmethod
    def get_booking_policy(instance):
        return booking_policy(instance)

    @staticmethod
    def get_cover_photo(business):
        return business.get_cover_photo_url()

    @staticmethod
    def get_pos_minimal_pay_by_app_payment(instance):  # pylint: disable=unused-argument
        minimal_payment = TransactionService.get_minimal_pay_by_app_payment()
        return {
            'amount': float(minimal_payment),
            'formatted_amount': format_currency(minimal_payment),
        }

    @staticmethod
    def get_promotion_trial_status(_):
        return TRIAL_STATUS_NOT_ACTIVE

    @staticmethod
    def get_gdpr_annex_signed(instance):
        if hasattr(instance, 'agreement'):
            return instance.agreement.annex_signed

        return get_default_annex_signed()

    @staticmethod
    def get_is_gdpr_first_run(instance):
        return not sget(instance, ['agreement', 'privacy_policy_agreement'])

    @staticmethod
    def get_has_meeting_provider(instance) -> bool:
        return has_zoom_credentials(instance)

    @staticmethod
    def get_suggested_package(instance):
        return instance.custom_data.get(
            CustomData.SUGGESTED_PACKAGE,
            Business.Package.LITE,
        )

    @staticmethod
    def get_primary_category_internal_name(instance):
        return safe_get(instance, ['primary_category', 'internal_name']) or '-'

    def to_representation(self, instance):
        result = super().to_representation(instance)

        for field, value in list(self.Meta.show_missing_as.items()):
            if field not in result or result[field] is None:
                result[field] = value

        return result

    @staticmethod
    def get_is_3d_secure_verified(instance):
        return instance.custom_data.get('is_3d_secure_verified')

    @staticmethod
    def get_printer_config(instance):
        if settings.API_COUNTRY == Country.BR:
            return True
        if settings.API_COUNTRY == Country.PL:
            return hasattr(instance, 'printer_config')
        return False

    @staticmethod
    def get_migrated_from(instance) -> str | None:
        if importer := instance.integrations.get('importer'):
            return importer.split()[0]
        return None

    def get_is_migration_completed(self, instance) -> bool | None:
        """
        Checks if migration is completed by checking if
        business made at least one Transaction.

        Currently used for "Versum" migration. In the future,
        this function can be expanded for other migrations.
        """

        if self.get_migrated_from(instance) is None:
            return None

        return instance.billing_transactions.filter(
            status=TransactionStatus.CHARGED,
            transaction_source=TransactionSource.BILLING_SUBSCRIPTION,
        ).exists()

    @staticmethod
    def get_can_extend_trial(instance) -> bool:
        """
        Checks business can extend their trial
        """
        is_within_active_range = bool(
            instance.active_from
            and (
                instance.active_from
                + timedelta(days=settings.TRIAL_EXTENSION_MIN_DAYS_AFTER_BUSINESS_ACTIVATION)
                <= tznow()
                <= instance.active_from
                + timedelta(days=settings.TRIAL_EXTENSION_MAX_DAYS_AFTER_BUSINESS_ACTIVATION)
            )
        )
        if (
            instance.status == Business.Status.TRIAL_BLOCKED
            and settings.ALLOW_TRIAL_EXTENSION
            and is_within_active_range
            and instance.has_proper_category_for_extend_trial
            and not instance.retrial_attempts.exists()
        ):
            return True
        return False

    @staticmethod
    def _has_social_links(validated_data: dict) -> bool:
        return validated_data.get('facebook_link') or validated_data.get('instagram_link')

    @transaction.atomic
    def update(self, instance, validated_data):
        validated_data.pop('package', None)  # 'package' is read_only on update
        _data = copy.deepcopy(validated_data)

        if 'region' in _data:
            instance.region = _data['region']

        if _data.get('has_braintree', False):
            # implementation of #60075
            from webapps.marketplace.tasks import make_sure_merchant_can_pay_for_boost_task

            make_sure_merchant_can_pay_for_boost_task.apply_async(
                args=(instance.id,),
                eta=tznow() + timedelta(seconds=30),
            )

        salon_network = _data.pop('salon_network', None)
        if salon_network:
            SalonNetwork.objects.get_or_create(business=instance)
            instance.salon_network.members.set(salon_network.get('members'))

        referral_code = _data.pop('referral_code', None)
        BusinessReferralCodeMixin.create_referral(referral_code, instance)

        self.update_business_policy_agreement(instance, _data)

        updated = super().update(instance, _data)

        if self._has_social_links(_data):
            step_link_profile.send(instance)

        return updated

    @staticmethod
    def update_business_policy_agreement(instance, _data):
        if 'new_terms_flow' in _data.keys():
            BusinessPolicyAgreement.objects.update_or_create(
                business=instance,
                defaults={'new_terms_flow': _data.pop('new_terms_flow')},
            )

    @transaction.atomic
    def create(self, validated_data):
        validated_data['owner'] = self.context['owner']
        validated_data['registration_source'] = self.context.get(
            'booking_source',
        )

        region = validated_data.pop('region', None)
        salon_network = validated_data.pop('salon_network', {})
        referral_code = validated_data.pop('referral_code', None)
        instance = super().create(validated_data)

        if region:
            instance.region = region
            instance.time_zone_name = region.time_zone_name
            instance.save(update_fields=['region', 'time_zone_name'])
        Warehouse.get_or_create_default_warehouse(instance)

        if salon_network:
            SalonNetwork.objects.create(business=instance)
            instance.salon_network.members.set(salon_network.get('members'))

        BusinessReferralCodeMixin.create_referral(referral_code, instance)

        if self._has_social_links(validated_data):
            step_link_profile.send(instance)

        return instance

    def run_validation(self, data=serializers.empty):
        if data is not serializers.empty:
            if data.get('primary_category') == 0:
                data['primary_category'] = None
            location = data.get('location') or {}
            if 'region' in location and location['region'] is None:
                del location['region']
        return super().run_validation(data)

    def _validate_booking_min_max_lead_time(self, attrs):
        booking_min_lead_time = attrs.get('booking_min_lead_time')
        booking_max_lead_time = attrs.get('booking_max_lead_time')

        if self.instance and (booking_min_lead_time or booking_max_lead_time):
            if not booking_min_lead_time:
                booking_min_lead_time = self.instance.booking_min_lead_time

            if not booking_max_lead_time:
                booking_max_lead_time = self.instance.booking_max_lead_time

        if booking_min_lead_time and booking_max_lead_time:
            if not Business.check_booking_min_max_lead_times(
                booking_min_lead_time, booking_max_lead_time
            ):
                self.fail('invalid_min_max_lead_time')

    def validate(self, attrs):
        attrs = super().validate(attrs)

        # prevent data cleaning
        for field in self.Meta.do_not_clean_fields:
            if field in attrs and not attrs[field]:
                attrs.pop(field)

        self._validate_booking_min_max_lead_time(attrs)

        booking_max_lead_time = attrs.get('booking_max_lead_time')
        booking_max_modification_time = attrs.get('booking_max_modification_time')

        if booking_max_lead_time or booking_max_modification_time:
            booking_max_lead_time = booking_max_lead_time or self.instance.booking_max_lead_time
            booking_max_modification_time = (
                booking_max_modification_time or self.instance.booking_max_modification_time
            )
            if not Business.check_booking_max_lead_modification_times(
                booking_max_lead_time, booking_max_modification_time
            ):
                self.fail('invalid_lead_modification_time')

        return attrs

    primary_category_error_messages = {
        BusinessCategoryEnum.AUTOMOTIVE: _(
            'With automotive as primary category '
            'all categories must be subcategories of automotive.'
        ),
        BusinessCategoryEnum.FINANCIAL_INSTITUTIONS: _(
            'With financial institutions as primary category '
            'all categories must be subcategories of financial '
            'institutions.'
        ),
        BusinessCategoryEnum.SHOPPING: _(
            'With shopping as primary category all categories must be subcategories of shopping.'
        ),
        BusinessCategoryEnum.TELECOM: _(
            'With telecommunications as primary category '
            'all categories must be subcategories of telecommunications.'
        ),
    }

    @staticmethod
    def validate_name(value):
        patterns = [
            r'[\w\d]@\w+\.',  # pseudo-email
            r'https?://',  # website link
            r'www.',  # common website subdomain
        ]
        phone_pattern = r'\d{7}'
        normalized_value = normalize('NFKC', value)

        if re.search(
            phone_pattern, normalized_value.replace(' ', '').replace('-', ''), flags=re.IGNORECASE
        ) or re.search('|'.join(patterns), normalized_value, flags=re.IGNORECASE):
            raise serializers.ValidationError(
                _('Business name cannot contain email, phone, or website link.'),
                code='invalid_name',
            )

        return value

    @staticmethod
    def validate_phone(value):
        if SecurityBlacklistFlag():
            BlacklistValidator.validate(value.replace(' ', ''), BlacklistType.PHONE)
        return value

    def validate_primary_category(self, value):
        categories = self.initial_data.get('categories')
        if not categories and self.instance:
            categories = [c.id for c in self.instance.categories.all()]

        if not categories:
            return None

        if not value:
            return BusinessCategory.objects.get(id=categories[0])

        if value.id not in categories:
            raise serializers.ValidationError(
                _("Primary category must be one of business categories."), code='invalid'
            )

        if value.internal_name in self.primary_category_error_messages:
            allowed_categories = BusinessCategory.objects.filter(
                parent__internal_name=value.internal_name,
            ).values_list('id', flat=True)
            if any(cat_id not in allowed_categories for cat_id in categories if cat_id != value.id):
                raise serializers.ValidationError(
                    self.primary_category_error_messages[value.internal_name], code='invalid'
                )

        primary_category = self.instance.primary_category if self.instance else None
        if (
            primary_category is not None
            and primary_category.internal_name in MODERATED_CATEGORIES
            and value != primary_category
        ):
            raise serializers.ValidationError(
                _('To change business category, contact the support department'), code='invalid'
            )

        return value

    @staticmethod
    def validate_booking_mode(value):
        # 16177: Manual -> Semi-Automatic hack
        if value and value == Business.BookingMode.MANUAL:
            value = Business.BookingMode.SEMIAUTO

        return value

    def validate_location(self, value):
        region = value.get('region')
        if not region:
            return value

        if (
            self.instance
            and self.instance.status != Business.Status.SETUP
            and self.instance.time_zone_name
            and self.instance.time_zone_name != region.time_zone_name
        ):
            raise serializers.ValidationError(
                _("You can not change your time zone. Please contact customer support.")
            )
        value['time_zone_name'] = region.time_zone_name
        return value

    @staticmethod
    def validate_locked_limit_hourly(value):
        try:
            return int(value)
        except (TypeError, ValueError):
            return None

    @staticmethod
    def get_referrer_reward(instance):
        """Returns prize for referring"""
        if not settings.B2B_REFERRAL_ENABLED:
            return None

        setting = B2BReferralSetting.get_setting(instance)
        return setting.referrer_reward_amount or None if setting else None

    @staticmethod
    def get_invited_reward(instance):
        """Returns prize for being referred"""
        if not settings.B2B_REFERRAL_ENABLED:
            return None

        setting = B2BReferralSetting.get_setting(instance)
        return setting.invited_reward_amount or None if setting else None

    def validate_visible(self, value):
        validate_business_visibility_change(self.instance, value)
        return value

    def get_show_adyen_to_stripe_consent(self, instance):  # pylint: disable=unused-argument
        return False

    def get_show_onboarding_walkthrough_banner(self, instance):
        if not instance.active_from or not instance.visible_from:
            return False

        max_active_from_delay = BusinessVisibleDelayRequestSerializer.VISIBLE_DELAY_MAX_VALUE
        max_visible_from_delay = 2

        is_visible_in_marketplace = self.fields['is_visible_in_marketplace'].get_attribute(instance)
        return (
            is_visible_in_marketplace
            and instance.active_from + timedelta(days=max_active_from_delay) >= tznow()
            and instance.visible_from + timedelta(days=max_visible_from_delay) >= tznow()
        )

    def get_show_activate_booksy_med_banner(self, instance):
        if (
            settings.API_COUNTRY != Country.PL
            or instance.status != Business.Status.TRIAL
            or not instance.primary_category
        ):
            return False

        return (
            OAuth2ApplicationCategoryLink.objects.filter(
                business_category=instance.primary_category,
                application_category__is_active=True,
                deleted__isnull=True,
            )
            .annotate(
                has_fizjo_app=Exists(
                    OAuth2Application.objects.filter(
                        category=OuterRef('application_category'),
                        client_id=settings.FIZJOREJESTRACJA_APPLICATION_CLIENT_ID,
                        is_active=True,
                    )
                ),
                has_installation=Exists(
                    OAuth2Installation.objects.filter(
                        business=instance,
                        application__category=OuterRef('application_category'),
                        application__client_id=settings.FIZJOREJESTRACJA_APPLICATION_CLIENT_ID,
                        deleted__isnull=True,
                    )
                ),
            )
            .filter(
                has_fizjo_app=True,
                has_installation=False,
            )
            .exists()
        )


class BusinessForOpeningHoursSerializer(BusinessSerializer):
    @staticmethod
    def get_referrer_reward(instance) -> float | None:
        if (reward := BusinessSerializer.get_referrer_reward(instance)) is None:
            return reward
        return float(reward)


class BusinessPackageSerializer(serializers.ModelSerializer):
    package = serializers.ChoiceField(choices=Business.ELIGIBLE_PACKAGE)

    class Meta:
        model = Business
        fields = ('package',)


class BusinessPackageAdminSerializer(BusinessPackageSerializer):
    package = serializers.ChoiceField(choices=Business.Package.choices())


class BusinessNameSerializer(serializers.ModelSerializer):
    class Meta:
        model = Business
        fields = ('id', 'name')


class ServicePromotionMixin:
    @property
    def icon(self):
        return '\U0001F6A8'  # fmt: skip

    @property
    def business(self):
        return self.context['business']

    def discounted_price(self, price):
        """Calculate discounted price for promotion.

        :param price: ServiceVariant price
        :return: discounted price

        """
        if not self.instance:
            return None

        price = Decimal(price)

        try:
            rate = Decimal(self.instance.promotion_options['discount_rate'])
            discounted_price = price - (rate / 100) * price

        except (AttributeError, KeyError, ValueError):
            discounted_price = None

        return discounted_price

    @property
    def booking_source(self):
        return {
            'booking_source': self.context.get('booking_source'),
        }

    def _validate_service_variant_ids(self, service_variant_ids):
        """Checks for active business ServiceVariants with price."""
        if not service_variant_ids:
            return []

        qs = (
            ServiceVariant.objects.filter(
                active=True,
                id__in=service_variant_ids,
                service__business=self.business,
            )
            .filter(Q(type__in=PriceType.has_price()) | Q(service__combo_type__isnull=False))
            .values_list('id', flat=True)
        )

        return list(set(service_variant_ids) & set(qs))

    def log_entry(self, action):
        user_id = getattr(self.context.get('user'), 'id', None)
        source_id = getattr(self.context.get('booking_source'), 'id', None)
        return {
            'action': action,
            'source_id': source_id,
            'user_id': user_id,
        }


class FlashSalePromotionSerializer(
    serializers.ModelSerializer,
    ServicePromotionMixin,
):
    """Flash Sale promotion serializer."""

    class Meta:
        model = ServicePromotion
        fields = (
            'id',
            'discount_rate',
            'promotion_duration',
            'promotion_start',
            'promotion_end',
            'booking_start',
            'booking_end',
            'service_variant_ids',
        )
        read_only_fields = ('id', 'promotion_start', 'type')

    booking_start = serializers.DateField(required=True)
    booking_end = serializers.DateField(required=False, allow_null=True)
    discount_rate = serializers.IntegerField(
        max_value=100,
        min_value=1,
        required=True,
    )
    promotion_duration = serializers.ChoiceField(
        choices=SERVICE_PROMOTION_DURATION,
        required=False,
    )
    service_variant_ids = serializers.ListField(
        child=serializers.IntegerField(required=True),
        required=True,
        allow_empty=False,
        allow_null=False,
    )
    promotion_end = serializers.DateField(required=False)

    @staticmethod
    def validate_booking_dates_order(data):
        promo_start = data['promotion_start']
        booking_start = data['booking_start']
        booking_end = data['booking_end']
        promotion_end = data['promotion_end']

        if booking_start < promo_start:
            raise serializers.ValidationError(
                {
                    'booking_start': 'Invalid booking_start date',
                }
            )

        if not booking_end:
            return

        if booking_start > booking_end:
            raise serializers.ValidationError(
                {
                    'booking_start': _(
                        'Select different dates. Make sure the start '
                        'date is before the end date.'
                    ),
                }
            )

        if promotion_end > booking_end:
            raise serializers.ValidationError(
                {
                    'booking_end': _(
                        'Select different dates. Make sure the "Sale Period" '
                        'does not end later than "Booking Dates to qualify".'
                    ),
                }
            )

    def validate(self, attrs):
        attrs = super().validate(attrs)

        tzinfo = self.business.get_timezone()

        if 'promotion_duration' in attrs:
            attrs.update(self._promo_dates_from_duration(attrs, tzinfo))
        elif 'promotion_end' in attrs:
            attrs.update(
                self._promo_dates_with_duration(attrs['promotion_end'], tzinfo),
            )
        else:
            raise serializers.ValidationError(
                'promotion_duration or promotion_end field is required',
            )

        attrs['booking_start'] = attrs['booking_start']
        attrs['booking_end'] = attrs.get('booking_end')

        # booking dates_order
        self.validate_booking_dates_order(attrs)

        attrs.update(self.booking_source)

        return attrs

    def validate_service_variant_ids(self, value):
        return self._validate_service_variant_ids(value)

    def to_internal_value(self, data):
        values = super().to_internal_value(data)

        values['type'] = SERVICE_VARIANT_FLASH_SALE
        values['business_id'] = self.business.id

        discount_rate = values.pop('discount_rate', 10)
        service_variant_ids = values.pop('service_variant_ids')
        values['promotion_options'] = {
            'discount_rate': discount_rate,
            'service_variant_ids': service_variant_ids,
        }

        return values

    def to_representation(self, instance):
        """
        Remove fields irrelevant for FM promotion.

        """
        ret = instance.promotion_options
        ret.update(
            {
                'booking_start': instance.booking_start,
                'booking_end': instance.booking_end,
                'promotion_start': instance.promotion_start,
                'promotion_end': instance.promotion_end,
                'promotion_duration': instance.promotion_duration,
                'blast_message': self.blast_message(instance),
                'blast_template': self.blast_template(instance),
            }
        )

        return ret

    @staticmethod
    def _promo_dates_from_duration(
        data: dict,
        tzinfo: t.Optional[tz.tzfile],
    ) -> dict:
        promotion_days = int(data['promotion_duration']) - 1
        duration = timedelta(days=promotion_days)
        now = tznow(tzinfo)
        return {
            'promotion_start': now.date(),
            'promotion_end': now.date() + duration,
        }

    @staticmethod
    def _promo_dates_with_duration(
        promotion_end: datetime,
        tzinfo: t.Optional[tz.tzfile],
    ) -> dict:
        now = tznow(tzinfo)
        if now.date() > promotion_end:
            raise serializers.ValidationError(
                'promotion_end must not be in the past',
            )
        duration = (promotion_end - now.date()).days + 1
        return {
            'promotion_duration': duration,
            'promotion_start': now.date(),
            'promotion_end': promotion_end,
        }

    @transaction.atomic
    def create(self, validated_data):
        instance = super().create(validated_data)

        instance.add_to_log(**self.log_entry(action='created'))
        instance.save()

        query_set = (
            ServicePromotion.objects.select_for_update()
            .filter(
                active=True,
                business=self.business,
                type=SERVICE_VARIANT_FLASH_SALE,
            )
            .exclude(
                id=instance.id,
            )
        )

        for prev_instance in query_set:
            prev_instance.active = False
            prev_instance.add_to_log(**self.log_entry(action='disable'))
            prev_instance.save()

        # profile completeness
        step_launch_promotions.send(self.business)

        return instance

    def blast_message(self, instance):
        blast_messages = {
            'today': {
                'booking_no_policy': _(
                    "Flash sale!"
                    " Get a %(discount_rate)s%% discount when you book Today."
                    " %(subdomain)s"
                ),
                'booking_policy_from': _(
                    "Flash sale!"
                    " Get a %(discount_rate)s%% discount when you come in for"
                    " appointment from %(booking_start)s."
                    " Small catch:"
                    " You need to book TODAY!"
                    " %(subdomain)s"
                ),
                'booking_policy_between': _(
                    "Flash sale!"
                    " Get a %(discount_rate)s%% discount when you come in for"
                    " appointment between %(booking_start)s and %(booking_end)s."
                    " Small catch:"
                    " You need to book TODAY!"
                    " %(subdomain)s"
                ),
            },
            'future': {
                'booking_no_policy': _(
                    "Flash sale!"
                    " Get a %(discount_rate)s%% discount when you book in next"
                    " %(days)s days."
                    " %(subdomain)s"
                ),
                'booking_policy_from': _(
                    "Flash sale!"
                    " Get a %(discount_rate)s%% discount when you come in for"
                    " appointment from %(booking_start)s."
                    " Small catch:"
                    " You need to book within the next %(days)s DAYS!"
                    " %(subdomain)s"
                ),
                'booking_policy_between': _(
                    "Flash sale!"
                    " Get a %(discount_rate)s%% discount when you come in for"
                    " appointment between %(booking_start)s and %(booking_end)s."
                    " Small catch:"
                    " You need to book within the next %(days)s DAYS!"
                    " %(subdomain)s"
                ),
            },
        }

        values = {
            'discount_rate': instance.promotion_options['discount_rate'],
            'booking_start': (
                instance.booking_start.strftime(settings.DATE_FORMAT)
                if instance.booking_start
                else ''
            ),
            'booking_end': (
                instance.booking_end.strftime(settings.DATE_FORMAT) if instance.booking_end else ''
            ),
            'days': instance.promotion_duration,
            'subdomain': self.business.get_seo_url(),
        }

        if (
            instance.booking_start
            and instance.booking_end
            and instance.booking_end == instance.promotion_start
        ):
            policy = 'booking_no_policy'

        elif instance.booking_start and instance.booking_end:
            policy = 'booking_policy_between'

        elif instance.booking_start and not instance.booking_end:
            policy = 'booking_policy_from'

        book_within = 'today' if instance.promotion_duration == 1 else 'future'
        # pylint: disable=possibly-used-before-assignment
        return blast_messages[book_within][policy] % values

    def blast_template(self, instance):
        """Returns body for new message blast format."""
        return CommonMessageBlastTemplate.create_blast_template_for_promotions(
            business=self.business, title=_('Flash Sale'), text=self.blast_message(instance)
        )


class LastMinutePromotionSerializer(
    serializers.ModelSerializer,
    ServicePromotionMixin,
):
    """Last minute promotion serializer."""

    class Meta:
        model = ServicePromotion
        fields = (
            'id',
            'discount_rate',
            'last_minute_hours',
            'service_variant_ids',
        )
        read_only_fields = ('id', 'type')

    discount_rate = serializers.IntegerField(
        max_value=100,
        min_value=1,
        required=True,
    )
    last_minute_hours = serializers.ChoiceField(
        required=True,
        choices=SERVICE_PROMOTION_LM_HOUR,
    )
    service_variant_ids = serializers.ListField(
        child=serializers.IntegerField(required=True),
        required=True,
        allow_empty=False,
        allow_null=False,
    )

    def validate(self, attrs):
        attrs = super().validate(attrs)
        attrs.update(self.booking_source)
        return attrs

    def validate_service_variant_ids(self, value):
        return self._validate_service_variant_ids(value)

    def to_internal_value(self, data):
        values = super().to_internal_value(data)

        values['business_id'] = self.business.id
        values['type'] = SERVICE_VARIANT_LAST_MINUTE

        discount_rate = values.pop('discount_rate', 10.0)
        service_variant_ids = values.pop('service_variant_ids')

        values['promotion_options'] = {
            'discount_rate': discount_rate,
            'service_variant_ids': service_variant_ids,
        }

        return values

    def to_representation(self, instance):
        ret = instance.promotion_options
        ret.update(
            {
                'last_minute_hours': instance.last_minute_hours,
                'blast_message': self.blast_message(instance),
                'blast_template': self.blast_template(instance),
            }
        )

        return ret

    @transaction.atomic
    def create(self, validated_data):
        instance = super().create(validated_data)

        instance.add_to_log(**self.log_entry(action='created'))
        instance.save()

        query_set = (
            ServicePromotion.objects.select_for_update()
            .filter(
                active=True,
                business=self.business,
                type=SERVICE_VARIANT_LAST_MINUTE,
            )
            .exclude(
                id=instance.id,
            )
        )

        for prev_instance in query_set:
            prev_instance.active = False
            prev_instance.add_to_log(**self.log_entry(action='disable'))
            prev_instance.save()

        # profile completeness
        step_launch_promotions.send(self.business)

        return instance

    def blast_message(self, instance):
        if instance.last_minute_hours == 1:
            message = _(
                'Book an appointment no more than %(within)s hour in advance'
                ' to score a %(discount_rate)s%% discount!'
                ' What are you waiting for? %(subdomain)s'
            )
        else:
            message = _(
                'Book an appointment no more than %(within)s hours in advance'
                ' to score a %(discount_rate)s%% discount!'
                ' What are you waiting for? %(subdomain)s'
            )
        return message % {
            'icon': self.icon,
            'within': instance.last_minute_hours,
            'discount_rate': instance.promotion_options['discount_rate'],
            'subdomain': self.business.get_seo_url(),
        }

    def blast_template(self, instance):
        """Returns body for new message blast format."""
        return CommonMessageBlastTemplate.create_blast_template_for_promotions(
            business=self.business, title=_('Last Minute'), text=self.blast_message(instance)
        )


class HappyHoursServiceVariantSerializer(serializers.Serializer):
    default_error_messages = {
        'rate_lt_1': _('Ensure discount_rate is greater than or equal to 1.'),
        'rate_gt_100': _('Ensure discount_rate is less than or equal to 100.'),
        'amt_lt_0.01': _('Ensure discount_amount is greater than or equal to 0.01.'),
    }

    discount_type = serializers.ChoiceField(choices=DiscountType.choices())
    discount_amount = serializers.DecimalField(
        decimal_places=2,
        max_digits=5,
    )
    service_variant_id = serializers.IntegerField(required=False)
    hour_from = serializers.TimeField(format='%H:%M', required=True)
    hour_till = serializers.TimeField(format='%H:%M', required=True)

    def validate(self, attrs):
        attrs = super().validate(attrs)
        attrs['hour_from'] = attrs['hour_from'].strftime('%H:%M')
        attrs['hour_till'] = attrs['hour_till'].strftime('%H:%M')

        if attrs['discount_type'] == DiscountType.RATE:
            if attrs['discount_amount'] < 1:
                self.fail('rate_lt_1')
            if attrs['discount_amount'] > 100:
                self.fail('rate_gt_100')

        elif attrs['discount_type'] == DiscountType.AMOUNT:
            if attrs['discount_amount'] < Decimal('0.01'):
                self.fail('amt_lt_0.01')

        return attrs


class HappyHourPromotionListSerializer(
    serializers.ListSerializer,
    ServicePromotionMixin,
):
    """Takes care of disabling previous active promotions."""

    @transaction.atomic
    def create(self, validated_data):
        business = self.context['business']
        days_of_week_to_disable = [data['happy_hours_week_day'] for data in validated_data]

        ServicePromotions.disable_happy_hours(
            business=business,
            days_of_week=days_of_week_to_disable,
            log_entry=self.log_entry(action='disable'),
        )

        promotions = [
            ServicePromotion(**data)
            for data in validated_data
            if data['promotion_options']['service_variants']
        ]
        instances = ServicePromotion.objects.bulk_create(promotions)

        for instance in instances:
            instance.add_to_log(**self.log_entry(action='create'))
            instance.save()

        # profile completeness
        step_launch_promotions.send(self.business)

        return instances

    @transaction.atomic
    def update(self, instance, validated_data):
        """Merges new promotions with existing ones."""
        business = self.context['business']

        _instance = defaultdict(dict)
        for inst in instance:
            _instance[inst.happy_hours_week_day] = {
                int(variant['service_variant_id']): variant
                for variant in inst.promotion_options['service_variants']
            }

        _validated_data = {
            int(day['happy_hours_week_day']): {
                int(variant['service_variant_id']): variant
                for variant in day['promotion_options']['service_variants']
            }
            for day in validated_data
        }

        for day in _validated_data:
            for variant_id in _validated_data[day]:
                _instance[day][variant_id] = _validated_data[day][variant_id]

        ServicePromotions.disable_happy_hours(
            business=business,
            days_of_week=list(_instance.keys()),
            log_entry=self.log_entry(action='disable'),
        )

        updated_validated_data = [
            {
                'business_id': business.id,
                'happy_hours_week_day': day,
                'promotion_options': {
                    'service_variants': list(_instance[day].values()),
                },
                'type': SERVICE_VARIANT_HAPPY_HOURS,
            }
            for day in _instance
        ]

        promotions = [
            ServicePromotion(**data)
            for data in updated_validated_data
            if data['promotion_options']['service_variants']
        ]
        instance = ServicePromotion.objects.bulk_create(promotions)

        # profile completeness
        step_launch_promotions.send(self.business)

        return instance

    def blast_message(self, data):
        discounts = defaultdict(list)
        for dow in data:
            for opt in dow['service_variants']:
                discounts[opt['discount_type']].append(Decimal(opt['discount_amount']))

        if discounts[DiscountType.RATE]:
            discount = f'{max(discounts[DiscountType.RATE]):.0f}%'
        elif discounts[DiscountType.AMOUNT]:
            discount = format_currency(max(discounts[DiscountType.AMOUNT]))
        else:
            discount = ''

        return _(
            # u"%(icon) "
            'Dear Customer, Happy Hours are back!'
            ' Book now with great discounts up to %(discount)s!'
            ' %(subdomain)s'
        ) % {
            'icon': self.icon,
            'discount': discount,
            'subdomain': self.business.get_seo_url(),
        }

    def blast_template(self, data):
        """Returns body for new message blast format."""
        return CommonMessageBlastTemplate.create_blast_template_for_promotions(
            business=self.business, title=_('Happy Hours'), text=self.blast_message(data)
        )


class HappyHourPromotionSerializer(
    serializers.ModelSerializer,
    ServicePromotionMixin,
):
    """Happy Hours promotion serializer."""

    class Meta:
        model = ServicePromotion
        fields = ('day_of_week', 'service_variants')
        read_only_fields = ('id', 'type')
        list_serializer_class = HappyHourPromotionListSerializer

    day_of_week = serializers.ChoiceField(
        choices=DAYS_OF_THE_WEEK,
        required=True,
        source='happy_hours_week_day',
    )
    service_variants = HappyHoursServiceVariantSerializer(many=True)

    def validate(self, attrs):
        attrs = super().validate(attrs)
        attrs.update(self.booking_source)
        return attrs

    def validate_service_variants(self, value):
        service_variant_ids = [sv.get('service_variant_id') for sv in value]
        valid_variant_ids = self._validate_service_variant_ids(
            service_variant_ids,
        )

        return [val for val in value if val['service_variant_id'] in valid_variant_ids]

    def to_internal_value(self, data):
        values = super().to_internal_value(data)

        values['business_id'] = self.business.id
        values['type'] = SERVICE_VARIANT_HAPPY_HOURS

        service_variants = values.pop('service_variants', [])

        for sv in service_variants:
            sv.update(
                {
                    'discount_amount': f'{sv["discount_amount"]:.2f}',
                }
            )

        values['promotion_options'] = {'service_variants': service_variants}

        return values

    def to_representation(self, instance):
        result = instance.promotion_options
        result.update(
            {
                'day_of_week': instance.happy_hours_week_day,
            }
        )
        return result

    def discounted_price(self, price):
        """Discounted price calculations.

        This method overrides general one from ServicePromotionMixin.
        :param price: ServiceVariant base price
        :return: discounted_price or None

        """
        if not self.instance:
            return None

        price = Decimal(price)

        try:
            discount_type = self.instance.promotion_options['discount_type']
            rate = Decimal(self.instance.promotion_options['discount_amount'])

            if discount_type == DiscountType.AMOUNT:
                discounted_price = price - rate
            else:
                discounted_price = price - (rate / 100 * price)

        except (AttributeError, KeyError, ValueError):
            discounted_price = None

        return discounted_price

    @transaction.atomic
    def create(self, validated_data):
        instance = super().create(validated_data)

        # profile completeness
        step_launch_promotions.send(self.business)

        return instance


class ServiceVariantPriceSerializer(serializers.Serializer):
    amount = serializers.DecimalField(
        decimal_places=2,
        max_digits=10,
        source='service_price.value',
    )
    formatted_amount = serializers.SerializerMethodField()

    @staticmethod
    def get_formatted_amount(instance):
        return instance.format_price()


class PromotionServiceVariantSerializer(serializers.Serializer):
    service_variant_id = serializers.IntegerField(source='id')
    price = ServiceVariantPriceSerializer(source='*')
    name = serializers.CharField(source='service.name')
    type = serializers.CharField(source='service_price.price_type')
    duration = DurationField(source='service_duration')


class PromotionsServiceVariantsSerializer(serializers.Serializer):
    service_categories = serializers.SerializerMethodField()

    def get_service_categories(self, instances):
        categories = defaultdict(list)

        for variant in instances:
            _category = self._service_category(variant)
            categories[_category].append(PromotionServiceVariantSerializer(variant).data)

        return [
            {
                'service_category': category,
                'service_variants': variants,
            }
            for category, variants in list(categories.items())
        ]

    @staticmethod
    def _service_category(variant):
        if not variant.service.service_category:
            return force_str(_('Not categorised'))

        return variant.service.service_category.name


class CancellationReasonSerializer(serializers.ModelSerializer):
    """Used for churn action (old/new admin)"""

    business = serializers.PrimaryKeyRelatedField(
        queryset=Business.objects.all(),
    )
    cancellation_date = serializers.DateTimeField(
        input_formats=['%Y-%m-%d', '%Y-%m-%d %H:%M:%S', 'iso-8601'],
    )
    left_for_competitor = serializers.BooleanField(default=False)

    class Meta:
        model = CancellationReason
        fields = (
            'id',
            'business',
            'cancellation_date',
            'cancellation_reason',
            'cancellation_info',
            'left_for_competitor',
            'competitor_name',
            'operator',
        )

    @staticmethod
    def validate_business(business):
        if not business.churn_available():
            raise serializers.ValidationError('Churn not available - wrong business status')
        return business

    @staticmethod
    def validate_new_billing(business, attrs):
        cancellation_date = attrs['cancellation_date'].date()
        booksy_operator = attrs.get('operator')
        today = tznow().date()

        subscription = BillingSubscription.get_current_subscription(business.pk)
        current_cycle_end = subscription.current_cycle_end.date() if subscription else None

        if (
            business.status == Business.Status.PAID
            and booksy_operator
            and BillingUserPermission(booksy_operator).is_billing_admin
        ):
            # billing admins are allowed to churn business before cycle switch
            # or without any active subscriptions
            if current_cycle_end is None:
                pending_subscription = BillingSubscription.get_current_or_pending_subscription(
                    business.pk
                )
                current_cycle_end = (
                    pending_subscription.date_start.date()
                    if pending_subscription
                    else (today + timedelta(days=14))
                )

            if cancellation_date < today or cancellation_date > current_cycle_end:
                raise serializers.ValidationError(
                    {
                        'cancellation_date': (
                            f'The date must be equal or greater that today ({today}) and '
                            f'lower or equal than {current_cycle_end}.'
                        )
                    }
                )
        elif business.status == Business.Status.PAID:
            if subscription is None:
                # It should never have happened.
                raise serializers.ValidationError({'business': 'Business has no subscription'})
            if cancellation_date != current_cycle_end:
                raise serializers.ValidationError(
                    {
                        'cancellation_date': (
                            f'The date must be equal to the end of current '
                            f'billing cycle ({current_cycle_end.isoformat()})'
                        )
                    }
                )
        # POA, POB
        elif subscription and (cancellation_date > current_cycle_end or cancellation_date < today):
            raise serializers.ValidationError(
                {
                    'cancellation_date': (
                        'The date must be greater than or equal to today '
                        'and less than or equal to the next billing '
                        f'period ({current_cycle_end.isoformat()}).'
                    )
                }
            )
        # POA, POB without subscription
        elif not subscription and cancellation_date != today:
            raise serializers.ValidationError(
                {
                    'cancellation_date': (
                        'No active subscription found. The date must be equal to today.'
                    )
                }
            )

        if cancellation := CancellationReason.objects.filter(
            business_id=business.pk, churn_done=False, deleted__isnull=True
        ).first():
            cancellation_date = (
                cancellation.cancellation_date.isoformat()
                if cancellation.cancellation_date
                else '[not set]'
            )
            raise serializers.ValidationError(
                {
                    'business': (
                        'This business already has a churn request '
                        f'(Request ID: {cancellation.id}, Date: {cancellation_date}).'
                    )
                }
            )

    def validate(self, attrs):
        attrs = super().validate(attrs)
        mass_processing = self.context.get('mass_processing', False)
        left_for_competitor = attrs.get('left_for_competitor')
        competitor_name = attrs.get('competitor_name')

        if left_for_competitor and not competitor_name:
            raise serializers.ValidationError({'competitor_name': 'Please provide competitor name'})

        business = attrs['business']
        cancellation_date = attrs['cancellation_date'].date()
        _now = tznow()

        if business.has_new_billing:
            self.validate_new_billing(
                business=business,
                attrs=attrs,
            )

        if not mass_processing and (
            active_subscription := business.subscriptions.filter(
                start__lt=_now,
                expiry__gt=_now,
                source=Business.PaymentSource.OFFLINE,
                business__payment_source=Business.PaymentSource.OFFLINE,
            ).last()
        ):
            expected_cancellation_date = active_subscription.expiry.date() + timedelta(days=1)

            if cancellation_date != expected_cancellation_date:
                raise serializers.ValidationError(
                    {
                        'cancellation_date': f"Cancellation date for offline subscription must be "
                        f"subscription expiry_date + 1 day! which is: {expected_cancellation_date}",
                    }
                )

        return attrs

    def create(self, validated_data):
        left_for_competitor = validated_data.pop('left_for_competitor', False)
        if left_for_competitor is False:
            validated_data.pop('competitor_name', None)
        validated_data['churn_done'] = False
        return super().create(validated_data)


class MassCancellationReasonSerializer(CancellationReasonSerializer):
    """Used for mass churn action"""

    cancellation_reason = BooksyTypedChoiceField(
        choices=list(CancellationReasonType.choices_reverse_map().keys()),
        coerce=lambda x: CancellationReasonType.choices_reverse_map()[x],
    )
    left_for_competitor = BooksyYesNoField(
        write_only=True,
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.context['mass_processing'] = True


class BlistingSerializer(serializers.ModelSerializer):
    class Meta:
        model = Business
        fields = (
            'active',
            'verification',
            'name',
            'address',
            'address2',
            'phone',
            'source_email',
            'primary_category',
            'city',
            'latitude',
            'longitude',
            'region',
        )

    def validate(self, attrs):
        if not (attrs['region'] and attrs['address']):
            raise serializers.ValidationError()
        email = attrs['source_email'] or ''
        from cliapps.business.b_listing import check_blisting_email

        if not check_blisting_email(email=email):
            raise serializers.ValidationError()
        return attrs

    phone = BooksyPhoneSerializerField(
        allow_blank=True,
        allow_null=True,
        required=False,
    )

    primary_category = serializers.PrimaryKeyRelatedField(
        allow_empty=True,
        allow_null=True,
        required=False,
        queryset=BusinessCategory.objects.all(),
    )


class OnlineBookingIntegrationSerializer(serializers.Serializer):
    partner = serializers.ChoiceField(choices=IntegrationPartner.choices())
    status = serializers.ChoiceField(choices=IntegrationStatus.choices())
    status_description = serializers.CharField()
    can_be_activated = serializers.BooleanField()


class ChangeOnlineBookingIntegrationStatusSerializer(serializers.Serializer):
    partner = serializers.ChoiceField(
        choices=[
            IntegrationPartner.RESERVE_WITH_GOOGLE,
        ]
    )


class BListingSearchRequestSerializer(serializers.Serializer):
    source_phone = BooksyCellPhoneCountrySpecificField(required=False)


class BusinessVisibleDelayRequestSerializer(serializers.Serializer):
    VISIBLE_DELAY_MAX_VALUE = 14

    visible_delay = serializers.IntegerField(
        default=0,
        min_value=0,
        max_value=VISIBLE_DELAY_MAX_VALUE,
    )


class PriceSerializer(serializers.Serializer):
    price = serializers.FloatField(default=Decimal)
    quantity = serializers.IntegerField(default=1)
    type = serializers.ChoiceField(choices=PriceType.choices())

    @staticmethod
    def validate_type(data):
        if data not in PriceType.values():
            raise ValidationError(f"Wrong price type {data}")
        return data


class ServicePriceSerializer(serializers.Serializer):
    prices = PriceSerializer(many=True, required=True)

    @staticmethod
    def calculate_final_price(data: list[dict]):
        if not data['prices']:
            return ServicePrice()

        return reduce(
            operator.iadd,
            [
                ServicePrice(value=price['price'] * price['quantity'], price_type=price['type'])
                for price in data['prices']
            ],
        )
