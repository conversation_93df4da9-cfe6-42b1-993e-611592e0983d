from urllib.parse import urljoin

import pytest
import responses
from django.conf import settings
from django.urls import reverse
from model_bakery import baker
from rest_framework import status

from drf_api.lib.base_drf_test_case import CustomerAPITestCase
from drf_api.tests.view_item_list_event_base import ViewItemListEventTestCaseMixin
from lib.baker_utils import get_or_create_booking_source
from lib.elasticsearch.consts import ESDocType, ESIndex
from lib.feature_flag.enums import ExperimentVariants
from lib.feature_flag.experiment.customer import SuggesterExperiments
from lib.feature_flag.feature import EnableSearchServiceInModernQueryHints
from lib.serializers import safe_get
from lib.test_utils import get_in_memory_img
from lib.tests.utils import override_eppo_feature_flag
from webapps.booking.models import BookingSources
from webapps.business.baker_recipes import (
    category_recipe,
    region_recipe,
    treatment_recipe,
)
from webapps.business.models.category import get_business_category_image_url
from webapps.consts import INTERNAL
from webapps.elasticsearch.elastic import ELASTIC
from webapps.elasticsearch.tests.elasticsearch_test_helpers import ElasticSearchTestCaseMixin
from webapps.images.enums import BusinessCategoryPlaceholderType
from webapps.images.models import BusinessPlaceholderImage
from webapps.kill_switch.models import KillSwitch
from webapps.user.baker_recipes import user_recipe

_SEARCH_SERVICE_SUGGESTIONS_URL = 'http://search_service:8080/v1/suggestions'


@pytest.mark.django_db
class ModernQueryHintsTestBase(CustomerAPITestCase, ElasticSearchTestCaseMixin):
    _FINGERPRINT = 'test-fingerprint'

    @classmethod
    def setUpTestData(cls):
        cls.url = reverse('modern_query_hints')
        cls.user = user_recipe.make()
        cls.region = region_recipe.make(latitude=0.0, longitude=0.0)
        cls.customer_booking_src = get_or_create_booking_source(
            app_type=BookingSources.CUSTOMER_APP,
            name=INTERNAL,
            api_key='customer_key',
        )

    def setUp(self) -> None:
        super().setUp()
        self.delete_documents()
        self.prevent_errors()

    def prevent_errors(self):
        self.fix_region()

    def fix_region(self):
        self.region.reindex()

    @classmethod
    def delete_documents(cls):
        cls.prepare_es_index(ESIndex.BUSINESS)
        cls.prepare_es_index(ESIndex.BUSINESS_CATEGORY)

    def get_response(self, **kwargs):
        args = self.get_default_args()
        args |= kwargs.pop('args', {})
        return self.client.get(self.url, args, **kwargs)

    def get_default_args(self):
        return {
            'text': 'foo',
            'location_id': self.region.id,
        }

    @staticmethod
    def run_indexing(business):
        index = ELASTIC.indices[ESIndex.BUSINESS]
        business_document = business.get_document()
        business_document.save()
        index.refresh()


class ModernQueryHintsTestCase(ModernQueryHintsTestBase):
    @responses.activate
    @override_eppo_feature_flag(
        {
            SuggesterExperiments.flag_name: ExperimentVariants.CONTROL,
            EnableSearchServiceInModernQueryHints.flag_name: True,
        }
    )
    def test_ok_control_group(self):
        responses.add(responses.GET, _SEARCH_SERVICE_SUGGESTIONS_URL, json={'suggestions': []})
        response = self.get_response()
        self._assert_response(response)

    @responses.activate
    @override_eppo_feature_flag(
        {
            SuggesterExperiments.flag_name: ExperimentVariants.VARIANT_A,
            EnableSearchServiceInModernQueryHints.flag_name: True,
        }
    )
    def test_ok_experiment_group(self):
        responses.add(responses.GET, _SEARCH_SERVICE_SUGGESTIONS_URL, json={'suggestions': []})
        response = self.get_response()
        self._assert_response(response)

    @responses.activate
    @override_eppo_feature_flag(
        {
            SuggesterExperiments.flag_name: ExperimentVariants.CONTROL,
            EnableSearchServiceInModernQueryHints.flag_name: True,
        }
    )
    def test_location_geo(self):
        responses.add(responses.GET, _SEARCH_SERVICE_SUGGESTIONS_URL, json={'suggestions': []})
        response = self.get_response(args={'location_geo': '54.66,55.55'})
        self._assert_response(response)

    @responses.activate
    @override_eppo_feature_flag(
        {
            SuggesterExperiments.flag_name: ExperimentVariants.CONTROL,
            EnableSearchServiceInModernQueryHints.flag_name: True,
        }
    )
    def test_ok_search_service_integration_fails(self):
        """Fallback to core"""
        responses.add(
            responses.GET,
            _SEARCH_SERVICE_SUGGESTIONS_URL,
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
        response = self.get_response()
        self._assert_response(response)

    def test_ok_search_service_integration_disabled(self):
        response = self.get_response()
        self._assert_response(response)

    def test_empty_query(self):
        response = self.client.get(self.url)
        self.assertEqual(status.HTTP_400_BAD_REQUEST, response.status_code)
        self.assertEqual(
            response.json()['errors'][0],
            {
                'field': 'text',
                'description': 'This field is required.',
                'code': 'required',
            },
        )

    def _assert_response(self, response):
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertTrue(
            set(response.json().keys()).issuperset(
                {
                    'suggestions',
                    'number_category_suggestions',
                }
            )
        )


class CategoriesInModernQueryHintsTestCase(ModernQueryHintsTestBase):
    def setUp(self):
        super().setUp()

        self.category = category_recipe.make(
            internal_name='foobar',
            slug='foobar',
            full_name='Foobar',
        )
        self.category.reindex(refresh_index=True)

        baker.make(
            BusinessPlaceholderImage,
            type=BusinessCategoryPlaceholderType.ICON_V2,
            category=self.category,
            image=get_in_memory_img(),
        )

    @responses.activate
    @override_eppo_feature_flag(
        {
            SuggesterExperiments.flag_name: ExperimentVariants.CONTROL,
            EnableSearchServiceInModernQueryHints.flag_name: True,
        }
    )
    def test_categories(self):
        """Search-service doesn't return suggestions of categories at this moment"""
        responses.add(responses.GET, _SEARCH_SERVICE_SUGGESTIONS_URL, json={'suggestions': []})

        response = self.get_response()
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertEqual(response.json()['number_category_suggestions'], 0)
        self.assertEqual(response.json()['suggestions'], [])

    @responses.activate
    @override_eppo_feature_flag(
        {
            SuggesterExperiments.flag_name: ExperimentVariants.CONTROL,
            EnableSearchServiceInModernQueryHints.flag_name: True,
        }
    )
    def test_categories_search_service_integration_fails(self):
        """Fallback to core"""
        responses.add(
            responses.GET,
            _SEARCH_SERVICE_SUGGESTIONS_URL,
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )

        response = self.get_response()
        self._assert_response(response)

    def test_categories_search_service_integration_disabled(self):
        response = self.get_response()
        self._assert_response(response)

    def _assert_response(self, response):
        self.assertEqual(status.HTTP_200_OK, response.status_code)

        expected_icon_v2_url = get_business_category_image_url(
            self.category.internal_name, BusinessCategoryPlaceholderType.ICON_V2
        )

        suggestions = response.json()['suggestions']
        categories_by_slug = {
            suggestion['query_parameters']['slug']: suggestion
            for suggestion in suggestions
            if suggestion['object_type'] == 'category'
        }

        self.assertEqual(response.json()['number_category_suggestions'], 1)
        self.assertDictEqual(
            categories_by_slug.get('foobar'),
            {
                'object_type': 'category',
                'label': self.category.full_name,
                'query_parameters': {
                    'id': self.category.id,
                    'name': self.category.full_name,
                    'slug': self.category.slug,
                    'type': self.category.type,
                    'icon_v2': expected_icon_v2_url,
                },
            },
        )


class BusinessesInModernQueryHintsTestCase(ModernQueryHintsTestBase):
    SUGGESTION = 'foobar'

    def setUp(self):
        super().setUp()
        self.create_doc(
            ESDocType.BUSINESS,
            id=1,
            name='my biz',
            slug='my-biz',
            sitemap_url='sitemap.url',
            thumbnail_photo='thumbnail_photo.url',
            is_b_listing=False,
            visible=True,
            hidden_in_search=False,
            business_location={'address': 'my address'},
            search_phrases=['foobar'],
            meta={'routing': 1, 'id': 'business:1'},
        )

    def test_business_suggestions_search_service_integration_disabled(self):
        response = self.get_response()
        self._assert_response(response=response)

    @responses.activate
    @override_eppo_feature_flag(
        {
            SuggesterExperiments.flag_name: ExperimentVariants.CONTROL,
            EnableSearchServiceInModernQueryHints.flag_name: True,
        }
    )
    def test_business_suggestions_search_service_integration_fails(self):
        """Fallback to core"""
        responses.add(
            responses.GET,
            _SEARCH_SERVICE_SUGGESTIONS_URL,
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
        response = self.get_response()
        self._assert_response(response=response)

    @override_eppo_feature_flag(
        {
            SuggesterExperiments.flag_name: '',
            EnableSearchServiceInModernQueryHints.flag_name: True,
        }
    )
    def test_business_suggestions_no_variant_assigned(self):
        """Fallback to core"""
        response = self.get_response()
        self._assert_response(response=response)

    @responses.activate
    @override_eppo_feature_flag(
        {
            SuggesterExperiments.flag_name: ExperimentVariants.CONTROL,
            EnableSearchServiceInModernQueryHints.flag_name: True,
        }
    )
    def test_business_suggestions_search_service_integration_control_group(self):
        """Suggestions of businesses are still provided by core"""
        responses.add(responses.GET, _SEARCH_SERVICE_SUGGESTIONS_URL, json={'suggestions': []})
        response = self.get_response()
        self._assert_response(response=response)

    @responses.activate
    @override_eppo_feature_flag(
        {
            SuggesterExperiments.flag_name: ExperimentVariants.VARIANT_A,
            EnableSearchServiceInModernQueryHints.flag_name: True,
        }
    )
    def test_business_suggestions_search_service_integration_experiment_group(self):
        """Suggestions of businesses are provided by search-service"""
        responses.add(
            responses.GET,
            _SEARCH_SERVICE_SUGGESTIONS_URL,
            json={
                'suggestions': [
                    {
                        'object_type': 'business',
                        'label': 'my biz',
                        'query_parameters': {
                            'id': 1,
                            'type': 'B',
                        },
                    }
                ]
            },
        )
        response = self.get_response()
        self._assert_response(response=response)

    def _assert_response(self, response):
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        business_suggestions = [
            suggestion
            for suggestion in response.json()['suggestions']
            if suggestion['object_type'] == 'business'
        ]
        self.assertListEqual(
            business_suggestions,
            [
                {
                    'object_type': 'business',
                    'label': 'my biz',
                    'query_parameters': {
                        'id': 1,
                        'thumbnail_photo': urljoin(
                            settings.MEDIA_URL, 'thumbnail_photo.url?size=100x100'
                        ),
                        'name': 'my biz',
                        'slug': 'my-biz',
                        'url': 'sitemap.url',
                        'address': 'my address',
                        'is_b_listing': False,
                    },
                },
            ],
        )


class TreatmentsInModernQueryHintsTestCase(ModernQueryHintsTestBase):
    def setUp(self):
        super().setUp()

        treatment = treatment_recipe.make(
            internal_name='foobar',
            slug='foobar',
            full_name='Foobar',
        )
        treatment.reindex(refresh_index=True)
        self.treatment_suggestion = {
            'object_type': 'treatment',
            'label': treatment.full_name,
            'query_parameters': {
                'id': treatment.id,
                'name': treatment.full_name,
                'slug': treatment.slug,
                'type': treatment.type,
                'icon_v2': '',
            },
        }

    def test_treatments_in_response_search_service_integration_disabled(self):
        response = self.get_response()
        self._assert_response(response=response)

    @responses.activate
    @override_eppo_feature_flag(
        {
            SuggesterExperiments.flag_name: ExperimentVariants.CONTROL,
            EnableSearchServiceInModernQueryHints.flag_name: True,
        }
    )
    def test_treatments_in_response_search_service_integration_fails(self):
        """Fallback to core"""
        responses.add(
            responses.GET,
            _SEARCH_SERVICE_SUGGESTIONS_URL,
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )

        response = self.get_response()
        self._assert_response(response=response)

    @override_eppo_feature_flag(
        {
            SuggesterExperiments.flag_name: '',
            EnableSearchServiceInModernQueryHints.flag_name: True,
        }
    )
    def test_treatments_in_response_no_variant_assigned(self):
        """Fallback to core"""
        response = self.get_response()
        self._assert_response(response=response)

    @responses.activate
    @override_eppo_feature_flag(
        {
            SuggesterExperiments.flag_name: ExperimentVariants.CONTROL,
            EnableSearchServiceInModernQueryHints.flag_name: True,
        }
    )
    def test_treatments_in_response_control_group(self):
        """Using search-service in control group"""

        responses.add(
            responses.GET,
            _SEARCH_SERVICE_SUGGESTIONS_URL,
            json={'suggestions': [self.treatment_suggestion]},
        )

        response = self.get_response()
        self._assert_response(response=response)

    @responses.activate
    @override_eppo_feature_flag(
        {
            SuggesterExperiments.flag_name: ExperimentVariants.VARIANT_A,
            EnableSearchServiceInModernQueryHints.flag_name: True,
        }
    )
    def test_treatments_in_response_experiment_group(self):
        """Using search-service by in experiment variant"""

        responses.add(
            responses.GET,
            _SEARCH_SERVICE_SUGGESTIONS_URL,
            json={'suggestions': [self.treatment_suggestion]},
        )

        response = self.get_response()
        self._assert_response(response=response)

    def _assert_response(self, response):
        self.assertEqual(status.HTTP_200_OK, response.status_code)

        self.assertEqual(response.json()['number_category_suggestions'], 1)
        self.assertDictEqual(
            response.json()['suggestions'][0],
            self.treatment_suggestion,
        )


class TreatmentHiddenFromHintsTest(ModernQueryHintsTestBase):
    def test_hiding_enabled_instance_visible(self):
        treatment = self.make_treatment(hidden=False)
        response = self.get_response()
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertTrue(self.is_treatment_in_response(treatment, response))

    def test_hiding_enabled_instance_hidden(self):
        treatment = self.make_treatment(hidden=True)
        response = self.get_response()
        self.assertEqual(status.HTTP_200_OK, response.status_code)
        self.assertFalse(self.is_treatment_in_response(treatment, response))

    @staticmethod
    def make_treatment(hidden):
        treatment = treatment_recipe.make(
            full_name='foo',
            hidden_from_business_search_hints=hidden,
        )
        treatment.reindex(refresh_index=True)
        return treatment

    @staticmethod
    def is_treatment_in_response(treatment, response):
        suggestions = response.json().get('suggestions', [])
        return any(
            safe_get(suggestion, ['query_parameters', 'id']) == treatment.id
            for suggestion in suggestions
        )


class ViewItemListEventTestCase(ViewItemListEventTestCaseMixin, ModernQueryHintsTestBase):
    item_list_id = 'Search_Hints'
    item_list_name = None
    expected_extra_event_params = {'search_query': 'foo'}

    def setUp(self):
        super().setUp()
        baker.make(
            KillSwitch,
            name=KillSwitch.MarTech.VIEW_ITEM_LIST_IN_QUERY_HINTS,
            is_killed=False,
        )

    def create_business(self, id_, manual_boost_score=0):
        self.create_doc(
            ESDocType.BUSINESS,
            id=id_,
            name='my biz',
            slug='my-biz',
            sitemap_url='sitemap.url',
            is_b_listing=False,
            visible=True,
            hidden_in_search=False,
            business_location={'address': 'my address'},
            suggest_businesses={'input': ['foobar'], 'contexts': {'business_categories': '*'}},
            manual_boost_score=manual_boost_score,
            search_phrases=['foobar'],
            meta={'routing': id_, 'id': f'business:{id_}'},
        )

    @property
    def request_args(self):
        return self.get_default_args()

    @staticmethod
    def results_are_not_empty(response):
        suggestions = response.json()['suggestions']
        return 'business' in {s['object_type'] for s in suggestions}
