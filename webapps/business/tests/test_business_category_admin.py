from django.contrib.auth.models import Group
from django.test import TestCase
from mock.mock import call, MagicMock, patch

from lib.rivers import River
from webapps.business._admin.views import BusinessCategoryAdmin
from webapps.business.baker_recipes import treatment_recipe
from webapps.business.models import BusinessCategory
from webapps.user.baker_recipes import user_recipe


class ChangePermissionTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        from service.management.commands.set_admin_permissions_from_intranet import Command

        super().setUpTestData()
        Command().manage_groups()

    def setUp(self) -> None:
        self.admin = BusinessCategoryAdmin(BusinessCategory, None)

    def test_superuser(self):
        user = self.make_user(is_superuser=True)
        self.assertTrue(self.has_permission(user))

    def test_other_user(self):
        user = self.make_user()
        self.assertFalse(self.has_permission(user))

    @staticmethod
    def make_user(**kwargs):
        user = user_recipe.make(**kwargs)
        group = Group.objects.get(name='it')
        user.groups.add(group)
        return user

    def has_permission(self, user):
        request = MagicMock(user=user.user_ptr)
        return self.admin.has_change_permission(request)


class ActionsTest(TestCase):
    def setUp(self) -> None:
        """Setting up objects at cls level raises TypeError
        TypeError: cannot pickle 'module' object
        """
        self.admin = BusinessCategoryAdmin(BusinessCategory, None)
        self.request = MagicMock(user=user_recipe.make)

    def test_hide_from_business_search_hints(self):
        treatment = treatment_recipe.make(hidden_from_business_search_hints=False)
        queryset = BusinessCategory.objects.filter(id=treatment.id)

        with (
            patch('lib.rivers.bump_document') as mock_bump_document,
            patch.object(BusinessCategoryAdmin, 'log_change') as mock_log_change,
            patch(
                'webapps.business.business_categories.cache.BusinessCategoryCache.cache_clear'
            ) as mock_cache_clear,
        ):
            self.admin.hide_from_business_search_hints(self.request, queryset)

        treatment.refresh_from_db()
        self.assertEqual(True, treatment.hidden_from_business_search_hints)
        mock_bump_document.assert_has_calls([self.get_bump_call(treatment)])
        mock_log_change.assert_called_once()
        mock_cache_clear.assert_called_once()

    def test_unhide_from_business_search_hints(self):
        treatment = treatment_recipe.make(hidden_from_business_search_hints=True)
        queryset = BusinessCategory.objects.filter(id=treatment.id)

        with (
            patch('lib.rivers.bump_document') as mock_bump_document,
            patch.object(BusinessCategoryAdmin, 'log_change') as mock_log_change,
            patch(
                'webapps.business.business_categories.cache.BusinessCategoryCache.cache_clear'
            ) as mock_cache_clear,
        ):
            self.admin.unhide_from_business_search_hints(self.request, queryset)

        treatment.refresh_from_db()
        self.assertEqual(False, treatment.hidden_from_business_search_hints)
        mock_bump_document.assert_has_calls([self.get_bump_call(treatment)])
        mock_log_change.assert_called_once()
        mock_cache_clear.assert_called_once()

    def test_exclude_from_auto_assignment(self):
        treatment = treatment_recipe.make(excluded_from_auto_assignment=False)
        queryset = BusinessCategory.objects.filter(id=treatment.id)

        with (
            patch('lib.rivers.bump_document') as mock_bump_document,
            patch.object(BusinessCategoryAdmin, 'log_change') as mock_log_change,
            patch(
                'webapps.business.business_categories.cache.BusinessCategoryCache.cache_clear'
            ) as mock_cache_clear,
        ):
            self.admin.exclude_from_auto_assignment(self.request, queryset)

        treatment.refresh_from_db()
        self.assertEqual(True, treatment.excluded_from_auto_assignment)
        mock_bump_document.assert_has_calls([self.get_bump_call(treatment)])
        mock_log_change.assert_called_once()
        mock_cache_clear.assert_called_once()

    def test_include_in_auto_assignment(self):
        treatment = treatment_recipe.make(excluded_from_auto_assignment=True)
        queryset = BusinessCategory.objects.filter(id=treatment.id)

        with (
            patch('lib.rivers.bump_document') as mock_bump_document,
            patch.object(BusinessCategoryAdmin, 'log_change') as mock_log_change,
            patch(
                'webapps.business.business_categories.cache.BusinessCategoryCache.cache_clear'
            ) as mock_cache_clear,
        ):
            self.admin.include_in_auto_assignment(self.request, queryset)

        treatment.refresh_from_db()
        self.assertEqual(False, treatment.excluded_from_auto_assignment)
        mock_bump_document.assert_has_calls([self.get_bump_call(treatment)])
        mock_log_change.assert_called_once()
        mock_cache_clear.assert_called_once()

    @staticmethod
    def get_bump_call(treatment):
        return call(
            River.BUSINESS_CATEGORY, treatment.id, _origin='signal:business.BusinessCategory'
        )
