import copy
import datetime
import os
import re
from collections import Counter, defaultdict

import braintree
from dal import autocomplete
from django import forms
from django.conf import settings
from django.contrib.admin import site, widgets
from django.contrib.admin.widgets import AdminRadioSelect
from django.db.models import Q
from django.db.models.fields.files import ImageFieldFile
from django.db.transaction import atomic
from django.forms import BaseInlineFormSet, DateField
from django.utils.html import format_html
from django.utils.translation import gettext as _

from lib.feature_flag.feature.admin import ShowPaidDuplicatedAccountCancellationReason
from lib.rivers import River, bump_document
from lib.tools import format_currency, tznow
from webapps.business._admin.widgets import BusinessGeopositionWidget, RentingVenueIdWidget
from webapps.business.business_visibility.exceptions import (
    BusinessVisibilityError,
    InvitesError,
    LastAvailableResourceError,
)
from webapps.business.business_visibility.validations import (
    InvitesValidation,
    check_errors_business_visibility_change,
)
from webapps.business.enums import (
    BoostPaymentSource,
    CancellationReasonType,
    CancellationType,
    CustomData,
)
from webapps.business.forms.fields import (
    FacebookInstagramContractorsWidgetField,
    IndependentContractorsField,
)
from webapps.business.models import (
    Business,
    BusinessCategory,
    BusinessPromotion,
    CancellationReason,
    ReTrialAttempt,
    Resource,
    Service,
    ServiceCategory,
    ServiceSuggestion,
    ServiceVariant,
    ServiceVariantPayment,
)
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.business.models.business_change import BusinessChange
from webapps.business.models.external import FacebookInstagramWidgetSettings
from webapps.business.serializers import (
    CancellationReasonSerializer,
)
from webapps.business.services.validations import (
    LastAvailableResourceValidation,
    LastAvailableServiceValidation,
)
from webapps.business.utils import validate_supported_image_format
from webapps.df_creator.admin.mixins import ValidateDigitalFlyerHashtagMixin
from webapps.images.enums import ImageTypeEnum, MAX_IMAGE_SIZE, SUPPORTED_EXTENSIONS
from webapps.images.models import Image as BusinessImage
from webapps.notification.enums import ScheduleState
from webapps.public_partners.models import PartnerPermissionBusiness
from webapps.navision.tasks.buyer_merchant_integration import sync_buyer_with_merchant_task
from webapps.structure.models import Region
from webapps.user.tools import get_system_user, get_umbrella_user, get_user_from_django_request
from webapps.wait_list.models import WaitListDisabled
from webapps.warehouse.models import Wholesaler
from webapps.zoom.utils import has_zoom_credentials


class QuestionsField(forms.CharField):
    max_questions = 3

    def prepare_value(self, value):
        if isinstance(value, (list, tuple)):
            return '\n'.join(value)
        return value

    def to_python(self, value):
        if isinstance(value, (list, tuple)):
            return value
        questions = [_f for _f in value.split('\n') if _f]
        return questions

    def validate(self, value):
        if len(value) > self.max_questions:
            raise forms.ValidationError('Max 3 questions are allowed')


class CancellationReasonForm(forms.ModelForm):
    class Meta:
        model = CancellationReason
        fields = (
            'business',
            'cancellation_date',
            'cancellation_reason',
            'cancellation_info',
            'left_for_competitor',
            'competitor_name',
        )

    def __init__(self, *args, **kwargs):
        self.operator = kwargs.pop('operator', None)
        super().__init__(*args, **kwargs)
        self.fields['cancellation_date'].input_formats = ['%Y-%m-%d']
        self.fields['cancellation_date'].label = 'Cancellation date (YYYY-MM-DD)'
        self.fields['cancellation_date'].required = True

        if not self.instance.id or (self.instance.churn_type == CancellationType.AUTOMATIC):
            self.fields['cancellation_reason'].required = True

        if not ShowPaidDuplicatedAccountCancellationReason():
            self.fields['cancellation_reason'].choices = [
                choice
                for choice in self.fields['cancellation_reason'].choices
                if choice[0] != CancellationReasonType.PAID_DUPLICATED_ACCOUNT.value
            ]

    business = forms.ModelChoiceField(
        queryset=Business.objects.all(),
        widget=widgets.ForeignKeyRawIdWidget(
            CancellationReason._meta.get_field('business').remote_field, site
        ),
    )

    left_for_competitor = forms.TypedChoiceField(
        choices=((0, 'No'), (1, 'Yes')),
        # choices values will be changed to str,
        # cast them into int instead of bool
        coerce=int,
        label='Competitor',
    )
    competitor_name = forms.CharField(
        required=False,
        label='Competitor name',
    )

    field_order = (
        'business',
        'cancellation_date',
        'left_for_competitor',
        'competitor_name',
        'cancellation_reason',
        'cancellation_comment',
    )

    def clean(self):
        cleaned_data = super().clean()

        if not self.errors:
            payload = copy.deepcopy(cleaned_data)
            payload['business'] = payload['business'].pk
            if self.operator:
                payload['operator'] = self.operator.pk

            serializer = CancellationReasonSerializer(data=payload)
            serializer.is_valid(raise_exception=False)

            for key, value in serializer.errors.items():
                self.add_error(key, value[0])
        return cleaned_data

    def save(self, **kwargs):  # pylint: disable=arguments-differ
        if not self.instance.id:
            # Doesn't work if set in clean()
            self.instance.churn_done = False

            if not self.cleaned_data.get('left_for_competitor'):
                self.instance.competitor_name = None
        self.instance.operator = self.operator
        return super().save(**kwargs)


class UndoChurnForm(forms.Form):
    business = forms.ModelChoiceField(
        queryset=Business.objects.all(),
        widget=widgets.ForeignKeyRawIdWidget(
            CancellationReason._meta.get_field('business').remote_field, site
        ),
    )


class ServiceAdminForm(forms.ModelForm):
    WARN_LAST_SERVICE = 'warn_last_service'
    questions = QuestionsField(
        widget=forms.Textarea(attrs={'rows': 3}),
        help_text='Each question on a new line. Max 3 questions',
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['treatment'] = forms.TypedChoiceField(
            coerce=int,
            widget=AdminRadioSelect(attrs={'class': 'radiolist'}),
            required=False,
        )

        treatment_category = defaultdict(list)
        filter_kwargs = {'visible_for_biz': True}
        if self.instance.treatment is not None:
            filter_kwargs['id'] = self.instance.treatment.id
        for treatment in BusinessCategory.objects.filter(
            Q(**filter_kwargs, _connector=Q.OR),
            type=BusinessCategory.TREATMENT,
        ).select_related('parent'):
            treatment_category[treatment.parent].append(treatment)

        choices = [(None, '-----')]
        for cat, treatments in list(treatment_category.items()):
            sub_cat = [cat and cat.name]
            sub_cat.append([])
            for treatment in treatments:
                sub_cat[-1].append([treatment.id, treatment.name])
            choices.append(sub_cat)
        self.fields['treatment'].choices = choices
        self.warn_last_service = 'warn_last_service'
        if 'description' in self.fields:
            self.fields['description'].widget = forms.Textarea()

    def clean_treatment(self):
        treatment = self.cleaned_data.get('treatment')
        if treatment:
            return BusinessCategory.objects.get(id=treatment)

    def clean_is_online_service(self):
        is_online_service = self.cleaned_data['is_online_service']

        zoom_credentials_exists = has_zoom_credentials(self.instance.business)
        if is_online_service and not zoom_credentials_exists:
            raise forms.ValidationError(
                'Business doesn\'t have Zoom API credentials configured. '
                'You can\'t mark service as online one.'
            )
        return is_online_service

    def add_warning_is_available_for_customer_booking(self):
        last_service_error_message = _(
            'Business has only one visible service for customers. '
            'Marking this service as unavailable for customers '
            'will make business disappear from marketplace. '
            'To mark service as unavailable anyway, please save again.'
        )
        self.add_error(
            'is_available_for_customer_booking',
            format_html(
                '{} <input type="hidden" id="warn-last-service" name={} value="0"/>',
                last_service_error_message,
                ServiceAdminForm.WARN_LAST_SERVICE,
            ),
        )

    def clean_is_available_for_customer_booking(self):
        is_available_for_customer_booking = self.cleaned_data['is_available_for_customer_booking']

        if (
            not is_available_for_customer_booking
            and self.instance.business.visible
            and ServiceAdminForm.WARN_LAST_SERVICE not in self.data
        ):
            try:
                LastAvailableServiceValidation(self.instance).validate()
            except BusinessVisibilityError:
                try:
                    InvitesValidation(self.instance.business).validate()
                except InvitesError as error:
                    raise forms.ValidationError(error.description)
                self.add_warning_is_available_for_customer_booking()

        return is_available_for_customer_booking


# pylint: disable=arguments-renamed
class GeopositionField(forms.MultiValueField):
    default_error_messages = {'invalid': _('Enter a valid geoposition.')}

    def __init__(self, *args, **kwargs):
        self.widget = BusinessGeopositionWidget()
        fields = (
            forms.DecimalField(label=_('latitude')),
            forms.DecimalField(label=_('longitude')),
        )
        if 'initial' in kwargs:
            kwargs['initial'] = kwargs['initial'].split(',')
        super().__init__(fields, **kwargs)

    def compress(self, value_list):  # pylint: disable=arguments-renamed
        if value_list:
            return value_list
        return ""


class BusinessForm(forms.ModelForm):
    _forbid_time_zone_change = True

    position = GeopositionField(required=False)

    if settings.COUNTRY_CONFIG.zipcode_regexp:
        zipcode = forms.RegexField(
            re.compile(settings.COUNTRY_CONFIG.zipcode_regexp, re.IGNORECASE),
            strip=True,
            required=False,
        )

    region = forms.ModelChoiceField(
        queryset=Region.objects.all(),
        widget=autocomplete.ModelSelect2(url='admin:region_autocomplete'),
        required=False,
    )
    seo_region = forms.ModelChoiceField(
        queryset=Region.objects.exclude(type=Region.Type.ZIP),
        widget=autocomplete.ModelSelect2(url='admin:seo_region_autocomplete'),
        required=False,
    )
    waitlist_disabled = forms.BooleanField(required=False)

    class Meta:
        model = Business
        exclude = []  # pylint: disable=modelform-uses-exclude

    def __init__(self, *args, **kwargs):
        """Limit choices to selected categories."""
        super().__init__(*args, **kwargs)

        if self.fields.get('invoice_address'):
            self.fields['invoice_address'].widget = forms.Textarea()

        if self.fields.get('renting_venue'):
            self.fields['renting_venue'].queryset = Business.objects.filter(
                status=Business.Status.VENUE,
                active=True,
            )

            self.fields['renting_venue'].widget = RentingVenueIdWidget(
                Business._meta.get_field('renting_venue').remote_field, site
            )

        self.initial['position'] = (
            f"{self.initial.get('latitude', 0)},{self.initial.get('longitude', 0)}"
        )

        if 'categories' in self.fields:
            self.fields['categories'].queryset = BusinessCategory.objects.filter(
                type=BusinessCategory.CATEGORY,
                deleted__isnull=True,
            )

        if 'primary_category' in self.fields:
            self.fields['primary_category'].queryset = BusinessCategory.objects.filter(
                type=BusinessCategory.CATEGORY,
                deleted__isnull=True,
            )

        if 'wholesalers' in self.fields:
            self.fields['wholesalers'].queryset = Wholesaler.objects.all()

        # new item
        if self.instance.id is None:
            # when creating renting venue business
            # set initial fields
            self.fields['active'].initial = True

        self.initial['waitlist_disabled'] = (
            self.instance.id is not None
            and WaitListDisabled.objects.filter(business_id=self.instance.id).exists()
        )

    def clean(self):
        from webapps.business.business_categories.cache import CategoryCache

        cleaned_data = super().clean()

        if cleaned_data.get('visible') and cleaned_data.get('custom_data', {}).get(
            CustomData.RESTRICTED_MARKETPLACE_VISIBILITY
        ):
            raise forms.ValidationError(
                _('Enabling Business visibility is not allowed.'),
                params={
                    'code': 'invalid',
                    'field': 'visible',
                    'type': 'validation',
                },
            )

        booking_max_lead_time = cleaned_data.get('booking_max_lead_time')
        booking_min_lead_time = cleaned_data.get('booking_min_lead_time')
        booking_max_modification_time = cleaned_data.get('booking_max_modification_time')

        if booking_max_lead_time is not None and booking_min_lead_time is not None:
            if not Business.check_booking_min_max_lead_times(
                booking_min_lead_time, booking_max_lead_time
            ):
                raise forms.ValidationError(
                    _('Min-Max lead times are incorrect'),
                    params={
                        'code': 'invalid',
                        'field': 'booking_min_lead_time,' 'booking_max_lead_time',
                        'type': 'validation',
                    },
                )

        if booking_max_lead_time is not None and booking_max_modification_time is not None:
            if not Business.check_booking_max_lead_modification_times(
                booking_max_lead_time, booking_max_modification_time
            ):
                raise forms.ValidationError(
                    _('Max lead & max modification times are incorrect'),
                    params={
                        'code': 'invalid',
                        'field': ('booking_max_lead_time,' 'booking_max_modification_time'),
                        'type': 'validation',
                    },
                )

        primary_category = cleaned_data.get('primary_category')

        other_category = CategoryCache.get_by_internal_name('Other')
        if other_category is not None:
            other_category_id = other_category.get('id')

            if primary_category and primary_category.id != other_category_id:
                cleaned_data['categories'] = [
                    c for c in cleaned_data.get('categories', []) if c.id != other_category_id
                ]
        return cleaned_data

    def clean_trial_till(self):
        trial_till = self.cleaned_data.get('trial_till')

        if 'trial_till' not in self.changed_data:
            return trial_till

        status = self.instance.status

        removed = trial_till is None and status == Business.Status.TRIAL
        in_past = trial_till is not None and trial_till < tznow()

        if removed or in_past:
            raise forms.ValidationError("Trial till date must be in the future.")

        return trial_till

    def clean_payment_source(self):
        payment_source = self.cleaned_data.get('payment_source')
        if (
            payment_source
            and self.instance.has_new_billing
            and payment_source != Business.PaymentSource.BRAINTREE_BILLING
        ):
            raise forms.ValidationError(
                "'Payment with' must be set to 'Booksy Billing' for a Business in a New Billing."
            )
        return payment_source

    def clean_moderation_status(self):
        moderation_status = self.cleaned_data.get('moderation_status')
        boost_payment_source = self.instance.boost_payment_source
        if (
            boost_payment_source == BoostPaymentSource.ONLINE
            and moderation_status
            in (
                Business.Moderation.MEDIUM,
                Business.Moderation.HIGH,
            )
            and moderation_status != self.instance.moderation_status
        ):
            from braintree.exceptions.braintree_error import BraintreeError

            braintree_timeout = braintree.Configuration.timeout
            try:
                braintree.Configuration.timeout = 3.0
                braintree.Customer.find(self.instance.external_api_id)
            except BraintreeError:
                raise forms.ValidationError(  # pylint: disable=raise-missing-from
                    "Cannot set Medium or High moderation status if user is"
                    " not conencted to braintree"
                )
            finally:
                braintree.Configuration.timeout = braintree_timeout
        return moderation_status

    def clean_boost_status(self):
        boost_status = self.cleaned_data.get('boost_status')
        if (
            boost_status in Business.BoostStatus.active_statuses()
            and self.instance.boost_status not in Business.BoostStatus.active_statuses()
        ):
            if not BusinessPromotion.start_boost(self.instance, dry_run=True):
                raise forms.ValidationError(
                    _("Business doesn't meet the requirements for promotion"),
                    params={
                        'code': 'invalid',
                        'field': 'type',
                        'type': 'validation',
                    },
                )
        return boost_status

    def clean_region(self):
        region = self.cleaned_data.get('region')
        params = {
            'type': 'validation',
            'code': 'invalid',
            'field': 'region',
        }
        if not region and self.instance.region:
            raise forms.ValidationError(
                _("This field is required"),
                params=params,
            )
        if (
            self._forbid_time_zone_change
            and region
            and self.instance.time_zone_name
            and self.instance.status != Business.Status.SETUP
            and self.instance.time_zone_name != region.time_zone_name
        ):
            raise forms.ValidationError(
                _("This region is in different time zone. Please use time zone change button."),
                params=params,
            )
        return region

    def save(self, commit=True):
        if 'region' in self.changed_data:
            region = self.cleaned_data['region']
            self.instance.time_zone_name = region.time_zone_name
        if 'visible' in self.changed_data and self.cleaned_data['visible'] is False:
            self.instance.delete_go_back_to_search_date()
        return super().save(commit)


class BusinessLocationForm(BusinessForm):
    _forbid_time_zone_change = False

    time_zone_name = forms.Field(disabled=True)
    shift_bookings = forms.TypedChoiceField(
        coerce=lambda x: x == 'True',
        label=_('time zone change behavior'),
        choices=(
            (True, _('adjust booking times to new time zone')),
            (False, _('keep booking times in old time zone')),
        ),
        widget=widgets.AdminRadioSelect,
        initial=True,
    )
    seo_region = None
    waitlist_disabled = None

    field_order = (
        'region',
        'time_zone_name',
        'shift_bookings',
        'address',
        'address2',
        'city',
        'zipcode',
        'position',
    )

    class Meta(BusinessForm.Meta):
        model = Business
        fields = (
            'region',
            'time_zone_name',
            'address',
            'address2',
            'city',
            'zipcode',
            'latitude',
            'longitude',
        )
        widgets = {
            'latitude': forms.HiddenInput(),
            'longitude': forms.HiddenInput(),
        }

    @atomic
    def save(self, commit=True):
        self.full_clean()
        region = self.cleaned_data['region']
        shift_bookings = self.cleaned_data['shift_bookings']

        self.instance.region = region
        if region and region.time_zone_name:
            self.instance.set_timezone(region.gettz(), shift_bookings=shift_bookings)

        return super().save(commit=commit)


class BusinessAdminForm(BusinessForm):
    allow_transform = forms.BooleanField(required=False)
    importer = forms.CharField(required=False)
    partner_integration_disabled = forms.BooleanField(required=False)

    class Meta:
        exclude = ()  # pylint: disable = modelform-uses-exclude

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        instance = kwargs.get('instance')
        if instance:
            self.fields['allow_transform'].initial = bool(
                instance.custom_data.get(
                    CustomData.CAN_TRANSFORM_INTO_B_LISTING,
                    True,
                )
            )

            self._init_partner_integration_disabled_field(instance)
            self._handle_visible_in_marketplace_field_details(instance)

    def _init_partner_integration_disabled_field(self, instance):
        permission = PartnerPermissionBusiness.all_objects.filter(business=instance).first()
        if permission:
            self.fields['partner_integration_disabled'].initial = bool(permission.deleted)
        else:
            self.fields['partner_integration_disabled'].widget.attrs['disabled'] = True

    def _handle_visible_in_marketplace_field_details(self, instance):
        visible_in_marketplace_value = instance.is_visible_in_marketplace
        help_texts = []
        errors = check_errors_business_visibility_change(
            business=instance,
            new_visibility_value=not visible_in_marketplace_value,
            admin_panel=True,
        )
        for error in errors:
            help_texts.append(str(error.description))
        if 'visible' in self.fields:
            if help_texts and not visible_in_marketplace_value:
                self.fields['visible'].help_text = '<br>'.join(
                    [
                        'Business not really visible because of some problems:<br>',
                        *help_texts,
                    ]
                )
            elif help_texts and visible_in_marketplace_value:
                self.fields['visible'].help_text = '<br>'.join(
                    [
                        "Business can't go invisible because of some problems:<br>",
                        *help_texts,
                    ]
                )

    def clean_buyer(self):
        buyer = self.cleaned_data.get('buyer')
        instance = getattr(self, 'instance', None)
        if instance and instance.pk and instance.buyer and buyer is None:
            raise forms.ValidationError('You cannot delete the buyer from a business!')
        return buyer

    def save(self, commit=True):
        BusinessPromotion.cache_promotion_updater(
            self.instance.id,
            BusinessPromotion.BY_ADMIN,
        )

        previous_buyer_id = None
        if previous_state := Business.objects.filter(id=self.instance.id).first():
            previous_buyer_id = previous_state.buyer_id

        if previous_buyer_id != self.instance.buyer_id:
            if previous_buyer_id is not None:
                sync_buyer_with_merchant_task.apply_async(
                    args=(previous_buyer_id,),
                    countdown=10,
                )
            if self.instance.buyer_id is not None:
                sync_buyer_with_merchant_task.apply_async(
                    args=(self.instance.buyer_id,),
                    countdown=10,
                )

        instance = super().save(commit=False)

        if 'importer' in self.cleaned_data:
            if importer := self.cleaned_data['importer']:
                instance.integrations['importer'] = importer
            elif 'importer' in instance.integrations:
                del instance.integrations['importer']

        self._process_partner_integration_disabled_field(instance)

        def _set_can_transform_into_b_listing():
            custom_data = instance.custom_data
            custom_data[CustomData.CAN_TRANSFORM_INTO_B_LISTING] = self.cleaned_data[
                'allow_transform'
            ]
            instance.custom_data = custom_data

        custom_data_dirty = instance.get_dirty_fields().get('custom_data')
        can_transform = instance.custom_data.get(
            CustomData.CAN_TRANSFORM_INTO_B_LISTING,
        )
        if custom_data_dirty is not None:
            can_transform_dirty = custom_data_dirty.get(CustomData.CAN_TRANSFORM_INTO_B_LISTING)
            if can_transform_dirty == can_transform:
                _set_can_transform_into_b_listing()
        else:
            _set_can_transform_into_b_listing()
        instance.save()
        self._handle_change_custom_data_booking_remind_before(instance, custom_data_dirty)
        return instance

    def _process_partner_integration_disabled_field(self, instance):
        if 'partner_integration_disabled' not in self.cleaned_data:
            return

        permission = PartnerPermissionBusiness.all_objects.filter(business=instance).first()
        if not permission:
            return

        if self.cleaned_data['partner_integration_disabled']:
            permission.soft_delete()
        else:
            permission.soft_undelete()

    @staticmethod
    def _handle_change_custom_data_booking_remind_before(
        instance: Business,
        custom_data_dirty: dict,
    ):
        from webapps.notification.models import NotificationSchedule

        if not custom_data_dirty:
            return

        new_value = instance.custom_data.get(CustomData.BOOKING_REMIND_BEFORE)
        old_value = custom_data_dirty.get(CustomData.BOOKING_REMIND_BEFORE, 24)
        if new_value is not None and new_value != old_value:
            all_future_appointments_data = instance.appointments.filter(
                booked_from__gte=tznow(),
            ).values('id', 'booked_from')
            reminders_reschedule_data = {}
            for appointment_data in all_future_appointments_data:
                task_id = (
                    f"booking_changed:customer_booking_reminder"
                    f":appointment_id={appointment_data['id']}"
                )
                new_scheduled = appointment_data['booked_from'] - datetime.timedelta(
                    hours=new_value
                )
                reminders_reschedule_data[task_id] = new_scheduled
            scheduled_reminders = NotificationSchedule.objects.filter(
                task_id__in=reminders_reschedule_data.keys(),
                state=ScheduleState.PENDING,
            )
            for scheduled_reminder in scheduled_reminders:
                new_scheduled = reminders_reschedule_data.get(scheduled_reminder.task_id)
                if not new_scheduled:
                    continue
                scheduled_reminder.scheduled = new_scheduled

            instance.custom_data.setdefault(CustomData.REMINDER_HOUR_CHANGE_OLD_VALUE, {})[
                tznow().isoformat()
            ] = old_value
            instance.save(update_fields=['custom_data', 'updated'])
            NotificationSchedule.objects.bulk_update(scheduled_reminders, ['scheduled'])


class BusinessCategoryForm(forms.ModelForm):
    questions = QuestionsField(
        widget=forms.Textarea(attrs={'rows': 3}),
        help_text='Each question on a new line. Max 3 questions',
    )
    visible_for_customer = forms.BooleanField(label='Visible for customer', required=False)

    class Meta:
        model = BusinessCategory
        exclude = []  # pylint: disable=modelform-uses-exclude

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.business_category_id = kwargs['instance'].id
        self.fields['emoji'].strip = False
        # pylint: disable=simplifiable-if-expression
        self.fields['visible_for_customer'].initial = (
            True if kwargs['instance'].deleted is None else False
        )
        # self.fields['parent'].widget.can_add_related = False
        # self.fields['parent'].queryset = \
        #     BusinessCategory.objects.filter(type=BusinessCategory.CATEGORY)

    def clean(self):
        cleaned_data = super().clean()

        business_type = cleaned_data.get('type')
        parent = cleaned_data.get('parent')
        if not (business_type and parent):
            return cleaned_data

        business_category = BusinessCategory.objects.get(id=self.business_category_id)

        if business_type == BusinessCategory.TREATMENT and business_category.children.count() > 0:
            raise forms.ValidationError(
                _('Business Category with treatments cannot become treatment'),
                params={'code': 'invalid', 'field': 'type', 'type': 'validation'},
            )

        if business_type == BusinessCategory.CATEGORY and parent is not None:
            raise forms.ValidationError(
                _('If type "Category" selected there is no parent allowed'),
                params={
                    'code': 'invalid',
                    'field': 'type,parent',
                    'type': 'validation',
                },
            )

        if business_type == BusinessCategory.TREATMENT and parent is None:
            raise forms.ValidationError(
                _('If type "Treatment" selected there must be also selected parent'),
                params={
                    'code': 'invalid',
                    'field': 'type,parent',
                    'type': 'validation',
                },
            )

        return cleaned_data

    def save(self, commit=True):
        if 'visible_for_customer' in self.changed_data:
            self.changed_data.append('deleted')
            if self.cleaned_data['visible_for_customer']:
                self.instance.deleted = None
            else:
                self.instance.deleted = tznow()
        instance = super().save(commit)
        return instance


class BusinessCustomerInfoForm(forms.ModelForm):
    business_secret_note = forms.CharField(widget=forms.Textarea, required=False)

    class Meta:
        model = BusinessCustomerInfo
        exclude = []  # pylint: disable=modelform-uses-exclude


class RentingVenueForm(BusinessForm):
    _forbid_time_zone_change = False

    contractors = IndependentContractorsField(
        help_text=_('Fill in form business_id e.g.: 1234, 1456, 2454'), required=False
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance.id is not None:
            # set initial value
            qs = self.instance.contractors.values_list('id', flat=True).distinct()
            self.fields['contractors'].initial = ', '.join([str(item) for item in qs])
            self.init_contractors = qs

        for field_name in (
            field_name for field_name in ('region', 'address') if field_name in self.fields
        ):
            self.fields[field_name].required = True

        if 'categories' in self.fields:
            self.fields['categories'].queryset = BusinessCategory.objects.filter(
                type=BusinessCategory.CATEGORY,
                deleted__isnull=True,
            )

        if 'primary_category' in self.fields:
            self.fields['primary_category'].queryset = BusinessCategory.objects.filter(
                type=BusinessCategory.CATEGORY,
                deleted__isnull=True,
            )

    @staticmethod
    def create_business_change(contractors_ids, operator, old_obj_vars, new_obj_vars):
        # write change for each value
        BusinessChange.bulk_change(
            business_ids=contractors_ids,
            operator=operator,
            old_obj_vars=old_obj_vars,
            new_obj_vars=new_obj_vars,
        )

    @classmethod
    def delete_contractors(cls, contractors_ids, request):
        business_values = ('renting_venue_id',)
        # extract old values
        old_obj_vars = BusinessChange.extract_old_values_business(
            contractors_ids,
            business_values,
        )
        # form new values
        new_obj_vars = {
            'default': {
                'renting_venue_id': None,
            }
        }
        Business.objects.filter(id__in=contractors_ids).update(renting_venue_id=None)
        # write change for each value
        cls.create_business_change(
            contractors_ids=contractors_ids,
            operator=get_user_from_django_request(request),
            old_obj_vars=old_obj_vars,
            new_obj_vars=new_obj_vars,
        )

    @classmethod
    def add_contractors(cls, contractors_ids, request, renting_venue_id):
        business_values = ('renting_venue_id',)
        # extract old values
        old_obj_vars = BusinessChange.extract_old_values_business(
            contractors_ids,
            business_values,
        )
        # form new values
        new_obj_vars = {
            'default': {
                'renting_venue_id': renting_venue_id,
            }
        }
        Business.objects.filter(id__in=contractors_ids).update(renting_venue_id=renting_venue_id)
        # write change for each value
        cls.create_business_change(
            contractors_ids=contractors_ids,
            operator=get_user_from_django_request(request),
            old_obj_vars=old_obj_vars,
            new_obj_vars=new_obj_vars,
        )

    def clean(self):
        cleaned_data = super().clean()

        primary_category = cleaned_data.get('primary_category')
        if not primary_category:
            raise forms.ValidationError(
                _('Primary category must be selected.'),
                params={
                    'code': 'invalid',
                    'field': 'primary_category',
                    'type': 'validation',
                },
            )
        return cleaned_data

    def save(self, commit=True):
        created = False
        instance = super().save(commit=False)
        if instance.id is None:
            # overwrite default values
            instance.status = Business.Status.VENUE
            instance.booking_mode = Business.BookingMode.AUTO
            instance.owner = get_umbrella_user()
            created = True

        # always save umbrella no mater what
        instance.save()

        # save all updated contractors
        contractors_ids = self.cleaned_data.get('contractors', [])
        updated_data = self.cleaned_data
        if created:
            # this is done also in RentingVenueAdmin.save_related(),
            # but we need it sooner
            if contractors_ids:
                self.add_contractors(
                    contractors_ids=contractors_ids,
                    request=self.request,
                    renting_venue_id=instance.id,
                )
                # copy region from first contractor
                first_contractor = Business.objects.get(id=contractors_ids[0])
                updated_data['latitude'] = first_contractor.latitude
                updated_data['longitude'] = first_contractor.longitude

                instance.latitude = first_contractor.latitude
                instance.longitude = first_contractor.longitude

                all_categories_ids = (
                    Business.objects.filter(id__in=contractors_ids)
                    .values_list('categories', flat=True)
                    .distinct()
                )
                categories = BusinessCategory.objects.filter(id__in=all_categories_ids)
                # pylint: disable=unnecessary-comprehension
                instance.categories.add(*[category for category in categories])
                instance.recalculate_umbrella_rank()
            else:
                # no contractors provided
                if instance.latitude is None or instance.longitude is None:
                    region = instance.region
                    if region:
                        updated_data['latitude'] = region.latitude
                        updated_data['longitude'] = region.longitude
                        instance.latitude = region.latitude
                        instance.longitude = region.longitude

            instance.save()

            BusinessChange.add(
                business=instance,
                obj=updated_data,
                old_obj_vars={},
                operator=get_user_from_django_request(self.request),
            )

        else:
            set_contractors = set(contractors_ids)
            initial_set_contractors = set(self.init_contractors or [])

            added_contractors = set_contractors - initial_set_contractors
            deleted_contractors = initial_set_contractors - set_contractors
            updated_contractors = initial_set_contractors ^ set_contractors
            if len(added_contractors):
                self.add_contractors(
                    contractors_ids=added_contractors,
                    request=self.request,
                    renting_venue_id=instance.id,
                )
                instance.add_contractors_categories(added_contractors)
            if len(deleted_contractors):
                self.delete_contractors(
                    contractors_ids=deleted_contractors,
                    request=self.request,
                )
                instance.remove_contractors_categories(deleted_contractors)
            if len(updated_contractors):
                instance.recalculate_umbrella_rank()
        return instance


class BListingForm(BusinessForm):
    _forbid_time_zone_change = False

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field_name in [
            field_name
            for field_name in ('phone', 'primary_category', 'source_email', 'region', 'address')
            if field_name in self.fields
        ]:
            self.fields[field_name].required = True
        del self.fields['zipcode']

    def save(self, commit=True):
        instance = super().save(commit=False)
        if instance.id is None:
            # overwrite default values
            instance.status = Business.Status.B_LISTING
            instance.active = True
            instance.visible = True
            instance.owner = get_system_user()
            instance.booking_mode = Business.BookingMode.AUTO
        instance.save()
        return instance


class HoursFormField(forms.CharField):
    def prepare_value(self, value):
        if isinstance(value, list):
            return ', '.join(
                [
                    f"{hour[0].strftime(settings.TIME_FORMAT)}-"
                    f"{hour[1].strftime(settings.TIME_FORMAT)}"
                    for hour in value
                ]
            )
        return value

    def to_python(self, value):
        if value in self.empty_values:
            return []

        if isinstance(value, (bytes, str)):
            parts = [p.strip() for p in value.split(',')]
            try:
                hours = list(map(self._parse_hour, parts))
            except:
                raise forms.ValidationError(  # pylint: disable=raise-missing-from
                    'Wrong value format: should be comma separated list of from-till pairs',
                    code='invalid',
                )
            return hours
        return value

    @staticmethod
    def _parse_hour(value):
        hour_from, hour_till = [p.strip() for p in value.split('-')]
        return [
            datetime.datetime.strptime(hour_from, settings.TIME_FORMAT).time(),
            datetime.datetime.strptime(hour_till, settings.TIME_FORMAT).time(),
        ]


class WeekHoursForm(forms.ModelForm):
    class Meta:
        fields = (
            'valid_from',
            'monday',
            'tuesday',
            'wednesday',
            'thursday',
            'friday',
            'saturday',
            'sunday',
        )

    valid_from = DateField(required=True)
    monday = HoursFormField(required=False)
    tuesday = HoursFormField(required=False)
    wednesday = HoursFormField(required=False)
    thursday = HoursFormField(required=False)
    friday = HoursFormField(required=False)
    saturday = HoursFormField(required=False)
    sunday = HoursFormField(required=False)


class ServiceVariantPaymentForm(forms.ModelForm):
    class Meta:
        model = ServiceVariantPayment
        fields = ['payment_type', 'payment_amount']

    def clean(self):
        clean_data = super().clean()
        payment_amount = clean_data.get('payment_amount')
        minimal_payment_country = settings.MINIMAL_POS_PAY_BY_APP_PAYMENT
        minimal_payment = minimal_payment_country.get(
            self.instance.country_code, minimal_payment_country.get('default')
        )
        if payment_amount and payment_amount < minimal_payment:
            raise forms.ValidationError(
                {
                    'payment_amount': _('Payment amount can be less then {}').format(
                        format_currency(minimal_payment)
                    )
                }
            )
        return clean_data


class ServiceVariantAdminForm(forms.ModelForm):
    class Meta:
        model = ServiceVariant
        fields = '__all__'


class ResourceModelForm(forms.ModelForm):
    class Meta:
        model = Resource
        fields = '__all__'

    def clean_services(self):
        services = self.cleaned_data['services']
        service_ids = set(services.values_list('id', flat=True))
        active_business_services = set(
            self.instance.business.services.filter(
                active=True,
                deleted__isnull=True,
            ).values_list(
                'id',
                flat=True,
            )
        )
        services_out_of_business = service_ids - active_business_services
        if services_out_of_business:
            msg = ", ".join(map(str, services_out_of_business))
            raise forms.ValidationError(
                f'Staffer can\'t perform services: {msg} ' '(inactive/from other business)'
            )
        return services

    def clean(self):
        cleaned_data = super().clean()

        staff_user = cleaned_data['staff_user']

        owner_of_other_business = (
            Business.objects.filter(
                owner=staff_user,
            )
            .exclude(
                id=self.instance.business_id,
            )
            .exists()
        )

        assigned_to_different_resource = (
            Resource.objects.filter(
                staff_user=staff_user,
                business_id=self.instance.business_id,
                active=True,
                deleted__isnull=True,
            )
            .exclude(
                id=self.instance.pk,
            )
            .exists()
        )

        if staff_user:
            if not owner_of_other_business and assigned_to_different_resource:
                raise forms.ValidationError(
                    {'staff_user': _('Selected user is already set as a different staffer')}
                )

            if cleaned_data['staff_email'] != staff_user.email:
                raise forms.ValidationError(
                    {'staff_email': _('Staff and user email must be the same')}
                )

    def save(self, commit=True):
        fields = {'staff_email', 'staff_user', 'staff_access_level'}
        changed_fields = set(self.changed_data)

        if fields.intersection(changed_fields):
            if self.instance.staff_user:
                self.instance.staff_user.delete_all_user_sessions()
        elif 'active' in changed_fields and self.cleaned_data['active']:
            self.instance.deleted = None

        if self.cleaned_data['visible'] is False:
            try:
                LastAvailableResourceValidation(self.instance).validate()
            except LastAvailableResourceError:
                self.instance.business.go_offline()

        instance = super().save(commit)
        return instance


class ServiceSuggestionForm(forms.ModelForm):
    class Meta:
        model = ServiceSuggestion
        fields = [
            'deleted',
            'treatment',
            'name',
            'duration',
            'price',
            'tax_rate',
            'popularity',
        ]

    treatment = forms.ModelChoiceField(
        queryset=BusinessCategory.objects.filter(
            type=BusinessCategory.TREATMENT,
        ),
        widget=autocomplete.ModelSelect2(url='admin:treatment_autocomplete'),
    )


class BusinessPhotoForm(forms.ModelForm):
    class Meta:
        model = BusinessImage
        fields = ['image', 'order', 'category', 'is_cover_photo']

    image = forms.FileField(widget=forms.ClearableFileInput())

    def clean(self):
        cleaned_data = super().clean()
        # Backward compatibility: creating photo in COVER category, changes
        # category to BIZ_PHOTO and sets is_cover_photo flag
        if cleaned_data and cleaned_data.get('category') == ImageTypeEnum.COVER_DEPRECATED:
            cleaned_data['category'] = ImageTypeEnum.BIZ_PHOTO
            cleaned_data['is_cover_photo'] = True

        if (
            cleaned_data
            and cleaned_data.get('is_cover_photo')
            and cleaned_data.get('category') != ImageTypeEnum.BIZ_PHOTO
        ):
            raise forms.ValidationError(
                'Only image from BIZ_PHOTO category may be set as cover photo'
            )

        return cleaned_data


class BListingServiceForm(forms.ModelForm):
    class Meta:
        model = Service
        fields = ['name', 'description', 'service_category', 'treatment']

    treatment = forms.ModelChoiceField(
        required=False,
        queryset=BusinessCategory.objects.filter(type=BusinessCategory.TREATMENT),
        widget=autocomplete.ModelSelect2(url='admin:treatment_autocomplete'),
    )


class CancellationReasonInlineForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance and self.instance.pk and self.instance.churn_done:
            for field in self.fields.keys():
                self.fields[field].disabled = True

    class Meta:
        model = CancellationReason
        fields = (
            'business',
            'cancellation_date',
            'cancellation_reason',
            'cancellation_info',
            'competitor_name',
            'deleted',
            'churn_done',
        )

    competitor_name = forms.CharField(
        required=False,
        label='Competitor name',
    )

    def clean(self):
        competitor_name = self.cleaned_data.get('competitor_name')
        # Treat empty name as no competitor
        if competitor_name is not None and not competitor_name:
            self.cleaned_data['competitor_name'] = None
        if (
            self.instance.pk
            and self.instance.business.has_new_billing
            and (
                self.instance.cancellation_date.replace(microsecond=0)
                != self.cleaned_data.get('cancellation_date')
            )
        ):
            raise forms.ValidationError(
                {
                    'cancellation_date': (
                        "Can't change the cancellation date for a business "
                        "with a new billing. Use 'Undo churn' option."
                    )
                }
            )


class SalonNetworkInlineFormSet(BaseInlineFormSet):
    def delete_existing(self, obj, commit=True):
        super().delete_existing(obj, commit=True)

    def save_new_objects(self, commit=True):
        return super().save_new_objects(commit=True)


class DigitalFlyerHashtagInlineFormset(
    ValidateDigitalFlyerHashtagMixin,
    forms.BaseInlineFormSet,
):
    def clean(self):
        if hasattr(self, 'cleaned_data'):
            for inline_row_data in self.cleaned_data:
                name = inline_row_data.get('name')
                category = inline_row_data.get('category')
                self.validate_name(name)
                if not inline_row_data.get('id'):
                    self.validate_name_in_cat(name, category)
        return super().clean()


class BusinessPhotoInlineFormSet(BaseInlineFormSet):
    def clean(self):
        super().clean()
        # aliases
        logo = ImageTypeEnum.LOGO
        original_cover = ImageTypeEnum.COVER_ORIGINAL_DEPRECATED
        # counter
        counter = Counter({key: 0 for key in [logo, original_cover]})
        mapping = {
            logo: 'Logo',
            original_cover: _('Original Cover Image'),
        }
        cover_photo_count = 0
        for form in self.forms:
            category = form.cleaned_data.get('category')
            if category in counter:
                counter[category] += 1
            is_cover_photo = form.cleaned_data.get('is_cover_photo')
            if is_cover_photo:
                cover_photo_count += 1

            image_file = form.cleaned_data.get('image')
            if (
                image_file and image_file.file and not isinstance(image_file, ImageFieldFile)
            ):  # ImageFieldFile - already existing photos
                valid_image_format = validate_supported_image_format(image_file)
                if not valid_image_format:
                    joined_extensions = ', '.join(SUPPORTED_EXTENSIONS)
                    raise forms.ValidationError(
                        f'Invalid image format or the real image format'
                        f' is not equal to the file extension. '
                        f'Supported extensions are {joined_extensions}'
                    )
                if image_file.size > MAX_IMAGE_SIZE:
                    raise forms.ValidationError('The file is too big - the max file size is 20 MB')

        if cover_photo_count > 1:
            raise forms.ValidationError('Only one photo may be marked as cover')

        for counter_key, count in list(counter.items()):
            if count > 1:
                m_type = mapping[counter_key]
                message = _('Only one image of type "{}" is allowed').format(m_type)
                raise forms.ValidationError(message)

    def create_new_instance(self, form, business_id):
        if not form.cleaned_data.get('image'):
            return
        try:
            image = self._get_image(form.cleaned_data['image'].file)
        except ValueError:
            return

        instance = BusinessImage()
        business = form.cleaned_data['business']
        instance.business = business
        instance.category = form.cleaned_data['category']
        instance.is_cover_photo = form.cleaned_data.get('is_cover_photo', False)
        instance.order = form.cleaned_data['order']
        instance.active = True
        file_name, _original_path = self.create_path(
            form.cleaned_data['category'],
            business_id,
        )
        # always use rgb
        instance.image.name = file_name
        image = image.convert('RGB')
        # save image
        image.save(open(instance.image.path, 'wb'))  # pylint: disable=consider-using-with
        if settings.FILE_UPLOAD_PERMISSIONS:
            os.chmod(instance.image.path, settings.FILE_UPLOAD_PERMISSIONS)

        # save the model
        # and reindex
        instance.save()
        if instance.is_cover_photo:
            BusinessImage.objects.filter(
                business_id=business_id,
                is_cover_photo=True,
            ).exclude(
                id=instance.id,
            ).update(
                is_cover_photo=False,
            )
        return instance

    def save_new_objects(self, commit=True):
        saved_instances = super().save_new_objects(commit)
        bump_document(River.IMAGE, [instance.id for instance in saved_instances])
        return saved_instances

    def save_existing_objects(self, commit=True):
        saved_instances = super().save_existing_objects(commit)
        bump_document(River.IMAGE, [instance.id for instance in saved_instances])
        return saved_instances


class MarketplaceBusinessFormSet(BaseInlineFormSet):
    """
    Created to save history
    """

    def clean(self):
        super().clean()
        today = tznow().date()
        form = self.forms[0]
        fields = ('planned_boost_deactivate_date', 'planned_boost_activate_date')
        for field in set(form.changed_data) & set(fields):
            if date_value := form.cleaned_data.get(field):
                if date_value <= today:
                    raise forms.ValidationError("The date has to be in the future")
            if field == 'planned_boost_activate_date':
                if not BusinessPromotion.start_boost(self.instance, dry_run=True):
                    raise forms.ValidationError("Boost cannot be enabled")
            if form.cleaned_data.get(fields[0]) and form.cleaned_data.get(
                fields[0]
            ) == form.cleaned_data.get(fields[1]):
                raise forms.ValidationError("Dates cannot be the same")

        return self.cleaned_data


class FacebookInstagramWidgetSettingsFormSet(BaseInlineFormSet):
    """
     We need this class because somebody override
    BusinessAdmin.save_formset method but we want to use original logic
    """


class FacebookInstagramWidgetSettingsForm(forms.ModelForm):
    class Meta:
        model = FacebookInstagramWidgetSettings
        fields = ('facebook_businesses', 'instagram_businesses')

    facebook_businesses = FacebookInstagramContractorsWidgetField(
        required=False,
        help_text=(
            'Enter businesses ids in a format:'
            ' "business_id, other_business_id...",'
            ' example: "1234, 4321, 5463"'
        ),
    )
    instagram_businesses = FacebookInstagramContractorsWidgetField(
        required=False,
        help_text=(
            'Enter businesses ids in a format:'
            ' "business_id, other_business_id...",'
            ' example: "1234, 4321, 5463"'
        ),
    )


class ServiceCategoryForm(forms.ModelForm):
    class Meta:
        model = ServiceCategory
        fields = (
            'id',
            'business',
            'name',
            'order',
            'show_first',
            'import_uid',
        )

    def clean(self):
        cleaned_data = super().clean()
        service_category = (
            ServiceCategory.objects.filter(
                business=self.instance.business_id,
                order=cleaned_data['order'],
            )
            .exclude(id=self.instance.id)
            .first()
        )
        if service_category:
            raise forms.ValidationError(f"{service_category} has the same order value")
        return cleaned_data


class ReTrialAttemptForm(forms.ModelForm):
    class Meta:
        model = ReTrialAttempt
        fields = ('business',)

    def clean(self):
        super().clean()
        business = self.cleaned_data.get('business')

        if business and business.status != Business.Status.TRIAL_BLOCKED:
            raise forms.ValidationError(
                f'You can add re Trial attempt only for business with status '
                f'Trial End Blocked, (current status: {business.get_status_display()})'
            )
