from typing import Optional, Protocol, Union


class CoordinationLike(Protocol):
    latitude: float
    longitude: float


class VenueCoordinationLike(Protocol):
    lat: float
    lon: float


class VenueLocationLike(Protocol):
    address: str
    address2: Optional[str]
    city: str
    zipcode: str
    coordinate: VenueCoordinationLike


class MetaLike(Protocol):
    score: Optional[float]
    id: Union[str, int, None]
    item_no: Optional[int]


class LocationLike(Protocol):
    zipcode: str
    coordinate: CoordinationLike
    address: str
    city: str


class LastUpdatedLike(Protocol):
    contractor_id: Optional[int]
    contractor_name: Optional[str]
    updated: Optional[object]


class VenueLikeHit(Protocol):
    id: Union[int, str]
    name: str
    cover_photo: Optional[str]
    thumbnail_photo: Optional[str]
    venue_location: VenueLocationLike
    meta: MetaLike
    last_updated: Optional[LastUpdatedLike]
    location: LocationLike
