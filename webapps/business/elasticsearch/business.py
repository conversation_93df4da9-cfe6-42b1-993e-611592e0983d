from copy import deepcopy
from itertools import chain
from unicodedata import normalize

import elasticsearch_dsl as dsl
from django.conf import settings
from django.db.models import Prefetch
from django.db.models.signals import m2m_changed, post_delete, post_save, pre_delete
from rest_framework import serializers

from country_config import Country
from lib.elasticsearch import analyzers as a
from lib.elasticsearch import fields as f
from lib.elasticsearch.consts import (
    MARKET_PLACE_PROMOTION,
    MARKET_PLACE_PROMOTION_VARIANT_A,
    MARKET_PLACE_PROMOTION_VARIANT_B,
    MARKET_PLACE_PROMOTION_VARIANT_C,
    SECONDARY_CATEGORY_SCRIPT,
    UTT_SCORE_SCRIPT,
    ESDocType,
)
from lib.elasticsearch.document import Document
from lib.elasticsearch.index import Index
from lib.elasticsearch.serializers import (
    BusinessCoordinatesSerializer,
    BusinessSuggestField,
    CoordinatesSerializer,
    DefaultZeroFloatField,
)
from lib.feature_flag.adapter import UserData
from lib.feature_flag.feature import (
    BooksyGiftcardsEnabledFlag,
    ShowBooksyGiftCardsForChosenProvidersFlag,
)
from lib.geocoding.primitives import Circle, circle_to_geojson
from lib.geocoding.utils import miles_to_km
from lib.rivers import River, bump_on_changed_fields, bump_on_signal
from lib.serializers import (
    DurationField,
    IdAndNameAndInternalNameSerializer,
    IdAndNameSerializer,
    MaxLengthCharField,
    RelativedeltaField,
)
from lib.tools import sget, sget_v2, tznow
from settings import elasticsearch as es_settings
from webapps.business.consts import (
    TOP_BUSINESS_SERVICES_IDS,
    TOP_FEMALE_SERVICES_IDS,
    TOP_MALE_SERVICES_IDS,
)
from webapps.business.elasticsearch import amenities
from webapps.business.elasticsearch import business_category as es_business_category
from webapps.business.elasticsearch import (
    open_hours,
    regions,
    resources,
    reviews,
    service_categories,
)
from webapps.business.elasticsearch.availability import PROPERTIES as AVAILABILITY_PROPS
from webapps.business.elasticsearch.availability import (
    BusinessAvailabilityPerCategorySerializer,
    BusinessAvailabilitySerialializer,
)
from webapps.business.enums import BusinessCategoryEnum, CustomData
from webapps.business.models import Business, SalonNetwork
from webapps.business.renting_venue import get_venue_last_updated, sync_venues_and_contractors
from webapps.business.serializers import BusinessSalonNetworkSerializer
from webapps.business.tools import get_all_regions, get_business_address, get_region_by_type
from webapps.images import elasticsearch as images_elastic
from webapps.images.enums import ImageTypeEnum
from webapps.kill_switch.models import KillSwitch
from webapps.marketplace.models import MarketplaceCommission
from webapps.pos.models import POS
from webapps.public_partners.models import PartnerPermissionBusiness
from webapps.public_partners.utils import get_partner_names_from_business
from webapps.schedule.base_serializers import LegacyHoursSerializer
from webapps.schedule.ports import get_business_default_hours
from webapps.schedule.tools import schedule_update
from webapps.search_engine_tuning.models import BusinessTuning
from webapps.structure.serializers import RegionSimpleSerializer
from webapps.utt.elasticsearch import treatment
from webapps.utt.elasticsearch.serializers import (
    BusinessCategorySerializer,
    BusinessTreatmentSerializer,
)
from webapps.wait_list.models import WaitListDisabled


# region serializers
class BusinessLocationSerializer(serializers.Serializer):
    coordinate = BusinessCoordinatesSerializer(source='*')
    city = serializers.SerializerMethodField()
    address = serializers.SerializerMethodField()

    @staticmethod
    def get_city(instance):
        city = getattr(instance, 'city', None)
        return (
            city
            if city
            else get_region_by_type(
                get_all_regions(instance),
                settings.ES_CITY_LVL,
            )
        )

    @staticmethod
    def get_address(instance):
        return get_business_address(instance)

    @staticmethod
    def get_source(instance):
        if not instance.is_venue():
            return {}

        city = instance.city
        zipcode = instance.zipcode
        if not all([city, zipcode]):
            all_regions = get_all_regions(instance)

            if all_regions:
                if not zipcode:
                    zipcode = get_region_by_type(all_regions, settings.ES_ZIP_LVL)
            if not city:
                city = get_region_by_type(all_regions, settings.ES_CITY_LVL, 'full_name')

        return {
            'city': city,
            'address': instance.address,
            'address2': instance.address2,
            'zipcode': zipcode,
        }


class VenueLocationSerializer(BusinessLocationSerializer):
    address = serializers.CharField()
    address2 = serializers.CharField()
    zipcode = serializers.SerializerMethodField()

    def get_zipcode(self, instance):
        zipcode = instance.zip
        if not zipcode:
            zipcode = get_region_by_type(get_all_regions(instance), settings.ES_ZIP_LVL)
        return zipcode

    def to_representation(self, instance):
        if instance.status != Business.Status.VENUE:
            return {}
        return super().to_representation(instance)


class LegacyHoursIndexSerializer(LegacyHoursSerializer):
    def to_representation(self, instance):
        data = super().to_representation(instance)
        data['open_from'] = data.pop('hour_from')
        data['open_till'] = data.pop('hour_till')
        return data


class TravelingToClientsESSerializer(serializers.Serializer):
    price = serializers.FloatField()
    price_type = serializers.CharField()
    distance = serializers.SerializerMethodField()
    policy = serializers.CharField()
    hide_address = serializers.BooleanField()
    traveling_only = serializers.BooleanField()

    location = serializers.SerializerMethodField()
    area = serializers.SerializerMethodField()

    def get_distance(self, instance):
        return f'{instance.distance} {instance.distance_unit}'

    def get_area(self, instance):
        circle = Circle(
            instance.business.latitude,
            instance.business.longitude,
            1000
            * (
                miles_to_km(instance.distance)
                if instance.distance_unit == 'mi'
                else instance.distance
            ),
        )
        return circle_to_geojson(circle)

    def get_location(self, instance):
        business = instance.business
        hide_address = instance.hide_address
        location_instance = business.region if hide_address else business
        return CoordinatesSerializer().to_representation(location_instance)


class BusinessDocumentSerializer(serializers.ModelSerializer):
    # pylint: disable=too-many-public-methods

    class Meta:
        model = Business
        fields = (
            'id',
            'active_from',
            'booking_mode',
            'name',
            'slug',
            'cover_photo',
            'thumbnail_photo',
            'non_cover_image_limited_count',
            'phone',
            'business_location',
            'venue_location',
            'reviews_rank',
            'reviews_stars',
            'reviews_rank_score',
            'reviews_count',
            'popularity',
            # region boost
            'manual_boost_score',
            'mp_promotion',
            'promotion_boost',
            # endregion boost
            'pricing_level',
            'address',
            'address2',
            'website',
            'facebook_link',
            'instagram_link',
            'public_email',
            'description',
            'ecommerce_link',
            'contractor_description',
            'contractors',
            'credit_cards',
            'parking',
            'wheelchair_access',
            'active',
            'noindex',
            'promoted',
            'boost_status',
            'show_similar_gallery',
            'hidden_in_search',
            'booking_max_lead_time',
            'booking_min_lead_time',
            'booking_max_modification_time',
            'owner_id',
            'regions',
            # 'region_ids',
            # 'region_names',
            'timezone_name',
            # TODO: Remove when UTT2 will be fully operational
            # region business categories
            'business_primary_category',
            'business_categories',
            'treatments',
            'treatment_count',
            # region gender
            'female_weight',
            'primary_female_weight',
            # endregion gender
            # endregion business categories
            # region treatments
            'primary_category',
            'categories_utt',
            'treatments_utt',
            # region gender
            'female_weight_utt',
            'primary_female_weight_utt',
            # endregion gender
            # endregion treatments
            'service_categories',
            'pos_pay_by_app_enabled',
            'pos_market_pay_enabled',
            'deposit_policy',
            'deposit_cancel_time',
            'service_fee',
            'visible',
            'subdomain',
            'suggest_businesses',
            # region open hours
            'open_hours',
            'opening_hours_note',
            'availability',
            'availability_utt',
            # endregion open hours
            'owner_email',
            'is_renting_venue',
            'umbrella_venue_name',
            '_is_active',
            'reviews_rating_score',
            'conversion',
            'sitemap_url',
            'is_b_listing',
            'b_listing_source_id',
            'waitlist_disabled',
            # region top services
            'hide_top_services',
            TOP_BUSINESS_SERVICES_IDS,
            TOP_FEMALE_SERVICES_IDS,
            TOP_MALE_SERVICES_IDS,
            # endregion top services
            # region discounts
            'promotions_profitability',
            'max_discount_rate',
            # endregion discounts
            'salon_network',
            'donations_enabled',
            'has_online_services',
            'has_online_vouchers',
            'has_safety_rules',
            'has_special_offers',
            'traveling',
            'is_financial_institution',
            'pixel_id',
            'package',
            'printer_config',
            'venue_last_updated',
            'amenities',
            'printer_config',
            'partners',
            'profile_type',
            'importer',
            'disable_customer_note',
            'hidden_on_web',
            'awards',
            'search_phrases',
            'accept_booksy_gift_cards',
            'legal_name',
            'accept_booksy_pay',
        )

    active_from = serializers.DateTimeField(format=settings.ES_DATETIME_FORMAT)
    cover_photo = serializers.SerializerMethodField()
    thumbnail_photo = serializers.SerializerMethodField()
    non_cover_image_limited_count = serializers.SerializerMethodField()
    business_location = BusinessLocationSerializer(source='*')
    venue_location = VenueLocationSerializer(source='*')
    reviews_rank = serializers.FloatField(source='reviews_rank_avg')
    reviews_rank_score = DefaultZeroFloatField()
    popularity = serializers.SerializerMethodField()
    description = MaxLengthCharField(max_length=4048)
    contractor_description = MaxLengthCharField(max_length=4048)
    contractors = serializers.PrimaryKeyRelatedField(many=True, read_only=True)
    noindex = serializers.SerializerMethodField()
    show_similar_gallery = serializers.BooleanField(
        source=f'custom_data.{CustomData.SHOW_SIMILAR_GALLERY}', required=False
    )
    hidden_in_search = serializers.BooleanField(
        source=f'custom_data.{CustomData.HIDDEN_IN_SEARCH}',
        required=False,
    )
    booking_max_lead_time = DurationField()
    booking_min_lead_time = DurationField()
    booking_max_modification_time = DurationField()
    regions = RegionSimpleSerializer(many=True, source='region.get_parents_with_me')
    timezone_name = serializers.CharField(source='get_timezone._long_name')

    # TODO: Remove when UTT2 will be fully operational
    # region business categories
    business_primary_category = IdAndNameAndInternalNameSerializer(source='primary_category')
    business_categories = serializers.SerializerMethodField()
    treatments = IdAndNameSerializer(many=True)
    # Real value is assigned in `BusinessDocumentSerializer.to_representation`.
    treatment_count = serializers.IntegerField(default=0)
    female_weight = serializers.SerializerMethodField()
    primary_female_weight = serializers.SerializerMethodField()
    # endregion business categories

    # region treatments
    primary_category = IdAndNameAndInternalNameSerializer(source='primary_category.utt')
    categories_utt = serializers.SerializerMethodField()
    treatments_utt = BusinessTreatmentSerializer(
        many=True,
    )
    # TODO: Remove '_utt' from name when UTT2 will be fully operational
    female_weight_utt = serializers.SerializerMethodField()
    primary_female_weight_utt = serializers.SerializerMethodField()
    # endregion treatments

    service_categories = service_categories.ServiceCategoriesSerializer(
        many=True, read_only=True, source='*'
    )

    pos_pay_by_app_enabled = serializers.BooleanField()
    pos_market_pay_enabled = serializers.BooleanField()
    deposit_policy = serializers.CharField(source='pos.deposit_policy')
    deposit_cancel_time = RelativedeltaField(source='pos.deposit_cancel_time')
    service_fee = serializers.FloatField(source='pos.service_fee')
    visible = serializers.BooleanField()

    suggest_businesses = BusinessSuggestField()

    # region open hours
    open_hours = serializers.SerializerMethodField()
    availability = serializers.SerializerMethodField()
    availability_utt = serializers.SerializerMethodField()
    # endregion open hours

    owner_email = serializers.CharField(source='owner.email')
    is_renting_venue = serializers.SerializerMethodField()
    umbrella_venue_name = serializers.CharField(source='renting_venue.name')
    # region promotion
    # This fields must be SerializerMethodField to return default value
    # otherwise expect undefined behaviour es scripts
    mp_promotion = serializers.SerializerMethodField()
    promotion_boost = serializers.SerializerMethodField()
    # endregion promotion
    reviews_rating_score = serializers.FloatField(source='search_tuning.reviews_rating_score')

    conversion = serializers.FloatField(source='search_tuning.conversion')
    _is_active = serializers.SerializerMethodField()
    is_b_listing = serializers.SerializerMethodField()
    b_listing_source_id = serializers.IntegerField(source='source.id')
    sitemap_url = serializers.CharField(source='get_marketplace_sitemap_url')
    disable_customer_note = serializers.BooleanField(
        source=f'custom_data.{CustomData.DISABLE_CUSTOMER_NOTE}',
        required=False,
    )
    # region top services
    hide_top_services = serializers.BooleanField(
        source=f'custom_data.{CustomData.HIDE_TOP_SERVICES}', required=False
    )
    top_business_services_ids = serializers.ListField(
        child=serializers.IntegerField(),
    )
    top_female_services_ids = serializers.ListField(
        child=serializers.IntegerField(),
    )
    top_male_services_ids = serializers.ListField(
        child=serializers.IntegerField(),
    )
    # endregion top services
    # region discounts
    promotions_profitability = serializers.FloatField(
        source='search_tuning.promotions_profitability',
        min_value=0,
        max_value=1,
    )
    max_discount_rate = serializers.IntegerField(
        source='search_tuning.max_discount_rate',
        min_value=0,
        max_value=1,
    )
    # endregion discounts
    waitlist_disabled = serializers.SerializerMethodField()
    salon_network = BusinessSalonNetworkSerializer(required=False)
    manual_boost_score = serializers.IntegerField()
    donations_enabled = serializers.BooleanField()
    has_online_services = serializers.SerializerMethodField()
    has_online_vouchers = serializers.SerializerMethodField()
    traveling = TravelingToClientsESSerializer()
    has_special_offers = serializers.SerializerMethodField()
    is_financial_institution = serializers.SerializerMethodField()
    pixel_id = serializers.CharField(
        source=f'custom_data.{CustomData.PIXEL_ID}',
        required=False,
    )
    venue_last_updated = serializers.SerializerMethodField()
    amenities = amenities.AmenitiesSerializer()
    printer_config = serializers.SerializerMethodField()
    partners = serializers.SerializerMethodField()
    hidden_on_web = serializers.BooleanField()
    importer = serializers.SerializerMethodField()
    promoted = serializers.SerializerMethodField()
    awards = serializers.ListField(
        child=serializers.CharField(), source='best_of_booksy_business_award_names'
    )
    search_phrases = serializers.SerializerMethodField()
    accept_booksy_gift_cards = serializers.SerializerMethodField()
    legal_name = serializers.SerializerMethodField()
    accept_booksy_pay = serializers.SerializerMethodField()

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # dynamic fields
        self.bst = None
        for name in settings.ES_BUCKET_FIELD_NAMES:
            get_method = f'get_{name}'
            setattr(self, get_method, self._get_category_booking_rate_n(name))

    # TODO: Remove when UTT2 will be fully operational
    def get_fields(self):
        fields = super().get_fields()
        for __index, name in enumerate(settings.ES_BUCKET_FIELD_NAMES):
            fields[name] = serializers.SerializerMethodField()
        return fields

    def to_representation(self, instance: Business):
        """provide business in context for nested fields"""
        self.context['business'] = instance

        if instance.is_traveling and instance.traveling.hide_address:
            instance.address = ''
            instance.address2 = ''

        doc = super().to_representation(instance)
        if not doc['is_renting_venue']:
            del doc['contractors']

        # This hack saves us 1 db request per document reindex.
        doc['treatment_count'] = len(doc['treatments'])

        # This will be deleted in the future. See #71805.
        # update_availability_from_es.apply_async(
        #     kwargs=dict(business_id=instance.id),
        #     # by this time all data
        #     # in elastic search must be
        #     # fresh and crispy
        #     eta=tznow() + timedelta(seconds=20)
        # )

        # to_representation it's called when index/reindex happening
        # to collect data and send it later to elastic that's why
        # I am updating date here
        doc['updated_index'] = tznow()

        return doc

    def get_popularity(self, instance):  # pylint: disable=unused-argument
        # TODO fix popularity
        return 0

    def get_cover_photo(self, instance):
        return instance.get_cover_photo_url() or ''

    def get_thumbnail_photo(self, instance):
        return instance.get_photo(ImageTypeEnum.LOGO) or ''

    def get_noindex(self, instance):
        """ "Don't index unpaid businesses"""
        return instance.custom_data.get(CustomData.NOINDEX, False)

    # TODO: Remove when UTT2 will be fully operational
    # region business categories
    @classmethod
    def get_business_categories(cls, instance):
        instance_categories = instance.categories
        instance_categories = cls.filter_us_barbers_categories_hack(
            instance,
            instance_categories,
        )
        return es_business_category.BusinessCategorySerializer(
            instance_categories,
            many=True,
        ).data

    @staticmethod
    def filter_us_barbers_categories_hack(instance, instance_categories):
        # TODO HACK 50221
        barbers = BusinessCategoryEnum.BARBERS
        if (
            instance.country_code == Country.US
            and instance.primary_category
            and instance.primary_category.internal_name == barbers
        ):
            instance_categories = instance_categories.filter(
                internal_name=barbers,
            )
        return instance_categories

    @staticmethod
    def get_female_weight(instance):
        if CustomData.FEMALE_WEIGHT in instance.custom_data:
            return instance.custom_data[CustomData.FEMALE_WEIGHT]
        weights = [c.female_weight for c in instance.categories.all()]
        return float(sum(weights)) / max(len(weights), 1)

    @staticmethod
    def get_primary_female_weight(instance):
        if CustomData.FEMALE_WEIGHT in instance.custom_data:
            return instance.custom_data[CustomData.FEMALE_WEIGHT]
        if not instance.primary_category:
            return None
        return instance.primary_category.female_weight

    # endregion business categories

    # region treatments
    @classmethod
    def get_categories_utt(cls, instance):
        # TODO HACK 50221
        instance_categories = instance.categories_utt
        instance_categories = cls.filter_us_barbers_categories_hack(
            instance,
            instance_categories,
        )

        instance_treatments = list(
            chain(
                instance_categories.all(),
                [instance.primary_category],
            )
        )

        categories_score = instance.search_tuning.utt_score if instance.has_tuning else {}
        return BusinessCategorySerializer(
            instance_treatments,
            many=True,
            context={'categories_score': categories_score},
        ).data

    @staticmethod
    def get_female_weight_utt(instance):
        if CustomData.FEMALE_WEIGHT in instance.custom_data:
            return instance.custom_data[CustomData.FEMALE_WEIGHT]
        weights = [c.female_weight for c in instance.categories_utt.all()]
        return float(sum(weights)) / max(len(weights), 1)

    @staticmethod
    def get_primary_female_weight_utt(instance):
        if CustomData.FEMALE_WEIGHT in instance.custom_data:
            return instance.custom_data[CustomData.FEMALE_WEIGHT]
        if not instance.primary_category or instance.primary_category.category_utt is None:
            return None
        return instance.primary_category.category_utt.female_weight

    # endregion treatments

    def get_is_renting_venue(self, instance):
        return instance.status == Business.Status.VENUE

    def get__is_active(self, instance):
        return (
            instance.active
            and instance.visible
            and (
                instance.status == Business.Status.VENUE
                or instance.is_b_listing()
                or instance.services.filter(active=True, service_variants__active=True).exists()
            )
        )

    def get_mp_promotion(self, instance):
        commission = MarketplaceCommission.get_commission(instance)
        if not instance.boosted:
            return 0.0

        commission = (
            MarketplaceCommission.DEFAULT_ES_COMMISSION
            if commission is None
            else commission.commission
        )
        if commission == 0:
            commission = MarketplaceCommission.DEFAULT_ES_COMMISSION

        return commission / 100.0

    @staticmethod
    def get_promotion_boost(instance):
        value = 1.0
        if instance.has_tuning:
            value = instance.search_tuning.boost
        return value

    @staticmethod
    def get_promoted(instance):
        return instance.boosted

    def get_is_b_listing(self, instance):
        return instance.is_b_listing()

    # TODO: Remove when UTT2 will be fully operational
    def _get_category_booking_rate_n(self, name):
        # in version es version 7.x
        # try to use Feature Vector datatype that will be more efficient
        # https://www.elastic.co/guide/en/elasticsearch/reference/master/feature-vector.html
        def wrapper(instance):
            """Calculate booking_rate for every category in business
            will be used in secondary_category script filter
            :param instance: business. instance
            :return: float
            """
            if self.bst is None:
                if instance.has_tuning:
                    self.bst = instance.search_tuning.get_secondary_categories_score()
                else:
                    self.bst = {}
            return self.bst.get(name, 0)

        return wrapper

    def get_open_hours(self, instance):
        week_hours = get_business_default_hours(business_id=instance.id)
        return LegacyHoursIndexSerializer(
            instance=week_hours,
            many=True,
        ).data

    def get_availability(self, instance):
        serializer = BusinessAvailabilityPerCategorySerializer(
            read_only=True,
            source='*',
            context={'use_max_lead_time': True},
        )
        return serializer.to_representation(instance)

    @staticmethod
    def get_availability_utt(instance):
        if KillSwitch.alive(KillSwitch.System.UTT2_BACKEND):
            serializer = BusinessAvailabilitySerialializer(
                read_only=True,
                source='*',
                context={'use_max_lead_time': True},
                seconds_format=True,
            )
            return serializer.to_representation(instance)
        return []

    def get_waitlist_disabled(self, instance):
        return WaitListDisabled.objects.filter(business=instance).exists()

    # endregion serializers

    def get_has_online_services(self, instance):
        return instance.services.filter(is_online_service=True).exists()

    def get_has_online_vouchers(self, instance) -> bool:
        return (
            instance.pos
            and instance.pos.voucher_templates.filter(
                active=True,
                online_purchase=True,
            ).exists()
        )

    def get_has_special_offers(self, instance):
        return instance.service_promotions.filter(active=True).exists()

    def get_is_financial_institution(self, instance):
        category_name = sget(instance, ['primary_category', 'internal_name'])
        return category_name == BusinessCategoryEnum.FINANCIAL_INSTITUTIONS

    def get_venue_last_updated(self, instance):
        return get_venue_last_updated(instance)

    @staticmethod
    def get_printer_config(instance):
        if settings.API_COUNTRY == Country.BR:
            return True
        if settings.API_COUNTRY == Country.PL:
            return hasattr(instance, 'printer_config')
        return False

    @staticmethod
    def get_partners(instance):
        return get_partner_names_from_business(instance)

    def get_importer(self, instance):
        return instance.integrations.get('importer', '')

    @classmethod
    def get_non_cover_image_limited_count(cls, instance):
        return instance.images.filter(is_cover_photo=False)[:15].count()

    def get_search_phrases(self, instance):
        phrase_parts = [instance.name]

        return [normalize('NFKC', ' '.join(phrase_parts))]

    @staticmethod
    def get_accept_booksy_gift_cards(instance):
        if not BooksyGiftcardsEnabledFlag() or not ShowBooksyGiftCardsForChosenProvidersFlag(
            UserData(subject_key=instance.id)
        ):
            return False

        if booksy_gift_cards_settings := sget_v2(instance, ['booksy_gift_cards_settings']):
            return (
                booksy_gift_cards_settings.accept_booksy_gift_cards
                and not booksy_gift_cards_settings.accept_booksy_gift_cards_manually_disabled
            )
        return False

    @staticmethod
    def get_accept_booksy_pay(instance):
        return instance.booksy_pay_available

    def get_legal_name(self, instance):
        if instance.versum_official_name:
            return instance.versum_official_name
        if instance.buyer:
            return instance.buyer.entity_name


class BusinessDocument(Document):
    class Meta:
        serializer = BusinessDocumentSerializer
        active_records_queryset = Business.objects.indexed_in_elasticsearch()
        queryset = Business.objects.prefetch_related(
            Prefetch('categories', queryset=es_business_category.QUERYSET),
            Prefetch(
                'pos_set',
                queryset=POS.objects.filter(active=True).select_related('voucher_additional_info'),
            ),
            Prefetch('service_categories'),
            Prefetch('contractors', queryset=Business.objects.only('id')),
            Prefetch('search_tuning', queryset=BusinessTuning.objects.all()),
            Prefetch('service_promotions', queryset=service_categories.SERVICE_PROMOTIONS_QUERYSET),
            Prefetch(
                'salon_network',
                queryset=SalonNetwork.objects.prefetch_related('members'),
            ),
            Prefetch(
                'partnerpermissionbusiness_set',
                queryset=PartnerPermissionBusiness.objects.select_related('partner'),
            ),
        ).select_related(
            'region',
            'primary_category',
            'owner',
            'renting_venue',
            'amenities',
            'printer_config',
            'booksy_gift_cards_settings',
        )
        routing = 'id'
        active_record_property = '_is_active'
        dynamic_fields = {name: dsl.Float(index=True) for name in settings.ES_BUCKET_FIELD_NAMES}

        es_bulk_size = 20
        es_index_task_batch_limit = 50

    id = dsl.Integer(index=True)
    active_from = dsl.Date(index=True)
    updated_index = dsl.Date()
    booking_mode = dsl.Text(index=False)
    name = f.text_multi
    slug = dsl.Keyword(index=False)
    cover_photo = f.BusinessPhotoUrl()
    thumbnail_photo = f.BusinessPhotoUrl()
    non_cover_image_limited_count = dsl.Integer(index=True)
    phone = f.text_alfanum
    business_location = dsl.Object(
        properties={
            'coordinate': dsl.GeoPoint(index=True),
            'city': dsl.Text(index=False),
            'address': f.text_multi,
        }
    )
    venue_location = dsl.Object(
        properties={
            'coordinate': dsl.GeoPoint(index=True),
            'city': f.text_multi,
            'address': f.text_multi,
            'address2': dsl.Text(index=False),
            'zipcode': dsl.Keyword(index=True),
        }
    )
    reviews_rank = dsl.Float(index=True)
    reviews_stars = dsl.Integer(index=True)
    reviews_count = dsl.Integer(index=True)
    reviews_rank_score = dsl.Float(index=True)
    # review score for function scoring
    reviews_rating_score = dsl.Float(index=True)
    popularity = dsl.Integer(index=True)
    manual_boost_score = dsl.Integer(index=True)

    promotion_boost = dsl.Float(index=True)
    mp_promotion = dsl.Float(index=False)

    conversion = dsl.Float(index=False)
    pricing_level = dsl.Integer(index=True)
    # this fields may contain business name
    # and we don't it be counted in
    #   1) term frequency and in
    #   2) inverted term frequency
    website = dsl.Keyword(index=False)
    facebook_link = dsl.Keyword(index=False)
    instagram_link = dsl.Keyword(index=False)
    public_email = dsl.Keyword(index=False)
    description_keyword = dsl.Keyword(index=False)
    ecommerce_link = dsl.Keyword(index=False)

    credit_cards = dsl.Text(index=False)
    parking = dsl.Text(index=False)
    wheelchair_access = dsl.Text(index=False)
    active = dsl.Boolean(index=True)
    noindex = dsl.Boolean(index=True)
    promoted = dsl.Boolean(index=True)
    show_similar_gallery = dsl.Boolean(index=True)
    hidden_in_search = dsl.Boolean(index=True)
    hide_top_services = dsl.Boolean(index=True)
    disable_customer_note = dsl.Boolean(index=False)
    booking_max_lead_time = dsl.Integer(index=False)  # in minutes
    booking_min_lead_time = dsl.Integer(index=False)  # in minutes
    booking_max_modification_time = dsl.Integer(index=False)  # in min
    owner_id = dsl.Integer(index=False)
    regions = dsl.Object(properties=regions.PROPERTIES)
    timezone_name = dsl.Text(index=False)
    # TODO: Remove when UTT2 will be fully operational

    # region business categories
    business_primary_category = dsl.Object(
        properties={
            'id': dsl.Integer(index=True),
            'name': f.text_multi,
            'internal_name': dsl.Text(index=False),
        },
    )
    business_categories = dsl.Object(
        properties={
            'id': dsl.Integer(index=True),
            'name': f.text_multi,
        },
    )
    treatments = dsl.Object(
        properties=es_business_category.PROPERTIES,
    )
    treatment_count = dsl.Integer(index=True)
    female_weight = dsl.Integer(index=True)
    primary_female_weight = dsl.Integer(index=True)
    # endregion business categories
    # region treatments
    primary_category = dsl.Object(properties=treatment.TREATMENT_FIELD_PROPERTIES)
    categories_utt = dsl.Nested(
        properties=treatment.TREATMENT_FIELD_PROPERTIES,
    )
    treatments_utt = dsl.Nested(
        properties=treatment.TREATMENT_FIELD_PROPERTIES,
    )
    # TODO: Remove '_utt' from name when UTT2 will be fully operational
    female_weight_utt = dsl.Integer(index=True)
    primary_female_weight_utt = dsl.Integer(index=True)
    # endregion treatments

    subdomain = dsl.Text(index=False)
    service_categories = f.Nested(properties=service_categories.PROPERTIES)
    # region open hours
    open_hours = dsl.Object(enabled=False)
    opening_hours_note = dsl.Text(index=False)
    # TODO: Remove when UTT2 will be fully operational
    availability = f.Nested(properties=AVAILABILITY_PROPS)
    # TODO: Remove '_utt' from name when UTT2 will be fully operational
    availability_utt = f.Nested(properties=AVAILABILITY_PROPS)
    # endregion open hours

    pos_pay_by_app_enabled = dsl.Boolean(index=True)
    pos_market_pay_enabled = dsl.Boolean(index=False)
    deposit_policy = dsl.Text(index=False)
    deposit_cancel_time = dsl.Object(enabled=False)
    service_fee = dsl.Float(index=True)
    visible = dsl.Boolean(index=True)
    suggest_businesses = dsl.Completion(
        analyzer=a.icu_folded_lowercase,
        preserve_separators=False,
        contexts=[
            {'name': 'business_categories', 'type': 'category'},
            {'name': 'is_b_listing', 'type': 'category'},
            {'name': 'location', 'type': 'geo', 'precision': es_settings.ES_COMPLETER_PRECISION},
        ],
    )
    owner_email = dsl.Keyword(index=True)
    is_renting_venue = dsl.Boolean(index=True)
    is_b_listing = dsl.Boolean(index=True)
    b_listing_source_id = dsl.Integer(index=True)
    sitemap_url = dsl.Text(index=False)
    top_female_services_ids = dsl.Integer(index=True, multi=True)
    top_male_services_ids = dsl.Integer(index=True, multi=True)
    top_services_ids = dsl.Integer(index=True, multi=True)
    promotions_profitability = dsl.Float(index=True)
    max_discount_rate = dsl.Integer(index=False)
    salon_network = f.Nested(
        properties={
            'members': dsl.Integer(index=True, multi=True),
        },
    )
    donations_enabled = dsl.Boolean(index=True)
    has_online_services = dsl.Boolean(index=True)
    has_online_vouchers = dsl.Boolean(index=False)
    has_safety_rules = dsl.Boolean(index=False)
    traveling = dsl.Object(
        properties={
            'price': dsl.Float(index=False),
            'price_type': dsl.Text(index=False),
            'distance': dsl.Text(index=False),
            'policy': dsl.Text(index=False),
            'hide_address': dsl.Boolean(index=False),
            'traveling_only': dsl.Boolean(index=False),
            'location': dsl.GeoPoint(index=True),
            'area': dsl.GeoShape(),
        },
    )
    has_special_offers = dsl.Boolean(index=True)
    is_financial_institution = dsl.Boolean(index=True)
    pixel_id = dsl.Text(index=False)
    venue_last_updated = dsl.Object(enabled=False)
    amenities = dsl.Object(properties=amenities.PROPERTIES)
    printer_config = dsl.Boolean(index=True)
    partners = dsl.Keyword(index=False, multi=True)
    profile_type = dsl.Keyword(index=False)
    hidden_on_web = dsl.Boolean(index=True)
    importer = dsl.Text(index=True)
    awards = dsl.Text(index=True, multi=True)
    accept_booksy_gift_cards = dsl.Boolean(index=True)
    accept_booksy_pay = dsl.Boolean(index=True)
    legal_name = dsl.Text(index=True)

    search_phrases = dsl.SearchAsYouType(
        multi=True,
        analyzer=a.business_name,
        search_analyzer=a.business_name_synonyms,
        doc_values=False,
        max_shingle_size=3,
    )

    # deprecated fields
    return_rate = dsl.Float(index=False)

    @classmethod
    def get_queryset(cls, ids=None):
        queryset = super(BusinessDocument, cls).get_queryset(ids)
        return queryset.prefetch_related(
            Prefetch('services', queryset=service_categories.get_services_queryset()),
        )

    @classmethod
    def reindex(cls, ids=None, use_celery=False, **kwargs):  # pylint: disable=arguments-differ
        if ids is not None and use_celery is False:
            # during real reindex update venue/contractors if needed
            sync_venues_and_contractors(ids)

        return super().reindex(ids, use_celery=use_celery, **kwargs)

    def save(self, **kwargs):  # pylint: disable=arguments-differ
        self.updated_index = tznow()
        super().save(**kwargs)


class BusinessIndex(Index):
    no_suffix_name = 'business_index'
    name = f'{no_suffix_name}_{es_settings.ES_SUFFIX}'
    index_settings = {
        'max_inner_result_window': 1000,  # allow staff number
        # https://www.elastic.co/guide/en/elasticsearch/reference/current/analysis-ngram-tokenizer.html # pylint: disable=line-too-long
        'max_ngram_diff': 20,
        'mapping': {'nested_objects': {'limit': 20000}},
    }
    documents = {
        ESDocType.BUSINESS: BusinessDocument,
        ESDocType.OPEN_HOURS: open_hours.BusinessOpenHoursDocument,
        ESDocType.IMAGE: images_elastic.ImageDocument,
        ESDocType.IMAGE_LIKE: images_elastic.ImageLikeDocument,
        ESDocType.IMAGE_COMMENT: images_elastic.ImageCommentDocument,
        ESDocType.RESOURCE: resources.ResourceDocument,
        ESDocType.REVIEW: reviews.ReviewDocument,
    }
    relations = {
        ESDocType.BUSINESS: [
            ESDocType.RESOURCE,
            ESDocType.OPEN_HOURS,
            ESDocType.REVIEW,
            ESDocType.IMAGE,
        ],
        ESDocType.IMAGE: [
            ESDocType.IMAGE_LIKE,
            ESDocType.IMAGE_COMMENT,
        ],
    }

    stored_scripts = {
        'secondary_category': {
            'script': {
                # you should pronounce it as `pain in ass`
                'lang': 'painless',
                'source': SECONDARY_CATEGORY_SCRIPT,
            },
        },
        'utt_score_script': {
            'script': {
                'lang': 'painless',
                'source': UTT_SCORE_SCRIPT,
            },
        },
        'market_place_promotion': {
            'script': {
                'lang': 'painless',
                'source': MARKET_PLACE_PROMOTION,
            },
        },
        'market_place_promotion_variant_a': {
            'script': {
                'lang': 'painless',
                'source': MARKET_PLACE_PROMOTION_VARIANT_A,
            },
        },
        'market_place_promotion_variant_b': {
            'script': {
                'lang': 'painless',
                'source': MARKET_PLACE_PROMOTION_VARIANT_B,
            },
        },
        'market_place_promotion_variant_c': {
            'script': {
                'lang': 'painless',
                'source': MARKET_PLACE_PROMOTION_VARIANT_C,
            },
        },
        'distance_from_business': {
            'script': {
                'lang': 'painless',
                'source': (
                    "params.loc == null ? null : "
                    "doc['business_location.coordinate']"
                    ".arcDistance(params.loc.lat, params.loc.lon)"
                ),
            },
        },
        'sort_by_ids': {
            'script': {
                'lang': 'painless',
                'source': ("params.ids.indexOf((int) doc['id'].value)"),
            },
        },
        'reviews_number_penalty': {
            'script': {
                'lang': 'painless',
                'source': (
                    "if (doc['reviews_count'].size() != 0) {"
                    "int len=(int)doc['reviews_count'].value; "
                    "len > 1 ? 350 : len > 0 ? 200 : 0;"
                    "} else { 0 }"
                ),
            }
        },
    }

    def __init__(self):
        self._default_analyzers = deepcopy(self._default_analyzers)
        self._default_analyzers['analyzer'].update(
            {
                'business_name_raw': a.business_name_raw.get_definition(),
                'business_name_synonyms': a.business_name_synonyms.get_definition(),
            }
        )

        super().__init__()


def region_with_parents(region):
    return region.get_parents_with_me() if region else []


bump_on_signal(River.BUSINESS, post_save, 'business.Business', 'id')

bump_on_signal(River.BUSINESS, post_save, 'business.BListing', 'id')
bump_on_signal(River.BUSINESS, post_delete, 'business.BListing', 'id')

bump_on_signal(
    River.BUSINESS,
    post_save,
    'business.BusinessCategory',
    lambda x: list(
        x.businesses.filter(
            active=True,
        ).values_list('id', flat=True)
    ),
)
bump_on_signal(
    River.BUSINESS,
    post_save,
    'business.BusinessCategory',
    lambda x: list(
        x.primary_businesses.filter(
            active=True,
        ).values_list('id', flat=True)
    ),
)
bump_on_signal(
    River.BUSINESS,
    post_save,
    'business.BusinessCategory',
    lambda x: list(
        x.treatment_businesses.filter(
            active=True,
        ).values_list('id', flat=True)
    ),
)

bump_on_signal(River.BUSINESS, post_save, 'business.ServiceCategory', 'business_id')
bump_on_signal(River.BUSINESS, post_save, 'business.Service', 'business_id')
bump_on_signal(River.BUSINESS, post_save, 'business.ServiceVariant', 'service.business_id')

# update when new resource added also all it services in business doc
bump_on_signal(River.BUSINESS, post_save, 'business.Resource', 'business_id')

bump_on_signal(River.BUSINESS, post_save, 'reviews.Review', 'business_id')
bump_on_changed_fields(
    River.BUSINESS,
    'pos.POS',
    'business_id',
    [
        'deposit_policy',
        'deposit_cancel_time',
        'service_fee',
    ],
)
bump_on_changed_fields(
    River.BUSINESS,
    'booksy_pay.BooksyPaySettings',
    'pos.business_id',
    [
        'enabled',
        'allowed',
    ],
)

bump_on_signal(River.OPEN_HOURS, post_save, 'schedule.Schedule', 'business_id')
bump_on_signal(River.BUSINESS, post_save, 'schedule.BusinessHours', 'business_id')
bump_on_signal(River.BUSINESS, post_save, 'schedule.ResourceHours', 'business_id')
bump_on_signal(River.BUSINESS, post_save, 'business.ServicePromotion', 'business_id')
bump_on_signal(River.BUSINESS, post_save, 'voucher.VoucherTemplate', 'pos.business_id')
bump_on_signal(River.BUSINESS, post_save, 'voucher.VoucherAdditionalInfo', 'pos.business_id')
bump_on_signal(River.BUSINESS, post_save, 'stripe_integration.StripeAccount', 'pos.business_id')
bump_on_signal(River.BUSINESS, post_save, 'business.TravelingToClients', 'business_id')
bump_on_signal(
    River.BUSINESS,
    post_save,
    'business_related.Amenities',
    'business_id',
)
bump_on_signal(River.AVAILABILITY, post_save, 'schedule.Schedule', 'business_id')
bump_on_signal(River.AVAILABILITY, pre_delete, 'schedule.Schedule', 'business_id')
bump_on_signal(River.AVAILABILITY, schedule_update, 'schedule.Schedule', 'business_id')

# reindex all bcis on business activity change (delete bci for inactive)
bump_on_changed_fields(
    River.BUSINESS_CUSTOMER,
    'business.Business',
    attr_or_fn=lambda biz: list(biz.business_customer_infos.values_list('id', flat=True)),
    fields=['active'],
)

bump_on_signal(
    River.BUSINESS,
    post_save,
    'business.SalonNetwork',
    'business_id',
)

bump_on_signal(
    River.BUSINESS,
    post_save,
    'business_related.BooksyGiftCardsSettings',
    'business_id',
)

# venue region start
for sender in [
    Business.categories.through,
    Business.treatments.through,
    Business.categories_utt.through,
    Business.treatments_utt.through,
]:
    bump_on_signal(River.BUSINESS, m2m_changed, sender, 'renting_venue_id')

bump_on_changed_fields(
    River.BUSINESS,
    'business.Business',
    attr_or_fn='renting_venue_id',
    fields=['primary_category'],
)


def venue_contractors_ids(business):
    if not business.is_venue():
        return []
    return list(business.contractors.all().values_list('id', flat=True))


bump_on_changed_fields(
    River.BUSINESS,
    'business.Business',
    attr_or_fn=venue_contractors_ids,
    fields=[
        'zipcode',
        'address',
        'address2',
        'city',
        'latitude',
        'longitude',
    ],
)
# venue region end
