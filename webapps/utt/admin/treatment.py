from django.contrib import admin

from lib.admin_helpers import ReadOnlyFieldsMixin, NoAddDelMixin, BaseModelAdmin
from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin
from webapps.user.groups import GroupNameV2
from webapps.utt import models


class TreatmentInline(NoAddDelMixin, admin.TabularInline):
    model = models.Treatment
    classes = ['collapse']
    extra = 0
    can_delete = False

    fields = readonly_fields = ['id', 'name', 'internal_name', 'level', 'parent']
    ordering = ('id',)


class TreatmentAdmin(ReadOnlyFieldsMixin, NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    placeholder = '-' * 3

    list_display = (
        'id',
        'internal_name',
        'name',
        'level',
        'category',
        'parent_name',
    )
    query_fields = [
        'id',
    ]
    search_fields = (
        'internal_name',
        'name',
    )
    list_filter = (
        'level',
        'category',
    )
    inlines = (TreatmentInline,)

    @classmethod
    def parent_name(cls, instance: models.Treatment):
        return instance.parent.name if instance.parent is not None else cls.placeholder
