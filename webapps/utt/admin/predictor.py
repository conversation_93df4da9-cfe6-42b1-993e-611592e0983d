from operator import itemgetter

from django import forms
from django.urls.base import reverse
from django.utils.html import format_html

from lib.admin_helpers import NoAddDelMixin, BaseModelAdmin
from lib.tools import relativedelta_total_seconds
from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin
from webapps.user.groups import GroupNameV2
from webapps.utt import models
from webapps.utt.features import TreatmentAssignmentUTT2
from webapps.utt.admin.tools import (
    change_status,
    GroupedModelChoiceField,
)
from webapps.utt.enums import PredictionStatus


class CategoryGroupedModelChoiceField(GroupedModelChoiceField):
    def label_from_instance(self, obj: models.Treatment):
        return obj.parent_representation


class ServiceTreatmentPredictionForm(forms.ModelForm):
    class Meta:
        model = models.ServiceTreatmentPrediction
        fields = ['treatment']

    treatment = CategoryGroupedModelChoiceField(
        group_by_field='category',
        group_label=lambda x: x.name,
        queryset=models.Treatment.ordered_queryset().all(),
        required=True,
        empty_label=None,
    )

    def save(self, commit=True):
        instance = super().save(commit=False)
        instance.service.treatment_utt = instance.treatment
        instance.service.save(update_fields=['treatment_utt'])
        TreatmentAssignmentUTT2.update_businesses_treatments([instance.service.business_id])

        instance.status = PredictionStatus.ACCEPTED
        instance.confirmation_priority = 0
        instance.save(update_fields=['treatment', 'status', 'confirmation_priority'])
        return instance


class ServiceTreatmentPredictionAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    placeholder = '-' * 3

    list_display = [
        'treatment_name',
        'service_name',
        'business_name',
        'business_primary_category',
        'service_category',
        'service_price',
        'service_duration',
    ]
    search_fields = [
        'service__name',
        'service__business__name',
    ]
    query_fields = [
        'service_id',
        'treatment_id',
        'service__business__id',
    ]
    ordering = ('-confirmation_priority', 'status')
    actions = [
        'accept_prediction',
    ]

    form = ServiceTreatmentPredictionForm
    readonly_fields = [
        'created',
        'updated',
        'status',
        'confirmation_priority',
        'predictions',
        'service_id',
        'service_name',
        'service_category',
        'service_price',
        'service_duration',
        'service_note',
        'service_description',
        'business_id',
        'business_name',
        'business_primary_category',
    ]
    fieldsets = [
        (
            None,
            {
                'fields': ('created', 'updated', 'confirmation_priority', 'status'),
            },
        ),
        (
            'Treatment',
            {
                'fields': ('predictions', 'treatment'),
            },
        ),
        (
            'Business',
            {
                'fields': (
                    'business_id',
                    'business_name',
                    'business_primary_category',
                ),
            },
        ),
        (
            'Service',
            {
                'fields': (
                    'service_id',
                    'service_name',
                    'service_category',
                    'service_price',
                    'service_duration',
                    'service_note',
                    'service_description',
                ),
            },
        ),
    ]

    # region fields
    @staticmethod
    def get_url_field(view_name: str, linked_text: str, linked_instance_id: int) -> str:
        return format_html(
            '<a href="{url}" class="viewsitelink">{linked_text}</a>',
            linked_text=linked_text,
            url=reverse(view_name, args=(linked_instance_id,)),
        )

    @staticmethod
    def business_id(instance: models.ServiceTreatmentPrediction):
        return instance.service.business_id

    @classmethod
    def business_name(cls, instance: models.ServiceTreatmentPrediction):
        return cls.get_url_field(
            'admin:business_business_change',
            instance.service.business.name,
            instance.service.business_id,
        )

    business_name.short_description = 'Business name'

    @staticmethod
    def business_primary_category(instance: models.ServiceTreatmentPrediction):
        bpc = instance.service.business.primary_category
        return bpc.category_utt.name if bpc.category_utt is not None else None

    @staticmethod
    def service_id(instance):
        return instance.service_id

    @classmethod
    def service_name(cls, instance: models.ServiceTreatmentPrediction):
        return cls.get_url_field(
            'admin:business_service_change',
            instance.service.name,
            instance.service_id,
        )

    service_id.short_description = 'Service name'

    @classmethod
    def service_category(cls, instance: models.ServiceTreatmentPrediction):
        return (
            instance.service.service_category.name
            if instance.service.service_category is not None
            else cls.placeholder
        )

    @classmethod
    def service_price(cls, instance: models.ServiceTreatmentPrediction):
        prices = [sv.price for sv in instance.service.service_variants.iterator()]
        return cls.parse_sv_values(prices)

    @classmethod
    def service_duration(cls, instance: models.ServiceTreatmentPrediction):
        duration = [
            relativedelta_total_seconds(sv.duration) // 60
            for sv in instance.service.service_variants.iterator()
        ]
        return cls.parse_sv_values(duration)

    @classmethod
    def service_description(cls, instance: models.ServiceTreatmentPrediction):
        return instance.service.description or cls.placeholder

    @classmethod
    def service_note(cls, instance: models.ServiceTreatmentPrediction):
        return instance.service.note or cls.placeholder

    @staticmethod
    def treatment_name(instance: models.ServiceTreatmentPrediction):
        return f'{instance.treatment.name} ({instance.treatment.category.name})'

    @staticmethod
    def predictions(instance: models.ServiceTreatmentPrediction):
        top_guesses = instance.top_guesses
        treatment_ids = [
            int(treatment_score[0])
            for treatment_score in sorted(top_guesses.items(), key=itemgetter(1), reverse=True)
        ]

        treatments = models.Treatment.objects.prefetch_related('category', 'parent').in_bulk(
            treatment_ids
        )
        treatments = [treatments[treatment_id] for treatment_id in treatment_ids]
        return '\n'.join(
            f'{position}. {treatment.category.name}' f' - {treatment.parent_representation}'
            for position, treatment in enumerate(treatments, 1)
        )

    @staticmethod
    def parse_sv_values(values):
        values = list(filter(None, values))
        if len(values) == 0:
            result = ''
        else:
            _min, _max = min(values), max(values)
            if _min == _max:
                result = _min
            else:
                result = f'{_min}-{_max}'
        return result

    # endregion

    @staticmethod
    def accept_prediction(_, __, queryset):
        change_status(queryset, PredictionStatus.ACCEPTED, 0)
