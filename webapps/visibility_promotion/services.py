from datetime import timed<PERSON><PERSON>

from django.core.exceptions import ValidationError
from django.db import transaction
from django.utils.translation import gettext_lazy as _

from lib.locks import VisibilityPromotionPurchaseLock, RedlockError
from lib.tools import format_currency, tznow
from webapps.business.enums import FeatureLabel, FeatureStatus, FeatureStatusColor
from webapps.marketplace.boost_details import BoostStatusProvider, BoostScoreManager
from webapps.navision.enums import TaxRateService
from webapps.navision.ports.tax_rates import TaxForNetSummary
from webapps.visibility_promotion.models import (
    VisibilityPromotionCandidate,
    VisibilityPromotionOffer,
    VisibilityPromotionOrder,
    PromotionPeriod,
)
from webapps.visibility_promotion.enums import OrderStatus, PromotionStatus


class PromotionEligibilityService:
    """
    Determines if a business is eligible to purchase visibility promotion.
    Checks feature access, existing promotions, and business requirements.
    """

    _allowed_boost_feature_statuses = {'deactivated', 'inactive'}

    @classmethod
    def can_access_visibility_promotion(cls, business_id: int, boost_status: str) -> bool:
        return cls._not_conflicting_with_boost(
            boost_status
        ) and cls._granted_early_access_to_visibility_promotion(business_id)

    @classmethod
    def _not_conflicting_with_boost(cls, boost_status: str) -> bool:
        return boost_status in cls._allowed_boost_feature_statuses

    @classmethod
    def _granted_early_access_to_visibility_promotion(cls, business_id: int) -> bool:
        return VisibilityPromotionCandidate.objects.filter(
            business_id=business_id, active=True
        ).exists()

    @classmethod
    def can_purchase_now(cls, business_id: int, boost_status: str) -> bool:
        return cls.can_access_visibility_promotion(
            business_id, boost_status
        ) and cls._can_start_new_purchase(business_id)

    @classmethod
    def _can_start_new_purchase(cls, business_id: int) -> bool:
        return not cls._has_pending_order(business_id) and not cls._has_active_promotion(
            business_id
        )

    @classmethod
    def _has_pending_order(cls, business_id: int) -> bool:
        return VisibilityPromotionOrder.objects.filter(
            business_id=business_id, status=OrderStatus.PENDING, deleted__isnull=True
        ).exists()

    @classmethod
    def _has_active_promotion(cls, business_id: int) -> bool:
        return PromotionPeriod.objects.filter(
            business_id=business_id, status=PromotionStatus.ACTIVE, deleted__isnull=True
        ).exists()


class GetAvailableOffersUseCase:
    """
    Calculates pricing for promotion offers including taxes and final amounts.
    """

    @classmethod
    def get_offers_for_business(cls, business):
        """
        Get all available offers with calculated taxes and formatted prices for a business.
        Returns list of offers formatted for API response.
        """
        formatted_offers = []
        for offer in VisibilityPromotionOffer.objects.filter(deleted__isnull=True).order_by(
            'duration_days'
        ):
            tax_summary = TaxForNetSummary.for_business(
                business=business,
                service=TaxRateService.BOOST,
                net_price=offer.price_net,
                round_results=True,
            )  # using the boost tax service until we have a proper tax service

            net_per_day = offer.price_net / offer.duration_days
            gross_per_day = tax_summary.gross_price / offer.duration_days

            formatted_offer = {
                'id': offer.id,
                'name': f'{offer.duration_days} days',
                'price_net': format_currency(offer.price_net),
                'price_gross': format_currency(tax_summary.gross_price),
                'tax': format_currency(tax_summary.tax),
                'price_net_per_day': format_currency(net_per_day),
                'price_gross_per_day': format_currency(gross_per_day),
            }

            formatted_offers.append(formatted_offer)

        return formatted_offers


class PurchasePromotionUseCase:
    """
    Orchestrates the complete promotion purchase workflow.
    Handles payment processing and promotion activation.
    """

    @classmethod
    def place_order(cls, business_id: int, offer_id: int) -> VisibilityPromotionOrder:
        """
        Creates a PENDING order for promotion purchase.
        Called by the API endpoint.
        """
        try:
            lock = VisibilityPromotionPurchaseLock.lock(business_id)
        except RedlockError as exc:
            raise ValidationError(
                "Another purchase is in progress. Please try again in a moment."
            ) from exc
        if not lock:
            raise ValidationError("Another purchase is in progress. Please try again in a moment.")

        try:
            with transaction.atomic():
                if not PromotionEligibilityService.can_purchase_now(
                    business_id, BoostStatusProvider.get_from_business_id(business_id)
                ):
                    raise ValidationError("Cannot purchase promotion at this time")

                try:
                    offer = VisibilityPromotionOffer.objects.get(id=offer_id, deleted__isnull=True)
                except VisibilityPromotionOffer.DoesNotExist as exc:
                    raise ValidationError("Selected offer is not available") from exc

                order = VisibilityPromotionOrder.objects.create(
                    business_id=business_id,
                    offer=offer,
                    price_net=offer.price_net,
                    duration_days=offer.duration_days,
                    status=OrderStatus.PENDING,
                )

                return order
        finally:
            VisibilityPromotionPurchaseLock.try_to_unlock(lock)

    @classmethod
    def activate_promotion(cls, order_id: int) -> int:
        """
        Activates promotion for a PENDING order immediately (MVP phase).
        Creates the promotion period and starts the visibility boost.
        Called by the Celery task.
        """
        with transaction.atomic():
            try:
                order = VisibilityPromotionOrder.objects.get(id=order_id, deleted__isnull=True)
            except VisibilityPromotionOrder.DoesNotExist as exc:
                raise ValidationError("Order not found") from exc

            if order.status != OrderStatus.PENDING:
                raise ValidationError("Order is not in pending status")

            now = tznow()
            active_till = now + timedelta(days=order.duration_days)

            promotion_period = PromotionPeriod.objects.create(
                business_id=order.business_id,
                active_from=now,
                active_till=active_till,
                status=PromotionStatus.ACTIVE,
                order=order,
            )

            BoostScoreManager.set_visibility_promotion_score(order.business_id)

            # note: order status remains PENDING as in the current implementation we collect
            # the payment at night

        return promotion_period.id


class GetPromotionStatusUseCase:
    """
    Retrieves current promotion status for businesses.
    Provides information about active or inactive promotion and remaining time.
    """

    @classmethod
    def get_promotion_status(cls, business_id: int) -> dict:
        """Returns visibility promotion status for BusinessFeatureStatusHandler"""

        active_promotion = PromotionPeriod.objects.filter(
            business_id=business_id, status=PromotionStatus.ACTIVE, deleted__isnull=True
        ).first()

        if not active_promotion:
            return {
                'status': FeatureStatus.INACTIVE,
                'status_color': FeatureStatusColor.GRAY,
                'label': _(FeatureLabel.INACTIVE),
                'date': None,
                'remaining_time': None,
            }

        now = tznow()
        remaining = active_promotion.active_till - now

        if remaining.total_seconds() <= 0:
            # Promotion should have expired (expiration service hasn't processed it yet)
            remaining_days = 0
            remaining_hours = 0
        else:
            remaining_days = remaining.days
            remaining_hours = remaining.seconds // 3600

        return {
            'status': FeatureStatus.ACTIVE,
            'status_color': FeatureStatusColor.GREEN,
            'label': _(FeatureLabel.ACTIVE),
            'date': active_promotion.active_from.strftime('%Y-%m-%d'),
            'remaining_time': f"{remaining_days} days, {remaining_hours} hours",
        }


class GetPromotionResultsUseCase:
    """
    Retrieves current promotion results and statistics for businesses.
    """


class PromotionExpirationService:
    """
    Manages the transition of active promotions to expired state and handles
    cleanup operations when promotion periods end.
    """
