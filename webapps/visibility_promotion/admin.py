from django.contrib import admin

from lib.admin_helpers import BaseModelAdmin, NoAddDelMixin
from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin
from webapps.user.groups import GroupNameV2
from webapps.visibility_promotion.models import (
    VisibilityPromotionCandidate,
    VisibilityPromotionOffer,
    VisibilityPromotionOrder,
    PromotionPeriod,
)


class VisibilityPromotionOfferAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    model = VisibilityPromotionOffer
    list_display = ['plan_name', 'duration_days', 'price_net']
    list_display_links = ['plan_name']
    readonly_fields = ['deleted']

    ordering = ('duration_days',)
    hide_keyword_field = True

    def get_actions(self, request):
        return None  # disables bulk actions (removes the checkbox column)

    def plan_name(self, obj):
        """Display user-friendly plan name"""
        return f'{obj.duration_days}-day plan'

    plan_name.short_description = 'Plan'


class VisibilityPromotionCandidateAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    model = VisibilityPromotionCandidate
    hide_keyword_field = True
    list_display = ['id', 'business_id', 'active']
    search_fields = ('business_id',)
    list_filter = ('active',)

    actions = [
        'make_candidates_active',
        'make_candidates_inactive',
    ]

    @admin.action(description='Mark selected candidates as active')
    def make_candidates_active(self, request, queryset):
        rows_updated = queryset.update(active=True)
        self.message_user(
            request, f'{rows_updated} candidate(s) were successfully marked as active.'
        )

    @admin.action(description='Mark selected candidates as inactive')
    def make_candidates_inactive(self, request, queryset):
        rows_updated = queryset.update(active=False)
        self.message_user(
            request, f'{rows_updated} candidate(s) were successfully marked as inactive.'
        )


class VisibilityPromotionOrderAdmin(GroupPermissionMixin, NoAddDelMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    model = VisibilityPromotionOrder

    list_display = [
        'id',
        'business_id',
        'offer_display',
        'price_net',
        'duration_days',
        'status',
        'created',
    ]
    list_display_links = ['id']
    list_filter = ['status', 'created']
    search_fields = ['business_id', 'id']
    readonly_fields = [
        'business_id',
        'offer',
        'price_net',
        'duration_days',
        'status',
        'billing_transaction_id',
        'created',
        'updated',
        'deleted',
    ]

    ordering = ('-created',)
    hide_keyword_field = True

    def get_actions(self, request):
        return None

    def offer_display(self, obj):
        """Display offer information"""
        return f"{obj.offer.duration_days}-day plan ({obj.offer.price_net})"

    offer_display.short_description = "Offer"

    def has_change_permission(self, request, obj=None):
        return False  # Read-only


class PromotionPeriodAdmin(GroupPermissionMixin, NoAddDelMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    model = PromotionPeriod

    list_display = [
        'id',
        'business_id',
        'status',
        'active_from',
        'active_till',
        'order_display',
        'created',
    ]
    list_display_links = ['id']
    list_filter = ['status', 'active_from', 'created']
    search_fields = ['business_id', 'id', 'order__id']
    readonly_fields = [
        'business_id',
        'active_from',
        'active_till',
        'status',
        'order',
        'created',
        'updated',
        'deleted',
    ]

    ordering = ('-created',)
    hide_keyword_field = True

    def get_actions(self, request):
        return None

    def order_display(self, obj):
        """Display linked order information"""
        return f"Order #{obj.order.id}"

    order_display.short_description = "Order"

    def has_change_permission(self, request, obj=None):
        return False  # Read-only


admin.site.register(VisibilityPromotionOffer, VisibilityPromotionOfferAdmin)
admin.site.register(VisibilityPromotionCandidate, VisibilityPromotionCandidateAdmin)
admin.site.register(VisibilityPromotionOrder, VisibilityPromotionOrderAdmin)
admin.site.register(PromotionPeriod, PromotionPeriodAdmin)
