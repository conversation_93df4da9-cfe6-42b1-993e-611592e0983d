# Generated by Django 4.2.23 on 2025-08-11 07:56

from django.db import migrations, models
import lib.models


class Migration(migrations.Migration):

    dependencies = [
        ('visibility_promotion', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='VisibilityPromotionCandidate',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                ('business_id', models.IntegerField(db_index=True)),
                (
                    'active',
                    models.BooleanField(
                        default=False,
                        help_text='Controls if the business can be considered as eligible for promotion',
                    ),
                ),
            ],
            options={
                'verbose_name': 'Visibility Promotion Candidate',
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
    ]
