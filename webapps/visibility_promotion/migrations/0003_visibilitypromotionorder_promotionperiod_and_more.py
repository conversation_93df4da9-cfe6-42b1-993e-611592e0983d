# Generated by Django 4.2.23 on 2025-08-13 06:44

from django.db import migrations, models
import django.db.models.deletion
import lib.models
import webapps.visibility_promotion.enums


class Migration(migrations.Migration):

    dependencies = [
        ('visibility_promotion', '0002_visibilitypromotioncandidate'),
    ]

    operations = [
        migrations.CreateModel(
            name='VisibilityPromotionOrder',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                ('business_id', models.IntegerField(db_index=True)),
                ('price_net', models.DecimalField(decimal_places=2, max_digits=10)),
                ('duration_days', models.PositiveIntegerField()),
                (
                    'status',
                    models.CharField(
                        choices=[
                            ('PENDING', 'Pending'),
                            ('COMPLETED', 'Completed'),
                            ('FAILED', 'Failed'),
                        ],
                        db_index=True,
                        default=webapps.visibility_promotion.enums.OrderStatus['PENDING'],
                        max_length=32,
                    ),
                ),
                (
                    'billing_transaction_id',
                    models.IntegerField(blank=True, db_index=True, null=True),
                ),
                (
                    'offer',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to='visibility_promotion.visibilitypromotionoffer',
                    ),
                ),
            ],
            options={
                'verbose_name': 'Visibility Promotion Order',
                'ordering': ['-created'],
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
        migrations.CreateModel(
            name='PromotionPeriod',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                ('business_id', models.IntegerField(db_index=True)),
                ('active_from', models.DateTimeField()),
                ('active_till', models.DateTimeField()),
                (
                    'status',
                    models.CharField(
                        choices=[
                            ('ACTIVE', 'Active'),
                            ('EXPIRED', 'Expired'),
                            ('TERMINATED', 'Terminated Early'),
                        ],
                        db_index=True,
                        default=webapps.visibility_promotion.enums.PromotionStatus['ACTIVE'],
                        max_length=32,
                    ),
                ),
                (
                    'order',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to='visibility_promotion.visibilitypromotionorder',
                    ),
                ),
            ],
            options={
                'verbose_name': 'Promotion Period',
                'ordering': ['-active_from'],
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
        migrations.AddConstraint(
            model_name='promotionperiod',
            constraint=models.UniqueConstraint(
                condition=models.Q(
                    ('deleted__isnull', True),
                    ('status', webapps.visibility_promotion.enums.PromotionStatus['ACTIVE']),
                ),
                fields=('business_id',),
                name='unique_active_promotion_per_business',
            ),
        ),
    ]
