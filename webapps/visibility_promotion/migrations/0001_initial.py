# Generated by Django 4.2.23 on 2025-08-06 13:36

from django.db import migrations, models
import lib.models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name='VisibilityPromotionOffer',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True, primary_key=True, serialize=False, verbose_name='ID'
                    ),
                ),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created (UTC)')),
                (
                    'updated',
                    models.DateTimeField(
                        auto_now=True, db_index=True, verbose_name='Updated (UTC)'
                    ),
                ),
                (
                    'deleted',
                    models.DateTimeField(blank=True, null=True, verbose_name='Deleted (UTC)'),
                ),
                (
                    'duration_days',
                    models.PositiveIntegerField(
                        help_text='Number of days for this offer (7, 14, or 30)', unique=True
                    ),
                ),
                (
                    'price_net',
                    models.DecimalField(decimal_places=2, help_text='Net price', max_digits=10),
                ),
            ],
            options={
                'verbose_name': 'Visibility Promotion Offer',
                'ordering': ['duration_days'],
            },
            managers=[
                ('objects', lib.models.AutoUpdateManager()),
            ],
        ),
    ]
