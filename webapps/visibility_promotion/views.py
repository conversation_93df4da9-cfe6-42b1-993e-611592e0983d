from rest_framework import status
from rest_framework.response import Response

from bo_obs.datadog.enums import BooksyTeams
from django.core.exceptions import ValidationError

from drf_api.base_views import BaseBooksySessionGenericAPIView
from drf_api.mixins import BusinessViewValidatorMixin
from lib.feature_flag.feature.visibility_promotion import VisibilityPromotionActiveOnBackend
from webapps.business.enums import FeatureStatus, FeatureLabel, FeatureStatusColor
from webapps.business.models import Resource
from webapps.visibility_promotion.serializers import (
    DashboardSerializer,
    OffersListSerializer,
    PurchasePromotionSerializer,
    TaskIdResponseSerializer,
)
from webapps.visibility_promotion.services import (
    GetAvailableOffersUseCase,
    PurchasePromotionUseCase,
)
from webapps.visibility_promotion.tasks import purchase_visibility_promotion_task


class VisibilityPromotionView(BusinessViewValidatorMixin, BaseBooksySessionGenericAPIView):
    required_minimum_access_level = Resource.STAFF_ACCESS_LEVEL_MANAGER

    def dispatch(self, request, *args, **kwargs):
        if not VisibilityPromotionActiveOnBackend():
            return Response(status=status.HTTP_404_NOT_FOUND)
        return super().dispatch(request, *args, **kwargs)


class OffersView(VisibilityPromotionView):
    """Get available promotion offers for the business"""

    booksy_teams = (BooksyTeams.CUSTOMER_SEARCH,)
    serializer_class = OffersListSerializer

    def get(self, request, business_pk):  # pylint: disable=unused-argument
        business = self.get_business()

        offers_data = GetAvailableOffersUseCase.get_offers_for_business(business)

        response_data = {'offers': offers_data}
        serializer = self.get_serializer(response_data)
        return Response(serializer.data, status=status.HTTP_200_OK)


class PurchaseView(VisibilityPromotionView):
    """Purchase a visibility promotion"""

    booksy_teams = (BooksyTeams.CUSTOMER_SEARCH,)
    query_serializer_class = PurchasePromotionSerializer
    serializer_class = TaskIdResponseSerializer

    def post(self, request, business_pk):  # pylint: disable=unused-argument
        business = self.get_business()

        request_serializer = self.query_serializer_class(data=request.data)
        request_serializer.is_valid(raise_exception=True)

        offer_id = request_serializer.validated_data['offer_id']

        try:
            order = PurchasePromotionUseCase.place_order(business.id, offer_id)
        except ValidationError as e:
            error_message = str(e)
            if hasattr(e, 'messages') and e.messages:
                error_message = e.messages[0]
            return Response({'error': error_message}, status=status.HTTP_400_BAD_REQUEST)

        task = purchase_visibility_promotion_task.delay(order.id)

        response_data = {'task_id': str(task.id)}
        serializer = self.get_serializer(response_data)
        return Response(serializer.data, status=status.HTTP_202_ACCEPTED)


class PromotionDashboardView(VisibilityPromotionView):
    """Get current promotion status for the business"""

    booksy_teams = (BooksyTeams.CUSTOMER_SEARCH,)
    serializer_class = DashboardSerializer

    def get(self, request, business_pk):  # pylint: disable=unused-argument
        support_links = [
            {'type': 'url', 'title': 'How it works?', 'url': 'https://booksy.com'},
            {'type': 'url', 'title': 'How we calculate the data?', 'url': 'https://booksy.com'},
            {'type': 'url', 'title': 'Terms & Conditions', 'url': 'https://booksy.com'},
            {'type': 'survey', 'title': 'Give feedback'},
            {'type': 'chat', 'title': 'Contact Us'},
        ]

        response_data = {
            'support_links': support_links,
            'current_plan': {
                'status': FeatureStatus.ACTIVE,
                'status_color': FeatureStatusColor.GREEN,
                'label': FeatureLabel.ACTIVE,
                'promotion_title': '7-day plan',
                'time_left_string': 'Remaining: 6 days, 5 hours',
                'time_left_percentage': 86,  # Approximately 6 days out of 7 = ~86%
            },
        }

        serializer = self.get_serializer(response_data)
        return Response(serializer.data, status=status.HTTP_200_OK)
