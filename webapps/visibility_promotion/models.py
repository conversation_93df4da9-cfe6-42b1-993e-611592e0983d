from django.core.exceptions import ValidationError
from django.db import models

from lib.models import ArchiveModel, ChangeArchivedModel
from webapps.visibility_promotion.enums import OrderStatus, PromotionStatus


class VisibilityPromotionOffer(ArchiveModel):
    """
    MVP model for visibility promotion offers.
    Limited to exactly 3 offers - one for each duration (7, 14, 30 days).
    """

    class Meta:
        verbose_name = 'Visibility Promotion Offer'
        ordering = ['duration_days']

    duration_days = models.PositiveIntegerField(
        unique=True, help_text="Number of days for this offer (7, 14, or 30)"
    )
    price_net = models.DecimalField(max_digits=10, decimal_places=2, help_text="Net price")

    def __str__(self):
        return f"{self.duration_days} days plan - {self.price_net} net"

    def clean(self):
        super().clean()

        # MVP limitation: only allow 7, 14, and 30 days
        if self.duration_days not in [7, 14, 30]:
            raise ValidationError("Only 7, 14, and 30 day offers are allowed in MVP")

        # MVP limitation: prevent more than 3 records
        if not self.pk:  # Only check on creation
            existing_count = VisibilityPromotionOffer.objects.filter(deleted__isnull=True).count()
            if existing_count >= 3:
                raise ValidationError(
                    "MVP limitation: Only 3 offers are allowed (7, 14, and 30 days)"
                )

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)


class VisibilityPromotionCandidate(ChangeArchivedModel):
    """
    Model for businesses chosen as candidates for the MVP visibility promotion.
    """

    class Meta:
        verbose_name = 'Visibility Promotion Candidate'

    business_id = models.IntegerField(db_index=True)
    active = models.BooleanField(
        default=False,
        help_text="Controls if the business can be considered as eligible for promotion",
    )

    def __str__(self):
        return (
            f'{self._meta.verbose_name} - id:{self.id}, '
            f'business_id:{self.business_id}, active:{self.active}'
        )


class VisibilityPromotionOrder(ArchiveModel):
    """
    Tracks every attempt to purchase a visibility promotion offer.
    Contains snapshot of offer data to preserve historical accuracy.
    """

    class Meta:
        verbose_name = 'Visibility Promotion Order'
        ordering = ['-created']

    business_id = models.IntegerField(db_index=True)
    # ^ business ID stored as integer to avoid cross-domain coupling
    offer = models.ForeignKey(VisibilityPromotionOffer, on_delete=models.PROTECT)

    price_net = models.DecimalField(max_digits=10, decimal_places=2)
    duration_days = models.PositiveIntegerField()
    # ^ snapshot of offer data at time of purchase to preserve historical accuracy

    status = models.CharField(
        db_index=True, max_length=32, choices=OrderStatus.choices(), default=OrderStatus.PENDING
    )
    billing_transaction_id = models.IntegerField(null=True, blank=True, db_index=True)
    # ^ reference to BillingTransaction ID (no FK to avoid coupling)

    def __str__(self):
        return (
            f"Order {self.id} - Businsess {self.business_id} - {self.duration_days} days"
            f" - {self.price_net} net ({self.status})"
        )


class PromotionPeriod(ArchiveModel):
    """
    Represents an active or historical visibility promotion period for a business.
    This is the source of truth for promotion status and timing.
    """

    class Meta:
        verbose_name = 'Promotion Period'
        ordering = ['-active_from']
        constraints = [
            models.UniqueConstraint(
                fields=['business_id'],
                condition=models.Q(status=PromotionStatus.ACTIVE, deleted__isnull=True),
                name='unique_active_promotion_per_business',
            )
        ]

    business_id = models.IntegerField(db_index=True)
    # ^ business ID stored as integer to avoid cross-domain coupling
    active_from = models.DateTimeField()
    active_till = models.DateTimeField()
    # ^ always set - when promotion should end
    status = models.CharField(
        db_index=True,
        max_length=32,
        choices=PromotionStatus.choices(),
        default=PromotionStatus.ACTIVE,
    )
    order = models.ForeignKey('VisibilityPromotionOrder', on_delete=models.PROTECT)

    def __str__(self):
        return (
            f"Business {self.business_id} promotion ({self.status})"
            f" from {self.active_from.strftime('%Y-%m-%d %H:%M')}"
            f" till {self.active_till.strftime('%Y-%m-%d %H:%M')}"
        )

    @property
    def is_active(self):
        return self.status == PromotionStatus.ACTIVE

    def clean(self):
        super().clean()

        # Ensure active_till is after active_from
        if self.active_from >= self.active_till:
            raise ValidationError("End datetime must be after start datetime")
