from django.utils.translation import gettext_lazy as _
from lib.enums import StrChoicesEnum


class OrderStatus(StrChoicesEnum):
    PENDING = 'PENDING', _('Pending')
    COMPLETED = 'COMPLETED', _('Completed')
    FAILED = 'FAILED', _('Failed')


class PromotionStatus(StrChoicesEnum):
    ACTIVE = 'ACTIVE', _('Active')
    EXPIRED = 'EXPIRED', _('Expired')
    TERMINATED = 'TERMINATED', _('Terminated Early')
