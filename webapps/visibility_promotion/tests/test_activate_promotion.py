from datetime import datetime, timedelta, timezone
from decimal import Decimal
from unittest.mock import patch
from django.core.exceptions import ValidationError
from django.test import TestCase
from freezegun import freeze_time

from lib.tools import tznow
from webapps.visibility_promotion.models import (
    VisibilityPromotionOffer,
    VisibilityPromotionOrder,
    PromotionPeriod,
)
from webapps.visibility_promotion.enums import OrderStatus, PromotionStatus
from webapps.visibility_promotion.services import PurchasePromotionUseCase


# Constant datetime used across multiple tests
FIXED_NOW = datetime(2025, 7, 15, 10, tzinfo=timezone.utc)


@patch('webapps.visibility_promotion.services.BoostScoreManager.set_visibility_promotion_score')
class TestActivatePromotion(TestCase):
    """Test cases for the activate_promotion function"""

    @classmethod
    def setUpTestData(cls):
        cls.business_id = 12345
        cls.offer = VisibilityPromotionOffer.objects.create(
            duration_days=7, price_net=Decimal('29.99')
        )

    def setUp(self):
        self.pending_order = VisibilityPromotionOrder.objects.create(
            business_id=self.business_id,
            offer=self.offer,
            price_net=self.offer.price_net,
            duration_days=self.offer.duration_days,
            status=OrderStatus.PENDING,
        )

    @freeze_time(FIXED_NOW)
    def test_activate_promotion_success(self, mock_boost_score):
        expected_active_till = FIXED_NOW + timedelta(days=self.offer.duration_days)

        promotion_period_id = PurchasePromotionUseCase.activate_promotion(self.pending_order.id)

        self.assertIsInstance(promotion_period_id, int)

        promotion_period = PromotionPeriod.objects.get(id=promotion_period_id)
        self.assertEqual(promotion_period.business_id, self.business_id)
        self.assertEqual(promotion_period.active_from, FIXED_NOW)
        self.assertEqual(promotion_period.active_till, expected_active_till)
        self.assertEqual(promotion_period.status, PromotionStatus.ACTIVE)
        self.assertEqual(promotion_period.order, self.pending_order)

        mock_boost_score.assert_called_once_with(self.business_id)

        self.pending_order.refresh_from_db()
        self.assertEqual(self.pending_order.status, OrderStatus.PENDING)

    def test_activate_promotion_order_not_found(self, mock_boost_score):
        with self.assertRaises(ValidationError) as context:
            PurchasePromotionUseCase.activate_promotion(99999)

        self.assertEqual(str(context.exception.message), "Order not found")
        mock_boost_score.assert_not_called()
        self.assertEqual(PromotionPeriod.objects.count(), 0)

    def test_activate_promotion_deleted_order(self, mock_boost_score):
        self.pending_order.deleted = tznow()
        self.pending_order.save()

        with self.assertRaises(ValidationError) as context:
            PurchasePromotionUseCase.activate_promotion(self.pending_order.id)

        self.assertEqual(str(context.exception.message), "Order not found")
        mock_boost_score.assert_not_called()
        self.assertEqual(PromotionPeriod.objects.count(), 0)

    def test_activate_promotion_non_pending_status(self, mock_boost_score):
        self.pending_order.status = OrderStatus.COMPLETED
        self.pending_order.save()

        with self.assertRaises(ValidationError) as context:
            PurchasePromotionUseCase.activate_promotion(self.pending_order.id)

        self.assertEqual(str(context.exception.message), "Order is not in pending status")
        mock_boost_score.assert_not_called()

        self.pending_order.status = OrderStatus.FAILED
        self.pending_order.save()

        with self.assertRaises(ValidationError) as context:
            PurchasePromotionUseCase.activate_promotion(self.pending_order.id)

        self.assertEqual(str(context.exception.message), "Order is not in pending status")
        mock_boost_score.assert_not_called()
        self.assertEqual(PromotionPeriod.objects.count(), 0)

    @freeze_time(FIXED_NOW)
    def test_activate_promotion_transaction_rollback_on_boost_score_failure(self, mock_boost_score):
        mock_boost_score.side_effect = Exception("Boost score service failed")

        with self.assertRaises(Exception) as context:
            PurchasePromotionUseCase.activate_promotion(self.pending_order.id)

        self.assertEqual(str(context.exception), "Boost score service failed")
        self.assertEqual(PromotionPeriod.objects.count(), 0)
