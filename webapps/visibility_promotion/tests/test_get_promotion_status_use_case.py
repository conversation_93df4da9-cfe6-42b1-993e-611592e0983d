from datetime import datetime, timedelta, timezone
from decimal import Decimal

from django.test import TestCase
from freezegun import freeze_time

from webapps.business.enums import FeatureStatus, FeatureStatusColor, FeatureLabel
from webapps.visibility_promotion.models import (
    VisibilityPromotionOffer,
    VisibilityPromotionOrder,
    PromotionPeriod,
)
from webapps.visibility_promotion.enums import OrderStatus, PromotionStatus
from webapps.visibility_promotion.services import GetPromotionStatusUseCase


# Constant datetime used across multiple tests
FIXED_NOW = datetime(2025, 7, 15, 10, tzinfo=timezone.utc)


class TestGetPromotionStatusUseCase(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.business_id = 12345
        cls.other_business_id = 67890
        cls.offer = VisibilityPromotionOffer.objects.create(
            duration_days=7, price_net=Decimal('29.99')
        )

    def setUp(self):
        self.order = VisibilityPromotionOrder.objects.create(
            business_id=self.business_id,
            offer=self.offer,
            price_net=self.offer.price_net,
            duration_days=self.offer.duration_days,
            status=OrderStatus.PENDING,
        )

    @freeze_time(FIXED_NOW)
    def test_no_promotion_exists(self):
        result = GetPromotionStatusUseCase.get_promotion_status(self.business_id)

        expected = {
            'status': FeatureStatus.INACTIVE,
            'status_color': FeatureStatusColor.GRAY,
            'label': FeatureLabel.INACTIVE,
            'date': None,
            'remaining_time': None,
        }
        self.assertEqual(result, expected)

    @freeze_time(FIXED_NOW)
    def test_no_active_promotion_only_expired(self):
        PromotionPeriod.objects.create(
            business_id=self.business_id,
            active_from=FIXED_NOW - timedelta(days=10),
            active_till=FIXED_NOW - timedelta(days=3),
            status=PromotionStatus.EXPIRED,
            order=self.order,
        )

        result = GetPromotionStatusUseCase.get_promotion_status(self.business_id)

        expected = {
            'status': FeatureStatus.INACTIVE,
            'status_color': FeatureStatusColor.GRAY,
            'label': FeatureLabel.INACTIVE,
            'date': None,
            'remaining_time': None,
        }
        self.assertEqual(result, expected)

    @freeze_time(FIXED_NOW)
    def test_no_active_promotion_only_terminated(self):
        PromotionPeriod.objects.create(
            business_id=self.business_id,
            active_from=FIXED_NOW - timedelta(days=10),
            active_till=FIXED_NOW - timedelta(days=3),
            status=PromotionStatus.TERMINATED,
            order=self.order,
        )

        result = GetPromotionStatusUseCase.get_promotion_status(self.business_id)

        expected = {
            'status': FeatureStatus.INACTIVE,
            'status_color': FeatureStatusColor.GRAY,
            'label': FeatureLabel.INACTIVE,
            'date': None,
            'remaining_time': None,
        }
        self.assertEqual(result, expected)

    @freeze_time(FIXED_NOW)
    def test_no_active_promotion_soft_deleted(self):
        promotion = PromotionPeriod.objects.create(
            business_id=self.business_id,
            active_from=FIXED_NOW - timedelta(days=1),
            active_till=FIXED_NOW + timedelta(days=6),
            status=PromotionStatus.ACTIVE,
            order=self.order,
        )
        promotion.delete()

        result = GetPromotionStatusUseCase.get_promotion_status(self.business_id)

        expected = {
            'status': FeatureStatus.INACTIVE,
            'status_color': FeatureStatusColor.GRAY,
            'label': FeatureLabel.INACTIVE,
            'date': None,
            'remaining_time': None,
        }
        self.assertEqual(result, expected)

    @freeze_time(FIXED_NOW)
    def test_active_promotion_days_remaining(self):
        active_from = FIXED_NOW - timedelta(days=2)
        active_till = FIXED_NOW + timedelta(days=5, hours=10)

        PromotionPeriod.objects.create(
            business_id=self.business_id,
            active_from=active_from,
            active_till=active_till,
            status=PromotionStatus.ACTIVE,
            order=self.order,
        )

        result = GetPromotionStatusUseCase.get_promotion_status(self.business_id)

        expected = {
            'status': FeatureStatus.ACTIVE,
            'status_color': FeatureStatusColor.GREEN,
            'label': FeatureLabel.ACTIVE,
            'date': active_from.strftime('%Y-%m-%d'),
            'remaining_time': "5 days, 10 hours",
        }
        self.assertEqual(result, expected)

    @freeze_time(FIXED_NOW)
    def test_active_promotion_hours_remaining(self):
        active_from = FIXED_NOW - timedelta(days=6, hours=16)
        active_till = FIXED_NOW + timedelta(hours=8)

        PromotionPeriod.objects.create(
            business_id=self.business_id,
            active_from=active_from,
            active_till=active_till,
            status=PromotionStatus.ACTIVE,
            order=self.order,
        )

        result = GetPromotionStatusUseCase.get_promotion_status(self.business_id)

        expected = {
            'status': FeatureStatus.ACTIVE,
            'status_color': FeatureStatusColor.GREEN,
            'label': FeatureLabel.ACTIVE,
            'date': active_from.strftime('%Y-%m-%d'),
            'remaining_time': "0 days, 8 hours",
        }
        self.assertEqual(result, expected)

    @freeze_time(FIXED_NOW)
    def test_active_promotion_just_started(self):
        active_from = FIXED_NOW
        active_till = FIXED_NOW + timedelta(days=7)

        PromotionPeriod.objects.create(
            business_id=self.business_id,
            active_from=active_from,
            active_till=active_till,
            status=PromotionStatus.ACTIVE,
            order=self.order,
        )

        result = GetPromotionStatusUseCase.get_promotion_status(self.business_id)

        expected = {
            'status': FeatureStatus.ACTIVE,
            'status_color': FeatureStatusColor.GREEN,
            'label': FeatureLabel.ACTIVE,
            'date': active_from.strftime('%Y-%m-%d'),
            'remaining_time': "7 days, 0 hours",
        }
        self.assertEqual(result, expected)

    @freeze_time(FIXED_NOW)
    def test_active_promotion_expired_not_processed(self):
        active_from = FIXED_NOW - timedelta(days=7, hours=5)
        active_till = FIXED_NOW - timedelta(hours=5)

        PromotionPeriod.objects.create(
            business_id=self.business_id,
            active_from=active_from,
            active_till=active_till,
            status=PromotionStatus.ACTIVE,
            order=self.order,
        )

        result = GetPromotionStatusUseCase.get_promotion_status(self.business_id)

        expected = {
            'status': FeatureStatus.ACTIVE,
            'status_color': FeatureStatusColor.GREEN,
            'label': FeatureLabel.ACTIVE,
            'date': active_from.strftime('%Y-%m-%d'),
            'remaining_time': "0 days, 0 hours",
        }
        self.assertEqual(result, expected)
