import random
from decimal import Decimal
from unittest.mock import patch, <PERSON><PERSON>, Magic<PERSON>ock
from django.core.exceptions import ValidationError
from django.test import TestCase

from lib.locks import RedlockError
from lib.tools import tznow
from webapps.visibility_promotion.models import (
    VisibilityPromotionOffer,
    VisibilityPromotionOrder,
    PromotionPeriod,
)
from webapps.visibility_promotion.enums import OrderStatus, PromotionStatus
from webapps.visibility_promotion.services import PurchasePromotionUseCase


@patch(
    'webapps.visibility_promotion.services.PromotionEligibilityService.can_purchase_now',
    <PERSON>ck(return_value=True),
)
@patch(
    'webapps.visibility_promotion.services.BoostStatusProvider.get_from_business_id',
    <PERSON>ck(return_value="doesn't matter"),
)
class TestPlaceOrderSuccess(TestCase):
    """Test successful order placement scenarios"""

    @classmethod
    def setUpTestData(cls):
        cls.business_id = 12345
        cls.offer = VisibilityPromotionOffer.objects.create(
            duration_days=7, price_net=Decimal('29.99')
        )

    @patch('webapps.visibility_promotion.services.VisibilityPromotionPurchaseLock')
    def test_place_order_success(self, mock_lock_class):
        """Test successful order placement with valid data and all expected fields populated"""
        order = PurchasePromotionUseCase.place_order(self.business_id, self.offer.id)

        self.assertIsInstance(order, VisibilityPromotionOrder)
        self.assertEqual(order.business_id, self.business_id)
        self.assertEqual(order.offer, self.offer)
        self.assertEqual(order.price_net, self.offer.price_net)
        self.assertEqual(order.duration_days, self.offer.duration_days)
        self.assertEqual(order.status, OrderStatus.PENDING)
        self.assertIsNone(order.billing_transaction_id)
        self.assertIsNotNone(order.created)

        mock_lock_class.lock.assert_called_once_with(self.business_id)
        mock_lock_class.try_to_unlock.assert_called_once()

    @patch('webapps.visibility_promotion.services.VisibilityPromotionPurchaseLock', MagicMock())
    def test_place_order_snapshot_data_correctness(self):
        """Test that order captures offer data at purchase time (snapshot)"""
        original_price = self.offer.price_net
        original_duration = self.offer.duration_days

        order = PurchasePromotionUseCase.place_order(self.business_id, self.offer.id)

        # Modify offer after order creation
        self.offer.price_net = Decimal('99.99')
        self.offer.duration_days = 30
        self.offer.save()

        # Order should still have original values (snapshot)
        order.refresh_from_db()
        self.assertEqual(order.price_net, original_price)
        self.assertEqual(order.duration_days, original_duration)


@patch(
    'webapps.visibility_promotion.services.BoostStatusProvider.get_from_business_id',
    Mock(return_value="doesn't matter"),
)
class TestPlaceOrderEligibilityFailures(TestCase):
    """Test order placement failures due to eligibility issues"""

    @classmethod
    def setUpTestData(cls):
        cls.offer = VisibilityPromotionOffer.objects.create(
            duration_days=7, price_net=Decimal('29.99')
        )

        cls.business_with_pending_order_id = 12345
        VisibilityPromotionOrder.objects.create(
            business_id=cls.business_with_pending_order_id,
            offer=cls.offer,
            price_net=cls.offer.price_net,
            duration_days=cls.offer.duration_days,
            status=OrderStatus.PENDING,
        )

        cls.business_with_active_promotion_id = 67890
        PromotionPeriod.objects.create(
            business_id=cls.business_with_active_promotion_id,
            active_from=tznow(),
            active_till=tznow(),
            status=PromotionStatus.ACTIVE,
            order=VisibilityPromotionOrder.objects.create(
                business_id=cls.business_with_active_promotion_id,
                offer=cls.offer,
                price_net=cls.offer.price_net,
                duration_days=cls.offer.duration_days,
                status=OrderStatus.COMPLETED,
            ),
        )

    @patch('webapps.visibility_promotion.services.VisibilityPromotionPurchaseLock')
    def test_place_order_with_pending_order_fails(self, mock_lock_class):
        with self.assertRaises(ValidationError) as cm:
            PurchasePromotionUseCase.place_order(self.business_with_pending_order_id, self.offer.id)

        self.assertEqual(str(cm.exception), "['Cannot purchase promotion at this time']")
        mock_lock_class.try_to_unlock.assert_called_once()

    @patch('webapps.visibility_promotion.services.VisibilityPromotionPurchaseLock')
    def test_place_order_with_active_promotion_fails(self, mock_lock_class):
        """Test that business with active promotion cannot place new order"""
        with self.assertRaises(ValidationError) as cm:
            PurchasePromotionUseCase.place_order(
                self.business_with_active_promotion_id, self.offer.id
            )

        self.assertEqual(str(cm.exception), "['Cannot purchase promotion at this time']")
        mock_lock_class.try_to_unlock.assert_called_once()

    @patch(
        'webapps.visibility_promotion.services.PromotionEligibilityService.can_purchase_now',
        Mock(return_value=False),
    )
    @patch('webapps.visibility_promotion.services.VisibilityPromotionPurchaseLock')
    def test_place_order_when_eligibility_service_returns_false(self, mock_lock_class):
        not_eligible_business_id = 99999

        with self.assertRaises(ValidationError) as cm:
            PurchasePromotionUseCase.place_order(not_eligible_business_id, self.offer.id)

        self.assertEqual(str(cm.exception), "['Cannot purchase promotion at this time']")
        mock_lock_class.try_to_unlock.assert_called_once()


@patch(
    'webapps.visibility_promotion.services.PromotionEligibilityService.can_purchase_now',
    Mock(return_value=True),
)
@patch(
    'webapps.visibility_promotion.services.BoostStatusProvider.get_from_business_id',
    Mock(return_value="doesn't matter"),
)
class TestPlaceOrderOfferValidation(TestCase):
    """Test order placement failures due to offer validation issues"""

    @classmethod
    def setUpTestData(cls):
        cls.business_id = 12345
        cls.valid_offer = VisibilityPromotionOffer.objects.create(
            duration_days=7, price_net=Decimal('29.99')
        )
        cls.deleted_offer = VisibilityPromotionOffer.objects.create(
            duration_days=14, price_net=Decimal('49.99')
        )
        cls.deleted_offer.delete()  # soft delete
        cls.non_existent_offer_id = 99999

    @patch('webapps.visibility_promotion.services.VisibilityPromotionPurchaseLock')
    def test_place_order_with_invalid_offer_fails(self, mock_lock_class):
        with self.assertRaises(ValidationError) as cm:
            PurchasePromotionUseCase.place_order(self.business_id, self.non_existent_offer_id)

        self.assertEqual(str(cm.exception), "['Selected offer is not available']")
        mock_lock_class.try_to_unlock.assert_called_once()

    @patch('webapps.visibility_promotion.services.VisibilityPromotionPurchaseLock')
    def test_place_order_with_deleted_offer_fails(self, mock_lock_class):
        with self.assertRaises(ValidationError) as cm:
            PurchasePromotionUseCase.place_order(self.business_id, self.deleted_offer.id)

        self.assertEqual(str(cm.exception), "['Selected offer is not available']")
        mock_lock_class.try_to_unlock.assert_called_once()


@patch(
    'webapps.visibility_promotion.services.PromotionEligibilityService.can_purchase_now',
    Mock(return_value=True),
)
@patch(
    'webapps.visibility_promotion.services.BoostStatusProvider.get_from_business_id',
    Mock(return_value="doesn't matter"),
)
class TestPlaceOrderLockBehavior(TestCase):
    """Test lock acquisition and release behavior"""

    @classmethod
    def setUpTestData(cls):
        cls.business_id = 12345
        cls.offer = VisibilityPromotionOffer.objects.create(
            duration_days=7, price_net=Decimal('29.99')
        )

    @patch('webapps.visibility_promotion.services.VisibilityPromotionPurchaseLock')
    def test_place_order_lock_acquisition_failure(self, mock_lock_class):
        """Test that lock acquisition failure raises appropriate ValidationError"""
        mock_lock_class.lock.side_effect = RedlockError("Lock failed")

        with self.assertRaises(ValidationError) as cm:
            PurchasePromotionUseCase.place_order(self.business_id, self.offer.id)

        self.assertEqual(
            str(cm.exception), "['Another purchase is in progress. Please try again in a moment.']"
        )
        mock_lock_class.try_to_unlock.assert_not_called()

    def test_place_order_concurrent_requests(self):
        """
        Test that concurrent requests are handled properly.
        Tests the real lock mechanism by mocking only try_to_unlock to prevent lock release.
        """
        from lib.locks import VisibilityPromotionPurchaseLock

        # Generate random business ID to avoid conflicts between test runs
        test_business_id = random.randint(100000, 999999)

        acquired_lock = None

        def mock_try_to_unlock(lock):
            # patch of `try_to_unlock` to capture the lock and prevent its release
            nonlocal acquired_lock
            acquired_lock = lock
            return True

        try:
            with patch(
                'webapps.visibility_promotion.services.VisibilityPromotionPurchaseLock'
                '.try_to_unlock',
                side_effect=mock_try_to_unlock,
            ):
                # the first call should succeed and acquire the lock
                order1 = PurchasePromotionUseCase.place_order(test_business_id, self.offer.id)
                self.assertEqual(order1.status, OrderStatus.PENDING)

                # verify that a lock was acquired and captured
                self.assertIsNotNone(acquired_lock)

            # the second call should fail because the lock is still held
            with self.assertRaises(ValidationError) as cm:
                PurchasePromotionUseCase.place_order(test_business_id, self.offer.id)

            self.assertEqual(
                str(cm.exception),
                "['Another purchase is in progress. Please try again in a moment.']",
            )

        finally:  # clean up: unlock the captured lock if we have it
            if acquired_lock:
                try:
                    # Only try to unlock if we have a proper lock object (not a boolean)
                    if hasattr(acquired_lock, 'resource'):
                        VisibilityPromotionPurchaseLock.try_to_unlock(acquired_lock)
                except Exception:  # pylint: disable=broad-exception-caught
                    # If cleanup fails, it's not critical for the test
                    pass

    @patch('webapps.visibility_promotion.services.VisibilityPromotionPurchaseLock')
    def test_place_order_lock_always_released(self, mock_lock_class):
        """Test that lock is released even when exceptions occur"""
        mock_lock = MagicMock()
        mock_lock_class.lock.return_value = mock_lock

        # force a database error by using invalid offer_id
        with self.assertRaises(ValidationError):
            PurchasePromotionUseCase.place_order(self.business_id, 99999)

        mock_lock_class.try_to_unlock.assert_called_once_with(mock_lock)


@patch(
    'webapps.visibility_promotion.services.BoostStatusProvider.get_from_business_id',
    Mock(return_value="doesn't matter"),
)
class TestPlaceOrderTransactionBehavior(TestCase):
    """Test atomic transaction behavior and edge cases"""

    @classmethod
    def setUpTestData(cls):
        cls.business_id = 12345
        cls.offer = VisibilityPromotionOffer.objects.create(
            duration_days=7, price_net=Decimal('29.99')
        )

    @patch('webapps.visibility_promotion.services.VisibilityPromotionPurchaseLock', MagicMock())
    def test_place_order_atomic_transaction(self):
        """Test that if eligibility check fails after lock, no order is created"""
        # Create a PENDING order to make eligibility fail
        VisibilityPromotionOrder.objects.create(
            business_id=self.business_id,
            offer=self.offer,
            price_net=self.offer.price_net,
            duration_days=self.offer.duration_days,
            status=OrderStatus.PENDING,
        )

        initial_order_count = VisibilityPromotionOrder.objects.count()

        with self.assertRaises(ValidationError):
            PurchasePromotionUseCase.place_order(self.business_id, self.offer.id)

        # No new order should be created due to transaction rollback
        final_order_count = VisibilityPromotionOrder.objects.count()
        self.assertEqual(final_order_count, initial_order_count)
