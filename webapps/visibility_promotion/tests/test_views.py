import uuid
from decimal import Decimal
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import pytest
from django.core.exceptions import ValidationError
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase

from lib.baker_utils import get_or_create_booking_source
from lib.feature_flag.feature.visibility_promotion import VisibilityPromotionActiveOnBackend
from lib.tests.utils import override_eppo_feature_flag
from webapps.booking.models import BookingSources
from webapps.business.baker_recipes import business_recipe, resource_recipe
from webapps.business.models import Resource
from webapps.consts import WEB
from webapps.invoicing.baker_recipes import user_recipe
from webapps.user.enums import AuthOriginEnum
from webapps.visibility_promotion.models import VisibilityPromotionOffer


@pytest.mark.django_db
class TestVisibilityPromotionViews(APITestCase):
    @classmethod
    def setUpTestData(cls):
        # Create shared data once per test class
        cls.booking_source = get_or_create_booking_source(
            app_type=BookingSources.BUSINESS_APP,
            name=WEB,
        )
        cls.user = user_recipe.make()
        cls.business = business_recipe.make()
        cls.resource = resource_recipe.make(
            business=cls.business,
            staff_user=cls.user,
            staff_access_level=Resource.STAFF_ACCESS_LEVEL_MANAGER,
        )

        cls.purchase_url = reverse('visibility_promotion_purchase', args=(cls.business.id,))
        cls.offers_url = reverse('visibility_promotion_offers', args=(cls.business.id,))
        cls.dashboard_url = reverse('visibility_promotion_dashboard', args=(cls.business.id,))

        # Create the standard 3 test offers
        cls.sample_offer = VisibilityPromotionOffer.objects.create(
            duration_days=7, price_net=Decimal('89.99')
        )
        VisibilityPromotionOffer.objects.create(duration_days=14, price_net=Decimal('149.99'))
        VisibilityPromotionOffer.objects.create(duration_days=30, price_net=Decimal('289.99'))

    def setUp(self):
        # Per-test setup for session and authentication
        self.headers = {'HTTP_X_API_KEY': self.booking_source.api_key}
        self.session = self.user.create_session(origin=AuthOriginEnum.BOOKSY, fingerprint='')
        self.client.credentials(HTTP_X_ACCESS_TOKEN=self.session.session_key)

    @override_eppo_feature_flag({VisibilityPromotionActiveOnBackend.flag_name: True})
    @patch('time.sleep', Mock(return_value=None))  # Mock sleep to avoid delays
    @patch('webapps.visibility_promotion.views.purchase_visibility_promotion_task')
    @patch(
        'webapps.visibility_promotion.services.PromotionEligibilityService.can_purchase_now',
        Mock(return_value=True),
    )
    @patch(
        'webapps.visibility_promotion.services.BoostStatusProvider.get_from_business_id',
        Mock(return_value="doesn't matter"),
    )
    def test_purchase_manager_access_success(self, mock_task):
        """Test that manager level user can access the endpoint"""
        mock_task.delay.return_value = MagicMock(id=uuid.uuid4())

        payload = {'offer_id': self.sample_offer.id}
        response = self.client.post(self.purchase_url, data=payload, **self.headers)

        # Verify basic stub functionality
        self.assertEqual(response.status_code, status.HTTP_202_ACCEPTED)
        self.assertIn('task_id', response.data)

        # Verify task was triggered
        self.assertEqual(mock_task.delay.call_count, 1)

    @override_eppo_feature_flag({VisibilityPromotionActiveOnBackend.flag_name: True})
    @patch(
        'webapps.visibility_promotion.services.TaxForNetSummary.for_business',
        Mock(return_value=Mock(gross_price=Decimal('110.69'), tax=Decimal('20.70'))),
    )
    @patch(
        'webapps.visibility_promotion.services.format_currency',
        Mock(side_effect=lambda x: f"{x:.2f} zł"),
    )
    def test_offers_manager_access_success(self):
        """Test that manager level user can access the offers endpoint with database data"""

        response = self.client.get(self.offers_url, **self.headers)

        # Verify endpoint is accessible and returns database data
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('offers', response.data)
        self.assertEqual(len(response.data['offers']), 3)  # Should return 3 offers from database

        # Verify the structure of the first offer
        first_offer = response.data['offers'][0]
        expected_keys = {
            'id',
            'name',
            'price_net',
            'price_gross',
            'tax',
            'price_net_per_day',
            'price_gross_per_day',
        }
        self.assertCountEqual(first_offer.keys(), expected_keys)

        # Verify that offers are ordered correctly
        offer_names = [offer['name'] for offer in response.data['offers']]
        self.assertEqual(offer_names, ['7 days', '14 days', '30 days'])

    @override_eppo_feature_flag({VisibilityPromotionActiveOnBackend.flag_name: True})
    def test_dashboard_manager_access_success(self):
        """Test that manager level user can access the dashboard endpoint"""
        response = self.client.get(self.dashboard_url, **self.headers)

        # Verify endpoint is accessible and returns mock data
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('support_links', response.data)
        self.assertIn('current_plan', response.data)
        self.assertEqual(len(response.data['support_links']), 5)  # Should return 5 support links

    @override_eppo_feature_flag({VisibilityPromotionActiveOnBackend.flag_name: True})
    @patch(
        'webapps.visibility_promotion.views.PurchasePromotionUseCase.place_order',
        side_effect=ValidationError("Cannot purchase promotion at this time"),
    )
    def test_purchase_validation_error_returns_400(self, mock_place_order):
        payload = {'offer_id': self.sample_offer.id}
        response = self.client.post(self.purchase_url, data=payload, **self.headers)

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)

        self.assertEqual(response.data['error'], 'Cannot purchase promotion at this time')
        mock_place_order.assert_called_once_with(self.business.id, self.sample_offer.id)
