from decimal import Decimal
from unittest.mock import Mock, patch
from datetime import <PERSON><PERSON><PERSON>

from django.test import TestCase
from model_bakery import baker

from lib.enums import StrEnum
from lib.tools import tznow
from webapps.business.baker_recipes import business_recipe
from webapps.navision.models import TaxRate
from webapps.visibility_promotion.enums import OrderStatus, PromotionStatus
from webapps.visibility_promotion.models import (
    VisibilityPromotionCandidate,
    VisibilityPromotionOffer,
    VisibilityPromotionOrder,
    PromotionPeriod,
)
from webapps.visibility_promotion.services import (
    GetAvailableOffersUseCase,
    PromotionEligibilityService,
)


class TestGetAvailableOffersUseCase(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.business = business_recipe.make()

        cls.offer_7_days = VisibilityPromotionOffer.objects.create(
            duration_days=7, price_net=Decimal('89.99')
        )
        cls.offer_14_days = VisibilityPromotionOffer.objects.create(
            duration_days=14, price_net=Decimal('149.99')
        )
        cls.offer_30_days = VisibilityPromotionOffer.objects.create(
            duration_days=30, price_net=Decimal('289.99')
        )

    @patch('webapps.visibility_promotion.services.TaxForNetSummary.for_business')
    @patch(
        'webapps.visibility_promotion.services.format_currency',
        Mock(side_effect=lambda x: f"{x:.2f} zł"),
    )
    def test_get_offers_with_tax_calculation(self, mock_tax_summary):
        mock_tax_summary.return_value = Mock(gross_price=Decimal('110.69'), tax=Decimal('20.70'))

        offers = GetAvailableOffersUseCase.get_offers_for_business(self.business)

        self.assertEqual(len(offers), 3)
        self.assertEqual(mock_tax_summary.call_count, 3)

        calls = mock_tax_summary.call_args_list
        self.assertEqual(calls[0][1]['business'], self.business)
        self.assertEqual(calls[0][1]['service'], TaxRate.Service.BOOST)
        self.assertEqual(calls[0][1]['net_price'], Decimal('89.99'))

    @patch(
        'webapps.visibility_promotion.services.TaxForNetSummary.for_business',
        Mock(return_value=Mock(gross_price=Decimal('110.69'), tax=Decimal('20.70'))),
    )
    @patch(
        'webapps.visibility_promotion.services.format_currency',
        Mock(side_effect=lambda x: f"{x:.2f} zł"),
    )
    def test_offers_response_structure(self):
        offers = GetAvailableOffersUseCase.get_offers_for_business(self.business)

        first_offer = offers[0]

        required_fields = [
            'id',
            'name',
            'price_net',
            'price_gross',
            'tax',
            'price_net_per_day',
            'price_gross_per_day',
        ]
        for field in required_fields:
            self.assertIn(field, first_offer)

        self.assertEqual(first_offer['id'], self.offer_7_days.id)
        self.assertEqual(first_offer['name'], '7 days')
        self.assertEqual(first_offer['price_net'], '89.99 zł')
        self.assertEqual(first_offer['price_gross'], '110.69 zł')
        self.assertEqual(first_offer['tax'], '20.70 zł')

    @patch(
        'webapps.visibility_promotion.services.TaxForNetSummary.for_business',
        Mock(
            return_value=Mock(
                gross_price=Decimal('84.00'),  # 70.00 net + 20% tax = 84.00 gross
                tax=Decimal('14.00'),  # 20% of 70.00 = 14.00
            )
        ),
    )
    @patch(
        'webapps.visibility_promotion.services.format_currency',
        Mock(side_effect=lambda x: f"{x:.2f} zł"),
    )
    def test_per_day_price_calculation(self):
        VisibilityPromotionOffer.objects.all().update(price_net=Decimal('70.00'))

        offers = GetAvailableOffersUseCase.get_offers_for_business(self.business)

        first_offer = offers[0]
        self.assertEqual(
            first_offer['name'], '7 days', 'Wrong test setup - expected 7-day offer first'
        )

        # Verify per-day calculations:
        # Net per day: 70.00 / 7 = 10.00
        # Gross per day: 84.00 / 7 = 12.00
        self.assertEqual(first_offer['price_net_per_day'], '10.00 zł')
        self.assertEqual(first_offer['price_gross_per_day'], '12.00 zł')

    @patch(
        'webapps.visibility_promotion.services.TaxForNetSummary.for_business',
        Mock(return_value=Mock(gross_price=Decimal('100.00'), tax=Decimal('10.00'))),
    )
    @patch(
        'webapps.visibility_promotion.services.format_currency',
        Mock(side_effect=lambda x: f"{x:.2f} zł"),
    )
    def test_offers_ordered_by_duration(self):
        offers = GetAvailableOffersUseCase.get_offers_for_business(self.business)

        self.assertEqual(offers[0]['id'], self.offer_7_days.id)
        self.assertEqual(offers[1]['id'], self.offer_14_days.id)
        self.assertEqual(offers[2]['id'], self.offer_30_days.id)

    @patch(
        'webapps.visibility_promotion.services.TaxForNetSummary.for_business',
        Mock(return_value=Mock(gross_price=Decimal('100'), tax=Decimal('10'))),
    )
    @patch(
        'webapps.visibility_promotion.services.format_currency',
        Mock(side_effect=lambda x: f"{x:.2f} zł"),
    )
    def test_only_active_offers_returned(self):
        self.offer_14_days.delete()  # ArchiveModel soft delete

        offers = GetAvailableOffersUseCase.get_offers_for_business(self.business)

        # Should only return 2 offers (7 and 30 days)
        self.assertEqual(len(offers), 2)
        offer_names = [offer['name'] for offer in offers]
        self.assertIn('7 days', offer_names)
        self.assertIn('30 days', offer_names)
        self.assertNotIn('14 days', offer_names)

    @patch('webapps.visibility_promotion.services.TaxForNetSummary.for_business')
    @patch(
        'webapps.visibility_promotion.services.format_currency',
        Mock(side_effect=lambda x: f"{x:.2f} zł"),
    )
    def test_boost_tax_service_integration(self, mock_tax_summary):
        mock_tax_summary.return_value = Mock(gross_price=Decimal('110.69'), tax=Decimal('20.70'))

        GetAvailableOffersUseCase.get_offers_for_business(self.business)

        mock_tax_summary.assert_called()
        call_args = mock_tax_summary.call_args_list[0]

        self.assertEqual(call_args[1]['business'], self.business)
        self.assertEqual(call_args[1]['service'], TaxRate.Service.BOOST)
        self.assertEqual(call_args[1]['round_results'], True)
        self.assertIn('net_price', call_args[1])


class BoostStatusTestEnum(StrEnum):
    """A sample enum to simulate the input for the service."""

    ACTIVE = 'active'
    DEACTIVATED = 'deactivated'
    INACTIVE = 'inactive'
    PENDING = 'pending'


class TestCheckEligibility(TestCase):
    def test_eligible_when_status_is_allowed_and_candidate_is_active(self):
        baker.make(VisibilityPromotionCandidate, business_id=123, active=True)

        is_eligible = PromotionEligibilityService.can_access_visibility_promotion(
            business_id=123, boost_status=BoostStatusTestEnum.INACTIVE.value
        )

        self.assertTrue(is_eligible)

    def test_ineligible_when_status_is_allowed_but_candidate_is_inactive(self):
        baker.make(VisibilityPromotionCandidate, business_id=123, active=False)

        is_eligible = PromotionEligibilityService.can_access_visibility_promotion(
            business_id=123, boost_status=BoostStatusTestEnum.INACTIVE.value
        )

        self.assertFalse(is_eligible)

    def test_ineligible_when_status_is_allowed_but_candidate_does_not_exist(self):
        is_eligible = PromotionEligibilityService.can_access_visibility_promotion(
            business_id=123, boost_status=BoostStatusTestEnum.INACTIVE.value
        )

        self.assertFalse(is_eligible)

    def test_ineligible_when_boost_status_is_none(self):
        is_eligible = PromotionEligibilityService.can_access_visibility_promotion(
            business_id=123, boost_status=None
        )

        self.assertFalse(is_eligible)

    def test_ineligible_when_boost_status_is_not_allowed(self):
        with self.assertNumQueries(0):
            is_eligible = PromotionEligibilityService.can_access_visibility_promotion(
                business_id=123, boost_status=BoostStatusTestEnum.ACTIVE.value
            )

        self.assertFalse(is_eligible)

    def test_ineligible_when_boost_status_is_not_an_enum(self):
        class NotAnEnum:
            pass

        is_eligible = PromotionEligibilityService.can_access_visibility_promotion(
            business_id=123, boost_status=NotAnEnum()
        )
        self.assertFalse(is_eligible)


class TestCanPurchaseNow(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.business_id = 12345
        cls.other_business_id = 67890

        cls.offer = VisibilityPromotionOffer.objects.create(
            duration_days=7, price_net=Decimal('89.99')
        )

    def setUp(self):
        VisibilityPromotionOrder.all_objects.filter(
            business_id__in=[self.business_id, self.other_business_id]
        ).delete()
        PromotionPeriod.all_objects.filter(
            business_id__in=[self.business_id, self.other_business_id]
        ).delete()

    @patch(
        'webapps.visibility_promotion.services.PromotionEligibilityService'
        '.can_access_visibility_promotion',
        return_value=True,
    )
    def test_can_purchase_when_feature_access_eligible_and_no_blockers(
        self, mock_can_access_visibility_promotion
    ):
        result = PromotionEligibilityService.can_purchase_now(self.business_id, 'inactive')
        self.assertTrue(result)
        mock_can_access_visibility_promotion.assert_called_once_with(self.business_id, 'inactive')

    @patch(
        'webapps.visibility_promotion.services.PromotionEligibilityService'
        '.can_access_visibility_promotion',
        return_value=False,
    )
    def test_cannot_purchase_when_access_not_eligible(self, mock_can_access_visibility_promotion):
        result = PromotionEligibilityService.can_purchase_now(self.business_id, 'active')
        self.assertFalse(result)
        mock_can_access_visibility_promotion.assert_called_once_with(self.business_id, 'active')

    @patch(
        'webapps.visibility_promotion.services.PromotionEligibilityService'
        '.can_access_visibility_promotion',
        Mock(return_value=True),
    )
    def test_cannot_purchase_when_has_pending_order(self):
        VisibilityPromotionOrder.objects.create(
            business_id=self.business_id,
            offer=self.offer,
            price_net=self.offer.price_net,
            duration_days=self.offer.duration_days,
            status=OrderStatus.PENDING,
        )

        result = PromotionEligibilityService.can_purchase_now(self.business_id, Mock())

        self.assertFalse(result)

    @patch(
        'webapps.visibility_promotion.services.PromotionEligibilityService'
        '.can_access_visibility_promotion',
        Mock(return_value=True),
    )
    def test_cannot_purchase_when_has_active_promotion(self):
        now = tznow()
        baker.make(
            PromotionPeriod,
            business_id=self.business_id,
            active_from=now - timedelta(days=1),
            active_till=now + timedelta(days=6),
            status=PromotionStatus.ACTIVE,
            order=baker.make(
                VisibilityPromotionOrder,
                business_id=self.business_id,
                offer=self.offer,
                status=OrderStatus.COMPLETED,
            ),
        )

        result = PromotionEligibilityService.can_purchase_now(self.business_id, Mock())

        self.assertFalse(result)

    @patch(
        'webapps.visibility_promotion.services.PromotionEligibilityService'
        '.can_access_visibility_promotion',
        Mock(return_value=True),
    )
    def test_cannot_purchase_when_has_active_promotion_with_past_dates(self):
        # Test that we check status, not dates - promotion is ACTIVE, but dates are in the past
        past_time = tznow() - timedelta(days=10)
        baker.make(
            PromotionPeriod,
            business_id=self.business_id,
            active_from=past_time - timedelta(days=7),
            active_till=past_time,  # ended 10 days ago
            status=PromotionStatus.ACTIVE,  # but still marked as ACTIVE
            order=baker.make(
                VisibilityPromotionOrder,
                business_id=self.business_id,
                offer=self.offer,
                status=OrderStatus.COMPLETED,
            ),
        )

        result = PromotionEligibilityService.can_purchase_now(self.business_id, Mock())

        self.assertFalse(result)

    @patch(
        'webapps.visibility_promotion.services.PromotionEligibilityService'
        '.can_access_visibility_promotion',
        Mock(return_value=True),
    )
    def test_ignores_soft_deleted_pending_orders(self):
        order = VisibilityPromotionOrder.objects.create(
            business_id=self.business_id,
            offer=self.offer,
            price_net=self.offer.price_net,
            duration_days=self.offer.duration_days,
            status=OrderStatus.PENDING,
        )
        order.delete()  # soft delete

        result = PromotionEligibilityService.can_purchase_now(self.business_id, Mock())

        self.assertTrue(result)

    @patch(
        'webapps.visibility_promotion.services.PromotionEligibilityService'
        '.can_access_visibility_promotion',
        Mock(return_value=True),
    )
    def test_ignores_deleted_active_promotions(self):
        now = tznow()
        promotion = baker.make(
            PromotionPeriod,
            business_id=self.business_id,
            active_from=now - timedelta(days=1),
            active_till=now + timedelta(days=6),
            status=PromotionStatus.ACTIVE,
            order=baker.make(
                VisibilityPromotionOrder,
                business_id=self.business_id,
                offer=self.offer,
                status=OrderStatus.COMPLETED,
            ),
        )
        promotion.delete()  # soft delete

        result = PromotionEligibilityService.can_purchase_now(self.business_id, Mock())

        self.assertTrue(result)

    @patch(
        'webapps.visibility_promotion.services.PromotionEligibilityService'
        '.can_access_visibility_promotion',
        Mock(return_value=True),
    )
    def test_ignores_completed_orders(self):
        VisibilityPromotionOrder.objects.create(
            business_id=self.business_id,
            offer=self.offer,
            price_net=self.offer.price_net,
            duration_days=self.offer.duration_days,
            status=OrderStatus.COMPLETED,
        )

        result = PromotionEligibilityService.can_purchase_now(self.business_id, Mock())

        self.assertTrue(result)

    @patch(
        'webapps.visibility_promotion.services.PromotionEligibilityService'
        '.can_access_visibility_promotion',
        Mock(return_value=True),
    )
    def test_ignores_failed_orders(self):
        VisibilityPromotionOrder.objects.create(
            business_id=self.business_id,
            offer=self.offer,
            price_net=self.offer.price_net,
            duration_days=self.offer.duration_days,
            status=OrderStatus.FAILED,
        )

        result = PromotionEligibilityService.can_purchase_now(self.business_id, Mock())

        self.assertTrue(result)

    @patch(
        'webapps.visibility_promotion.services.PromotionEligibilityService'
        '.can_access_visibility_promotion',
        Mock(return_value=True),
    )
    def test_ignores_expired_promotions(self):
        now = tznow()
        baker.make(
            PromotionPeriod,
            business_id=self.business_id,
            active_from=now - timedelta(days=7),
            active_till=now - timedelta(days=1),
            status=PromotionStatus.EXPIRED,
            order=baker.make(
                VisibilityPromotionOrder,
                business_id=self.business_id,
                offer=self.offer,
                status=OrderStatus.COMPLETED,
            ),
        )

        result = PromotionEligibilityService.can_purchase_now(self.business_id, Mock())

        self.assertTrue(result)

    @patch(
        'webapps.visibility_promotion.services.PromotionEligibilityService'
        '.can_access_visibility_promotion',
        Mock(return_value=True),
    )
    def test_ignores_terminated_promotions(self):
        now = tznow()
        baker.make(
            PromotionPeriod,
            business_id=self.business_id,
            active_from=now - timedelta(days=7),
            active_till=now + timedelta(days=7),
            status=PromotionStatus.TERMINATED,
            order=baker.make(
                VisibilityPromotionOrder,
                business_id=self.business_id,
                offer=self.offer,
                status=OrderStatus.COMPLETED,
            ),
        )

        result = PromotionEligibilityService.can_purchase_now(self.business_id, Mock())

        self.assertTrue(result)

    @patch(
        'webapps.visibility_promotion.services.PromotionEligibilityService'
        '.can_access_visibility_promotion',
        Mock(return_value=True),
    )
    def test_different_business_pending_order_does_not_affect(self):
        # create pending order for different business
        VisibilityPromotionOrder.objects.create(
            business_id=self.other_business_id,
            offer=self.offer,
            price_net=self.offer.price_net,
            duration_days=self.offer.duration_days,
            status=OrderStatus.PENDING,
        )

        result = PromotionEligibilityService.can_purchase_now(self.business_id, Mock())

        self.assertTrue(result)

    @patch(
        'webapps.visibility_promotion.services.PromotionEligibilityService'
        '.can_access_visibility_promotion',
        Mock(return_value=True),
    )
    def test_different_business_active_promotion_does_not_affect(self):
        # create active promotion for different business
        now = tznow()
        order = baker.make(
            VisibilityPromotionOrder,
            business_id=self.other_business_id,
            offer=self.offer,
            status=OrderStatus.COMPLETED,
        )
        baker.make(
            PromotionPeriod,
            business_id=self.other_business_id,
            active_from=now - timedelta(days=1),
            active_till=now + timedelta(days=6),
            status=PromotionStatus.ACTIVE,
            order=order,
        )

        result = PromotionEligibilityService.can_purchase_now(self.business_id, Mock())

        self.assertTrue(result)
