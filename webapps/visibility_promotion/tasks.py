import logging
from django.core.exceptions import ValidationError
from django.db import DatabaseError, IntegrityError, OperationalError

from lib.celery_tools import celery_task
from webapps.visibility_promotion.services import PurchasePromotionUseCase

logger = logging.getLogger('visibility_promotion')


@celery_task(ignore_result=False)
def purchase_visibility_promotion_task(order_id: int):
    """
    Process a visibility promotion order.
    Takes order_id and provide a promotion activation.
    Returns promotion_period_id on success or error details on failure.
    """
    try:
        promotion_period_id = PurchasePromotionUseCase.activate_promotion(order_id)
        return {'status': 'SUCCESS', 'promotion_period_id': promotion_period_id}
    except ValidationError as e:
        # Business logic errors - safe to expose to frontend
        return {'status': 'FAILED', 'error': str(e)}
    except (DatabaseError, IntegrityError, OperationalError) as e:
        logger.error(
            'Database error in purchase_visibility_promotion_task for order_id=%s: %s',
            order_id,
            str(e),
            exc_info=True,
        )
        raise  # re-raise for monitoring
    except Exception as e:
        logger.error(
            'Unexpected error in purchase_visibility_promotion_task for order_id=%s: %s',
            order_id,
            str(e),
            exc_info=True,
        )
        raise  # re-raise for monitoring
