import datetime
import re
import typing as t
from decimal import Decimal

import stripe
from django.conf import settings
from django.db.models import Sum
from django.utils.translation import gettext_lazy as _
from stripe.error import InvalidRequestError

from country_config import Country
from lib.deeplink import (
    STRIPE_DASHBOARD,
    DeepLinkCache,
)
from lib.feature_flag.feature.payment import (
    StripeCurrentlyDueInKYCFlag,
    StripeTurnOnDebitNegativeBalancesFlag,
    StripeSendAccountCountryFlag,
)
from lib.locks import (
    <PERSON>lockError,
    StripeAccountCreationLock,
    StripeUpdateStatusToFailLock,
)
from lib.payment_providers.enums import StripeAccountType
from lib.payments.enums import (
    Currency,
    PaymentProviderCode,
    PayoutType,
)
from lib.serializers import safe_get
from lib.tools import (
    clean_string,
    major_unit,
    minor_unit,
    remove_empty_values,
    tznow,
    sget,
)
from settings.stripe import STRIPE_FAST_PAYOUT_COUNTRIES_CONFIG
from webapps.business.models import Business
from webapps.payment_gateway.models import (
    BalanceTransaction,
    Wallet,
)
from webapps.payment_gateway.ports import PaymentGatewayPort
from webapps.payment_gateway.services.wallet import WalletService
from webapps.payment_providers.models import Payout as PaymentProvidersPayout
from webapps.point_of_sale.adapters import get_business_wallet_id_adapter
from webapps.point_of_sale.models import BasketPayment
from webapps.pos.calculations import round_decimal, recalculate_tips
from webapps.pos.enums import (
    PaymentProviderEnum,
    PaymentTypeEnum,
    POSPlanPaymentTypeEnum,
    receipt_status,
)
from webapps.pos.models import (
    POS,
    PaymentRow,
    PaymentRowChange,
    Transaction,
)
from webapps.pos.provider.proxy import ProxyProvider
from webapps.pos.refund import is_refund_possible
from webapps.pos.tip_calculations import SimpleTip
from webapps.stripe_integration.consts.messages import STRIPE_MESSAGES
from webapps.stripe_integration.enums import (
    StripeAccountStatus,
    StripeBalanceTransactionType,
    StripePaymentIntentActions,
    StripePaymentIntentMetadata,
    StripePayoutMethodType,
    StripePayoutStatus,
    StripeRefundResponseStatuses,
)
from webapps.stripe_integration.events import stripe_refund_event
from webapps.stripe_integration.exceptions import (
    AccountNotDeletable,
    CancelPaymentIntentNotPossible,
    DuplicatedPaymentIntent,
    PaymentIntentRecaptured,
    ReaderAlreadyCanceled,
    ReaderIsBusy,
    ReaderNotFound,
    ReaderUnreachable,
    StripeAccountNotVerified,
    StripeMultipleAccountsAttempt,
    WrongTipConfiguration,
)
from webapps.stripe_integration.models import (
    StripeAccount,
    StripeBalanceTransaction,
    StripeCustomer,
    StripeLocation,
    StripePaymentIntent,
    StripePayout,
)
from webapps.stripe_integration.stripe_exceptions import CommonStatementDescriptorStripeException
from webapps.stripe_integration.tools import (
    get_fees_from_fee_settings,
    iterable_to_dict,
    stripe_payment_intent_idempotent_cancel,
)
from webapps.stripe_terminal import STRIPE_HARDWARE_API_VERSION
from webapps.structure.enums import RegionType
from webapps.structure.models import Region
from webapps.user.models import User


# pylint: disable=too-many-public-methods
class StripeProvider:
    codename = PaymentProviderEnum.STRIPE_PROVIDER.value
    label = PaymentProviderEnum.STRIPE_PROVIDER.label

    @staticmethod
    def extract_stripe_payment_row(transaction: Transaction):
        return transaction.latest_receipt.payment_rows.filter(
            status__in=[receipt_status.PENDING],
            payment_type__code__in=PaymentTypeEnum.terminal_methods(),
        ).first()

    @staticmethod
    def get_pos_plan(payment_row: PaymentRow):
        pos = payment_row.receipt.transaction.pos
        payment_type_code = payment_row.payment_type.code
        if payment_type_code in [
            PaymentTypeEnum.PAY_BY_APP,
            PaymentTypeEnum.PREPAYMENT,
            PaymentTypeEnum.BLIK,
            PaymentTypeEnum.KEYED_IN_PAYMENT,
        ]:
            return pos.get_pos_plan(POSPlanPaymentTypeEnum.STRIPE_MOBILE_PAYMENT)
        if payment_type_code == PaymentTypeEnum.TAP_TO_PAY:
            return pos.get_pos_plan(POSPlanPaymentTypeEnum.TAP_TO_PAY)
        if payment_type_code == PaymentTypeEnum.BOOKSY_PAY:
            return pos.get_pos_plan(POSPlanPaymentTypeEnum.BOOKSY_PAY)
        return pos.get_pos_plan(POSPlanPaymentTypeEnum.STRIPE_TERMINAL)

    @staticmethod
    def calculate_splits(pr: PaymentRow, stripe_account: StripeAccount = None):
        """
        Calculates splits for single paymentRow.
        """
        pos_plan = StripeProvider.get_pos_plan(pr)
        total_amount = minor_unit(pr.amount or 0)
        provision_amount = total_amount
        if pr.tip_amount:
            tip_amount = minor_unit(pr.tip_amount)
            # should not happen
            if not stripe_account:
                stripe_account = pr.receipt.transaction.pos.stripe_account
            if not stripe_account.charge_for_tips and settings.STRIPE_ALLOW_TIPS_WITHOUT_FEE:
                tip_percentage = tip_amount / (total_amount - tip_amount) * 100
                if tip_percentage <= settings.STRIPE_TIP_PERCENTAGE_THRESHOLD:
                    provision_amount = total_amount - tip_amount

        provision = int(
            round_decimal(
                Decimal(pos_plan.provision) * Decimal(provision_amount),
                0,
            )
        )
        txn_fee = minor_unit(pos_plan.txn_fee)

        return {
            'total_amount': total_amount,
            'currency_code': settings.CURRENCY_CODE,
            'txn_fee': txn_fee,
            'provision': provision,
            'amount': total_amount - provision - txn_fee,
        }

    @staticmethod
    def calculate_refund_splits(pr: PaymentRow, amount=None):
        """
        Calculates refund splits for single paymentRow.
        """
        pos_plan = StripeProvider.get_pos_plan(pr)
        total_amount = minor_unit(amount or pr.amount or 0)
        refund_provision = int(round_decimal(pos_plan.refund_provision * total_amount, 0))
        refund_txn_fee = minor_unit(pos_plan.refund_txn_fee)

        return {
            'currency_code': settings.CURRENCY_CODE,
            'refund_txn_fee': refund_txn_fee,
            'refund_provision': refund_provision,
            'total_fee_amount': refund_txn_fee + refund_provision,
        }

    @staticmethod
    def calculate_chargeback_splits(pr: PaymentRow):
        """
        Calculates chargeback splits for single paymentRow.
        """
        pos_plan = StripeProvider.get_pos_plan(pr)
        total_amount = minor_unit(pr.amount or 0)
        chargeback_provision = int(round_decimal(pos_plan.chargeback_provision * total_amount, 0))
        chargeback_txn_fee = minor_unit(pos_plan.chargeback_txn_fee)

        return {
            'currency_code': settings.CURRENCY_CODE,
            'total_amount': total_amount,
            'chargeback_txn_fee': chargeback_txn_fee,
            'chargeback_provision': chargeback_provision,
            'total_fee_amount': chargeback_txn_fee + chargeback_provision,
        }

    @staticmethod
    def calculate_fast_payout_splits(account: StripeAccount, amount: Decimal):
        pos = account.pos

        wallet = Wallet.objects.get(business_id=pos.business.id)
        fee_settings = WalletService.get_wallet_fee_settings(
            wallet=wallet,
            payment_provider_code=PaymentProviderCode.STRIPE,
        )
        plan_provision, plan_fixed_fee = get_fees_from_fee_settings(fee_settings)

        total_amount = minor_unit(amount)

        # fast_payout_provision is a percent of the total amount substracted from it
        # total_payout_amount is equal to total_amount - fast_payout_provision - transaction_fee

        fast_payout_provision = int(
            round_decimal(
                (total_amount * plan_provision) / (1 + plan_provision),
                0,
            )
        )
        fast_payout_txn_fee = minor_unit(plan_fixed_fee)
        total_fee_amount = fast_payout_provision + fast_payout_txn_fee

        return {
            'currency_code': settings.CURRENCY_CODE,
            'total_amount': total_amount,
            'fast_payout_provision': fast_payout_provision,
            'fast_payout_txn_fee': fast_payout_txn_fee,
            'total_fee_amount': total_fee_amount,
            'total_payout_amount': total_amount - total_fee_amount,
        }

    @staticmethod
    def delete_stripe_account(account: StripeAccount):
        if not account.can_be_safely_deleted:
            raise AccountNotDeletable

        stripe.Account.delete(account.external_id)
        account.soft_delete()

    @staticmethod
    def get_or_create_stripe_customer(user: User) -> t.Optional[stripe.Customer]:
        if not user:
            return None

        if customer_obj := user.stripe_customers.first():
            return stripe.Customer.retrieve(customer_obj.external_id)

        customer = stripe.Customer.create(
            email=user.email,
            name=user.full_name,
            phone=user.cell_phone,
            metadata={
                "user_id": user.id,
            },
        )
        StripeCustomer.objects.create(user=user, external_id=customer.id)

        return customer

    @staticmethod
    def update_stripe_customer(user: User):
        if customer_obj := user.stripe_customers.first():
            stripe.Customer.modify(
                customer_obj.external_id,
                email=user.email,
                name=user.full_name,
                phone=user.cell_phone,
            )

    @staticmethod
    def update_stripe_account(business: Business):
        pos = business.pos
        if pos and (stripe_account := pos.stripe_account):
            try:
                stripe.Account.modify(
                    stripe_account.external_id,
                    settings=StripeProvider.generate_stripe_account_settings(pos),
                )
            except InvalidRequestError as e:
                if CommonStatementDescriptorStripeException.is_equal(e):
                    stripe.Account.modify(
                        stripe_account.external_id,
                        settings=StripeProvider.generate_stripe_account_settings(
                            pos=pos,
                            use_default_statement_descriptor=True,
                        ),
                    )
                else:
                    raise e from e

    @staticmethod
    def update_stripe_reader_label(stripe_account: StripeAccount, reader_id: str, label: str):
        # first check if user owns given terminal
        try:
            reader = stripe.terminal.Reader.retrieve(reader_id)
        except stripe.error.InvalidRequestError as e:
            # reader doesnt exist
            raise ReaderNotFound from e

        location = StripeProvider.get_location(stripe_account)

        if not location:
            raise ReaderNotFound

        if reader.location != location.external_id:
            #  todo sb is trying to change reader that they don't own, maybe we should report ;)
            raise ReaderNotFound

        if reader.label == label:
            # we can avoid unnecessary request
            return

        stripe.terminal.Reader.modify(reader_id, label=label)

    @staticmethod
    def create_payment_intent(
        transaction: Transaction,
        payment_row: PaymentRow,
        terminal_identifier: str = None,
        terminal_device_type: str = None,
    ) -> t.Tuple[str, StripePaymentIntent]:
        """
        returns client secret and payment intent object
        """
        # TODO co jak nie ma stripe_account
        pos: POS = transaction.pos
        stripe_account: StripeAccount = pos.stripe_account

        if not stripe_account:
            return None, None

        if payment_row.intents.exists():
            # 1 payment row should only have 1 intent!
            # 1 intent can have multiple PRs (historical ones)
            raise DuplicatedPaymentIntent

        customer = StripeProvider.get_or_create_stripe_customer(user=transaction.customer)

        splits = StripeProvider.calculate_splits(payment_row, stripe_account)
        application_fee = splits['provision'] + splits['txn_fee']

        on_behalf_of = stripe_account.external_id
        if stripe_account.account_type == StripeAccountType.CUSTOM:
            on_behalf_of = None

        payment_intent = stripe.PaymentIntent.create(
            amount=minor_unit(payment_row.amount),
            currency=transaction.currency_symbol,
            payment_method_types=['card_present'],
            capture_method='manual',
            application_fee_amount=application_fee,
            on_behalf_of=on_behalf_of,
            transfer_data={
                'destination': stripe_account.external_id,
            },
            customer=customer.id if customer else None,
            metadata={StripePaymentIntentMetadata.TERMINAL_PAYMENT: True},
        )
        # We need to keep capture_method as manual.
        # Stripe does not allow automatic capture for terminal payments.
        # Metadata is used to distinguish payment_intent notifications later.

        intent_object = StripePaymentIntent.objects.create(
            external_id=payment_intent.id,
            account=stripe_account,
            terminal_identifier=terminal_identifier,
            terminal_device_type=terminal_device_type,
        )
        intent_object.payment_rows.set([payment_row])
        payment_row.payment_splits = splits
        payment_row.save(update_fields=['payment_splits'])

        return payment_intent.client_secret, intent_object

    @staticmethod
    def send_for_refund(payment_row: PaymentRow, operator: User, from_admin: bool = True):
        possible = is_refund_possible(payment_row)

        if not possible:
            raise AssertionError(  # pylint: disable=broad-exception-raised
                'This row cannot be refunded'
            )

        if not payment_row.refund_requested:
            payment_row.refund_requested = tznow()
            payment_row.refund_operator = operator
            payment_row.save(update_fields=['refund_requested', 'refund_operator'])

        splits = StripeProvider.calculate_refund_splits(payment_row)
        payment_intent: StripePaymentIntent = payment_row.intents.last()

        refund = stripe.Refund.create(
            amount=minor_unit(payment_row.amount),
            payment_intent=payment_intent.external_id,
            refund_application_fee=False,
            reverse_transfer=True,
        )

        # pylint: disable=consider-using-f-string
        if refund.status == StripeRefundResponseStatuses.SUCCEEDED:
            refund_row = payment_row.update_status(
                status=receipt_status.SENT_FOR_REFUND,
                settled=True,
                operator=operator,
                payment_splits=splits,
                log_action=PaymentRowChange.SENT_FOR_REFUND,
                # pylint: disable=consider-using-f-string
                log_note='{} sent for refund action'.format('Admin' if from_admin else 'User'),
            )

            stripe_refund_event.send(payment_intent)
            return refund_row
        # pylint: enable=consider-using-f-string

    @staticmethod
    def charge_fast_payout_fee(
        payout: StripePayout,
    ):
        if not payout.fast_payout_splits:
            return
        payout.provision_charged = True
        payout.save(update_fields=['provision_charged'])

    @staticmethod
    def process_refund(payment_intent: StripePaymentIntent, success: bool):
        payment_intent.payment_row.process_refund(success)

    @staticmethod
    def set_tip_settings(stripe_account: StripeAccount):
        if settings.CURRENCY_CODE.lower() not in [Currency.USD, Currency.EUR, Currency.GBP]:
            # Tipping is not available in every currency:
            # https://stripe.com/docs/api/terminal/configuration/create
            return

        location = StripeProvider.get_location(stripe_account)
        if not location:
            return
        tip_rates = list(
            stripe_account.pos.tips.filter(
                rate__gt=0,
            ).values_list(
                'rate',
                flat=True,
            )[:3]
        )
        if len(tip_rates) < 3:
            raise WrongTipConfiguration()

        tipping = {
            settings.CURRENCY_CODE.lower(): {
                'percentages': tip_rates,
            },
        }
        if not location.reader_configuration_id:
            location.reader_configuration_id = stripe.terminal.Configuration.create(
                tipping=tipping,
                stripe_version=STRIPE_HARDWARE_API_VERSION,
            ).id
            stripe.terminal.Location.modify(
                location.external_id,
                configuration_overrides=location.reader_configuration_id,
                stripe_version=STRIPE_HARDWARE_API_VERSION,
            )
            location.save(update_fields=['reader_configuration_id'])
        else:
            stripe.terminal.Configuration.modify(
                location.reader_configuration_id,
                tipping=tipping,
                stripe_version=STRIPE_HARDWARE_API_VERSION,
            )

    @staticmethod
    def refresh_tip_fom_payment_intent(intent: StripePaymentIntent):
        stripe_intent = stripe.PaymentIntent.retrieve(intent.external_id)
        stripe_intent_tip = safe_get(stripe_intent, ['amount_details', 'tip', 'amount'])
        payment_row = intent.payment_row
        transaction = payment_row.receipt.transaction
        if not stripe_intent_tip:
            return

        tip_amount = major_unit(stripe_intent_tip)
        data = {
            'tip': {
                'rate': tip_amount,
                'type': SimpleTip.TIP_TYPE__HAND,
            },
        }
        old_total = transaction.total
        recalculate_tips(
            transaction,
            request_data=data,
            update_instance=True,
            force_percent=True,
        )
        transaction.save(update_fields=['total'])
        payment_row.amount = payment_row.amount + transaction.total - old_total
        payment_row.tip_amount = transaction.tip.amount

        splits = StripeProvider.calculate_splits(payment_row, transaction.pos.stripe_account)
        payment_row.payment_splits = splits

        application_fee = splits['provision'] + splits['txn_fee']  # TODO POS-1086
        stripe.PaymentIntent.modify(
            intent.external_id,
            application_fee_amount=application_fee,
        )

        payment_row.save(update_fields=['amount', 'tip_amount', 'payment_splits'])

    @staticmethod
    def handle_status_failed(
        intent_object: StripePaymentIntent,
        log_note: str,
        operator: User = None,
        error_code: str = None,
    ):
        try:
            _lock = StripeUpdateStatusToFailLock.lock(intent_object.external_id)
        except RedlockError:
            return True
        if not _lock:
            return True

        if intent_object.payment_row.status != receipt_status.PAYMENT_FAILED:
            is_canceled = stripe_payment_intent_idempotent_cancel(intent_object.external_id)
            if not is_canceled:
                return StripeProvider._handle_error(
                    intent_object, StripePaymentIntentActions.FAILED
                )

            intent_object.payment_row.update_status(
                receipt_status.PAYMENT_FAILED,
                provider=StripeProvider.codename,
                operator=operator,
                log_action=PaymentRowChange.SINGLE_ROW_UPDATE,
                log_note=log_note,
            )
            if error_code:
                intent_object.error_code = error_code
                intent_object.save(update_fields=['error_code'])
        StripeUpdateStatusToFailLock.try_to_unlock(_lock)
        return True

    @staticmethod
    def _process_cancel(intent_object: StripePaymentIntent, operator: User):
        transaction = intent_object.payment_row.receipt.transaction
        last_pr = transaction.payment_rows.filter(
            payment_type__code__in=PaymentTypeEnum.terminal_methods(),
        ).last()
        last_status = last_pr.status

        if last_status in [receipt_status.PENDING]:
            is_canceled = stripe_payment_intent_idempotent_cancel(intent_object.external_id)
            if not is_canceled:
                return StripeProvider._handle_error(
                    intent_object,
                    StripePaymentIntentActions.CANCEL,
                )

        log_note = 'StripePaymentIntentActionHandler set payment status cancelled'
        if last_status in [receipt_status.PENDING, receipt_status.PAYMENT_FAILED]:
            transaction.update_payment_rows(
                receipt_status.PAYMENT_CANCELED,
                operator=operator,
                log_action=PaymentRowChange.MULTI_ROW_UPDATE,
                log_note=log_note,
            )

    @staticmethod
    def _process_fail(intent_object: StripePaymentIntent, operator: User, error_code: str = None):
        log_note = 'StripePaymentIntentActionHandler set payment status'
        StripeProvider.handle_status_failed(intent_object, log_note, operator, error_code)

    @staticmethod
    def _handle_error(
        intent_object: StripePaymentIntent,
        action: StripePaymentIntentActions = None,
        operator: User = None,
    ):
        log_note = f'StripePaymentIntentActionHandler set payment \
        status Stripe error on action: {action}'

        pr = intent_object.payment_row

        if pr.status != receipt_status.PAYMENT_FAILED:
            pr.update_status(
                receipt_status.PAYMENT_FAILED,
                operator=operator,
                provider=StripeProvider.codename,
                log_action=PaymentRowChange.SINGLE_ROW_UPDATE,
                log_note=log_note,
            )

    @staticmethod
    def process_actions(
        action: StripePaymentIntentActions,
        intent_object: StripePaymentIntent,
        operator: User,
        action_kwargs: dict = None,
    ):
        handler = {
            StripePaymentIntentActions.CANCEL: StripeProvider._process_cancel,
            StripePaymentIntentActions.FAILED: StripeProvider._process_fail,
        }[action]

        action_kwargs = action_kwargs or {}

        return handler(intent_object, operator, **action_kwargs)

    @staticmethod
    def generate_stripe_account_metadata(pos: POS):
        """
        in Adyen we "store" info about the business inside an id (id_to_external_api),
        Stripe allows us to keep data in a nicer form, by assigning key-value pairs in metadata
        """

        level = settings.LEGACY_DEPLOYMENT_LEVEL
        level = '' if level == 'live' else level

        return {
            "business_id": pos.business.pk,
            "country_code": settings.API_COUNTRY,
            "test_environment_code": level,
        }

    @staticmethod
    def generate_stripe_statement_descriptor(pos: POS, use_default=False):
        default_value = _("Salon {business_id}").format(business_id=pos.business_id)

        if use_default:
            return default_value

        # formatting guide: https://stripe.com/docs/statement-descriptors
        min_length = 5
        max_length = 22

        statement_descriptor = clean_string(pos.business.name)

        chars_to_exclude = ["<", ">", '\\', "'", '"', "*"]
        statement_descriptor = "".join(c for c in statement_descriptor if c not in chars_to_exclude)

        statement_descriptor = statement_descriptor[:max_length]
        if len(statement_descriptor) < min_length:
            return default_value

        return statement_descriptor

    @staticmethod
    def generate_stripe_account_settings(pos: POS, use_default_statement_descriptor=False):
        """To be used in stripe.Account.create/modify"""

        account_settings = {
            'payments': {
                'statement_descriptor_prefix': 'Booksy',
                'statement_descriptor': StripeProvider.generate_stripe_statement_descriptor(
                    pos=pos,
                    use_default=use_default_statement_descriptor,
                ),
            },
        }

        if StripeTurnOnDebitNegativeBalancesFlag():
            account_settings['payouts'] = {"debit_negative_balances": True}

        return account_settings

    @staticmethod
    def get_or_create_stripe_account(
        pos: POS,
        account_token: str = None,
        additional_request_params: dict = None,
        force_synchronize_with_new_structure: bool = False,
        account_type: StripeAccountType = StripeAccountType.CUSTOM,
    ) -> t.Tuple[StripeAccount, bool]:
        def create_stripe_acc(
            _pos: POS,
            use_default_statement_descriptor=False,
            stripe_account_token: str = None,
            stripe_additional_request_params: dict = None,
        ):
            business_profile = {
                # right now we don't want to send a proper url, only booksy.com
                # "product_description": _pos.business.get_seo_url(protocol=True),
                "product_description": "https://booksy.com/",
            }

            extra_params = {}
            if StripeSendAccountCountryFlag():
                extra_params['country'] = pos.business.country_code
            business_profile.update(
                {
                    "mcc": "7230",  # https://stripe.com/docs/connect/setting-mcc#list
                }
            )
            extra_params.update(
                {
                    "email": _pos.business.owner.email,
                }
            )

            request_params = {
                'type': str(account_type),
                'capabilities': {
                    'card_payments': {
                        'requested': account_type != StripeAccountType.CUSTOM,
                    },
                    'transfers': {
                        'requested': True,
                    },
                },
                'business_profile': business_profile,
                'settings': StripeProvider.generate_stripe_account_settings(
                    _pos,
                    use_default_statement_descriptor,
                ),
                'metadata': StripeProvider.generate_stripe_account_metadata(_pos),
                'account_token': stripe_account_token,
                **extra_params,
            }
            if stripe_additional_request_params:
                request_params.update(stripe_additional_request_params)
            return stripe.Account.create(
                **request_params,
            )

        if stripe_account := pos.stripe_account:
            return stripe_account, False

        try:
            lock = StripeAccountCreationLock.lock(pos.id)
            if not lock:
                raise StripeMultipleAccountsAttempt
            if stripe_account := pos.stripe_account:
                # two phase locking
                return stripe_account, False
        except RedlockError as e:
            raise StripeMultipleAccountsAttempt from e

        try:
            account = create_stripe_acc(
                pos,
                stripe_account_token=account_token,
                stripe_additional_request_params=additional_request_params,
            )
        except InvalidRequestError as e:
            if CommonStatementDescriptorStripeException.is_equal(e):
                account = create_stripe_acc(
                    pos,
                    use_default_statement_descriptor=True,
                    stripe_account_token=account_token,
                    stripe_additional_request_params=additional_request_params,
                )
            else:
                raise e from e
        stripe_account = StripeAccount.objects.create(
            pos=pos,
            external_id=account.id,
            charges_enabled=account.charges_enabled,
            payouts_enabled=account.payouts_enabled,
            account_type=account_type,
            force_synchronize_with_new_structure=force_synchronize_with_new_structure,
        )
        StripeAccountCreationLock.try_to_unlock(lock)

        return stripe_account, True

    @staticmethod
    def create_account_link(
        account: StripeAccount, refresh_url: str, return_url: str
    ) -> stripe.AccountLink:
        """
        Creates an account link that is invalidated after the first usage
        """

        account_link = stripe.AccountLink.create(
            account=account.external_id,
            refresh_url=refresh_url,
            return_url=return_url,
            type='account_onboarding',
            collect='currently_due' if StripeCurrentlyDueInKYCFlag() else 'eventually_due',
        )
        if not account.account_link_first_time_created:
            account.account_link_first_time_created = tznow()
            account.save(update_fields=['account_link_first_time_created'])
        return account_link

    @staticmethod
    def create_connection_token() -> stripe.terminal.ConnectionToken:
        return stripe.terminal.ConnectionToken.create()

    @staticmethod
    def create_reader(
        label,
        registration_code,
        external_location_id,
    ):
        # only for internet readers!

        return stripe.terminal.Reader.create(
            registration_code=registration_code,
            location=external_location_id,
            label=label,
        )

    @staticmethod
    def add_payment_method_err_msg(payment_row: PaymentRow):
        """Provider specific. Returns reject reason.

        :param payment_row: PaymentRow instance
        :return dict(
            'type': PaymentRow.PAYMENT_ROW__REJECT_REASON,
            'msg': text,
            'error': text,
        )
        """

        payment_intent = payment_row.intents.last()

        msg = _('Something went wrong!')

        if payment_intent and (code := payment_intent.error_code):
            msg = STRIPE_MESSAGES.get(code, msg)

        return {
            'type': PaymentRow.PAYMENT_ROW__REJECT_REASON,
            'msg': msg,
            'error': '',  # for coherence with AdyenEEPaymentProvider
        }

    @staticmethod
    def _get_balance_transactions(
        payout: StripePayout,
        stripe_account: StripeAccount,
        transaction_type: StripeBalanceTransactionType,
    ) -> list:
        attributes_to_expand = {
            StripeBalanceTransactionType.CHARGE: ['data.source.application_fee'],
            StripeBalanceTransactionType.REFUND: ['data.source.charge.application_fee'],
            StripeBalanceTransactionType.PAYMENT: [
                'data.source.source_transfer.source_transaction'
            ],
            StripeBalanceTransactionType.PAYMENT_REFUND: [
                'data.source.charge.application_fee',
                'data.source.source_transfer_reversal.source_refund',
            ],
            StripeBalanceTransactionType.TRANSFER: ['data.source'],
        }[transaction_type]

        return list(
            # stripe_account param explained: https://stripe.com/docs/api/connected_accounts
            stripe.BalanceTransaction.list(
                limit=100,  # 100 - max page size in Stripe API, max page size = min request amount
                payout=payout.external_id,
                type=transaction_type,
                expand=attributes_to_expand,
                stripe_account=stripe_account.external_id,  # request on behalf of connected acc
            ).auto_paging_iter()
        )

    @staticmethod
    def sync_payout_balance_transactions(
        payout: StripePayout,
        stripe_account: StripeAccount,
        transaction_type: StripeBalanceTransactionType,
        batch_size: int = 1000,
    ):
        from webapps.r_and_d.pattern.datetime_utils import datetime_from_timestamp

        getters = {
            StripeBalanceTransactionType.CHARGE: {
                'amount': ['source', 'amount'],
                'application_fee': ['source', 'application_fee_amount'],
                'payment_intent': ['source', 'payment_intent'],
                'source_transfer_id': [''],
            },
            StripeBalanceTransactionType.PAYMENT: {
                'amount': ['source', 'amount'],
                'application_fee': ['source', 'application_fee_amount'],
                'payment_intent': [
                    'source',
                    'source_transfer',
                    'source_transaction',
                    'payment_intent',
                ],
                'source_transfer_id': [
                    'source',
                    'source_transfer',
                    'id',
                ],
            },
            StripeBalanceTransactionType.REFUND: {
                'amount': ['source', 'charge', 'amount_refunded'],
                'application_fee': ['source', 'charge', 'application_fee', 'amount_refunded'],
                'payment_intent': ['source', 'payment_intent'],
                'source_transfer_id': [''],
            },
            StripeBalanceTransactionType.PAYMENT_REFUND: {
                'amount': ['source', 'charge', 'amount_refunded'],
                'application_fee': ['source', 'charge', 'application_fee', 'amount_refunded'],
                'payment_intent': [
                    'source',
                    'source_transfer_reversal',
                    'source_refund',
                    'payment_intent',
                ],
                'source_transfer_id': [''],
            },
            StripeBalanceTransactionType.TRANSFER: {
                'amount': ['source', 'amount'],
                'application_fee': [''],  # there is no application fee
                'payment_intent': [''],  # no payment intent too
                'source_transfer_id': [''],
            },
        }[transaction_type]

        balance_transactions: dict = iterable_to_dict(
            StripeProvider._get_balance_transactions(payout, stripe_account, transaction_type),
            ['source', 'id'],
        )

        bt_qs = payout.balance_transactions.filter(transaction_type=transaction_type)
        bt_objects: dict = iterable_to_dict(bt_qs, ['external_id'])

        # (soft) delete objects that exist in our db but not in Stripe
        bt_qs.exclude(external_id__in=balance_transactions).delete()

        payment_intent_qs = StripePaymentIntent.objects.filter(
            external_id__in=[
                safe_get(bt, getters['payment_intent']) for bt in balance_transactions.values()
            ]
        )
        payment_intent_objects: dict = iterable_to_dict(payment_intent_qs, ['external_id'])

        # update existing objects and add new ones
        bt_objects_to_update = []
        bt_objects_to_add = []
        for bt_id, bt in balance_transactions.items():  # pylint: disable=invalid-name
            bt_obj = bt_objects.get(bt_id) or (
                StripeBalanceTransaction(
                    external_id=bt_id,
                    payout=payout,
                    transaction_type=transaction_type,
                )
            )

            # we do this in order to use the same sign for amount and application fee
            # (bt.get("amount") contains sign and safe_get(bt, getters['amount']) does not)
            coeff = -1 if bt.get('amount') < 0 else 1

            # abs() below is there just in case
            bt_obj.amount = abs(safe_get(bt, getters['amount']) or 0) * coeff
            bt_obj.application_fee = abs(safe_get(bt, getters['application_fee']) or 0) * coeff
            bt_obj.payment_intent = payment_intent_objects.get(
                safe_get(bt, getters['payment_intent'])
            )
            bt_obj.source_transfer_id = safe_get(bt, getters['source_transfer_id'])
            bt_obj.description = safe_get(bt, ['description'])
            bt_obj.transaction_created = datetime_from_timestamp(
                safe_get(bt, ['source', 'created'])
            )

            if bt_obj.pk:
                bt_objects_to_update.append(bt_obj)
            else:
                bt_objects_to_add.append(bt_obj)

        StripeBalanceTransaction.objects.bulk_update(
            bt_objects_to_update,
            fields=[
                'amount',
                'application_fee',
                'payment_intent',
                'source_transfer_id',
                'description',
                'transaction_created',
            ],
            batch_size=batch_size,
        )
        StripeBalanceTransaction.objects.bulk_create(bt_objects_to_add, batch_size=batch_size)

    @staticmethod
    def create_or_update_payout(
        payout_object: dict,
        stripe_account: StripeAccount,
        event_created_timestamp: int = None,
    ) -> StripePayout:
        """
        takes in Stripe object, creates or updates local StripePayout object
        """
        from webapps.r_and_d.pattern.datetime_utils import datetime_from_timestamp

        payout, _ = StripePayout.objects.get_or_create(
            external_id=payout_object['id'],
            defaults={
                'account': stripe_account,
            },
        )

        # sync balance transactions for standard payouts
        if payout_object['method'] == StripePayoutMethodType.STANDARD:
            for bt_type in [
                StripeBalanceTransactionType.CHARGE,
                StripeBalanceTransactionType.PAYMENT,
                StripeBalanceTransactionType.REFUND,
                StripeBalanceTransactionType.PAYMENT_REFUND,
                StripeBalanceTransactionType.TRANSFER,
            ]:
                StripeProvider.sync_payout_balance_transactions(payout, stripe_account, bt_type)

        # retrieve destination if not expanded
        if not payout.destination:
            destination = payout_object['destination']
            if isinstance(destination, str):
                destination = StripeProvider.get_payout_destination(payout)

            if isinstance(destination, (stripe.Card, stripe.BankAccount)):
                payout.destination = destination.to_dict_recursive()

        payout.payout_created = datetime_from_timestamp(payout_object['created'])
        payout.amount = payout_object['amount']
        payout.application_fee = (
            payout.balance_transactions.aggregate(Sum('application_fee'))['application_fee__sum']
            or 0
        )

        old_payout_status = payout.status
        payout.status = payout_object['status']
        payout.method = payout_object['method']
        payout.save(
            update_fields=[
                'amount',
                'application_fee',
                'status',
                'method',
                'destination',
                'payout_created',
            ]
        )

        if all(
            [
                old_payout_status != StripePayoutStatus.PAID,
                payout.status == StripePayoutStatus.PAID,
                payout.method == StripePayoutMethodType.STANDARD,
                event_created_timestamp,
            ]
        ):
            payout_paid_date = datetime.date.fromtimestamp(event_created_timestamp)
            PaymentRow.objects.filter(
                intents__balance_transactions__payout=payout,
                status__in=[
                    receipt_status.PAYMENT_SUCCESS,
                    receipt_status.REFUNDED,
                ],
            ).update(
                payout_booking_date=payout_paid_date,
            )

        return payout

    @staticmethod
    def get_location(account: StripeAccount):
        return account.locations.filter(default=True).first()

    @staticmethod
    def get_payout_destination(payout: StripePayout):
        payout_obj = stripe.Payout.retrieve(
            payout.external_id,
            stripe_account=payout.account.external_id,
            expand=['destination'],
        )
        return payout_obj['destination']

    @staticmethod
    def get_or_create_location(account: StripeAccount):
        if location := StripeProvider.get_location(account):
            return location

        if not account.status == StripeAccountStatus.VERIFIED:
            raise StripeAccountNotVerified

        # todo maybe before creating a location we should check if business has all required details

        business = account.pos.business
        state_code = StripeProvider._get_stripe_state_code(business)
        postal_code = business.zipcode or (business.region.name if business.region else None)
        city = business.city or (
            business.region.get_parent_name_by_type(RegionType.CITY) if business.region else None
        )
        location_name = business.name

        stripe_location = stripe.terminal.Location.create(
            display_name=location_name,
            # remove empty values, because Stripe assumes empty params as attempt to unset them
            address=remove_empty_values(
                {
                    'line1': business.address,
                    'line2': business.address2,
                    'city': city,
                    'state': state_code,
                    'country': business.country_code,
                    'postal_code': postal_code,
                }
            ),
            metadata=StripeProvider.generate_stripe_account_metadata(account.pos),
        )
        return StripeLocation.objects.create(
            account=account,
            external_id=stripe_location.id,
            name=location_name,
            default=True,
        )

    @staticmethod
    def _get_stripe_state_code(business: Business) -> str:
        # For Spain Stripe requires 'Province' what in our structure belongs to type 'Region'
        # Because we don't have existing type that satisfy requirement and
        # in our structure there are many Region objects with type 'Region' but only
        # subset of them are province in hierarchy and are valid to be sent
        # thus for those that are eligible, they have abbrev filled
        if settings.API_COUNTRY == Country.ES:
            parent_regions = (
                business.region.get_parents_by_type(Region.Type.REGION) if business.region else []
            )
            regions_with_abbrev = [r for r in parent_regions if r.abbrev]
            return regions_with_abbrev[0].abbrev if len(regions_with_abbrev) else None

        state = business.region.get_parent_by_type(Region.Type.STATE) if business.region else None
        return state.abbrev if state else None

    @staticmethod
    def has_valid_stripe_amount(payment_row: PaymentRow) -> bool:
        # https://stripe.com/docs/currencies#minimum-and-maximum-charge-amounts
        if payment_row.amount < 0.5:
            return False

        return True

    @staticmethod
    def get_deeplink_to_bcr_dashboard():
        return DeepLinkCache.get(STRIPE_DASHBOARD)

    @staticmethod
    def process_chargeback(intent: StripePaymentIntent) -> PaymentRow:
        payment_row: PaymentRow = intent.payment_row
        if intent.chargeback_initiated:
            return payment_row
        StripeProvider._save_chargeback_as_initiated(intent)

        if payment_row.status in receipt_status.CHARGEBACK_STATUSES:
            return payment_row

        splits = StripeProvider.calculate_chargeback_splits(payment_row)

        return payment_row.update_status(
            status=receipt_status.CHARGEBACK,
            settled=True,
            payment_splits=splits,
            log_action=PaymentRowChange.SINGLE_ROW_UPDATE,
            log_note='Stripe chargeback',
        )

    @staticmethod
    def _get_instant_balance(balance_object: stripe.Balance) -> Decimal:
        for balance in balance_object.instant_available:
            if not balance['currency'].casefold() == settings.CURRENCY_CODE.casefold():
                continue
            return major_unit(balance.amount)
        return Decimal(0)

    @staticmethod
    def _get_total_balance(balance_object: stripe.Balance) -> Decimal:
        pending = Decimal(0)
        for balance in balance_object.pending:
            if not balance['currency'].casefold() == settings.CURRENCY_CODE.casefold():
                continue
            pending = major_unit(balance.amount)
            break

        available = Decimal(0)
        for balance in balance_object.available:
            if not balance['currency'].casefold() == settings.CURRENCY_CODE.casefold():
                continue
            available = major_unit(balance.amount)
            break

        return pending + available

    @staticmethod
    def get_instant_available(account: StripeAccount) -> Decimal:
        balance_object = stripe.Balance.retrieve(stripe_account=account.external_id)
        if not balance_object:
            return Decimal(0)

        if settings.POS__FAST_PAYOUTS:
            return StripeProvider._get_instant_balance(balance_object=balance_object)

        return StripeProvider._get_total_balance(balance_object=balance_object)

    @staticmethod
    def get_available_payout_methods(account: StripeAccount) -> set:
        payout_methods = set()
        if fast_payout_conf := STRIPE_FAST_PAYOUT_COUNTRIES_CONFIG.get(settings.API_COUNTRY):
            account = stripe.Account.retrieve(account.external_id)
            if external_accounts := account.external_accounts:
                for external_account in external_accounts:
                    if external_account.object == fast_payout_conf['source_type_getter']:
                        for method in external_account.available_payout_methods:
                            payout_methods.add(method)

        return payout_methods

    @staticmethod
    def get_bank_or_card_details(account: StripeAccount) -> dict:
        account = stripe.Account.retrieve(account.external_id)
        if external_accounts := account.external_accounts:
            for external_account in external_accounts:
                return {external_account.object: external_account}
        return {}

    @staticmethod
    def trigger_fast_payout(
        account: StripeAccount, splits: dict, description: t.Optional[str]
    ) -> StripePayout:
        pos = account.pos
        wallet = PaymentGatewayPort.get_business_wallet(business_id=pos.business.id)
        payout_bt = PaymentGatewayPort.initialize_payout(
            wallet_id=wallet.id,
            amount=splits['total_amount'],
            payment_provider_code=PaymentProviderCode.STRIPE,
            payout_type=PayoutType.FAST,
        )
        payment_providers_id = (
            BalanceTransaction.objects.only(
                "id",
                "external_id",
            )
            .get(id=payout_bt.id)
            .external_id
        )

        pp_payout = PaymentProvidersPayout.objects.get(id=payment_providers_id)
        payload = {
            'id': pp_payout.stripe_payout.external_id,
            'stripe_account': account.external_id,
            'expand': ['destination'],
        }
        stripe_payout_obj = stripe.Payout.retrieve(**payload)

        payout = StripeProvider.create_or_update_payout(
            stripe_payout_obj,
            account,
        )
        payout.fast_payout_splits = splits
        payout.description = description
        payout.save()

        return payout

    @staticmethod
    def capture_payment_intent(intent: StripePaymentIntent):
        try:
            intent = stripe.PaymentIntent.capture(intent.external_id)
        except InvalidRequestError as e:
            if exception := StripeProvider.convert_stripe_error_to_exception(e):
                raise exception from e  # pylint: disable=raising-bad-type
            raise e
        return intent

    @staticmethod
    def convert_stripe_error_to_exception(stripe_error: InvalidRequestError):
        regex_patterns_with_exceptions = [
            (
                r"^This PaymentIntent could not be captured because it has already been captured.$",
                PaymentIntentRecaptured,
            ),
            (
                r"^Reader is currently unreachable, please ensure the reader is powered on and connected to the internet before retrying your request.$",  # pylint: disable=line-too-long
                ReaderUnreachable,
            ),
            (
                r"^Reader is currently busy processing another request, please retry your request soon.$",  # pylint: disable=line-too-long
                ReaderIsBusy,
            ),
            (
                r"^You cannot cancel this PaymentIntent because it has a status of succeeded. Only a PaymentIntent with one of the following statuses may be canceled: requires_payment_method, requires_capture, requires_confirmation, requires_action, processing.$",  # pylint: disable=line-too-long
                CancelPaymentIntentNotPossible,
            ),
            (
                r"^Reader action was canceled by a subsequent request.$",
                ReaderAlreadyCanceled,
            ),
        ]
        for regex_pattern, exception in regex_patterns_with_exceptions:
            r = re.search(regex_pattern, stripe_error.user_message)
            if r:
                return exception
        return None

    @staticmethod
    def _save_chargeback_as_initiated(intent: StripePaymentIntent):
        intent.chargeback_initiated = True
        intent.save(update_fields=['chargeback_initiated'])

    @staticmethod
    def is_verified(business: Business) -> bool:
        return sget(business, ['pos', 'stripe_account', 'status']) == StripeAccountStatus.VERIFIED


class StripeProxyProvider(ProxyProvider):
    codename = PaymentProviderEnum.STRIPE_PROXY_PROVIDER.value
    label = PaymentProviderEnum.STRIPE_PROXY_PROVIDER.label

    @staticmethod
    def create_payment_intent(  # pylint: disable=unused-argument
        transaction: Transaction,
        payment_row: PaymentRow,
        terminal_identifier: str = None,
        terminal_device_type: str = None,
    ) -> t.Tuple[str, StripePaymentIntent]:
        """
        Returns client secret and payment intent object

        in this flow payment_intent is already created - it's done during
        basket_payment initialization so all we need is just retrieve
        existing data
        """

        basket_payment_id = payment_row.basket_payment_id
        basket_payment = BasketPayment.objects.get(id=basket_payment_id)
        wallet_id = get_business_wallet_id_adapter(basket_payment.basket.business_id)

        client_secret = PaymentGatewayPort.get_payment_client_token(
            balance_transaction_id=basket_payment.balance_transaction_id,
            wallet_id=wallet_id,
        )
        payment_intent = payment_row.intents.last()

        fields_to_update = []
        if terminal_identifier is not None:
            payment_intent.terminal_identifier = terminal_identifier
            fields_to_update.append("terminal_identifier")
        if terminal_device_type is not None:
            payment_intent.terminal_device_type = terminal_device_type
            fields_to_update.append("terminal_device_type")
        payment_intent.save(update_fields=fields_to_update)

        return client_secret, payment_intent

    @staticmethod
    def process_actions(
        action: StripePaymentIntentActions,
        intent_object: StripePaymentIntent,
        operator: User,
        action_kwargs: dict = None,
    ):
        handler = {
            StripePaymentIntentActions.CANCEL: StripeProxyProvider._process_cancel,
            StripePaymentIntentActions.FAILED: StripeProxyProvider._process_fail,
        }[action]

        action_kwargs = action_kwargs or {}

        return handler(intent_object, operator, **action_kwargs)

    @staticmethod
    def _process_fail(intent_object: StripePaymentIntent, operator: User, error_code: str = None):
        log_note = 'StripePaymentIntentActionHandler set payment status'
        StripeProxyProvider.handle_status_failed(intent_object, log_note, operator, error_code)

    @staticmethod
    def handle_status_failed(
        intent_object: StripePaymentIntent,
        log_note: str,  # pylint: disable=unused-argument
        operator: User = None,  # pylint: disable=unused-argument
        error_code: str = None,
    ):
        try:
            _lock = StripeUpdateStatusToFailLock.lock(intent_object.external_id)
        except RedlockError:
            return True
        if not _lock:
            return True

        basket_payment_id = intent_object.payment_rows.last().basket_payment_id
        basket_payment = BasketPayment.objects.get(id=basket_payment_id)
        wallet_id = get_business_wallet_id_adapter(basket_payment.basket.business_id)

        PaymentGatewayPort.mark_payment_as_failed(
            balance_transaction_id=basket_payment.balance_transaction_id,
            wallet_id=wallet_id,
            error_code=error_code,
        )

        StripeUpdateStatusToFailLock.try_to_unlock(_lock)
        return True

    @staticmethod
    def _process_cancel(
        intent_object: StripePaymentIntent,
        operator: User,  # pylint: disable=unused-argument
    ):
        transaction = intent_object.payment_row.receipt.transaction
        last_pr = transaction.payment_rows.filter(
            payment_type__code__in=PaymentTypeEnum.terminal_methods(),
        ).last()

        ProxyProvider.cancel_payment(last_pr, 'StripeProxyProvider _process cancel')

    @staticmethod
    def send_for_refund(
        payment_row: 'PaymentRow',
        operator: User,
        from_admin: bool = True,
        payment_splits: dict = None,
    ):
        return ProxyProvider.send_for_refund(
            payment_row=payment_row,
            operator=operator,
            from_admin=from_admin,
            payment_splits=StripeProvider.calculate_refund_splits(pr=payment_row),
        )

    # refund related methods are inherited from proxyprovider with no change
