from lib.celery_tools import celery_task


@celery_task
def charge_fast_payout_paid_fee_task(payout_id: int):
    # pylint: disable=cyclic-import
    from webapps.stripe_integration.enums import StripePayoutMethodType, StripePayoutStatus
    from webapps.stripe_integration.models import StripePayout
    from webapps.stripe_integration.provider import StripeProvider

    payout = StripePayout.objects.filter(
        id=payout_id,
        method=StripePayoutMethodType.INSTANT,
        status=StripePayoutStatus.PAID,
        provision_charged=False,
    ).first()

    if not payout:
        return
    StripeProvider.charge_fast_payout_fee(payout)


@celery_task
def trigger_fast_payout_paid_fees_task():
    # pylint: disable=cyclic-import
    from webapps.stripe_integration.enums import StripePayoutMethodType, StripePayoutStatus
    from webapps.stripe_integration.models import StripePayout

    payouts_to_charge_fees_ids = StripePayout.objects.filter(
        method=StripePayoutMethodType.INSTANT,
        status=StripePayoutStatus.PAID,
        provision_charged=False,
    ).values_list('id', flat=True)

    for payout_id in payouts_to_charge_fees_ids:
        charge_fast_payout_paid_fee_task.delay(payout_id=payout_id)


@celery_task
def synchronize_stripe_payout_task(event_body: dict):
    from webapps.stripe_integration.services import SynchronizeService

    SynchronizeService.synchronize_stripe_payout(event_body)
