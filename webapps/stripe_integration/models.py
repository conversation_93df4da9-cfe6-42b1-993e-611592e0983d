from typing import Optional
from urllib.parse import urljoin

import stripe
from django.conf import settings
from django.db import models
from django.db.models import J<PERSON><PERSON>ield
from django.utils.functional import cached_property

from lib.feature_flag.feature.payment import FixStripeAccountHandlerLatency
from lib.fields.unique_foreign_key_field import UniqueForeignKey
from lib.models import (
    ArchiveManager,
    ArchiveModel,
    AutoUpdateQuerySet,
    BaseArchiveManager,
)
from lib.payment_providers.enums import StripeAccountType
from lib.queryset import MidnightQuerySet
from lib.tools import format_currency
from webapps.business.models.bci import BusinessCustomerInfo
from webapps.pos.enums import PaymentTypeEnum
from webapps.pos.enums.receipt_status import SUCCESS_STATUSES_WITH_PREPAYMENT
from webapps.pos.models import (
    POS,
    PaymentRow,
    PaymentType,
)
from webapps.stripe_integration.enums import (
    FastPayoutStatus,
    StripeAccountOnboardingInfo,
    StripeAccountOnboardingStatus,
    StripeAccountStatus,
    StripeBalanceTransactionType,
    StripePayoutMethodType,
    StripePayoutStatus,
    StripeTerminalIntegrationMode,
)
from webapps.stripe_integration.tools import format_stripe_amount
from webapps.stripe_terminal.enums import StripeStatusType
from webapps.stripe_terminal.models import (
    Order,
    OrderPayment,
)
from webapps.user.models import User


# TODO na razie wszystko nazywa sie Stripe, ale moze warto to zimenic.
class StripeAccount(ArchiveModel):  # pylint: disable=abstract-method
    external_id = models.CharField(max_length=32, unique=True)
    pos = models.ForeignKey(
        POS,
        null=True,
        blank=True,
        on_delete=models.DO_NOTHING,
        db_constraint=False,
        related_name='stripe_accounts',
    )
    status = models.CharField(
        max_length=24,
        choices=StripeAccountStatus.choices(),
        default=StripeAccountStatus.TURNED_OFF,
    )

    # this can only go False -> True
    kyc_verified_at_least_once = models.BooleanField(default=False)

    tos_acceptance_date = models.DateTimeField(null=True, blank=True)
    account_link_first_time_created = models.DateTimeField(
        null=True,
        default=None,
    )

    # set in admin panel, used to force onboarding status
    onboarding_info = models.CharField(
        max_length=8,
        choices=StripeAccountOnboardingInfo.choices(),
        default=StripeAccountOnboardingInfo.DEFAULT,
    )
    integration_mode = models.CharField(
        max_length=3,
        choices=StripeTerminalIntegrationMode.choices(),
        default=StripeTerminalIntegrationMode.SDK,
    )
    charge_for_tips = models.BooleanField(default=True)
    charges_enabled = models.BooleanField(default=False)
    payouts_enabled = models.BooleanField(default=False)
    account_type = models.CharField(
        max_length=8,
        choices=StripeAccountType.choices(),
    )
    blocked = models.BooleanField(default=False)

    fast_payout_max_limit = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=settings.STRIPE_FAST_PAYOUT_DEFAULT_DAILY_MAX_LIMIT,
    )
    fast_payout_min_limit = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=settings.STRIPE_FAST_PAYOUT_DEFAULT_DAILY_MIN_LIMIT,
    )
    fast_payout_merchant_max_limit = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=settings.STRIPE_FAST_PAYOUT_DEFAULT_DAILY_MAX_MERCHANT_LIMIT,
    )
    default_payout_method_for_fast_payout = models.CharField(max_length=128, null=True)
    force_synchronize_with_new_structure = models.BooleanField(default=False)

    objects = ArchiveManager()
    all_objects = models.Manager()

    def __str__(self):
        return f'{self.id}, {self.external_id}'

    @property
    def onboarding_status(self):
        """Method DEPRECATED. bcr_onboarding_status should be used instead."""
        return self.bcr_onboarding_status

    @property
    def bcr_onboarding_status(self):  # pylint: disable=too-many-return-statements
        if not settings.POS__STRIPE_TERMINAL:
            return None

        business_id = self.pos.business_id

        if self.onboarding_info == StripeAccountOnboardingInfo.SKIP:
            return StripeAccountOnboardingStatus.FIRST_SUCCEEDED_TRANSACTION

        succeeded_order = OrderPayment.objects.filter(
            business_id=business_id,
            stripe_status=OrderPayment.StripePaymentStatusType.SUCCEEDED,
        ).exists()
        if not succeeded_order:
            return None

        pt = PaymentType.all_objects.filter(
            code=PaymentTypeEnum.STRIPE_TERMINAL, pos=self.pos
        ).first()

        if pt:
            payment_row = PaymentRow.objects.filter(
                status__in=SUCCESS_STATUSES_WITH_PREPAYMENT,
                payment_type=pt,
            ).exists()

            if payment_row:
                return StripeAccountOnboardingStatus.FIRST_SUCCEEDED_TRANSACTION

        shipped_terminal = Order.objects.filter(
            business_id=business_id, stripe_status=StripeStatusType.SHIPPED
        ).exists()
        if shipped_terminal:
            return StripeAccountOnboardingStatus.TERMINAL_SHIPPED

        if succeeded_order:
            return StripeAccountOnboardingStatus.TERMINAL_ORDERED

        return None

    @property
    def has_successful_transaction(self):
        business_id = self.pos.business_id

        if FixStripeAccountHandlerLatency():
            return StripePaymentIntent.objects.filter(
                account=self,
                payment_rows__status__in=SUCCESS_STATUSES_WITH_PREPAYMENT,
            ).exists()

        return (
            StripePaymentIntent.get_by_bussiness_id(
                business_id,
            )
            .filter(
                payment_rows__status__in=SUCCESS_STATUSES_WITH_PREPAYMENT,
            )
            .exists()
        )

    @property
    def fast_payouts_status(self):
        # pylint: disable=cyclic-import, too-many-return-statements
        from webapps.stripe_integration.provider import StripeProvider

        if not settings.POS__FAST_PAYOUTS or not self.pos.fast_payouts_visible:
            return FastPayoutStatus.HIDDEN

        if self.pos.fast_payouts_admin_blocked:
            return FastPayoutStatus.BLOCKED

        if not self.status == StripeAccountStatus.VERIFIED:
            return FastPayoutStatus.MISSING_KYC

        if not self.has_successful_transaction:
            return FastPayoutStatus.COMPLETE_FIRST_TRANSACTION

        if not self.pos.fast_payouts_merchant_enabled:
            return FastPayoutStatus.MERCHANT_DISABLED

        if not self.pos.fast_payouts_available:
            external_acc_details = StripeProvider.get_bank_or_card_details(self)
            if external_acc_details.get("card"):
                return FastPayoutStatus.WRONG_DEBIT_CARD
            if external_acc_details.get("bank_account"):
                return FastPayoutStatus.WRONG_BANK_ACCOUNT
            return FastPayoutStatus.MISSING_PAYOUT_METHOD

        if not self.pos.fast_payouts_available_today:
            return FastPayoutStatus.AVAILABLE_TOMORROW

        return FastPayoutStatus.AVAILABLE

    @property
    def can_be_safely_deleted(self):
        # pylint: disable=too-many-return-statements
        if self.deleted:
            return False

        if self.kyc_verified_at_least_once:
            return False

        if self.locations.exists():
            return False

        if self.intents.exists():
            return False

        if self.payouts.exists():
            return False

        return True

    @property
    def stripe_dashboard_url(self):
        return urljoin(settings.STRIPE_DASHBOARD_URL, f"connect/accounts/{self.external_id}")

    @property
    def fast_payout_min_amount(self):
        return self.fast_payout_min_limit

    @property
    def fast_payout_max_amount(self):
        return min(
            [
                self.fast_payout_max_limit,
                self.fast_payout_merchant_max_limit,
            ]
        )


# TODO na razie oddzielne modele, byc moze zrobic abstract model
class StripeConnectNotification(ArchiveModel):
    """Connect webhooks are for activity on any connected account.
    This includes the important account.updated event for any connected
    account and direct charges."""

    external_id = models.CharField(max_length=32, unique=True)
    type = models.CharField(max_length=64)
    data = models.JSONField(blank=True, null=True, default=dict)
    handled_successfully = models.BooleanField(default=None, null=True)
    handling_errors = models.JSONField(blank=True, null=True, default=list)
    account = models.ForeignKey(
        'stripe_integration.StripeAccount',
        related_name='notifications',
        on_delete=models.CASCADE,
        null=True,
    )


class StripeAccountNotification(ArchiveModel):
    """Account webhooks are for activity on your own account.
    (e.g., most requests made using your API keys and without authenticating
    as another Stripe account). This includes all types of charges,
    except those made directly on a connected account"""

    external_id = models.CharField(max_length=32, unique=True)
    type = models.CharField(max_length=64)
    data = models.JSONField(blank=True, null=True, default=dict)
    handled_successfully = models.BooleanField(default=None, null=True)
    handling_errors = models.JSONField(blank=True, null=True, default=list)
    payment_intent = models.ForeignKey(
        'stripe_integration.StripePaymentIntent',
        related_name='notifications',
        on_delete=models.CASCADE,
        null=True,
    )


class StripeLocation(ArchiveModel):
    external_id = models.CharField(max_length=32, unique=True)
    reader_configuration_id = models.CharField(max_length=48, unique=True, null=True, blank=True)
    name = models.CharField(max_length=255)
    default = models.BooleanField(default=False)
    # We only allow one Location per Account
    account = UniqueForeignKey(
        'stripe_integration.StripeAccount',
        related_name='locations',
        on_delete=models.CASCADE,
    )

    @property
    def readers(self) -> list:
        # "location" param in .list() takes in stripe id of the location
        return list(
            stripe.terminal.Reader.list(location=self.external_id, limit=100).auto_paging_iter()
        )


class StripePaymentIntent(ArchiveModel):
    external_id = models.CharField(max_length=32, unique=True)
    account = models.ForeignKey(
        'stripe_integration.StripeAccount',
        on_delete=models.CASCADE,
        related_name='intents',
    )
    payment_rows = models.ManyToManyField(
        # unfortunately it's m2m, but it should be intent 1-* paymentrow
        # (many payment rows because of the pr cloning for history)
        # it is very important tho for 1 pr to have only 1 intent
        'pos.PaymentRow',
        related_name='intents',
    )
    terminal_identifier = models.CharField(max_length=255, blank=True, null=True)
    terminal_device_type = models.CharField(max_length=32, blank=True, null=True)
    error_code = models.CharField(max_length=255, blank=True, null=True)
    is_authorized = models.BooleanField(default=False)
    chargeback_initiated = models.BooleanField(default=False)
    # chargeback_initiated field was added
    # due to possibility of multiple chargeback calls (PAYM-1184)

    @property
    def payment_row(self):
        """Due to coping PaymentRows when Receipt is updated we need to connect paymentIntent,
        with all of them, but only for the last its valid.
        """
        return self.payment_rows.last()

    @cached_property
    def customer(self) -> Optional[User]:
        return self.payment_row.receipt.transaction.customer

    @cached_property
    def customer_card(self) -> Optional[BusinessCustomerInfo]:
        return self.payment_row.receipt.transaction.customer_card

    @classmethod
    def assign_payment_rows(cls, payment_rows):
        """
        If parent of payment_row has assigned PaymentIntent, we want to assign it to child also.
        """

        payment_rows_with_prefetches = (
            PaymentRow.objects.filter(
                id__in=[pr.id for pr in payment_rows],
                parent_payment_row__isnull=False,
            )
            .select_related(
                'parent_payment_row',
            )
            .prefetch_related('parent_payment_row__intents')
        )

        objects_to_create = []

        for payment_row in payment_rows_with_prefetches:
            intents = payment_row.parent_payment_row.intents.all()

            for intent in intents:
                objects_to_create.append(
                    cls.payment_rows.through(
                        stripepaymentintent_id=intent.id,
                        paymentrow_id=payment_row.id,
                    )
                )

        cls.payment_rows.through.objects.bulk_create(objects_to_create)

    @classmethod
    def get_by_bussiness_id(cls, business_id):
        return StripePaymentIntent.objects.filter(
            payment_rows__receipt__transaction__pos__business__id=business_id
        )

    @staticmethod
    def get_by_external_id(external_id: str) -> Optional['StripePaymentIntent']:
        return StripePaymentIntent.objects.filter(external_id=external_id).first()

    @property
    def stripe_dashboard_url(self):
        return urljoin(settings.STRIPE_DASHBOARD_URL, f"payments/{self.external_id}")

    @property
    def formatted_amount(self) -> str:
        return format_currency(self.payment_row.amount)


class StripePayoutMidnightQuerySet(MidnightQuerySet, AutoUpdateQuerySet):
    tz_field = 'account__pos__business__time_zone_name'
    dt_field = 'payout_created'


class StripePayout(ArchiveModel):
    """
    Same as Payout object in Stripe API (our custom fields are marked with # additional)
    """

    external_id = models.CharField(max_length=32, unique=True)
    amount = models.IntegerField(default=0, blank=True)
    status = models.CharField(choices=StripePayoutStatus.choices(), max_length=32)
    method = models.CharField(
        choices=StripePayoutMethodType.choices(),
        max_length=32,
    )
    destination = models.JSONField(null=True, blank=True)
    payout_created = models.DateTimeField(null=True, blank=True)  # field name: created in api
    application_fee = models.IntegerField(default=0, blank=True)  # additional
    account = models.ForeignKey(
        'stripe_integration.StripeAccount',
        on_delete=models.CASCADE,
        related_name='payouts',
    )  # additional
    fast_payout_splits = JSONField(null=True, blank=True)  # additional
    description = models.CharField(null=True, blank=True, max_length=100)  # additional
    provision_charged = models.BooleanField(default=False)  # additional

    objects = BaseArchiveManager.from_queryset(StripePayoutMidnightQuerySet)()

    @property
    def formatted_amount(self) -> str:
        return format_stripe_amount(self.amount)

    @property
    def formatted_application_fee(self) -> str:
        return format_stripe_amount(self.application_fee)

    @cached_property
    def balance_transactions_breakdown(self):
        from django.db.models import Sum

        qs = (
            self.balance_transactions.all()
            .values('transaction_type')
            .annotate(amount=Sum('amount'))
        )
        return dict(qs.values_list('transaction_type', 'amount'))


class StripeBalanceTransaction(ArchiveModel):
    """
    NOT the same as BalanceTransaction in Stripe API
    """

    external_id = models.CharField(
        max_length=32, unique=True
    )  # id of an object of a given type (charge/refund), not the balance transaction itself
    transaction_type = models.CharField(
        choices=StripeBalanceTransactionType.choices(), max_length=32
    )
    description = models.CharField(max_length=255, null=True, blank=True)
    payment_intent = models.ForeignKey(
        StripePaymentIntent,
        on_delete=models.DO_NOTHING,
        related_name='balance_transactions',
        null=True,  # null in case of fees or BGC payment etc
    )
    source_transfer_id = models.CharField(max_length=64, null=True, blank=True)
    amount = models.IntegerField(default=0, blank=True)
    application_fee = models.IntegerField(default=0, blank=True)
    payout = models.ForeignKey(
        StripePayout, on_delete=models.CASCADE, related_name='balance_transactions'
    )
    transaction_created = models.DateTimeField(blank=True, null=True)  # field name: created in api

    objects = ArchiveManager()
    all_objects = models.Manager()

    @property
    def formatted_amount(self) -> str:
        return format_stripe_amount(self.amount)

    @property
    def formatted_application_fee(self) -> str:
        return format_stripe_amount(self.application_fee)


class StripeCustomer(ArchiveModel):
    external_id = models.CharField(max_length=32, unique=True)
    user = UniqueForeignKey(
        'user.User',
        related_name='stripe_customers',
        on_delete=models.CASCADE,
    )

    @property
    def stripe_dashboard_url(self):
        return urljoin(settings.STRIPE_DASHBOARD_URL, f"customers/{self.external_id}")
