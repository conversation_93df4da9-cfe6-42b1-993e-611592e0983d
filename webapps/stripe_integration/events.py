from lib.events import EventSignal

stripe_first_time_kyc_passed_event = EventSignal(
    event_type='stripe_first_time_kyc_passed'  # instance: StripeAccount
)

stripe_kyc_failed_event = EventSignal(event_type='stripe_kyc_failed')  # instance: StripeAccount

stripe_kyc_requires_update_event = EventSignal(
    event_type='stripe_kyc_requires_update'  # instance: StripeAccount
)

stripe_refund_event = EventSignal(event_type='stripe_refund')  # instance: StripePaymentIntent

stripe_payout_paid_event = EventSignal(event_type='stripe_payout_paid')  # instance: StripePayout

stripe_payout_failed_event = EventSignal(
    event_type='stripe_payout_failed'  # instance: StripePayout
)
