from django.contrib import admin

from lib.admin_helpers import BaseModelAdmin
from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin
from webapps.family_and_friends.models import (
    BCIRelation,
    MemberAppointment,
    MemberBusinessCustomerInfo,
    MemberInvitation,
    MemberProfile,
    MemberRelations,
    MemberTransaction,
)
from webapps.user.groups import GroupNameV2


class MemberProfileAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = ()
    model = MemberProfile

    list_display = (
        'id',
        'first_name',
        'last_name',
        'email',
        'cell_phone',
        'user_profile',
        'created',
        'updated',
        'deleted',
    )
    list_display_links = (
        'id',
        'user_profile',
    )

    search_fields = ('=id', '=user_profile__id', 'email', 'cell_phone')
    list_filter = ('updated',)
    raw_id_fields = ('user_profile', 'photo')


admin.site.register(MemberProfile, MemberProfileAdmin)


class MemberBusinessCustomerInfoAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = ()
    model = MemberBusinessCustomerInfo
    list_display = (
        'id',
        'member',
        'bci',
    )
    list_display_links = (
        'id',
        'member',
        'bci',
    )

    search_fields = (
        '=id',
        '=bci__id',
        '=member__id',
    )
    raw_id_fields = ('member', 'bci')


admin.site.register(MemberBusinessCustomerInfo, MemberBusinessCustomerInfoAdmin)


class MemberInvitaitonAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = ()
    model = MemberInvitation
    list_display = (
        'id',
        'key',
        'parent',
        'member',
        'status',
        'valid_till',
    )
    list_display_links = (
        'id',
        'parent',
        'member',
    )
    search_fields = (
        '=id',
        '=parent__id',
        '=member__id',
    )
    list_filter = ('status', 'valid_till')
    raw_id_fields = ('parent', 'member')


admin.site.register(MemberInvitation, MemberInvitaitonAdmin)


class MemberRelationsAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = ()
    model = MemberRelations
    list_display = (
        'id',
        'parent',
        'member',
        'relationship_type',
    )
    list_display_links = (
        'id',
        'parent',
        'member',
    )
    search_fields = (
        '=id',
        '=parent__id',
        '=member__id',
    )
    list_filter = ('relationship_type',)
    raw_id_fields = ('member', 'parent')


admin.site.register(MemberRelations, MemberRelationsAdmin)


class MemberTransactionAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = ()
    model = MemberTransaction

    list_display = (
        'id',
        'member',
        'transaction',
    )
    list_display_links = (
        'id',
        'member',
        'transaction',
    )
    search_fields = (
        '=id',
        '=transaction__id',
        '=member__id',
    )
    raw_id_fields = ('member', 'transaction')


admin.site.register(MemberTransaction, MemberTransactionAdmin)


class MemberAppointmentAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = ()
    model = MemberAppointment
    list_display = ('id', 'appointment', 'booked_for', 'booked_by')
    list_display_links = ('id', 'appointment', 'booked_for', 'booked_by')
    search_fields = ('=id', '=appointment__id', '=booked_for__id', '=booked_by__id')
    raw_id_fields = ('appointment', 'booked_for', 'booked_by')


admin.site.register(MemberAppointment, MemberAppointmentAdmin)


class BCIRelationAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = ()
    model = BCIRelation
    list_display = (
        'id',
        'parent_bci',
        'member_bci',
        'relationship_type',
        'use_parent_data',
    )
    list_display_links = (
        'id',
        'parent_bci',
        'member_bci',
    )
    search_fields = (
        '=id',
        '=parent_bci__id',
        '=member_bci__id',
    )
    list_filter = ('relationship_type',)
    raw_id_fields = (
        'member_bci',
        'parent_bci',
    )


admin.site.register(BCIRelation, BCIRelationAdmin)
