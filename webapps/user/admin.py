from typing import ClassVar
from django import forms
from django.conf import settings
from django.contrib import admin, messages
from django.contrib.admin import helpers
from django.contrib.admin.widgets import FilteredSelectMultiple
from django.contrib.auth.models import Group, Permission
from django.db.models.query import Prefetch
from django.shortcuts import redirect
from django.urls import re_path as url
from django.urls import reverse as url_reverse
from django.utils.html import escape, format_html
from django.utils.translation import gettext_lazy as _

from lib.admin_helpers import (
    BaseModelAdmin,
    NoDelMixin,
    NoAddDelMixin,
)
from lib.enums import StrEnum
from lib.tools import get_object_or_404
from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin
from webapps.booking.models import BookingSources
from webapps.business.models.category import BusinessCategory
from webapps.booksy_med.models import BooksyMedAgreement
from webapps.business._admin.views import ResourceInline
from webapps.marketplace.models import (
    MATCH_TRATMENT_ASSIGNED,
    MATCH_TRATMENT_CORRECT,
    MATCH_TRATMENT_MATCHED,
    MATCH_TRATMENT_REJECTED,
    MatchTreatmentLog,
)
from webapps.user.groups import GroupName, GroupNameV2
from webapps.user.models import (
    EXTERNAL_GROUP_NAMES,
    EmailToken,
    ExternalUser,
    GroupV2,
    UnsubscribedEmail,
    User,
    UserInternalData,
    UserPolicyAgreement,
    UserProfile,
    UserSessionCache,
    CustomerFavoriteCategory,
)
from webapps.user.tasks.gdpr import (
    gdpr_customer_data_export_task,
)
from webapps.user.tasks.sync import _sync_user_booksy_auth_proxy  # pylint: disable=protected-access
from webapps.user.tools import can_change_password
from webapps.user.utils import delete_user, mark_user_with_deletion_request


class UserModelForm(forms.ModelForm):
    class Meta:
        model = User
        fields = '__all__'

    def clean_facebook_id(self):
        return self.cleaned_data.get('facebook_id') or None

    def clean_email(self):
        email = self.cleaned_data.get('email')
        if email:
            qs_user = User.objects.filter(
                email__iexact=email.lower(),
            )
            if self.instance:
                qs_user = qs_user.exclude(id=self.instance.id)
            if qs_user.exists():
                raise forms.ValidationError(
                    _('User with provided email already exists.'),
                    params={
                        'type': 'database',
                        'entity': 'user',
                        'code': 'entity_exists',
                        'field': 'email',
                    },
                )

        return email


class ExternalUserModelForm(forms.ModelForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        groups = Group.objects.filter(name__in=EXTERNAL_GROUP_NAMES).values_list('id', 'name')
        self.fields['groups'].required = True
        self.fields['groups'].widget.choices = groups

    class Meta:
        model = ExternalUser
        fields = '__all__'


class UserProfileInline(NoAddDelMixin, admin.TabularInline):
    model = UserProfile
    raw_id_fields = ('user', 'region', 'photo')
    readonly_fields = ('created', 'updated', 'deleted')

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        if obj.user.businesses.all():
            form.base_fields['business_id'].initial = obj.user.businesses.all()[0].id
        else:
            form.base_fields['business_id'].initial = obj.user.staffers.all()[0].business.id
            form.base_fields['business_role'].initial = obj.user.staffers.all()[
                0
            ].staff_access_level
        return form


class UserPolicyAgreementInline(NoAddDelMixin, admin.TabularInline):
    model = UserPolicyAgreement
    readonly_fields = (
        'created',
        'privacy_policy_agreement',
        'marketing_agreement',
        'partner_marketing_agreement',
    )
    exclude = ('deleted',)
    verbose_name = "GDPR Agreement"
    verbose_name_plural = "GDPR Agreements"


class UserSessionCacheInline(NoAddDelMixin, admin.TabularInline):
    model = UserSessionCache
    verbose_name = 'User Session'
    verbose_name_plural = 'User Sessions'
    readonly_fields = (
        'access_level',
        'superuser',
    )
    fields = (
        'access_level',
        'superuser',
    )


class UserInternalDataInline(NoDelMixin, admin.StackedInline):
    model = UserInternalData
    verbose_name = "Internal Data"
    verbose_name_plural = "Internal Data"


class UserBooksyMedAgreementInline(NoDelMixin, admin.StackedInline):
    model = BooksyMedAgreement
    extra = 0
    verbose_name = "Booksy Med Agreement"
    verbose_name_plural = "Booksy Med Agreements"
    fields = (
        'medical_data_consent',
        'deleted',
    )
    readonly_fields = ('deleted',)


class UserFavoriteCategoryForm(forms.ModelForm):
    category = forms.ModelChoiceField(
        queryset=BusinessCategory.objects.filter(type=BusinessCategory.CATEGORY), label="Category"
    )

    class Meta:
        model = CustomerFavoriteCategory
        fields = '__all__'


class UserFavoriteCategory(admin.StackedInline):
    model = CustomerFavoriteCategory
    verbose_name = "Customer Favorite Category"
    verbose_name_plural = "Customer Favorite Categories"
    form = UserFavoriteCategoryForm
    extra = 1


class UserAccountDeletionRequestedFilter(admin.SimpleListFilter):
    title = 'Account Deletion Requested'
    parameter_name = 'account_deletion_requested'

    def lookups(self, request, model_admin):
        # fmt: off
        return (
            ('true', _('Show Users with account deletion request only')),
        )
        # fmt: on

    def queryset(self, request, queryset):
        if self.value() == 'true':
            return queryset.filter(internal_data__account_deletion_requested=True)
        return queryset


class UserAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    inlines = [
        UserProfileInline,
        ResourceInline,
        UserPolicyAgreementInline,
        UserSessionCacheInline,
        UserInternalDataInline,
        UserBooksyMedAgreementInline,
        UserFavoriteCategory,
    ]
    form = UserModelForm
    list_display = [
        'id',
        'email',
        'first_name',
        'last_name',
        'cell_phone',
        'date_joined',
        'is_active',
        'deleted',
        'include_in_analysis',
        'related_staffers',
        'owned_businesses',
        'user_profiles',
    ]
    list_per_page = 20
    search_fields = [
        '=id',
        'email',
        'first_name',
        'last_name',
        'cell_phone',
    ]
    list_filter = [
        'profiles__profile_type',
        'is_active',
        'include_in_analysis',
        'is_staff',
        'is_superuser',
        UserAccountDeletionRequestedFilter,
    ]
    readonly_fields = [
        'is_staff',
        'is_superuser',
        'superuser',
        'last_login',
        'date_joined',
        'groups',
        'user_permissions',
        'change_password_link',
        '_password_change_required',
        'owned_businesses',
        'have_agreements',
        'created',
        'updated',
        'customer_cards',
    ]
    fieldsets = (
        (
            None,
            {
                'fields': (
                    ('first_name', 'last_name'),
                    'email',
                    'is_active',
                    'have_agreements',
                    ('last_login', 'date_joined', 'created', 'updated'),
                    'facebook_id',
                    'apple_user_uuid',
                    'google_id',
                    ('cell_phone', 'home_phone', 'work_phone'),
                    'gender',
                    'payment_auto_accept',
                    'customer_cards',
                )
            },
        ),
        (
            'Password',
            {
                'classes': ('collapse',),
                'fields': (
                    '_password_change_required',
                    'change_password_link',
                ),
            },
        ),
        (
            'Admin',
            {
                'classes': ('collapse',),
                'fields': (
                    'username',
                    'is_superuser',
                    'is_staff',
                    'superuser',
                    'groups',
                    'user_permissions',
                    'include_in_analysis',
                ),
            },
        ),
        (
            'Owned businesses',
            {
                'fields': ('owned_businesses',),
            },
        ),
    )
    actions = ['customer_profile_gdpr_export']

    def get_queryset(self, request):
        return (  # pylint: disable=duplicate-code
            super()
            .get_queryset(request)
            .prefetch_related(
                'profiles',
                'businesses',
                'staffers',
                Prefetch(
                    'user_permissions',
                    queryset=Permission.objects.select_related('content_type'),
                ),
            )
        )

    @staticmethod
    def have_agreements(obj):
        return obj.agreement_exist

    have_agreements.boolean = True

    def _password_change_required(self, _obj):
        return format_html(
            '<a href="{}">Enforce password reset</a>',
            url_reverse('admin:enforce_password_reset'),
        )

    def change_password_link(self, obj):
        if obj.id is None:
            return '-'
        if hasattr(self, 'request_user') and not can_change_password(self.request_user, obj):
            return 'Not allowed'
        return format_html(
            '<a href="{}change_password?user_id={}">Change Password</a>',
            url_reverse('admin:index'),
            obj.id,
        )

    def related_staffers(self, obj):
        return (
            format_html(
                ', '.join(self._get_staffer_link(staffer) for staffer in obj.staffers.all())
            )
            or '-'
        )

    @staticmethod
    def _get_staffer_link(staffer):
        return format_html(
            '<a href="{}" class="viewsitelink">{}</a>',
            url_reverse('admin:business_resource_change', args=(staffer.id,)),
            escape(staffer.name),
        )

    def owned_businesses(self, obj):
        return (
            format_html(
                ', '.join(self._get_business_link(business) for business in obj.businesses.all())
            )
            or '-'
        )

    @staticmethod
    def customer_cards(instance):
        return format_html(
            '<a href="{0}?user_id={1}">{2}</a>',
            url_reverse(
                'admin:business_businesscustomerinfo_changelist',
            ),
            instance.id,
            _("List of customer cards"),
        )

    customer_cards.short_description = ''

    @staticmethod
    def _get_business_link(business):
        return format_html(
            '<a href="{}" class="viewsitelink">{}</a>',
            url_reverse('admin:business_business_change', args=(business.id,)),
            business.name,
        )

    @staticmethod
    def user_profiles(obj):
        return (
            ', '.join(profile.get_profile_type_display() for profile in obj.profiles.all()) or '-'
        )

    def get_form(self, request, obj=None, **kwargs):  # pylint: disable=arguments-differ
        self.log_user_out = self.get_log_user_out(obj)
        self.delete_customer_user = self.get_delete_customer_user(request, obj)
        self.request_deletion_of_user = self.get_request_deletion_of_user(request, obj)
        self.sync_user_auth = self.get_sync_user_auth(request, obj)
        self.request_user = request.user
        form = super().get_form(request, obj, **kwargs)
        return form

    @staticmethod
    def get_sync_user_auth(request, obj=None):
        if obj is None:
            return ''

        task_url = url_reverse("admin:sync_user_booksy_auth", args=(obj.id,))
        return f"""
            <a href="{task_url}" class="btn">
                Sync user with booksy-auth
            </a>
        """

    @staticmethod
    def get_log_user_out(obj=None):
        if obj is None or not obj.usersessioncache_set.count():
            return ''
        task_url = url_reverse("admin:log_user_out", args=(obj.id,))
        return f"""
            <a href="{task_url}" class="btn">
                Log user out
            </a>
        """

    def get_delete_customer_user(self, request, obj=None):
        if obj is None or not self._can_delete_customer_user(obj):
            return ''
        href = url_reverse("admin:delete_customer_user", args=(obj.id,))
        changelist_filters = request.GET.get('_changelist_filters')
        if changelist_filters:
            href = f'{href}?{changelist_filters}'
        onclick = "return confirm('This action will permanently remove user data. Are you sure ?')"
        return f"""
            <a href="{href}" class="btn btn-danger" onclick="{onclick}">
                Remove Personal Data
            </a>
        """

    def get_request_deletion_of_user(self, request, obj):
        if obj is None or (
            hasattr(obj, 'internal_data') and obj.internal_data.account_deletion_requested
        ):
            return ''
        href = url_reverse("admin:request_deletion_of_user", args=(obj.id,))
        onclick = (
            "return confirm('This action will notify user and businesses that this customer "
            f"used and after {settings.DELETE_ACCOUNT_AFTER_DAYS} "
            "cause permanent user data removal. Are you sure ?')"
        )
        return f"""
            <a href="{href}" class="btn btn-danger" onclick="{onclick}">
                Request Data Removal
            </a>
        """

    @staticmethod
    def _can_delete_customer_user(obj):
        return not obj.deleted

    def get_urls(self):
        urls = super().get_urls()
        additional_urls = [
            url(
                r'log_user_out/(?P<user_id>\d+)$',
                self.admin_site.admin_view(self.log_out_user_view),
                name='log_user_out',
            ),
            url(
                r'delete_customer_user/(?P<user_id>\d+)/?',
                self.admin_site.admin_view(self.delete_customer_user_view),
                name='delete_customer_user',
            ),
            url(
                r'request_deletion_of_user/(?P<user_id>\d+)/?',
                self.admin_site.admin_view(self.request_deletion_of_user_view),
                name='request_deletion_of_user',
            ),
            url(
                r'sync_user_booksy_auth/(?P<user_id>\d+)/?',
                self.admin_site.admin_view(self.sync_user_with_booksy_auth_view),
                name='sync_user_booksy_auth',
            ),
        ]
        return additional_urls + urls

    @staticmethod
    def log_out_user_view(request, user_id):
        user = get_object_or_404(User, id=user_id)
        user.delete_all_user_sessions()
        # TODO VIT delete all fro books_auth
        messages.success(
            request,
            f'User {str(user)} id:{user_id} logged out. Removed all sessions.',
        )
        return redirect(
            url_reverse(
                'admin:user_user_change',
                args=(user_id,),
            )
        )

    def delete_customer_user_view(self, request, user_id):
        user = get_object_or_404(User, id=user_id)
        removed, error = delete_user(user)
        if removed:
            messages.success(
                request, f'Customer user {str(user)} id:{user_id} deleted succesfully.'
            )
        else:
            messages.error(request, f'Customer user {str(user)} id:{user_id} not deleted: {error}')
        redirect_link = url_reverse(
            'admin:user_user_changelist',
        )
        query_string = request.META.get('QUERY_STRING')
        if query_string:
            redirect_link = f'{redirect_link}?{query_string}'
        return redirect(redirect_link)

    def sync_user_with_booksy_auth_view(self, request, user_id):
        user = get_object_or_404(User, id=user_id)
        results = _sync_user_booksy_auth_proxy([user_id])
        msg = f'User {str(user)} id:{user_id} synced '
        if results:
            messages.success(request, msg + 'succesfully.')
        else:
            messages.error(request, msg + 'unsuccessfully.')

        return redirect(
            url_reverse(
                'admin:user_user_change',
                args=(user_id,),
            )
        )

    def request_deletion_of_user_view(self, request, user_id):
        user = get_object_or_404(User, id=user_id)
        mark_user_with_deletion_request(
            user,
            BookingSources.objects.filter(app_type=BookingSources.ADMIN_APP, name='Admin').first(),
        )
        messages.success(
            request,
            f'User {str(user)} id:{user_id} removal requested. Removal will happen '
            f'in {settings.DELETE_ACCOUNT_AFTER_DAYS} days',
        )
        return redirect(
            url_reverse(
                'admin:user_user_change',
                args=(user_id,),
            )
        )

    def customer_profile_gdpr_export(self, request, queryset):  # pylint: disable=unused-argument
        # We don't need to evaluate the queryset received, we just need ids
        selected_ids = request.POST.getlist(helpers.ACTION_CHECKBOX_NAME)
        profile_ids = UserProfile.objects.filter(
            user_id__in=selected_ids,
            profile_type=UserProfile.Type.CUSTOMER,
        ).values_list('id', flat=True)
        gdpr_customer_data_export_task.delay(list(profile_ids))
        messages.success(
            request,
            f'GDPR data export will be sent to {len(profile_ids)} customer profiles.'
            f' Business profiles will be omitted.',
        )

    customer_profile_gdpr_export.short_description = "Export users' data and send it to them (GDPR)"


class ExternalUserAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    form = ExternalUserModelForm
    readonly_fields = [
        'last_login',
        'date_joined',
        'is_staff',
        'is_superuser',
    ]
    fields = [
        'password',
        'groups',
        'username',
        'first_name',
        'last_name',
        'email',
        'is_active',
        'deleted',
        'apple_user_uuid',
        'booking_score',
        'last_login',
        'date_joined',
        'is_staff',
        'is_superuser',
    ]
    list_display = ['email', 'accepted', 'rejected', 'assigned', 'matched', 'is_active']

    def get_queryset(self, request):
        return (
            super().get_queryset(request).filter(groups__name__in=EXTERNAL_GROUP_NAMES).distinct()
        )

    @staticmethod
    def count_match_status(user, status):
        return MatchTreatmentLog.objects.filter(user=user, status=status).count()

    def accepted(self, request):
        return self.count_match_status(user=request.user, status=MATCH_TRATMENT_CORRECT)

    def rejected(self, request):
        return self.count_match_status(user=request.user, status=MATCH_TRATMENT_REJECTED)

    def assigned(self, request):
        return self.count_match_status(user=request.user, status=MATCH_TRATMENT_ASSIGNED)

    def matched(self, request):
        return self.count_match_status(user=request.user, status=MATCH_TRATMENT_MATCHED)

    def save_model(self, request, obj, form, change):
        obj.is_staff = True
        obj.set_password(obj.password)
        super().save_model(request, obj, form, change)


class EmailTokenAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    list_display = [
        'token',
        'email',
    ]
    search_fields = [
        'email',
    ]
    list_per_page = 100
    date_hierarchy = 'expiry_date'

    def get_form(self, request, obj=None, **kwargs):  # pylint: disable=arguments-differ
        form = super().get_form(request, obj, **kwargs)
        form.base_fields['token'].widget = forms.Textarea()
        return form

    def save_model(self, request, obj, form, change):
        from lib.tokens import unsubscribe_token_generator

        if not obj.token:
            obj.token = unsubscribe_token_generator.make_token(obj.email)
        obj.save()


class UnsubscribedEmailAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    list_display = [
        'email',
        'created',
    ]
    search_fields = [
        'email',
    ]
    list_per_page = 100


class GroupForm(forms.ModelForm):
    class Meta:
        model = Group
        fields = ('name', 'user_set')

    user_set = forms.ModelMultipleChoiceField(
        queryset=User.objects.filter(is_staff=True),
        widget=FilteredSelectMultiple(_('user_set'), False),
        required=False,
    )


class BaseGroupAdmin(BaseModelAdmin):
    form = GroupForm
    list_display = [
        'id',
        'name',
        'members',
    ]

    @staticmethod
    def members(obj):
        return ', '.join(obj.user_set.values_list('email', flat=True))

    def save_model(self, request, obj, form, change):
        current_users = set(obj.user_set.all())
        new_users = set(form.cleaned_data['user_set'])

        current_user_ids = {user.id for user in current_users}
        new_user_ids = {user.id for user in new_users}

        added_user_ids = new_user_ids - current_user_ids
        removed_user_ids = current_user_ids - new_user_ids

        added_users = {user for user in new_users if user.id in added_user_ids}
        removed_users = {user for user in current_users if user.id in removed_user_ids}

        if removed_users:
            obj.user_set.remove(*removed_users)

        if added_users:
            obj.user_set.add(*added_users)

        # Mark that we handled user changes to prevent duplicate logs
        self._handled_user_changes = bool(added_users or removed_users)

        if added_users:
            added_emails = [user.email for user in added_users]
            change_message = f'<span style="color: green;">ADDED: {", ".join(added_emails)}</span>'
            super().log_change(request, obj, change_message)

        if removed_users:
            removed_emails = [user.email for user in removed_users]
            change_message = (
                f'<span style="color: red;">REMOVED: {", ".join(removed_emails)}</span>'
            )
            super().log_change(request, obj, change_message)

        super().save_model(request, obj, form, change)

    def log_change(self, request, obj, message):
        """
        Override to prevent empty logs for user_set changes.
        """
        # If the user changes are handled, skip Django's logging entirely
        if hasattr(self, '_handled_user_changes') and self._handled_user_changes:
            return

        # Otherwise, use Django's default logging
        super().log_change(request, obj, message)

    def get_form(self, request, obj=None, change=False, **kwargs):
        GroupForm.base_fields['user_set'].initial = obj.user_set.all()
        return GroupForm

    def has_add_permission(self, request):
        return False

    def has_view_permission(self, request, obj=None):
        return request.user.is_superuser

    def has_delete_permission(self, request, obj=None):
        return False

    def has_change_permission(self, request, obj=None):
        return request.user.is_superuser


class FilteredGroupAdminMixin:
    group_names_enum: ClassVar[type[StrEnum] | None] = None

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        if self.group_names_enum:
            return qs.filter(name__in=self.group_names_enum.values())
        return qs


class GroupAdmin(FilteredGroupAdminMixin, BaseGroupAdmin):
    group_names_enum = GroupName


class GroupAdminV2(FilteredGroupAdminMixin, BaseGroupAdmin):
    group_names_enum = GroupNameV2


admin.site.register(User, UserAdmin)
admin.site.register(ExternalUser, ExternalUserAdmin)
admin.site.register(EmailToken, EmailTokenAdmin)
admin.site.register(UnsubscribedEmail, UnsubscribedEmailAdmin)
admin.site.register(Group, GroupAdmin)
admin.site.register(GroupV2, GroupAdminV2)
