from django.contrib import admin

from lib.admin_helpers import BaseModelAdmin
from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin
from webapps.registrationcode.models import RegistrationCode
from webapps.user.groups import GroupNameV2


class RegistrationCodeAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = ()
    list_display = ['code', 'description', 'active']
    search_fields = ['code', 'businesses__name']
    list_per_page = 100


admin.site.register(RegistrationCode, RegistrationCodeAdmin)
