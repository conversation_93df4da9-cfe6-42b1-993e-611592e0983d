from django import forms
from django.contrib import admin

from lib.feature_flag.killswitch import UseOAuth2<PERSON>oom<PERSON>lient

from lib.admin_helpers import BaseModelAdmin, NoAddDelMixin, NoDelMixin
from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin
from webapps.user.groups import GroupNameV2
from webapps.zoom.client import ZoomClient, get_oauth2_client
from webapps.zoom.models import ZoomBusinessCredentials, ZoomMeeting, ZoomBusinessOAuth2Credentials


# region jwt-credentials
class ZoomBusinessCredentialsInlineFormset(forms.BaseInlineFormSet):
    def clean(self):
        if hasattr(self, 'cleaned_data'):
            data = self.cleaned_data[0]
            api_key = data.get('api_key')
            api_secret = data.get('api_secret')
            if api_key and api_secret:
                credentials_valid = ZoomClient.validate_credentials(api_key, api_secret)
                if not credentials_valid:
                    raise forms.ValidationError('Provided Zoom API credentials seem to be invalid')

        return super().clean()


class ZoomBusinessCredentialsInline(NoDelMixin, admin.TabularInline):
    model = ZoomBusinessCredentials
    formset = ZoomBusinessCredentialsInlineFormset
    verbose_name_plural = 'Zoom API Credentials'
    fields = (
        'api_key',
        'api_secret',
        'user_id',
        'allow_group_meetings_for_parallel_bookings',
    )
    readonly_fields = ('user_id',)


# endregion jwt-credentials


def validate_oauth2_credentials(data: dict):
    account_id = data.get('account_id')
    client_id = data.get('client_id')
    client_secret = data.get('client_secret')
    if all((account_id, client_id, client_secret)):
        client = get_oauth2_client(account_id, client_id, client_secret)
        if not client.validate_credentials():
            raise forms.ValidationError(
                'Provided Zoom API OAuth2 credentials seem to be invalid',
            )


# region oauth2-credentials
class ZoomBusinessOAuth2CredentialsInlineFormset(forms.BaseInlineFormSet):
    def clean(self):
        if hasattr(self, 'cleaned_data'):
            data = self.cleaned_data[0]
            validate_oauth2_credentials(data)
        return super().clean()


class ZoomBusinessOAuth2CredentialsInline(NoDelMixin, admin.TabularInline):
    model = ZoomBusinessOAuth2Credentials
    formset = ZoomBusinessOAuth2CredentialsInlineFormset
    verbose_name_plural = 'Zoom OAuth2 API Credentials'
    fields = (
        'account_id',
        'client_id',
        'client_secret',
        'allow_group_meetings_for_parallel_bookings',
    )
    readonly_fields = ('user_id',)


# endregion oauth2-credentials


class ZoomMeetingInline(NoAddDelMixin, admin.TabularInline):
    model = ZoomMeeting
    verbose_name_plural = 'Zoom meeting details'
    fields = ('zoom_id', 'appointment_id', 'join_url')
    readonly_fields = fields


# region jwt-credentials-admin
class ZoomBusinessCredentialsAdminForm(forms.ModelForm):
    def clean(self):
        cleaned_data = super().clean()
        api_key = cleaned_data.get('api_key')
        api_secret = cleaned_data.get('api_secret')
        if api_key and api_secret:
            credentials_valid = ZoomClient.validate_credentials(api_key, api_secret)
            if not credentials_valid:
                raise forms.ValidationError('Provided Zoom API credentials seem to be invalid')
        return cleaned_data


class ZoomBusinessCredentialsAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    model = ZoomBusinessCredentials
    verbose_name_plural = 'Zoom API Credentials'
    form = ZoomBusinessCredentialsAdminForm
    list_display = [
        'id',
        'business',
        'api_key',
        'api_secret',
        'user_id',
        'allow_group_meetings_for_parallel_bookings',
    ]
    search_fields = ('=id', '=business__id')
    readonly_fields = [
        'id',
        'created',
        'updated',
        'deleted',
        'business',
        'user_id',
    ]

    def get_readonly_fields(self, request, obj=None):
        ro_fields = list(super().get_readonly_fields(request, obj))
        if UseOAuth2ZoomClient():
            ro_fields.extend(
                ['api_key', 'api_secret', 'allow_group_meetings_for_parallel_bookings'],
            )
        return ro_fields


# endregion jwt-credentials-admin


# region oauth2-credentials-admin
class ZoomBusinessOAuth2CredentialsAdminForm(forms.ModelForm):
    def clean(self):
        cleaned_data = super().clean()
        validate_oauth2_credentials(cleaned_data)
        return cleaned_data


class ZoomBusinessOAuth2CredentialsAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    model = ZoomBusinessOAuth2Credentials
    list_display = (
        'id',
        'business',
        'account_id',
        'client_id',
        'client_secret',
        'user_id',
        'allow_group_meetings_for_parallel_bookings',
    )
    search_fields = ('=id', '=business__id')
    readonly_fields = (
        'id',
        'created',
        'updated',
        'deleted',
        'business',
        'user_id',
    )


# endregion oauth2-credentials-admin


class ZoomMeetingAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    model = ZoomMeeting
    list_display = [
        'id',
        'appointment_id',
        'business',
        'zoom_id',
        'join_url',
    ]
    search_fields = [
        '=id',
        '=business__id',
        '=appointment__id',
        '=zoom_id',
    ]
    readonly_fields = [
        'id',
        'created',
        'updated',
        'deleted',
        'appointment',
        'business',
        'zoom_id',
        'join_url',
        'start_url',  # requires call to Zoom Api, don't show on list view!
    ]


admin.site.register(ZoomBusinessCredentials, ZoomBusinessCredentialsAdmin)
admin.site.register(ZoomBusinessOAuth2Credentials, ZoomBusinessOAuth2CredentialsAdmin)
admin.site.register(ZoomMeeting, ZoomMeetingAdmin)
