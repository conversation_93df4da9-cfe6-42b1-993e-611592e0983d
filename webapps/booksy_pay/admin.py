from django.contrib import admin

from lib.admin_helpers import BaseModelAdmin, NoAddDelMixin
from webapps.pos.models import BPActivationSplashAdminProxy


class BPActivationSplashAdmin(NoAddDelMixin, BaseModelAdmin):
    search_fields = ['=business__id', '=operator__id']
    readonly_fields = ['business', 'operator', 'seen_at']
    list_display = ['id', 'business', 'operator', 'seen_at']


admin.site.register(BPActivationSplashAdminProxy, BPActivationSplashAdmin)
