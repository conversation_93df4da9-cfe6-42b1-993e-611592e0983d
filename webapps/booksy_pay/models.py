import typing as t
from django.conf import settings
from django.db import models
from django.db.models.query import QuerySet

from dateutil.relativedelta import relativedelta
from dirtyfields import DirtyFieldsMixin
from lib.models import ArchiveModel, AutoAddHistoryModel, HistoryModel
from lib.interval.fields import Interval<PERSON>ield
from webapps.billing.models import AutoUpdateAndAutoAddHistoryManager


class BooksyPaySettings(DirtyFieldsMixin, AutoAddHistoryModel, ArchiveModel):
    pos = models.OneToOneField(
        'pos.POS',
        on_delete=models.CASCADE,
        related_name='booksy_pay_settings',
    )
    enabled = models.BooleanField(
        default=False,
        null=False,
        help_text='Whether Booksy Pay is currently enabled',
    )
    allowed = models.BooleanField(
        default=False,
        null=False,
        help_text='Whether Booksy Pay is allowed to be used',
    )
    first_enabled_at = models.DateTimeField(
        null=True,
        blank=True,
    )
    late_cancellation_window = IntervalField(
        null=False,
        blank=False,
        default=relativedelta(**settings.BOOKSY_PAY_LATE_CANCELLATION_WINDOW_DEFAULT),
    )

    objects = AutoUpdateAndAutoAddHistoryManager()

    def __str__(self):
        return f'Booksy Pay Settings for POS {self.pos.id}'

    class Meta:
        verbose_name = 'Booksy Pay Settings'
        verbose_name_plural = 'Booksy Pay Settings'

        indexes = [
            models.Index(
                fields=['first_enabled_at'],
                name='first_enabled_at_not_null_idx',
                condition=models.Q(first_enabled_at__isnull=False),
            ),
        ]


class BooksyPaySettingsHistory(HistoryModel):
    model = models.ForeignKey(
        BooksyPaySettings,
        on_delete=models.DO_NOTHING,
        related_name='history',
    )

    @classmethod
    def extract_vars_from_instance(
        cls,
        obj: models.Model,
        fields: t.Optional[t.Iterable] = None,
    ) -> dict:
        _vars = super().extract_vars_from_instance(obj, fields)
        for key in _vars.keys():
            if isinstance(_vars[key], relativedelta):
                _vars[key] = repr(_vars[key])
        return _vars

    @classmethod
    def extract_vars_from_instances(  # pylint: disable=unnecessary-dict-index-lookup
        cls,
        instances: t.Union[QuerySet, list[int]],
        fields: t.Optional[t.Iterable] = None,
    ) -> dict[int, dict]:
        _vars = super().extract_vars_from_instances(instances, fields)
        for object_id, data in _vars.items():
            for field_name in data.keys():
                if isinstance(_vars[object_id][field_name], relativedelta):
                    _vars[object_id][field_name] = repr(_vars[object_id][field_name])
        return _vars

    class Meta:
        verbose_name = 'Booksy Pay Settings History'
        verbose_name_plural = 'Booksy Pay Settings History'

    def __str__(self):
        return f'Booksy Pay Settings History for POS {self.model.pos.id}'
