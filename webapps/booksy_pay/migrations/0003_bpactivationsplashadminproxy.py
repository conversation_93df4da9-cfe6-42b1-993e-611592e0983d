# Generated by Django 4.2.23 on 2025-08-08 10:43

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("pos", "0301_alter_paymentmethod_card_type_and_more"),
        ("booksy_pay", "0002_booksy_pay_settings_late_cancelation_add_default"),
    ]

    operations = [
        migrations.CreateModel(
            name="BPActivationSplashAdminProxy",
            fields=[],
            options={
                "verbose_name": "Booksy Pay Activation Splash",
                "verbose_name_plural": "Booksy Pay Activation Splashes",
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("pos.bpactivationsplash",),
        ),
    ]
