from datetime import datetime, timedelta
from decimal import Decimal

from django.db.models import Q, QuerySet
from django.utils.translation import gettext_lazy as _, pgettext_lazy

from lib.tools import sget
from webapps.booking.enums import AppointmentStatus
from webapps.booking.models import Appointment
from webapps.booksy_pay.consts import BP_AVAILABLE__DAYS_BEFORE_APPT
from webapps.booksy_pay.enums import BooksyPayGroupedStatus
from webapps.booksy_pay.types.settings import (
    BooksyPaySettingsDTO,
    BooksyPayStatusBadge,
)
from webapps.business.enums import PriceType
from webapps.business.models import Business
from webapps.pos.enums import PaymentTypeEnum, receipt_status
from webapps.pos.models import Transaction
from webapps.pos.tools import get_minimal_pba_amount
from webapps.pos.utils import is_call_for_status, is_no_show_protection_active
from webapps.user.models import User


def is_booksy_pay_eligible(  # pylint: disable=too-many-arguments,too-many-positional-arguments
    appointment_id: int,
    business_id: int,
    appointment_status: AppointmentStatus,
    appointment_service_variants_ids: list[int],
    is_appointment_paid: bool,
    is_appointment_prepaid: bool,
    is_paid_by_booksy_pay_: bool,
    booksy_pay_eligible: bool,
    appointment_total_type: PriceType,
    payment: 'Transaction',
    is_booksy_gift_card_appointment: bool,
    is_trusted_client: bool,
    appointment_total_value: Decimal | None = None,
) -> bool:
    chargeback_occurred = (
        appointment_id
        and payment
        and payment.latest_receipt.status_code in receipt_status.CHARGEBACK_STATUSES
    )

    if (  # pylint: disable=too-many-boolean-expressions
        not any(
            appointment_service_variants_ids
        )  # may happen that subbooking.service_variant_id is empty because of FLS-4683
        or appointment_status
        not in (
            Appointment.STATUS.ACCEPTED,
            Appointment.STATUS.FINISHED,
        )
        or appointment_total_type != PriceType.FIXED
        or not appointment_total_value
        or appointment_total_value < get_minimal_pba_amount()
        or is_paid_by_booksy_pay_
        or is_appointment_paid
        or is_appointment_prepaid
        or (
            is_trusted_client is False
            and is_no_show_protection_active(
                business_id=business_id,
                service_variants_ids=appointment_service_variants_ids,
            )
        )
        or not booksy_pay_eligible
        or chargeback_occurred
        or (appointment_id and is_call_for_status(appointment_id))
        or is_booksy_gift_card_appointment
    ):
        return False

    return True


def is_booksy_pay_payment_window_open(
    booked_from: datetime | None,
    booked_till: datetime | None,
    now: datetime,
    before: timedelta = timedelta(days=BP_AVAILABLE__DAYS_BEFORE_APPT),
    after: timedelta = timedelta(hours=2),
) -> bool:
    if not booked_from or not booked_till:
        return False

    if booked_from - before > now:
        return False

    if booked_till + after < now:
        return False

    return True


def get_booksy_pay_cashback_recipient(appointment: Appointment) -> User | None:
    """
    A customer who booked an appointment shall receive a Booksy Pay cashback.
    Therefore, to cover the Family & Friends case, it's important to also include
    the `booked_by` in the logic.
    """
    bci = appointment.booked_by if appointment.is_family_and_friends else appointment.booked_for

    if not bci or not bci.user_id:
        return

    return bci.user


# pylint: disable=too-many-arguments, too-many-positional-arguments
def has_finished_booksy_pay_transaction(
    appointment: Appointment,
    txn_min_total_value: Decimal | None = None,
    num_of_txns: int = 1,
    ignore_appointment_txn: bool = False,
    created_gte: datetime | None = None,
    created_lte: datetime | None = None,
) -> bool:
    """
    Checks if a customer has any Boosky Pay transactions already given the criteria.

    Args:
        appointment: the appointment object.
        txn_min_total_value: a minimum amount of value expected.
        num_of_txns: the number of required transactions.
        ignore_appointment_txn: allows to ignore a transaction related to a given appointment. It's
                                useful while determining the screen that shall be shown for the
                                after payment step (in this scenario, it's needed to know
                                the screen shown before payment).
        created_gte: transactions created from a given date (inclusive)
        created_lte: transactions created util a given date (inclusive)
    """
    if not (user := get_booksy_pay_cashback_recipient(appointment)):
        return False

    filter_params = {
        'transaction_type': Transaction.TRANSACTION_TYPE__PAYMENT,
        'latest_receipt__status_code__in': (
            receipt_status.BOOKSY_PAY_SUCCESS,
            receipt_status.PAYMENT_SUCCESS,
        ),
        'latest_receipt__payment_type__code': PaymentTypeEnum.BOOKSY_PAY,
        'appointment__status': Appointment.STATUS.FINISHED,
    }
    exclude_params = {}

    if txn_min_total_value:
        filter_params['total__gte'] = txn_min_total_value

    if created_gte:
        filter_params['created__gte'] = created_gte

    if created_lte:
        filter_params['created__lte'] = created_lte

    if ignore_appointment_txn:
        exclude_params['appointment'] = appointment

    if num_of_txns > 1:
        # It performs a LIMIT and OFFSET query, effectively checking
        # if there is a `num_of_txns`th record.
        return (
            user.transactions.filter(**filter_params)
            .exclude(**exclude_params)[num_of_txns - 1 : num_of_txns]
            .exists()
        )

    return user.transactions.filter(**filter_params).exclude(**exclude_params).exists()


def get_booksy_pay_transaction(appointment_id: int) -> QuerySet:
    return Transaction.objects.filter(
        Q(latest_receipt__status_code=receipt_status.BOOKSY_PAY_SUCCESS)
        | Q(
            latest_receipt__status_code=receipt_status.PAYMENT_SUCCESS,
            latest_receipt__payment_type__code=PaymentTypeEnum.BOOKSY_PAY,
        ),
        appointment_id=appointment_id,
        transaction_type=Transaction.TRANSACTION_TYPE__PAYMENT,
    )


booksy_pay_status_badge_map = {
    BooksyPayGroupedStatus.ACTIVE: BooksyPayStatusBadge(
        label=_('Active'),
        type='positive',
        leading_icon='tick_circle',
    ),
    BooksyPayGroupedStatus.NEW: BooksyPayStatusBadge(
        label=pgettext_lazy('booksy_pay_status_label', 'New'),
        type='sea',
    ),
    BooksyPayGroupedStatus.VERIFICATION_PENDING: BooksyPayStatusBadge(
        label=_('Verification pending'),
        type='warning',
        leading_icon='clock',
    ),
}


def get_booksy_pay_status_badge(
    business: Business,
    user: 'User',
    booksy_pay_settings: BooksyPaySettingsDTO,
) -> BooksyPayStatusBadge | None:
    if (
        business.status not in (Business.Status.PAID,)
        or user != business.owner
        or not booksy_pay_settings.allowed
    ):
        return

    if not booksy_pay_settings.enabled:
        return booksy_pay_status_badge_map[BooksyPayGroupedStatus.NEW]

    if not sget(business, ['pos', 'stripe_kyc_completed']):
        return booksy_pay_status_badge_map[BooksyPayGroupedStatus.VERIFICATION_PENDING]

    return booksy_pay_status_badge_map[BooksyPayGroupedStatus.ACTIVE]
