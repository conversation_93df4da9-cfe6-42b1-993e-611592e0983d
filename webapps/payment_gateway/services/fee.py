from dataclasses import asdict

from django.db import transaction

from lib.db import PAY<PERSON><PERSON>S_DB
from lib.payment_gateway.enums import (
    BalanceTransactionStatus,
    BalanceTransactionType,
    FeeType,
    WalletOwnerType,
)
from lib.payment_gateway.events import payment_gateway_balance_transaction_created_event
from lib.payments.enums import PaymentProviderCode
from webapps.payment_gateway.adapters import PaymentProvidersAdapter
from webapps.payment_gateway.consts import MINIMAL_TRANSFER_AMOUNT
from webapps.payment_gateway.exceptions import Invalid<PERSON>mount, InvalidSenderReceiver
from webapps.payment_gateway.models import BalanceTransaction, Fee, Wallet
from webapps.payment_gateway.services.balance_transaction import BalanceTransactionService


class FeeService:
    @staticmethod
    def _validate_payment_sender_and_receiver(sender: Wallet, receiver: Wallet, fee_type: FeeType):
        """
        checks if such sender-receiver combination is valid
        mainly business -> booksy allowed
        booksy -> business allowed in case of won chargeback case (chargeback reversal)
        """
        if fee_type == FeeType.FUNDS_REINSTATED:
            if not (
                sender.owner_type == WalletOwnerType.BOOKSY
                and receiver.owner_type == WalletOwnerType.BUSINESS
            ):
                raise InvalidSenderReceiver
            return

        # all other fees should be business -> booksy, validate accordingly
        valid_receivers = {
            # sender: [list of possible receivers]
            WalletOwnerType.BUSINESS: [WalletOwnerType.BOOKSY],
        }.get(sender.owner_type, [])
        if receiver.owner_type not in valid_receivers:
            raise InvalidSenderReceiver

    @staticmethod
    def _validate_fee_amount(amount: int):
        if amount < MINIMAL_TRANSFER_AMOUNT:
            raise InvalidAmount(amount)

    # pylint: disable=too-many-arguments, too-many-positional-arguments
    @staticmethod
    def initialize_fee(
        amount: int,
        fee_type: FeeType,
        sender: Wallet,
        receiver: Wallet,
        payment_provider_code: PaymentProviderCode,
        parent_balance_transaction: BalanceTransaction,
    ) -> BalanceTransaction:
        """
        Create the fee in an idempotent way (1 fee_type per balance transaction allowed)
        """
        FeeService._validate_payment_sender_and_receiver(
            sender=sender,
            receiver=receiver,
            fee_type=fee_type,
        )
        FeeService._validate_fee_amount(amount=amount)

        idempotency_key = f"{parent_balance_transaction.id}__{fee_type}"
        balance_transaction, created = BalanceTransaction.objects.get_or_create(
            idempotency_key=idempotency_key,
            defaults={
                "receiver": receiver,
                "sender": sender,
                "amount": amount,
                "status": BalanceTransactionStatus.PROCESSING,
                "payment_method": None,
                "payment_provider_code": payment_provider_code,
                "transaction_type": BalanceTransactionType.FEE,
                "external_id": None,  # will be filled later
                "payout": None,
                "parent_balance_transaction": parent_balance_transaction,
            },
        )
        if not created:
            return balance_transaction

        with transaction.atomic(using=PAYMENTS_DB):
            fee = Fee(
                balance_transaction=balance_transaction,
                fee_type=fee_type,
            )
            fee.save()
            BalanceTransactionService.save_to_history(balance_transaction)

        external_id = PaymentProvidersAdapter.initialize_transfer_fund(
            sender_wallet=sender,
            receiver_wallet=receiver,
            amount=amount,
            provider_code=payment_provider_code,
        ).id

        with transaction.atomic(using=PAYMENTS_DB):
            balance_transaction.external_id = external_id
            balance_transaction.save(update_fields=['external_id'])
            BalanceTransactionService.save_to_history(balance_transaction)

        payment_gateway_balance_transaction_created_event.send(asdict(balance_transaction.entity))

        if fee_type == FeeType.REFUND and payment_provider_code == PaymentProviderCode.STRIPE:
            PaymentProvidersAdapter.process_transfer_fund(
                transfer_fund_id=balance_transaction.external_id,
                refund_fee_metadata={
                    'FeeType': "refund_fee",
                },
            )
        else:
            PaymentProvidersAdapter.process_transfer_fund(
                transfer_fund_id=balance_transaction.external_id,
            )
        return balance_transaction
