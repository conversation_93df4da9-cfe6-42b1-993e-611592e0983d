from dataclasses import asdict
import datetime
from typing import Optional

from django.db import transaction
from django.db.models import Sum
from django.utils import timezone

from lib.db import PAYMENTS_DB
from lib.feature_flag.feature.payment import StripeCheckRefundOnlineBalance
from lib.payment_gateway.enums import (
    BalanceTransactionStatus,
    BalanceTransactionType,
    FeeType,
)
from lib.payment_gateway.events import payment_gateway_balance_transaction_created_event
from lib.payment_providers.entities import ProviderAccountOnlineBalance
from lib.payments.enums import RefundError
from lib.smartlock import SmartLock
from webapps.payment_gateway.adapters import PaymentProvidersAdapter
from webapps.payment_gateway.consts import MINIMAL_TRANSFER_AMOUNT, REFUND_MAX_DAYS
from webapps.payment_gateway.exceptions import InvalidAmount, OperationNotAllowed
from webapps.payment_gateway.models import BalanceTransaction, Refund
from webapps.payment_gateway.services.balance_transaction import BalanceTransactionService
from webapps.payment_gateway.services.wallet import WalletService
from webapps.payment_gateway.tasks import initialize_fee_task


class RefundService:
    @staticmethod
    def get_refundable_amount(balance_transaction: BalanceTransaction) -> int:
        if balance_transaction.status != BalanceTransactionStatus.SUCCESS:
            return 0

        already_refunded_amount = (
            balance_transaction.child_balance_transactions.filter(
                transaction_type=BalanceTransactionType.REFUND,
                status__in=[
                    BalanceTransactionStatus.PROCESSING,
                    BalanceTransactionStatus.SUCCESS,
                ],
            ).aggregate(Sum("amount"))['amount__sum']
            or 0
        )

        refundable_amount = balance_transaction.amount - already_refunded_amount

        return refundable_amount

    @staticmethod
    def is_refund_possible(  # pylint:disable=too-many-return-statements
        payment_balance_transaction: BalanceTransaction,
        amount: int,
    ) -> tuple[bool, Optional[RefundError]]:
        if payment_balance_transaction.transaction_type != BalanceTransactionType.PAYMENT:
            # sanity check
            raise OperationNotAllowed

        if amount <= 0:
            # sanity check
            raise InvalidAmount(amount)

        if payment_balance_transaction.status != BalanceTransactionStatus.SUCCESS:
            return False, RefundError.INVALID_PAYMENT_STATUS

        refundable_amount = RefundService.get_refundable_amount(payment_balance_transaction)
        if refundable_amount == 0:
            return False, RefundError.ALREADY_REFUNDED

        if amount > refundable_amount:
            return False, RefundError.INVALID_REFUND_AMOUNT

        if (
            payment_balance_transaction.created + datetime.timedelta(days=REFUND_MAX_DAYS)
            < timezone.now()
        ):  # if refund expiry date was earlier than now
            return False, RefundError.EXPIRED

        if StripeCheckRefundOnlineBalance():
            online_balance: ProviderAccountOnlineBalance = (
                PaymentProvidersAdapter.get_provider_account_online_balance(
                    account_holder_id=payment_balance_transaction.receiver.account_holder_id,
                    payment_provider_code=payment_balance_transaction.payment_provider_code,
                )
            )
            booksy_online_balance: ProviderAccountOnlineBalance = (
                PaymentProvidersAdapter.get_provider_account_online_balance(
                    account_holder_id=WalletService.get_booksy_wallet().account_holder_id,
                    payment_provider_code=payment_balance_transaction.payment_provider_code,
                )
            )
            if online_balance.available < amount and booksy_online_balance.available < amount:
                return False, RefundError.MISSING_BALANCE

        return True, None

    @staticmethod
    def initialize_refund(
        payment_balance_transaction: BalanceTransaction,
        amount: Optional[int] = None,
        metadata: dict[str, str] | None = None,
    ) -> BalanceTransaction:
        """
        Entrypoint for refund flow

        :param payment_balance_transaction: payment that will be refunded
        :param amount: if None, full available amount will be used
        :param metadata: values expected to be in bt metadata
        """
        if amount is None:
            amount = RefundService.get_refundable_amount(
                balance_transaction=payment_balance_transaction,
            )

        with SmartLock(key=str(payment_balance_transaction.id)):
            payment_balance_transaction.refresh_from_db()

            possible, error = RefundService.is_refund_possible(
                payment_balance_transaction=payment_balance_transaction,
                amount=amount,
            )
            if not possible:
                # this should not happen because is_refund_possible should be checked
                # before displaying a button for creating a refund
                if error in [RefundError.INVALID_REFUND_AMOUNT, RefundError.ALREADY_REFUNDED]:
                    raise InvalidAmount(error)
                raise OperationNotAllowed(error)

            refund_payment_operation_id = None

            with transaction.atomic(using=PAYMENTS_DB):
                balance_transaction = BalanceTransaction(
                    receiver=payment_balance_transaction.sender,
                    sender=payment_balance_transaction.receiver,
                    amount=amount,
                    status=BalanceTransactionStatus.PROCESSING,
                    payment_method=payment_balance_transaction.payment_method,
                    payment_provider_code=payment_balance_transaction.payment_provider_code,
                    transaction_type=BalanceTransactionType.REFUND,
                    external_id=refund_payment_operation_id,
                    payout=None,
                    parent_balance_transaction=payment_balance_transaction,
                    metadata=metadata or {},
                )
                balance_transaction.save()
                refund = Refund(
                    balance_transaction=balance_transaction,
                    reason="",  # todo (in phase 2)
                )
                refund.save()
                BalanceTransactionService.save_to_history(balance_transaction)

                with SmartLock(key=str(balance_transaction.id)):
                    refund_payment_operation = PaymentProvidersAdapter.initialize_refund(
                        payment_external_id=payment_balance_transaction.external_id,
                        amount=amount,
                    )

                    balance_transaction.external_id = refund_payment_operation.id
                    balance_transaction.save(update_fields=['external_id'])

            payment_gateway_balance_transaction_created_event.send(
                asdict(balance_transaction.entity)
            )
            return balance_transaction

    @staticmethod
    def update_refund(
        balance_transaction: BalanceTransaction,
        new_status: BalanceTransactionStatus,
    ):
        BalanceTransactionService.update_bt_status(
            balance_transaction=balance_transaction,
            new_status=new_status,
        )
        if new_status != BalanceTransactionStatus.SUCCESS:
            return

        RefundService.charge_fees(refund_balance_transaction=balance_transaction)

    @staticmethod
    def charge_fees(refund_balance_transaction: BalanceTransaction):
        payment_bt = refund_balance_transaction.parent_balance_transaction
        fee_amount = payment_bt.payment.refund_splits_entity.calculate_fee(
            amount=refund_balance_transaction.amount or payment_bt.amount,
        )
        if fee_amount < MINIMAL_TRANSFER_AMOUNT:
            return
        initialize_fee_task.delay(
            amount=fee_amount,
            fee_type=FeeType.REFUND,
            sender_id=refund_balance_transaction.sender.id,
            receiver_id=WalletService.get_booksy_wallet().id,
            payment_provider_code=refund_balance_transaction.payment_provider_code,
            parent_balance_transaction_id=refund_balance_transaction.id,
        )
