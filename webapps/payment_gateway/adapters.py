import uuid
from typing import Optional

from lib.payment_providers.entities import (
    AccountHolderEntity,
    AuthAdditionalDataEntity,
    AuthorizePaymentMethodDataEntity,
    CustomerEntity,
    PaymentClientTokenEntity,
    PaymentEntity,
    PaymentOperationEntity,
    PayoutEntity,
    PayoutDetailsEntity,
    TransferFundEntity,
)
from lib.payments.enums import PaymentProviderCode, PayoutType
from lib.point_of_sale.enums import PaymentMethodType
from webapps.payment_gateway.models import Wallet
from webapps.payment_providers.ports.account_holder_ports import PaymentProvidersAccountHolderPort
from webapps.payment_providers.ports.customer_ports import PaymentProvidersCustomerPort
from webapps.payment_providers.ports.payment_ports import PaymentProvidersPaymentPort

# pylint: disable=too-many-arguments, unused-argument


class PaymentProvidersAdapter:
    @staticmethod
    def initialize_payment(  # pylint: disable=too-many-positional-arguments
        receiver_wallet: Wallet,
        amount: int,
        fee_amount: int,
        payment_method: PaymentMethodType,
        provider_code: PaymentProviderCode,
        capture_automatically: bool,
        payment_token: str = None,
        sender_wallet: Optional[Wallet] = None,
        additional_data: Optional[dict] = None,
        metadata: Optional[dict] = None,
    ) -> PaymentEntity:
        response = PaymentProvidersPaymentPort.initialize_payment(
            account_holder_id=receiver_wallet.account_holder_id,
            payment_provider_code=provider_code,
            payment_method_type=payment_method,
            amount=amount,
            fee_amount=fee_amount,
            payment_token=payment_token,
            auto_capture=capture_automatically,
            customer_id=sender_wallet.customer_id if sender_wallet else None,
            additional_data=additional_data,
            metadata=metadata,
        )
        return response.entity

    @staticmethod
    def authorize_payment(
        payment_id: uuid.UUID,
        payment_method_data: AuthorizePaymentMethodDataEntity,
        additional_data: AuthAdditionalDataEntity | None,
        off_session: bool | None = None,
    ) -> PaymentEntity:
        response = PaymentProvidersPaymentPort.authorize_payment(
            payment_id=payment_id,
            payment_method_data=payment_method_data,
            additional_data=additional_data,
            off_session=off_session,
        )
        return response.entity

    @staticmethod
    def modify_payment(
        payment_id: uuid.UUID,
        fee_amount: int,
    ) -> PaymentEntity:
        response = PaymentProvidersPaymentPort.modify_payment(
            payment_id=payment_id,
            fee_amount=fee_amount,
        )
        return response.entity

    @staticmethod
    def capture_payment(
        payment_id: uuid.UUID,
    ) -> PaymentEntity:
        response = PaymentProvidersPaymentPort.capture_payment(
            payment_id=payment_id,
        )
        return response.entity

    @staticmethod
    def initialize_transfer_fund(
        sender_wallet: Wallet,
        receiver_wallet: Wallet,
        amount: int,
        provider_code: PaymentProviderCode,
    ) -> TransferFundEntity:
        # initializes a transfer fund within a payment provider system
        response = PaymentProvidersPaymentPort.initialize_transfer_fund(
            sender_account_holder_id=sender_wallet.account_holder_id,
            receiver_account_holder_id=receiver_wallet.account_holder_id,
            amount=amount,
            payment_provider_code=provider_code,
        )
        return response.entity

    @staticmethod
    def process_transfer_fund(
        transfer_fund_id: uuid.UUID,
        refund_fee_metadata: dict[str, str] = None,
        metadata: dict[str, str] | None = None,
    ):
        response = PaymentProvidersPaymentPort.process_transfer_fund(
            transfer_fund_id=transfer_fund_id,
            refund_fee_metadata=refund_fee_metadata,
            metadata=metadata,
        )
        return response.entity

    @staticmethod
    def get_payment_client_token(payment_id: uuid.UUID) -> PaymentClientTokenEntity:
        response = PaymentProvidersPaymentPort.get_payment_client_token(payment_id=payment_id)
        return response.entity

    @staticmethod
    def cancel_payment(payment_external_id: uuid.UUID) -> PaymentEntity:
        response = PaymentProvidersPaymentPort.cancel_payment(payment_id=payment_external_id)
        return response.entity

    @staticmethod
    def mark_payment_as_failed(payment_id: str, error_code: Optional[str] = None):
        response = PaymentProvidersPaymentPort.mark_payment_as_failed(
            payment_id=payment_id,
            error_code=error_code,
        )
        return response.entity

    @staticmethod
    def initialize_refund(
        payment_external_id: str,
        amount: int,
    ) -> PaymentOperationEntity:
        response = PaymentProvidersPaymentPort.send_for_refund(
            payment_id=payment_external_id,
            amount=amount,
        )
        return response.entity

    @staticmethod
    def get_available_payout_amount(
        account_holder_id: uuid.UUID,
        payment_provider_code: PaymentProviderCode,
    ) -> dict:
        response = PaymentProvidersPaymentPort.get_available_payout_amount(
            account_holder_id=account_holder_id,
            payment_provider_code=payment_provider_code,
        )
        return response.entity

    @staticmethod
    def get_provider_account_online_balance(
        account_holder_id: uuid.UUID,
        payment_provider_code: PaymentProviderCode,
    ):
        response = PaymentProvidersAccountHolderPort.get_provider_account_online_balance(
            account_holder_id=account_holder_id,
            payment_provider_code=payment_provider_code,
        )
        return response.entity

    @staticmethod
    def initialize_payout(
        account_holder_id: uuid.UUID,
        payment_provider_code: PaymentProviderCode,
        amount: int,
        payout_type: PayoutType,
    ) -> PayoutEntity:
        response = PaymentProvidersPaymentPort.initialize_payout(
            account_holder_id=account_holder_id,
            payment_provider_code=payment_provider_code,
            amount=amount,
            payout_type=payout_type,
        )
        return response.entity

    @staticmethod
    def get_payout_details(payout_id: uuid.UUID) -> PayoutDetailsEntity:
        response = PaymentProvidersPaymentPort.get_payout_details(payout_id=payout_id)
        return response.entity

    @staticmethod
    def create_common_account_holder(statement_name: str, business_id: int) -> AccountHolderEntity:
        response = PaymentProvidersAccountHolderPort.create_common_account_holder(
            statement_name=statement_name,
            metadata={
                "business_id": business_id,
            },
        )
        return response.entity

    @staticmethod
    def create_provider_account_holder(
        account_holder_id: uuid.UUID,
        payment_provider_code: PaymentProviderCode,
    ) -> None:
        PaymentProvidersAccountHolderPort.create_provider_account_holder(
            account_holder_id=account_holder_id,
            payment_provider_code=payment_provider_code,
        )

    @staticmethod
    def create_common_customer(email: str, name: str, phone: str, user_id: int) -> CustomerEntity:
        response = PaymentProvidersCustomerPort.create_common_customer(
            email=email,
            name=name,
            phone=phone,
            metadata={
                "user_id": user_id,
            },
        )
        return response.entity

    @staticmethod
    def update_common_customer(
        customer_id: uuid.UUID,
        email: str = None,
        name: str = None,
        phone: str = None,
        metadata: dict = None,
    ) -> CustomerEntity:
        response = PaymentProvidersCustomerPort.update_common_customer(
            customer_id=customer_id,
            email=email,
            name=name,
            phone=phone,
            metadata=metadata,
        )
        return response.entity

    @staticmethod
    def update_common_account_holder(
        account_holder_id: uuid.UUID,
        statement_name: str = None,
        metadata: dict = None,
    ) -> AccountHolderEntity:
        response = PaymentProvidersAccountHolderPort.update_common_account_holder(
            account_holder_id=account_holder_id,
            statement_name=statement_name,
            metadata=metadata,
        )
        return response.entity

    @staticmethod
    def get_payment(payment_id: uuid.UUID) -> PaymentEntity:
        return PaymentProvidersPaymentPort.get_payment(payment_id=payment_id).entity

    @staticmethod
    def get_customer_info(customer_id: uuid.UUID) -> CustomerEntity:
        return PaymentProvidersCustomerPort.get_customer_info(customer_id=customer_id).entity

    @staticmethod
    def get_account_holder_info(account_holder_id: uuid.UUID) -> AccountHolderEntity:
        return PaymentProvidersAccountHolderPort.get_account_holder_info(
            account_holder_id=account_holder_id
        ).entity
