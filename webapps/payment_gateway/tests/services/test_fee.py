import uuid

import pytest
from mock import patch
from mock.mock import MagicMock
from model_bakery import baker

from lib.payment_gateway.enums import FeeType, BalanceTransactionType
from lib.payment_providers.enums import TransferFundStatus
from lib.payment_providers.entities import TransferFundEntity
from lib.payments.enums import PaymentProviderCode

from webapps.payment_gateway.exceptions import InvalidSenderReceiver, InvalidAmount
from webapps.payment_gateway.models import BalanceTransaction
from webapps.payment_gateway.services.fee import FeeService
from webapps.payment_gateway.tests.services.base import BaseServiceTestCase
from webapps.payment_providers.services.stripe.stripe import (
    StripeTransferFundServices,
)
from webapps.stripe_integration.tests.mocks import (
    mock_stripe_transfer_create,
    mock_stripe_transfer_for_refund_fee_create,
)


@pytest.mark.django_db
class FeeServiceTests(BaseServiceTestCase):
    def test_initialize_fee__invalid_sender_receiver(self):
        with self.assertRaises(InvalidSenderReceiver):
            FeeService.initialize_fee(
                amount=10,
                fee_type=FeeType.REFUND,
                sender=self.booksy_wallet,
                receiver=self.biz_wallet,
                payment_provider_code=PaymentProviderCode.STRIPE,
                parent_balance_transaction=None,
            )

    def test_initialize_fee__invalid_amount(self):
        with self.assertRaises(InvalidAmount):
            FeeService.initialize_fee(
                amount=0,
                fee_type=FeeType.REFUND,
                sender=self.biz_wallet,
                receiver=self.booksy_wallet,
                payment_provider_code=PaymentProviderCode.STRIPE,
                parent_balance_transaction=None,
            )

    @patch('webapps.payment_gateway.actions.send_balance_transaction_message', MagicMock())
    @patch('webapps.payment_gateway.services.fee.PaymentProvidersAdapter.process_transfer_fund')
    @patch('webapps.payment_gateway.services.fee.PaymentProvidersAdapter.initialize_transfer_fund')
    def test_initialize_fee(self, initialize_transfer_fund_mock, process_transfer_fund_mock):
        initialize_transfer_fund_mock.side_effect = lambda **_: TransferFundEntity(
            id=uuid.uuid4(),
            amount=50,
            status=TransferFundStatus.PROCESSING,
        )
        stripe_dispute_bt = baker.make(
            BalanceTransaction,
            transaction_type=BalanceTransactionType.DISPUTE,
            payment_provider_code=PaymentProviderCode.STRIPE,
        )
        adyen_dispute_bt = baker.make(
            BalanceTransaction,
            transaction_type=BalanceTransactionType.DISPUTE,
            payment_provider_code=PaymentProviderCode.ADYEN,
        )
        adyen_refund_bt = baker.make(
            BalanceTransaction,
            transaction_type=BalanceTransactionType.REFUND,
            payment_provider_code=PaymentProviderCode.ADYEN,
        )

        # check standard-flow creation
        bt1 = FeeService.initialize_fee(
            amount=50,
            fee_type=FeeType.DISPUTE,
            sender=self.biz_wallet,
            receiver=self.booksy_wallet,
            payment_provider_code=PaymentProviderCode.STRIPE,
            parent_balance_transaction=stripe_dispute_bt,
        )
        self.assertEqual(initialize_transfer_fund_mock.call_count, 1)
        self.assertEqual(stripe_dispute_bt.child_balance_transactions.count(), 1)

        # check idempotency
        bt2 = FeeService.initialize_fee(
            amount=50,
            fee_type=FeeType.DISPUTE,
            sender=self.biz_wallet,
            receiver=self.booksy_wallet,
            payment_provider_code=PaymentProviderCode.STRIPE,
            parent_balance_transaction=stripe_dispute_bt,
        )
        self.assertEqual(initialize_transfer_fund_mock.call_count, 1)
        self.assertEqual(bt2.id, bt1.id)
        self.assertEqual(stripe_dispute_bt.child_balance_transactions.count(), 1)

        # check if it's possible to assign different fee types to 1 bt
        bt3 = FeeService.initialize_fee(
            amount=50,
            fee_type=FeeType.PAYMENT_REVERSAL,
            sender=self.biz_wallet,
            receiver=self.booksy_wallet,
            payment_provider_code=PaymentProviderCode.STRIPE,
            parent_balance_transaction=stripe_dispute_bt,
        )
        self.assertEqual(initialize_transfer_fund_mock.call_count, 2)
        self.assertNotEqual(bt3.id, bt1.id)
        self.assertEqual(stripe_dispute_bt.child_balance_transactions.count(), 2)

        # check transfer code assignment
        bt4 = FeeService.initialize_fee(
            amount=60,
            fee_type=FeeType.REFUND,
            sender=self.biz_wallet,
            receiver=self.booksy_wallet,
            payment_provider_code=PaymentProviderCode.ADYEN,
            parent_balance_transaction=adyen_refund_bt,
        )
        self.assertEqual(initialize_transfer_fund_mock.call_count, 3)
        initialize_transfer_fund_mock.assert_called_with(
            sender_wallet=self.biz_wallet,
            receiver_wallet=self.booksy_wallet,
            amount=60,
            provider_code=PaymentProviderCode.ADYEN,
        )
        process_transfer_fund_mock.assert_called_with(
            transfer_fund_id=bt4.external_id,
        )
        bt5 = FeeService.initialize_fee(
            amount=70,
            fee_type=FeeType.DISPUTE,
            sender=self.biz_wallet,
            receiver=self.booksy_wallet,
            payment_provider_code=PaymentProviderCode.ADYEN,
            parent_balance_transaction=adyen_dispute_bt,
        )
        self.assertEqual(initialize_transfer_fund_mock.call_count, 4)
        initialize_transfer_fund_mock.assert_called_with(
            sender_wallet=self.biz_wallet,
            receiver_wallet=self.booksy_wallet,
            amount=70,
            provider_code=PaymentProviderCode.ADYEN,
        )
        process_transfer_fund_mock.assert_called_with(
            transfer_fund_id=bt5.external_id,
        )

    @patch(
        'webapps.payment_providers.services.common.CommonTransferFundServices._get_service_class',
        MagicMock(return_value=StripeTransferFundServices),
    )
    @patch(
        'webapps.payment_providers.services.common.CommonTransferFundServices.update_status',
        MagicMock(),
    )
    @patch('webapps.payment_gateway.actions.send_balance_transaction_message', MagicMock())
    @patch('webapps.payment_providers.models.TransferFund.objects.get', MagicMock())
    @patch('webapps.payment_gateway.services.fee.PaymentProvidersAdapter.initialize_transfer_fund')
    @patch('stripe.Charge.modify')
    @mock_stripe_transfer_for_refund_fee_create
    def test_stripe_send_metadata_refund_fee(
        self,
        create_transfer_mock,
        modify_charge,
        initialize_transfer_fund_mock,
    ):
        initialize_transfer_fund_mock.side_effect = lambda **_: TransferFundEntity(
            id=uuid.uuid4(),
            amount=50,
            status=TransferFundStatus.PROCESSING,
        )

        stripe_dispute_bt = baker.make(
            BalanceTransaction,
            transaction_type=BalanceTransactionType.DISPUTE,
            payment_provider_code=PaymentProviderCode.STRIPE,
        )

        FeeService.initialize_fee(
            amount=50,
            fee_type=FeeType.REFUND,
            sender=self.biz_wallet,
            receiver=self.booksy_wallet,
            payment_provider_code=PaymentProviderCode.STRIPE,
            parent_balance_transaction=stripe_dispute_bt,
        )
        modify_charge.assert_called_with('py_2412516541', metadata={"FeeType": "refund_fee"})

        @patch(
            'webapps.payment_providers.services.common.CommonTransferFundServices._get_service_class',  # pylint: disable=line-too-long
            MagicMock(return_value=StripeTransferFundServices),
        )
        @patch(
            'webapps.payment_providers.services.common.CommonTransferFundServices.update_status',
            MagicMock(),
        )
        @patch('webapps.payment_gateway.actions.send_balance_transaction_message', MagicMock())
        @patch('webapps.payment_providers.models.TransferFund.objects.get', MagicMock())
        @patch(
            'webapps.payment_gateway.services.fee.PaymentProvidersAdapter.initialize_transfer_fund'
        )
        @patch('stripe.Charge.modify')
        @mock_stripe_transfer_for_refund_fee_create
        def test_stripe_send_metadata_refund_fee_flag_off(
            self,
            create_transfer_mock,
            modify_charge,
            initialize_transfer_fund_mock,
        ):
            initialize_transfer_fund_mock.side_effect = lambda **_: TransferFundEntity(
                id=uuid.uuid4(),
                amount=50,
                status=TransferFundStatus.PROCESSING,
            )

            stripe_dispute_bt = baker.make(
                BalanceTransaction,
                transaction_type=BalanceTransactionType.DISPUTE,
                payment_provider_code=PaymentProviderCode.STRIPE,
            )

            FeeService.initialize_fee(
                amount=50,
                fee_type=FeeType.REFUND,
                sender=self.biz_wallet,
                receiver=self.booksy_wallet,
                payment_provider_code=PaymentProviderCode.STRIPE,
                parent_balance_transaction=stripe_dispute_bt,
            )
            modify_charge.assert_not_called()

        @patch(
            'webapps.payment_providers.services.common.CommonTransferFundServices._get_service_class',  # pylint: disable=line-too-long
            MagicMock(return_value=StripeTransferFundServices),
        )
        @patch(
            'webapps.payment_providers.services.common.CommonTransferFundServices.update_status',
            MagicMock(),
        )
        @patch('webapps.payment_gateway.actions.send_balance_transaction_message', MagicMock())
        @patch('webapps.payment_providers.models.TransferFund.objects.get', MagicMock())
        @patch(
            'webapps.payment_gateway.services.fee.PaymentProvidersAdapter.initialize_transfer_fund'
        )
        @patch('stripe.Charge.modify')
        @mock_stripe_transfer_create
        def test_stripe_send_metadata_refund_fee_stripe_dont_send_destination_payment(
            self,
            create_transfer_mock,
            modify_charge,
            initialize_transfer_fund_mock,
        ):
            initialize_transfer_fund_mock.side_effect = lambda **_: TransferFundEntity(
                id=uuid.uuid4(),
                amount=50,
                status=TransferFundStatus.PROCESSING,
            )

            stripe_dispute_bt = baker.make(
                BalanceTransaction,
                transaction_type=BalanceTransactionType.DISPUTE,
                payment_provider_code=PaymentProviderCode.STRIPE,
            )

            FeeService.initialize_fee(
                amount=50,
                fee_type=FeeType.REFUND,
                sender=self.biz_wallet,
                receiver=self.booksy_wallet,
                payment_provider_code=PaymentProviderCode.STRIPE,
                parent_balance_transaction=stripe_dispute_bt,
            )
            modify_charge.assert_not_called()
