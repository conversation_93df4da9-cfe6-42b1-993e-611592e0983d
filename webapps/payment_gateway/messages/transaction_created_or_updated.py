import uuid
from dataclasses import asdict
from dataclasses import dataclass, field
from datetime import datetime
from typing import List

from dacite import from_dict
from dataclasses_avroschema import AvroModel

from lib.payment_gateway.enums import (
    BalanceTransactionStatus,
    BalanceTransactionType,
    DisputeType,
    FeeType,
    PaymentMethodType,
    PaymentStatus,
    WalletOwnerType,
    TransferFundOrigin,
)
from lib.payments.enums import (
    PaymentError,
    PaymentProviderCode,
    PayoutStatus,
    PayoutType,
    PayoutError,
)
from lib.point_of_sale.enums import (
    BasketPaymentSource,
)
from lib.tools import sget_v2
from webapps.booking.ports import AppointmentPort
from webapps.payment_gateway.models import BalanceTransaction
from webapps.payment_providers.ports.payment_ports import PaymentProvidersPaymentPort
from webapps.point_of_sale.ports import BasketPaymentPort, RelatedBasketItemPort
from webapps.pos.ports import TransactionPort


@dataclass
class PaymentDetails(AvroModel):
    status: PaymentStatus
    error_code: PaymentError | None
    capture_date: datetime | None


@dataclass
class RefundDetails(AvroModel):
    reason: str | None


@dataclass
class DisputeDetails(AvroModel):
    type: DisputeType


@dataclass
class PayoutDetails(AvroModel):
    payout_type: PayoutType
    status: PayoutStatus
    expected_arrival_date: datetime | None
    error_code: PayoutError | None


@dataclass
class FeeDetails(AvroModel):
    fee_type: FeeType


@dataclass
class TransferFundDetails(AvroModel):
    origin: TransferFundOrigin


@dataclass
class Wallet(AvroModel):
    statement_name: str
    owner_type: WalletOwnerType
    business_id: int | None = None
    user_id: int | None = None


@dataclass
class Staffer(AvroModel):
    id: int


@dataclass
class TransactionCreatedOrUpdatedEvent(AvroModel):  # pylint: disable=too-many-instance-attributes
    id: uuid.UUID
    created: datetime | None
    updated: datetime | None
    receiver: Wallet | None  # None for payout
    sender: Wallet
    amount: int
    fee_amount: int
    status: BalanceTransactionStatus
    payment_method: PaymentMethodType | None  # None for payout
    payment_provider_code: PaymentProviderCode
    transaction_type: BalanceTransactionType
    parent_balance_transaction_id: uuid.UUID | None

    payment: PaymentDetails | None
    refund: RefundDetails | None
    dispute: DisputeDetails | None
    payout: PayoutDetails | None
    fee: FeeDetails | None
    transfer_fund: TransferFundDetails | None

    source: BasketPaymentSource | None
    operator_id: int | None

    transaction_id: int | None  # old POS
    appointment_id: int | None
    staffers: List[Staffer] = field(default_factory=list)

    provider_external_id: str | None = None


@dataclass
class TransactionCreatedOrUpdatedEventKey(
    AvroModel
):  # pylint: disable=too-many-instance-attributes
    business_id: int | None
    balance_transaction_id: uuid.UUID


def construct_transaction_created_or_updated_event_payload(
    balance_transaction_id: uuid.UUID,
) -> TransactionCreatedOrUpdatedEvent:
    balance_transaction = BalanceTransaction.objects.get(id=balance_transaction_id)
    provider_external_id = None
    if (
        balance_transaction.transaction_type == BalanceTransactionType.TRANSFER_FUND
        and balance_transaction.payment_provider_code == PaymentProviderCode.STRIPE
    ):
        transfer_fund = PaymentProvidersPaymentPort.get_transfer_fund(
            balance_transaction.external_id
        )
        provider_external_id = sget_v2(transfer_fund, ['entity', 'provider_external_id'])
    basket_payment = BasketPaymentPort.get_basket_payment_entity(
        balance_transaction_id=balance_transaction.id
    )
    transaction_entity = (
        TransactionPort.get_transaction(basket_payment_id=basket_payment.id)
        if basket_payment
        else None
    )
    transaction_id = transaction_entity.id if transaction_entity else None
    appointment_id = (
        RelatedBasketItemPort.get_appointment_id_for_basket(basket_payment.basket_id)
        if basket_payment
        else None
    )

    if appointment_id:
        staffers_ids = AppointmentPort.get_appointment_staffers_ids(appointment_id) or []
    elif transaction_id:
        staffers_ids = TransactionPort.get_transaction_commission_staffers_id(transaction_id)
    else:
        staffers_ids = []

    additional_data = {
        'updated': balance_transaction.updated,
        'receiver': (
            from_dict(data_class=Wallet, data=asdict(balance_transaction.receiver.entity))
            if balance_transaction.receiver
            else None
        ),
        'sender': from_dict(data_class=Wallet, data=asdict(balance_transaction.sender.entity)),
        'transaction_id': transaction_id,
        'source': basket_payment.source if basket_payment else None,
        'operator_id': basket_payment.operator_id if basket_payment else None,
        'appointment_id': appointment_id,
        'provider_external_id': provider_external_id,
        'staffers': [{'id': staffer_id} for staffer_id in staffers_ids],
    }
    return from_dict(
        data_class=TransactionCreatedOrUpdatedEvent,
        data=asdict(balance_transaction.entity) | additional_data,
    )
