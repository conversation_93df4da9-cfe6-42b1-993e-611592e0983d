from django.contrib import (
    admin,
    messages,
)
from django.http.response import HttpResponseRedirect

from lib.admin_helpers import BaseModelAdmin

from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin
from webapps.c2b_referral import models
from webapps.c2b_referral.models import RewardC2B
from webapps.user.groups import GroupNameV2
from webapps.user.tools import get_user_from_django_request


def status_granted_to_received(modeladmin, request, queryset):
    filtered_reward_qs = queryset.filter(status=RewardC2B.STATUS_GRANTED)
    for reward in filtered_reward_qs:
        reward.status = RewardC2B.STATUS_RECEIVED
        reward.save()


status_granted_to_received.short_description = (
    "Mark selected C2B Reward with status 'Granted' as 'Received'"
)


class RewardC2BAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    read_only_access_groups = ()
    change_form_template = 'admin/change_forms/change_form__rewardc2b.html'
    list_display = [
        'id',
        'user',
        'business',
        'status',
        'required_paying_months',
        'end_date',
        'overdue',
    ]
    list_filter = ['status', 'overdue']
    search_fields = [
        '=business__id',
        '=business__name',
        '=business__owner__email',
        '=reward',
        '=user__email',
        '=user__first_name',
        '=user__id',
        '=user__last_name',
    ]
    raw_id_fields = ['business', 'user']
    actions = [status_granted_to_received]

    def has_add_permission(self, request, obj=None):
        return False

    @staticmethod
    def is_eligible_user(request):
        user = get_user_from_django_request(request)
        if user is None:
            return False
        emails = {
            '<EMAIL>',
            '<EMAIL>',
        }
        return user.is_superuser or user.email in emails

    def get_readonly_fields(self, request, obj=None):
        fields = super(RewardC2BAdmin, self).get_readonly_fields(request, obj)
        if not self.is_eligible_user(request):
            fields = fields + ('status', 'end_date')

        return fields

    def response_change(self, request, obj):
        if "_status_to_received" in request.POST:
            if obj.status == RewardC2B.STATUS_GRANTED:
                obj.status = RewardC2B.STATUS_RECEIVED
                obj.save(update_fields=['status'])

                self.message_user(
                    request,
                    "Status has changed from 'Granted' to 'Received'",
                )
                return HttpResponseRedirect(".")
            else:
                messages.error(
                    request,
                    "You can only change Rewards with status 'Granted'!",
                )
                return HttpResponseRedirect(".")
        return super(RewardC2BAdmin, self).response_change(request, obj)


admin.site.register(models.RewardC2B, RewardC2BAdmin)
