import pytest
import responses
from django.conf import settings
from django.test import override_settings
from mock import patch
from model_bakery import baker

from lib.feature_flag.feature import MarketPayB2BReferralSettingEnabledFlag
from lib.tests.utils import override_feature_flag

from webapps.admin_extra.forms.payout import (
    B2BReferralRewardPayOutForm,
)

from webapps.b2b_referral.models import B2BReferral, B2BReferralReward
from webapps.b2b_referral.tests.test_b2breferral import prepare_data
from webapps.market_pay.models import ManualPayout
from webapps.market_pay.tasks import send_manual_reward_payout_task
from webapps.b2b_referral.enums import B2BReferralStatus, B2BRewardStatus


def mock_200():
    responses.add(
        responses.POST, settings.ADYEN_MANUAL_PAY_OUT_URL, json={'podaje haslo': 'okon'}, status=200
    )


def mock_422():
    responses.add(
        responses.POST, settings.ADYEN_MANUAL_PAY_OUT_URL, json={'podaje haslo': 'kura'}, status=422
    )


@pytest.mark.django_db
@override_settings(MARKET_PAY_ENABLED=False)
def test_create_reward_without_kyc():
    """
    Creating reward when KYC is disabled.
    Result: B2BReferralReward object are created, Prize objects are not created,
    """
    _business, _business2, _setting, deal = prepare_data(True)

    deal.mark_as_currently_on_trial()
    deal.mark_as_pending_reward()
    deal.refresh_from_db()

    assert deal.status == B2BReferralStatus.PENDING_REWARD
    assert deal.invited_reward.amount == 10
    assert not deal.invited_reward.prizes.exists()

    assert deal.referrer_reward.amount == 10
    assert not deal.referrer_reward.prizes.exists()


@pytest.mark.django_db
@pytest.mark.parametrize(
    'override_setting, override_ff',
    [
        ({'MARKET_PAY_ENABLED': True}, {MarketPayB2BReferralSettingEnabledFlag: False}),
        ({'MARKET_PAY_B2B_REFERRAL_ENABLED': True}, {MarketPayB2BReferralSettingEnabledFlag: True}),
    ],
)
def test_create_reward_with_kyc(override_setting, override_ff):
    """
    Creating reward when KYC is enabled.
    Result: B2BReferralReward and Prize objects are created,
    """
    with override_settings(**override_setting):
        with override_feature_flag(override_ff):
            business, business2, _setting, deal = prepare_data(True)

            deal.mark_as_currently_on_trial()
            deal.mark_as_pending_reward()
            deal.refresh_from_db()

            assert deal.status == B2BReferralStatus.PENDING_REWARD

            assert deal.referrer_reward.amount == 10
            referrer_prize = deal.referrer_reward.prizes.get()
            assert referrer_prize.amount == 1000  # minor unit
            assert referrer_prize.settled is False
            assert referrer_prize.pos == business.pos

            assert deal.invited_reward.amount == 10
            invited_prize = deal.invited_reward.prizes.get()
            assert invited_prize.amount == 1000  # minor unit
            assert invited_prize.settled is False
            assert invited_prize.pos == business2.pos


@pytest.mark.django_db
@pytest.mark.parametrize(
    'override_setting, override_ff',
    [
        ({'MARKET_PAY_ENABLED': True}, {MarketPayB2BReferralSettingEnabledFlag: False}),
        ({'MARKET_PAY_B2B_REFERRAL_ENABLED': True}, {MarketPayB2BReferralSettingEnabledFlag: True}),
    ],
)
def test_create_reward_with_kyc_invited_auto_payout(override_setting, override_ff):
    """
    Creating reward when KYC is enabled, but auto_payout is disabled.
    Result: B2BReferralReward objects are created, Prize objects are not created
    """
    with override_settings(**override_setting):
        with override_feature_flag(override_ff):
            _business, business2, _setting, deal = prepare_data(
                both=True, referrer_auto_payout=False, invited_auto_payout=True
            )

            deal.mark_as_currently_on_trial()
            deal.mark_as_pending_reward()
            deal.refresh_from_db()

            assert deal.status == B2BReferralStatus.PENDING_REWARD
            assert deal.invited_reward.amount == 10
            invited_prize = deal.invited_reward.prizes.get()
            assert invited_prize.amount == 1000  # minor unit
            assert invited_prize.settled is False
            assert invited_prize.pos == business2.pos

            assert deal.referrer_reward.amount == 10
            assert not deal.referrer_reward.prizes.exists()


@pytest.mark.django_db
@pytest.mark.parametrize(
    'override_setting, override_ff',
    [
        ({'MARKET_PAY_ENABLED': True}, {MarketPayB2BReferralSettingEnabledFlag: False}),
        ({'MARKET_PAY_B2B_REFERRAL_ENABLED': True}, {MarketPayB2BReferralSettingEnabledFlag: True}),
    ],
)
def test_create_reward_with_kyc_referrer_auto_payout(override_setting, override_ff):
    """
    Creating reward when KYC is enabled, but auto_payout is disabled.
    Result: B2BReferralReward objects are created, Prize objects are not created
    """
    with override_settings(**override_setting):
        with override_feature_flag(override_ff):
            business, _business2, _setting, deal = prepare_data(
                both=True, referrer_auto_payout=True, invited_auto_payout=False
            )

            deal.mark_as_currently_on_trial()
            deal.mark_as_pending_reward()
            deal.refresh_from_db()

            assert deal.status == B2BReferralStatus.PENDING_REWARD
            assert deal.invited_reward.amount == 10
            assert not deal.invited_reward.prizes.exists()

            assert deal.referrer_reward.amount == 10
            referrer_prize = deal.referrer_reward.prizes.get()
            assert referrer_prize.amount == 1000  # minor unit
            assert referrer_prize.settled is False
            assert referrer_prize.pos == business.pos


@responses.activate
@pytest.mark.django_db
@override_settings(MARKET_PAY_ENABLED=True)
@override_settings(ADYEN_ENABLED=True)
def test_manual_pay_out_referrer():
    """
    Tests if reward changes its status during manual payout.
    Result: Reward is in REWARDED_MANUALLY state. Deal is in PAID state.
    """
    mock_200()
    _business, _business2, _setting, deal = prepare_data(True)
    deal.mark_as_currently_on_trial()
    deal.mark_as_pending_reward()

    send_manual_reward_payout_task.run(
        referral_reward_id=deal.referrer_reward.id,
        bic=123,
        iban=123,
        owner_name='Krzysiu',
    )
    payout = ManualPayout.objects.get()
    deal.refresh_from_db()

    assert payout.response == {'body': {'podaje haslo': 'okon'}, 'status_code': 200}
    assert payout.referral_reward.status == B2BRewardStatus.REWARDED_MANUALLY
    assert deal.status == B2BReferralStatus.PAID


@responses.activate
@pytest.mark.django_db
@override_settings(MARKET_PAY_ENABLED=True)
@override_settings(ADYEN_ENABLED=True)
def test_manual_pay_out_invited():
    """
    Tests if reward changes its status during manual payout.
    Result: Reward is in REWARDED_MANUALLY state. Deal is in PENDING_REWARD
    state.
    """
    mock_200()
    _business, _business2, _setting, deal = prepare_data(True)
    deal.mark_as_currently_on_trial()
    deal.mark_as_pending_reward()

    send_manual_reward_payout_task.run(
        referral_reward_id=deal.invited_reward.id,
        bic=123,
        iban=123,
        owner_name='Krzysiu',
    )
    payout = ManualPayout.objects.get()
    payout.referral_reward.refresh_from_db()
    deal.refresh_from_db()

    assert payout.response == {'body': {'podaje haslo': 'okon'}, 'status_code': 200}
    assert payout.referral_reward.status == B2BRewardStatus.REWARDED_MANUALLY
    assert deal.status == B2BReferralStatus.PENDING_REWARD


@responses.activate
@pytest.mark.django_db
@override_settings(MARKET_PAY_ENABLED=True)
@override_settings(ADYEN_ENABLED=True)
def test_manual_pay_out_failed():
    """
    Tests if reward changes its status during manual payout.
    Result: Reward is in READY state. Deal is in PENDING_REWARD
    state.
    """
    mock_422()
    _business, _business2, _setting, deal = prepare_data(True)
    deal.mark_as_currently_on_trial()
    deal.mark_as_pending_reward()

    send_manual_reward_payout_task.run(
        referral_reward_id=deal.referrer_reward.id,
        bic=123,
        iban=123,
        owner_name='Krzysiu',
    )
    payout = ManualPayout.objects.get()
    deal.refresh_from_db()

    assert payout.response == {'body': {'podaje haslo': 'kura'}, 'status_code': 422}
    assert payout.referral_reward.status == B2BRewardStatus.READY
    assert deal.status == B2BReferralStatus.PENDING_REWARD


@pytest.mark.django_db
@override_settings(ADYEN_ENABLED=True)
@pytest.mark.parametrize(
    'override_setting, override_ff',
    [
        ({'MARKET_PAY_ENABLED': True}, {MarketPayB2BReferralSettingEnabledFlag: False}),
        ({'MARKET_PAY_B2B_REFERRAL_ENABLED': True}, {MarketPayB2BReferralSettingEnabledFlag: True}),
    ],
)
def test_disable_prizes(override_setting, override_ff):
    """
    Before manual flow all Prizes should be disabled.
    """
    with override_settings(**override_setting):
        with override_feature_flag(override_ff):
            _business, _business2, _setting, deal = prepare_data()
            deal.mark_as_currently_on_trial()
            deal.mark_as_pending_reward()

            prize = deal.referrer_reward.prizes.get()
            assert prize.settled is False

            with patch('webapps.market_pay.models.ManualPayout.send'):
                send_manual_reward_payout_task.run(
                    referral_reward_id=deal.referrer_reward.id,
                    bic=123,
                    iban=123,
                    owner_name='Krzysiu',
                )

            prize.refresh_from_db()
            assert prize.settled is True


def check_form(form, form_data):
    form = form(data=form_data)
    form.is_valid()

    print(form.errors)

    assert form.is_valid() is True

    send_manual_reward_payout_task.run(**form.cleaned_data)


@pytest.mark.django_db
@override_settings(API_COUNTRY='gb')
@patch('webapps.market_pay.models.ManualPayout.create_referral_reward_payout')
def test_regular_form(create_handler_mock):
    reward = baker.make(
        B2BReferralReward,
        referral=baker.make(B2BReferral),
        status=B2BRewardStatus.READY,
    )

    form_data = dict(
        referral_reward_id=reward.id,
        bic=123456789,
        iban=1234,
        owner_name='Krzysiu',
    )

    check_form(B2BReferralRewardPayOutForm, form_data)
    assert create_handler_mock.call_count == 1


@pytest.mark.django_db
@override_settings(API_COUNTRY='us')
def test_regular_us_form():
    reward = baker.make(
        B2BReferralReward,
        referral=baker.make(B2BReferral),
        status=B2BRewardStatus.READY,
    )

    form_data = dict(
        referral_reward_id=reward.id,
        bic=123456789,
        iban=1234,
        owner_name='Krzysiu',
    )

    form = B2BReferralRewardPayOutForm(data=form_data)
    form.is_valid()

    print(form.errors)

    assert form.is_valid() is False
