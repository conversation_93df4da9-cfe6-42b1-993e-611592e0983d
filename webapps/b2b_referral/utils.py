import logging

from django.conf import settings

from lib import jinja_renderer
from webapps.b2b_referral.enums import (
    B2BReferralStatus,
)
from webapps.b2b_referral.models import (
    B2BReferral,
    B2BReferralSetting,
)
from webapps.business.models import Business

logger = logging.getLogger('booksy.b2b_referral')


def get_terms():
    """
    Get b2b referral terms and conditions for current API_COUNTRY.
    :return: referral terms (formatting: newline as '/n')
    """

    renderer = jinja_renderer.JinjaRenderer()

    return renderer.renderer.load(
        f'b2b_referral/terms_{settings.API_COUNTRY}.txt',
    ).generate()


def mark_referral_deal_as_currently_on_trial(business_id: int):
    """
    Check if selected business has any B2BReferral deal active.
    If yes method marks it as CURRENTLY_ON_TRIAL.
    """
    referral = B2BReferral.objects.filter(
        invited_id=business_id,
        status=B2BReferralStatus.INVITED,
    )

    if len(referral) != 1:
        logger.info('Something went wrong. Business should be invited exactly one time.')
        return

    referral = referral.first()
    referral.mark_as_currently_on_trial()

    # Create single business setting for invited business if it has none.
    setting_for_invited = B2BReferralSetting.get_setting(referral.invited)
    if not setting_for_invited:
        setting_for_referrer = B2BReferralSetting.get_setting(referral.referrer)

        # Not all settings are allowed to replicate
        if setting_for_referrer.ambassador:
            return

        copy_into_single_business_setting(setting_for_referrer, referral.invited)


def mark_referral_deal_as_pending_reward(business_id: int):
    """
    Check if selected business has any B2BReferral deal active.
    If yes method marks it as PENDING_REWARD.
    """

    referral = B2BReferral.objects.filter(
        invited_id=business_id,
        status=B2BReferralStatus.CURRENTLY_ON_TRIAL,
    )

    if len(referral) != 1:
        logger.info('Something went wrong. Business should be invited exactly one time.')
        return

    referral = referral.first()
    referral.mark_as_pending_reward()


def copy_into_single_business_setting(setting: B2BReferralSetting, business: Business):
    """
    When freshly invited business has no setting create one for him.
    Copies deal from referrer and create single business setting. (#61741)
    """

    setting.id = None
    setting.region_id = None
    setting.series_id = None
    setting.business_id = business.id
    setting.save()
