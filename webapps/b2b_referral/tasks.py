from lib.celery_tools import post_transaction_task


@post_transaction_task
def create_invite_notification_for_setting_task(b2b_referral_setting_id: int):
    """Creates invite notification for all business affected by it.

    :param b2b_referral_setting_id: id of B2BReferralSetting object.
    """
    from webapps.b2b_referral.models import B2BReferralSetting
    from webapps.pop_up_notification.models import (
        B2BReferralNotification,
    )

    setting = B2BReferralSetting.objects.get(id=b2b_referral_setting_id)

    if not setting.referral_enabled:
        return {"created": 0, "reason": 'Referral disabled'}

    businesses = setting.get_affected_businesses()

    notifications = []
    for business in businesses:
        notifications.append(
            B2BReferralNotification.create_invite_notification(
                business=business,
                setting=setting,
            )
        )
    # TODO add bulk
    return {"created": len(notifications)}


@post_transaction_task
def create_signup_notification_task(b2b_referral_id: int):
    """Performs B2BReferralNotification.create_signup_notification() async.

    :param b2b_referral_id: id of B2BReferral object.
    """
    from webapps.b2b_referral.models import B2BReferral
    from webapps.pop_up_notification.models import B2BReferralNotification

    referral = B2BReferral.objects.get(id=b2b_referral_id)
    created = B2BReferralNotification.create_signup_notification(referral)

    return {"created": created, "referral_id": b2b_referral_id}


@post_transaction_task
def create_dyk_notification_task(b2b_referral_id: int):
    """Performs B2BReferralNotification.create_dyk_notification() async.

    :param b2b_referral_id: id of B2BReferral object.
    """
    from webapps.b2b_referral.models import B2BReferral
    from webapps.pop_up_notification.models import B2BReferralNotification

    referral = B2BReferral.objects.get(id=b2b_referral_id)
    created = B2BReferralNotification.create_dyk_notification(referral)

    return {"created": created, "referral_id": b2b_referral_id}


@post_transaction_task
def create_finish_invited_notification_task(b2b_referral_id: int):
    """Performs B2BReferralNotification.create_finish_invited_notification()
    async.

    :param b2b_referral_id: id of B2BReferral object.
    """
    from webapps.b2b_referral.models import B2BReferral
    from webapps.pop_up_notification.models import B2BReferralNotification

    referral = B2BReferral.objects.get(id=b2b_referral_id)
    created = B2BReferralNotification.create_finish_invited_notification(referral)

    return {"created": created, "referral_id": b2b_referral_id}


@post_transaction_task
def create_finish_referrer_notification_task(b2b_referral_id: int):
    """Performs B2BReferralNotification.create_finish_referrer_notification()
    async.

    :param b2b_referral_id: id of B2BReferral object.
    """
    from webapps.b2b_referral.models import B2BReferral
    from webapps.pop_up_notification.models import B2BReferralNotification

    referral = B2BReferral.objects.get(id=b2b_referral_id)

    created = B2BReferralNotification.create_finish_referrer_notification(referral)

    return {"created": created, "referral_id": b2b_referral_id}
