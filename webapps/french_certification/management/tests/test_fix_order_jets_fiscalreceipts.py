import itertools
import re
from collections.abc import Iterable
from datetime import datetime, timedelta, timezone
from enum import StrEnum
from functools import partial
from uuid import uuid4
from zoneinfo import ZoneInfo

import pytest
from django.db import IntegrityError, transaction
from freezegun import freeze_time
from model_bakery import baker

from webapps.business.baker_recipes import business_recipe
from webapps.business.models import Business
from webapps.french_certification.enums import ClosePeriodType, JETEventCode
from webapps.french_certification.models import FiscalReceipt, JET
from webapps.french_certification.management.fix_order_jets_fiscalreceipts import detect, fix
from webapps.french_certification.services.fiscal_receipt import FiscalReceiptService
from webapps.french_certification.services.jet import JETService
from webapps.french_certification.tasks import close_periods
from webapps.french_certification.utils import get_data_from_basket_payment
from webapps.invoicing.baker_recipes import fc_seller_recipe
from webapps.point_of_sale.models import Basket, BasketPayment

pytestmark = [
    pytest.mark.django_db,
]


class Usecase(StrEnum):
    OK = "ok"
    JET = "jet"
    FISCALRECEIPT = "fiscalreceipt"


businesses = {
    Usecase.OK: (100, 200),
    Usecase.JET: (101,),
    Usecase.FISCALRECEIPT: (201,),
}
businesses_affected = tuple(
    sorted(
        set(
            itertools.chain(
                businesses[Usecase.JET],
                businesses[Usecase.FISCALRECEIPT],
            )
        )
    )
)


@pytest.fixture(autouse=True)
def frozen_time() -> datetime:
    with freeze_time(
        (frozen_at := datetime(2025, 7, 30, 8, 22, tzinfo=ZoneInfo("Europe/Warsaw"))), tick=True
    ):
        yield frozen_at


@pytest.fixture(autouse=True)
@transaction.atomic
def _database(
    frozen_time: datetime,
    fiscal_receipts_per_business=4,
) -> None:
    businesses_ok = businesses[Usecase.OK]
    businesses_affected_jet = businesses[Usecase.JET]
    businesses_affected_fiscalreceipt = businesses[Usecase.FISCALRECEIPT]

    # Businesses created 7 days ago
    with freeze_time(frozen_time - timedelta(days=7)):
        for business_id in (
            *businesses_ok,
            *businesses_affected_jet,
            *businesses_affected_fiscalreceipt,
        ):
            fc_seller_recipe.make(
                business=business_recipe.make(id=business_id),
            )
            JETService.data_initialization_event(business_id=business_id)

    # Each business closed periods up to 2 days ago and had some fiscal receipts
    for business_id in (
        *businesses_ok,
        *businesses_affected_jet,
        *businesses_affected_fiscalreceipt,
    ):
        with freeze_time(frozen_time - timedelta(days=2), tick=True):
            for _ in range(fiscal_receipts_per_business):
                create_fiscal_receipt(business_id)
            close_periods(Business.objects.get(pk=business_id), ClosePeriodType.DAY)

    # JETs & fiscal receipts got messed up yesterday...
    with freeze_time(frozen_time - timedelta(days=1)):
        break_jets(businesses_affected_jet)
        break_fiscal_receipts(businesses_affected_fiscalreceipt)


def break_jets(business_ids: Iterable[int]) -> None:
    # By forcing day in the past, we mess up the numbering
    with freeze_time(datetime.now(timezone.utc) - timedelta(days=3)):
        for business_id in business_ids:
            JETService.offered_article_event(
                business_id=business_id,
                basket_item_id=uuid4(),
                operator_id=None,
            )


def break_fiscal_receipts(business_ids: Iterable[int]) -> None:
    with freeze_time(datetime.now(timezone.utc) - timedelta(days=3)):
        for business_id in business_ids:
            create_fiscal_receipt(business_id)


def create_fiscal_receipt(business_id: int) -> None:
    # Each FR is backed up a Basket with BasketPayment
    basket = baker.make(Basket, business_id=business_id)
    basket_payment = baker.make(BasketPayment, basket=basket)
    FiscalReceipt.objects.create(
        business_id=business_id,
        basket_id=basket.id,
        basket_data=dict(
            basket_items_data={},
            basket_payments_data={
                str(basket_payment.id): get_data_from_basket_payment(basket_payment),
            },
            number_of_lines=0,
        ),
        loyalty_card_data={},
    )


def test__detect__empty_database_then_nothing_detected() -> None:
    with transaction.atomic():
        JET.objects.all().delete()
        FiscalReceipt.objects.all().delete()
    assert detect() == []


def test__detect__identifies_affected_businesses() -> None:
    assert detect() == list(businesses_affected)


@pytest.mark.parametrize("business_id", list(businesses[Usecase.JET]))
def test__fix__enables_closing_periods_for_business(
    frozen_time: datetime,
    business_id: int,
    period_type=ClosePeriodType.DAY,
) -> None:
    business = Business.objects.get(pk=business_id)
    closer = partial(close_periods, business, period_type)
    with freeze_time(frozen_time + timedelta(days=3), tick=True):
        with pytest.raises(
            IntegrityError,
            match=re.compile("french_certification_jet_business_id_number_a2013f66_uniq"),
        ):
            closer()
        fix(business_id)
        closer()

    assert_manual_intervention_registered(business_id)
    assert_integrity_preserved(business_id)


@pytest.mark.parametrize("business_id", list(businesses[Usecase.FISCALRECEIPT]))
def test__fix__enables_creation_of_fiscal_receipts(
    frozen_time: datetime,
    business_id: int,
) -> None:
    basket = baker.make(Basket, business_id=business_id)
    creator = partial(
        FiscalReceiptService.create_fiscal_receipt,
        basket_id=basket.id,
        operator_id=None,
        basket_data=dict(
            basket_items_data={},
            basket_payments_data={},
        ),
        loyalty_card_data={},
    )
    with freeze_time(frozen_time + timedelta(days=3), tick=True):
        with pytest.raises(
            IntegrityError,
            match=re.compile("french_certification_fis_business_id_number_2e9d8d97_uniq"),
        ):
            creator()
        fix(business_id)
        creator()

    assert_manual_intervention_registered(business_id)
    assert_integrity_preserved(business_id)


def assert_manual_intervention_registered(business_id: int) -> None:
    assert JET.objects.get(
        business_id=business_id,
        event_code=JETEventCode.MAINTENANCE_INTERVENTIONS_FOLLOW_UP,
    )


def assert_integrity_preserved(business_id: int) -> None:
    assert not (
        JET.objects.filter(
            business_id=business_id,
            event_code=JETEventCode.INTEGRITY_FAILURE,
        ).exists()
    )
