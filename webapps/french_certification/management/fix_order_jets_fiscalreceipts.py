import itertools
import logging
from datetime import timed<PERSON><PERSON>

from django.db import transaction
from django.db.models import (
    DateTimeField,
    F,
    Max,
    Model,
    OuterRef,
    QuerySet,
    Subquery,
)

from webapps.french_certification.models import <PERSON>scalReceipt, JET
from webapps.french_certification.services.jet import JETService

log = logging.getLogger(f"booksy.{__name__}")

MANUAL_INTERVENTION_MESSAGE = (
    "Due to FLS-5578, "
    "modified creation time of a row {obj_id}'s of {table_name} from '{old_ts}' to '{new_ts}'."
)


def detect() -> list[int]:
    qs_jets = _build_detect_query(JET.objects)
    qs_fiscalreceipts = _build_detect_query(FiscalReceipt.objects)
    return list(sorted(set(itertools.chain(qs_jets, qs_fiscalreceipts))))


def fix(business_id: int) -> None:
    # Problematic row has been created after the good ones (has the highest number)
    # but with the mocked creation time (has lower than the previous number).
    # We fix this by modyfication of its creation time to be slighly after the previous number.
    _fix(JET, business_id)
    _fix(FiscalReceipt, business_id)


def _build_detect_query(qs: QuerySet) -> QuerySet:
    query = qs.values('business_id').annotate(
        max_created=Max('created'),
        max_number=Max('number'),
        max_number_created=Subquery(
            qs.filter(business_id=OuterRef('business_id'))
            .order_by("-number")
            .values('created')[:1],
            output_field=DateTimeField(),
        ),
    )
    # good observation point here!
    return (
        query.exclude(max_created=F("max_number_created"))
        .order_by("business_id")
        .values_list("business_id", flat=True)
    )


def _fix(modelcls: Model, business_id: int):
    tablename = modelcls._meta.db_table
    with transaction.atomic():
        last_two_objs = list(
            modelcls.objects.filter(business_id=business_id).order_by("-number")[:2]
        )
        if (objs_count := len(last_two_objs)) != 2:
            log.info(
                "skipped biz=%d: unexpected number of %s rows (%d)",
                business_id,
                tablename,
                objs_count,
            )
            return

        obj, prev_obj = last_two_objs
        if prev_obj.created < obj.created:
            log.info("skipped biz=%d: %s rows are in order", business_id, tablename)
            return

        old_created_at, new_created_at = (
            obj.created,
            prev_obj.created + timedelta(seconds=1),
        )
        # Since "save" is overriden, to prohibit this case, then we can't modify
        # the existing object directly. However, using QuerySet skips "save"!
        modelcls.objects.filter(id=obj.id).update(created=new_created_at)
        JETService.manual_intervention_event(
            business_id=business_id,
            message=MANUAL_INTERVENTION_MESSAGE.format(
                table_name=tablename,
                obj_id=obj.id,
                old_ts=old_created_at.isoformat(),
                new_ts=new_created_at.isoformat(),
            ),
        )
    # Higher log level in order to make sure it's visible in the log stream.
    log.warning(
        "modified creation time of table=%s row=%s (biz=%d) to fix numbers' order",
        modelcls._meta.db_table,
        obj.id,
        business_id,
        extra={
            "created_before": old_created_at.isoformat(),
            "created_after": new_created_at.isoformat(),
        },
    )
