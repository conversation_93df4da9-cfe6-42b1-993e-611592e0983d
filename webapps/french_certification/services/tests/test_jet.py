import datetime

from django.test import TestCase
from freezegun import freeze_time
from mock.mock import patch
from model_bakery import baker

from webapps.business.models import Business, Resource
from webapps.french_certification.enums import ClosePeriodType, JETEventCode, JETEventDescription
from webapps.french_certification.models import JET
from webapps.french_certification.services.jet import JETService
from webapps.french_certification.tests.common import (
    create_grand_total,
    get_fiscal_receipt_with_basket_data,
)
from webapps.invoicing.baker_recipes import fc_seller_recipe
from webapps.point_of_sale.models import Basket, BasketItem


class TestJETService(TestCase):
    def setUp(self):
        self.business = baker.make(Business)
        self.closing_date = datetime.date(2022, 5, 30)
        fc_seller_recipe.make(business=self.business)

    def test_integrity_failure_event(self):
        basket = baker.make(Basket, business_id=self.business.id)
        fiscal_receipt = get_fiscal_receipt_with_basket_data(
            business_id=basket.business_id,
            basket_id=basket.id,
            operator_id=None,
            basket_items=[],
            basket_payments=[],
        )

        JETService.integrity_failure_event(
            business_id=self.business.id,
            item=fiscal_receipt,
            operator_id=15,
        )

        jet = JET.objects.get(business_id=self.business.id)
        self.assertEqual(jet.event_code, 90)
        self.assertEqual(jet.event_description, 'integrity_failure')
        self.assertEqual(jet.operator_id, 15)
        self.assertEqual(jet.additional_info, f'Detected in FiscalReceipt {fiscal_receipt.id}')

    def test_user_rights_management_event(self):
        JETService.user_rights_management_event(
            business_id=self.business.id,
            resource_id=156,
            new_access_level=Resource.STAFF_ACCESS_LEVEL_MANAGER,
            operator_id=20,
        )

        jet = JET.objects.get(business_id=self.business.id)
        self.assertEqual(jet.event_code, 130)
        self.assertEqual(jet.event_description, 'user_rights_management')
        self.assertEqual(jet.operator_id, 20)
        self.assertEqual(jet.additional_info, 'Resource 156 access level changed to manager')

    def test_fiscal_receipt_cancellation_event(self):
        basket = baker.make(Basket, business_id=self.business.id)
        fiscal_receipt = get_fiscal_receipt_with_basket_data(
            business_id=basket.business_id,
            basket_id=basket.id,
            operator_id=None,
            basket_items=[],
            basket_payments=[],
        )

        JETService.fiscal_receipt_cancellation_event(
            business_id=self.business.id,
            fiscal_receipt=fiscal_receipt,
            reason='missclick',
            operator_id=30,
        )

        jet = JET.objects.get(business_id=self.business.id)
        self.assertEqual(jet.event_code, 190)
        self.assertEqual(jet.event_description, 'fiscal_receipt_cancellation')
        self.assertEqual(jet.operator_id, 30)
        self.assertEqual(jet.additional_info, f'Cancelled {fiscal_receipt}, reason: missclick')

    def test_major_software_version_update_event(self):
        JETService.major_software_version_update_event(
            business_id=self.business.id,
            version=6,
            commit_hash='abcd1234',
        )

        jet = JET.objects.get(business_id=self.business.id)
        self.assertEqual(jet.event_code, 250)
        self.assertEqual(jet.event_description, 'major_software_version_update')
        self.assertIsNone(jet.operator_id)
        self.assertEqual(jet.additional_info, 'Version: 6, commit hash: abcd1234')

    def test_minor_software_version_update_event(self):
        JETService.minor_software_version_update_event(
            business_id=self.business.id,
            version=6,
            commit_hash='abcd1234',
        )

        jet = JET.objects.get(business_id=self.business.id)
        self.assertEqual(jet.event_code, 255)
        self.assertEqual(jet.event_description, 'minor_software_version_update')
        self.assertIsNone(jet.operator_id)
        self.assertEqual(jet.additional_info, 'Version: 6, commit hash: abcd1234')

    def test_offered_article_event(self):
        basket_item = baker.make(BasketItem)

        JETService.offered_article_event(
            business_id=self.business.id,
            basket_item_id=basket_item.id,
            operator_id=45,
        )

        jet = JET.objects.get(business_id=self.business.id)
        self.assertEqual(jet.event_code, 328)
        self.assertEqual(jet.event_description, 'offered_article')
        self.assertEqual(jet.operator_id, 45)
        self.assertEqual(jet.additional_info, f'Offered BasketItem {basket_item.id}')

    def test_taxable_company_change_event(self):
        JETService.taxable_company_change_event(
            business_id=self.business.id,
            changed_field='city',
            operator_id=70,
        )

        jet = JET.objects.get(business_id=self.business.id)
        self.assertEqual(jet.event_code, 410)
        self.assertEqual(jet.event_description, 'taxable_company_change')
        self.assertEqual(jet.operator_id, 70)
        self.assertEqual(jet.additional_info, 'Changed field: city')

    def test_day_closed_event(self):
        JETService.day_closed_event(
            business_id=self.business.id,
            closing_date=self.closing_date,
        )

        jet = JET.objects.get(business_id=self.business.id)
        self.assertEqual(jet.event_code, 50)
        self.assertEqual(jet.event_description, 'day_or_month_closed')
        self.assertEqual(jet.operator_id, None)
        self.assertEqual(jet.additional_info, 'Daily closure: 2022-05-30')

    def test_month_closed_event(self):
        JETService.month_closed_event(
            business_id=self.business.id,
            closing_date=self.closing_date,
        )

        jet = JET.objects.get(business_id=self.business.id)
        self.assertEqual(jet.event_code, 50)
        self.assertEqual(jet.event_description, 'day_or_month_closed')
        self.assertEqual(jet.operator_id, None)
        self.assertEqual(jet.additional_info, 'Monthly closure: 2022-05')

    def test_year_closed_event(self):
        JETService.year_closed_event(
            business_id=self.business.id,
            closing_date=self.closing_date,
        )

        jet = JET.objects.get(business_id=self.business.id)
        self.assertEqual(jet.event_code, 60)
        self.assertEqual(jet.event_description, 'year_closed')
        self.assertEqual(jet.operator_id, None)
        self.assertEqual(jet.additional_info, 'Year closed: 2022')

    def test_fiscal_day_archiving_event(self):
        daily_grand_total = create_grand_total(
            business_id=self.business.id,
            net_total=20000,
            gross_total=25000,
            cumulative_gross_total=25000,
            type=ClosePeriodType.DAY,
            period_start=datetime.datetime(2022, 5, 30, tzinfo=self.business.get_timezone()),
            period_end=datetime.datetime(2022, 5, 31, tzinfo=self.business.get_timezone()),
        )

        JETService.intermediate_fiscal_archiving_event(grand_total=daily_grand_total)

        jet = JET.objects.get(business_id=self.business.id)
        self.assertEqual(jet.event_code, 20)
        self.assertEqual(jet.event_description, 'intermediate_fiscal_archiving')
        self.assertEqual(jet.operator_id, None)
        self.assertEqual(jet.additional_info, 'Day archived: 2022-05-30')

    def test_fiscal_month_archiving_event(self):
        monthly_grand_total = create_grand_total(
            business_id=self.business.id,
            net_total=10000,
            gross_total=11500,
            cumulative_gross_total=11500,
            type=ClosePeriodType.MONTH,
            period_start=datetime.datetime(2022, 5, 1, tzinfo=self.business.get_timezone()),
            period_end=datetime.datetime(2022, 6, 1, tzinfo=self.business.get_timezone()),
        )

        JETService.intermediate_fiscal_archiving_event(grand_total=monthly_grand_total)

        jet = JET.objects.get(business_id=self.business.id)
        self.assertEqual(jet.event_code, 20)
        self.assertEqual(jet.event_description, 'intermediate_fiscal_archiving')
        self.assertEqual(jet.operator_id, None)
        self.assertEqual(jet.additional_info, 'Month archived: 2022-05')

    def test_fiscal_year_archiving_event(self):
        yearly_grand_total = create_grand_total(
            business_id=self.business.id,
            net_total=10000,
            gross_total=14500,
            cumulative_gross_total=14500,
            type=ClosePeriodType.YEAR,
            period_start=datetime.datetime(2020, 1, 1, tzinfo=self.business.get_timezone()),
            period_end=datetime.datetime(2021, 1, 1, tzinfo=self.business.get_timezone()),
        )

        JETService.fiscal_year_archiving_event(grand_total=yearly_grand_total)

        jet = JET.objects.get(business_id=self.business.id)
        self.assertEqual(jet.event_code, 30)
        self.assertEqual(jet.event_description, 'fiscal_year_archiving')
        self.assertEqual(jet.operator_id, None)
        self.assertEqual(jet.additional_info, 'Year archived: 2020')

    def test_data_initialization_event(self):
        JETService.data_initialization_event(
            business_id=self.business.id,
        )

        jet = JET.objects.get(business_id=self.business.id)
        self.assertEqual(jet.event_code, 260)
        self.assertEqual(jet.event_description, 'data_initialization')
        self.assertEqual(jet.operator_id, None)
        self.assertEqual(jet.additional_info, 'Data initialization')

    def test_manual_intervention_event(
        self,
        message="this is a message",
        event_code=JETEventCode.MAINTENANCE_INTERVENTIONS_FOLLOW_UP,
        event_desc=JETEventDescription.MAINTENANCE_INTERVENTIONS_FOLLOW_UP,
    ):
        rv = JETService.manual_intervention_event(business_id=self.business.id, message=message)
        assert rv is None and JET.objects.get(
            business_id=self.business.id,
            event_code=event_code,
            event_description=event_desc,
            additional_info=message,
        )

    def test_get_initalization_time(self):
        self.assertIsNone(JETService.get_initialization_time(business_id=self.business.id))

        now = datetime.datetime.now(datetime.timezone.utc)
        with freeze_time(now):
            JETService.data_initialization_event(business_id=self.business.id)
        initialized_at = JETService.get_initialization_time(business_id=self.business.id)
        self.assertEqual(initialized_at, now)

    def test_has_migrated_to_french_certification(self):
        JETService.data_initialization_event(business_id=self.business.id)
        business_nonfc = baker.make(Business)
        now = datetime.datetime.now(datetime.timezone.utc)

        with self.subTest("non-FC business is not migrated"):
            self.assertFalse(
                JETService.has_migrated_to_french_certification(
                    business_id=business_nonfc.id,
                    created_at=now,
                )
            )

        max_delay = datetime.timedelta(days=1000)
        with patch(
            "webapps.french_certification.services.jet"
            ".MAX_DELAY_BETWEEN_BUSINESS_CREATION_AND_DATA_INITIALIZATION",
            max_delay,
        ):
            with self.subTest(
                "FC business created some time before JET initialization is migrated",
            ):
                self.assertTrue(
                    JETService.has_migrated_to_french_certification(
                        business_id=self.business.id,
                        created_at=now - max_delay - datetime.timedelta(days=1),
                    )
                )

            with self.subTest(
                "FC business created some short time before JET initialization is not migrated",
            ):
                self.assertFalse(
                    JETService.has_migrated_to_french_certification(
                        business_id=self.business.id,
                        created_at=now - max_delay,
                    )
                )
