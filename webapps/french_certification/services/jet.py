import datetime

from webapps.french_certification.consts import (
    MAX_DELAY_BETWEEN_BUSINESS_CREATION_AND_DATA_INITIALIZATION,
)
from webapps.french_certification.enums import (
    ClosePeriodType,
    JETEventAdditionalInfo,
    JET<PERSON>ventCode,
    JETEventDescription,
)
from webapps.french_certification.exceptions import DataInitializationEventRequired
from webapps.french_certification.models import (
    GrandTotal,
    JET,
)
from webapps.french_certification.utils import get_start_closing_from


class JETService:
    @staticmethod
    def integrity_failure_event(*, business_id, item, operator_id):
        JET.objects.create(
            event_code=JETEventCode.INTEGRITY_FAILURE,
            event_description=JETEventDescription.INTEGRITY_FAILURE,
            business_id=business_id,
            operator_id=operator_id,
            additional_info=JETEventAdditionalInfo.INTEGRITY_FAILURE.format(item=item),
        )

    @staticmethod
    def user_rights_management_event(
        *,
        business_id,
        resource_id,
        new_access_level,
        operator_id,
    ):
        JET.objects.create(
            event_code=JETEventCode.USER_RIGHTS_MANAGEMENT,
            event_description=JETEventDescription.USER_RIGHTS_MANAGEMENT,
            business_id=business_id,
            operator_id=operator_id,
            additional_info=JETEventAdditionalInfo.USER_RIGHTS_MANAGEMENT.format(
                resource_id=resource_id,
                new_access_level=new_access_level,
            ),
        )

    @staticmethod
    def major_software_version_update_event(
        *,
        business_id,
        version,
        commit_hash,
    ):
        JET.objects.create(
            event_code=JETEventCode.MAJOR_SOFTWARE_VERSION_UPDATE,
            event_description=JETEventDescription.MAJOR_SOFTWARE_VERSION_UPDATE,
            business_id=business_id,
            additional_info=JETEventAdditionalInfo.MAJOR_SOFTWARE_VERSION_UPDATE.format(
                version=version,
                commit_hash=commit_hash,
            ),
        )

    @staticmethod
    def minor_software_version_update_event(
        *,
        business_id,
        version,
        commit_hash,
    ):
        JET.objects.create(
            event_code=JETEventCode.MINOR_SOFTWARE_VERSION_UPDATE,
            event_description=JETEventDescription.MINOR_SOFTWARE_VERSION_UPDATE,
            business_id=business_id,
            additional_info=JETEventAdditionalInfo.MINOR_SOFTWARE_VERSION_UPDATE.format(
                version=version,
                commit_hash=commit_hash,
            ),
        )

    @staticmethod
    def fiscal_receipt_cancellation_event(*, business_id, fiscal_receipt, reason, operator_id):
        JET.objects.create(
            event_code=JETEventCode.FISCAL_RECEIPT_CANCELLATION,
            event_description=JETEventDescription.FISCAL_RECEIPT_CANCELLATION,
            business_id=business_id,
            operator_id=operator_id,
            additional_info=JETEventAdditionalInfo.FISCAL_RECEIPT_CANCELLATION.format(
                fiscal_receipt=fiscal_receipt,
                reason=reason,
            ),
        )

    @staticmethod
    def offered_article_event(*, business_id, basket_item_id, operator_id):
        JET.objects.create(
            event_code=JETEventCode.OFFERED_ARTICLE,
            event_description=JETEventDescription.OFFERED_ARTICLE,
            business_id=business_id,
            operator_id=operator_id,
            additional_info=JETEventAdditionalInfo.OFFERED_ARTICLE.format(
                basket_item_id=basket_item_id
            ),
        )

    @staticmethod
    def taxable_company_change_event(*, business_id, changed_field, operator_id):
        JET.objects.create(
            event_code=JETEventCode.TAXABLE_COMPANY_CHANGE,
            event_description=JETEventDescription.TAXABLE_COMPANY_CHANGE,
            business_id=business_id,
            operator_id=operator_id,
            additional_info=JETEventAdditionalInfo.TAXABLE_COMPANY_CHANGE.format(
                changed_field=changed_field
            ),
        )

    @staticmethod
    def day_closed_event(*, business_id, closing_date: datetime.date):
        JET.objects.create(
            event_code=JETEventCode.DAY_OR_MONTH_CLOSED,
            event_description=JETEventDescription.DAY_OR_MONTH_CLOSED,
            business_id=business_id,
            operator_id=None,
            additional_info=JETEventAdditionalInfo.DAY_OR_MONTH_CLOSED.format(
                period_type=ClosePeriodType.DAY.label, date=closing_date.strftime('%Y-%m-%d')
            ),
        )

    @staticmethod
    def month_closed_event(*, business_id, closing_date: datetime.date):
        JET.objects.create(
            event_code=JETEventCode.DAY_OR_MONTH_CLOSED,
            event_description=JETEventDescription.DAY_OR_MONTH_CLOSED,
            business_id=business_id,
            operator_id=None,
            additional_info=JETEventAdditionalInfo.DAY_OR_MONTH_CLOSED.format(
                period_type=ClosePeriodType.MONTH.label, date=closing_date.strftime('%Y-%m')
            ),
        )

    @staticmethod
    def year_closed_event(*, business_id, closing_date: datetime.date):
        JET.objects.create(
            event_code=JETEventCode.YEAR_CLOSED,
            event_description=JETEventDescription.YEAR_CLOSED,
            business_id=business_id,
            operator_id=None,
            additional_info=JETEventAdditionalInfo.YEAR_CLOSED.format(year=closing_date.year),
        )

    @staticmethod
    def fiscal_year_archiving_event(*, grand_total: GrandTotal):
        JET.objects.create(
            event_code=JETEventCode.FISCAL_YEAR_ARCHIVING,
            event_description=JETEventDescription.FISCAL_YEAR_ARCHIVING,
            business_id=grand_total.business_id,
            operator_id=None,
            additional_info=JETEventAdditionalInfo.FISCAL_YEAR_ARCHIVING.format(
                year=grand_total.closure_date
            ),
        )

    @staticmethod
    def intermediate_fiscal_archiving_event(*, grand_total: GrandTotal):
        JET.objects.create(
            event_code=JETEventCode.INTERMEDIATE_FISCAL_ARCHIVING,
            event_description=JETEventDescription.INTERMEDIATE_FISCAL_ARCHIVING,
            business_id=grand_total.business_id,
            operator_id=None,
            additional_info=JETEventAdditionalInfo.FISCAL_DAY_OR_MONTH_ARCHIVING.format(
                period_type=grand_total.type.name.capitalize(), date=grand_total.closure_date
            ),
        )

    @staticmethod
    def data_initialization_event(*, business_id):
        JET.objects.create(
            event_code=JETEventCode.DATA_INITIALIZATION,
            event_description=JETEventDescription.DATA_INITIALIZATION,
            business_id=business_id,
            operator_id=None,
            additional_info=JETEventAdditionalInfo.DATA_INITIALIZATION,
        )

    @staticmethod
    def manual_intervention_event(
        *,
        business_id: int,
        message: str,
    ) -> None:
        JET.objects.create(
            business_id=business_id,
            additional_info=message,
            event_code=JETEventCode.MAINTENANCE_INTERVENTIONS_FOLLOW_UP,
            event_description=JETEventDescription.MAINTENANCE_INTERVENTIONS_FOLLOW_UP,
        )

    @staticmethod
    def get_initialization_time(*, business_id: int) -> datetime.datetime | None:
        try:
            return get_start_closing_from(business_id)
        except DataInitializationEventRequired:
            return None

    @staticmethod
    def has_migrated_to_french_certification(
        *,
        business_id: int,
        created_at: datetime.datetime,
    ) -> bool:
        initialized_at = JETService.get_initialization_time(business_id=business_id)
        if initialized_at is None:
            return False

        migration_threshold = (
            initialized_at - MAX_DELAY_BETWEEN_BUSINESS_CREATION_AND_DATA_INITIALIZATION
        )
        return created_at <= migration_threshold
