from django.contrib import admin
from django.db.models import Q
from django.utils.html import escape, format_html

from lib.admin_helpers import (
    BaseModelAdmin,
    ChangeLogAdminMixin,
    NoAddDelMixin,
    ReadOnlyFieldsMixin,
)
from lib.admin_helpers import admin_link
from lib.deeplink import CLAIM_VOUCHER, generate_deeplink
from lib.tools import sget_v2
from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin
from webapps.business.forms.fields import TableCreator
from webapps.user.groups import GroupNameV2
from webapps.voucher.models import (
    Voucher,
    VoucherChangeLog,
    VoucherTemplate,
    VoucherTemplateServiceVariant,
    VoucherServiceVariant,
    VoucherOrder,
    VoucherAdditionalInfo,
    VoucherClaim,
)


class GenericVoucherServiceVariantInline(ReadOnlyFieldsMixin, NoAddDelMixin, admin.StackedInline):
    exclude = ['deleted']
    extra = 0

    def get_extra(self, request, obj=None, **kwargs):
        return 0  # add disallowed


class VoucherTemplateServiceVariantInline(GenericVoucherServiceVariantInline):
    model = VoucherTemplateServiceVariant


class VoucherServiceVariantInline(GenericVoucherServiceVariantInline):
    model = VoucherServiceVariant


class VoucherTemplateAdmin(ReadOnlyFieldsMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = ()
    inlines = [
        VoucherTemplateServiceVariantInline,
    ]
    list_filter = (
        'active',
        'deleted',
        'online_purchase',
        'status',
        'type',
    )
    search_fields = (
        '=pos__business__id',
        'pos__business__name',
    )
    fields = [
        'id',
        'pos',
        'type',
        'active',
        'name',
        'description',
        'valid_till',
        'item_price',
        'value',
        'online_purchase',
        'background_image',
        'font_color',
    ]
    raw_id_fields = [
        'pos',
    ]

    list_display = [
        'id',
        'name',
        'type',
        'active',
        'status',
        'pos',
        'series',
        'online_purchase',
    ]

    def get_queryset(self, request):
        """
        Method has been overriden to change `_default_manager` to `all_objects`.
        """
        qs = self.model.all_objects.get_queryset()
        ordering = self.get_ordering(request)
        if ordering:
            qs = qs.order_by(*ordering)
        return qs


admin.site.register(VoucherTemplate, VoucherTemplateAdmin)


class OnlineGiftCardFilter(admin.SimpleListFilter):
    title = 'purchased online'
    parameter_name = 'purchased_online'

    def lookups(self, request, model_admin):
        return (True, 'Yes'), (False, 'No')

    def queryset(self, request, queryset):
        value = self.value()
        if value is None:
            return queryset
        if value == 'True':
            return queryset.filter(voucher_changes__status='D')
        return queryset.exclude(voucher_changes__status='D')


class VoucherAdmin(
    ReadOnlyFieldsMixin, ChangeLogAdminMixin, NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin
):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = ()
    list_filter = ('status', 'voucher_template__type', OnlineGiftCardFilter)
    search_fields = ('=pos__business__id', 'pos__business__name')
    inlines = [VoucherServiceVariantInline]

    fields = [
        'code',
        'voucher_type',
        'pos',
        'voucher_template',
        'current_balance',
        'valid_from',
        'valid_till',
        'status',
        'customer',
        'transaction',
        'purchased_online',
        'waitting_for_recipient',
        'voucher_change_log',
    ]

    list_display = [
        'id',
        'voucher_type',
        'status',
        'customer',
        'purchased_online',
        'waitting_for_recipient',
    ]

    readonly_fields = ['voucher_type', 'voucher_change_log', 'purchased_online', 'transaction']

    @staticmethod
    def voucher_type(obj):
        return obj.voucher_template.get_type_display()

    @staticmethod
    def transaction(obj):
        transaction_row = obj.transaction_rows.first()
        transaction = sget_v2(transaction_row, ['transaction'])
        return format_html(
            '<a href="{}">{}</a>',
            admin_link(transaction),
            transaction,
        )

    def voucher_change_log(self, obj):
        max_num_change = 20
        voucher_changes_qs = VoucherChangeLog.objects.filter(
            voucher_id=obj.id,
        ).order_by('created')

        slice_end = voucher_changes_qs.count()
        slice_start = slice_end - max_num_change
        if slice_start > 0:
            # get only last max_num_change
            voucher_changes = voucher_changes_qs[slice_start:slice_end]
        else:
            # nothing to slice less then max_num_change items
            voucher_changes = voucher_changes_qs

        diff_table = TableCreator(('ID', 'Created', 'Operator', 'Type', 'PaymentRow ID', 'Diff'))
        return format_html(
            diff_table.form_table(
                [
                    [
                        change.id,
                        str(change.created),
                        change.operator.email if change.operator else "Booksy",
                        change.get_type_display(),
                        diff_table.get_id_link(change.payment_row),
                        self.diff_display(change),
                    ]
                    for change in voucher_changes[::-1]
                ]
            )
        )


admin.site.register(Voucher, VoucherAdmin)


class ActiveOrderFilter(admin.SimpleListFilter):
    title = 'active'
    parameter_name = 'active'

    def lookups(self, request, model_admin):
        return (True, 'Yes'), (False, 'No')

    def queryset(self, request, queryset):
        value = self.value()
        if value is None:
            return queryset

        filter_ = Q(voucher_id__isnull=True)
        if value == 'True':
            return queryset.filter(filter_)
        return queryset.exclude(filter_)


class VoucherOrderAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = ()
    list_display = [
        'id',
        'bussines_id',
        'voucher_template_id',
        'customer_id',
        'voucher_id',
        'friends_name',
        'friends_phone',
    ]

    search_fields = [
        '=voucher_template__pos__business__id',
        '=voucher_template__id',
        '=customer__id',
        '=voucher__id',
        'friends_name',
        'friends_phone',
    ]

    list_filter = [
        ActiveOrderFilter,
    ]

    raw_id_fields = [
        'voucher_template',
        'customer',
        'voucher',
    ]

    fields = [
        'voucher_template',
        'customer',
        'voucher',
        'friends_name',
        'friends_phone',
    ]

    @staticmethod
    def bussines_id(obj):
        return obj.voucher_template.pos.business_id


admin.site.register(VoucherOrder, VoucherOrderAdmin)


class VoucherAdditionalInfoAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = ()
    list_filter = ('sold_vouchers_before_migration_threshold',)
    search_fields = (
        'pos__id',
        '=pos__business__id',
        '=pos__business__name',
    )
    list_display = (
        'id',
        'pos_id',
        'pos',
        'sold_vouchers_before_migration_threshold',
    )
    raw_id_fields = ('pos',)


admin.site.register(VoucherAdditionalInfo, VoucherAdditionalInfoAdmin)


class IsConsumedListFilter(admin.SimpleListFilter):
    title = 'consumed'
    parameter_name = 'consumed'

    def lookups(self, request, model_admin):
        return [
            (1, 'Yes'),
            (0, 'No'),
        ]

    def queryset(self, request, queryset):
        if self.value() is not None:
            return queryset.filter(
                consumed__isnull=(not bool(int(self.value()))),
            )
        return queryset


class VoucherClaimAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = ()
    list_display = (
        'id',
        'hash',
        'voucher_id',
        'get_business',
        'consumed',
    )

    search_fields = (
        '=voucher__pos__business__id',
        'voucher__pos__business__name__icontains',
        '=voucher__id',
        '=hash',
    )

    list_filter = (IsConsumedListFilter,)

    fields = [
        'hash',
        'consumed',
        'from_user',
        'to_user',
        'voucher',
        'deeplink',
    ]

    raw_id_fields = ['from_user', 'to_user', 'voucher']

    readonly_fields = ('deeplink',)

    @admin.display(description='Business', ordering='obj.voucher.pos.business__id')
    def get_business(self, obj):
        biz = obj.voucher.pos.business

        return format_html(
            '<a href="{}">{}: {} ({})</a>',
            admin_link(biz),
            biz.id,
            escape(biz.name),
            escape(biz.owner.email),
        )

    @staticmethod
    def deeplink(obj):
        if not obj:
            return ''

        key = f'{CLAIM_VOUCHER}/{obj.hash}'

        return generate_deeplink(
            app_type='C',
            data={
                '$deeplink_path': key,
                '$ios_deeplink_path': key,
                'mobile_deeplink': key,
            },
        )


admin.site.register(VoucherClaim, VoucherClaimAdmin)
