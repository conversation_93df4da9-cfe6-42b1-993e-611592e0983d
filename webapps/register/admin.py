from django.contrib import admin

from lib.admin_helpers import NoAddDelMixin, ReadOnlyFieldsMixin, BaseModelAdmin
from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin
from webapps.register.models import (
    Register,
    RegisterClosingAmount,
    RegisterOperation,
)
from webapps.user.groups import GroupNameV2


class RegisterClosingAmountInline(ReadOnlyFieldsMixin, NoAddDelMixin, admin.TabularInline):
    model = RegisterClosingAmount


class RegisterOperationInline(ReadOnlyFieldsMixin, NoAddDelMixin, admin.TabularInline):
    model = RegisterOperation


class RegisterAdmin(ReadOnlyFieldsMixin, NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = ()
    list_display = (
        'id',
        'pos',
        'created',
        'is_open',
        'opened_by',
        'opened_at',
        'closed_by',
        'closed_at',
    )
    search_fields = (
        '=id',
        '=pos__business__id',
        '=opened_by__email',
        '=closed_by__email',
    )
    inlines = [RegisterClosingAmountInline, RegisterOperationInline]


admin.site.register(Register, RegisterAdmin)
