from copy import copy

from django.test import TestCase

from lib.test_utils import user_recipe
from lib.tools import tznow
from webapps.booking.book_again_v2.book_again_info import (
    BookAgainInfoFactory,
    ObjectNotFoundException,
)
from webapps.booking.book_again_v2.data_retriever import BookAgainAbstractDataReteriever
from webapps.booking.enums import BookAgainStatusesEnum, SubbookingServiceVariantMode
from webapps.booking.serializers.book_again_v2 import BookingAgainInfoSerializer
from webapps.business.baker_recipes import business_recipe
from webapps.business.enums import BusinessBookingStatusesEnum

BUSINESS_ID = 5555
REGION_ID = 80
REGION_SLUG = '01-023'
USER_ID = 234
SERVICE_VARIANT_ID = 99999
APPOINTMENT_ID = 3131
SUBBOOKING_ID = 80909
TREATMENT_ID = 31913
PRIMARY_CATEGORY_ID = 490
LATITUDE = 39.828127
LONGITUDE = 20.9908877
COMBO_SERVICE_VARIANT_1 = 21
COMBO_SERVICE_VARIANT_2 = 22
COMBO_SERVICE_VARIANT_3 = 23
STAFFER1_ID = 4000
STAFFER2_ID = 5000
STAFFER3_ID = 6000
MEMBER_ID = 13
PARENT_ID = 14
BCI_MEMBER = 666
MEMBER_USER = 222


class TestDataReceiver(BookAgainAbstractDataReteriever):
    def __init__(self, *args, **kwargs):
        super().__init__(business_id=BUSINESS_ID, user_id=USER_ID, appointment_id=APPOINTMENT_ID)
        self.subbooking_data = kwargs.get('subbooking_data')
        self.addons_data = kwargs.get('addon_data')
        self.business_data = kwargs.get('business_data')
        self.appointment_data = kwargs.get('appointment_data')
        self.combo_membership_data = kwargs.get('combo_membership_data')

    def get_business_data(self) -> dict:
        return self.business_data or {
            'id': BUSINESS_ID,
            'visible': True,
            'deleted': None,
            'active': True,
            'region_id': REGION_ID,
            'region_name': REGION_SLUG,
            'region_latitude': LATITUDE,
            'region_longitude': LONGITUDE,
            'primary_category_id': PRIMARY_CATEGORY_ID,
            'primary_category_slug': 'nails-trimming',
            'is_blacklisted_user': False,
        }

    def get_appointment_data(self):
        return self.appointment_data or {
            'id': APPOINTMENT_ID,
            'booked_for': 678,
            'booked_for_user': USER_ID,
            'booked_by': None,
            'booked_by_user': None,
            'member_exists': False,
            'member_id': None,
        }

    def get_subbookings_data(self):
        return self.subbooking_data or [
            {
                'id': SUBBOOKING_ID,
                'treatment_id': TREATMENT_ID,
                'treatment_slug': 'treatment1',
                'treatment_internal_name': 'treatment1',
                'service_variant_id': SERVICE_VARIANT_ID,
                'service_variant_active': True,
                'service_active': True,
                'service_is_available_for_customer_booking': True,
                'combo_parent_id': None,
                'staffer_id': None,
                'appliance_id': None,
                'is_staffer_still_active': False,
                'is_any_staffer_available': True,
            }
        ]

    def get_addons_data(self, _subbooking_ids: list[int]):
        return self.addons_data or []

    def get_combo_membership_data(self, _combo_ids: list[int]) -> list[dict]:
        return self.combo_membership_data or []


class TestBookAgainInfoFactory(TestCase):
    def setUp(self):
        self.user = user_recipe.make()
        self.tdr = TestDataReceiver()
        self.factory = BookAgainInfoFactory(
            appointment_id=self.tdr.appointment_id,
            user_id=self.tdr.user_id,
            business_id=self.tdr.business_id,
        )

    AVAILABLE = BusinessBookingStatusesEnum.AVAILABLE

    def get_no_appointment_response(
        self,
        business_booking_status: BusinessBookingStatusesEnum = AVAILABLE,
        book_again_status: BookAgainStatusesEnum = BookAgainStatusesEnum.OK,
        treatments: list = None,
    ):
        return {
            'business_search_info': {
                'region': {
                    'id': REGION_ID,
                    'slug': REGION_SLUG,
                    'latitude': LATITUDE,
                    'longitude': LONGITUDE,
                },
                'primary_category': {
                    'id': PRIMARY_CATEGORY_ID,
                    'slug': 'nails-trimming',
                },
                'treatments': (
                    [{'id': TREATMENT_ID, 'slug': 'treatment1'}]
                    if treatments is None
                    else treatments
                ),
            },
            'business_booking_status': business_booking_status,
            'book_again_status': book_again_status,
            'book_again_appointment': None,
        }

    def get_appointment_response(
        self,
        subbookings: list = None,
        book_for_family_member: int = None,
        service_variant_id: int = None,
        business_booking_status: BusinessBookingStatusesEnum = AVAILABLE,
        book_again_status: BookAgainStatusesEnum = BookAgainStatusesEnum.OK,
    ):
        if not subbookings:
            subbookings = [
                {
                    'service_variant': {
                        'id': service_variant_id,
                        'mode': SubbookingServiceVariantMode.VARIANT,
                    },
                    'addons': [],
                    'staffer_id': None,
                    'combo_children': [],
                    'appliance_id': None,
                }
            ]
        return {
            'book_again_appointment': {
                'subbookings': subbookings,
                'book_for_family_member': book_for_family_member,
            },
            'business_search_info': None,
            'business_booking_status': business_booking_status,
            'book_again_status': book_again_status,
        }

    def test_appointment_with_removed_addon(self):
        self.tdr.addons_data = [
            {
                'subbooking': SUBBOOKING_ID,
                'service_addon': 9201,
                'quantity': 2,
                'addon_deleted': tznow(),
                'max_allowed_quantity': None,
                'is_available_for_customer_booking': True,
                'still_in_same_service': False,
            }
        ]
        self.factory.data_retriever = self.tdr

        subbookings_data = [
            {
                'service_variant': {
                    'id': SERVICE_VARIANT_ID,
                    'mode': SubbookingServiceVariantMode.VARIANT,
                },
                'staffer_id': None,
                'combo_children': [],
                'appliance_id': None,
                'addons': [],
            },
        ]
        self.assertDictEqual(
            BookingAgainInfoSerializer(self.factory.get_info()).data,
            self.get_appointment_response(
                subbookings=subbookings_data,
                book_again_status=BookAgainStatusesEnum.SERVICE_MODIFIED,
            ),
        )

    def test_appointment_with_removed_staffer(self):
        subbookings_data = self.tdr.get_subbookings_data()
        subbookings_data[0]['staffer_id'] = STAFFER1_ID
        self.tdr.subbooking_data = subbookings_data
        self.factory.data_retriever = self.tdr
        subbookings_response = [
            {
                'service_variant': {
                    'id': subbookings_data[0]['service_variant_id'],
                    'mode': SubbookingServiceVariantMode.VARIANT,
                },
                'staffer_id': -1,
                'combo_children': [],
                'appliance_id': None,
                'addons': [],
            },
        ]
        self.assertDictEqual(
            BookingAgainInfoSerializer(self.factory.get_info()).data,
            self.get_appointment_response(
                subbookings=subbookings_response,
                book_again_status=BookAgainStatusesEnum.STAFFER_NOT_AVAILABLE,
            ),
        )

    def test_appointment_without_services(self):
        subbookings_data = self.tdr.get_subbookings_data()
        subbookings_data[0]['treatment_id'] = None
        subbookings_data[0]['treatment_slug'] = None
        subbookings_data[0]['treatment_internal_name'] = None
        subbookings_data[0]['service_variant_id'] = None
        subbookings_data[0]['service_variant_active'] = None
        subbookings_data[0]['service_active'] = None
        subbookings_data[0]['service_is_available_for_custmer_booking'] = None
        self.tdr.subbooking_data = subbookings_data
        self.factory.data_retriever = self.tdr
        self.assertDictEqual(
            BookingAgainInfoSerializer(self.factory.get_info()).data,
            self.get_no_appointment_response(
                book_again_status=BookAgainStatusesEnum.ALL_SERVICES_DELETED, treatments=[]
            ),
        )

    def test_deleted_business(self):
        business_data = self.tdr.get_business_data()
        business_data['visible'] = False
        business_data['deleted'] = tznow()
        self.tdr.business_data = business_data
        self.factory.data_retriever = self.tdr
        self.assertDictEqual(
            BookingAgainInfoSerializer(self.factory.get_info()).data,
            self.get_no_appointment_response(
                business_booking_status=BusinessBookingStatusesEnum.NOT_ACTIVE,
                book_again_status=BookAgainStatusesEnum.UNKNOWN,
            ),
        )

    def test_not_visible_business(self):
        business_data = self.tdr.get_business_data()
        business_data['visible'] = False
        self.tdr.business_data = business_data
        self.factory.data_retriever = self.tdr
        self.assertDictEqual(
            BookingAgainInfoSerializer(self.factory.get_info()).data,
            self.get_no_appointment_response(
                business_booking_status=BusinessBookingStatusesEnum.ONLINE_BOOKING_OFF,
                book_again_status=BookAgainStatusesEnum.UNKNOWN,
            ),
        )

    def test_non_existing_appointment(self):
        self.business = business_recipe.make()
        with self.assertRaises(ObjectNotFoundException):
            BookAgainInfoFactory(123456789, self.user.id, self.business.id).get_info()

    def test_blacklisted_user(self):
        business_data = self.tdr.get_business_data()
        business_data['is_blacklisted_user'] = True
        self.tdr.business_data = business_data
        self.factory.data_retriever = self.tdr
        self.assertDictEqual(
            BookingAgainInfoSerializer(self.factory.get_info()).data,
            self.get_no_appointment_response(
                business_booking_status=BusinessBookingStatusesEnum.SPIN,
                book_again_status=BookAgainStatusesEnum.UNKNOWN,
            ),
        )

    def test_appointment_with_service_not_active(self):
        subbookings_data = self.tdr.get_subbookings_data()
        subbookings_data[0]['service_active'] = False
        self.tdr.subbooking_data = subbookings_data
        self.factory.data_retriever = self.tdr
        self.assertDictEqual(
            BookingAgainInfoSerializer(self.factory.get_info()).data,
            self.get_no_appointment_response(
                book_again_status=BookAgainStatusesEnum.ALL_SERVICES_DELETED
            ),
        )

    def test_appointment_with_service_not_performed_by_any_staffer(self):
        subbookings_data = self.tdr.get_subbookings_data()
        subbookings_data[0]['is_any_staffer_available'] = False
        self.tdr.subbooking_data = subbookings_data
        self.factory.data_retriever = self.tdr
        self.assertDictEqual(
            BookingAgainInfoSerializer(self.factory.get_info()).data,
            self.get_no_appointment_response(
                book_again_status=BookAgainStatusesEnum.ALL_SERVICES_DELETED
            ),
        )

    def test_appointment_with_service_not_available_for_booking(self):
        subbookings_data = self.tdr.get_subbookings_data()
        subbookings_data[0]['service_is_available_for_customer_booking'] = False
        self.tdr.subbooking_data = subbookings_data
        self.factory.data_retriever = self.tdr
        self.assertDictEqual(
            BookingAgainInfoSerializer(self.factory.get_info()).data,
            self.get_no_appointment_response(
                book_again_status=BookAgainStatusesEnum.ALL_SERVICES_DELETED
            ),
        )

    def test_appointment_with_one_service_not_available_for_booking(self):
        subbookings_data = self.tdr.get_subbookings_data()
        subbookings_data.append(copy(subbookings_data[0]))
        subbookings_data[0]['service_is_available_for_customer_booking'] = False
        subbookings_data[1]['id'] = subbookings_data[0]['id'] + 1
        subbookings_data[1]['service_variant_id'] = subbookings_data[0]['service_variant_id'] + 1
        self.tdr.subbooking_data = subbookings_data
        self.factory.data_retriever = self.tdr
        self.assertDictEqual(
            BookingAgainInfoSerializer(self.factory.get_info()).data,
            self.get_appointment_response(
                service_variant_id=subbookings_data[1]['service_variant_id'],
                book_again_status=BookAgainStatusesEnum.SOME_SERVICES_DELETED,
            ),
        )

    def test_appointment_with_one_service_not_active(self):
        subbookings_data = self.tdr.get_subbookings_data()
        subbookings_data.append(copy(subbookings_data[0]))
        subbookings_data[0]['service_active'] = False
        subbookings_data[1]['id'] = subbookings_data[0]['id'] + 1
        subbookings_data[1]['service_variant_id'] = subbookings_data[0]['service_variant_id'] + 1
        self.tdr.subbooking_data = subbookings_data
        self.factory.data_retriever = self.tdr
        self.assertDictEqual(
            BookingAgainInfoSerializer(self.factory.get_info()).data,
            self.get_appointment_response(
                service_variant_id=subbookings_data[1]['service_variant_id'],
                book_again_status=BookAgainStatusesEnum.SOME_SERVICES_DELETED,
            ),
        )

    def test_appointment_with_service_variant_not_active(self):
        subbookings_data = self.tdr.get_subbookings_data()
        subbookings_data[0]['service_variant_active'] = False
        self.tdr.subbooking_data = subbookings_data
        self.factory.data_retriever = self.tdr
        self.assertDictEqual(
            BookingAgainInfoSerializer(self.factory.get_info()).data,
            self.get_no_appointment_response(
                book_again_status=BookAgainStatusesEnum.ALL_SERVICES_DELETED
            ),
        )

    def test_appointment_with_one_service_variant_not_active(self):
        subbookings_data = self.tdr.get_subbookings_data()
        subbookings_data.append(copy(subbookings_data[0]))
        subbookings_data[0]['service_variant_active'] = False
        subbookings_data[1]['id'] = subbookings_data[0]['id'] + 1
        subbookings_data[1]['service_variant_id'] = subbookings_data[0]['service_variant_id'] + 1
        self.tdr.subbooking_data = subbookings_data
        self.factory.data_retriever = self.tdr
        self.assertDictEqual(
            BookingAgainInfoSerializer(self.factory.get_info()).data,
            self.get_appointment_response(
                service_variant_id=subbookings_data[1]['service_variant_id'],
                book_again_status=BookAgainStatusesEnum.SOME_SERVICES_DELETED,
            ),
        )

    def _prepare_combo_tdr_data(self):
        subbookings_data = self.tdr.get_subbookings_data()
        for _ in range(3):
            subbookings_data.append(copy(subbookings_data[0]))
            subbookings_data[-1]['id'] = subbookings_data[0]['id'] + 1
            subbookings_data[-1]['combo_parent_id'] = SUBBOOKING_ID
            subbookings_data[-1]['is_staffer_still_active'] = True
        subbookings_data[1]['staffer_id'] = STAFFER1_ID
        subbookings_data[2]['staffer_id'] = STAFFER2_ID
        subbookings_data[3]['staffer_id'] = STAFFER1_ID
        subbookings_data[1]['service_variant_id'] = COMBO_SERVICE_VARIANT_1
        subbookings_data[2]['service_variant_id'] = COMBO_SERVICE_VARIANT_2
        subbookings_data[3]['service_variant_id'] = COMBO_SERVICE_VARIANT_3
        self.tdr.subbooking_data = subbookings_data
        self.tdr.combo_membership_data = [
            {'combo': SERVICE_VARIANT_ID, 'child': COMBO_SERVICE_VARIANT_1},
            {'combo': SERVICE_VARIANT_ID, 'child': COMBO_SERVICE_VARIANT_2},
            {'combo': SERVICE_VARIANT_ID, 'child': COMBO_SERVICE_VARIANT_3},
        ]
        self.factory.data_retriever = self.tdr

    def _get_combo_subbookings_data(self):
        return [
            {
                'service_variant': {
                    'id': SERVICE_VARIANT_ID,
                    'mode': SubbookingServiceVariantMode.VARIANT,
                },
                'addons': [],
                'staffer_id': None,
                'combo_children': [
                    {
                        'service_variant': {
                            'id': COMBO_SERVICE_VARIANT_1,
                            'mode': SubbookingServiceVariantMode.VARIANT,
                        },
                        'staffer_id': STAFFER1_ID,
                        'addons': [],
                        'appliance_id': None,
                    },
                    {
                        'service_variant': {
                            'id': COMBO_SERVICE_VARIANT_2,
                            'mode': SubbookingServiceVariantMode.VARIANT,
                        },
                        'staffer_id': STAFFER2_ID,
                        'addons': [],
                        'appliance_id': None,
                    },
                    {
                        'service_variant': {
                            'id': COMBO_SERVICE_VARIANT_3,
                            'mode': SubbookingServiceVariantMode.VARIANT,
                        },
                        'staffer_id': STAFFER1_ID,
                        'addons': [],
                        'appliance_id': None,
                    },
                ],
                'appliance_id': None,
            }
        ]

    def test_existing_combo_appointment(self):
        self._prepare_combo_tdr_data()
        subbookings_data = self._get_combo_subbookings_data()
        self.assertDictEqual(
            BookingAgainInfoSerializer(self.factory.get_info()).data,
            self.get_appointment_response(
                subbookings=subbookings_data, book_again_status=BookAgainStatusesEnum.OK
            ),
        )

    def test_existing_combo_appointment_with_missing_service_variant(self):
        self._prepare_combo_tdr_data()
        self.factory.data_retriever.subbooking_data[2]['service_variant_active'] = False
        del self.factory.data_retriever.combo_membership_data[1]
        subbookings_data = self._get_combo_subbookings_data()
        del subbookings_data[0]['combo_children'][1]
        self.assertDictEqual(
            BookingAgainInfoSerializer(self.factory.get_info()).data,
            self.get_appointment_response(
                subbookings=subbookings_data,
                book_again_status=BookAgainStatusesEnum.SERVICE_MODIFIED,
            ),
        )

    def test_existing_combo_appointment_with_one_child_service_not_available_for_booking(self):
        self._prepare_combo_tdr_data()
        self.factory.data_retriever.subbooking_data[1][
            'service_is_available_for_customer_booking'
        ] = False
        subbookings_data = self._get_combo_subbookings_data()
        self.assertDictEqual(
            BookingAgainInfoSerializer(self.factory.get_info()).data,
            self.get_appointment_response(subbookings=subbookings_data),
        )

    def test_existing_combo_appointment_with_new_service_variant(self):
        self._prepare_combo_tdr_data()
        del self.factory.data_retriever.subbooking_data[1]
        subbookings_data = self._get_combo_subbookings_data()
        subbookings_data[0]['combo_children'][0]['staffer_id'] = None
        self.assertDictEqual(
            BookingAgainInfoSerializer(self.factory.get_info()).data,
            self.get_appointment_response(
                subbookings=subbookings_data,
                book_again_status=BookAgainStatusesEnum.SERVICE_MODIFIED,
            ),
        )

    def test_existing_combo_appointment_with_staffer_not_available(self):
        self._prepare_combo_tdr_data()
        self.factory.data_retriever.subbooking_data[3]['staffer_id'] = STAFFER3_ID
        self.factory.data_retriever.subbooking_data[3]['is_staffer_still_active'] = False
        subbookings_data = self._get_combo_subbookings_data()
        subbookings_data[0]['combo_children'][2]['staffer_id'] = -1
        self.assertDictEqual(
            BookingAgainInfoSerializer(self.factory.get_info()).data,
            self.get_appointment_response(
                subbookings=subbookings_data,
                book_again_status=BookAgainStatusesEnum.STAFFER_NOT_AVAILABLE,
            ),
        )

    def test_family_and_friends_appointment(self):
        appointment_data = self.tdr.get_appointment_data()
        appointment_data['booked_by'] = appointment_data['booked_for']
        appointment_data['booked_by_user'] = appointment_data['booked_for_user']
        appointment_data['booked_for'] = BCI_MEMBER
        appointment_data['booked_for_user'] = MEMBER_USER
        appointment_data['member_id'] = MEMBER_ID
        appointment_data['member_exists'] = True
        self.tdr.appointment_data = appointment_data
        self.factory.data_retriever = self.tdr
        self.assertDictEqual(
            BookingAgainInfoSerializer(self.factory.get_info()).data,
            self.get_appointment_response(
                book_for_family_member=MEMBER_ID,
                service_variant_id=SERVICE_VARIANT_ID,
            ),
        )

    def test_family_and_friends_appointment_booked_for_me(self):
        appointment_data = self.tdr.get_appointment_data()
        appointment_data['booked_by'] = BCI_MEMBER
        appointment_data['booked_by_user'] = MEMBER_USER
        appointment_data['member_id'] = PARENT_ID
        self.tdr.appointment_data = appointment_data
        self.factory.data_retriever = self.tdr
        self.assertDictEqual(
            BookingAgainInfoSerializer(self.factory.get_info()).data,
            self.get_appointment_response(
                service_variant_id=SERVICE_VARIANT_ID,
            ),
        )

    def test_family_and_friends_appointment_member_not_longer_in_family(self):
        appointment_data = self.tdr.get_appointment_data()
        appointment_data['booked_by'] = appointment_data['booked_for']
        appointment_data['booked_by_user'] = appointment_data['booked_for_user']
        appointment_data['booked_for'] = BCI_MEMBER
        appointment_data['booked_for_user'] = MEMBER_USER
        appointment_data['member_id'] = MEMBER_ID
        appointment_data['member_exists'] = False
        self.tdr.appointment_data = appointment_data
        self.factory.data_retriever = self.tdr
        self.assertDictEqual(
            BookingAgainInfoSerializer(self.factory.get_info()).data,
            self.get_appointment_response(
                service_variant_id=SERVICE_VARIANT_ID,
                book_again_status=BookAgainStatusesEnum.FAMILY_AND_FRIENDS_MEMBER_DELETED,
            ),
        )
