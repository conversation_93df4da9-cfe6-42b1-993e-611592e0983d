import pytest
from django.test import TestCase, override_settings, testcases
from model_bakery import baker
from parameterized import parameterized

from webapps.booking.models import (
    Appointment,
    BookingSources,
    consts,
)
from webapps.booking.ports import AppointmentPort
from webapps.booking.tests.utils import create_appointment
from webapps.business.models import Resource


@pytest.mark.django_db
class TestAppointmentPortGetCustomerAppointmentsIds(TestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.booking_source = baker.make(
            BookingSources,
            name=consts.WEB,
            app_type=BookingSources.CUSTOMER_APP,
        )

    @override_settings(LIVE_DEPLOYMENT="yes")
    def test_when_live_deployment_then_raises(self):
        with pytest.raises(RuntimeError, match="HEAVY"):
            AppointmentPort.get_customer_appointments_ids()

    def test_when_no_appointments_then_returns_no_ids(self):
        self.assertEqual(AppointmentPort.get_customer_appointments_ids(), [])

    def test_when_no_customer_appointments_then_returns_no_ids(self):
        baker.make(
            Appointment,
            type=Appointment.TYPE.RESERVATION,
            source=self.booking_source,
        )
        baker.make(
            Appointment,
            type=Appointment.TYPE.BUSINESS,
            source=self.booking_source,
        )

        self.assertEqual(AppointmentPort.get_customer_appointments_ids(), [])

    def test_when_customers_appointments_then_returns_ids(self):
        appointment_ids = [100, 200]
        for id_ in appointment_ids:
            baker.make(
                Appointment,
                id=id_,
                type=Appointment.TYPE.CUSTOMER,
                source=self.booking_source,
            )
            baker.make(
                Appointment,
                id=id_ + 1,
                type=Appointment.TYPE.BUSINESS,
                source=self.booking_source,
            )
            baker.make(
                Appointment,
                id=id_ + 2,
                type=Appointment.TYPE.RESERVATION,
                source=self.booking_source,
            )

        self.assertEqual(AppointmentPort.get_customer_appointments_ids(), appointment_ids)


@pytest.mark.django_db
class TestAppointmentPortGetAppointmentStaffersIds(TestCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.staffers = baker.make(Resource, type=Resource.STAFF, _quantity=2)

    def test_basic_booking(self):
        appointment = create_appointment([{'staffer': self.staffers[0]}])
        self.assertEqual(
            AppointmentPort.get_appointment_staffers_ids(appointment.id), [self.staffers[0].id]
        )

    def test_multibooking(self):
        appointment = create_appointment(
            [{'staffer': self.staffers[0]}, {'staffer': self.staffers[1]}],
        )
        self.assertEqual(
            AppointmentPort.get_appointment_staffers_ids(appointment.id),
            [staffer.id for staffer in self.staffers],
        )

    def test_combo_booking(self):
        appointment = create_appointment(
            [
                {
                    'combo_children': [
                        {'staffer': self.staffers[0]},
                        {'staffer': self.staffers[1]},
                    ]
                }
            ],
        )
        self.assertEqual(
            AppointmentPort.get_appointment_staffers_ids(appointment.id),
            [staffer.id for staffer in self.staffers],
        )

    def test_no_staffers(self):
        appointment = create_appointment([])
        self.assertEqual(
            AppointmentPort.get_appointment_staffers_ids(appointment.id),
            [],
        )


@pytest.mark.django_db
class TestAppointmentPortIsAppointmentInactiveStatus(testcases.TestCase):

    @parameterized.expand(
        [
            (Appointment.STATUS.CANCELED, True),
            (Appointment.STATUS.DECLINED, True),
            (Appointment.STATUS.REJECTED, True),
            (Appointment.STATUS.NOSHOW, True),
            (Appointment.STATUS.ACCEPTED, False),
        ]
    )
    def test_check_is_appointment_inactive_status(self, appointment_status, expected_response):
        appointment = baker.make(
            Appointment,
            status=appointment_status,
        )
        self.assertEqual(
            AppointmentPort.is_appointment_in_inactive_status(appointment.id), expected_response
        )

    def test_check_is_appointment_inactive_status_no_appointment(self):
        self.assertEqual(AppointmentPort.is_appointment_in_inactive_status(-1), False)
