import uuid
import typing as t

from domain_services.booking.src.domains.appointment.dto.draft import Draft
from webapps.consts import INTERNAL

# TODO check if BookingSource can be imported in global scope


def get_internal_booking_source():
    from webapps.booking.models import BookingSources

    return BookingSources.get_cached(name=INTERNAL, app_type=BookingSources.INTERNAL_APP)


def is_internal_booking_source(booking_source):
    from webapps.booking.models import BookingSources

    if booking_source is None:
        return False
    return (
        booking_source.name == INTERNAL and booking_source.app_type == BookingSources.INTERNAL_APP
    )


def validate_api_key(
    api_key, request_path, api_key_required, source_name=None
) -> t.<PERSON><PERSON>[bool, t.Optional['BookingSources']]:
    """
    Validate api key from request. Consider this function as rudimental.

    When all views would be migrated from core_api to drf_api this function can be deleted.
    Implementation for validating api_key for drf_api is in drf_api.mixins.BookingSourceMixin.
    """
    from webapps.booking.models import BookingSources

    invalid_api_key = False
    if source_name:
        source = BookingSources.get_cached(
            api_key=api_key,
            name=source_name,
        )
    else:
        source = BookingSources.get_cached(api_key=api_key)
    if source is None:
        if api_key_required or api_key is not None:
            invalid_api_key = True
        else:
            # hack for service.other.website handlers
            source = BookingSources.guess_from_path(request_path)

    # validate app_type
    if source is not None and source.app_type is not BookingSources.UNIVERSAL_APP:
        if '/customer_api/' in request_path and source.app_type not in BookingSources.CUSTOMER_APP:
            invalid_api_key = True
        if '/business_api/' in request_path and source.app_type not in BookingSources.BUSINESS_APP:
            invalid_api_key = True
        if '/internal/' in request_path and source.app_type != BookingSources.INTERNAL_APP:
            invalid_api_key = True
        if '/printer_api/' in request_path and source.app_type != BookingSources.BUSINESS_APP:
            invalid_api_key = True
    # TODO do we need first argument as boolean
    return invalid_api_key, source


def get_draft_by_id(draft_id: uuid.UUID, user_id: int) -> Draft:
    from domain_services.booking.src.domains.appointment.factory import (
        AppointmentServiceFactory,
    )

    return AppointmentServiceFactory.create().get_draft(draft_id=draft_id, user_id=user_id)
