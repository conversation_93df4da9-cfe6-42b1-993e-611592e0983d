import uuid
from datetime import date, datetime, time
from typing import Any

from rest_framework.request import Request

from domain_services.booking.src.domains.appointment.aggregate.draft import (
    DraftAggregate,
)
from domain_services.booking.src.domains.appointment.dto.draft import (
    Draft,
    DraftItem,
    DraftTimeSlot,
)
from domain_services.booking.src.domains.appointment.errors.draft import (
    AddDraftServiceFailedError,
    ChangeDraftStafferFailedError,
    ChangeDraftStafferInvalidStafferError,
    ChangeDraftStafferNoTimeSlotError,
    ChangeDraftTimeslotFailedError,
    CreateDraftFailedError,
    DraftBelongsToAnotherUserError,
    DraftItemNotFoundError,
    RemoveDraftItemFailedError,
    OnlyDraftItemCannotBeRemovedError,
    WrongDraftItemsForReorderError,
    ReorderFailedError,
)
from domain_services.booking.src.domains.calendar.dto import StaffersAvailability
from webapps.booking.enums import SubbookingServiceVariantMode
from webapps.booking.models import BookingSources
from webapps.booking.v2.commons.legacy_api_client import (
    LegacyAggregate,
    LegacyAppointmentDryRunFailedError,
)
from webapps.booking.v2.commons.utils import day_minutes_to_time
from webapps.user.models import User


# pylint: disable=duplicate-code
class LegacyApiDraftAggregate(DraftAggregate, LegacyAggregate):
    def create(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self,
        business_id: int,
        booked_from: datetime,
        service_variant_id: int,
        user: User | None = None,
        fingerprint: str | None = None,
        user_agent: str | None = None,
        booking_source: BookingSources | None = None,
        staffer_id: int | None = None,
        *,
        px_timezone: str | None = None,
        request: Request | None = None,
        **_: Any,
    ) -> Draft:
        user_id = user.id if user else None
        legacy_request_payload = self._legacy_request_payload_for_create(
            business_id=business_id,
            service_variant_id=service_variant_id,
            staffer_id=staffer_id,
            booked_from=booked_from,
        )

        try:
            draft = self._legacy_api_client.execute_appointment_dry_run(
                user_id=user_id,
                business_id=business_id,
                staffer_id=staffer_id,
                request_payload=legacy_request_payload,
                timezone=px_timezone,
                staffer_selection=True,
                **self._extract_monolith_api_params(request),
            )
        except LegacyAppointmentDryRunFailedError as e:
            raise CreateDraftFailedError(str(e)) from e

        return draft

    def claim_draft(
        self,
        draft: Draft,
        user_id: int,
    ) -> Draft:
        if draft.user_id and user_id != draft.user_id:
            raise DraftBelongsToAnotherUserError(draft.id)
        draft.user_id = user_id
        return draft

    def change_staffer(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self,
        draft: Draft,
        draft_item_id: uuid.UUID,
        staffer_id: int,
        staffers_availability: StaffersAvailability,
        user: User | None = None,
        fingerprint: str | None = None,
        user_agent: str | None = None,
        booking_source: BookingSources | None = None,
        *,
        request: Request | None = None,
        **_: Any,
    ) -> Draft:
        item = self.get_item(draft, draft_item_id)
        staffer_id = None if staffer_id == self.NO_STAFFER_PREFERENCE else staffer_id
        if staffer_id and staffer_id not in staffers_availability.staffers_slots:
            raise ChangeDraftStafferInvalidStafferError(item.id, staffer_id)
        item.staffer_id = staffer_id
        item.requested_staffer_id = staffer_id
        if staffer_id:
            staffer_time_slot = staffers_availability.staffers_slots[staffer_id]
            if staffer_time_slot is None:
                raise ChangeDraftStafferNoTimeSlotError(item.id, staffer_id)
            item.time_slot = time_slot = DraftTimeSlot(
                date=staffer_time_slot.date,
                start_time=staffer_time_slot.day_minutes,
            )
            # TODO: Update logic for non-simple bookings
            booked_from = datetime.combine(
                time_slot.date, day_minutes_to_time(time_slot.start_time)
            )
            draft.booked_from = booked_from
            item.booked_from = booked_from
            booked_till = datetime.combine(
                date=time_slot.date,
                time=day_minutes_to_time(time_slot.start_time + item.service.variant.duration),
            )
            item.booked_till = booked_till
            draft.booked_till = booked_till

        legacy_request_payload = self._legacy_request_payload_for_change_staffer(draft=draft)

        try:
            draft = self._legacy_api_client.execute_appointment_dry_run(
                user_id=draft.user_id,
                business_id=draft.business_id,
                draft=draft,
                staffer_id=staffer_id,
                request_payload=legacy_request_payload,
                timezone=draft.px_timezone,
                staffer_selection=True,
                **self._extract_monolith_api_params(request),
            )
        except LegacyAppointmentDryRunFailedError as e:
            raise ChangeDraftStafferFailedError(str(e)) from e

        return draft

    def change_time_slot(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self,
        draft: Draft,
        slot_date: date,
        slot_time: time,
        user: User | None = None,
        fingerprint: str | None = None,
        user_agent: str | None = None,
        booking_source: BookingSources | None = None,
        *,
        request: Request | None = None,
        **_: Any,
    ) -> Draft:
        legacy_request_payload = self._legacy_request_payload_for_change_timeslot(
            draft=draft,
            timeslot=datetime.combine(slot_date, slot_time),
        )

        try:
            draft = self._legacy_api_client.execute_appointment_dry_run(
                user_id=draft.user_id,
                business_id=draft.business_id,
                draft=draft,
                request_payload=legacy_request_payload,
                timezone=draft.px_timezone,
                **self._extract_monolith_api_params(request),
            )
        except LegacyAppointmentDryRunFailedError as e:
            raise ChangeDraftTimeslotFailedError(str(e)) from e

        return draft

    def add_service(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self,
        draft: Draft,
        service_variant_id: int,
        user: User | None = None,
        fingerprint: str | None = None,
        user_agent: str | None = None,
        booking_source: BookingSources | None = None,
        *,
        request: Request | None = None,
        **_: Any,
    ) -> Draft:
        legacy_request_payload = self._legacy_request_payload_for_add_service(
            draft=draft,
            service_variant_id=service_variant_id,
        )

        try:
            new_draft = self._legacy_api_client.execute_appointment_dry_run(
                user_id=draft.user_id,
                business_id=draft.business_id,
                draft=draft,
                request_payload=legacy_request_payload,
                timezone=draft.px_timezone,
                staffer_selection=True,
                **self._extract_monolith_api_params(request),
            )
        except LegacyAppointmentDryRunFailedError as e:
            return self._add_service_with_default_params(
                draft=draft,
                legacy_request_payload=legacy_request_payload,
                error=e,
                request=request,
                new_draft=None,
            )

        if draft.long_wait_time:
            return self._add_service_with_default_params(
                draft=draft,
                legacy_request_payload=legacy_request_payload,
                error=None,
                request=request,
                new_draft=new_draft,
            )

        return new_draft

    def _add_service_with_default_params(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self,
        draft: Draft,
        legacy_request_payload: dict[str, Any],
        error: Exception | None = None,
        request: Request | None = None,
        new_draft: Draft | None = None,
    ) -> Draft:

        if legacy_request_payload['subbookings'][-1]['staffer_id'] == -1:
            if error:
                raise AddDraftServiceFailedError(str(error)) from error
            return new_draft
        # Don't set staffer from previous draft item
        legacy_request_payload['subbookings'][-1]['staffer_id'] = -1
        try:
            draft = self._legacy_api_client.execute_appointment_dry_run(
                user_id=draft.user_id,
                business_id=draft.business_id,
                draft=draft,
                request_payload=legacy_request_payload,
                timezone=draft.px_timezone,
                staffer_selection=True,
                **self._extract_monolith_api_params(request),
            )
        except LegacyAppointmentDryRunFailedError as err:
            raise AddDraftServiceFailedError(str(err)) from err

        return draft

    def reorder(
        self,
        draft: Draft,
        draft_item_ids: list[uuid.UUID],
        *,
        request: Request | None = None,
        **_: Any,
    ) -> Draft:
        draft_items = {item.id: item for item in draft.items}
        if sorted(draft_items) != sorted(draft_item_ids):
            raise WrongDraftItemsForReorderError(draft_item_ids)

        new_draft_items = [draft_items[item_id] for item_id in draft_item_ids]
        legacy_request_payload = self._legacy_request_payload_for_reorder(
            draft=draft,
            new_draft_items=new_draft_items,
        )

        try:
            draft = self._legacy_api_client.execute_appointment_dry_run(
                user_id=draft.user_id,
                business_id=draft.business_id,
                draft=draft,
                request_payload=legacy_request_payload,
                timezone=draft.px_timezone,
                **self._extract_monolith_api_params(request),
            )
        except LegacyAppointmentDryRunFailedError as e:
            raise ReorderFailedError(str(e)) from e

        return draft

    def remove_draft_item(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self,
        draft: Draft,
        draft_item_id: uuid.UUID,
        *,
        request: Request | None = None,
        **_: Any,
    ) -> Draft:

        # Validation that draf item exists and draft is multibooking
        _draft_item = self.get_item(draft, draft_item_id)
        if len(draft.items) == 1:
            raise OnlyDraftItemCannotBeRemovedError(draft_item_id)
        legacy_request_payload = self._legacy_request_payload_for_remove_draft_item(
            draft=draft, draft_item_id=draft_item_id
        )

        try:
            draft = self._legacy_api_client.execute_appointment_dry_run(
                user_id=draft.user_id,
                business_id=draft.business_id,
                draft=draft,
                request_payload=legacy_request_payload,
                timezone=draft.px_timezone,
                **self._extract_monolith_api_params(request),
            )
        except LegacyAppointmentDryRunFailedError as e:
            if draft_item_id != draft.items[0].id:
                raise RemoveDraftItemFailedError(str(e)) from e
            legacy_request_payload = self._legacy_request_payload_for_remove_draft_item(
                draft=draft, draft_item_id=draft_item_id, should_change_booked_from=False
            )
            try:
                draft = self._legacy_api_client.execute_appointment_dry_run(
                    user_id=draft.user_id,
                    business_id=draft.business_id,
                    draft=draft,
                    request_payload=legacy_request_payload,
                    timezone=draft.px_timezone,
                    **self._extract_monolith_api_params(request),
                )
            except LegacyAppointmentDryRunFailedError as err:
                raise RemoveDraftItemFailedError(str(err)) from err

        return draft

    def add_customer_note(
        self,
        draft: Draft,
        customer_note: str,
        **_: Any,
    ) -> Draft:
        draft.customer_note = customer_note
        return draft

    def get_item(
        self,
        draft: Draft,
        draft_item_id: uuid.UUID,
    ) -> DraftItem:
        item = next((item for item in draft.items if item.id == draft_item_id), None)
        if not item:
            raise DraftItemNotFoundError(draft_item_id)
        return item

    def _legacy_request_payload_for_create(
        self,
        business_id: int,
        service_variant_id: int,
        booked_from: datetime,
        staffer_id: int | None = None,
    ) -> dict[str, Any]:
        return {
            'business_id': business_id,
            'subbookings': [
                {
                    'service_variant': {
                        'id': service_variant_id,
                        'mode': SubbookingServiceVariantMode.VARIANT,
                    },
                    'staffer_id': staffer_id if staffer_id is not None else -1,
                    'booked_from': booked_from.isoformat(),
                }
            ],
        }

    def _legacy_request_payload_for_change_staffer(
        self,
        draft: Draft,
    ) -> dict[str, Any]:
        return {
            "business_id": draft.business_id,
            "booked_from": draft.booked_from.replace(tzinfo=None).isoformat(),
            "subbookings": [
                {
                    "booked_from": item.booked_from.replace(tzinfo=None).isoformat(),
                    'service_variant': {
                        'id': item.service.variant.id,
                        'mode': SubbookingServiceVariantMode.VARIANT,
                    },
                    "staffer_id": item.requested_staffer_id or -1,
                }
                for item in draft.items
            ],
        }

    def _legacy_request_payload_for_change_timeslot(
        self,
        draft: Draft,
        timeslot: datetime,
    ) -> dict[str, Any]:
        return {
            "business_id": draft.business_id,
            "booked_from": timeslot.replace(tzinfo=None).isoformat(),
            "subbookings": [
                {
                    "booked_from": timeslot.replace(tzinfo=None).isoformat(),
                    'service_variant': {
                        'id': item.service.variant.id,
                        'mode': SubbookingServiceVariantMode.VARIANT,
                    },
                    "staffer_id": item.requested_staffer_id or -1,
                }
                for item in draft.items
            ],
        }

    def _legacy_request_payload_for_add_service(
        self,
        draft: Draft,
        service_variant_id: int,
    ) -> dict[str, Any]:
        return {
            'business_id': draft.business_id,
            'booked_from': draft.booked_from.replace(tzinfo=None).isoformat(),
            'subbookings': [
                {
                    'booked_from': item.booked_from.replace(tzinfo=None).isoformat(),
                    'service_variant': {
                        'id': item.service.variant.id,
                        'mode': SubbookingServiceVariantMode.VARIANT,
                    },
                    'staffer_id': item.requested_staffer_id or -1,
                }
                for item in draft.items
            ]
            + [
                {
                    'booked_from': draft.booked_from.replace(tzinfo=None).isoformat(),
                    'service_variant': {
                        'id': service_variant_id,
                        'mode': SubbookingServiceVariantMode.VARIANT,
                    },
                    'staffer_id': draft.items[-1].requested_staffer_id or -1,
                }
            ],
        }

    def _legacy_request_payload_for_remove_draft_item(
        self, draft: Draft, draft_item_id: uuid.UUID, should_change_booked_from: bool = True
    ) -> dict[str, Any]:
        result = {
            'business_id': draft.business_id,
            'booked_from': draft.booked_from.replace(tzinfo=None).isoformat(),
            'subbookings': [
                {
                    'booked_from': item.booked_from.replace(tzinfo=None).isoformat(),
                    'service_variant': {
                        'id': item.service.variant.id,
                        'mode': SubbookingServiceVariantMode.VARIANT,
                    },
                    'staffer_id': item.requested_staffer_id or -1,
                }
                for item in draft.items
                if item.id != draft_item_id
            ],
        }
        if should_change_booked_from and draft.items[0].id == draft_item_id:
            result['subbookings'][0]['booked_from'] = result['booked_from']
        return result

    def _legacy_request_payload_for_reorder(
        self,
        draft: Draft,
        new_draft_items: list[DraftItem],
    ) -> dict[str, Any]:
        return {
            'business_id': draft.business_id,
            'booked_from': draft.booked_from.replace(tzinfo=None).isoformat(),
            'subbookings': [
                {
                    'booked_from': item.booked_from.replace(tzinfo=None).isoformat(),
                    'service_variant': {
                        'id': item.service.variant.id,
                        'mode': SubbookingServiceVariantMode.VARIANT,
                    },
                    'staffer_id': item.requested_staffer_id or -1,
                }
                for item in new_draft_items
            ],
        }
