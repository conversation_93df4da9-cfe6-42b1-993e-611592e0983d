import uuid
from datetime import date, timedelta

from django.db import transaction

from domain_services.booking.src.domains.appointment.dto.draft import (
    Draft,
    DraftItem,
    DraftItemService,
    DraftItemServiceVariant,
    DraftTimeSlot,
)
from domain_services.booking.src.domains.appointment.repository import (
    AppointmentInfoRepository,
    DraftRepository,
)
from domain_services.booking.src.domains.calendar.aggregate.types import TimeSlot
from lib.db import DRAFTS_DB, using_db_for_reads
from lib.tools import tznow
from webapps.appointment_drafts.models import (
    AppointmentInfoModel,
    DraftAppointment,
    DraftAppointmentItem,
)
from webapps.booking.v2.commons.utils import timedelta_to_minutes, relativedelta_to_minutes


class PostgresDraftRepository(DraftRepository):
    @using_db_for_reads(DRAFTS_DB)
    def get(self, draft_id: uuid.UUID) -> Draft | None:
        try:
            draft = DraftAppointment.objects.get(id=draft_id)
        except DraftAppointment.DoesNotExist:
            return None

        items = list(
            DraftAppointmentItem.objects.filter(appointment_id=draft_id).order_by(
                'booked_from', 'created'
            )
        )
        duration = timedelta_to_minutes(
            draft.booked_till - draft.booked_from
            if draft.booked_till and draft.booked_from
            else timedelta()
        )

        return Draft(
            id=draft_id,
            version=draft.version,
            user_id=draft.user_id,
            business_id=draft.business_id,
            booked_from=draft.booked_from,
            booked_till=draft.booked_till,
            duration=duration,
            total=draft.total,
            customer_note=draft.customer_note,
            px_timezone=draft.px_timezone,
            items=[
                DraftItem(
                    id=item.id,
                    booked_from=item.booked_from,
                    booked_till=item.booked_till,
                    service=DraftItemService(
                        id=item.service_id,
                        name=item.service_name,
                        price=item.service_price,
                        omnibus_price=item.omnibus_price,
                        variant=DraftItemServiceVariant(
                            id=item.variant_id,
                            name=item.variant_name,
                            duration=item.variant_duration,
                            price=item.price,
                        ),
                    ),
                    staffer_id=item.staffer_id,
                    requested_staffer_id=item.requested_staffer_id,
                    wait_time=relativedelta_to_minutes(item.wait_time) if item.wait_time else 0,
                    time_slot=self._time_slot_to_dto(item.time_slot) if item.time_slot else None,
                    price=item.price,
                    price_before_discount=item.price_before_discount,
                    discount=item.discount,
                )
                for item in items
            ],
        )

    def _time_slot_to_dto(self, time_slot: dict) -> TimeSlot:
        return DraftTimeSlot(
            date=date.fromisoformat(time_slot['date']),
            start_time=time_slot['start_time'],
        )

    @transaction.atomic(using=DRAFTS_DB)
    def insert(self, draft: Draft) -> None:
        DraftAppointment.objects.create(
            id=draft.id,
            version=draft.version,
            user_id=draft.user_id,
            business_id=draft.business_id,
            booked_from=draft.booked_from,
            booked_till=draft.booked_till,
            px_timezone=draft.px_timezone,
            total=draft.total,
        )

        for item in draft.items:
            is_staffer_requested = self._is_staffer_requested(
                item.staffer_id, item.requested_staffer_id
            )

            DraftAppointmentItem.objects.create(
                id=item.id,
                appointment_id=draft.id,
                booked_from=item.booked_from,
                booked_till=item.booked_till,
                service_id=item.service.id,
                service_name=item.service.name,
                service_price=item.service.price,
                variant_id=item.service.variant.id,
                variant_name=item.service.variant.name,
                variant_duration=item.service.variant.duration,
                staffer_id=item.staffer_id,
                requested_staffer_id=item.requested_staffer_id,
                is_staffer_requested_by_client=is_staffer_requested,
                wait_time=timedelta(minutes=item.wait_time),
                time_slot=self._time_slot_to_dict(item.time_slot) if item.time_slot else None,
                price=item.price,
                discount=item.discount,
                price_before_discount=item.price_before_discount,
                omnibus_price=item.service.omnibus_price,
            )

    @transaction.atomic(using=DRAFTS_DB)
    def update(self, draft: Draft) -> None:
        DraftAppointment.objects.filter(
            id=draft.id,
        ).update(
            version=draft.version,
            user_id=draft.user_id,
            business_id=draft.business_id,
            booked_from=draft.booked_from,
            booked_till=draft.booked_till,
            customer_note=draft.customer_note,
            px_timezone=draft.px_timezone,
            total=draft.total,
        )

        for item in draft.items:
            is_staffer_requested = self._is_staffer_requested(
                item.staffer_id, item.requested_staffer_id
            )

            DraftAppointmentItem.objects.update_or_create(
                id=item.id,
                defaults={
                    "appointment_id": draft.id,
                    "booked_from": item.booked_from,
                    "booked_till": item.booked_till,
                    "service_id": item.service.id,
                    "service_name": item.service.name,
                    "service_price": item.service.price,
                    "variant_id": item.service.variant.id,
                    "variant_name": item.service.variant.name,
                    "variant_duration": item.service.variant.duration,
                    "staffer_id": item.staffer_id,
                    "requested_staffer_id": item.requested_staffer_id,
                    "is_staffer_requested_by_client": is_staffer_requested,
                    "wait_time": timedelta(minutes=item.wait_time),
                    "time_slot": (
                        self._time_slot_to_dict(item.time_slot) if item.time_slot else None
                    ),
                    "price": item.price,
                    "price_before_discount": item.price_before_discount,
                    "discount": item.discount,
                    "omnibus_price": item.service.omnibus_price,
                },
            )
        DraftAppointmentItem.objects.filter(appointment_id=draft.id).exclude(
            id__in=[item.id for item in draft.items]
        ).delete()

    @transaction.atomic(using=DRAFTS_DB)
    def remove(self, draft_id: uuid.UUID) -> None:
        DraftAppointmentItem.objects.filter(appointment__id=draft_id).delete()
        DraftAppointment.objects.filter(id=draft_id).delete()

    @using_db_for_reads(DRAFTS_DB)
    def get_oldest_draft_ids(self, older_than: int, limit: int) -> list[uuid.UUID]:
        return list(
            DraftAppointment.objects.filter(updated__lte=tznow() - timedelta(minutes=older_than))
            .order_by('updated')
            .values_list('id', flat=True)[:limit]
        )

    def _time_slot_to_dict(self, time_slot: TimeSlot) -> dict:
        return {
            'date': time_slot.date.isoformat(),
            'start_time': time_slot.start_time,
        }

    def _is_staffer_requested(
        self,
        staffer_id: int | None,
        requested_staffer_id: int | None,
    ) -> bool:
        return requested_staffer_id is not None and staffer_id == requested_staffer_id


class PostgresAppointmentInfoRepository(AppointmentInfoRepository):
    def insert(self, appointment_id: int):
        AppointmentInfoModel.objects.create(appointment_id=appointment_id)
