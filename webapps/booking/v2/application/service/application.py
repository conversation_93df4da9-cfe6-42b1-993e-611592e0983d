import uuid
from dataclasses import dataclass, field
from datetime import date, datetime, time, timedelta
from math import sqrt

from typing import Any, Generator, Iterable
from zoneinfo import ZoneInfo

from ddtrace import tracer
from django.conf import settings
from django.utils.dateparse import parse_duration
from rest_framework.request import Request

from domain_services.booking.src.commons.errors import BusinessNotFoundError
from domain_services.booking.src.commons.types import DayMinutes, Discount, ResourceId
from domain_services.booking.src.domains.analytics.factory.meta_params_factory import (
    ProductAnalyticsParamsFactory,
)
from domain_services.booking.src.domains.analytics.service import AnalyticService
from domain_services.booking.src.domains.appointment.dto.appointment import Appointment
from domain_services.booking.src.domains.appointment.dto.draft import Draft
from domain_services.booking.src.domains.appointment.enums import AppointmentStatusV2
from domain_services.booking.src.domains.appointment.errors.draft import (
    DraftBookAgainBookingNotPossibleError,
    DraftBookAgainError,
    AppointmentNotFoundError,
)
from domain_services.booking.src.domains.appointment.service import AppointmentService
from domain_services.booking.src.domains.business.dto import Business
from domain_services.booking.src.domains.business.service import BusinessService
from domain_services.booking.src.domains.calendar.dto import (
    ComboBooking,
    PossibleSlotsMap,
    SlotDetails,
    TimeSlotsQuery,
)
from domain_services.booking.src.domains.calendar.service import CalendarService
from domain_services.booking.src.domains.staffer.dto import Staffer
from domain_services.booking.src.domains.staffer.service import StafferService
from domain_services.booking.src.domains.user.dto import User as UserDto
from domain_services.booking.src.domains.user.service import UserService
from lib.tools import tznow
from webapps.booking.book_again_v2.book_again_info import (
    BookAgainInfoFactory,
    ObjectNotFoundException,
)
from webapps.booking.models import BookingSources
from webapps.booking.v2.application.enums import CalendarSlotsMarker, DayPeriod
from webapps.booking.v2.application.http_api.response.appointment import (
    AppointmentResponse,
)
from webapps.booking.v2.application.http_api.response.calendar import (
    CalendarResponse,
    TimeSlotsResponse,
)
from webapps.booking.v2.application.http_api.response.draft import DraftResponse
from webapps.booking.v2.application.http_api.response.provider_feature_checklist import (
    ProviderFeatureChecklistResponse,
)
from webapps.booking.v2.application.http_api.response.staffer import (
    DraftServiceStaffersResponse,
    prepare_draft_staffers_response,
)
from webapps.booking.v2.commons.utils import (
    day_minutes_to_time,
    timedelta_to_minutes,
    to_day_minutes,
)
from webapps.booking.v2.targeting.domain.service.targeting import TargetingService
from webapps.user.models import User


def _set_dd_debug_info(  # pylint: disable=too-many-arguments, too-many-positional-arguments
    call_number, start_date, business_id, draft, booking_settings, last_date, first_possible_date
):
    if span := tracer.current_root_span():
        debug_value = (
            f"Start date: {start_date}, "
            f"business_id: {business_id}, "
            f"max lead time: {booking_settings.max_lead_time}, "
            f"min lead time: {booking_settings.min_lead_time}, "
            f"last_date: {last_date}, "
            f"first_possible_date: {first_possible_date}, "
            f"draft: {draft.__dict__}, "
            f"which call: {call_number}"
        )
        span.set_tag("debug_info_about_draft", debug_value)


class FirstAvailableTimeSlotNotFound(Exception):
    def __init__(self) -> None:
        super().__init__("First available time slot not found")


@dataclass
class ApplicationServiceCalendarConfig:
    morning_end_minutes: int = to_day_minutes(
        time.fromisoformat(settings.DAYTIME_THRESHOLDS['morning'][1])
    )
    afternoon_end_minutes: int = to_day_minutes(
        time.fromisoformat(settings.DAYTIME_THRESHOLDS['afternoon'][1])
    )
    slots_marker_limits: dict[CalendarSlotsMarker, int] = field(
        default_factory=lambda: {
            CalendarSlotsMarker.A_FEW: 5,
            CalendarSlotsMarker.A_BIT: 10,
        }
    )


@dataclass
class ApplicationServiceConfig:
    calendar: ApplicationServiceCalendarConfig


# pylint: disable=duplicate-code
class ApplicationService:
    def __init__(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self,
        appointment_service: AppointmentService,
        business_service: BusinessService,
        calendar_service: CalendarService,
        staffer_service: StafferService,
        targeting_service: TargetingService,
        analytic_service: AnalyticService,
        user_service: UserService,
        config: ApplicationServiceConfig,
    ) -> None:
        self._appointment_service = appointment_service
        self._business_service = business_service
        self._calendar_service = calendar_service
        self._staffer_service = staffer_service
        self._targeting_service = targeting_service
        self._analytic_service = analytic_service
        self._user_service = user_service
        self._config = config

    def get_draft(
        self,
        draft_id: uuid.UUID,
        user_id: int | None = None,
        **_: Any,
    ) -> DraftResponse:
        draft = self._appointment_service.get_draft(
            draft_id=draft_id,
            user_id=user_id,
        )
        business = self._business_service.get_business(business_id=draft.business_id)
        staffers = self._fetch_staffers_from_draft(draft)

        return self._create_draft_response(draft, business, staffers)

    def book_again(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self,
        appointment_id: int,
        user: User | None = None,
        fingerprint: str | None = None,
        user_agent: str | None = None,
        booking_source: BookingSources | None = None,
        meta: dict[str, str] | None = None,
        *,
        request: Request | None = None,
        **_: Any,
    ) -> DraftResponse:
        # pylint: disable=protected-access
        business_id = self._appointment_service._appointment_aggregate.get_business_id(
            appointment_id
        )
        if not business_id:
            raise AppointmentNotFoundError(appointment_id)

        business = self._business_service.get_business(business_id=business_id)

        try:
            book_again_info = BookAgainInfoFactory(appointment_id, user.id, business_id).get_info()
        except ObjectNotFoundException as e:
            raise DraftBookAgainError(e) from e

        if not book_again_info.book_again_status.is_booking_possible():
            raise DraftBookAgainBookingNotPossibleError(book_again_info.book_again_status)

        booked_from = self._calculate_booked_from(
            business,
            book_again_info.book_again_appointment.subbookings[0].service_variant.id,
            book_again_info.book_again_appointment.subbookings[0].staffer_id,
        )
        # TODO: handle add-ons & combos when they're supported in SB
        draft = self._appointment_service.create_draft(
            user=user,
            fingerprint=fingerprint,
            user_agent=user_agent,
            booking_source=booking_source,
            business_id=business_id,
            service_variant_id=book_again_info.book_again_appointment.subbookings[
                0
            ].service_variant.id,
            staffer_id=book_again_info.book_again_appointment.subbookings[0].staffer_id,
            booked_from=booked_from,
            request=request,
        )

        for subbooking in book_again_info.book_again_appointment.subbookings[1:]:
            draft = self._appointment_service.add_service(
                draft_id=draft.id,
                version=draft.version,
                service_variant_id=subbooking.service_variant.id,
                staffer_id=subbooking.staffer_id,
                user=user,
                fingerprint=fingerprint,
                user_agent=user_agent,
                booking_source=booking_source,
                request=request,
            )

        staffers = self._fetch_staffers_from_draft(draft)
        product_analytics_params = ProductAnalyticsParamsFactory.create(
            meta, business=business, user_id=user.id, draft=draft
        )
        bci = self._business_service.get_business_customer_info_for_user(
            business_id=business_id, user_id=user.id
        )
        self._analytic_service.send_draft_created(
            product_analytics_params,
            user=(
                UserDto(
                    id=user.id,
                    locale=user.customer_profile.language,
                    first_name=user.first_name,
                    last_name=user.last_name,
                    gender=user.gender,
                )
                if user
                else None
            ),
            business=business,
            draft=draft,
            business_customer_info=bci,
        )

        return self._create_draft_response(draft, business, staffers)

    def create_draft(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self,
        business_id: int,
        service_variant_id: int,
        staffer_id: int | None = None,
        booked_from: datetime | None = None,
        user: User | None = None,
        fingerprint: str | None = None,
        user_agent: str | None = None,
        booking_source: BookingSources | None = None,
        meta: dict[str, str] | None = None,
        *,
        request: Request | None = None,
        **_: Any,
    ) -> DraftResponse:
        user_id = user.id if user else None
        business = self._business_service.get_business(business_id=business_id)

        booked_from = (
            booked_from.replace(tzinfo=ZoneInfo(key=business.timezone))
            if booked_from
            else self._calculate_booked_from(business, service_variant_id, staffer_id)
        )
        draft = self._appointment_service.create_draft(
            user=user,
            fingerprint=fingerprint,
            user_agent=user_agent,
            booking_source=booking_source,
            business_id=business_id,
            service_variant_id=service_variant_id,
            staffer_id=staffer_id,
            booked_from=booked_from,
            request=request,
            timezone=business.timezone,
        )

        staffers = self._fetch_staffers_from_draft(draft)
        product_analytics_params = ProductAnalyticsParamsFactory.create(
            meta, business=business, user_id=user_id, draft=draft
        )
        bci = self._business_service.get_business_customer_info_for_user(
            business_id=business_id, user_id=user_id
        )
        self._analytic_service.send_draft_created(
            product_analytics_params,
            user=(
                UserDto(
                    id=user.id,
                    locale=user.customer_profile.language,
                    first_name=user.first_name,
                    last_name=user.last_name,
                    gender=user.gender,
                )
                if user
                else None
            ),
            business=business,
            draft=draft,
            business_customer_info=bci,
        )

        return self._create_draft_response(draft, business, staffers)

    def get_staffers(  # pylint: disable=too-many-arguments
        self,
        draft_id: uuid.UUID,
        draft_item_id: uuid.UUID,
        user_id: int | None = None,
        **_: Any,
    ) -> DraftServiceStaffersResponse:
        staffers_availability = self._appointment_service.check_staffers_availability(
            draft_id=draft_id,
            draft_item_id=draft_item_id,
            user_id=user_id,
        )
        staffers = self._staffer_service.get_staffers(list(staffers_availability.staffers_slots))

        return prepare_draft_staffers_response(staffers, staffers_availability)

    def change_staffer(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self,
        draft_id: uuid.UUID,
        version: uuid.UUID,
        draft_item_id: uuid.UUID,
        staffer_id: int,
        user: User | None = None,
        fingerprint: str | None = None,
        user_agent: str | None = None,
        booking_source: BookingSources | None = None,
        *,
        request: Request | None = None,
        **_: Any,
    ) -> DraftResponse:
        user_id = user.id if user else None
        draft = self._appointment_service.get_draft(draft_id=draft_id, user_id=user_id)
        business = self._business_service.get_business(business_id=draft.business_id)
        draft = self._appointment_service.change_staffer(
            user=user,
            fingerprint=fingerprint,
            user_agent=user_agent,
            booking_source=booking_source,
            draft_id=draft_id,
            version=version,
            draft_item_id=draft_item_id,
            staffer_id=staffer_id,
            request=request,
            timezone=business.timezone,
        )
        staffers = self._fetch_staffers_from_draft(draft)

        return self._create_draft_response(draft, business, staffers)

    def change_time_slot(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self,
        draft_id: uuid.UUID,
        version: uuid.UUID,
        slot_date: date,
        slot_time: time,
        user: User | None = None,
        fingerprint: str | None = None,
        user_agent: str | None = None,
        booking_source: BookingSources | None = None,
        *,
        request: Request | None = None,
        **_: Any,
    ) -> DraftResponse:
        user_id = user.id if user else None
        draft = self._appointment_service.get_draft(draft_id=draft_id, user_id=user_id)
        business = self._business_service.get_business(business_id=draft.business_id)
        draft = self._appointment_service.change_time_slot(
            user=user,
            fingerprint=fingerprint,
            user_agent=user_agent,
            booking_source=booking_source,
            draft_id=draft_id,
            version=version,
            slot_date=slot_date,
            slot_time=slot_time,
            request=request,
            timezone=business.timezone,
        )
        staffers = self._fetch_staffers_from_draft(draft)

        return self._create_draft_response(draft, business, staffers)

    def set_first_available_timeslot(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self,
        draft_id: uuid.UUID,
        version: uuid.UUID,
        start_date: date | None,
        user: User | None = None,
        fingerprint: str | None = None,
        user_agent: str | None = None,
        booking_source: BookingSources | None = None,
        *,
        request: Request | None = None,
        **_: Any,
    ):
        user_id = user.id if user else None
        draft = self._appointment_service.get_draft(draft_id=draft_id, user_id=user_id)
        business = self._business_service.get_business(business_id=draft.business_id)
        booking_settings = business.booking_settings
        now_ = tznow()
        last_date = (now_ + booking_settings.max_lead_time).date()
        first_possible_date = (now_ + booking_settings.min_lead_time).date()

        if start_date and start_date < first_possible_date:
            start_date = first_possible_date

        if start_date and start_date > last_date:
            _set_dd_debug_info(
                1, start_date, business.id, draft, booking_settings, last_date, first_possible_date
            )
            raise FirstAvailableTimeSlotNotFound

        if not start_date:
            start_date = first_possible_date

        appointment_item = draft.items[0]
        possible_slots = self._find_first_slot(
            start_date=start_date,
            last_date=last_date,
            business_id=business.id,
            service_variant_id=appointment_item.service.variant.id,
            staffer_id=appointment_item.staffer_id,
        )
        if not possible_slots:
            _set_dd_debug_info(
                2, start_date, business.id, draft, booking_settings, last_date, first_possible_date
            )
            raise FirstAvailableTimeSlotNotFound

        date_ = next(iter(possible_slots.keys()))
        time_ = day_minutes_to_time(next(iter(possible_slots[date_].keys())))

        draft = self._appointment_service.change_time_slot(
            user=user,
            fingerprint=fingerprint,
            user_agent=user_agent,
            booking_source=booking_source,
            draft_id=draft_id,
            version=version,
            slot_date=date_,
            slot_time=time_,
            request=request,
            timezone=business.timezone,
        )
        staffers = self._fetch_staffers_from_draft(draft)

        return self._create_draft_response(draft, business, staffers)

    def add_service(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self,
        draft_id: uuid.UUID,
        version: uuid.UUID,
        service_variant_id: int | None,
        user: User | None = None,
        fingerprint: str | None = None,
        user_agent: str | None = None,
        booking_source: BookingSources | None = None,
        *,
        request: Request | None = None,
        **_: Any,
    ) -> DraftResponse:
        user_id = user.id if user else None
        draft = self._appointment_service.get_draft(draft_id=draft_id, user_id=user_id)
        business = self._business_service.get_business(business_id=draft.business_id)
        draft = self._appointment_service.add_service(
            user=user,
            fingerprint=fingerprint,
            user_agent=user_agent,
            booking_source=booking_source,
            draft_id=draft_id,
            version=version,
            service_variant_id=service_variant_id,
            request=request,
            timezone=business.timezone,
        )
        staffers = self._fetch_staffers_from_draft(draft)

        return self._create_draft_response(draft, business, staffers)

    def reorder(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self,
        draft_id: uuid.UUID,
        version: uuid.UUID,
        draft_item_ids: list[uuid.UUID],
        user: User | None = None,
        fingerprint: str | None = None,
        user_agent: str | None = None,
        booking_source: BookingSources | None = None,
        *,
        request: Request | None = None,
        **_: Any,
    ) -> DraftResponse:
        user_id = user.id if user else None
        draft = self._appointment_service.get_draft(draft_id=draft_id, user_id=user_id)
        business = self._business_service.get_business(business_id=draft.business_id)
        draft = self._appointment_service.reorder(
            user=user,
            fingerprint=fingerprint,
            user_agent=user_agent,
            booking_source=booking_source,
            draft_id=draft_id,
            version=version,
            draft_item_ids=draft_item_ids,
            request=request,
            timezone=business.timezone,
        )
        staffers = self._fetch_staffers_from_draft(draft)

        return self._create_draft_response(draft, business, staffers)

    def remove_draft_item(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self,
        draft_id: uuid.UUID,
        version: uuid.UUID,
        draft_item_id: uuid.UUID,
        user: User | None = None,
        fingerprint: str | None = None,
        user_agent: str | None = None,
        booking_source: BookingSources | None = None,
        *,
        request: Request | None = None,
        **_: Any,
    ) -> DraftResponse:
        user_id = user.id if user else None
        draft = self._appointment_service.get_draft(draft_id=draft_id, user_id=user_id)
        business = self._business_service.get_business(business_id=draft.business_id)
        draft = self._appointment_service.remove_draft_item(
            user=user,
            fingerprint=fingerprint,
            user_agent=user_agent,
            booking_source=booking_source,
            draft_id=draft_id,
            version=version,
            draft_item_id=draft_item_id,
            request=request,
            timezone=business.timezone,
        )
        staffers = self._fetch_staffers_from_draft(draft)

        return self._create_draft_response(draft, business, staffers)

    def get_time_slots(  # pylint: disable=too-many-arguments
        self,
        draft_id: uuid.UUID,
        start: date,
        end: date,
        user_id: int | None = None,
        **_: Any,
    ) -> TimeSlotsResponse:
        draft = self._appointment_service.get_draft(
            user_id=user_id,
            draft_id=draft_id,
        )
        possible_slots = self._calendar_service.find_time_slots(
            time_slots_query=TimeSlotsQuery(
                business_id=draft.business_id,
                start_date=start,
                end_date=end,
                bookings=[
                    ComboBooking(
                        service_variant_id=item.service.variant.id,
                        staffer_id=item.staffer_id,
                        addons=[],
                        combo_children=[],
                    )
                    for item in draft.items
                ],
            ),
        )

        return self._create_time_slots_response(possible_slots)

    def get_calendar(  # pylint: disable=too-many-arguments
        self,
        draft_id: uuid.UUID,
        start: date,
        end: date,
        user_id: int | None = None,
        **_: Any,
    ) -> CalendarResponse:
        draft = self._appointment_service.get_draft(
            user_id=user_id,
            draft_id=draft_id,
        )
        possible_slots = self._calendar_service.find_time_slots(
            time_slots_query=TimeSlotsQuery(
                business_id=draft.business_id,
                start_date=start,
                end_date=end,
                bookings=[
                    ComboBooking(
                        service_variant_id=item.service.variant.id,
                        staffer_id=item.staffer_id,
                        addons=[],
                        combo_children=[],
                    )
                    for item in draft.items
                ],
            ),
            include_empty_days=True,
        )

        return self._create_calendar_response(
            possible_slots=possible_slots,
        )

    def schedule(  # pylint: disable=too-many-arguments
        self,
        draft_id: uuid.UUID,
        user_id: int | None = None,
        meta: dict[str, str] | None = None,
        *,
        request: Request | None = None,
        **_: Any,
    ) -> AppointmentResponse:
        draft = self._appointment_service.get_draft(draft_id=draft_id, user_id=user_id)
        business = self._business_service.get_business(draft.business_id)

        appointment = self._appointment_service.schedule(
            user_id=user_id,
            draft_id=draft_id,
            request=request,
            timezone=business.timezone,
        )
        product_analytics_params = ProductAnalyticsParamsFactory.create(
            meta, business=business, draft=draft, user_id=user_id, appointment_id=appointment.id
        )
        user = self._user_service.get_user(user_id)
        self._analytic_service.send_appointment_created(product_analytics_params, user=user)

        return self._create_appointment_response(appointment)

    def add_customer_note(  # pylint: disable=too-many-arguments
        self,
        draft_id: uuid.UUID,
        version: uuid.UUID,
        user_id: int | None = None,
        customer_note: str | None = None,
        **_: Any,
    ) -> DraftResponse:
        draft = self._appointment_service.add_customer_note(
            user_id=user_id,
            draft_id=draft_id,
            customer_note=customer_note,
            version=version,
        )
        business = self._business_service.get_business(business_id=draft.business_id)
        staffers = self._fetch_staffers_from_draft(draft)

        return self._create_draft_response(draft, business, staffers)

    def claim_draft(  # pylint: disable=too-many-arguments
        self,
        draft_id: uuid.UUID,
        user_id: int,
        version: uuid.UUID,
        **_: Any,
    ) -> DraftResponse:
        draft = self._appointment_service.claim_draft(
            user_id=user_id, draft_id=draft_id, version=version
        )
        business = self._business_service.get_business(business_id=draft.business_id)
        staffers = self._fetch_staffers_from_draft(draft)

        return self._create_draft_response(draft, business, staffers)

    def provider_feature_checklist(self, provider_id: int) -> ProviderFeatureChecklistResponse:
        provider_feature_checklist = self._targeting_service.provider_feature_checklist(provider_id)
        if not provider_feature_checklist:
            raise BusinessNotFoundError(provider_id)

        return ProviderFeatureChecklistResponse(
            {
                "feature_checklist": {
                    "forwarded": provider_feature_checklist.forwarded,
                    "physio": provider_feature_checklist.physio,
                    "network": provider_feature_checklist.network,
                    "umbrella_venue": provider_feature_checklist.umbrella_venue,
                    "has_combo_services": provider_feature_checklist.has_combo_services,
                    "has_mobile_services": provider_feature_checklist.has_mobile_services,
                    "has_online_services": provider_feature_checklist.has_online_services,
                    "has_addons": provider_feature_checklist.has_addons,
                    "has_payments": provider_feature_checklist.has_payments,
                    "has_questions": provider_feature_checklist.has_questions,
                    "has_custom_forms": provider_feature_checklist.has_custom_forms,
                }
            }
        )

    def _calculate_booked_from(
        self, business: Business, service_variant_id: int, staffer_id: int | None = None
    ) -> datetime:
        booking_settings = business.booking_settings
        now = tznow(tz=business.timezone)
        start_date = (now + booking_settings.min_lead_time).date()
        last_date = (now + booking_settings.max_lead_time).date()
        possible_slots = self._find_first_slot(
            start_date=start_date,
            last_date=last_date,
            business_id=business.id,
            service_variant_id=service_variant_id,
            staffer_id=staffer_id,
        )
        if not possible_slots:
            raise FirstAvailableTimeSlotNotFound

        date_ = next(iter(possible_slots.keys()))
        time_ = day_minutes_to_time(next(iter(possible_slots[date_].keys())))
        booked_from = datetime.combine(date_, time_).replace(tzinfo=ZoneInfo(key=business.timezone))

        return booked_from

    def _fetch_staffers_from_draft(
        self,
        draft: Draft,
    ) -> list[Staffer]:
        staffers = [
            self._staffer_service.get_staffer(item.staffer_id) if item.staffer_id else None
            for item in draft.items
        ]
        return staffers

    def _create_draft_response(
        self,
        draft: Draft,
        business: Business,
        staffers: list[Staffer],
    ) -> DraftResponse:
        return DraftResponse(
            {
                "appointment": {
                    "id": draft.id,
                    "version": draft.version,
                    "status": AppointmentStatusV2.DRAFT,
                    "business": {
                        "id": business.id,
                        "name": business.name,
                    },
                    "booked_from": draft.booked_from,
                    "booked_till": draft.booked_till,
                    "duration": draft.duration,
                    "total": draft.total if draft.total is not None else "",
                    "core_id": None,
                    "customer_note": draft.customer_note,
                    "long_wait_time": draft.long_wait_time,
                    "subbookings": [
                        {
                            "id": item.id,
                            "service": {
                                "id": item.service.id,
                                "name": item.service.name,
                                "price": item.service.price,
                                "price_before_discount": item.price_before_discount,
                                "omnibus_price": item.service.omnibus_price,
                                "variant": {
                                    "id": item.service.variant.id,
                                    "name": item.service.variant.name,
                                    "duration": item.service.variant.duration,
                                },
                            },
                            "staffer": (
                                {
                                    "id": staffer.id,
                                    "name": staffer.name,
                                    "photo_url": staffer.photo_url,
                                }
                                if staffer
                                else None
                            ),
                            "booked_from": item.booked_from,
                            "booked_till": item.booked_till,
                            "wait_time": item.wait_time,
                        }
                        for item, staffer in zip(draft.items, staffers)
                    ],
                },
                "suggestions": {},
                "modifications": {},
            },
        )

    def _create_appointment_response(self, appointment: Appointment) -> AppointmentResponse:
        business = self._business_service.get_business(business_id=appointment.business_id)
        staffers_map = {
            staffer.id: {
                "id": staffer.id,
                "name": staffer.name,
                "photo_url": staffer.photo_url,
            }
            for staffer in self._staffer_service.get_staffers(
                filter(
                    None,
                    (item.staffer_id for item in appointment.items),
                ),
            )
        }
        return AppointmentResponse(
            {
                "appointment": {
                    "id": appointment.id,
                    "version": None,
                    "status": appointment.status,
                    "business": {
                        "id": business.id,
                        "name": business.name,
                    },
                    "booked_from": appointment.booked_from,
                    "booked_till": appointment.booked_till,
                    "duration": appointment.duration,
                    "total": appointment.total if appointment.total is not None else "",
                    "core_id": appointment.core_id,
                    "customer_note": appointment.customer_note,
                    "long_wait_time": appointment.long_wait_time,
                    "subbookings": [
                        {
                            "id": item.id,
                            "service": {
                                "id": item.service.id,
                                "name": item.service.name,
                                "price": item.service.price,
                                "price_before_discount": item.price_before_discount,
                                "omnibus_price": item.service.omnibus_price,
                                "variant": {
                                    "id": item.service.variant.id,
                                    "name": item.service.variant.name,
                                    "duration": item.service.variant.duration,
                                },
                            },
                            "staffer": staffers_map.get(item.staffer_id),
                            "booked_from": item.booked_from,
                            "booked_till": item.booked_till,
                            "wait_time": timedelta_to_minutes(
                                parse_duration(item.wait_time) if item.wait_time else timedelta(0)
                            ),
                        }
                        for item in appointment.items
                    ],
                },
                "suggestions": {},
                "modifications": {},
            },
        )

    def _create_time_slots_response(self, possible_slots: PossibleSlotsMap) -> TimeSlotsResponse:
        return TimeSlotsResponse(
            {
                'timeslots': {
                    _date.isoformat(): [
                        {
                            't': day_minutes_to_time(day_minutes),
                            'p': discount,
                            'l': day_period,
                        }
                        for day_period, day_minutes, _, discount in self._get_day_period_slots(
                            slots.items()
                        )
                    ]
                    for _date, slots in possible_slots.items()
                },
            }
        )

    def _get_day_period_slots(
        self,
        day_slots: Iterable[tuple[DayMinutes, SlotDetails]],
    ) -> Generator[tuple[DayPeriod, DayMinutes, set[ResourceId], Discount | None], None, None]:
        return (
            (
                self._get_day_period(day_minutes),
                day_minutes,
                slot_details.resources,
                slot_details.discount,
            )
            for day_minutes, slot_details in day_slots
        )

    def _get_day_period(self, day_minutes: DayMinutes) -> DayPeriod:
        if day_minutes < self._config.calendar.morning_end_minutes:
            return DayPeriod.MORNING
        if day_minutes < self._config.calendar.afternoon_end_minutes:
            return DayPeriod.AFTERNOON
        return DayPeriod.EVENING

    def _create_calendar_response(
        self,
        possible_slots: PossibleSlotsMap,
    ) -> CalendarResponse:
        calendar = {}

        for date_, slots in possible_slots.items():
            no_preference_calendar = {
                'working': (
                    False if slots is None else True  # pylint: disable=simplifiable-if-expression
                ),
            }

            formated_date = date_.isoformat()
            calendar[formated_date] = no_preference_calendar

            if not slots:
                continue

            no_preference_calendar['slots_marker'] = self._as_calendar_slots_marker(len(slots))
            for day_period, _, _, _ in self._get_day_period_slots(slots.items()):
                no_preference_calendar[day_period] = True

        return CalendarResponse(
            {
                'calendar': calendar,
            }
        )

    def _as_calendar_slots_marker(self, number_of_slots: int) -> CalendarSlotsMarker:
        if number_of_slots <= self._config.calendar.slots_marker_limits[CalendarSlotsMarker.A_FEW]:
            return CalendarSlotsMarker.A_FEW

        if number_of_slots <= self._config.calendar.slots_marker_limits[CalendarSlotsMarker.A_BIT]:
            return CalendarSlotsMarker.A_BIT

        return CalendarSlotsMarker.A_LOT

    def _find_first_slot(  # pylint: disable=too-many-arguments,too-many-positional-arguments
        self,
        start_date: date,
        last_date: date,
        business_id: int,
        service_variant_id: int,
        staffer_id: int,
    ) -> PossibleSlotsMap:
        days_to_check = self._days_in_the_future_to_check_when_finding_first_available_time_slot(
            start=start_date,
            end=last_date,
        )

        loop_start_date = start_date
        for day_to_check in days_to_check:
            loop_end_date = start_date + timedelta(days=day_to_check)
            possible_slots = self._calendar_service.find_first_free_time_slot(
                time_slots_query=TimeSlotsQuery(
                    business_id=business_id,
                    start_date=loop_start_date,
                    end_date=loop_end_date,
                    bookings=[
                        ComboBooking(
                            service_variant_id=service_variant_id,
                            staffer_id=staffer_id,
                            addons=[],
                            combo_children=[],
                        ),
                    ],
                ),
            )
            if possible_slots:
                return possible_slots

            loop_start_date = loop_end_date + timedelta(days=1)

        return {}

    def _days_in_the_future_to_check_when_finding_first_available_time_slot(
        self,
        start: date,
        end: date,
    ) -> list[int]:
        days_delta = (end - start).days

        if days_delta < 0:
            return [0]

        days_delta_sqrt = sqrt(days_delta)

        # E.g.
        # days_delta = 20
        # days_delta_sqrt = 4.47
        # days_to_check = [0, 1, 4, 9, 16]
        days_to_check = [x**2 for x in range(int(days_delta_sqrt) + 1)]

        # Make sure the last possible day in included
        if days_to_check[-1] != days_delta:
            days_to_check.append(days_delta)

        return days_to_check
