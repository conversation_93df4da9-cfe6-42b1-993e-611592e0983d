import uuid
from datetime import date, datetime, time

from dateutil.relativedelta import relativedelta
import freezegun
from django.http.response import HttpResponse
from django.urls import reverse
from rest_framework import status
from webapps.booking.baker_recipes import appointment_recipe, booking_recipe
from webapps.appointment_drafts.models import (
    DraftAppointment,
    DraftAppointmentItem,
)
from webapps.booking.v2.application.http_api.tests.utils.decorator import tornado_in_drf
from webapps.booking.v2.application.http_api.tests.utils.draft_test_case import (
    TZ,
    DraftTestCase,
)
from webapps.business.baker_recipes import (
    bci_recipe,
    business_recipe,
    service_recipe,
    service_variant_recipe,
    staffer_recipe,
    treatment_recipe,
)
from webapps.business.models.models import Business
from webapps.user.baker_recipes import user_recipe

DATE = date(2024, 2, 12)
DATETIME = datetime.combine(DATE, time(12, 0), tzinfo=TZ)


@tornado_in_drf()
class TestBookAgainDraft(DraftTestCase):

    @staticmethod
    def analytics_params():
        return {
            "booking_source": "instagram",
            "traffic_source": "some traffic source",
            "traffic_channel": "some traffic channel",
            "traffic_initial_referrer": "some initial referer",
            "traffic_referrer": "some traffic referrer",
            "task_id": "MessageBlastNotification, 39",
            "platform": "some platform",
            "app_version": "3.14",
            "device_type": "iOS",
            "appointment_id": "123",
        }

    def _send_request(
        self,
        appointment_id: int,
        meta: dict | None = None,
        headers: dict | None = None,
    ) -> HttpResponse:
        url = reverse('book_again_draft')
        request_payload = {
            'appointment_id': appointment_id,
        }
        if meta:
            request_payload['meta'] = meta

        return self.client.post(
            url,
            data=request_payload,
            headers=headers,
        )

    @freezegun.freeze_time(DATETIME)
    def test__book_again_draft__not_logged_user(self) -> None:
        self.client.credentials(HTTP_X_API_KEY=self.booking_source.api_key)
        fingerprint = str(uuid.uuid4())
        headers = {
            'X-Fingerprint': fingerprint,
        }
        response = self._send_request(headers=headers, appointment_id=123)

        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    @freezegun.freeze_time(DATETIME)
    def test__book_again_draft__single_service(self) -> None:
        subbooking = booking_recipe.make(
            service_variant=self.variant,
            resources=[self.staffer],
            appointment=appointment_recipe.make(
                business=self.business,
                booked_for=bci_recipe.make(user=self.user),
            ),
            _save_kwargs=dict(override=True),
        )

        response = self._send_request(appointment_id=subbooking.appointment.id)

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        appointment_response = self.get_appointment_response()
        appointment_response['booked_from'] = '2024-02-12T12:45'
        appointment_response['booked_till'] = '2024-02-12T13:00'
        appointment_response['subbookings'][0]['booked_from'] = '2024-02-12T12:45'
        appointment_response['subbookings'][0]['booked_till'] = '2024-02-12T13:00'
        appointment_response['subbookings'][0]['service']['omnibus_price'] = None
        appointment_response['subbookings'][0]['service']['price_before_discount'] = None
        self.assertDictEqual(response.data, self.get_response(appointment_response))
        assert DraftAppointmentItem.objects.count() == 1
        subbooking = DraftAppointmentItem.objects.first()
        assert subbooking.variant_id == self.variant.id
        assert subbooking.staffer_id == self.staffer.id
        assert subbooking.requested_staffer_id is self.staffer.id
        assert subbooking.is_staffer_requested_by_client is True
        draft = DraftAppointment.objects.get(id=response.data['appointment']['id'])
        assert draft.version == appointment_response['version']

    @freezegun.freeze_time(DATETIME)
    def test__book_again_draft__multiple_services(self) -> None:
        appointment = appointment_recipe.make(
            business=self.business,
            booked_for=bci_recipe.make(user=self.user),
        )
        hour_long_service_variant = service_variant_recipe.make(
            service=self.service,
            resources=[self.staffer],
            duration=relativedelta(hours=1),
        )
        booking_recipe.make(
            service_variant=hour_long_service_variant,
            resources=[self.staffer],
            appointment=appointment,
            _save_kwargs=dict(override=True),
        )
        booking_recipe.make(
            service_variant=self.variant,
            resources=[self.staffer],
            appointment=appointment,
            _save_kwargs=dict(override=True),
        )

        response = self._send_request(appointment_id=appointment.id)
        appointment_response = self.get_appointment_response()
        appointment_response['booked_from'] = '2024-02-12T12:45'
        appointment_response['booked_till'] = '2024-02-12T14:00'
        appointment_response['duration'] = '1h 15min'
        appointment_response['total'] = '$30.00'
        appointment_response['subbookings'] = [
            self.get_draft_item_response(),
            self.get_draft_item_response(),
        ]
        appointment_response['subbookings'][0]['booked_from'] = '2024-02-12T12:45'
        appointment_response['subbookings'][0]['booked_till'] = '2024-02-12T13:45'
        appointment_response['subbookings'][0]['service']['omnibus_price'] = None
        appointment_response['subbookings'][0]['service']['price_before_discount'] = None
        appointment_response['subbookings'][0]['service']['variant']['duration'] = '1h'
        appointment_response['subbookings'][0]['service']['variant'][
            'id'
        ] = hour_long_service_variant.id
        appointment_response['subbookings'][0]['service']['variant'][
            'name'
        ] = hour_long_service_variant.label
        appointment_response['subbookings'][1]['booked_from'] = '2024-02-12T13:45'
        appointment_response['subbookings'][1]['booked_till'] = '2024-02-12T14:00'
        appointment_response['subbookings'][1]['service']['omnibus_price'] = None
        appointment_response['subbookings'][1]['service']['price_before_discount'] = None

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

        self.assertDictEqual(response.data, self.get_response(appointment_response))
        assert DraftAppointmentItem.objects.count() == 2
        assert DraftAppointmentItem.objects.filter(
            variant_id=hour_long_service_variant.id,
        ).exists()
        assert DraftAppointmentItem.objects.filter(
            variant_id=self.variant.id,
        ).exists()

        draft = DraftAppointment.objects.get(id=response.data['appointment']['id'])
        assert draft.version == appointment_response['version']

    @freezegun.freeze_time(DATETIME)
    def test__book_again_draft__multibooking_but_one_service_is_unavailable(self) -> None:
        appointment = appointment_recipe.make(
            business=self.business,
            booked_for=bci_recipe.make(user=self.user),
        )
        unavailable_service_variant = service_variant_recipe.make(
            service=self.service,
            active=False,
            resources=[self.staffer],
            duration=relativedelta(hours=1),
        )
        booking_recipe.make(
            service_variant=unavailable_service_variant,
            resources=[self.staffer],
            appointment=appointment,
            _save_kwargs=dict(override=True),
        )
        booking_recipe.make(
            service_variant=self.variant,
            resources=[self.staffer],
            appointment=appointment,
            _save_kwargs=dict(override=True),
        )

        response = self._send_request(appointment_id=appointment.id)
        appointment_response = self.get_appointment_response()
        appointment_response['booked_from'] = '2024-02-12T12:45'
        appointment_response['booked_till'] = '2024-02-12T13:00'
        appointment_response['subbookings'][0]['booked_from'] = '2024-02-12T12:45'
        appointment_response['subbookings'][0]['booked_till'] = '2024-02-12T13:00'
        appointment_response['subbookings'][0]['service']['omnibus_price'] = None
        appointment_response['subbookings'][0]['service']['price_before_discount'] = None

        assert response.status_code == status.HTTP_201_CREATED, response.data
        assert response.data == self.get_response(appointment_response)

        assert DraftAppointmentItem.objects.count() == 1
        subbooking = DraftAppointmentItem.objects.first()
        assert subbooking.variant_id == self.variant.id
        assert subbooking.staffer_id == self.staffer.id
        assert subbooking.requested_staffer_id is self.staffer.id
        assert subbooking.is_staffer_requested_by_client is True
        draft = DraftAppointment.objects.get(id=response.data['appointment']['id'])
        assert draft.version == appointment_response['version']

    @freezegun.freeze_time(DATETIME)
    def test__book_again_draft__multibooking_all_services_are_unavailable(self) -> None:
        appointment = appointment_recipe.make(
            business=self.business,
            booked_for=bci_recipe.make(user=self.user),
        )
        unavailable_service_variant = service_variant_recipe.make(
            service=self.service,
            active=False,
            resources=[self.staffer],
            duration=relativedelta(hours=1),
        )
        unavailable_service_variant_2 = service_variant_recipe.make(
            service=self.service,
            active=False,
            resources=[self.staffer],
            duration=relativedelta(hours=1),
        )
        booking_recipe.make(
            service_variant=unavailable_service_variant,
            resources=[self.staffer],
            appointment=appointment,
            _save_kwargs=dict(override=True),
        )
        booking_recipe.make(
            service_variant=unavailable_service_variant_2,
            resources=[self.staffer],
            appointment=appointment,
            _save_kwargs=dict(override=True),
        )

        response = self._send_request(appointment_id=appointment.id)

        assert response.status_code == status.HTTP_400_BAD_REQUEST, response.data
        assert response.data == {
            "errors": [
                {
                    "description": "Staffer or service no longer available",
                    "code": "book-again-not-possible-for-appointment",
                    'type': None,
                    'field': None,
                }
            ]
        }

    @freezegun.freeze_time(DATETIME)
    def test__book_again_draft__single_service_but_staffer_is_unavailable(self) -> None:
        unavailable_staffer = staffer_recipe.make(
            active=False,
        )
        service_variant = service_variant_recipe.make(
            service=self.service,
            resources=[self.staffer, unavailable_staffer],
        )
        subbooking = booking_recipe.make(
            service_variant=service_variant,
            resources=[unavailable_staffer],
            appointment=appointment_recipe.make(
                business=self.business,
                booked_for=bci_recipe.make(user=self.user),
            ),
            _save_kwargs=dict(override=True),
        )

        response = self._send_request(appointment_id=subbooking.appointment.id)

        assert response.status_code == status.HTTP_201_CREATED, response.data
        appointment_response = self.get_appointment_response()
        appointment_response['booked_from'] = '2024-02-12T12:45'
        appointment_response['booked_till'] = '2024-02-12T13:00'
        appointment_response['subbookings'][0]['booked_from'] = '2024-02-12T12:45'
        appointment_response['subbookings'][0]['booked_till'] = '2024-02-12T13:00'
        appointment_response['subbookings'][0]['service']['omnibus_price'] = None
        appointment_response['subbookings'][0]['service']['price_before_discount'] = None
        appointment_response['subbookings'][0]['service']['variant']['id'] = service_variant.id
        appointment_response['subbookings'][0]['service']['variant']['name'] = service_variant.label
        appointment_response['subbookings'][0]['service']['variant']['duration'] = "15min"

        assert response.data == self.get_response(appointment_response)
        assert DraftAppointmentItem.objects.count() == 1
        subbooking = DraftAppointmentItem.objects.first()
        assert subbooking.variant_id == service_variant.id
        assert subbooking.staffer_id == self.staffer.id
        assert subbooking.requested_staffer_id == -1
        assert subbooking.is_staffer_requested_by_client is False
        draft = DraftAppointment.objects.get(id=response.data['appointment']['id'])
        assert draft.version == appointment_response['version']

    @freezegun.freeze_time(DATETIME)
    def test__book_again_draft__business_not_available(self) -> None:
        inactive_business = business_recipe.make(
            status=Business.Status.BLOCKED,
        )
        subbooking = booking_recipe.make(
            appointment=appointment_recipe.make(
                business=inactive_business,
                booked_for=bci_recipe.make(user=self.user),
            ),
            service_variant=service_variant_recipe.make(
                service=service_recipe.make(
                    treatment=treatment_recipe.make(internal_name='Haircut'),
                ),
            ),
            _save_kwargs=dict(override=True),
        )

        response = self._send_request(appointment_id=subbooking.appointment.id)

        assert response.status_code == status.HTTP_400_BAD_REQUEST, response.data
        assert response.data == {
            "errors": [
                {
                    "description": "Staffer or service no longer available",
                    "code": "book-again-not-possible-for-appointment",
                    'type': None,
                    'field': None,
                }
            ]
        }

    @freezegun.freeze_time(DATETIME)
    def test__book_again_draft__no_staffer_can_perform_service(self) -> None:
        unavailable_staffer = staffer_recipe.make(
            visible=False,
        )
        service_variant = service_variant_recipe.make(
            service=self.service,
            resources=[unavailable_staffer],
        )
        subbooking = booking_recipe.make(
            service_variant=service_variant,
            resources=[unavailable_staffer],
            appointment=appointment_recipe.make(
                business=self.business,
                booked_for=bci_recipe.make(user=self.user),
            ),
            _save_kwargs=dict(override=True),
        )

        response = self._send_request(appointment_id=subbooking.appointment.id)

        assert response.status_code == status.HTTP_400_BAD_REQUEST, response.data
        assert response.data == {
            "errors": [
                {
                    "description": "Staffer or service no longer available",
                    "code": "book-again-not-possible-for-appointment",
                    'type': None,
                    'field': None,
                }
            ]
        }

    @freezegun.freeze_time(DATETIME)
    def test__book_again_draft__appointment_not_associated_with_user(self) -> None:
        subbooking = booking_recipe.make(
            appointment=appointment_recipe.make(
                business=self.business,
                booked_for=bci_recipe.make(user=user_recipe.make()),
            ),
            _save_kwargs=dict(override=True),
        )

        response = self._send_request(appointment_id=subbooking.appointment.id)

        assert response.status_code == status.HTTP_400_BAD_REQUEST, response.data
        assert response.data == {
            'errors': [
                {
                    'code': 'draft-book-again-error',
                    'description': 'Booking issue. Please try again.',
                    'type': None,
                    'field': None,
                }
            ]
        }

    @freezegun.freeze_time(DATETIME)
    def test__book_again_draft__appointment_not_found(self) -> None:
        response = self._send_request(appointment_id=9999999999999)

        assert response.status_code == status.HTTP_400_BAD_REQUEST, response.data
        assert response.data == {
            'errors': [
                {
                    'code': 'appointment-not-found',
                    'description': 'Booking issue. Please try again.',
                    'type': None,
                    'field': None,
                }
            ]
        }
