import uuid
from datetime import datetime, timedelta
from unittest import mock

import freezegun
from dateutil import tz
from django.urls import reverse
from rest_framework import status

from domain_services.booking.src.domains.appointment.enums import AppointmentStatusV2
from webapps.appointment_drafts.baker import draft_item_recipe
from webapps.appointment_drafts.models import DraftAppointment, DraftAppointmentItem
from webapps.booking.tests.utils import create_appointment
from webapps.booking.v2.application.http_api.tests.utils.decorator import tornado_in_drf
from webapps.booking.v2.application.http_api.tests.utils.draft_test_case import (
    DraftTestCase,
    BOOKED_FROM,
)
from webapps.booking.v2.commons.utils import timedelta_to_minutes, to_day_minutes
from webapps.business.baker_recipes import service_variant_recipe
from webapps.user.baker_recipes import user_recipe


@tornado_in_drf()
@freezegun.freeze_time(datetime(2024, 2, 12, 9, 0, tzinfo=tz.gettz('UTC')))
class TestAddDraftService(DraftTestCase):
    @staticmethod
    def url(draft_id: uuid.UUID) -> str:
        return reverse('add_draft_service', kwargs={'draft_id': str(draft_id)})

    def test_not_existing_draft(self):
        draft, _ = self.get_draft()
        resp = self.client.post(
            self.url(uuid.uuid4()),
            data={
                'service_variant_id': self.variant.id,
                'version': str(draft.version),
            },
        )
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)

    def test_wrong_user(self):
        draft, _ = self.get_draft(user_id=user_recipe.make().id)
        resp = self.client.post(
            self.url(uuid.uuid4()),
            data={
                'service_variant_id': self.variant.id,
                'version': str(draft.version),
            },
        )
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)

    def test_add_service(self):
        draft, _ = self.get_draft()
        resp = self.client.post(
            self.url(draft.id),
            data={
                'service_variant_id': self.variant.id,
                'version': str(draft.version),
            },
        )
        self.assertEqual(resp.status_code, status.HTTP_200_OK)
        assert 2 == DraftAppointmentItem.objects.count()
        assert 1 == DraftAppointment.objects.count()

        appointment_response = self.get_appointment_response()
        appointment_response['booked_from'] = '2024-02-14T17:30'
        appointment_response['booked_till'] = '2024-02-14T18:00'
        appointment_response['subbookings'][0]['booked_from'] = '2024-02-14T17:30'
        appointment_response['subbookings'][0]['booked_till'] = '2024-02-14T17:45'
        appointment_response['subbookings'][0]['service']['id'] = self.service.id
        appointment_response['subbookings'][0]['service']['omnibus_price'] = None
        appointment_response['subbookings'][0]['service']['price_before_discount'] = None
        appointment_response['duration'] = '30min'
        appointment_response['total'] = '$30.00'
        new_subbooking = self.get_draft_item_response()
        new_subbooking['booked_from'] = '2024-02-14T17:45'
        new_subbooking['booked_till'] = '2024-02-14T18:00'
        new_subbooking['service']['omnibus_price'] = None
        new_subbooking['service']['price_before_discount'] = None
        appointment_response['subbookings'].append(new_subbooking)
        self.assertDictEqual(resp.data, self.get_response(appointment_response))
        new_subbooking_id = resp.data['appointment']['subbookings'][1]['id']
        subbooking = DraftAppointmentItem.objects.get(id=uuid.UUID(new_subbooking_id))
        self.assertEqual(subbooking.staffer_id, self.staffer.id)
        self.assertEqual(subbooking.requested_staffer_id, None)
        self.assertFalse(subbooking.is_staffer_requested_by_client)

    def test_add_service_set_last_staffer(self):
        draft, _ = self.get_draft()
        staffer2 = self.get_additional_staffer()
        variant = service_variant_recipe.make(
            service=self.service, resources=[self.staffer, staffer2]
        )
        new_booked_from = draft.booked_from + timedelta(minutes=15)
        draft_item_recipe.make(
            appointment=draft,
            variant_id=variant.id,
            variant_name=variant.label,
            variant_duration=timedelta_to_minutes(variant.duration),
            price="$25.00",
            requested_staffer_id=staffer2.id,
            staffer_id=staffer2.id,
            service_id=self.service.id,
            service_name=self.service.name,
            booked_from=new_booked_from,
            booked_till=new_booked_from + timedelta(minutes=15),
            time_slot={
                'date': new_booked_from.date().isoformat(),
                'start_time': to_day_minutes(new_booked_from.time()),
            },
            service_price="$25.00",
            omnibus_price='$25.00',
            price_before_discount='$25.00',
            discount='$0.00',
        )
        resp = self.client.post(
            self.url(draft.id),
            data={
                'service_variant_id': variant.id,
                'version': str(draft.version),
            },
        )
        self.assertEqual(resp.status_code, status.HTTP_200_OK)
        assert 3 == DraftAppointmentItem.objects.count()
        assert 1 == DraftAppointment.objects.count()
        appointment_response = {
            'id': mock.ANY,
            'business': {
                'id': self.business.id,
                'name': self.business.name,
            },
            'subbookings': [
                {
                    'id': mock.ANY,
                    'booked_from': '2024-02-14T17:30',
                    'booked_till': '2024-02-14T17:45',
                    'wait_time': '',
                    'staffer': {
                        'id': self.staffer.id,
                        'name': self.staffer.name,
                        'photo_url': self.staffer.photo.image_url if self.staffer.photo else None,
                    },
                    'service': {
                        'id': self.service.id,
                        'name': self.service.name,
                        'price': '$15.00',
                        'price_before_discount': None,
                        'omnibus_price': None,
                        'variant': {
                            'id': self.variant.id,
                            'name': self.variant.label,
                            'duration': '15min',
                        },
                    },
                },
                {
                    'id': mock.ANY,
                    'booked_from': '2024-02-14T17:45',
                    'booked_till': '2024-02-14T18:00',
                    'wait_time': '',
                    'staffer': {
                        'id': staffer2.id,
                        'name': staffer2.name,
                        'photo_url': staffer2.photo.image_url if staffer2.photo else None,
                    },
                    'service': {
                        'id': self.service.id,
                        'name': self.service.name,
                        'price': '$15.00',
                        'price_before_discount': None,
                        'omnibus_price': None,
                        'variant': {
                            'id': variant.id,
                            'name': variant.label,
                            'duration': '15min',
                        },
                    },
                },
                {
                    'id': mock.ANY,
                    'booked_from': '2024-02-14T18:00',
                    'booked_till': '2024-02-14T18:15',
                    'wait_time': '',
                    'staffer': {
                        'id': staffer2.id,
                        'name': staffer2.name,
                        'photo_url': staffer2.photo.image_url if staffer2.photo else None,
                    },
                    'service': {
                        'id': self.service.id,
                        'name': self.service.name,
                        'price': '$15.00',
                        'price_before_discount': None,
                        'omnibus_price': None,
                        'variant': {
                            'id': variant.id,
                            'name': variant.label,
                            'duration': '15min',
                        },
                    },
                },
            ],
            'booked_from': '2024-02-14T17:30',
            'booked_till': '2024-02-14T18:15',
            'status': AppointmentStatusV2.DRAFT,
            'customer_note': None,
            'core_id': None,
            'version': mock.ANY,
            'duration': '45min',
            'total': '$45.00',
            'long_wait_time': False,
        }
        assert resp.json() == self.get_response(appointment_response)

    def test_add_service_last_staffer_not_available(self):
        draft, _ = self.get_draft()
        staffer2 = self.get_additional_staffer()
        variant = service_variant_recipe.make(
            service=self.service, resources=[self.staffer, staffer2]
        )
        new_booked_from = draft.booked_from + timedelta(minutes=15)
        draft_item_recipe.make(
            appointment=draft,
            variant_id=variant.id,
            variant_name=variant.label,
            variant_duration=timedelta_to_minutes(variant.duration),
            price="$25.00",
            requested_staffer_id=staffer2.id,
            staffer_id=staffer2.id,
            service_id=self.service.id,
            service_name=self.service.name,
            booked_from=new_booked_from,
            booked_till=new_booked_from + timedelta(minutes=15),
            time_slot={
                'date': new_booked_from.date().isoformat(),
                'start_time': to_day_minutes(new_booked_from.time()),
            },
            service_price="$25.00",
            omnibus_price='$25.00',
            price_before_discount='$25.00',
            discount='$0.00',
        )
        create_appointment(
            [
                {
                    'service_variant': variant,
                    'staffer': staffer2,
                    'booked_from': draft.booked_from,
                    'booked_till': draft.booked_from + timedelta(minutes=15),
                }
            ],
            business=self.business,
        )
        create_appointment(
            [
                {
                    'service_variant': None,
                    'staffer': staffer2,
                    'booked_from': new_booked_from + timedelta(minutes=15),
                    'booked_till': new_booked_from + timedelta(hours=4),
                }
            ],
            business=self.business,
        )

        resp = self.client.post(
            self.url(draft.id),
            data={
                'service_variant_id': variant.id,
                'version': str(draft.version),
            },
        )
        self.assertEqual(resp.status_code, status.HTTP_200_OK)
        assert 3 == DraftAppointmentItem.objects.count()
        assert 1 == DraftAppointment.objects.count()
        appointment_response = {
            'id': mock.ANY,
            'business': {
                'id': self.business.id,
                'name': self.business.name,
            },
            'subbookings': [
                {
                    'id': mock.ANY,
                    'booked_from': '2024-02-14T17:30',
                    'booked_till': '2024-02-14T17:45',
                    'wait_time': '',
                    'staffer': {
                        'id': self.staffer.id,
                        'name': self.staffer.name,
                        'photo_url': self.staffer.photo.image_url if self.staffer.photo else None,
                    },
                    'service': {
                        'id': self.service.id,
                        'name': self.service.name,
                        'price': '$15.00',
                        'price_before_discount': None,
                        'omnibus_price': None,
                        'variant': {
                            'id': self.variant.id,
                            'name': self.variant.label,
                            'duration': '15min',
                        },
                    },
                },
                {
                    'id': mock.ANY,
                    'booked_from': '2024-02-14T17:45',
                    'booked_till': '2024-02-14T18:00',
                    'wait_time': '',
                    'staffer': {
                        'id': staffer2.id,
                        'name': staffer2.name,
                        'photo_url': staffer2.photo.image_url if staffer2.photo else None,
                    },
                    'service': {
                        'id': self.service.id,
                        'name': self.service.name,
                        'price': '$15.00',
                        'price_before_discount': None,
                        'omnibus_price': None,
                        'variant': {
                            'id': variant.id,
                            'name': variant.label,
                            'duration': '15min',
                        },
                    },
                },
                {
                    'id': mock.ANY,
                    'booked_from': '2024-02-14T18:00',
                    'booked_till': '2024-02-14T18:15',
                    'wait_time': '',
                    'staffer': None,
                    'service': {
                        'id': self.service.id,
                        'name': self.service.name,
                        'price': '$15.00',
                        'price_before_discount': None,
                        'omnibus_price': None,
                        'variant': {
                            'id': variant.id,
                            'name': variant.label,
                            'duration': '15min',
                        },
                    },
                },
            ],
            'booked_from': '2024-02-14T17:30',
            'booked_till': '2024-02-14T18:15',
            'status': AppointmentStatusV2.DRAFT,
            'customer_note': None,
            'core_id': None,
            'version': mock.ANY,
            'duration': '45min',
            'total': '$45.00',
            'long_wait_time': False,
        }
        assert resp.json() == self.get_response(appointment_response)

    def test_add_service_last_staffer_available_after_reorder(self):
        draft, _ = self.get_draft()
        staffer2 = self.get_additional_staffer()
        variant = service_variant_recipe.make(
            service=self.service, resources=[self.staffer, staffer2]
        )
        new_booked_from = draft.booked_from + timedelta(minutes=15)
        draft_item_recipe.make(
            appointment=draft,
            variant_id=variant.id,
            variant_name=variant.label,
            variant_duration=timedelta_to_minutes(variant.duration),
            price="$25.00",
            requested_staffer_id=staffer2.id,
            staffer_id=staffer2.id,
            service_id=self.service.id,
            service_name=self.service.name,
            booked_from=new_booked_from,
            booked_till=new_booked_from + timedelta(minutes=15),
            time_slot={
                'date': new_booked_from.date().isoformat(),
                'start_time': to_day_minutes(new_booked_from.time()),
            },
            service_price="$25.00",
            omnibus_price='$25.00',
            price_before_discount='$25.00',
            discount='$0.00',
        )
        create_appointment(
            [
                {
                    'service_variant': None,
                    'staffer': staffer2,
                    'booked_from': new_booked_from + timedelta(minutes=15),
                    'booked_till': new_booked_from + timedelta(hours=4),
                }
            ],
            business=self.business,
        )

        resp = self.client.post(
            self.url(draft.id),
            data={
                'service_variant_id': variant.id,
                'version': str(draft.version),
            },
        )
        self.assertEqual(resp.status_code, status.HTTP_200_OK)
        assert 3 == DraftAppointmentItem.objects.count()
        assert 1 == DraftAppointment.objects.count()
        appointment_response = {
            'id': mock.ANY,
            'business': {
                'id': self.business.id,
                'name': self.business.name,
            },
            'subbookings': [
                {
                    'id': mock.ANY,
                    'booked_from': '2024-02-14T17:30',
                    'booked_till': '2024-02-14T17:45',
                    'wait_time': '',
                    'staffer': {
                        'id': staffer2.id,
                        'name': staffer2.name,
                        'photo_url': staffer2.photo.image_url if staffer2.photo else None,
                    },
                    'service': {
                        'id': self.service.id,
                        'name': self.service.name,
                        'price': '$15.00',
                        'price_before_discount': None,
                        'omnibus_price': None,
                        'variant': {
                            'id': variant.id,
                            'name': variant.label,
                            'duration': '15min',
                        },
                    },
                },
                {
                    'id': mock.ANY,
                    'booked_from': '2024-02-14T17:45',
                    'booked_till': '2024-02-14T18:00',
                    'wait_time': '',
                    'staffer': {
                        'id': staffer2.id,
                        'name': staffer2.name,
                        'photo_url': staffer2.photo.image_url if staffer2.photo else None,
                    },
                    'service': {
                        'id': self.service.id,
                        'name': self.service.name,
                        'price': '$15.00',
                        'price_before_discount': None,
                        'omnibus_price': None,
                        'variant': {
                            'id': variant.id,
                            'name': variant.label,
                            'duration': '15min',
                        },
                    },
                },
                {
                    'id': mock.ANY,
                    'booked_from': '2024-02-14T18:00',
                    'booked_till': '2024-02-14T18:15',
                    'wait_time': '',
                    'staffer': {
                        'id': self.staffer.id,
                        'name': self.staffer.name,
                        'photo_url': self.staffer.photo.image_url if self.staffer.photo else None,
                    },
                    'service': {
                        'id': self.service.id,
                        'name': self.service.name,
                        'price': '$15.00',
                        'price_before_discount': None,
                        'omnibus_price': None,
                        'variant': {
                            'id': self.variant.id,
                            'name': self.variant.label,
                            'duration': '15min',
                        },
                    },
                },
            ],
            'booked_from': '2024-02-14T17:30',
            'booked_till': '2024-02-14T18:15',
            'status': AppointmentStatusV2.DRAFT,
            'customer_note': None,
            'core_id': None,
            'version': mock.ANY,
            'duration': '45min',
            'total': '$45.00',
            'long_wait_time': False,
        }
        assert resp.json() == self.get_response(appointment_response)

    def test_add_service_last_staffer_with_too_long_wait_time(self):
        draft, _ = self.get_draft()
        staffer2 = self.get_additional_staffer()
        variant = service_variant_recipe.make(
            service=self.service, resources=[self.staffer, staffer2]
        )
        new_booked_from = draft.booked_from + timedelta(minutes=15)
        draft_item_recipe.make(
            appointment=draft,
            variant_id=variant.id,
            variant_name=variant.label,
            variant_duration=timedelta_to_minutes(variant.duration),
            price="$25.00",
            requested_staffer_id=staffer2.id,
            staffer_id=staffer2.id,
            service_id=self.service.id,
            service_name=self.service.name,
            booked_from=new_booked_from,
            booked_till=new_booked_from + timedelta(minutes=15),
            time_slot={
                'date': new_booked_from.date().isoformat(),
                'start_time': to_day_minutes(new_booked_from.time()),
            },
            service_price="$25.00",
            omnibus_price='$25.00',
            price_before_discount='$25.00',
            discount='$0.00',
        )
        create_appointment(
            [
                {
                    'service_variant': variant,
                    'staffer': staffer2,
                    'booked_from': draft.booked_from,
                    'booked_till': draft.booked_from + timedelta(minutes=15),
                }
            ],
            business=self.business,
        )
        create_appointment(
            [
                {
                    'service_variant': None,
                    'staffer': staffer2,
                    'booked_from': new_booked_from + timedelta(minutes=15),
                    'booked_till': new_booked_from + timedelta(hours=2),
                }
            ],
            business=self.business,
        )

        resp = self.client.post(
            self.url(draft.id),
            data={
                'service_variant_id': variant.id,
                'version': str(draft.version),
            },
        )
        self.assertEqual(resp.status_code, status.HTTP_200_OK)
        assert 3 == DraftAppointmentItem.objects.count()
        assert 1 == DraftAppointment.objects.count()
        appointment_response = {
            'id': mock.ANY,
            'business': {
                'id': self.business.id,
                'name': self.business.name,
            },
            'subbookings': [
                {
                    'id': mock.ANY,
                    'booked_from': '2024-02-14T17:30',
                    'booked_till': '2024-02-14T17:45',
                    'wait_time': '',
                    'staffer': {
                        'id': self.staffer.id,
                        'name': self.staffer.name,
                        'photo_url': self.staffer.photo.image_url if self.staffer.photo else None,
                    },
                    'service': {
                        'id': self.service.id,
                        'name': self.service.name,
                        'price': '$15.00',
                        'price_before_discount': None,
                        'omnibus_price': None,
                        'variant': {
                            'id': self.variant.id,
                            'name': self.variant.label,
                            'duration': '15min',
                        },
                    },
                },
                {
                    'id': mock.ANY,
                    'booked_from': '2024-02-14T17:45',
                    'booked_till': '2024-02-14T18:00',
                    'wait_time': '',
                    'staffer': {
                        'id': staffer2.id,
                        'name': staffer2.name,
                        'photo_url': staffer2.photo.image_url if staffer2.photo else None,
                    },
                    'service': {
                        'id': self.service.id,
                        'name': self.service.name,
                        'price': '$15.00',
                        'price_before_discount': None,
                        'omnibus_price': None,
                        'variant': {
                            'id': variant.id,
                            'name': variant.label,
                            'duration': '15min',
                        },
                    },
                },
                {
                    'id': mock.ANY,
                    'booked_from': '2024-02-14T18:00',
                    'booked_till': '2024-02-14T18:15',
                    'wait_time': '',
                    'staffer': None,
                    'service': {
                        'id': self.service.id,
                        'name': self.service.name,
                        'price': '$15.00',
                        'price_before_discount': None,
                        'omnibus_price': None,
                        'variant': {
                            'id': variant.id,
                            'name': variant.label,
                            'duration': '15min',
                        },
                    },
                },
            ],
            'booked_from': '2024-02-14T17:30',
            'booked_till': '2024-02-14T18:15',
            'status': AppointmentStatusV2.DRAFT,
            'customer_note': None,
            'core_id': None,
            'version': mock.ANY,
            'duration': '45min',
            'total': '$45.00',
            'long_wait_time': False,
        }
        assert resp.json() == self.get_response(appointment_response)

    def test_add_service_last_staffer_acceptable_wait_time(self):
        draft, _ = self.get_draft()
        staffer2 = self.get_additional_staffer()
        variant = service_variant_recipe.make(
            service=self.service, resources=[self.staffer, staffer2]
        )
        new_booked_from = draft.booked_from + timedelta(minutes=15)
        draft_item_recipe.make(
            appointment=draft,
            variant_id=variant.id,
            variant_name=variant.label,
            variant_duration=timedelta_to_minutes(variant.duration),
            price="$25.00",
            requested_staffer_id=staffer2.id,
            staffer_id=staffer2.id,
            service_id=self.service.id,
            service_name=self.service.name,
            booked_from=new_booked_from,
            booked_till=new_booked_from + timedelta(minutes=15),
            time_slot={
                'date': new_booked_from.date().isoformat(),
                'start_time': to_day_minutes(new_booked_from.time()),
            },
            service_price="$25.00",
            omnibus_price='$25.00',
            price_before_discount='$25.00',
            discount='$0.00',
        )
        create_appointment(
            [
                {
                    'service_variant': variant,
                    'staffer': staffer2,
                    'booked_from': draft.booked_from,
                    'booked_till': draft.booked_from + timedelta(minutes=15),
                }
            ],
            business=self.business,
        )
        create_appointment(
            [
                {
                    'service_variant': None,
                    'staffer': staffer2,
                    'booked_from': new_booked_from + timedelta(minutes=15),
                    'booked_till': new_booked_from + timedelta(minutes=30),
                }
            ],
            business=self.business,
        )

        resp = self.client.post(
            self.url(draft.id),
            data={
                'service_variant_id': variant.id,
                'version': str(draft.version),
            },
        )
        self.assertEqual(resp.status_code, status.HTTP_200_OK)
        assert 3 == DraftAppointmentItem.objects.count()
        assert 1 == DraftAppointment.objects.count()
        appointment_response = {
            'id': mock.ANY,
            'business': {
                'id': self.business.id,
                'name': self.business.name,
            },
            'subbookings': [
                {
                    'id': mock.ANY,
                    'booked_from': '2024-02-14T17:30',
                    'booked_till': '2024-02-14T17:45',
                    'wait_time': '',
                    'staffer': {
                        'id': self.staffer.id,
                        'name': self.staffer.name,
                        'photo_url': self.staffer.photo.image_url if self.staffer.photo else None,
                    },
                    'service': {
                        'id': self.service.id,
                        'name': self.service.name,
                        'price': '$15.00',
                        'price_before_discount': None,
                        'omnibus_price': None,
                        'variant': {
                            'id': self.variant.id,
                            'name': self.variant.label,
                            'duration': '15min',
                        },
                    },
                },
                {
                    'id': mock.ANY,
                    'booked_from': '2024-02-14T17:45',
                    'booked_till': '2024-02-14T18:00',
                    'wait_time': '',
                    'staffer': {
                        'id': staffer2.id,
                        'name': staffer2.name,
                        'photo_url': staffer2.photo.image_url if staffer2.photo else None,
                    },
                    'service': {
                        'id': self.service.id,
                        'name': self.service.name,
                        'price': '$15.00',
                        'price_before_discount': None,
                        'omnibus_price': None,
                        'variant': {
                            'id': variant.id,
                            'name': variant.label,
                            'duration': '15min',
                        },
                    },
                },
                {
                    'id': mock.ANY,
                    'booked_from': '2024-02-14T18:15',
                    'booked_till': '2024-02-14T18:30',
                    'wait_time': '15min',
                    'staffer': {
                        'id': staffer2.id,
                        'name': staffer2.name,
                        'photo_url': staffer2.photo.image_url if staffer2.photo else None,
                    },
                    'service': {
                        'id': self.service.id,
                        'name': self.service.name,
                        'price': '$15.00',
                        'price_before_discount': None,
                        'omnibus_price': None,
                        'variant': {
                            'id': variant.id,
                            'name': variant.label,
                            'duration': '15min',
                        },
                    },
                },
            ],
            'booked_from': '2024-02-14T17:30',
            'booked_till': '2024-02-14T18:30',
            'status': AppointmentStatusV2.DRAFT,
            'customer_note': None,
            'core_id': None,
            'version': mock.ANY,
            'duration': '1h',
            'total': '$45.00',
            'long_wait_time': False,
        }
        assert resp.json() == self.get_response(appointment_response)

    def test_add_service_with_appliance(self):
        draft, _ = self.get_draft()
        appliance = self.get_appliance()
        variant = service_variant_recipe.make(service=self.service, resources=[appliance])
        resp = self.client.post(
            self.url(draft.id),
            data={
                'service_variant_id': variant.id,
                'version': str(draft.version),
            },
        )
        self.assertEqual(resp.status_code, status.HTTP_200_OK)
        appointment_response = {
            'id': mock.ANY,
            'business': {
                'id': self.business.id,
                'name': self.business.name,
            },
            'subbookings': [
                {
                    'id': mock.ANY,
                    'booked_from': '2024-02-14T17:30',
                    'booked_till': '2024-02-14T17:45',
                    'wait_time': '',
                    'staffer': {
                        'id': self.staffer.id,
                        'name': self.staffer.name,
                        'photo_url': self.staffer.photo.image_url if self.staffer.photo else None,
                    },
                    'service': {
                        'id': self.service.id,
                        'name': self.service.name,
                        'price': '$15.00',
                        'price_before_discount': None,
                        'omnibus_price': None,
                        'variant': {
                            'id': self.variant.id,
                            'name': self.variant.label,
                            'duration': '15min',
                        },
                    },
                },
                {
                    'id': mock.ANY,
                    'booked_from': '2024-02-14T17:45',
                    'booked_till': '2024-02-14T18:00',
                    'wait_time': '',
                    'staffer': None,
                    'service': {
                        'id': self.service.id,
                        'name': self.service.name,
                        'price': '$15.00',
                        'price_before_discount': None,
                        'omnibus_price': None,
                        'variant': {
                            'id': variant.id,
                            'name': variant.label,
                            'duration': '15min',
                        },
                    },
                },
            ],
            'booked_from': '2024-02-14T17:30',
            'booked_till': '2024-02-14T18:00',
            'status': AppointmentStatusV2.DRAFT,
            'customer_note': None,
            'core_id': None,
            'version': mock.ANY,
            'duration': '30min',
            'total': '$30.00',
            'long_wait_time': False,
        }
        assert resp.json() == self.get_response(appointment_response)

    def test_add_service_with_appliance_and_long_wait_time(self):
        draft, _ = self.get_draft(booked_from=BOOKED_FROM - timedelta(minutes=15))
        appliance = self.get_appliance()
        variant = service_variant_recipe.make(service=self.service, resources=[appliance])
        create_appointment(
            [
                {
                    'service_variant': variant,
                    'appliance': appliance,
                    'booked_from': draft.booked_from + timedelta(minutes=15),
                    'booked_till': draft.booked_from + timedelta(hours=1),
                }
            ],
            business=self.business,
        )
        resp = self.client.post(
            self.url(draft.id),
            data={
                'service_variant_id': variant.id,
                'version': str(draft.version),
            },
        )
        self.assertEqual(resp.status_code, status.HTTP_200_OK)
        appointment_response = {
            'id': mock.ANY,
            'business': {
                'id': self.business.id,
                'name': self.business.name,
            },
            'subbookings': [
                {
                    'id': mock.ANY,
                    'booked_from': '2024-02-14T17:15',
                    'booked_till': '2024-02-14T17:30',
                    'wait_time': '',
                    'staffer': {
                        'id': self.staffer.id,
                        'name': self.staffer.name,
                        'photo_url': self.staffer.photo.image_url if self.staffer.photo else None,
                    },
                    'service': {
                        'id': self.service.id,
                        'name': self.service.name,
                        'price': '$15.00',
                        'price_before_discount': None,
                        'omnibus_price': None,
                        'variant': {
                            'id': self.variant.id,
                            'name': self.variant.label,
                            'duration': '15min',
                        },
                    },
                },
                {
                    'id': mock.ANY,
                    'booked_from': '2024-02-14T18:15',
                    'booked_till': '2024-02-14T18:30',
                    'wait_time': '45min',
                    'staffer': None,
                    'service': {
                        'id': self.service.id,
                        'name': self.service.name,
                        'price': '$15.00',
                        'price_before_discount': None,
                        'omnibus_price': None,
                        'variant': {
                            'id': variant.id,
                            'name': variant.label,
                            'duration': '15min',
                        },
                    },
                },
            ],
            'booked_from': '2024-02-14T17:15',
            'booked_till': '2024-02-14T18:30',
            'status': AppointmentStatusV2.DRAFT,
            'customer_note': None,
            'core_id': None,
            'version': mock.ANY,
            'duration': '1h 15min',
            'total': '$30.00',
            'long_wait_time': True,
        }
        assert resp.json() == self.get_response(appointment_response)

    def test_add_service_with_appliance_not_available(self):
        draft, _ = self.get_draft()
        appliance = self.get_appliance()
        variant = service_variant_recipe.make(service=self.service, resources=[appliance])
        create_appointment(
            [
                {
                    'service_variant': variant,
                    'appliance': appliance,
                    'booked_from': draft.booked_from,
                    'booked_till': draft.booked_from + timedelta(hours=2),
                }
            ],
            business=self.business,
        )
        resp = self.client.post(
            self.url(draft.id),
            data={
                'service_variant_id': variant.id,
                'version': str(draft.version),
            },
        )
        self.assertEqual(resp.status_code, status.HTTP_400_BAD_REQUEST)
