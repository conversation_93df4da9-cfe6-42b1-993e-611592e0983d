from datetime import datetime, time, timedelta

import freezegun
from dateutil import tz
from dateutil.relativedelta import relativedelta
from mock import mock

from domain_services.booking.src.domains.appointment.dto.draft import Draft
from domain_services.booking.src.domains.appointment.enums import AppointmentStatusV2
from drf_api.lib.base_drf_test_case import CustomerAPITestCase
from drf_api.service.tests.base import AuthenticatedCustomerAPITestCase
from webapps.appointment_drafts.baker import draft_item_recipe, draft_recipe
from webapps.booking.v2.commons.utils import timedelta_to_minutes, to_day_minutes
from webapps.business.baker_recipes import (
    business_recipe,
    service_recipe,
    service_variant_recipe,
    staffer_recipe,
    appliance_recipe,
)
from webapps.images.baker_recipes import photo_recipe
from webapps.pos.baker_recipes import pos_recipe
from webapps.schedule.enums import DayOfWeek
from webapps.schedule.ports import (
    set_business_default_hours,
    set_many_resources_default_hours,
)

TZ_NAME = 'UTC'
TZ = tz.gettz(TZ_NAME)
DEFAULT_HOURS = [(time(10, 0, tzinfo=tz.gettz(TZ_NAME)), time(18, 30, tzinfo=tz.gettz(TZ_NAME)))]
DEFAULT_OPENING_HOURS = {
    DayOfWeek.monday: DEFAULT_HOURS,
    DayOfWeek.tuesday: DEFAULT_HOURS,
    DayOfWeek.wednesday: DEFAULT_HOURS,
    DayOfWeek.thursday: DEFAULT_HOURS,
    DayOfWeek.friday: DEFAULT_HOURS,
    DayOfWeek.saturday: DEFAULT_HOURS,
}
BOOKED_FROM = datetime(2024, 2, 14, 17, 30, tzinfo=TZ)


class DraftTestCase(CustomerAPITestCase, AuthenticatedCustomerAPITestCase):

    @freezegun.freeze_time(datetime(2024, 2, 12, 9, 0, tzinfo=TZ))
    def setUp(self):
        self.customer_booking_src = self.booking_source
        super().setUp()
        self.business = business_recipe.make(time_zone_name=TZ_NAME)
        self.service = service_recipe.make(business=self.business)
        self.staffer = staffer_recipe.make(
            business=self.business,
            services=[self.service],
            photo=photo_recipe.make(),
        )
        self.variant = service_variant_recipe.make(service=self.service, resources=[self.staffer])
        self.very_long_variant = service_variant_recipe.make(
            service=self.service,
            resources=[self.staffer],
            duration=relativedelta(hours=12),
        )
        timezone = self.business.get_timezone()

        set_business_default_hours(
            business_id=self.business.id,
            hours=DEFAULT_OPENING_HOURS,
            tz=timezone,
        )
        set_many_resources_default_hours(
            business_id=self.business.id,
            resource_ids=[self.staffer.id],
            hours=DEFAULT_OPENING_HOURS,
            tz=timezone,
        )
        pos_recipe.make(business=self.business)
        self.business.refresh_from_db()

    def get_additional_staffer(self):
        staffer = staffer_recipe.make(
            business=self.business,
            services=[self.service],
            photo=photo_recipe.make(),
        )
        set_many_resources_default_hours(
            business_id=self.business.id,
            resource_ids=[staffer.id],
            hours=DEFAULT_OPENING_HOURS,
            tz=self.business.get_timezone(),
        )
        return staffer

    def get_appliance(self):
        appliance = appliance_recipe.make(
            business=self.business,
            services=[self.service],
            photo=photo_recipe.make(),
        )
        set_many_resources_default_hours(
            business_id=self.business.id,
            resource_ids=[appliance.id],
            hours=DEFAULT_OPENING_HOURS,
            tz=self.business.get_timezone(),
        )
        return appliance

    def get_draft_item_response(self):
        return {
            'id': mock.ANY,
            'booked_from': '2024-02-13T11:00',
            'booked_till': '2024-02-13T11:15',
            'wait_time': '',
            'staffer': {
                'id': self.staffer.id,
                'name': self.staffer.name,
                'photo_url': self.staffer.photo.image_url if self.staffer.photo else None,
            },
            'service': {
                'id': self.service.id,
                'name': self.service.name,
                'price': '$15.00',
                'price_before_discount': '$16.00',
                'omnibus_price': '$14.00',
                'variant': {
                    'id': self.variant.id,
                    'name': self.variant.label,
                    'duration': '15min',
                },
            },
        }

    def get_appointment_response(self):
        return {
            'id': mock.ANY,
            'business': {
                'id': self.business.id,
                'name': self.business.name,
            },
            'subbookings': [self.get_draft_item_response()],
            'booked_from': '2024-02-13T11:00',
            'booked_till': '2024-02-13T11:15',
            'status': AppointmentStatusV2.DRAFT,
            'customer_note': None,
            'core_id': None,
            'version': mock.ANY,
            'duration': '15min',
            'total': '$15.00',
            'long_wait_time': False,
        }

    def get_draft(self, user_id: int | None = 0, booked_from: datetime = BOOKED_FROM):
        user_id = user_id if user_id != 0 else self.user.id
        draft = draft_recipe.make(
            user_id=user_id,
            business_id=self.business.id,
            booked_from=booked_from,
            customer_note='Boring conversation anyway',
            total='$15.00',
        )
        draft_subbooking = draft_item_recipe.make(
            appointment=draft,
            variant_id=self.variant.id,
            variant_name=self.variant.label,
            variant_duration=timedelta_to_minutes(self.variant.duration),
            price="$15.00",
            staffer_id=self.staffer.id,
            service_id=self.service.id,
            service_name=self.service.name,
            booked_from=booked_from,
            booked_till=datetime(
                year=2024,
                month=2,
                day=14,
                hour=18,
                minute=0,
                tzinfo=self.business.get_timezone(),
            ),
            time_slot={
                'date': BOOKED_FROM.date().isoformat(),
                'start_time': to_day_minutes(BOOKED_FROM.time()),
            },
            service_price="$15.00",
            omnibus_price='$14.00',
            price_before_discount='$16.00',
            discount='$1.00',
        )
        return draft, draft_subbooking

    def get_response(self, appointment_response):
        return {
            'appointment': appointment_response,
            'modifications': {},
            'suggestions': {},
        }

    def add_draft_items(
        self,
        draft: Draft,
        duration=relativedelta(minutes=15),
        booked_from: datetime = BOOKED_FROM,
        booked_from_shift=15,
    ):
        variant2 = service_variant_recipe.make(
            service=self.service, resources=[self.staffer], duration=duration
        )
        variant3 = service_variant_recipe.make(
            service=self.service, resources=[self.staffer], duration=duration
        )
        new_booked_froms = [
            booked_from + timedelta(minutes=booked_from_shift),
            booked_from + timedelta(minutes=booked_from_shift * 2),
        ]
        for variant, new_booked_from in zip([variant2, variant3], new_booked_froms):
            draft_item_recipe.make(
                appointment=draft,
                variant_id=variant.id,
                variant_name=variant.label,
                variant_duration=timedelta_to_minutes(variant.duration),
                price="$25.00",
                staffer_id=self.staffer.id,
                service_id=self.service.id,
                service_name=self.service.name,
                booked_from=new_booked_from,
                booked_till=None,
                time_slot={
                    'date': new_booked_from.date().isoformat(),
                    'start_time': to_day_minutes(new_booked_from.time()),
                },
                service_price="$25.00",
                omnibus_price='$25.00',
                price_before_discount='$25.00',
                discount='$0.00',
            )
        return draft, [variant2, variant3]
