import uuid
from datetime import datetime, timedelta
from unittest import mock

import freezegun
from dateutil.relativedelta import relativedelta
from django.urls import reverse
from parameterized import parameterized
from rest_framework import status

from domain_services.booking.src.domains.appointment.enums import AppointmentStatusV2
from webapps.appointment_drafts.models import DraftAppointmentItem
from webapps.booking.tests.utils import create_appointment
from webapps.booking.v2.application.http_api.tests.utils.decorator import tornado_in_drf
from webapps.booking.v2.application.http_api.tests.utils.draft_test_case import (
    TZ,
    DraftTestCase,
)
from webapps.user.baker_recipes import user_recipe


@tornado_in_drf()
@freezegun.freeze_time(datetime(2024, 2, 12, 9, 0, tzinfo=TZ))
class TestRemoveDraftItem(DraftTestCase):

    @staticmethod
    def url(draft_id: uuid.UUID) -> str:
        return reverse('remove_draft_item', kwargs={'draft_id': str(draft_id)})

    def test__not_existing_draft(self) -> None:
        response = self.client.post(
            self.url(uuid.uuid4()),
            data={
                'draft_item_id': str(uuid.uuid4()),
                'version': str(uuid.uuid4()),
            },
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test__not_existing_subbooking(self) -> None:
        draft = self.get_draft()[0]
        draft, _ = self.add_draft_items(draft)
        response = self.client.post(
            self.url(draft.id),
            data={
                'subbooking_id': str(uuid.uuid4()),
                'version': str(draft.version),
            },
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test__wrong_user(self) -> None:
        draft, subbooking = self.get_draft(user_id=user_recipe.make().id)
        draft, _ = self.add_draft_items(draft)
        response = self.client.post(
            self.url(draft.id),
            data={
                'draft_item_id': str(subbooking.id),
                'version': str(draft.version),
            },
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test__only_one_subbooking(self) -> None:
        draft, subbooking = self.get_draft()
        response = self.client.post(
            self.url(draft.id),
            data={
                'subbooking_id': str(subbooking.id),
                'version': str(draft.version),
            },
        )
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    @parameterized.expand([(0, [1, 2]), (1, [0, 2]), (2, [0, 1])])
    def test__remove_draft_item(self, draft_item_idx, left_draft_items_idx) -> None:
        draft, _ = self.get_draft()
        draft, _ = self.add_draft_items(draft)
        draft_items = list(draft.items.all())
        assert DraftAppointmentItem.objects.filter(appointment_id=draft.id).count() == 3
        response = self.client.post(
            self.url(draft.id),
            data={
                'subbooking_id': str(draft_items[draft_item_idx].id),
                'version': str(draft.version),
            },
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        appointment_response = {
            'id': mock.ANY,
            'business': {
                'id': self.business.id,
                'name': self.business.name,
            },
            'subbookings': [
                {
                    'id': mock.ANY,
                    'booked_from': '2024-02-14T17:30',
                    'booked_till': '2024-02-14T17:45',
                    'wait_time': '',
                    'staffer': {
                        'id': self.staffer.id,
                        'name': self.staffer.name,
                        'photo_url': self.staffer.photo.image_url if self.staffer.photo else None,
                    },
                    'service': {
                        'id': self.service.id,
                        'name': self.service.name,
                        'price': '$15.00',
                        'price_before_discount': None,
                        'omnibus_price': None,
                        'variant': {
                            'id': draft_items[left_draft_items_idx[0]].variant_id,
                            'name': draft_items[left_draft_items_idx[0]].variant_name,
                            'duration': '15min',
                        },
                    },
                },
                {
                    'id': mock.ANY,
                    'booked_from': '2024-02-14T17:45',
                    'booked_till': '2024-02-14T18:00',
                    'wait_time': '',
                    'staffer': {
                        'id': self.staffer.id,
                        'name': self.staffer.name,
                        'photo_url': self.staffer.photo.image_url if self.staffer.photo else None,
                    },
                    'service': {
                        'id': self.service.id,
                        'name': self.service.name,
                        'price': '$15.00',
                        'price_before_discount': None,
                        'omnibus_price': None,
                        'variant': {
                            'id': draft_items[left_draft_items_idx[1]].variant_id,
                            'name': draft_items[left_draft_items_idx[1]].variant_name,
                            'duration': '15min',
                        },
                    },
                },
            ],
            'booked_from': '2024-02-14T17:30',
            'booked_till': '2024-02-14T18:00',
            'status': AppointmentStatusV2.DRAFT,
            'customer_note': None,
            'core_id': None,
            'version': mock.ANY,
            'duration': '30min',
            'total': '$30.00',
            'long_wait_time': False,
        }
        assert response.json() == self.get_response(appointment_response)
        assert DraftAppointmentItem.objects.filter(appointment_id=draft.id).count() == 2

    def test__remove_draft_item_cannot_change_slot(self) -> None:
        booked_from = datetime(
            year=2024, month=2, day=14, hour=16, minute=0, tzinfo=self.business.get_timezone()
        )
        draft, subbooking = self.get_draft(booked_from=booked_from)
        draft, variants = self.add_draft_items(
            draft, duration=relativedelta(minutes=30), booked_from=booked_from, booked_from_shift=30
        )
        create_appointment(
            [
                {
                    'service_variant': self.variant,
                    'staffer': self.staffer,
                    'booked_from': booked_from + timedelta(minutes=15),
                    'booked_till': booked_from + timedelta(minutes=30),
                }
            ],
            business=self.business,
        )
        response = self.client.post(
            self.url(draft.id),
            data={
                'subbooking_id': str(subbooking.id),
                'version': str(draft.version),
            },
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        appointment_response = {
            'id': mock.ANY,
            'business': {
                'id': self.business.id,
                'name': self.business.name,
            },
            'subbookings': [
                {
                    'id': mock.ANY,
                    'booked_from': '2024-02-14T16:30',
                    'booked_till': '2024-02-14T17:00',
                    'wait_time': '',
                    'staffer': {
                        'id': self.staffer.id,
                        'name': self.staffer.name,
                        'photo_url': self.staffer.photo.image_url if self.staffer.photo else None,
                    },
                    'service': {
                        'id': self.service.id,
                        'name': self.service.name,
                        'price': '$15.00',
                        'price_before_discount': None,
                        'omnibus_price': None,
                        'variant': {
                            'id': variants[0].id,
                            'name': variants[0].label,
                            'duration': '30min',
                        },
                    },
                },
                {
                    'id': mock.ANY,
                    'booked_from': '2024-02-14T17:00',
                    'booked_till': '2024-02-14T17:30',
                    'wait_time': '',
                    'staffer': {
                        'id': self.staffer.id,
                        'name': self.staffer.name,
                        'photo_url': self.staffer.photo.image_url if self.staffer.photo else None,
                    },
                    'service': {
                        'id': self.service.id,
                        'name': self.service.name,
                        'price': '$15.00',
                        'price_before_discount': None,
                        'omnibus_price': None,
                        'variant': {
                            'id': variants[1].id,
                            'name': variants[1].label,
                            'duration': '30min',
                        },
                    },
                },
            ],
            'booked_from': '2024-02-14T16:30',
            'booked_till': '2024-02-14T17:30',
            'status': AppointmentStatusV2.DRAFT,
            'customer_note': None,
            'core_id': None,
            'version': mock.ANY,
            'duration': '1h',
            'total': '$30.00',
            'long_wait_time': False,
        }
        assert response.json() == self.get_response(appointment_response)

    def test__remove_draft_item_change_middle_slot(self) -> None:
        booked_from = datetime(
            year=2024, month=2, day=14, hour=15, minute=0, tzinfo=self.business.get_timezone()
        )
        # [15:00-15:15]
        draft, _ = self.get_draft(booked_from=booked_from)
        # [15:15-15:30,15:30-15:45]
        draft, variants = self.add_draft_items(
            draft, duration=relativedelta(minutes=15), booked_from=booked_from, booked_from_shift=15
        )
        # [16:00-16:30,16:30-16:45]
        draft, variants2 = self.add_draft_items(
            draft, duration=relativedelta(minutes=30), booked_from=booked_from, booked_from_shift=60
        )
        # Other appointment [15:45-16:00]
        create_appointment(
            [
                {
                    'service_variant': self.variant,
                    'staffer': self.staffer,
                    'booked_from': booked_from + timedelta(minutes=45),
                    'booked_till': booked_from + timedelta(minutes=60),
                }
            ],
            business=self.business,
        )
        draft_items = list(draft.items.all())
        response = self.client.post(
            self.url(draft.id),
            data={
                'subbooking_id': str(draft_items[3].id),
                'version': str(draft.version),
            },
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        appointment_response = {
            'id': mock.ANY,
            'business': {
                'id': self.business.id,
                'name': self.business.name,
            },
            'subbookings': [
                {
                    'id': mock.ANY,
                    'booked_from': '2024-02-14T15:00',
                    'booked_till': '2024-02-14T15:15',
                    'wait_time': '',
                    'staffer': {
                        'id': self.staffer.id,
                        'name': self.staffer.name,
                        'photo_url': self.staffer.photo.image_url if self.staffer.photo else None,
                    },
                    'service': {
                        'id': self.service.id,
                        'name': self.service.name,
                        'price': '$15.00',
                        'price_before_discount': None,
                        'omnibus_price': None,
                        'variant': {
                            'id': self.variant.id,
                            'name': self.variant.label,
                            'duration': '15min',
                        },
                    },
                },
                {
                    'id': mock.ANY,
                    'booked_from': '2024-02-14T15:15',
                    'booked_till': '2024-02-14T15:30',
                    'wait_time': '',
                    'staffer': {
                        'id': self.staffer.id,
                        'name': self.staffer.name,
                        'photo_url': self.staffer.photo.image_url if self.staffer.photo else None,
                    },
                    'service': {
                        'id': self.service.id,
                        'name': self.service.name,
                        'price': '$15.00',
                        'price_before_discount': None,
                        'omnibus_price': None,
                        'variant': {
                            'id': variants[0].id,
                            'name': variants[0].label,
                            'duration': '15min',
                        },
                    },
                },
                {
                    'id': mock.ANY,
                    'booked_from': '2024-02-14T15:30',
                    'booked_till': '2024-02-14T15:45',
                    'wait_time': '',
                    'staffer': {
                        'id': self.staffer.id,
                        'name': self.staffer.name,
                        'photo_url': self.staffer.photo.image_url if self.staffer.photo else None,
                    },
                    'service': {
                        'id': self.service.id,
                        'name': self.service.name,
                        'price': '$15.00',
                        'price_before_discount': None,
                        'omnibus_price': None,
                        'variant': {
                            'id': variants[1].id,
                            'name': variants[1].label,
                            'duration': '15min',
                        },
                    },
                },
                {
                    'id': mock.ANY,
                    'booked_from': '2024-02-14T16:00',
                    'booked_till': '2024-02-14T16:30',
                    'wait_time': '15min',
                    'staffer': {
                        'id': self.staffer.id,
                        'name': self.staffer.name,
                        'photo_url': self.staffer.photo.image_url if self.staffer.photo else None,
                    },
                    'service': {
                        'id': self.service.id,
                        'name': self.service.name,
                        'price': '$15.00',
                        'price_before_discount': None,
                        'omnibus_price': None,
                        'variant': {
                            'id': variants2[1].id,
                            'name': variants2[1].label,
                            'duration': '30min',
                        },
                    },
                },
            ],
            'booked_from': '2024-02-14T15:00',
            'booked_till': '2024-02-14T16:30',
            'status': AppointmentStatusV2.DRAFT,
            'customer_note': None,
            'core_id': None,
            'version': mock.ANY,
            'duration': '1h 30min',
            'total': '$60.00',
            'long_wait_time': False,
        }
        assert response.json() == self.get_response(appointment_response)
