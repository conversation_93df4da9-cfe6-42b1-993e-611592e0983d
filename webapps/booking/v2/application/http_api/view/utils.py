import logging
from functools import wraps

from django.utils.translation import gettext as _
from rest_framework import status as httpstatus
from rest_framework.response import Response

from domain_services.booking.src.commons.errors import BusinessNotFoundError
from domain_services.booking.src.domains.appointment.errors.appointment import (
    ScheduleDraftFailedError,
)
from domain_services.booking.src.domains.appointment.errors.draft import (
    AddDraftServiceFailedError,
    ChangeDraftStafferFailedError,
    ChangeDraftStafferInvalidStafferError,
    ChangeDraftStafferNoTimeSlotError,
    ChangeDraftTimeslotFailedError,
    CreateDraftFailedError,
    DraftBelongsToAnotherUserError,
    DraftBookAgainBookingNotPossibleError,
    DraftBookAgainError,
    DraftItemNotFoundError,
    DraftNotFoundError,
    DraftVersionMismatchError,
    OnlyDraftItemCannotBeRemovedError,
    RemoveDraftItemFailedError,
    WrongDraftItemsForReorderError,
    ReorderFailedError,
    AppointmentNotFoundError,
)
from webapps.booking.v2.application.http_api.response.error import ErrorResponse
from webapps.booking.v2.application.service.application import (
    FirstAvailableTimeSlotNotFound,
)
from webapps.booking.v2.commons.errors import ValidationError

logger = logging.getLogger('booksy.booking.v2.http_api')

ERRORS = {
    ValidationError: {
        "status": httpstatus.HTTP_400_BAD_REQUEST,
        "code": "validation",
        "description_id": "Booking issue. Please try again.",
    },
    DraftNotFoundError: {
        "status": httpstatus.HTTP_400_BAD_REQUEST,
        "code": "draft-not-found",
        "description_id": "Booking issue. Please try again.",
    },
    BusinessNotFoundError: {
        "status": httpstatus.HTTP_400_BAD_REQUEST,
        "code": "business-not-found",
        "description_id": "Booking issue: Business unavailable. Please try again.",
    },
    DraftBelongsToAnotherUserError: {
        "status": httpstatus.HTTP_400_BAD_REQUEST,
        "code": "draft-belongs-to-another-user",
        "description_id": "Booking issue. Please try again.",
    },
    DraftVersionMismatchError: {
        "status": httpstatus.HTTP_400_BAD_REQUEST,
        "code": "draft-version-mismatch",
        "description_id": "Booking updated. Please check the details.",
    },
    DraftItemNotFoundError: {
        "status": httpstatus.HTTP_400_BAD_REQUEST,
        "code": "draft-item-not-found",
        "description_id": "Booking issue. Please try again.",
    },
    FirstAvailableTimeSlotNotFound: {
        "status": httpstatus.HTTP_400_BAD_REQUEST,
        "code": "first-available-time-slot-not-found",
        "description_id": "Time slot not found. Please try again.",
    },
    ChangeDraftStafferInvalidStafferError: {
        "status": httpstatus.HTTP_400_BAD_REQUEST,
        "code": "change-draft-staffer-invalid-staffer",
        "description_id": "Unable to select staffer. Please try a different professional.",
    },
    ChangeDraftStafferNoTimeSlotError: {
        "status": httpstatus.HTTP_400_BAD_REQUEST,
        "code": "change-draft-staffer-no-time-slot",
        "description_id": "Staff member unavailable. Please try a different professional.",
    },
    CreateDraftFailedError: {
        "status": httpstatus.HTTP_400_BAD_REQUEST,
        "code": "create-draft-failed",
        "description_id": "Booking issue. Please try again.",
    },
    ChangeDraftStafferFailedError: {
        "status": httpstatus.HTTP_400_BAD_REQUEST,
        "code": "change-draft-staffer-failed",
        "description_id": "Unable to select staffer. Please try a different professional.",
    },
    ChangeDraftTimeslotFailedError: {
        "status": httpstatus.HTTP_400_BAD_REQUEST,
        "code": "change-draft-time-slot-failed",
        "description_id": "Time slot not found. Please try again.",
    },
    AddDraftServiceFailedError: {
        "status": httpstatus.HTTP_400_BAD_REQUEST,
        "code": "add-draft-service-failed",
        "description_id": "Booking issue: Unable to add service.",
    },
    ScheduleDraftFailedError: {
        "status": httpstatus.HTTP_400_BAD_REQUEST,
        "code": "schedule-draft-failed",
        "description_id": "Booking issue. Please try again.",
    },
    RemoveDraftItemFailedError: {
        "status": httpstatus.HTTP_400_BAD_REQUEST,
        "code": "remove-draft-item-failed",
        "description_id": "Booking issue. Unable to remove service.",
    },
    OnlyDraftItemCannotBeRemovedError: {
        "status": httpstatus.HTTP_400_BAD_REQUEST,
        "code": "only-one-draft-item-cannot-be-removed",
        "description_id": "Booking issue. Unable to remove service. "
        "Please try to edit the appointment.",
    },
    WrongDraftItemsForReorderError: {
        "status": httpstatus.HTTP_400_BAD_REQUEST,
        "code": "wrong-draft-items-for-reorder",
        "description_id": "Booking issue. Unable to change service order.",
    },
    ReorderFailedError: {
        "status": httpstatus.HTTP_400_BAD_REQUEST,
        "code": "reorder-failed",
        "description_id": "Booking issue. Unable to change service order.",
    },
    DraftBookAgainError: {
        "status": httpstatus.HTTP_400_BAD_REQUEST,
        "code": "draft-book-again-error",
        "description_id": "Booking issue. Please try again.",
    },
    DraftBookAgainBookingNotPossibleError: {
        "status": httpstatus.HTTP_400_BAD_REQUEST,
        "code": "book-again-not-possible-for-appointment",
        "description_id": "Staffer or service no longer available",
    },
    AppointmentNotFoundError: {
        "status": httpstatus.HTTP_400_BAD_REQUEST,
        "code": "appointment-not-found",
        "description_id": "Booking issue. Please try again.",
    },
    Exception: {
        "status": httpstatus.HTTP_500_INTERNAL_SERVER_ERROR,
        "code": "unexpected",
        "description_id": "Booking issue. Please try again.",
    },
}


def _create_error_response(
    status: int,
    code: str,
    description_id: str,
) -> Response:
    return Response(
        status=status,
        data=ErrorResponse(
            {
                "errors": [
                    {
                        "code": code,
                        "description": _(description_id),
                    }
                ]
            }
        ).data,
    )


def with_error_handling(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:  # pylint: disable=broad-exception-caught
            logger.exception(e)

            error = ERRORS.get(type(e), ERRORS[Exception])

            return _create_error_response(
                status=error["status"],
                code=error["code"],
                description_id=error["description_id"],
            )

    return wrapper
