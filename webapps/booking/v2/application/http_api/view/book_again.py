from rest_framework import status
from rest_framework.exceptions import ValidationError as SerializerValidationError
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from webapps.booking.v2.application.http_api.request.book_again import (
    BookAgainRequest,
)
from webapps.booking.v2.application.http_api.response.draft import DraftResponse
from webapps.booking.v2.application.http_api.view.base import BaseView
from webapps.booking.v2.application.http_api.view.utils import with_error_handling
from webapps.booking.v2.commons.errors import ValidationError


# pylint: disable=duplicate-code
class BookAgainDraftView(BaseView):
    serializer_class = BookAgainRequest
    response_serializer_class = DraftResponse
    permission_classes = [IsAuthenticated]

    @with_error_handling
    def post(self, request: Request) -> Response:
        user = self.user if self.user and not self.user.is_anonymous else None
        user_id = user.id if user else None
        request_serializer = self.get_serializer(data=self.request.data)

        try:
            request_serializer.is_valid(raise_exception=True)
        except SerializerValidationError as e:
            raise ValidationError from e

        appointment_id = request_serializer.validated_data['appointment_id']

        meta = request_serializer.validated_data.get('meta') or {}
        meta['fingerprint'] = request.headers.get('X-Fingerprint')
        meta['user_id'] = user_id
        meta.pop('rwg_token', None)
        meta.pop('merchant_changed', None)

        draft_response = self._application_service.book_again(
            user=user,
            appointment_id=appointment_id,
            fingerprint=self.fingerprint,
            user_agent=self.user_agent,
            booking_source=self.booking_source,
            request=request,
            meta=meta,
        )

        return Response(
            status=status.HTTP_201_CREATED,
            data=draft_response.data,
        )
