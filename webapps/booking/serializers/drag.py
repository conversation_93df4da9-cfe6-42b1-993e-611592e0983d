from django.utils.translation import gettext as _
from rest_framework import serializers
from rest_framework.fields import get_attribute

from lib.tools import tznow
from webapps.adyen.typing import DeviceDataDict
from webapps.booking.appointment_wrapper import AppointmentWrapper
from webapps.booking.enums import WhoMakesChange
from webapps.booking.models import Appointment
from webapps.booking.models import SubBooking
from webapps.booking.serializers.appointment import (
    SubbookingResourceField,
    UpdateFutureBookingsField,
)
from webapps.booking.serializers.utils import auto_notify_about_reschedule
from webapps.booking.serializers.fields import BookingDateTimeField
from webapps.booking.serializers.validators import validate_booking_time, validate_gap_hole
from webapps.business import serializers as business_serializers
from webapps.business.models import Resource, ServiceVariant
from webapps.notification.scenarios import start_scenario
from webapps.notification.scenarios.scenarios_booking import (
    BookingFinishedScenario,
)
from webapps.pos.deposit import charge_prepayment_on_confirm


class DragBookingSerializer(
    business_serializers.SerializerBusinessMixin,
    serializers.Serializer,
):
    """Write-only serializer used for draging bookings on calendar."""

    booked_from = BookingDateTimeField(required=False)
    booked_till = BookingDateTimeField(required=False)
    staffer_id = SubbookingResourceField(
        required=False,
        allow_null=True,
        resource_type=Resource.STAFF,
    )
    appliance_id = SubbookingResourceField(
        required=False,
        allow_null=True,
        resource_type=Resource.APPLIANCE,
    )
    # auto fields:
    # - status - calculated in .validate()

    # control stuff
    _version = serializers.IntegerField(required=True, source='appointment._version')
    overbooking = serializers.BooleanField(required=True, write_only=True)
    _notify_about_reschedule = serializers.BooleanField(
        required=False,
        write_only=True,
        default=False,
    )
    _update_future_bookings = UpdateFutureBookingsField(write_only=True)
    _force_no_variant_service = serializers.BooleanField(
        required=False, default=False, write_only=True
    )
    _update_combo_bookings = serializers.BooleanField(
        required=False,
        write_only=True,
        default=False,
    )
    CONTROL_FIELDS = (
        'appointment',
        'overbooking',
        '_notify_about_reschedule',
        '_update_future_bookings',
        '_force_no_variant_service',
        '_update_combo_bookings',
    )

    def update(self, instance: SubBooking, validated_data: dict) -> SubBooking:
        # set all modified data
        appointment = instance.appointment
        appointment.updated_by = self.context['user']
        prev_status = appointment.status
        appointment.status = validated_data['status']

        staffer = validated_data['staffer_id']
        appliance = validated_data['appliance_id']
        staffer_changed = staffer and instance.staffer_id != staffer.id
        appliance_changed = appliance and instance.appliance_id != appliance.id
        force_custom_service = validated_data.get('_force_no_variant_service')
        is_combo = bool(instance.combo_parent or instance.combo_children)

        instance.staffer = staffer  # noqa
        instance.appliance = appliance  # noqa
        # Moving to another staffer means that it's no longer autoassigned and requested
        if staffer_changed:
            instance.autoassign = False
            instance.is_staffer_requested_by_client = False

        if force_custom_service and (staffer_changed or appliance_changed) and not is_combo:
            service_variant = (
                ServiceVariant.objects.prefetch_for_booking(
                    business_id=appointment.business_id,
                    service_variant_ids=[instance.service_variant_id],
                ).first()
                if instance.service_variant_id
                else None
            )
            if service_variant and (
                (staffer_changed and staffer.id not in service_variant.staffer_ids)
                or (appliance_changed and appliance.id not in service_variant.appliance_ids)
            ):
                # New staffer does not handle this service but we "force" it
                # so service is being changed to manual one
                instance.service_name = instance.service_variant.service.name
                instance.service_variant = None

        booked_from = validated_data['booked_from']
        booked_till = validated_data['booked_till']

        # booking time or staffer modified
        if (
            booked_from != instance.booked_from
            or booked_till != instance.booked_till
            or staffer_changed
        ):
            if instance.combo_parent:
                # `_make_combo_subbooking` updates cached subbooking.combo_children,
                # used in AppointmentWrapper to save changes, this needs to be called for changes
                # on the `instance` combo booking to be persisted.
                subbooking = self._make_combo_subbooking(
                    instance,
                    booked_from,
                    booked_till,
                    update_siblings=validated_data.get('_update_combo_bookings'),
                )
            else:
                subbooking = instance
                subbooking.booked_from = booked_from
                subbooking.booked_till = booked_till
        else:
            subbooking = instance

        current_subbookings = appointment.subbookings
        new_subbookings = [
            subbooking if subbooking.id == booking.id else booking
            for booking in current_subbookings
        ]
        appointment_wrapper = AppointmentWrapper(new_subbookings)
        appointment_wrapper.save(
            to_delete=[],
            overbooking=validated_data['overbooking'],
            who_makes_change=WhoMakesChange.BUSINESS,
            update_future=validated_data.get('_update_future_bookings', False),
            prev_status=prev_status,
        )

        return instance

    @staticmethod
    def _make_combo_subbooking(
        instance: SubBooking,
        booked_from,
        booked_till,
        update_siblings=False,
    ):
        subbooking = instance.combo_parent
        combo_children = [  # noqa
            instance if instance.id == child.id else child for child in subbooking.combo_children
        ]

        if update_siblings:
            if subbooking.service_variant.combo_parallel:
                for child in combo_children:
                    delta = booked_from - child.booked_from
                    child.booked_from += delta
                    child.booked_till += delta
            else:
                updated_idx = combo_children.index(instance)
                children_before = combo_children[:updated_idx]
                children_after = combo_children[updated_idx + 1 :]

                start_delta = booked_from - instance.booked_from
                end_delta = booked_till - instance.booked_till

                for child in children_before:
                    child.booked_from += start_delta
                    child.booked_till += start_delta
                for child in children_after:
                    child.booked_from += end_delta
                    child.booked_till += end_delta

        instance.booked_from = booked_from
        instance.booked_till = booked_till

        subbooking.combo_children = combo_children  # noqa
        subbooking.booked_from = min(child.booked_from for child in combo_children)
        subbooking.booked_till = max(child.booked_till for child in combo_children)
        return subbooking

    def validate__version(self, value):
        if self.instance.appointment._version != value:  # pylint: disable=protected-access
            raise serializers.ValidationError(
                _('This appointment has been changed in the meantime. Please refresh its details.')
            )

    def validate_booked_from(self, value):
        if (
            self.instance.booked_from.astimezone(self.timezone).date() != value.date()
            and self.instance.is_multibooking()
        ):
            raise serializers.ValidationError(
                _('Dragging of multibooking to another day is not allowed'),
                code='drag_not_allowed',
            )
        return value

    def _calculate_status(self, attrs, appointment):
        """Calculate booking status after save."""

        now = tznow()
        appointment_obj = get_attribute(self.instance, ['appointment'])
        booked_till = attrs['booked_till']
        status = get_attribute(appointment_obj, ['status'])
        booking_type = get_attribute(appointment_obj, ['type'])
        booked_from = attrs['booked_from']

        if status in Appointment.STATUSES_NONEDITABLE:
            return status

        previous_booked_from = appointment.booked_from
        previous_booked_till = appointment.booked_till
        new_booked_from = min(
            [booked_from]
            + [bk.booked_from for bk in appointment.subbookings if bk.id != self.instance.id]
        )
        new_booked_till = max(
            [booked_till]
            + [bk.booked_till for bk in appointment.subbookings if bk.id != self.instance.id]
        )
        # edit ongoing appointment - don't change status
        if (
            new_booked_till > now > new_booked_from
            and previous_booked_till > now > previous_booked_from
        ):
            return status

        if new_booked_till > now:  # future
            status = Appointment.STATUS.ACCEPTED
        elif status != Appointment.STATUS.NOSHOW:  # past
            status = Appointment.STATUS.FINISHED

        if (
            attrs['_notify_about_reschedule']
            and status not in Appointment.STATUSES_TIME_PASSED
            and booking_type == Appointment.TYPE.CUSTOMER
            and status != Appointment.STATUS.PROPOSED
        ):
            # we have modified customer booking
            # and want the customer to confirm it
            status = Appointment.STATUS.PROPOSED

        return status

    def validate(self, attrs):
        appointment = self.instance.appointment

        ### RESOURCES ###
        # setdefault resources from instance
        staffer = attrs.setdefault('staffer_id', self.instance.staffer)
        appliance = attrs.setdefault('appliance_id', self.instance.appliance)
        # check if selected resources handle selected services
        if (service_variant := self.instance.service_variant) and not attrs.get(
            '_force_no_variant_service'
        ):
            if (
                # staffer was changed
                self.instance.staffer != staffer
                and service_variant.staffer_ids
                and (not staffer or staffer.id not in service_variant.staffer_ids)
            ):
                raise serializers.ValidationError(
                    {
                        'staffer_id': [
                            _(
                                'This service is not provided by chosen '
                                'staff member. Please select another one.'
                            )
                        ],
                    }
                )
            if (
                # appliance was changed
                self.instance.appliance != appliance
                and service_variant.appliance_ids
                and (not appliance or appliance.id not in service_variant.appliance_ids)
            ):
                raise serializers.ValidationError(
                    {
                        'appliance_id': [
                            _(
                                'This service is not provided by chosen '
                                'resource. Please select another one.'
                            )
                        ],
                    }
                )

        ### BOOKED FROM & TILL ###
        now = self.business.tznow
        is_reserved_time = appointment.type == Appointment.TYPE.RESERVATION

        _booked_from = attrs.setdefault('booked_from', self.instance.booked_from)
        booked_till = attrs.setdefault('booked_till', self.instance.booked_till)
        validate_booking_time(attrs)

        attrs['service_variant'] = self.instance.service_variant
        validate_gap_hole(attrs)
        auto_notify_about_reschedule(attrs, [attrs], self.instance)

        # STATUS
        self._appointment_wrapper = AppointmentWrapper(
            subbookings=list(appointment.subbookings),
        )
        status = appointment.status
        status_list = (
            Appointment.STATUS.PROPOSED,
            *Appointment.STATUSES_NONEDITABLE,
            *Appointment.STATUSES_TIME_PASSED,
        )
        if is_reserved_time:
            attrs['status'] = (
                Appointment.STATUS.ACCEPTED if booked_till > now else Appointment.STATUS.FINISHED
            )
        elif (
            attrs['_notify_about_reschedule']
            and status not in status_list
            and appointment.type == Appointment.TYPE.CUSTOMER
        ):
            attrs['status'] = (
                Appointment.STATUS.PROPOSED if booked_till > now else Appointment.STATUS.FINISHED
            )
        else:
            attrs['status'] = self._calculate_status(attrs, self._appointment_wrapper)

        return attrs

    def save(self, **kwargs) -> SubBooking:
        # remember appointment's status and booked_till
        appointment = self.instance.appointment
        booked_till_before = appointment.booked_till
        status_before = appointment.status

        subbooking = super().save(**kwargs)

        # Dragging appointment made it accepted, so we should receive money
        if status_before in [
            Appointment.STATUS.UNCONFIRMED,
            Appointment.STATUS.PROPOSED,
        ] and subbooking.appointment.status in [
            Appointment.STATUS.ACCEPTED,
            Appointment.STATUS.FINISHED,
        ]:
            charge_prepayment_on_confirm(
                appointment_id=self.instance.appointment_id,
                device_data=DeviceDataDict(
                    fingerprint=self.context.get('device_fingerprint', ''),
                    phone_number=self.context.get('cell_phone', ''),
                    user_agent=self.context.get('user_agent', ''),
                ),
                extra_data=self.context.get('payment_extra_data'),
            )

        # moved from future to past
        if all(
            (
                booked_till_before > tznow() > appointment.booked_till,
                status_before in [Appointment.STATUS.ACCEPTED, Appointment.STATUS.PROPOSED],
                appointment.status == Appointment.STATUS.FINISHED,
            )
        ):
            start_scenario(
                BookingFinishedScenario,
                appointment=appointment,
            )

        self._appointment_wrapper.send_signal()
        return subbooking
