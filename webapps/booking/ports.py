import uuid
from datetime import datetime, timedelta
from decimal import Decimal
from typing import List

import pytz
from dateutil.relativedelta import relativedelta
from django.conf import settings

from lib.business.entities import ServiceVariantPaymentEntity
from lib.db import READ_ONLY_DB, using_db_for_reads
from lib.tools import major_unit
from service.booking.tools import check_tokenized_payments_v2
from webapps.booking.appointment_checkout import PrepayRate
from webapps.booking.adapters import get_draft_by_id
from webapps.booking.models import Appointment
from webapps.booking.tools.tools import iter_leaf_services
from domain_services.booking.src.domains.appointment.dto.draft import Draft


class BookingPort:
    @staticmethod
    def check_tokenized_payments_for_payment_creation(partner: str) -> bool:
        payment_possibilities = check_tokenized_payments_v2()
        return payment_possibilities[partner]


class DraftPort:
    @staticmethod
    def get_draft_by_id(draft_id: uuid.UUID, user_id: int) -> Draft:
        return get_draft_by_id(draft_id=draft_id, user_id=user_id)


class AppointmentPort:
    @staticmethod
    def get_customer_appointments_ids() -> list[int]:
        """
        Gets IDs of ALL customer appointments. DO NOT USE IN LIVE ENVIRONMENT!
        """
        # Precaution mechanism against running this in production or some T1 environment.
        if settings.LIVE_DEPLOYMENT:
            raise RuntimeError(  # pylint: disable=broad-exception-raised
                "This method invokes a HEAVY query! It should NOT be used on real databases.",
            )
        return list(
            Appointment.objects.filter(type=Appointment.TYPE.CUSTOMER).values_list(
                "id",
                flat=True,
            )
        )

    @staticmethod
    def get_customer_appointments_previous_month_count(business_id: int):
        today = datetime.now(pytz.UTC).date()
        first_day_of_previous_month = today.replace(day=1) - relativedelta(months=1)
        last_day_of_previous_month = today.replace(day=1) - timedelta(days=1)

        return Appointment.objects.filter(
            business_id=business_id,
            type=Appointment.TYPE.CUSTOMER,
            created__date__range=(first_day_of_previous_month, last_day_of_previous_month),
        ).count()

    @staticmethod
    @using_db_for_reads(READ_ONLY_DB)
    def get_finished_customer_appointments_count(business_id: int) -> int:
        return Appointment.objects.filter(
            business=business_id,
            type=Appointment.TYPE.CUSTOMER.value,
            status=Appointment.STATUS.FINISHED.value,
        ).count()

    @staticmethod
    @using_db_for_reads(READ_ONLY_DB)
    def get_appointment_staffers_ids(appointment_id: int) -> List[int] | None:
        appointment = (
            Appointment.objects.only('id')
            .prefetch_related(
                'bookings',
                'bookings__resources',
                'bookings__combo_children_set',
                'bookings__combo_children_set__resources',
            )
            .filter(id=appointment_id)
            .first()
        )

        if not appointment:
            return None

        return list(
            {
                subbooking.staffer_id
                for subbooking in iter_leaf_services(appointment.subbookings)
                if subbooking.staffer_id is not None
            }
        )

    @staticmethod
    @using_db_for_reads(READ_ONLY_DB)
    def is_appointment_in_inactive_status(appointment_id: int) -> bool:
        appointment = Appointment.objects.filter(id=appointment_id).first()
        return appointment.status in Appointment.STATUSES_INACTIVE if appointment else False


def get_prepay_rate_port(
    price: int, service_variant_payment: ServiceVariantPaymentEntity
) -> Decimal:
    return PrepayRate(
        value=service_variant_payment.rate,
        is_amount=service_variant_payment.is_amount,
        is_cancellation_fee=False,
    ).apply(price=major_unit(price))
