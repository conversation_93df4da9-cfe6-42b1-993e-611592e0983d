from decimal import Decimal

from django.utils.translation import gettext_lazy as _

from webapps.booking.no_show_protection.business_appointment.validation_rules import validation_rule
from webapps.pos.services import TransactionService


class DepositValueTooSmallError(validation_rule.ValidationError):
    pass


class DepositValueAtLeastMinDepositValue(validation_rule.ValidationRule):

    def __init__(self, deposit_value: Decimal):
        super().__init__()
        self._deposit_value = deposit_value

    def is_valid(self) -> bool:
        minimal_payment = TransactionService.get_minimal_pay_by_app_payment()
        if self._deposit_value >= minimal_payment:
            return True
        self._errors = [
            DepositValueTooSmallError(
                _(
                    "Deposit of {value} is less than the "
                    + "minimum required deposit of {minimal_value}."
                ).format(value=self._deposit_value, minimal_value=minimal_payment)
            )
        ]
        return False
