# Generated by Django 4.2.23 on 2025-08-07 12:25

from django.db import migrations


def forwards_func(apps, schema_editor):
    Poll = apps.get_model('survey', 'Poll')
    PollChoice = apps.get_model('survey', 'PollChoice')
    db_alias = schema_editor.connection.alias

    poll, _ = Poll.objects.using(db_alias).get_or_create(
        name='registration_number_of_staffers',
        defaults={
            'response_model_name': 'RegistrationPollResponse',
            'ordered': True,
            'max_selections': 1,
            'description': 'What’s your team size?',
            'additional_description': None,
        },
    )
    
    existing_internal_names = [
        'just_me',
        '23_staff_members', 
        '46_staff_members',
        'more_than_6_staff_members'
    ]    
    PollChoice.objects.using(db_alias).filter(
        poll=poll,
        internal_name__in=existing_internal_names
    ).update(visible=False)
    
    new_choices = [
        ('just_me_2', {'choice': 'Just me', 'order': 1, 'visible': True}),
        ('24_staff_members', {'choice': '2-4 staff members', 'order': 2, 'visible': True}),
        ('59_staff_members', {'choice': '5-9 staff members', 'order': 3, 'visible': True}),
        ('more_than_10_staff_members', {'choice': 'More than 10 staff members', 'order': 4, 'visible': True}),
    ]
    
    for internal_name, choice_data in new_choices:
        PollChoice(
            poll=poll,
            internal_name=internal_name,
            choice=choice_data['choice'],
            order=choice_data['order'],
            visible=choice_data['visible'],
        ).save(using=db_alias)


def backwards_func(apps, schema_editor):
    Poll = apps.get_model('survey', 'Poll')
    PollChoice = apps.get_model('survey', 'PollChoice')
    db_alias = schema_editor.connection.alias

    try:
        poll = Poll.objects.using(db_alias).get(name='registration_number_of_staffers')
    except Poll.DoesNotExist:
        return
    
    new_internal_names = [
        'just_me_2',
        '24_staff_members',
        '59_staff_members', 
        'more_than_10_staff_members'
    ]
    
    PollChoice.objects.using(db_alias).filter(
        poll=poll,
        internal_name__in=new_internal_names
    ).delete()
    
    existing_internal_names = [
        'just_me',
        '23_staff_members',
        '46_staff_members', 
        'more_than_6_staff_members'
    ]
    PollChoice.objects.using(db_alias).filter(
        poll=poll,
        internal_name__in=existing_internal_names
    ).update(visible=True)


class Migration(migrations.Migration):

    dependencies = [
        ('survey', '0018_add_appointments_poll'),
    ]

    operations = [migrations.RunPython(forwards_func, backwards_func)]
