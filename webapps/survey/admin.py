from django.contrib import admin
from django.contrib.admin import TabularInline


from lib.admin_helpers import BaseModelAdmin, NoAddDelMixin, NoDelMixin, NoChangeMixin
from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin
from webapps.survey.models import (
    BoostClaimPollResponse,
    MarketplacePollResponse,
    Poll,
    PollChoice,
    PollResponse,
    RegistrationPollResponse,
    TrialExtensionPollResponse,
)
from webapps.user.groups import GroupNameV2


class PollChoiceInline(NoAddDelMixin, TabularInline):
    model = PollChoice
    readonly_fields = ('id', 'poll', 'choice', 'description', 'visible')


class PollAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    list_display = ('id', 'name')
    inlines = (PollChoiceInline,)


class PollChoiceAdmin(NoDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    list_display = ('id', 'poll', 'choice', 'internal_name', 'description', 'visible')


class PollResponseAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    readonly_fields = list_display = ('id', 'choice', 'user', 'created')


class MarketplacePollResponseAdmin(
    NoChangeMixin, NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin
):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    readonly_fields = list_display = (
        'id',
        'choice',
        'user',
        'business',
        'promotion_id',
        'created',
        '_created',
    )
    exclude = ('promotion',)


class BoostClaimPollResponseAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    readonly_fields = list_display = (
        'id',
        'choice',
        'user',
        'business',
        'transaction_row_id',
        'created',
        '_created',
    )


class RegistrationPollResponseAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    readonly_fields = list_display = (
        'id',
        'choice',
        'user',
        'business',
        'created',
        'updated',
    )


class TrialExtensionPollResponseAdmin(NoAddDelMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = (GroupNameV2.BASIC_MODERATOR,)
    readonly_fields = list_display = (
        'id',
        'choice',
        'user',
        'business',
        'created',
        'updated',
    )


admin.site.register(Poll, PollAdmin)
admin.site.register(PollChoice, PollChoiceAdmin)
admin.site.register(PollResponse, PollResponseAdmin)
admin.site.register(MarketplacePollResponse, MarketplacePollResponseAdmin)
admin.site.register(BoostClaimPollResponse, BoostClaimPollResponseAdmin)
admin.site.register(RegistrationPollResponse, RegistrationPollResponseAdmin)
admin.site.register(TrialExtensionPollResponse, TrialExtensionPollResponseAdmin)
