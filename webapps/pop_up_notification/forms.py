from django import forms
from django.contrib.admin import TabularInline
from django.db.models import Q

from webapps.pop_up_notification.models import (
    BusinessYouMayLikeNotification,
    CategoryYouMayLikeNotification,
    ConsentsNotification,
    ShortReviewNotification,
    LateCancellationNotification,
    MaxLeadTimeNotification,
    MobilePaymentsIntroduction,
    MobilePaymentsMigration,
    FamilyAndFriendsInvitationNotification,
    FamilyAndFriendsInvitationResponseNotification,
    FamilyAndFriendsUnlinkNotification,
)


# pylint: disable=modelform-uses-exclude


class BusinessYouMayLikeForm(forms.ModelForm):
    class Meta:
        model = BusinessYouMayLikeNotification
        exclude = (
            'deleted',
            'notification_type',
        )

    dismissed = forms.CharField(
        label='DateTime Dismissed',
        widget=forms.TextInput(attrs={'readonly': 'readonly'}),
        required=False,
    )

    def __init__(self, *args, **kwargs):
        instance = kwargs.get('instance')
        if instance:
            if instance.deleted:
                deleted = instance.deleted.strftime('%Y-%m-%d %H:%M')
                self.declared_fields['dismissed'].initial = deleted
        else:
            self.declared_fields['dismissed'].initial = None

        super().__init__(*args, **kwargs)


class CategoryYouMayLikeForm(forms.ModelForm):
    class Meta:
        model = CategoryYouMayLikeNotification
        exclude = (
            'deleted',
            'notification_type',
            'business',
        )

    dismissed = forms.CharField(
        label='DateTime Dismissed',
        widget=forms.TextInput(attrs={'readonly': 'readonly'}),
        required=False,
    )

    def __init__(self, *args, **kwargs):
        instance = kwargs.get('instance')
        if instance:
            if instance.deleted:
                deleted = instance.deleted.strftime('%Y-%m-%d %H:%M')
                self.declared_fields['dismissed'].initial = deleted
        else:
            self.declared_fields['dismissed'].initial = None
        super().__init__(*args, **kwargs)


class ShortReviewForm(forms.ModelForm):
    class Meta:
        model = ShortReviewNotification
        exclude = (
            'deleted',
            'notification_type',
            'business',
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)


class LateCancellationForm(forms.ModelForm):
    class Meta:
        model = LateCancellationNotification
        exclude = (
            'deleted',
            'notification_type',
        )


class MaxLeadTimeForm(forms.ModelForm):
    class Meta:
        model = MaxLeadTimeNotification
        exclude = (
            'deleted',
            'notification_type',
        )


class MobilePaymentsIntroductionForm(forms.ModelForm):
    class Meta:
        model = MobilePaymentsIntroduction
        exclude = (
            'deleted',
            'notification_type',
        )


class MobilePaymentsMigrationForm(forms.ModelForm):
    class Meta:
        model = MobilePaymentsMigration
        exclude = (
            'deleted',
            'notification_type',
        )


class MobilePaymentsTurnOnPrepaymentsForm(forms.ModelForm):
    class Meta:
        model = MobilePaymentsMigration
        exclude = (
            'deleted',
            'notification_type',
        )


class ConsentsInlineFormset(forms.models.BaseInlineFormSet):

    def clean(self):
        count = 0
        user_ids = set()
        business_ids = set()
        expected_user_id = self.instance.user_id
        expected_business_id = self.instance.business_id

        for form in [
            form for form in self.forms if form.is_valid() and form not in self.deleted_forms
        ]:
            if not form.cleaned_data:
                continue
            count += 1
            business_ids.add(form.cleaned_data['consent'].customer.business_id)
            user_ids.add(form.cleaned_data['consent'].customer.user_id)

        if count < 1:
            raise forms.ValidationError('Select at least one consent')

        if expected_user_id and user_ids != {expected_user_id}:
            raise forms.ValidationError('Consents must belong same user as in notification')

        if expected_business_id and business_ids != {expected_business_id}:
            raise forms.ValidationError('Consents must belong same business as in notification')


class ConsentsInline(TabularInline):
    model = ConsentsNotification.consents.through
    formset = ConsentsInlineFormset
    verbose_name_plural = 'Consents'
    raw_id_fields = ['consent']
    readonly_fields = ['consent_form_title']
    extra = 1

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('consent')

    def formfield_for_foreignkey(self, db_field, request=None, **kwargs):
        field = super().formfield_for_foreignkey(db_field, request, **kwargs)

        if db_field.name == 'consent':
            if request.object is not None:
                field.error_messages['invalid_choice'] = (
                    'Select a consent with customer that matches the notification user.'
                )
                query = Q(customer__user_id=request.object.user_id)
                if request.object.business_id:
                    query &= Q(customer__business_id=request.object.business_id)
                field.queryset = field.queryset.filter(query)
            else:
                field.queryset = field.queryset.filter(customer__user__isnull=False)

        return field

    @staticmethod
    def consent_form_title(obj):
        return obj.consent.form_title

    consent_form_title.short_description = 'Form title'


class ConsentsNotificationForm(forms.ModelForm):

    class Meta:
        model = ConsentsNotification
        fields = [
            'user',
            'business',
            'used',
            'valid_since',
            'valid_till',
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if 'business' in self.fields:
            self.fields['business'].required = True


class FamilyAndFriendsInvitationNotificationForm(forms.ModelForm):
    class Meta:
        model = FamilyAndFriendsInvitationNotification
        exclude = ('notification_type',)


class FamilyAndFriendsUnlinkNotificationForm(forms.ModelForm):
    class Meta:
        model = FamilyAndFriendsUnlinkNotification
        exclude = ('notification_type',)


class FamilyAndFriendsInvitationResponseNotificationForm(forms.ModelForm):
    class Meta:
        model = FamilyAndFriendsInvitationResponseNotification
        exclude = ('notification_type',)
