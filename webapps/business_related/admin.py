from django.contrib import admin

from lib.admin_helpers import (
    BaseModelAdmin,
    NoAddDelMixin,
    NoChangeMixin,
)
from webapps.admin_extra.custom_permissions_classes import GroupPermissionMixin
from webapps.business_related.models import (
    Amenities,
    ClaimLog,
    SafetyRule,
)
from webapps.user.groups import GroupNameV2


class SafetyRuleAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = ()
    fields = ['text']
    list_display = ['text']
    ordering = ['id']
    actions_on_bottom = False


admin.site.register(SafetyRule, SafetyRuleAdmin)


class BookedForClaimLogAdmin(NoAddDelMixin, NoChangeMixin, GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = ()
    list_display = [
        'id',
        'source_bci',
        'target_bci',
        'old_user',
        'new_user',
        'status',
    ]
    readonly_fields = [
        'id',
        'source_bci',
        'target_bci',
        'old_user',
        'new_user',
        'status',
        'source_model_related_objects',
        'merge_reason',
        'type',
    ]

    list_filter = [
        'status',
        'merge_reason',
        'type',
    ]
    search_fields = (
        '=id',
        '=source_bci__id',
        '=target_bci__id',
        '=old_user__id',
        '=new_user__id',
    )


admin.site.register(ClaimLog, BookedForClaimLogAdmin)


class AmenitiesAdmin(GroupPermissionMixin, BaseModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = ()
    list_display = [
        'business_id',
        'parking',
        'credit_cards',
        'wheelchair_access',
        'kids',
        'animals',
        'wifi',
        'loyalty',
    ]

    raw_id_fields = ['business']


admin.site.register(Amenities, AmenitiesAdmin)
