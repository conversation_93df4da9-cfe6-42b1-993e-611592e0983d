from django.contrib import admin

from .models import BlacklistItem
from ..admin_extra.custom_permissions_classes import GroupPermissionMixin
from ..user.groups import GroupNameV2


class BlacklistItemAdmin(GroupPermissionMixin, admin.ModelAdmin):
    full_access_groups = (GroupNameV2.BUSINESS_EDITOR,)
    read_only_access_groups = ()
    list_display = ('name', 'blacklist_type', 'value', 'enabled', 'created', 'updated')
    search_fields = ('name', 'value')
    list_filter = ('blacklist_type', 'enabled')


admin.site.register(BlacklistItem, BlacklistItemAdmin)
