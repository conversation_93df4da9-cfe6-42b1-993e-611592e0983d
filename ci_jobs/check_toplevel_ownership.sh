#!/usr/bin/env -S sh -e
die() {
    echo $1 >&2
    exit 1
}

which gfind 2>&1 >/dev/null && alias find=gfind

which find 2>&1 >/dev/null || die 'This utility requires GNU Find which is missing!'
which git 2>&1 >/dev/null || die 'This utility requires Git which is missing!'
which basename 2>&1 >/dev/null || die 'This utility requires basename which is missing!'
which mktemp 2>&1 >/dev/null || die 'This utility requires mktemp which is missing!'
which awk 2>&1 >/dev/null || die 'This utility requires awk which is missing!'

cd "$(git rev-parse --show-toplevel)"
thisfile=`basename $0`
patterns_file=`mktemp -t "$thisfile.XXXXXX"`
patterns_git_file=`mktemp -t "$thisfile.XXXXXX"`

find drf_api/service lib service webapps -mindepth 1 -maxdepth 1 -type d | sort --unique > $patterns_file
echo "dumped possible roots for services to $patterns_file" >&2
while read path; do
    if git ls-files --error-unmatch $path 2>/dev/null >/dev/null; then
        grep --silent "^$path/\?$" ./CODEOWNERS || echo $path
    fi
done < $patterns_file > $patterns_git_file
echo "dumped tracked possible roots for services to $patterns_git_file" >&2

awk -f - $patterns_git_file <<EOF
  // { ctr+=1; print }

  END { 
    if (ctr == 0) {
      exit 0
    }
    printf("\n🔴 There are %d services without explicit ownership!\n👉 Use CODEOWNERS file to assign ownership!\n", ctr)
    exit 1
  }
EOF
