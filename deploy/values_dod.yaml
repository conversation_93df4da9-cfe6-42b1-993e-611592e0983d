deploy:
  admin: true
  api: true
  celeryAllQueueWorker: true
  grpcApi: true
  publicApi: true
  init: true
  dbInit: false
  elasticInit: false
  executeScriptsInit: false
  otherInit: false

variables:
  subdomainHost: booksy-subdomains-api-grpc.devops-tools.svc.cluster.local:9099
  authHost: auth-server.devops-tools.svc.cluster.local:8010
  deeplinksGrpcHost: deeplinks-grpc-api.devops-tools.svc.cluster.local:8010
  loggingUseStdout: "false"
  truncate_logs: "false"
  apiUwsgiProcesses: 1
  apiUwsgiLazyApp: False
  apiUwsgiForkHooks: True
  apiUwsgiRssReload: True
  apiUwsgiThreads: 4
  adminUwsgiProcesses: 1
  adminUwsgiThreads: 2
  adminUwsgiLazyApp: False
  adminUwsgiForkHooks: True
  adminUwsgiRssReload: True
  adminUwsgiRssReloadThreshold: 700
  publicApiUwsgiLazyApp: False
  publicApiUwsgiForkHooks: True
  publicApiUwsgiRssReload: True
  reportsApiUwsgiLazyApp: False
  reportsApiUwsgiForkHooks: True
  reportsApiUwsgiRssReload: True
  searchApiUwsgiLazyApp: False
  searchApiUwsgiForkHooks: True
  searchApiUwsgiRssReload: True
  statsAndReports:
    projectId: stats-and-reports-dev
    collectionName: stats-and-reports


  celeryAllQueueWorkerProcesses: 4

  fiscalArchives:
    GCSProjectID: bks-dev-3-eu-w1
    GCSBucketName: bks-dev-3-eu-w1-fiscal-archives

  pubsub:
    projectID: bks-dev-3-eu-w1
    
  ecommerce:
    pubsub:
      projectID: bks-dev-3-eu-w1

  redis:
    celeryBackendHost: redis-email-bulk
    celeryBrokerHost: redis-email-bulk
    celeryBeatHost: redis-email-bulk
    celeryBulkCacheHost: redis-email-bulk
    simplifiedBookingRedisHost: redis-email-bulk

  providerCalendar:
    firebase:
      projectNumber: "297235164397"

  kafka:
    resourceNamespace: "pm.booksy.t1.{{ .Release.Namespace }}.{{ .Values.variables.booksyCountryCode }}"
    brokerBootstrapServers: seed-ee4af287.cpepgn3conq6u97h1pe0.byoc.prd.cloud.redpanda.com:9092
    schemaRegistryUrl: https://schema-registry-5535af92.cpepgn3conq6u97h1pe0.byoc.prd.cloud.redpanda.com:30081
    kafkaConnectUrl: https://api-ee4af287.cpepgn3conq6u97h1pe0.byoc.prd.cloud.redpanda.com/v1/kafka-connect/clusters/redpanda
    oauthTokenUrl: https://auth.prd.cloud.redpanda.com/oauth/token

    resourcesSecretManager:
      projectID: bks-kafka-dev-1-eu-w1

extraEnvs:
  - name: WEB_TOOLS_URL
    value: https://web-tools.booksy.pm
  - name: STATS_AND_REPORTS_V2_GCP_FIRESTORE_PROJECT_ID
    value: '{{ .Values.variables.statsAndReports.projectId }}'
  - name: STATS_AND_REPORTS_V2_GCP_FIRESTORE_COLLECTION_NAME
    value: '{{ .Values.variables.statsAndReports.collectionName }}'
  - name: USE_NEW_SECRETS_MANAGER
    value: 'True'
  - name: DD_TRACE_SAMPLING_RULES
    value: |
      [
        {"sample_rate": 1.0, "tags": {"error": "*"}},
        {"sample_rate": 1.0, "tags": {"slow_trace": "true"}},
        {"sample_rate": 0.01}
      ]
  - name: DD_SPAN_SAMPLING_RULES
    value: |
      [
        {"service": "*", "sample_rate": 0.01}
      ]

resources:
  api:
    limits:
      cpu: 2000m
      memory: 1Gi
    requests:
      cpu: 200m
      memory: 1Gi
  admin:
    limits:
      cpu: 2000m
      memory: 1Gi
    requests:
      cpu: 200m
      memory: 1Gi
  celeryBeat:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 100m
      memory: 512Mi
  celeryAllQueueWorker:
    limits:
      cpu: 2000m
      memory: 1.3Gi
    requests:
      cpu: 500m
      memory: 1.3Gi
  grpcApi:
    limits:
      cpu: 500m
      memory: 1Gi
    requests:
      cpu: 100m
      memory: 1Gi
  publicApi:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 250m
      memory: 1Gi
  tools:
    limits:
      cpu: 1000m
      memory: 2Gi
    requests:
      cpu: 250m
      memory: 256Mi
  init:
    limits:
      cpu: 2000m
      memory: 2Gi
    requests:
      cpu: 1000m
      memory: 2Gi
  dbInit:
    limits:
      cpu: 2000m
      memory: 2Gi
    requests:
      cpu: 1000m
      memory: 2Gi
  elasticInit:
    limits:
      cpu: 2000m
      memory: 2Gi
    requests:
      cpu: 1000m
      memory: 2Gi
  executeScriptsInit:
    limits:
      cpu: 2000m
      memory: 2Gi
    requests:
      cpu: 1000m
      memory: 2Gi
  otherInit:
    limits:
      cpu: 2000m
      memory: 2Gi
    requests:
      cpu: 1000m
      memory: 2Gi

nodeSelector:
  adminApi:
    role: pvm-worker
  admin:
    role: pvm-worker
  api:
    role: pvm-worker
  celeryAllQueueWorker:
    role: pvm-worker
  celeryBeat:
    role: pvm-worker
  grpcApi:
    role: pvm-worker
  publicApi:
    role: pvm-worker
  reportsApi:
    role: pvm-worker
  tools:
    role: ondemand_tools
  init:
    role: pvm-worker
  dbInit:
    role: pvm-worker
  elasticInit:
    role: pvm-worker
  executeScriptsInit:
    role: pvm-worker
  otherInit:
    role: pvm-worker

tolerations:
  tools: 
    - key: ondemand_tools
      value: "true"
      effect: NoSchedule

providerCalendarImporter:
  # fill in t1 - bucket per env
  bucketName: 
  projectId: provider-calendar-importer-dev
