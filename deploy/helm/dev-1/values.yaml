core:
  deploy:
    adminApi: true
    admin: true
    api: true
    celery: true
    grpcApi: true
    publicApi: true
    reportsApi: true
    searchApi: true
    tools: false
    init: true
  replicaCount:
    adminApi: 2
    admin: 2
    api: 2
    grpcApi: 2
    publicApi: 2
    reportsApi: 2
    searchApi: 2
  autoscaling:
    api:
      enabled: true
      minReplicas: 2
      maxReplicas: 4
      CPUTargetUtilizationPercentage: 50
    celeryIndex:
      enabled: true
      datadogMetricsEnabled: true
    searchApi:
      enabled: true
      minReplicas: 2
      maxReplicas: 4
      CPUTargetUtilizationPercentage: 50
    celeryPriority:
      enabled: true
      minReplicas: 1
      maxReplicas: 2
      datadogMetricsEnabled: true
      datadogTargetMetricAverageValue: 1000
    celeryPush:
      enabled: true
      minReplicas: 1
      maxReplicas: 2
      datadogMetricsEnabled: true
      datadogTargetMetricAverageValue: 1000
    celeryRegular:
      enabled: true
      minReplicas: 1
      maxReplicas: 2
      datadogMetricsEnabled: true
      datadogTargetMetricAverageValue: 1000
    celerySegment:
      enabled: true
      minReplicas: 1
      maxReplicas: 2
      datadogMetricsEnabled: true
      datadogTargetMetricAverageValue: 1000
  variables:
    ciTests: true
    booksyVariant: test
    deploymentLevel: anthosmesh
    booksyCountryCode: pl
    booksyRedisDB: 1
    environmentDomain: anthos-mesh.booksy.pm
    subdomainHost: booksy-subdomains-api-grpc.subdomains.svc.cluster.local:9099
    launchDarklyProxyURL: http://launch-darkly.launch-darkly.svc.cluster.local:8030
    postgresql:
      masterHost: postgresql-pl.postgresql-pl.svc.cluster.local
      paymentsHost: postgresql-pl.postgresql-pl.svc.cluster.local
      draftsHost: postgresql-pl.postgresql-pl.svc.cluster.local
      LBslaveHosts: postgresql-pl.postgresql-pl.svc.cluster.local
      LBslaveReportsHosts: postgresql-pl.postgresql-pl.svc.cluster.local
    elasticsearch:
      host: elasticsearch.elasticsearch.svc.cluster.local
      port: 9200
    redis:
      celeryBeatHost:  redis-celery.redis.svc.cluster.local
      celeryBrokerHost: redis-celery.redis.svc.cluster.local
      celeryBulkCacheHost: redis-email-bulk.redis.svc.cluster.local
      riverRedisHost: redis-river.redis.svc.cluster.local
      celeryBackendHost: redis-celery-backend.redis.svc.cluster.local
      enableThrottling: true
      throttlingRedisHost: redis-throttling.redis.svc.cluster.local
      enableRedisFifo: true
      redisFifoHost: redis-fifo.redis.svc.cluster.local
    kafka:
      resourceNamespace: "pm.booksy.t1.{{ .Release.Namespace }}.{{ .Values.variables.booksyCountryCode }}"
      brokerBootstrapServers: seed-ee4af287.cpepgn3conq6u97h1pe0.byoc.prd.cloud.redpanda.com:9092
      schemaRegistryUrl: https://schema-registry-5535af92.cpepgn3conq6u97h1pe0.byoc.prd.cloud.redpanda.com:30081
      kafkaConnectUrl: https://api-ee4af287.cpepgn3conq6u97h1pe0.byoc.prd.cloud.redpanda.com/v1/kafka-connect/clusters/redpanda
      oauthTokenUrl: https://auth.prd.cloud.redpanda.com/oauth/token
      resourcesSecretManager:
        projectID: bks-kafka-dev-1-eu-w1
  datadog:
    enable: true
    enableAdminLogging: true
    enableApiLogging: true
    enableCeleryLogging: true
    enableGRPCApiLogging: true
    enablePublicApiLogging: true
    enableReportsApiLogging: true
    enableSearchApiLogging: true
    enableInitLogging: true
    enableCeleryCustomMetrics: true
    enableUWSGIApiCustomMetrics: true
    enableUWSGISearchApiCustomMetrics: true
  ingressApi:
    enable: false
  ingressSearchApi:
    enable: false
  ingressPublicApi:
    enable: false
  ingressReportsApi:
    enable: false
  ingressAdmin:
    enable: false
  ingressAdminApi:
    enable: false
  adminConfig:
    useCustomConfig: false
    config: |
      adyen_3dsecure_enabled: true
      apns_sandbox_enabled: false
      b2b_referral_enabled: false
      business_registration_closed: false
      businesses_multi_categories: true
      can_create_umbrella: false
      consent_form_sms_request: false
      cors__allow_all_origins: false
      current_business_android_force_update: false
      current_business_android_version: '459'
      current_business_ios_force_update: true
      current_business_ios_version: '966'
      current_customer_android_force_update: false
      current_customer_android_version: '209'
      current_customer_ios_force_update: false
      current_customer_ios_version: '361'
      customer_registration_closed: false
      debug: false
      debug_sql: false
      elearning_enabled: false
      family_and_friends_enabled: false
      group_permissions_enabled: true
      iap_android: true
      iap_braintree: true
      iap_ios: true
      invoices_enabled: false
      marketpay_avs_enabled: false
      marketpay_enabled: true
      noshow_feature_edu_mail: false
      noshow_mail: false
      onboarding_enabled: true
      popup_notifications_enabled: true
      popup_phase2: true
      popup_phase3: true
      popup_phase4: true
      pos: true
      pos__apple_pay: true
      pos__bsx: false
      pos__default_payment_provider_v2: stripe
      pos__google_pay: true
      pos__pay_by_app: true
      pos__prepayments: true
      pos__refunds: true
      pos__square: true
      pos__stripe_terminal: true
      unverified_push_payments: true
      vouchers_enabled: true
      waitlist_enabled: true
  image:
    tag: 1.277.1-dev.1
    branch: dev
