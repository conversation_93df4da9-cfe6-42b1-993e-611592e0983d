core:
  deploy:
    celeryAllQueueWorker: true
    celery: false
  #autoscaling:
    # api:
    #   enabled: true
    #   minReplicas: 4
    #   maxReplicas: 8
    #   CPUTargetUtilizationPercentage: 50

  variables:
    booksyVariant: live
    booksyCountryCode: pt
    booksyRedisDB: 5
    subdomainHost: booksy-subdomains-api-grpc.subdomains.svc.cluster.local:9099
    launchDarklyProxyURL: http://launch-darkly.launch-darkly.svc.cluster.local:8030
    authHost: auth-server.auth.svc.cluster.local:8010
    grpcClientProxyHost: http://grpc-it-client-proxy.grpc-it.svc.cluster.local:9911
    postgresql: 
      masterHost: bks-prd-1-eu-w1-pg-primary-lb-5432-cross-mesh.svc.cluster.local
      paymentsHost: **********
      draftsHost: **********
      LBslaveHosts: bks-prd-1-eu-w1-pg-replica-lb-5432-cross-mesh.svc.cluster.local
      LBslaveReportsHosts: bks-prd-1-eu-w1-pg-reports-replica-cross-mesh.svc.cluster.local
      pgcat:
        enabled: false
    elasticsearch:
      host: bks-prd-1-eu-w1-es-lb-cross-mesh.svc.cluster.local
      port: 9200
      numerOfReplicas: 2
      numerOfShards: 1
    redis:
      celeryBackendHost: bks-prd-1-eu-w1-redis-celery.svc.cluster.local
      celeryBeatHost: bks-prd-1-eu-w1-redis-celery.svc.cluster.local
      celeryBrokerHost: bks-prd-1-eu-w1-redis-celery.svc.cluster.local
      celeryBulkCacheHost: bks-prd-1-eu-w1-v2-redis-email-bulk.svc.cluster.local
      riverRedisHost: bks-prd-1-eu-w1-redis-river.svc.cluster.local
      enableThrottling: true
      throttlingRedisHost: bks-prd-1-eu-w1-redis-throttling.svc.cluster.local
      enableRedisFifo: true
      redisFifoHost: bks-prd-1-eu-w1-v2-redis-email-bulk.svc.cluster.local
      subdomainsRedisHost: bks-prd-1-eu-w1-redis-core-subdomains.svc.cluster.local
      simplifiedBookingRedisHost: bks-prd-1-eu-w1-redis-simplified-booking.svc.cluster.local

    boostClaims:
      GCSProjectID: bks-prd-1-eu-w1

    workloadIdentity:
      projectID: bks-prd-workloads-1
      
    captcha:
      projectID: bks-prd-workloads-1

    kafka:
      brokerBootstrapServers: seed-80b9beaf.cpbid5a77h8g0iu4fh0g.byoc.prd.cloud.redpanda.com:9092
      schemaRegistryUrl: https://schema-registry-ca76f2c9.cpbid5a77h8g0iu4fh0g.byoc.prd.cloud.redpanda.com:30081
      kafkaConnectUrl: https://api-80b9beaf.cpbid5a77h8g0iu4fh0g.byoc.prd.cloud.redpanda.com/v1/kafka-connect/clusters/redpanda

      resourcesSecretManager:
        projectID: bks-kafka-prd-1-eu-w1

  booksyWorkloadPriority:
    admin: p5
    api: p5
    celeryBeat: p5
    celeryAllQueueWorker: p5
    celeryEta: p5
    grpcApi: p5
    publicApi: p5
    reportsApi: p5
    searchApi: p5
    readOnlyApi: p5
    pubsubWorkerNotificationsWebhooks: p5
    pubsubWorkerProviderCalendarImporter: p5
    tools: p5
    checkQueue: p5
    init: p1
    dbInit: p1
    elasticInit: p1
    executeScriptsInit: p1
    otherInit: p1
