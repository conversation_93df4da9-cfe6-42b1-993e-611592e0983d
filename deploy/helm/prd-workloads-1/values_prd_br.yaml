core:
  image:
    repository: us-central1-docker.pkg.dev/bks-ar-pkg/images-prd/core
  pdb:
    api:
      maxUnavailable: 30%
    reportsApi:
      maxUnavailable: 30%

  replicaCount:
    celeryRegular: 3
    reportsApi: 4

  autoscaling:
    admin:
      enabled: true
      minReplicas: 4
      maxReplicas: 5
      CPUTargetUtilizationPercentage: 60
    api:
      enabled: true
      minReplicas: 4
      maxReplicas: 40
    celeryPriority:
      enabled: true
      minReplicas: 2
      maxReplicas: 8
      cpuHpaEnabled: true
      CPUTargetUtilizationPercentage: 25
      datadogMetricsEnabled: true
      datadogTargetMetricAverageValue: 5000
    celeryPush:
      enabled: true
      minReplicas: 1
      maxReplicas: 4
      datadogMetricsEnabled: true
      datadogTargetMetricAverageValue: 1000
    celeryRegular:
      enabled: true
      minReplicas: 1
      maxReplicas: 9
      cpuHpaEnabled: true
      CPUTargetUtilizationPercentage: 25
      datadogMetricsEnabled: true
      datadogTargetMetricAverageValue: 5000
    celerySegment:
      enabled: true
      minReplicas: 1
      maxReplicas: 10
      cpuHpaEnabled: true
      CPUTargetUtilizationPercentage: 25
      datadogMetricsEnabled: true
      datadogTargetMetricAverageValue: 5000
    searchApi:
      enabled: true
      minReplicas: 4
      maxReplicas: 40
      CPUTargetUtilizationPercentage: 30
  
  variables:
    apiUwsgiProcesses: 4
    booksyVariant: live
    booksyCountryCode: br
    booksyRedisDB: 1
    subdomainHost: booksy-subdomains-api-grpc.subdomains.svc.cluster.local:9099
    launchDarklyProxyURL: http://launch-darkly.launch-darkly.svc.cluster.local:8030
    authHost: auth-server.auth.svc.cluster.local:8010
    grpcClientProxyHost: http://grpc-br-client-proxy.grpc-br.svc.cluster.local:9911
    celeryPriorityProcesses: 6
    celeryRegularProcesses: 6
    celerySegmentProcesses: 6
    postgresql: 
      masterHost: postgres-primary-lb-5432-cross-mesh.svc.cluster.local
      paymentsHost: *********
      draftsHost: **********
      LBslaveHosts: postgres-replica-lb-5432-cross-mesh.svc.cluster.local
      LBslaveReportsHosts: postgres-lb-reports-replica-cross-mesh.svc.cluster.local
      pgcat:
        enabled: false
    elasticsearch:
      host: elasticsearch-load-balancer-cross-mesh.svc.cluster.local
      port: 9200
      numerOfReplicas: 2
      numerOfShards: 3
    redis:
      celeryBackendHost: bks-prd-2-us-c1-v2-redis-celery.svc.cluster.local
      celeryBeatHost: bks-prd-2-us-c1-v2-redis-celery.svc.cluster.local
      celeryBrokerHost: bks-prd-2-us-c1-v2-redis-celery.svc.cluster.local
      celeryBulkCacheHost: bks-prd-2-us-c1-v2-redis-email-bulk.svc.cluster.local
      riverRedisHost: bks-prd-2-us-c1-redis-river.svc.cluster.local
      enableThrottling: true
      throttlingRedisHost: bks-prd-2-us-c1-redis-throttling.svc.cluster.local
      enableRedisFifo: true
      redisFifoHost: bks-prd-2-us-c1-v2-redis-email-bulk.svc.cluster.local
      subdomainsRedisHost: bks-prd-2-us-c1-redis-core-subdomains.svc.cluster.local
      simplifiedBookingRedisHost: bks-prd-2-us-c1-redis-simplified-booking.svc.cluster.local

    boostClaims:
      GCSProjectID: bks-prd-2-us-c1

    workloadIdentity:
      projectID: bks-prd-workloads-1
      
    captcha:
      projectID: bks-prd-workloads-1

    kafka:
      brokerBootstrapServers: seed-312ca77d.csuvtaaj0bofp9ktsi70.byoc.prd.cloud.redpanda.com:9092
      schemaRegistryUrl: https://schema-registry-8c152181.csuvtaaj0bofp9ktsi70.byoc.prd.cloud.redpanda.com:30081
      kafkaConnectUrl: https://api-312ca77d.csuvtaaj0bofp9ktsi70.byoc.prd.cloud.redpanda.com/v1/kafka-connect/clusters/redpanda

      resourcesSecretManager:
        projectID: bks-kafka-prd-2-us-c1
    
  resources:
    api:
      limits:
        cpu: 4000m
        memory: 3Gi
      requests:
        cpu: 3000m
        memory: 3Gi
    celeryIndex:
      limits:
        cpu: 3000m
        memory: 4Gi
      requests:
        cpu: 2000m
        memory: 4Gi
    celeryPriority:
      limits:
        cpu: 5000m
        memory: 6Gi
      requests:
        cpu: 4000m
        memory: 6Gi
    celeryRegular:
      limits:
        cpu: 5000m
        memory: 6Gi
      requests:
        cpu: 4000m
        memory: 6Gi
    celerySegment:
      limits:
        cpu: 5000m
        memory: 6Gi
      requests:
        cpu: 4000m
        memory: 6Gi
    reportsApi:
      limits:
        cpu: 2000m
        memory: 4Gi
      requests:
        cpu: 2000m
        memory: 4Gi
    tools:
      limits:
        cpu: 4000m
        memory: 10Gi
      requests:
        cpu: 900m
        memory: 9Gi

  booksyWorkloadPriority:
    admin: p3
    api: p2
    celeryBeat: p2
    celeryEta: p2
    celeryIndex: p2
    celeryPriority: p2
    celeryPush: p2
    celeryRegular: p2
    celerySegment: p2
    grpcApi: p2
    publicApi: p5
    reportsApi: p3
    searchApi: p2
    readOnlyApi: p5
    pubsubWorkerNotificationsWebhooks: p5
    pubsubWorkerProviderCalendarImporter: p5
    tools: p5
    checkQueue: p5
    init: p1
    dbInit: p1
    elasticInit: p1
    executeScriptsInit: p1
    otherInit: p1
