core:
  image:
    repository: europe-west1-docker.pkg.dev/bks-ar-pkg/images/core
    pullPolicy: IfNotPresent
    pullSecret: gcr-json-key
  deployment:
    mesh: true
    meshOnlyService: false
  hooks: true
  deploy:
    adminApi: false
    admin: true
    api: true
    celery: true
    celeryEta: true
    grpcApi: true
    publicApi: true
    reportsApi: true
    searchApi: true
    tools: false
    init: false
    dbInit: true
    elasticInit: true
    executeScriptsInit: true
    otherInit: true
    readOnlyApi: false
    pubsubWorkerProviderCalendarImporter: true
    pubsubWorkerFrenchCertification: false
    pubsubWorkerGiftcards: false
    pubsubWorkerEcommerce: false
    pubsubWorkerNotificationsWebhooks: true
  pdb:
    adminApi:
      enable: true
      enableMaxUnavailable: true
      maxUnavailable: 50%
    admin:
      enable: true
      enableMaxUnavailable: true
      maxUnavailable: 50%
    api:
      enable: true
      enableMaxUnavailable: true
      maxUnavailable: 50%
    celeryEta:
      enable: true
    grpcApi:
      enable: true
      enableMaxUnavailable: true
      maxUnavailable: 50%
    publicApi:
      enable: true
      enableMaxUnavailable: true
      maxUnavailable: 50%
    reportsApi:
      enable: true
      enableMaxUnavailable: true
      maxUnavailable: 50%
    searchApi:
      enable: true
      enableMaxUnavailable: true
      maxUnavailable: 50%
  replicaCount:
    adminApi: 2
    admin: 2
    api: 2
    grpcApi: 2
    publicApi: 2
    reportsApi: 2
    searchApi: 2
    celeryEta: 2
    pubsubWorkerProviderCalendarImporter: 1
    pubsubWorkerEcommerce: 1
    pubsubWorkerNotificationsWebhooks: 1
  autoscaling:
    api:
      enabled: true
      minReplicas: 4
      maxReplicas: 8
      CPUTargetUtilizationPercentage: 35
    celeryIndex:
      enabled: true
      cpuHpaEnabled: true
      datadogMetricsEnabled: true
      CPUTargetUtilizationPercentage: 75
    celeryAllQueueWorker:
      enabled: true
      cpuHpaEnabled: true
      datadogMetricsEnabled: true
    searchApi:
      enabled: true
      minReplicas: 4
      maxReplicas: 8
      CPUTargetUtilizationPercentage: 50
  datadog:
    enable: true
    enableAdminLogging: true
    enableApiLogging: true
    enableCeleryLogging: true
    enableGRPCApiLogging: true
    enablePublicApiLogging: true
    enableReportsApiLogging: true
    enableSearchApiLogging: true
    enableInitLogging: true
    enableCeleryCustomMetrics: true
    enableUWSGIApiCustomMetrics: true
    enableUWSGISearchApiCustomMetrics: true
    enableDatabaseMonitoring: full
    enablepubsubWorkerLogging: true
  extraEnvs:
    - name: DD_TRACE_SAMPLING_RULES
      value: |
        [
          {"sample_rate": 1.0, "tags": {"error": "*"}},
          {"sample_rate": 1.0, "tags": {"slow_trace": "true"}},
          {"sample_rate": 0.01}
        ]
    - name: DD_SPAN_SAMPLING_RULES
      value: |
        [
          {"service": "*", "sample_rate": 0.01}
        ]
    - name: DD_PSYCOPG_TRACE_CONNECT
      value: "true"

  variables:
    gcpGenAiProjectId: bks-genai-prd
    useUnifiedEnvProd: true
    apiUwsgiThreads: 5
    apiUwsgiListenQueue: 400
    adminUwsgiRssReloadThreshold: 700
    searchApiUwsgiThreads: 4
    environmentDomain: booksy.com
    deploymentLevel: prod
    deeplinksGrpcHost: deeplinks-grpc-api.deeplinks.svc.cluster.local:8010
    qrCodes:
      GCSProjectID: bks-prd-5-global
      GCSBucketName: bks-prd-5-global-qr-codes
      GCSDomainName: qr-codes.booksy.com
    fiscalArchives:
      GCSProjectID: bks-prd-1-eu-w1
      GCSBucketName: bks-prd-1-eu-w1-fiscal-archives
    pubsub:
      projectID: bks-prd-5-global
    ecommerce:
      pubsub:
        projectID: bks-prd-5-global
    giftcards:
      pubsub:
        projectID: bks-prd-5-global
    secretManager:
      projectID: bks-secrets-prd
    workloadIdentity:
      useWorkloadIdentity: true
    invoicingGrpcHost: invoicing-grpc-api.invoicing.svc.cluster.local:8010
    timeslotRestHost: timeslot-service-{{ .Values.variables.booksyCountryCode }}-rest-api.timeslot-service-{{ .Values.variables.booksyCountryCode }}:3000
    searchServiceHost: search-service-{{ .Values.variables.booksyCountryCode }}.search-service-{{ .Values.variables.booksyCountryCode }}.svc.cluster.local:80
    kafka:
      resourceNamespace: "com.booksy.{{ .Values.variables.booksyCountryCode }}"
      oauthTokenUrl: https://auth.prd.cloud.redpanda.com/oauth/token
    enableConnectionDrain: true

    importWizard:
      gsBusinessImagesBucketPrefix: bks-admin-import-wizard-prd-import-wizard-business-images

  serviceAdmin:
    enableNEG: false
  serviceApi:
    enableNEG: false
  serviceGrpcApi:
    type: LoadBalancer
    allowGlobalAccess: true
  servicePublicApi:
    enableNEG: false
  serviceReportsApi:
    enableNEG: false
  serviceSearchApi:
    enableNEG: false
  ingressApi:
    enable: false
  ingressSearchApi:
    enable: false
  ingressPublicApi:
    enable: false
  ingressReportsApi:
    enable: false
  ingressAdmin:
    enable: false
  ingressAdminApi:
    enable: false
  adminConfig:
    useCustomConfig: false
    #nodeSelector:
    #  api:
    #    api_node: "true"
    #
    #tolerations:
    #  api:
    #  - key: "api_node"
    #    value: "true"
    #    operator: "Equal"
    #    effect: "NoSchedule"
    
  providerCalendarImporter:
    bucketName:
    projectId: provider-calendar-importer-prd
  resources:
    pubsubWorkerProviderCalendarImporter:
      limits:
        cpu: 1
        memory: 2Gi
      requests:
        cpu: 1
        memory: 2Gi
    pubsubWorkerNotificationsWebhooks:
      limits:
        cpu: 1
        memory: 2Gi
      requests:
        cpu: 1
        memory: 2Gi
  nodeSelector:
    adminApi:
      api_node: "true"
    admin:
      api_node: "true"
    api:
      api_node: "true"
    celeryBeat:
      async_node: "true"
    celeryIndex:
      async_node: "true"
    celeryPriority:
      async_node: "true"
    celeryPushWorker:
      async_node: "true"
    celeryRegular:
      async_node: "true"
    celerySegment:
      async_node: "true"
    celeryAllQueueWorker:
      async_node: "true"
    celeryEta:
      async_node: "true"
    grpcApi:
      api_node: "true"
    publicApi:
      api_node: "true"
    reportsApi:
      api_node: "true"
    searchApi:
      api_node: "true"
    tools:
      tools_node: "true"
    pubsubWorkerEcommerce:
      async_node: "true"
    pubsubWorkerGiftcards:
      async_node: "true"
    pubsubWorkerNotificationsWebhooks:
      async_node: "true"
    pubsubWorkerFrenchCertification:
      async_node: "true"
    pubsubWorkerProviderCalendarImporter:
      async_node: "true"
    # init jobs
    init:
      init_node: "true"
    dbInit:
      init_node: "true"
    elasticInit:
      init_node: "true"
    executeScriptsInit:
      init_node: "true"
    otherInit:
      init_node: "true"

  tolerations:
    adminApi:
    - key: "api_node"
      value: "true"
      operator: "Equal"
      effect: "NoSchedule"
    admin:
    - key: "api_node"
      value: "true"
      operator: "Equal"
      effect: "NoSchedule"
    api:
    - key: "api_node"
      value: "true"
      operator: "Equal"
      effect: "NoSchedule"
    celeryBeat:
    - key: "async_node"
      value: "true"
      operator: "Equal"
      effect: "NoSchedule"
    celeryIndex:
    - key: "async_node"
      value: "true"
      operator: "Equal"
      effect: "NoSchedule"
    celeryPriority:
    - key: "async_node"
      value: "true"
      operator: "Equal"
      effect: "NoSchedule"
    celeryPushWorker:
    - key: "async_node"
      value: "true"
      operator: "Equal"
      effect: "NoSchedule"
    celeryRegular:
    - key: "async_node"
      value: "true"
      operator: "Equal"
      effect: "NoSchedule"
    celerySegment:
    - key: "async_node"
      value: "true"
      operator: "Equal"
      effect: "NoSchedule"
    celeryAllQueueWorker:
    - key: "async_node"
      value: "true"
      operator: "Equal"
      effect: "NoSchedule"
    celeryEta:
    - key: "async_node"
      value: "true"
      operator: "Equal"
      effect: "NoSchedule"
    grpcApi:
    - key: "api_node"
      value: "true"
      operator: "Equal"
      effect: "NoSchedule"
    publicApi:
    - key: "api_node"
      value: "true"
      operator: "Equal"
      effect: "NoSchedule"
    reportsApi:
    - key: "api_node"
      value: "true"
      operator: "Equal"
      effect: "NoSchedule"
    searchApi:
    - key: "api_node"
      value: "true"
      operator: "Equal"
      effect: "NoSchedule"
    tools:
    - key: "tools_node"
      value: "true"
      operator: "Equal"
      effect: "NoSchedule"
    pubsubWorkerEcommerce:
    - key: "async_node"
      value: "true"
      operator: "Equal"
      effect: "NoSchedule"
    pubsubWorkerGiftcards:
    - key: "async_node"
      value: "true"
      operator: "Equal"
      effect: "NoSchedule"
    pubsubWorkerNotificationsWebhooks:
    - key: "async_node"
      value: "true"
      operator: "Equal"
      effect: "NoSchedule"
    pubsubWorkerFrenchCertification:
    - key: "async_node"
      value: "true"
      operator: "Equal"
      effect: "NoSchedule"
    pubsubWorkerProviderCalendarImporter:
    - key: "async_node"
      value: "true"
      operator: "Equal"
      effect: "NoSchedule"
    # init jobs
    init:
    - key: "init_node"
      value: "true"
      operator: "Equal"
      effect: "NoSchedule"
    dbInit:
    - key: "init_node"
      value: "true"
      operator: "Equal"
      effect: "NoSchedule"
    elasticInit:
    - key: "init_node"
      value: "true"
      operator: "Equal"
      effect: "NoSchedule"
    executeScriptsInit:
    - key: "init_node"
      value: "true"
      operator: "Equal"
      effect: "NoSchedule"
    otherInit:
    - key: "init_node"
      value: "true"
      operator: "Equal"
      effect: "NoSchedule"
