core:
  pdb:
    api:
      maxUnavailable: 10%
    grpcApi:
      maxUnavailable: 30%
    publicApi:
      maxUnavailable: 10%
    reportsApi:
      maxUnavailable: 10%
    searchApi:
      maxUnavailable: 30%

  deploy:
    pubsubWorkerEcommerce: true

  replicaCount:
    celeryPriority: 3
    celeryRegular: 3
    publicApi: 8
    reportsApi: 10
    grpcApi: 6
    
  autoscaling:
    admin:
      enabled: true
      minReplicas: 5
      maxReplicas: 6
    api:
      enabled: true
      minReplicas: 10
      maxReplicas: 200
    celeryPriority:
      enabled: true
      minReplicas: 3
      maxReplicas: 5
      datadogMetricsEnabled: true
      datadogTargetMetricAverageValue: 5000
    celeryPush:
      enabled: true
      minReplicas: 1
      maxReplicas: 4
      datadogMetricsEnabled: true
      datadogTargetMetricAverageValue: 1000
    celeryRegular:
      enabled: true
      minReplicas: 3
      maxReplicas: 5
      datadogMetricsEnabled: true
      datadogTargetMetricAverageValue: 5000
    celerySegment:
      enabled: true
      minReplicas: 1
      maxReplicas: 8
      datadogMetricsEnabled: true
      datadogTargetMetricAverageValue: 5000
    searchApi:
      enabled: true
      minReplicas: 5
      maxReplicas: 30
      CPUTargetUtilizationPercentage: 45

  variables:
    apiUwsgiProcesses: 4
    booksyVariant: live
    booksyCountryCode: pl
    booksyRedisDB: 1
    subdomainHost: booksy-subdomains-api-grpc.subdomains.svc.cluster.local:9099
    launchDarklyProxyURL: http://launch-darkly.launch-darkly.svc.cluster.local:8030
    authHost: auth-server.auth.svc.cluster.local:8010
    grpcClientProxyHost: http://grpc-pl-client-proxy.grpc-pl.svc.cluster.local:9911
    bgcGrpcHost: booksy-gift-cards-api-pl-grpc-api.booksy-gift-cards-api-pl.svc.cluster.local:8010
    mergerUrl: booksy-merger-pl-grpc.booksy-merger-pl.svc.cluster.local:3000
    postgresql: 
      masterHost: bks-prd-3-eu-w1-pg-primary-lb-5432-cross-mesh.svc.cluster.local
      paymentsHost: **********
      draftsHost: **********
      # secondaryReplicaHost: ***********
      LBslaveHosts: bks-prd-3-eu-w1-pg-replica-lb-5432-cross-mesh.svc.cluster.local
      LBslaveReportsHosts: bks-prd-3-eu-w1-pg-reports-replica-cross-mesh.svc.cluster.local
      secondaryReplica:
        enabled: false
    elasticsearch:
      host: bks-prd-3-eu-w1-es-lb-cross-mesh.svc.cluster.local
      port: 9200
      numerOfReplicas: 2
      numerOfShards: 5
    redis:
      celeryBackendHost: bks-prd-3-eu-w1-redis-celery.svc.cluster.local
      celeryBeatHost: bks-prd-3-eu-w1-redis-celery.svc.cluster.local
      celeryBrokerHost: bks-prd-3-eu-w1-redis-celery.svc.cluster.local
      celeryBulkCacheHost: bks-prd-3-eu-w1-redis-email-bulk.svc.cluster.local
      riverRedisHost: bks-prd-3-eu-w1-redis-river.svc.cluster.local
      enableThrottling: true
      throttlingRedisHost: bks-prd-3-eu-w1-redis-throttling.svc.cluster.local
      enableRedisFifo: true
      redisFifoHost: bks-prd-3-eu-w1-redis-fifo.svc.cluster.local
      subdomainsRedisHost: bks-prd-3-eu-w1-redis-core-subdomains.svc.cluster.local
      simplifiedBookingRedisHost: bks-prd-3-eu-w1-redis-simplified-booking.svc.cluster.local

    boostClaims:
      GCSProjectID: bks-prd-3-eu-w1

    publicApiUwsgiProcesses: 4
    searchApiUwsgiProcesses: 4
    reportsApiUwsgiProcesses: 4
    celeryPriorityProcesses: 15
    celeryRegularProcesses: 15
    celerySegmentProcesses: 15

    workloadIdentity:
      projectID: bks-prd-workloads-1
      
    captcha:
      projectID: bks-prd-workloads-1

    kafka:
      brokerBootstrapServers: seed-80b9beaf.cpbid5a77h8g0iu4fh0g.byoc.prd.cloud.redpanda.com:9092
      schemaRegistryUrl: https://schema-registry-ca76f2c9.cpbid5a77h8g0iu4fh0g.byoc.prd.cloud.redpanda.com:30081
      kafkaConnectUrl: https://api-80b9beaf.cpbid5a77h8g0iu4fh0g.byoc.prd.cloud.redpanda.com/v1/kafka-connect/clusters/redpanda

      resourcesSecretManager:
        projectID: bks-kafka-prd-1-eu-w1

  resources:
    api:
      limits:
        cpu: 4000m
        memory: 3Gi
      requests:
        cpu: 3000m
        memory: 3Gi
    celeryIndex:
      limits:
        cpu: 4000m
        memory: 8Gi
      requests:
        cpu: 3000m
        memory: 8Gi
    celeryPriority:
      limits:
        cpu: 10000m
        memory: 10Gi
      requests:
        cpu: 10000m
        memory: 10Gi
    celeryRegular:
      limits:
        cpu: 10000m
        memory: 10Gi
      requests:
        cpu: 10000m
        memory: 10Gi
    celerySegment:
      limits:
        cpu: 10000m
        memory: 10Gi
      requests:
        cpu: 10000m
        memory: 10Gi
    reportsApi:
      limits:
        cpu: 2000m
        memory: 4Gi
      requests:
        cpu: 2000m
        memory: 4Gi
    searchApi:
      limits:
        cpu: 4000m
        memory: 3Gi
      requests:
        cpu: 3000m
        memory: 3Gi
    publicApi:
      limits:
        memory: 2Gi
    tools:
      limits:
        cpu: 4000m
        memory: 10Gi
      requests:
        cpu: 900m
        memory: 9Gi
    pubsubWorkerEcommerce:
      limits:
        cpu: 1
        memory: 2Gi
      requests:
        cpu: 1
        memory: 2Gi

  booksyWorkloadPriority:
    admin: p3
    api: p1
    celeryBeat: p1
    celeryAllQueueWorker: p1
    celeryEta: p1
    celeryIndex: p1
    celeryPriority: p1
    celeryPush: p1
    celeryRegular: p1
    celerySegment: p1
    grpcApi: p1
    publicApi: p3
    reportsApi: p2
    searchApi: p1
    pubsubWorkerEcommerce: p2
    pubsubWorkerNotificationsWebhooks: p2
    pubsubWorkerProviderCalendarImporter: p2
    tools: p5
    checkQueue: p5
    init: p1
    dbInit: p1
    elasticInit: p1
    executeScriptsInit: p1
    otherInit: p1
