core:
  pdb:
    api:
      maxUnavailable: 20%
    grpcApi:
      maxUnavailable: 30%
    reportsApi:
      maxUnavailable: 30%

  replicaCount:
    celeryPriority: 3
    celeryRegular: 3
    reportsApi: 6
    grpcApi: 4

  autoscaling:
    admin:
      enabled: true
      minReplicas: 4
      maxReplicas: 5
      CPUTargetUtilizationPercentage: 60
    api:
      enabled: true
      minReplicas: 10
      maxReplicas: 45
    celeryPriority:
      enabled: true
      minReplicas: 2
      maxReplicas: 5
      datadogMetricsEnabled: true
      datadogTargetMetricAverageValue: 10000
    celeryPush:
      enabled: true
      minReplicas: 1
      maxReplicas: 2
      datadogMetricsEnabled: true
      datadogTargetMetricAverageValue: 1000
    celeryRegular:
      enabled: true
      minReplicas: 2
      maxReplicas: 3
      datadogMetricsEnabled: true
      datadogTargetMetricAverageValue: 10000
    celerySegment:
      enabled: true
      minReplicas: 1
      maxReplicas: 5
      datadogMetricsEnabled: true
      datadogTargetMetricAverageValue: 5000
    searchApi:
      enabled: true
      minReplicas: 4
      maxReplicas: 15

  variables:
    apiUwsgiProcesses: 4
    booksyVariant: live
    booksyCountryCode: gb
    booksyRedisDB: 3
    subdomainHost: booksy-subdomains-api-grpc.subdomains.svc.cluster.local:9099
    launchDarklyProxyURL: http://launch-darkly.launch-darkly.svc.cluster.local:8030
    authHost: auth-server.auth.svc.cluster.local:8010
    grpcClientProxyHost: http://grpc-gb-client-proxy.grpc-gb.svc.cluster.local:9911
    searchApiUwsgiProcesses: 4
    celeryRegularProcesses: 15
    celeryPriorityProcesses: 15
    celerySegmentProcesses: 15
    postgresql: 
      masterHost: bks-prd-1-eu-w1-pg-primary-lb-5432-cross-mesh.svc.cluster.local
      paymentsHost: **********
      draftsHost: **********
      LBslaveHosts: bks-prd-1-eu-w1-pg-replica-lb-5432-cross-mesh.svc.cluster.local
      LBslaveReportsHosts: bks-prd-1-eu-w1-pg-reports-replica-cross-mesh.svc.cluster.local
      pgcat:
        enabled: false
    elasticsearch:
      host: bks-prd-1-eu-w1-es-lb-cross-mesh.svc.cluster.local
      port: 9200
      numerOfReplicas: 2
      numerOfShards: 3
    redis:
      celeryBackendHost: bks-prd-1-eu-w1-redis-celery.svc.cluster.local
      celeryBeatHost: bks-prd-1-eu-w1-redis-celery.svc.cluster.local
      celeryBrokerHost: bks-prd-1-eu-w1-redis-celery.svc.cluster.local
      celeryBulkCacheHost: bks-prd-1-eu-w1-v2-redis-email-bulk.svc.cluster.local
      riverRedisHost: bks-prd-1-eu-w1-redis-river.svc.cluster.local
      enableThrottling: true
      throttlingRedisHost: bks-prd-1-eu-w1-redis-throttling.svc.cluster.local
      enableRedisFifo: true
      redisFifoHost: bks-prd-1-eu-w1-v2-redis-email-bulk.svc.cluster.local
      subdomainsRedisHost: bks-prd-1-eu-w1-redis-core-subdomains.svc.cluster.local
      simplifiedBookingRedisHost: bks-prd-1-eu-w1-redis-simplified-booking.svc.cluster.local

    boostClaims:
      GCSProjectID: bks-prd-1-eu-w1

    workloadIdentity:
      projectID: bks-prd-workloads-1
      
    captcha:
      projectID: bks-prd-workloads-1

    kafka:
      brokerBootstrapServers: seed-80b9beaf.cpbid5a77h8g0iu4fh0g.byoc.prd.cloud.redpanda.com:9092
      schemaRegistryUrl: https://schema-registry-ca76f2c9.cpbid5a77h8g0iu4fh0g.byoc.prd.cloud.redpanda.com:30081
      kafkaConnectUrl: https://api-80b9beaf.cpbid5a77h8g0iu4fh0g.byoc.prd.cloud.redpanda.com/v1/kafka-connect/clusters/redpanda

      resourcesSecretManager:
        projectID: bks-kafka-prd-1-eu-w1

  resources:
    api:
      limits:
        cpu: 4000m
        memory: 3Gi
      requests:
        cpu: 3000m
        memory: 3Gi
    celeryIndex:
      limits:
        cpu: 4000m
        memory: 6Gi
      requests:
        cpu: 3000m
        memory: 6Gi
    celeryPriority:
      limits:
        cpu: 10000m
        memory: 10Gi
      requests:
        cpu: 10000m
        memory: 10Gi
    celeryRegular:
      limits:
        cpu: 10000m
        memory: 10Gi
      requests:
        cpu: 10000m
        memory: 10Gi
    celerySegment:
      limits:
        cpu: 10000m
        memory: 10Gi
      requests:
        cpu: 10000m
        memory: 10Gi
    reportsApi:
      limits:
        cpu: 2000m
        memory: 4Gi
      requests:
        cpu: 2000m
        memory: 4Gi
    searchApi:
      limits:
        cpu: 4000m
        memory: 3Gi
      requests:
        cpu: 3000m
        memory: 3Gi
    tools:
      limits:
        cpu: 4000m
        memory: 10Gi
      requests:
        cpu: 900m
        memory: 9Gi

  booksyWorkloadPriority:
    admin: p3
    api: p2
    celeryBeat: p2
    celeryEta: p2
    celeryIndex: p2
    celeryPriority: p2
    celeryPush: p2
    celeryRegular: p2
    celerySegment: p2
    grpcApi: p2
    publicApi: p4
    reportsApi: p3
    searchApi: p2
    readOnlyApi: p5
    pubsubWorkerNotificationsWebhooks: p3
    pubsubWorkerProviderCalendarImporter: p3
    tools: p5
    checkQueue: p5
    init: p1
    dbInit: p1
    elasticInit: p1
    executeScriptsInit: p1
    otherInit: p1
