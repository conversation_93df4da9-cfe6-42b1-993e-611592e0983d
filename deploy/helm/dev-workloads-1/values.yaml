core:
  deploy:
    admin: true
    api: true
    celeryAllQueueWorker: true
    celeryEta: true
    grpcApi: true
    publicApi: true
    reportsApi: true
    searchApi: true
    tools: false
    init: false
    dbInit: true
    elasticInit: true
    executeScriptsInit: true
    otherInit: true

  variables:
    subdomainHost: booksy-subdomains-api-grpc.subdomains.svc.cluster.local:9099
    authHost: auth-server.auth.svc.cluster.local:8010
    deeplinksGrpcHost: deeplinks-grpc-api.deeplinks.svc.cluster.local:8010
    loggingUseStdout: "false"
    truncate_logs: "false"
    apiUwsgiProcesses: 1
    apiUwsgiThreads: 4
    adminUwsgiProcesses: 1
    adminUwsgiThreads: 4
    celeryAllQueueWorkerProcesses: 4

    fiscalArchives:
      GCSProjectID: bks-dev-3-eu-w1
      GCSBucketName: bks-dev-3-eu-w1-fiscal-archives
  
    pubsub:
      projectID: bks-dev-3-eu-w1
      
    ecommerce:
      pubsub:
        projectID: bks-dev-3-eu-w1
  
    redis:
      celeryBackendHost: redis-email-bulk
      celeryBrokerHost: redis-email-bulk
      celeryBulkCacheHost: redis-email-bulk
  
    providerCalendar:
      firebase:
        projectNumber: "297235164397"

    kafka:
      resourceNamespace: "pm.booksy.t1.{{ .Release.Namespace }}.{{ .Values.variables.booksyCountryCode }}"
      brokerBootstrapServers: seed-ee4af287.cpepgn3conq6u97h1pe0.byoc.prd.cloud.redpanda.com:9092
      schemaRegistryUrl: https://schema-registry-5535af92.cpepgn3conq6u97h1pe0.byoc.prd.cloud.redpanda.com:30081
      kafkaConnectUrl: https://api-ee4af287.cpepgn3conq6u97h1pe0.byoc.prd.cloud.redpanda.com/v1/kafka-connect/clusters/redpanda
      oauthTokenUrl: https://auth.prd.cloud.redpanda.com/oauth/token
      resourcesSecretManager:
        projectID: bks-kafka-dev-1-eu-w1

    importWizard:
      gsBusinessImagesBucketPrefix: bks-admin-import-wizard-dev-import-wizard-business-images
  
  extraEnvs:
    - name: WEB_TOOLS_URL
      value: https://web-tools.booksy.pm
  
  ingressApi:
    enable: false
  ingressAdmin:
    enable: false
  ingressGrpcApi:
    enable: false
  ingressPublicApi:
    enable: false
  ingressReportsApi:
    enable: false
  ingressSearchApi:
    enable: false

  resources:
    api:
      limits:
        cpu: 2000m
        memory: 1Gi
      requests:
        cpu: 200m
        memory: 1Gi
    admin:
      limits:
        cpu: 2000m
        memory: 1Gi
      requests:
        cpu: 200m
        memory: 1Gi
    celeryBeat:
      limits:
        cpu: 500m
        memory: 512Mi
      requests:
        cpu: 100m
        memory: 512Mi
    celeryAllQueueWorker:
      limits:
        cpu: 2000m
        memory: 1.3Gi
      requests:
        cpu: 500m
        memory: 1.3Gi
    grpcApi:
      limits:
        cpu: 500m
        memory: 1Gi
      requests:
        cpu: 100m
        memory: 1Gi
    publicApi:
      limits:
        cpu: 1000m
        memory: 1Gi
      requests:
        cpu: 250m
        memory: 1Gi
    tools:
      limits:
        cpu: 1000m
        memory: 2Gi
      requests:
        cpu: 250m
        memory: 256Mi
    init:
      limits:
        cpu: 2000m
        memory: 2Gi
      requests:
        cpu: 1000m
        memory: 2Gi
    dbInit:
      limits:
        cpu: 2000m
        memory: 2Gi
      requests:
        cpu: 1000m
        memory: 2Gi
    elasticInit:
      limits:
        cpu: 2000m
        memory: 2Gi
      requests:
        cpu: 1000m
        memory: 2Gi
    executeScriptsInit:
      limits:
        cpu: 2000m
        memory: 2Gi
      requests:
        cpu: 1000m
        memory: 2Gi
    otherInit:
      limits:
        cpu: 2000m
        memory: 2Gi
      requests:
        cpu: 1000m
        memory: 2Gi
  
  nodeSelector:
    admin:
      apis_node: "true"
    api:
      apis_node: "true"
    readOnlyApi:
      apis_node: "true"
    celeryBeat:
      apis_node: "true"
    celeryAllQueueWorker:
      apis_node: "true"
    celeryEta:
      apis_node: "true"
    celeryIndex:
      apis_node: "true"
    celeryPriority:
      apis_node: "true"
    celeryPushWorker:
      apis_node: "true"
    celeryRegular:
      apis_node: "true"
    celerySegment:
      apis_node: "true"
    grpcApi:
      apis_node: "true"
    publicApi:
      apis_node: "true"
    reportsApi:
      apis_node: "true"
    searchApi:
      apis_node: "true"
    pubsubWorkerEcommerce:
      apis_node: "true"
    pubsubWorkerGiftcards:
      apis_node: "true"
    pubsubWorkerNotificationsWebhooks:
      apis_node: "true"
    pubsubWorkerFrenchCertification:
      apis_node: "true"
    pubsubWorkerProviderCalendarImporter:
      apis_node: "true"
    tools:
      apis_node: "true"
    checkQueue:
      apis_node: "true"
    init:
      apis_node: "true"
    dbInit:
      apis_node: "true"
    elasticInit:
      apis_node: "true"
    executeScriptsInit:
      apis_node: "true"
    otherInit:
      apis_node: "true"
  
  tolerations:
    admin:
      - key: "apis_node"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
    api:
      - key: "apis_node"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
    readOnlyApi:
      - key: "apis_node"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
    celeryBeat:
      - key: "apis_node"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
    celeryAllQueueWorker:
      - key: "apis_node"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
    celeryEta:
      - key: "apis_node"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
    celeryIndex:
      - key: "apis_node"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
    celeryPriority:
      - key: "apis_node"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
    celeryPushWorker:
      - key: "apis_node"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
    celeryRegular:
      - key: "apis_node"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
    celerySegment:
      - key: "apis_node"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
    grpcApi:
      - key: "apis_node"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
    publicApi:
      - key: "apis_node"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
    reportsApi:
      - key: "apis_node"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
    searchApi:
      - key: "apis_node"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
    pubsubWorkerEcommerce:
      - key: "apis_node"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
    pubsubWorkerGiftcards:
      - key: "apis_node"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
    pubsubWorkerNotificationsWebhooks:
      - key: "apis_node"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
    pubsubWorkerFrenchCertification:
      - key: "apis_node"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
    pubsubWorkerProviderCalendarImporter:
      - key: "apis_node"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
    tools:
      - key: "apis_node"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
    checkQueue:
      - key: "apis_node"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
    init:
      - key: "apis_node"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
    dbInit:
      - key: "apis_node"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
    elasticInit:
      - key: "apis_node"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
    executeScriptsInit:
      - key: "apis_node"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
    otherInit:
      - key: "apis_node"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"
  
  providerCalendarImporter:
    # fill in t1 - bucket per env
    bucketName: 
    projectId: provider-calendar-importer-dev