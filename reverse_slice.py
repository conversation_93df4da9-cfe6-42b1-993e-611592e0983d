#!/usr/bin/env python3
import os, ast, argparse
from collections import defaultdict, deque

# -------- utils

def module_name(root, path):
    rel = os.path.relpath(path, root).replace(os.sep, "/")
    if rel.endswith("__init__.py"):
        rel = rel[:-12]
    elif rel.endswith(".py"):
        rel = rel[:-3]
    parts = [p for p in rel.split("/") if p]
    return ".".join(parts)

def qualname(mod, cls_stack, func_stack):
    parts = [mod] + cls_stack + func_stack
    return ".".join([p for p in parts if p])

def attr_chain(node):
    # returns list of names for a.b.c -> ["a","b","c"], else []
    names = []
    while isinstance(node, ast.Attribute):
        names.append(node.attr)
        node = node.value
    if isinstance(node, ast.Name):
        names.append(node.id)
        return list(reversed(names))
    return []

# -------- index & analysis

class Indexer(ast.NodeVisitor):
    def __init__(self, modname):
        self.modname = modname
        self.cls_stack = []
        self.func_stack = []
        self.defs = set()          # fully-qualified functions/methods defined here
        self.simple_to_full = defaultdict(set)  # "foo" -> {"pkg.mod.foo", ...}
        self.calls = defaultdict(set)  # caller_fq -> {callee_simple or callee_full or special nodes}
        self.allocs = defaultdict(set) # caller_fq -> {"varname" known to be MyClass}
        self.target_hits = set()       # functions that (directly) use MyClass.method
        # set externally:
        self.target_class = None
        self.target_method = None

    # ---- context helpers
    def curf(self):
        return qualname(self.modname, self.cls_stack, self.func_stack)

    def visit_ClassDef(self, node):
        self.cls_stack.append(node.name)
        self.generic_visit(node)
        self.cls_stack.pop()

    def visit_FunctionDef(self, node):
        self.func_stack.append(node.name)
        fq = self.curf()
        self.defs.add(fq)
        self.simple_to_full[node.name].add(fq)
        self.generic_visit(node)
        self.func_stack.pop()

    visit_AsyncFunctionDef = visit_FunctionDef

    def visit_Assign(self, node):
        # track x = MyClass() or x = pkg.MyClass()
        if not self.func_stack:  # only inside functions/methods
            return
        targets = [t.id for t in node.targets if isinstance(t, ast.Name)]
        if not targets:
            return
        val = node.value
        if isinstance(val, ast.Call):
            # MyClass() or pkg.MyClass()
            cname = None
            if isinstance(val.func, ast.Name) and val.func.id == self.target_class:
                cname = self.target_class
            else:
                ch = attr_chain(val.func)
                if len(ch) >= 2 and ch[-1] == self.target_class:
                    cname = self.target_class
            if cname:
                self.allocs[self.curf()].update(targets)

    def visit_Call(self, node):
        if not self.func_stack:
            return
        caller = self.curf()

        # 1) record general callee for later caller-of-caller traversal
        callee_simple = None
        callee_full_guess = None

        if isinstance(node.func, ast.Name):
            callee_simple = node.func.id
        elif isinstance(node.func, ast.Attribute):
            chain = attr_chain(node.func)  # e.g. ["module", "func"] or ["obj","method"]
            if len(chain) >= 2:
                # module.func or obj.method
                callee_simple = chain[-1]
                # If looks like module.func and module is this module name, make a full guess
                mod_prefix = ".".join(chain[:-1])
                # cheap guess: module.func where module matches a known prefix of this project
                if mod_prefix and (mod_prefix == self.modname or mod_prefix.startswith(self.modname.split(".")[0])):
                    callee_full_guess = mod_prefix + "." + callee_simple

        if callee_full_guess:
            self.calls[caller].add(callee_full_guess)
        elif callee_simple:
            self.calls[caller].add(callee_simple)

        # 2) target usage heuristics
        # a) MyClass.method(...)
        is_direct_class_method = False
        if isinstance(node.func, ast.Attribute):
            chain = attr_chain(node.func)
            if len(chain) >= 2 and chain[-2] == self.target_class and chain[-1] == self.target_method:
                is_direct_class_method = True

        # b) x.method(...) where x previously allocated as MyClass()
        is_instance_method = False
        if isinstance(node.func, ast.Attribute) and node.func.attr == self.target_method:
            base = node.func.value
            if isinstance(base, ast.Name):
                if base.id in self.allocs.get(caller, set()):
                    is_instance_method = True

        if is_direct_class_method or is_instance_method:
            self.target_hits.add(caller)

        self.generic_visit(node)

def walk_project(root):
    pyfiles = []
    for base, _, files in os.walk(root):
        if any(seg in base for seg in (os.sep+"venv", os.sep+".venv", os.sep+"__pycache__", os.sep+".git")):
            continue
        for fn in files:
            if fn.endswith(".py"):
                pyfiles.append(os.path.join(base, fn))
    return pyfiles

def build_indices(root, target_class, target_method):
    defs_all = set()
    simple_to_full_all = defaultdict(set)
    calls_all = defaultdict(set)
    target_hit_funcs = set()

    for path in walk_project(root):
        try:
            mod = module_name(root, path)
            with open(path, "r", encoding="utf-8") as f:
                src = f.read()
            tree = ast.parse(src, filename=path)
        except Exception:
            continue
        ix = Indexer(mod)
        ix.target_class = target_class
        ix.target_method = target_method
        ix.visit(tree)

        defs_all |= ix.defs
        for k, v in ix.simple_to_full.items():
            simple_to_full_all[k].update(v)
        for a, bs in ix.calls.items():
            calls_all[a].update(bs)
        target_hit_funcs |= ix.target_hits

    return defs_all, simple_to_full_all, calls_all, target_hit_funcs

# Resolve ambiguous simple names: only keep if unique
def resolve_calls(calls, defs_all, simple_to_full):
    resolved = defaultdict(set)
    for caller, callees in calls.items():
        for c in callees:
            if c in defs_all:
                resolved[caller].add(c)
            else:
                cands = simple_to_full.get(c, set())
                if len(cands) == 1:
                    resolved[caller].add(next(iter(cands)))
                # else: skip ambiguous external/unknown
    return resolved

def bfs_reverse(caller_to_callee, target_node, seeds, max_depth):
    # Build reverse index
    rev = defaultdict(set)
    for a, bs in caller_to_callee.items():
        for b in bs:
            rev[b].add(a)

    # we include a special node for the target method; edges from seed callers -> target_node
    for s in seeds:
        rev[target_node].add(s)

    seen = {target_node}
    q = deque([(target_node, 0)])
    nodes = {target_node} | set(seeds)
    edges = set((s, target_node) for s in seeds)

    while q:
        n, d = q.popleft()
        if d == max_depth:
            continue
        for caller in rev.get(n, ()):
            if caller not in nodes:
                nodes.add(caller)
            edges.add((caller, n))
            if caller not in seen:
                seen.add(caller)
                q.append((caller, d + 1))
    return nodes, edges

def write_dot(out_path, nodes, edges, target_node, seeds):
    with open(out_path, "w", encoding="utf-8") as f:
        f.write("digraph ReverseUsages {\n  rankdir=LR;\n")
        for n in nodes:
            if n == target_node:
                f.write(f'  "{n}" [shape=doubleoctagon, style=filled, fillcolor=lightgray];\n')
            elif n in seeds:
                f.write(f'  "{n}" [shape=doublecircle];\n')
            else:
                f.write(f'  "{n}" [shape=ellipse];\n')
        for a, b in edges:
            f.write(f'  "{a}" -> "{b}";\n')
        f.write("}\n")

def main():
    ap = argparse.ArgumentParser(description="Static reverse usages of Class.method (approx).")
    ap.add_argument("--root", default=".", help="Project root")
    ap.add_argument("--class", dest="klass", required=True, help="Class name, e.g. MyClass")
    ap.add_argument("--method", dest="method", required=True, help="Method name, e.g. do_it")
    ap.add_argument("--depth", type=int, default=2, help="Caller depth")
    ap.add_argument("--out", default="reverse_usages.dot")
    args = ap.parse_args()

    defs_all, simple_to_full, calls, seeds = build_indices(args.root, args.klass, args.method)
    calls_resolved = resolve_calls(calls, defs_all, simple_to_full)

    target_node = f"{args.klass}.{args.method}"
    nodes, edges = bfs_reverse(calls_resolved, target_node, seeds, args.depth)
    write_dot(args.out, nodes, edges, target_node, seeds)

    print(f"Target '{target_node}': {len(seeds)} direct usages.")
    print(f"Wrote {args.out} with {len(nodes)} nodes / {len(edges)} edges.")

if __name__ == "__main__":
    main()
