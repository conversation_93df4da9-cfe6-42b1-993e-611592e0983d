from lib.kafka_events.tests.generic_schema_stability_test_case import BaseSchemaStabilityTest
from webapps.payment_gateway.messages.transaction_created_or_updated import (
    TransactionCreatedOrUpdatedEvent,
    TransactionCreatedOrUpdatedEventKey,
)

# pylint: disable=line-too-long


class TestTransactionEventSchemaStability(BaseSchemaStabilityTest):
    event_class = TransactionCreatedOrUpdatedEvent
    key_class = TransactionCreatedOrUpdatedEventKey
    owner_team = 'New Financial Services AKA Golden Hamsters'

    expected_event_schema = '{"type": "record", "name": "TransactionCreatedOrUpdatedEvent", "fields": [{"name": "id", "type": {"type": "string", "logicalType": "uuid"}}, {"name": "created", "type": [{"type": "long", "logicalType": "timestamp-millis"}, "null"]}, {"name": "updated", "type": [{"type": "long", "logicalType": "timestamp-millis"}, "null"]}, {"name": "receiver", "type": [{"type": "record", "name": "Wallet", "fields": [{"name": "statement_name", "type": "string"}, {"name": "owner_type", "type": {"type": "enum", "name": "WalletOwnerType", "symbols": ["booksy", "customer", "business", "anonymous"]}}, {"name": "business_id", "type": ["null", "long"], "default": null}, {"name": "user_id", "type": ["null", "long"], "default": null}]}, "null"]}, {"name": "sender", "type": "Wallet"}, {"name": "amount", "type": "long"}, {"name": "fee_amount", "type": "long"}, {"name": "status", "type": {"type": "enum", "name": "BalanceTransactionStatus", "symbols": ["processing", "success", "failed", "canceled"]}}, {"name": "payment_method", "type": [{"type": "enum", "name": "PaymentMethodType", "symbols": ["terminal", "card", "google_pay", "apple_pay", "tap_to_pay", "booksy_gift_card", "blik", "keyed_in_payment", "klarna"], "doc": "only payment methods that use some external provider"}, "null"]}, {"name": "payment_provider_code", "type": {"type": "enum", "name": "PaymentProviderCode", "symbols": ["adyen", "stripe", "booksy_gift_cards"]}}, {"name": "transaction_type", "type": {"type": "enum", "name": "BalanceTransactionType", "symbols": ["payment", "refund", "dispute", "payout", "fee", "transfer_fund"]}}, {"name": "parent_balance_transaction_id", "type": [{"type": "string", "logicalType": "uuid"}, "null"]}, {"name": "payment", "type": [{"type": "record", "name": "PaymentDetails", "fields": [{"name": "status", "type": {"type": "enum", "name": "PaymentStatus", "symbols": ["new", "sent_for_authorization", "action_required", "authorized", "authorization_failed", "sent_for_capture", "captured", "capture_failed", "canceled"]}}, {"name": "error_code", "type": [{"type": "enum", "name": "PaymentError", "symbols": ["connection_error", "generic_error", "generic_payment_error", "generic_card_error", "not_permitted", "fraudulent", "restricted_card", "merchant_blacklist", "reenter_transaction", "bank_account_verification_failed", "debit_not_authorized", "refer_to_customer", "routing_number_invalid", "issuer_not_available", "bank_account_invalid", "processing_error_card", "authentication_required", "transfers_not_allowed", "stripe_account_problem", "account_country_invalid_address", "country_unsupported", "card_not_supported", "currency_not_supported", "invalid_account_card", "invalid_blik_code", "account_invalid", "account_not_verified_yet", "expired_card", "incorrect_cvc", "expired_card_at_appointment", "incorrect_number", "tax_id_invalid", "taxes_calculation_failed", "invalid_expiry_month", "invalid_expiry_year", "invalid_characters", "incorrect_pin", "incorrect_zip", "pin_required", "card_decline_rate_limit_exceeded", "card_declined", "phone_number_required", "email_invalid", "incorrect_address", "state_unsupported", "pin_try_exceeded", "terminal_location_country_unsupported", "insufficient_funds", "amount_too_large", "amount_too_small", "invalid_amount", "charge_already_captured", "charge_already_refunded", "technical_problem", "instant_payouts_unsupported", "payouts_not_allowed", "timeout", "order_creation_failed", "order_required_settings", "order_status_invalid", "shipping_calculation_failed", "out_of_inventory", "payment_expired", "payment_failed", "url_invalid", "three_d_secure_problem", "booksy_gift_card_payment_failed", "card_read_timed_out", "tap_to_pay_device_tampered", "tap_to_pay_nfc_disabled", "location_services_disabled", "tap_to_pay_insecure_environment"], "doc": "Purpose of this enum is to provide a meaningful code for the client,\\n    not a technical one nor one for debugging\\n    (for debugging, more specific error codes are stored in payment_providers app)\\n    these codes may overlap with those from PSPs, but it is not a rule that they are the same!"}, "null"]}, {"name": "capture_date", "type": [{"type": "long", "logicalType": "timestamp-millis"}, "null"]}]}, "null"]}, {"name": "refund", "type": [{"type": "record", "name": "RefundDetails", "fields": [{"name": "reason", "type": ["string", "null"]}]}, "null"]}, {"name": "dispute", "type": [{"type": "record", "name": "DisputeDetails", "fields": [{"name": "type", "type": {"type": "enum", "name": "DisputeType", "symbols": ["chargeback", "second_chargeback", "reversed_chargeback"]}}]}, "null"]}, {"name": "payout", "type": [{"type": "record", "name": "PayoutDetails", "fields": [{"name": "payout_type", "type": {"type": "enum", "name": "PayoutType", "symbols": ["fast", "regular"]}}, {"name": "status", "type": {"type": "enum", "name": "PayoutStatus", "symbols": ["in_payment_processor", "in_transit", "arrived", "canceled", "failed"]}}, {"name": "expected_arrival_date", "type": [{"type": "long", "logicalType": "timestamp-millis"}, "null"]}, {"name": "error_code", "type": [{"type": "enum", "name": "PayoutError", "symbols": ["account_closed", "account_frozen", "bank_account_restricted", "bank_ownership_changed", "could_not_process", "debit_not_authorized", "declined", "insufficient_funds", "invalid_account_number", "incorrect_account_holder_name", "incorrect_account_holder_address", "incorrect_account_holder_tax_id", "invalid_currency", "no_account", "unsupported_card", "generic_error"]}, "null"]}]}, "null"]}, {"name": "fee", "type": [{"type": "record", "name": "FeeDetails", "fields": [{"name": "fee_type", "type": {"type": "enum", "name": "FeeType", "symbols": ["refund", "dispute", "payment_reversal", "funds_reinstated", "payout"]}}]}, "null"]}, {"name": "transfer_fund", "type": [{"type": "record", "name": "TransferFundDetails", "fields": [{"name": "origin", "type": {"type": "enum", "name": "TransferFundOrigin", "symbols": ["internal", "booksy_gift_cards"]}}]}, "null"]}, {"name": "source", "type": [{"type": "enum", "name": "BasketPaymentSource", "symbols": ["prepayment", "cancellation_fee", "payment", "booksy_pay"]}, "null"]}, {"name": "operator_id", "type": ["long", "null"]}, {"name": "transaction_id", "type": ["long", "null"]}, {"name": "appointment_id", "type": ["long", "null"]}, {"name": "staffers", "type": {"type": "array", "items": {"type": "record", "name": "Staffer", "fields": [{"name": "id", "type": "long"}]}, "name": "staffer"}, "default": []}, {"name": "provider_external_id", "type": ["null", "string"], "default": null}]}'
    expected_key_schema = '{"type": "record", "name": "TransactionCreatedOrUpdatedEventKey", "fields": [{"name": "business_id", "type": ["long", "null"]}, {"name": "balance_transaction_id", "type": {"type": "string", "logicalType": "uuid"}}]}'
