import uuid
from dataclasses import dataclass
from lib.point_of_sale.enums import BasketTipType, MakePaymentPaymentMethodType

# for "id" fields:
# pylint: disable=invalid-name


@dataclass(frozen=True)
class POSEntity:
    id: int
    receipt_footer_line_1: str | None
    receipt_footer_line_2: str | None
    commissions_enabled: bool
    registers_enabled: bool


@dataclass(frozen=True)
class PaymentRowEntity:
    id: int
    receipt_id: int


@dataclass(frozen=True)
class ReceiptDetailsEntity:
    id: int
    receipt_number: str
    assigned_number: str
    transaction_id: int
    customer_data: str | None
    customer_id: int | None
    customer_name: str | None
    customer_email: str | None


@dataclass
class HandleGiftCardTransactionEntity:
    appointment_status: str
    charge_bgc: bool


@dataclass(frozen=True)
class TransactionEntity:
    id: uuid.UUID


@dataclass(frozen=True)
class TransactionRegisterEntity:
    id: int | None
    is_open: bool | None


@dataclass(frozen=True)
class ExtraData:
    forwarded_ip: str
    language: str
    fingerprint: str
    user_agent: str


@dataclass(frozen=True)
class TipData:
    type: BasketTipType
    value: int


@dataclass(frozen=True)
class MakePaymentPaymentData:
    tip: TipData | None
    payment_method: MakePaymentPaymentMethodType
    tokenized_pm_id: uuid.UUID | None
    token: str | None


@dataclass(frozen=True)
class MakePaymentRequestEntity:
    appointment_id: int
    user_id: int
    payment_data: MakePaymentPaymentData
    extra_data: ExtraData


@dataclass(frozen=True)
class MakePaymentResponseEntity:
    basket_id: uuid.UUID | None
    errors: list[dict] | None = None


@dataclass(frozen=True)
class CalculatePaymentAmountRequestEntity:
    draft_id: uuid.UUID
    user_id: int
    extra_data: ExtraData
    tip: TipData | None = None
    booking_source_id: int | None = None


@dataclass(frozen=True)
class CalculatePaymentAmountResponseEntity:
    draft_id: uuid.UUID
    errors: list[dict] | None = None
