"""
The distributed locks in Redis (both with the Redlock algorithm) are
described here: https://redis.io/topics/distlock

There is an existing python implementation of the Redlock algorithm (which
we make a use of): https://github.com/SPSCommerce/redlock-py

"""

import logging

from django.conf import settings
from redlock import Lock, Redlock, MultipleRedlockException
from typing import Union

from lib.redis_client import from_url
from django.core.cache import cache
from settings.stripe import READER_LOCK_TIME_S

logger = logging.getLogger('booksy.redlock')


class RedlockError(Exception):
    pass


def redlock_client():
    connection_list = [from_url(settings.DJANGO_REDIS_CACHE_URL).connection_pool.connection_kwargs]
    return Redlock(connection_list=connection_list)


class AbstractLock:
    """A base class for creating simple time expiring locks.

    All classes inheriting from AbstractLock must override the `lock_name`
    and `lock_expiration_time_ms` attributes.
    """

    lock_name = None  # NOTE that name should be unique for all lock classes
    lock_expiration_time_ms = None

    @classmethod
    def _get_resource_name(cls, resource_id):
        return '{lock_name}:{resource_id}'.format(lock_name=cls.lock_name, resource_id=resource_id)

    @classmethod
    def lock(cls, resource_id) -> Union[Lock, bool]:
        if cls.lock_name is None:
            raise NotImplementedError()
        if cls.lock_expiration_time_ms is None:
            raise NotImplementedError()

        try:
            return redlock_client().lock(
                resource=cls._get_resource_name(resource_id), ttl=cls.lock_expiration_time_ms
            )
        except MultipleRedlockException as e:
            logger.exception(e)
            raise RedlockError(e)

    @classmethod
    def try_to_lock(cls, resource_id):
        """Tries to lock on resource

        :return: True if lock has been acquired or a Redis error occurred.
                 False if lock could not have been acquired gracefully.
        """

        try:
            lock_acquired = cls.lock(resource_id)
            if not lock_acquired:
                logger.error(
                    'Could not acquire lock: {} for resource: {}'.format(
                        cls.lock_name,
                        resource_id,
                    )
                )
                return False
        except RedlockError:
            # One or more Redis errors have occurred. The lock could not
            # have been acquired but we cannot block the request due to some
            # communication problems with Redis.
            pass
        return True

    @classmethod
    def unlock(cls, lock):
        """
        :param lock: named tuple obtained from successful lock() call
        """
        try:
            redlock_client().unlock(lock)
        except MultipleRedlockException as e:
            logger.exception(e)
            raise RedlockError(e)

    @classmethod
    def try_to_unlock(cls, lock):
        """
        :param lock: named tuple obtained from successful lock() call
        """
        try:
            cls.unlock(lock)
        except RedlockError:
            # One or more Redis errors have occurred. The lock could not
            # have been acquired but we cannot block the request due to some
            # communication problems with Redis.
            return False
        return True


class DjangoCacheLock:
    """
    An optional class for creating simple time expiring locks,
    where only resource_id is needed to unlock resource.
    If you do not have a specific reason, please use AbstractLock instead.

    All classes inheriting from DjangoCacheLock must override the `lock_name`
    and `lock_expiration_time_s` attributes.
    """

    lock_name = None  # NOTE that name should be unique for all lock classes
    lock_expiration_time_s = None

    @classmethod
    def _get_resource_name(cls, resource_id):
        return 'DJANGO_CACHE_{lock_name}_{resource_id}'.format(
            lock_name=cls.lock_name, resource_id=resource_id
        )

    @classmethod
    def lock(cls, resource_id):
        """
        Tries to lock on resource.

        :return: True if lock has been acquired.
                 False if given resource has been already locked.
        """
        return cls.basic_lock(
            cls._get_resource_name(resource_id),
            cls._get_resource_name(resource_id),
            cls.lock_expiration_time_s,
        )

    @staticmethod
    def basic_lock(resource_name, value, timeout):
        return cache.add(resource_name, value, timeout)

    @classmethod
    def unlock(cls, resource_id):
        """
        Tries to unlock resource.

        :return: True if lock has been removed.
                 False if given resource has not been locked.
        """
        return cls.basic_unlock(cls._get_resource_name(resource_id))

    @staticmethod
    def basic_unlock(resource_name):
        return cache.delete(resource_name)


class UserPosTransactionLock(AbstractLock):
    """This lock prevents creating multiple transactions by the same user
    within a 2 seconds debounce time range.
    """

    lock_name = 'user-pos-transaction-lock'
    lock_expiration_time_ms = 2000


class ConfirmAppointmentPrepaymentLock(AbstractLock):
    """Lock prevents creating multiple transactions for the same booking when
    business confirms appointment with prepayment.
    """

    lock_name = 'confirm-appointment-lock'
    lock_expiration_time_ms = 2000


class BusinessActionLock(AbstractLock):
    lock_name = 'business-action-lock'
    lock_expiration_time_ms = 2000


class BusinessTransactionActionLock(AbstractLock):
    lock_name = 'business-transaction-action-lock'
    lock_expiration_time_ms = 2000


class CustomerTransactionActionLock(AbstractLock):
    lock_name = 'customer-transaction-action-lock'
    lock_expiration_time_ms = 500


class CustomerActionBasketPaymentLock(AbstractLock):
    lock_name = 'customer-action-basket-payment-lock'
    lock_expiration_time_ms = 500


class BraintreeStaffActionLock(AbstractLock):
    lock_name = 'braintree-action-lock'
    lock_expiration_time_ms = 60_000  # 60 sek


class BillingCycleSwitchLock(AbstractLock):
    lock_name = 'billing-cycle-switch-lock'
    lock_expiration_time_ms = 3_600_000  # 1 hour


class BillingSubscriptionLock(AbstractLock):
    lock_name = 'billing-subscription-lock'
    lock_expiration_time_ms = 60_000


class BillingOneOffChargeLock(AbstractLock):
    lock_name = 'billing-one-off-charge-lock'
    lock_expiration_time_ms = 60_000


class BillingBoostLock(AbstractLock):
    lock_name = 'billing-boost-lock'
    lock_expiration_time_ms = 40_000


class BillingInitializeSubscriptionLock(AbstractLock):
    lock_name = 'billing-initialize-subscription-lock'
    lock_expiration_time_ms = 30_000


class BillingInitializeRetryChargeSubscriptionLock(AbstractLock):
    lock_name = 'billing-initialize-retry-charge-subscription-lock'
    lock_expiration_time_ms = 30_000


class BillingMigratedSubscriptionInitialTaskLock(AbstractLock):
    lock_name = 'billing-migrated-subscription-initial-task-lock'
    lock_expiration_time_ms = 15 * 60 * 1000


class KYCSignatoryLock(AbstractLock):
    lock_name = 'kyc-signatory-lock'
    lock_expiration_time_ms = 60000


class StripeAccountCreationLock(AbstractLock):
    lock_name = 'stripe-account-creation-lock'
    lock_expiration_time_ms = 15000


class InvoicingLock(AbstractLock):
    lock_name = 'invoicing-lock'
    lock_expiration_time_ms = 1000 * 60 * 15


class ProcessInvoicingErrorLock(AbstractLock):
    lock_name = 'process-invoicing-error-lock'
    lock_expiration_time_ms = 1000 * 60 * 15


class SyncWithNavisionLock(AbstractLock):
    lock_name = 'sync-with-navision-lock'
    lock_expiration_time_ms = 1000 * 60 * 15


class StripeUpdateStatusToFailLock(AbstractLock):
    lock_name = 'stripe-update-status-fail'
    lock_expiration_time_ms = 1000 * 60


class StripeRefreshTipsLock(AbstractLock):
    lock_name = 'stripe-refresh-tips'
    lock_expiration_time_ms = 1000 * 60


class BoostChargeLock(AbstractLock):
    lock_name = 'boost-charge-lock'
    lock_expiration_time_ms = 1000 * 60 * 10


class BoostReactivationLock(AbstractLock):
    lock_name = 'boost-reactivation-lock'
    lock_expiration_time_ms = 1000 * 60


class BoostRefundLock(AbstractLock):
    lock_name = 'boost-refund-lock'
    lock_expiration_time_ms = 1000 * 60 * 10


class BoostSetPaidLock(AbstractLock):
    lock_name = 'boost-set-paid-lock'
    lock_expiration_time_ms = 1000 * 60


class VisibilityPromotionPurchaseLock(AbstractLock):
    lock_name = 'visibility-promotion-purchase-lock'
    lock_expiration_time_ms = 1000 * 60


class StripeTerminalLock(DjangoCacheLock):
    lock_name = 'stripe-terminal-lock'
    lock_expiration_time_s = READER_LOCK_TIME_S


class ServiceVariantLock(AbstractLock):
    lock_name = 'service-variant-lock'
    lock_expiration_time_ms = 2000


class BooksyPayPaymentLock(AbstractLock):
    """
    Lock prevents creating multiple Booksy pay transactions for the same booking.
    """

    lock_name = 'booksy-pay-payment-lock'
    lock_expiration_time_ms = 2000


class FacebookUpdateServicesLock(AbstractLock):
    lock_name = 'facebook-update-services-lock'
    lock_expiration_time_ms = 2000


class EditTransactionLock(AbstractLock):
    lock_name = 'edit-transaction-lock'
    lock_expiration_time_ms = 2000


class HandleNoShowBGCTransfer(AbstractLock):
    lock_name = 'handle-no-show-bgc-transfer'
    lock_expiration_time_ms = 5000
