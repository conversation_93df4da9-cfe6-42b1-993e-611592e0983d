from django.utils.translation import gettext_lazy as _, pgettext_lazy

from lib.enums import StrChoicesEnum, StrEnum


class PaymentMethodType(StrChoicesEnum):
    # Online
    TERMINAL = 'terminal', _('Terminal')
    CARD = 'card', _('Card (Mobile Payment)')  # special type for PBA
    SQUARE = 'square', 'Square'
    GOOGLE_PAY = 'google_pay', _('Google Pay')
    APPLE_PAY = 'apple_pay', _('Apple Pay')
    TAP_TO_PAY = 'tap_to_pay', _('Tap To Pay')
    BLIK = 'blik', _('Blik')
    KEYED_IN_PAYMENT = 'keyed_in_payment', _('Keyed In Payment')
    KLARNA = 'klarna', _('Klarna')

    # Offline
    CASH = 'cash', _('Cash')
    CHECK = 'check', _('Check')
    CREDIT_CARD = 'credit_card', _('Credit Card')
    SUBSCRIPTION = 'subscription', _('Subscription Card')
    STORE_CREDIT = 'store_credit', _('Store Credit')
    BANK_TRANSFER = 'bank_transfer', _('Bank Transfer')
    AMERICAN_EXPRESS = 'american_express', 'American Express'
    PAYPAL = 'paypal', 'PayPal'
    BOOKSY_GIFT_CARD = 'booksy_gift_card', _('Booksy Gift Card')
    EGIFT_CARD = 'egift_card', _('Gift Card')
    MEMBERSHIP = 'membership', _('Membership')
    PACKAGE = 'package', _('Package')
    DIRECT_PAYMENT = 'direct_payment', _('Direct Payment')

    PAY_BY_APP_DONATIONS = 'pba_donations', _('Donations')  # deprecated
    # These two paymentTypes are deprecated. Still here, because of displaying
    # receips. >>
    GIFT_CARD = 'giftcard', _('Own GiftCard')
    VOUCHER = 'voucher', _('Voucher')
    # <<

    @classmethod
    def online_method(cls):
        return {
            cls.TERMINAL,
            cls.CARD,
            cls.GOOGLE_PAY,
            cls.APPLE_PAY,
            cls.BOOKSY_GIFT_CARD,
            cls.KEYED_IN_PAYMENT,
            cls.KLARNA,
        }

    @classmethod
    def basket_online_payments(cls):
        return [
            cls.TERMINAL,
            cls.CARD,
            cls.GOOGLE_PAY,
            cls.APPLE_PAY,
            cls.TAP_TO_PAY,
            cls.BLIK,
            cls.KEYED_IN_PAYMENT,
            cls.KLARNA,
        ]


class MakePaymentPaymentMethodType(StrChoicesEnum):
    CARD = 'card', _('Card (Mobile Payment)')
    GOOGLE_PAY = 'google_pay', _('Google Pay')
    APPLE_PAY = 'apple_pay', _('Apple Pay')
    BLIK = 'blik', _('Blik')
    BOOKSY_GIFT_CARD = 'booksy_gift_card', _('Booksy Gift Card')


class POSTaxType(StrChoicesEnum):
    EXCLUDED = 'excluded', _('Tax excluded, added to subtotal (US specific)')
    INCLUDED = 'included', _('Tax included in the product price (EU specific)')


class BasketType(StrEnum):
    PAYMENT = 'payment'
    DEPOSIT = 'deposit'


class BasketPaymentStatus(StrChoicesEnum):
    PENDING = 'pending', _('Pending')
    ACTION_REQUIRED = 'action_required', _('Action required')
    SUCCESS = 'success', _('Success')
    FAILED = 'failed', _('Failed')
    CANCELED = 'canceled', _('Canceled')


class BasketStatus(StrChoicesEnum):
    PENDING = 'pending', _('Pending')
    ACTION_REQUIRED = 'action_required', _('Action required')
    SUCCESS = 'success', _('Success')
    FAILED = 'failed', _('Failed')
    CANCELED = 'canceled', _('Canceled')
    SENT_FOR_REFUND = 'sent_for_refund', _('Sent for refund')
    REFUNDED = 'refunded', _('Refunded')
    CHARGEBACK = 'chargeback', _('Chargeback')
    CHARGEBACK_REVERSED = 'chargeback_reversed', _('Chargeback reversed')
    SECOND_CHARGEBACK = 'second_chargeback', _('Second Chargeback')


BASKET_STATUS_LABEL = {
    BasketStatus.PENDING: pgettext_lazy('receipt status', 'Pending'),
    BasketStatus.ACTION_REQUIRED: pgettext_lazy('receipt status', '3DSecure required'),
    BasketStatus.SUCCESS: pgettext_lazy('receipt status', 'Paid'),
    BasketStatus.FAILED: pgettext_lazy('receipt status', 'Failed'),
    BasketStatus.CANCELED: pgettext_lazy('receipt status', 'Cancelled'),
    BasketStatus.SENT_FOR_REFUND: pgettext_lazy('receipt status', 'Sent for refund'),
    BasketStatus.REFUNDED: pgettext_lazy('receipt status', 'Refunded'),
    BasketStatus.CHARGEBACK: pgettext_lazy('receipt status', 'Chargeback'),
    BasketStatus.CHARGEBACK_REVERSED: pgettext_lazy('receipt status', 'Chargeback Reversed'),
    BasketStatus.SECOND_CHARGEBACK: pgettext_lazy('receipt status', 'Second Chargeback'),
}


BASKET_STATUS_DESCRIPTION = {
    BasketStatus.ACTION_REQUIRED: pgettext_lazy(
        'receipt status', 'Transaction requires 3DSecure authentication'
    ),
    BasketStatus.SENT_FOR_REFUND: pgettext_lazy('receipt status', 'Refund proccess started'),
}


class BasketPaymentType(StrEnum):
    PAYMENT = 'payment'
    REFUND = 'refund'
    CHARGEBACK = 'dispute'
    CHARGEBACK_REVERSED = 'chargeback_reversed'
    SECOND_CHARGEBACK = 'second_chargeback'


class BasketItemType(StrChoicesEnum):
    ADDON = 'addon', _('Add-On')
    SERVICE = 'service', _('Service')
    PRODUCT = 'product', _('Product')
    DEPOSIT = 'deposit', _('Cancellation Fee')
    VOUCHER = 'voucher', _('Voucher')
    TRAVEL_FEE = 'travel_fee', _('Travel Fee')
    PREPAYMENT = 'prepayment', _('Prepayment')


class RelatedBasketItemType(StrEnum):
    SERVICE_VARIANT = 'service_variant'
    COMMODITY = 'commodity'
    VOUCHER = 'voucher'
    ADDON = 'addon'
    APPOINTMENT = 'appointment'
    SUBBOOKING = 'subbooking'


class BasketItemTaxType(StrChoicesEnum):
    """Its copy of POSTaxType form posv2 app."""

    EXCLUDED = 'excluded', _('Tax excluded, added to subtotal (US specific)')
    INCLUDED = 'included', _('Tax included in the product price (EU specific)')


class BasketTipType(StrEnum):
    PERCENT = 'percent'
    AMOUNT = 'amount'


class CancellationFeeAuthStatus(StrChoicesEnum):
    PENDING = 'pending', _('Pending')
    SUCCESS = 'success', _('Success')
    FAILED = 'failed', _('Failed')
    CANCELED = 'canceled', _('Canceled')


class BasketPaymentSource(StrChoicesEnum):
    PREPAYMENT = 'prepayment', _('Deposit')
    CANCELLATION_FEE = 'cancellation_fee', _('Cancellation Fee')
    PAYMENT = 'payment', _('Payment')
    BOOKSY_PAY = 'booksy_pay', _('Booksy Pay')


class BasketPaymentAnalyticsTrigger(StrChoicesEnum):
    CUSTOMER__PREPAYMENT_AUTH = 'customer__prepayment_auth', _('Customer deposit')
    BUSINESS__PREPAYMENT_REQUEST = 'business__prepayment_request', _('Business deposit request')
    BUSINESS__PREPAYMENT_CONFIRM = 'business__prepayment_confirm', _('Customer deposit')
    BUSINESS__CANCELLATION_FEE_CHARGE = 'business__cancellation_fee_charge', _(
        'Cancellation Fee Charge'
    )
    BUSINESS__MOBILE_PAYMENT_CFP = 'business__mobile_payment_cfp', _('Mobile Payment CFP')
    CUSTOMER__MOBILE_PAYMENT_CONFIRM = 'customer__mobile_payment_confirm', _(
        'Mobile Payment Confirm'
    )
    BUSINESS__MOBILE_PAYMENT_AUTO_ACCEPT = 'business__mobile_payment_auto_accept', _(
        'Mobile Payment Auto Accept'
    )
    BUSINESS__BOOKSY_CARD_READER_CFP = 'business__booksy_card_reader_cfp', _(
        'Booksy Card Reader CFP'
    )
    BUSINESS__OFFLINE_PAYMENT = 'business__offline_payment', _('Offline Payment')
    CUSTOMER__VOUCHER_ONLINE_PURCHASE = 'customer__voucher_online_purchase', _(
        'Voucher Online Purchase'
    )
    BUSINESS__TAP_TO_PAY = 'business__tap_to_pay', _('Tap to Pay')
    CUSTOMER__BOOKSY_PAY = 'customer__booksy_pay', _('Booksy Pay')
    BUSINESS__BLIK = 'business__blik', _('Blik')
    BUSINESS__KEYED_IN_PAYMENT = 'business__keyed_in_payment', _('Keyed In Payment')
    KIP_RETRY = 'keyed_in_payment_retry', _('Keyed In Payment Retry')
    BUSINESS__PREPAYMENT_AUTH = 'business__prepayment_auth', _('Prepayment Auth')


class BasketTipSource(StrChoicesEnum):
    PREPAYMENT = 'prepayment', _('Deposit')
    CHECKOUT = 'checkout', _('Checkout')
    CUSTOMER_APP = 'customer_app', _('Customer App')
    BCR = 'bcr', _('BCR')
    BOOKSY_PAY = 'booksy_pay', _('Booksy Pay')

    @classmethod
    def checkout_related_source(cls):
        return [
            cls.CHECKOUT,
            cls.CUSTOMER_APP,
            cls.BCR,
        ]


class ResponseEntityType(StrChoicesEnum):
    BASKET_PAYMENT_ENTITY = 'basket_payment_entity', _('Basket Payment Entity')
    BASKET_PAYMENT_CANCEL_RESULT_ENTITY = 'basket_payment_cancel_result_entity', _(
        'Basket Payment Cancel Result Entity'
    )


class BasketCustomerPaymentAction(StrEnum):
    CANCEL_PAYMENT = 'cancel_payment'
    MAKE_PAYMENT = 'make_payment'


class BasketElementType(StrChoicesEnum):
    SUBTOTAL = 'subtotal', _('Subtotal')
    DISCOUNT = 'discount', _('Discount')
    TAXES_AND_FEES = 'taxes_and_fees', _('Taxes & Fees')
    TIPS = 'tips', _('Tips')


class ConvenienceFeeMode(StrChoicesEnum):
    """
    PX_FULL_COST - fees are taken from pos plans
    SPLIT - fees from pos plans are overridden by convenience fee settings
    CX_FULL_COST - fees from pos plans are overridden by convenience fee settings
    """
    PX_FULL_COST = 'px_full_cost', _('Merchant covers all the fees (default)')
    SPLIT = 'split', _('Fees are split between merchant and customer (convenience fee)')
    CX_FULL_COST = 'cx_full_cost', _('Customer covers all the fees (convenience fee)')
