from redbeat.schedulers import <PERSON><PERSON>eat<PERSON>cheduler, RedBeatJSONEncoder, logger, json, get_redis

UPDATE_ENTRY_ERROR = """\

Couldn't update entry %r to redis schedule: %r. Contents: %r
"""


class RedBeatSchedulerReloadOnStart(RedBeatScheduler):
    def _ensure_config_from_file_take_precedence(self, dict_):
        logger.debug("beat: Enforce tasks config from files.")
        for name, entry in dict_.items():
            redis_key = self.Entry.generate_key(self.app, name)

            try:
                file_entry = self._maybe_entry(name, entry)
                redis_definition = file_entry.load_definition(redis_key)

                json_file_definition = json.dumps(
                    {
                        'name': file_entry.name,
                        'task': file_entry.task,
                        'args': file_entry.args,
                        'kwargs': file_entry.kwargs,
                        'options': file_entry.options,
                        'schedule': file_entry.schedule,
                        'enabled': file_entry.enabled,
                    },
                    cls=RedBeatJSONEncoder,
                )
                file_definition = file_entry.decode_definition(json_file_definition)

                if redis_definition != file_definition:
                    logger.warning(
                        "beat: Override definition from file. Old: %s New: %s",
                        redis_definition,
                        file_definition,
                    )
                    get_redis(self.app).hset(redis_key, 'definition', json_file_definition)
            except KeyError:
                continue
            except Exception as exc:  # pylint: disable=broad-exception-caught
                logger.error(UPDATE_ENTRY_ERROR, name, exc, entry)
                continue
        logger.debug("beat: Finished enforcing tasks config from files.")

    def update_from_dict(self, dict_):
        self._ensure_config_from_file_take_precedence(dict_)
        super().update_from_dict(dict_)
