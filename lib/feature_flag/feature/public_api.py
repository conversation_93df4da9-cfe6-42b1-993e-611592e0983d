from lib.feature_flag.enums import FeatureFlagAdapter
from lib.feature_flag.flag_base import BooleanFlag


class PublicAPIAppointmentChangedByUserFlag(BooleanFlag):
    flag_name = 'Feature_PublicAPIAppointmentChangedByUser'
    adapter = FeatureFlagAdapter.EPPO


class PublicAPIBooksyImporterEnabledFlag(BooleanFlag):
    flag_name = 'Feature_PublicAPIBooksyImporterEnabled'
    adapter = FeatureFlagAdapter.LD


class PublicAPICustomerDBOptimizationFlag(BooleanFlag):
    flag_name = 'Feature_PublicAPICustomerDBOptimization'
    adapter = FeatureFlagAdapter.EPPO


class PublicAPIPartnerAppMetadataExposedFlag(BooleanFlag):
    flag_name = 'Feature_PublicAPIPartnerAppMetadataExposed'
    adapter = FeatureFlagAdapter.LD


class PublicAPIResourceImportDBOptimizationFlag(BooleanFlag):
    flag_name = 'Feature_PublicAPIResourceImportDBOptimization'
    adapter = FeatureFlagAdapter.EPPO


class PublicAPIServiceVariantFreePriceValidation(BooleanFlag):
    flag_name = 'Fix_PublicAPIServiceVariantFreePriceValidation'
    adapter = FeatureFlagAdapter.EPPO


class PublicAPIServiceVariantStartsAtZeroFix(BooleanFlag):
    flag_name = 'Bug_PublicAPIServiceVariantStartsAtZeroFix'
    adapter = FeatureFlagAdapter.EPPO


class PublicAPIUserPersistenceRefactorFlag(BooleanFlag):
    flag_name = 'Feature_PublicAPIUserPersistenceRefactor'
    adapter = FeatureFlagAdapter.LD


class PublicAPIUserUpdatesFixes(BooleanFlag):
    flag_name = 'Feature_PublicAPIUserUpdatesFixes'
    adapter = FeatureFlagAdapter.EPPO


class PublicAPIWebhookEnabledFlag(BooleanFlag):
    flag_name = 'Feature_PublicAPIWebhookEnabled'
    adapter = FeatureFlagAdapter.LD
