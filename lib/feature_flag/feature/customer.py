from lib.feature_flag.enums import FeatureFlagAdapter
from lib.feature_flag.flag_base import BooleanFlag, DictFlag, StringFlag


class AppleDrfEndpointFlag(BooleanFlag):
    flag_name = 'Feature_AppleDrfEndpointFlag'
    adapter = FeatureFlagAdapter.EPPO


class AutomaticAccountLinkingFlag(BooleanFlag):
    flag_name = 'Feature_AutomaticAccountLinkingFlag'
    adapter = FeatureFlagAdapter.EPPO


class BooksyAuthTempExtraMetadataFlag(BooleanFlag):
    flag_name = 'Feature_BooksyAuthTempExtraMetadata'
    adapter = FeatureFlagAdapter.EPPO


class BooksyAuthTimeoutsFlag(DictFlag):
    flag_name = 'Feature_BooksyAuthTimeouts'
    adapter = FeatureFlagAdapter.EPPO


class CancellationFeeCustomerBookingFlowTestFlag(DictFlag):
    flag_name = 'cancelationfee-customer-booking-flow-test'
    adapter = FeatureFlagAdapter.EPPO


class CancellationFeeEppoExperimentTest(BooleanFlag):
    flag_name = 'CancelatioFeeEppoDelayTest'
    adapter = FeatureFlagAdapter.EPPO


class PeopleAlsoBookedFlag(BooleanFlag):
    flag_name = 'Feature_PeopleAlsoBookedFlag'
    adapter = FeatureFlagAdapter.EPPO


class RefactorSaveRegisterAgreements(BooleanFlag):
    flag_name = 'Refactor_SaveRegisterAgreements'
    adapter = FeatureFlagAdapter.EPPO


class SessionAuthImprovementFlag(BooleanFlag):
    flag_name = 'Feature_SessionAuthImprovement'
    adapter = FeatureFlagAdapter.EPPO


class CustomerRecommendedNewFlag(DictFlag):
    flag_name = 'Feature_CustomerRecommendedNew'
    adapter = FeatureFlagAdapter.EPPO


class UseLastBookingLocationInSelectedForYouFlag(BooleanFlag):
    flag_name = 'Feature_UseLastBookingLocationInSelectedForYou'
    adapter = FeatureFlagAdapter.EPPO


class CustomerMyBooksySugestedForYouGalleryFlag(DictFlag):
    flag_name = 'Feature_CustomerMyBooksySelectedForYouGallery'
    adapter = FeatureFlagAdapter.EPPO


class SimpleSerchableInRecommendedForYouFlag(BooleanFlag):
    flag_name = 'Feature_SimpleSerchableInRecommendedForYou'
    adapter = FeatureFlagAdapter.EPPO


class AutomaticGoogleAccountLinkingFlag(BooleanFlag):
    flag_name = 'Feature_AutomaticGoogleAccountLinkingFlag'
    adapter = FeatureFlagAdapter.EPPO


class SkipFavoriteCategoryUpdateFlag(BooleanFlag):
    flag_name = 'Feature_SkipFavoriteCategoryUpdate'
    adapter = FeatureFlagAdapter.EPPO


class FixAutomaticAccountLinkingCellPhoneFlag(BooleanFlag):
    flag_name = 'Fix_AutomaticAccountLinkingCellPhoneFlag'
    adapter = FeatureFlagAdapter.EPPO


class CustomerPhoneLimitsFlag(StringFlag):
    flag_name = 'Feature_CustomerPhoneLimits'
    adapter = FeatureFlagAdapter.EPPO


class PhoneLimitterFlag(BooleanFlag):
    flag_name = 'Feature_PhoneLimitter'
    adapter = FeatureFlagAdapter.EPPO


class NotificationBadgesFlag(DictFlag):
    flag_name = 'Fix_NotificationBadges'
    adapter = FeatureFlagAdapter.EPPO


class VistedLikedSelected4UFlag(DictFlag):
    """
    E.g.:
    {
        "Android": "3.28.1_634",
        "iPhone": "3.28.3"
    }
    """

    flag_name = 'Feature_VistedLikedSelected4'
    adapter = FeatureFlagAdapter.EPPO


class VistedLikedS4URandomizationFlag(DictFlag):
    flag_name = 'Feature_VistedLikedS4URandomization'
    adapter = FeatureFlagAdapter.EPPO


class CommaSeparatedListFieldFixFlag(BooleanFlag):
    flag_name = 'Fix_CommaSeparatedListField'
    adapter = FeatureFlagAdapter.EPPO


class S4UServiceVariantCountFixFlag(BooleanFlag):
    flag_name = 'Fix_S4UServiceVariantCount'
    adapter = FeatureFlagAdapter.EPPO


class VisitedLikedS4UOrderExperimentOnFlag(DictFlag):
    flag_name = 'Feature_VisitedLikedS4UOrderExperimentOnFlag'
    adapter = FeatureFlagAdapter.EPPO


class GalleryNameS4UExperimentOnFlag(BooleanFlag):
    flag_name = 'Feature_GalleryNameS4UExperimentOn'
    adapter = FeatureFlagAdapter.EPPO


class HCaptchaFailedLogFlag(BooleanFlag):
    flag_name = 'Feature_HCaptchaFailedLogBoolFlag'
    adapter = FeatureFlagAdapter.EPPO


class LogS4UVisitedLikedFlag(BooleanFlag):
    flag_name = 'Feature_LogS4UVisitedLikedFlag'
    adapter = FeatureFlagAdapter.EPPO


class CategoriesViewUserGenderFlag(BooleanFlag):
    flag_name = 'Feature_CategoriesViewUserGenderFlag'
    adapter = FeatureFlagAdapter.EPPO


class CategoriesOrderExperimentOnFlag(DictFlag):
    """
    E.g.:
    {
        'variant_a_position': 3,
        'variant_b_position': 5
    }
    """

    flag_name = 'Feature_CategoriesOrderExperimentOn'
    adapter = FeatureFlagAdapter.EPPO


class S4UAfterNegativeReviewFlag(BooleanFlag):
    flag_name = 'Feature_S4UAfterNegativeReviewFlag'
    adapter = FeatureFlagAdapter.EPPO


class TranslateCategoriesNamesFlag(BooleanFlag):
    flag_name = 'Feature_TranslateCategoriesNames'
    adapter = FeatureFlagAdapter.EPPO


class S4UAfterBookingCreatedFlag(BooleanFlag):
    flag_name = 'Feature_S4UAfterBookingCreated'
    adapter = FeatureFlagAdapter.EPPO


class S4UDeeplinkNailsCampaignFlag(BooleanFlag):
    flag_name = 'Feature_S4UDeeplinkNailsCampaign'
    adapter = FeatureFlagAdapter.EPPO
