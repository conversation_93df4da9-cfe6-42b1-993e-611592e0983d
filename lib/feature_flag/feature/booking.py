from lib.feature_flag.enums import FeatureFlagAdapter
from lib.feature_flag.flag_base import BooleanFlag, StringFlag


class DisableParallelSameComboDrawer(BooleanFlag):
    flag_name = 'Feature_DisableParallelSameComboDrawer'
    adapter = FeatureFlagAdapter.EPPO


class InvokeDecoupledDryRunFromSimplifiedBooking(BooleanFlag):
    flag_name = 'Feature_InvokeDecoupledDryRunFromSimplifiedBooking'
    adapter = FeatureFlagAdapter.EPPO


class SimplifiedBookingTargeting(BooleanFlag):
    flag_name = 'Feature_SimplifiedBookingTargeting'
    adapter = FeatureFlagAdapter.EPPO


class TrackAppointmentCreatedAnalytics(BooleanFlag):
    flag_name = 'Feature_TrackAppointmentCreatedAnalytics'
    adapter = FeatureFlagAdapter.EPPO


class SendCBStartedForCustomer(BooleanFlag):
    flag_name = 'Feature_SendCBStartedForCustomer'
    adapter = FeatureFlagAdapter.EPPO


class IncentivizeWithGiftCardStickyAssignmentSimulation(BooleanFlag):
    flag_name = 'Feature_IncentivizeWithGiftCardStickyAssignmentSimulation'
    adapter = FeatureFlagAdapter.EPPO


class DecoupledDryRunFromAPI(BooleanFlag):
    flag_name = 'Feature_DecoupledDryRunFromAPI'
    adapter = FeatureFlagAdapter.EPPO


class StreamlinedBookingAnalyticsWithSegment(BooleanFlag):
    flag_name = 'Feature_StreamlinedBookingAnalyticsWithSegment'
    adapter = FeatureFlagAdapter.EPPO


class BookAgainUseReadOnlyDBFlag(BooleanFlag):
    flag_name = 'Feature_BookAgainUseReadOnlyDBFlag'
    adapter = FeatureFlagAdapter.EPPO


class RemoveBookingCntInBulkFinishAppointments(BooleanFlag):
    flag_name = 'Feature_RemoveBookingCntInBulkFinishAppointments'
    adapter = FeatureFlagAdapter.EPPO


class BooksyGiftCardsComboServicesBugV0Flag(BooleanFlag):
    flag_name = 'Bug_BooksyGiftCardsComboServicesBugV0'
    adapter = FeatureFlagAdapter.EPPO


class UseNewAlgorithmForAdditionalTimeslotsExperiment(BooleanFlag):
    flag_name = 'Feature_UseNewAlgorithmForAdditionalTimeslotsExperiment'
    adapter = FeatureFlagAdapter.EPPO


class UseNewAlgorithmForAdditionalTimeslots(BooleanFlag):
    flag_name = 'Feature_UseNewAlgorithmForAdditionalTimeslots'
    adapter = FeatureFlagAdapter.EPPO


class RecordBenchmarkTimeSlotsMethod(StringFlag):
    flag_name = 'Feature_RecordBenchmarkTimeSlotsMethod'
    adapter = FeatureFlagAdapter.EPPO


class ExperimentTimeslotsComparison(StringFlag):
    flag_name = 'Experiment_TimeslotsComparison'
    adapter = FeatureFlagAdapter.EPPO
