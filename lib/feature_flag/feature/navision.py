from lib.feature_flag.enums import FeatureFlagAdapter
from lib.feature_flag.flag_base import BooleanFlag


class NavisionAutoAssignTaxGroupFlag(BooleanFlag):
    flag_name = 'Feature_NavisionAutoAssignTaxGroup'
    adapter = FeatureFlagAdapter.EPPO


class NavisionConfirmTaxIdUSInvoiceDetailsFlag(BooleanFlag):
    flag_name = 'Feature_NavisionConfirmTaxIdUSInvoiceDetails'
    adapter = FeatureFlagAdapter.EPPO


class NavisionDefaultTaxGroupFlag(BooleanFlag):
    flag_name = 'Fix_NavisionDefaultTaxGroup'
    adapter = FeatureFlagAdapter.EPPO


class NavisionFixMerchantSyncIEFlag(BooleanFlag):
    flag_name = 'Fix_NavisionFixMerchantSyncIEFlag'
    adapter = FeatureFlagAdapter.EPPO


class NavisionGetAndSyncMerchantFlag(BooleanFlag):
    flag_name = 'Feature_NavisionGetAndSyncMerchant'
    adapter = FeatureFlagAdapter.EPPO


class NavisionGRPCInvoicingExcludedFlag(BooleanFlag):
    flag_name = 'Feature_NavisionGRPCInvoicingExcludedFlag'
    adapter = FeatureFlagAdapter.EPPO


class NavisionInvoiceBBSaaSPaidAfterMigrationFlag(BooleanFlag):
    flag_name = 'Feature_NavisionInvoiceBBSaaSPaidAfterMigration'
    adapter = FeatureFlagAdapter.EPPO


class NavisionInvoiceDetailsPubSubMessageFlag(BooleanFlag):
    flag_name = 'Feature_NavisionInvoiceDetailsPubSubMessage'
    adapter = FeatureFlagAdapter.EPPO


class NavisionOptimizeBusinessesToBoostInvoiceQueryFlag(BooleanFlag):
    flag_name = 'Fix_NavisionOptimizeBusinessesToBoostInvoiceQuery'
    adapter = FeatureFlagAdapter.EPPO


class NavisionSaaSOnlineNewApproachEnabledFlag(BooleanFlag):
    flag_name = 'Feature_NavisionSaaSOnlineNewApproachEnabled'
    adapter = FeatureFlagAdapter.EPPO


class NavisionSendHOFTestInvoicesToSandbox2Flag(BooleanFlag):
    flag_name = 'Feature_NavisionSendHOFTestInvoicesToSandbox2'
    adapter = FeatureFlagAdapter.EPPO


class NavisionVatTaxIrelandFlag(BooleanFlag):
    flag_name = 'Feature_NavisionVatTaxIreland'
    adapter = FeatureFlagAdapter.EPPO


class RemoveNavisionAPIClientV1Flag(BooleanFlag):
    flag_name = 'Refactor_RemoveNavisionAPIClientV1'
    adapter = FeatureFlagAdapter.EPPO


class UseNavisionInvoicingEnabledServiceFlag(BooleanFlag):
    flag_name = 'Feature_UseNavisionInvoicingEnabledService'
    adapter = FeatureFlagAdapter.EPPO


class UseNavisionMerchantAPIClientV2Flag(BooleanFlag):
    flag_name = 'Feature_UseNavisionMerchantAPIClientV2'
    adapter = FeatureFlagAdapter.EPPO
