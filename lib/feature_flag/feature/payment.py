from lib.feature_flag.enums import FeatureFlagAdapter
from lib.feature_flag.flag_base import BooleanFlag, DictFlag, IntegerFlag


class AutoBADepositParams(DictFlag):
    flag_name = 'Feature_AutoBADepositParams'
    adapter = FeatureFlagAdapter.EPPO


class AutoFastPayoutsParams(DictFlag):
    flag_name = 'Feature_AutoFastPayoutsParams'
    adapter = FeatureFlagAdapter.EPPO


class AutomaticKYCConsentAssignmentFlag(BooleanFlag):
    flag_name = 'Feature_AutomaticKYCConsentAssignmentFlag'
    adapter = FeatureFlagAdapter.EPPO


class BasketPaymentCompletedAnalyticsFlag(BooleanFlag):
    flag_name = 'Feature_BasketPaymentCompletedAnalyticsFlag'
    adapter = FeatureFlagAdapter.EPPO


class BlikDiscountFlag(DictFlag):
    """
    Expected format:
    {} - feature disabled

    {
    "prepayment_promo_start": 1719828610,
    "prepayment_promo_end": 1720606210,
    "checkout_promo_start": 1721030430,
    "checkout_promo_end": 1721451600,
    } - sample value

    all fields represent a timestamp (in seconds) - 1719828610 == Monday, 1 July 2024 10:10:10

    all fields should be an integer, not null. If you want to disable the campaign, set the fields
    to a very low value i.e.

    "checkout_promo_start": 1,
    "checkout_promo_end": 2,

    this will effectively disable the checkout campaign (values for checkout
    and prepayment can be different)

    ALL PARAMETERS ARE EXPECTED TO BE SET (OR {} IF FEATURE DISABLED)
    """

    flag_name = 'Feature_BlikDiscountFlag'
    adapter = FeatureFlagAdapter.LD


class BooksyWalletShowNSPButtonOnlyForWebFlag(BooleanFlag):
    flag_name = 'Feature_BooksyWalletShowNSPButtonOnlyForWeb'
    adapter = FeatureFlagAdapter.EPPO


class CancellationFeeFullAmountAuthorizationFlag(BooleanFlag):
    flag_name = 'Feature_CancellationFeeFullAmountAuthorizationFlag'
    adapter = FeatureFlagAdapter.EPPO


class CheckIfBGCAlreadyAssignedToAppointment(BooleanFlag):
    flag_name = 'Feature_CheckIfBGCAlreadyAssignedToAppointment'
    adapter = FeatureFlagAdapter.EPPO


class CxPromotedPaymentMethods(DictFlag):
    flag_name = 'Feature_CxPromotedPaymentMethods'
    adapter = FeatureFlagAdapter.LD


class DisableNewAdyenProvider(BooleanFlag):
    flag_name = 'Feature_DisableNewAdyenProvider'
    adapter = FeatureFlagAdapter.EPPO


class EditTransactionWithLockFlag(BooleanFlag):
    flag_name = 'Feature_EditTransactionWithLockFlag'
    adapter = FeatureFlagAdapter.EPPO


class EffortlessKYCAfterEkranZBaba(BooleanFlag):
    flag_name = 'Feature_EffortlessKYCAfterEkranZBaba'
    adapter = FeatureFlagAdapter.EPPO


class EnablePayoutMethodNotificationAndChangelogFlag(BooleanFlag):
    flag_name = 'Feature_EnablePayoutMethodNotificationAndChangelog'
    adapter = FeatureFlagAdapter.EPPO


class EnablePayoutsPaymentsSettingBannerFlag(BooleanFlag):
    flag_name = 'Feature_EnablePayoutsPaymentsSettingBannerFlag'
    adapter = FeatureFlagAdapter.LD


class ExtendedWaitForPaymentRowTransactionFlag(IntegerFlag):
    # number of seconds after which the waiting will stop (0 == disabled)
    flag_name = 'Feature_ExtendedWaitForPaymentRowTransactionFlag'
    adapter = FeatureFlagAdapter.EPPO


class FastPayoutTimeRestrictionFlag(DictFlag):
    flag_name = 'Feature_FastPayoutTimeRestrictionFlag'
    adapter = FeatureFlagAdapter.LD


class FeatureDepositOnBusinessAppointment(BooleanFlag):
    flag_name = 'Feature_DepositOnBusinessAppointment'
    adapter = FeatureFlagAdapter.EPPO


class FixStripeAccountHandlerLatency(BooleanFlag):
    flag_name = 'Fix_FixStripeAccountHandlerLatency'
    adapter = FeatureFlagAdapter.EPPO


class HigherPrepaymentSplashFlag(DictFlag):
    flag_name = 'Feature_HigherPrepaymentSplashFlag'
    adapter = FeatureFlagAdapter.EPPO


class KeepPrepaymentSplashFlag(BooleanFlag):
    flag_name = 'Feature_KeepPrepaymentSplashFlag'
    adapter = FeatureFlagAdapter.EPPO


class KIPMOTOWebhookFlag(BooleanFlag):
    flag_name = 'Feature_KIP_MOTO_Webhook'
    adapter = FeatureFlagAdapter.EPPO


class NethoneAttemptReferenceSendingFlag(BooleanFlag):
    flag_name = 'Feature_NethoneAttemptReferenceSendingFlag'
    adapter = FeatureFlagAdapter.EPPO


class NoShowProtectionSplashFlag(BooleanFlag):
    flag_name = 'Feature_NoShowProtectionSplash'
    adapter = FeatureFlagAdapter.EPPO


class NoShowProtectionThresholdsSplashFlag(DictFlag):
    flag_name = 'Feature_NoShowProtectionThresholdsSplash'
    adapter = FeatureFlagAdapter.EPPO


class NSPFixedPrize(BooleanFlag):
    flag_name = 'Feature_NSPFixedPrize'
    adapter = FeatureFlagAdapter.EPPO


class OTPMinimumRequiredVersionFlag(DictFlag):
    """
    Expected format:
    {} - default values will be taken
    {"ios": "3.27.2", "android": "3.26.2_627"} - sample valid value
    ALL PARAMETERS ARE EXPECTED TO BE SET (OR {} IF FEATURE DISABLED)
    """

    flag_name = 'Feature_OTPMinimumRequiredVersion'
    adapter = FeatureFlagAdapter.EPPO


class OTPSeparateCeleryQueue(BooleanFlag):
    flag_name = 'otpseparateceleryqueue'
    adapter = FeatureFlagAdapter.EPPO


class PartialRefundsFlag(BooleanFlag):
    flag_name = 'Feature_PartialRefundsFlag'
    adapter = FeatureFlagAdapter.EPPO


class PaymentRowSummaryUseReadReplicaFlag(BooleanFlag):
    flag_name = 'Feature_PaymentRowSummaryUseReadReplica'
    adapter = FeatureFlagAdapter.EPPO


class PrepaymentsForBusinessAppointmentEnabled(BooleanFlag):
    flag_name = 'Feature_PrepaymentsForBusinessAppointment'
    adapter = FeatureFlagAdapter.LD


class ProfileAndSettingsTTPPromoFlag(BooleanFlag):
    flag_name = 'Feature_ProfileAndSettingsTTPPromoFlag'
    adapter = FeatureFlagAdapter.LD


class PromoteTTPAndBCRPaymentsToCustomersFlag(BooleanFlag):
    flag_name = 'Feature_PromoteTTPAndBCRPaymentsToCustomersFlag'
    adapter = FeatureFlagAdapter.EPPO


class QuickCardEntryPaymentMethodFlag(BooleanFlag):
    flag_name = 'QuickCardEntryPaymentMethodFlag'
    adapter = FeatureFlagAdapter.EPPO


class ReorganizePaymentTypeTilesEnabled(BooleanFlag):
    flag_name = 'Feature_ReorganizePaymentTypeTilesEnabled'
    adapter = FeatureFlagAdapter.LD


class SendConnectedAccountUpdatedEventFlag(BooleanFlag):
    flag_name = 'Feature_SendConnectedAccountUpdatedEvent'
    adapter = FeatureFlagAdapter.EPPO


class SendTransactionEventToBooksyWalletFlag(BooleanFlag):
    flag_name = 'Feature_SendTransactionEventToBooksyWallet'
    adapter = FeatureFlagAdapter.EPPO


class ShowBooksyWalletForTrialKYCPx(BooleanFlag):
    flag_name = 'Feature_ShowBooksyWalletForTrialKYCPx'
    adapter = FeatureFlagAdapter.EPPO


class ShowNewFinancialCenterFlag(BooleanFlag):
    flag_name = 'Feature_ShowNewFinancialCenterFlag'
    adapter = FeatureFlagAdapter.EPPO


class StripeCheckRefundOnlineBalance(BooleanFlag):
    flag_name = 'Feature_StripeCheckRefundOnlineBalance'
    adapter = FeatureFlagAdapter.LD


class StripeCurrentlyDueInKYCFlag(BooleanFlag):
    flag_name = 'Feature_StripeCurrentlyDueInKYCFlag'
    adapter = FeatureFlagAdapter.EPPO


class StripeImproveHandlingNotifications(BooleanFlag):
    flag_name = 'Feature_StripeImproveHandlingNotifications'
    adapter = FeatureFlagAdapter.LD


class StripeSendAccountCountryFlag(BooleanFlag):
    flag_name = 'Feature_StripeSendAccountCountry'
    adapter = FeatureFlagAdapter.EPPO


class StripeTurnOnDebitNegativeBalancesFlag(BooleanFlag):
    flag_name = 'Feature_StripeTurnOnDebitNegativeBalancesFlag'
    adapter = FeatureFlagAdapter.LD


class TapToPayDiscountFlag(DictFlag):
    """
    Expected format:
    {} - feature disabled
    {"max_payment_amount": 100000, "payments_count_limit": 3, max_time_to_use: 10080} - sample value
    max_payment_amount - amount in CENTS - 100000 == 1000$
    payments_count_limit - maximum amount of transactions - i.e. 3 free transactions
    max_time_to_use - maximum amount of time in MINUTES - 10080 == 7 days (7*24*60=10080)
    ALL PARAMETERS ARE EXPECTED TO BE SET (OR {} IF FEATURE DISABLED)
    """

    flag_name = 'Feature_TapToPayDiscountFlag'
    adapter = FeatureFlagAdapter.LD


class ThreeDSecureInAppointmentEditionFlag(BooleanFlag):
    flag_name = 'Feature_ThreeDSecureInAppointmentEditionFlag'
    adapter = FeatureFlagAdapter.EPPO


class UpdateBGCPaymentRowIfPBASucceededFlag(BooleanFlag):
    flag_name = 'Feature_UpdateBGCPaymentRowIfPBASucceeded'
    adapter = FeatureFlagAdapter.EPPO
