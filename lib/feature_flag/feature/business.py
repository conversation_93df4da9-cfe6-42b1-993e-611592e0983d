from lib.feature_flag.enums import FeatureFlagAdapter
from lib.feature_flag.flag_base import BooleanFlag


class AccountExistsBusinessMigrationFlag(BooleanFlag):
    flag_name = 'Feature_AccountExistsBusinessMigration'
    adapter = FeatureFlagAdapter.EPPO


class AutomateProviderRecruitmentFlag(BooleanFlag):
    flag_name = 'Feature_AutomateProviderRecruitmentFlag'
    adapter = FeatureFlagAdapter.EPPO


class AutoSwitchChurnedPxToBooksyBillingFlag(BooleanFlag):
    flag_name = 'Feature_AutoSwitchChurnedPxToBooksyBillingFlag'
    adapter = FeatureFlagAdapter.EPPO


class CalendarBannerFlag(BooleanFlag):
    flag_name = 'Feature_CalendarBanner'
    adapter = FeatureFlagAdapter.EPPO


class DisableWhitelistingIpForStafferMyBusinessesFlag(BooleanFlag):
    flag_name = 'Feature_DisableWhitelistingIpForStafferMyBusinesses'
    adapter = FeatureFlagAdapter.EPPO


class EnablePayoutsCalendarBannerFlag(BooleanFlag):
    flag_name = 'Feature_EnablePayoutsCalendarBannerFlag'
    adapter = FeatureFlagAdapter.LD


class EnableServiceImportLimitFlag(BooleanFlag):
    flag_name = 'Feature_EnableServiceImportLimit'
    adapter = FeatureFlagAdapter.EPPO


class LoginUserRetryFlag(BooleanFlag):
    flag_name = 'Feature_LoginUserRetry'
    adapter = FeatureFlagAdapter.EPPO


class MoneyInYourAccountBannerFlag(BooleanFlag):
    flag_name = 'Feature_MoneyInYourAccountBanner'
    adapter = FeatureFlagAdapter.EPPO


class MyBusinessesMigrationFlag(BooleanFlag):
    flag_name = 'Feature_MyBusinessesMigrationFlag'
    adapter = FeatureFlagAdapter.EPPO


class OptimizeProfileCompletenessUpdateFlag(BooleanFlag):
    flag_name = 'Feature_OptimizeProfileCompletenessUpdate'
    adapter = FeatureFlagAdapter.EPPO


class PreventResetCurrentStafferServicesFlag(BooleanFlag):
    flag_name = 'Bug_PreventResetCurrentStafferServices'
    adapter = FeatureFlagAdapter.EPPO


class ResourceVisibleValidationFlag(BooleanFlag):
    flag_name = 'Bug_ResourceVisibleValidation'
    adapter = FeatureFlagAdapter.LD


class SettingsBannerFlag(BooleanFlag):
    flag_name = 'Feature_SettingsBannerFlag'
    adapter = FeatureFlagAdapter.EPPO


class RemoveNoneShortStatusLabelFlag(BooleanFlag):
    flag_name = 'Bug_RemoveNoneShortStatusLabelFlag'
    adapter = FeatureFlagAdapter.EPPO


class CheckUpdateBusinessDeeplinkFlag(BooleanFlag):
    flag_name = 'Feature_CheckUpdateBusinessDeeplink'
    adapter = FeatureFlagAdapter.EPPO


class BusinessDetailsReplica(BooleanFlag):
    flag_name = 'qps_pg_get_business_details_replica_bool'
    adapter = FeatureFlagAdapter.EPPO


class LimitServicesBusinessListingWebFlag(BooleanFlag):
    flag_name = 'Feature_LimitServicesBusinessListingWeb'
    adapter = FeatureFlagAdapter.EPPO


class AccountExistsAddProfileTypeFlag(BooleanFlag):
    flag_name = 'Feature_AccountExistsAddProfileType'
    adapter = FeatureFlagAdapter.EPPO
