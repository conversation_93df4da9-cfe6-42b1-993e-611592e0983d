from lib.feature_flag.enums import FeatureFlagAdapter
from lib.feature_flag.flag_base import BooleanFlag


class HideVersumAppointmentImporter(BooleanFlag):
    flag_name = 'Feature_HideVersumAppointmentImporterField'
    adapter = FeatureFlagAdapter.EPPO


class ShowInvoiceAddressInBuyerAdminFlag(BooleanFlag):
    flag_name = 'Feature_ShowInvoiceAddressInBuyerAdmin'
    adapter = FeatureFlagAdapter.EPPO


class UseOktaInAdminPermissionsScript(BooleanFlag):
    flag_name = 'Feature_UseOktaInAdminPermissionsScript'
    adapter = FeatureFlagAdapter.EPPO


class UseSetAdminPermissionsFromWorkspace(BooleanFlag):
    flag_name = 'Feature_UseSetAdminPermissionsFromWorkspace'
    adapter = FeatureFlagAdapter.EPPO


class ShowPaidDuplicatedAccountCancellationReason(BooleanFlag):
    flag_name = 'Feature_ShowPaidDuplicatedAccountCancellationReason'
    adapter = FeatureFlagAdapter.EPPO


class ShowEnforcePasswordResetFlag(BooleanFlag):
    flag_name = 'Feature_ShowEnforcePasswordReset'
    adapter = FeatureFlagAdapter.EPPO


class UseNewAdminGroupPermissions(BooleanFlag):
    flag_name = 'Feature_UseNewAdminGroupPermissions'
    adapter = FeatureFlagAdapter.EPPO
