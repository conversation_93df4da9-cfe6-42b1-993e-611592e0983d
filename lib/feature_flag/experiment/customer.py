from lib.feature_flag.enums import FeatureFlagAdapter
from lib.feature_flag.flag_base import StringFlag


class VistedLikedSelected4UExperiment(StringFlag):
    flag_name = 'Experiment_VistedLikedSelected4You'
    adapter = FeatureFlagAdapter.EPPO


class S4UGalleriesOrderExperiment(StringFlag):
    flag_name = 'Experiment_SelectedForYouGalleriesOrder'
    adapter = FeatureFlagAdapter.EPPO


class SuggesterExperiments(StringFlag):
    flag_name = 'Experiment_SuggesterExperiments'
    adapter = FeatureFlagAdapter.EPPO


class VisitedLikedS4URandomizationExperiment(StringFlag):
    flag_name = 'Experiment_VisitedLikedS4URandomization'
    adapter = FeatureFlagAdapter.EPPO


class SelectedForYouV4RandomizedScoringExperiment(StringFlag):
    flag_name = 'Experiment_SelectedForYouV4RandomizedScoring'
    adapter = FeatureFlagAdapter.EPPO


class VisitedLikedS4UOrderExperiment(StringFlag):
    flag_name = 'Experiment_VisitedLikedS4UOrderExperiment'
    adapter = FeatureFlagAdapter.EPPO


class GalleryNameS4UExperiment(StringFlag):
    flag_name = 'Experiment_GalleryNameS4U'
    adapter = FeatureFlagAdapter.EPPO


class CategoriesOrderExperiment(StringFlag):
    flag_name = 'Experiment_CategoriesOrder'
    adapter = FeatureFlagAdapter.EPPO


class S4UAfterBookingExperiment(StringFlag):
    flag_name = 'Experiment_S4UAfterBooking'
    adapter = FeatureFlagAdapter.EPPO
