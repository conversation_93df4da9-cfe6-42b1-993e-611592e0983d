from lib.feature_flag.enums import FeatureFlagAdapter
from lib.feature_flag.flag_base import BooleanFlag


class AlloyDBReportsReplicaCeleryTaskFlag(BooleanFlag):
    flag_name = 'KillSwitch_AlloyDBReportsReplicaCeleryTaskFlag'
    adapter = FeatureFlagAdapter.EPPO


class DisableBListingsFlag(BooleanFlag):
    flag_name = 'KillSwitch_DisableBListings'
    adapter = FeatureFlagAdapter.EPPO


class DisableCalendarChangeResponseFlag(BooleanFlag):
    flag_name = 'KillSwitch_DisableCalendarChangeResponse'
    adapter = FeatureFlagAdapter.LD


class DisableForceRefreshInCustomerInfoFlag(BooleanFlag):
    flag_name = 'KillSwitch_DisableForceRefreshInCustomerInfoFlag'
    adapter = FeatureFlagAdapter.EPPO


class DisableForceRefreshInToolsFlag(BooleanFlag):
    flag_name = 'KillSwitch_DisableForceRefreshInToolsFlag'
    adapter = FeatureFlagAdapter.EPPO


class DisablePopulatingAppointmentIndexFlag(BooleanFlag):
    flag_name = 'KillSwitch_DisablePopulatingAppointmentIndex'
    adapter = FeatureFlagAdapter.EPPO


class DisableZowieTokenGeneration(BooleanFlag):
    flag_name = 'KillSwitch_DisableZowieTokenGeneration'
    adapter = FeatureFlagAdapter.EPPO


class HintsAndWalkThroughEventsPublishingFlag(BooleanFlag):
    flag_name = 'KillSwitch_HintsAndWalkthrough_Events_Publishing'
    adapter = FeatureFlagAdapter.EPPO


class ReadForceUpdateOptionsFromFeatureFlagFlag(BooleanFlag):
    flag_name = 'KillSwitch_ReadForceUpdateOptionsFromFeatureFlag'
    adapter = FeatureFlagAdapter.LD


class ReadOnlyDBBusinessCustomerInfoFlag(BooleanFlag):
    flag_name = 'KillSwitch_ReadOnlyDBBusinessCustomerInfoFlag'
    adapter = FeatureFlagAdapter.EPPO


class ReadOnlyDBLoginFlag(BooleanFlag):
    flag_name = 'KillSwitch_ReadOnlyDBLoginFlag'
    adapter = FeatureFlagAdapter.EPPO


class UseCeleryInReindexImagesWithBusinessInAdminFlag(BooleanFlag):
    flag_name = 'KillSwitch_UseCeleryInReindexImagesWithBusinessInAdmin'
    adapter = FeatureFlagAdapter.LD


class UseOAuth2ZoomClient(BooleanFlag):
    flag_name = 'KillSwitch_UseOauth2ForZoomMeetings'
    adapter = FeatureFlagAdapter.LD


class UserDetailsPublishingFlag(BooleanFlag):
    flag_name = 'KillSwitch_UserDetails_Events_Publishing'
    adapter = FeatureFlagAdapter.EPPO
