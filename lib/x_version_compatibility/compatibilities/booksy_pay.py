import operator as op

from packaging.version import InvalidVersion, Version
from packaging.version import parse as parse_version

from lib.feature_flag.feature.booksy_pay import (
    BusinessBooksyPayOnboardingXVersionCompatibilityFlag,
)
from lib.tools import sget_v2
from lib.x_version_compatibility.compatibilities.base import BaseXVersionCompatibility
from lib.x_version_compatibility.dataclasses import CompatibilityRule
from lib.x_version_compatibility.typing import AppType, RequestType, SourceName
from lib.x_version_compatibility.utils import parse_x_version
from webapps.booking.models import BookingSources
from webapps.consts import (
    ANDROID,
    ANDROID_SOLO,
    IPHONE,
    IPHONE_SOLO,
    FRONTDESK,
    FRONTDESK_ANDROID,
    FRONTDESK_IOS,
)


class BooksyPayCompatibility(BaseXVersionCompatibility):
    DEFAULT_VALUE: bool = True
    RULES: dict[AppType, dict[SourceName, CompatibilityRule]] = {
        BookingSources.CUSTOMER_APP: {
            ANDROID: CompatibilityRule(
                client_version=parse_x_version('2.23.1_409'),
                operator=op.ge,
            ),
            IPHONE: CompatibilityRule(
                client_version=parse_x_version('2.26.3'),
                operator=op.ge,
            ),
        },
    }


class BusinessRulesHelper:

    def __init__(self, app_versions: dict[SourceName, str]):
        self.android_version: str | None = app_versions.get(ANDROID)
        self.iphone_version: str | None = app_versions.get(IPHONE)
        self.frontdesk_version: str | None = app_versions.get(FRONTDESK)

    def _get_android_rules(self) -> dict[SourceName, CompatibilityRule]:
        if self.android_version is None:
            return {}

        return {
            ANDROID: CompatibilityRule(
                client_version=parse_x_version(self.android_version),
                operator=op.ge,
            ),
            ANDROID_SOLO: CompatibilityRule(
                client_version=parse_x_version(self.android_version),
                operator=op.ge,
            ),
        }

    def _get_iphone_rules(self) -> dict[SourceName, CompatibilityRule]:
        if self.iphone_version is None:
            return {}

        return {
            IPHONE: CompatibilityRule(
                client_version=parse_x_version(self.iphone_version),
                operator=op.ge,
            ),
            IPHONE_SOLO: CompatibilityRule(
                client_version=parse_x_version(self.iphone_version),
                operator=op.ge,
            ),
        }

    def _get_frontdesk_rules(self) -> dict[SourceName, CompatibilityRule]:
        """
        Note: Frontdesk does not support the `X-Version` header, hence `packaging.version.parse`
        is used here to parse its `X-App-Version` header.
        """
        if self.frontdesk_version is None:
            return {}

        return {
            FRONTDESK: CompatibilityRule(
                client_version=parse_version(self.frontdesk_version),
                operator=op.ge,
            ),
            FRONTDESK_ANDROID: CompatibilityRule(
                client_version=parse_version(self.frontdesk_version),
                operator=op.ge,
            ),
            FRONTDESK_IOS: CompatibilityRule(
                client_version=parse_version(self.frontdesk_version),
                operator=op.ge,
            ),
        }

    def get_rules(self) -> dict[SourceName, CompatibilityRule]:
        return {
            **self._get_android_rules(),
            **self._get_iphone_rules(),
            **self._get_frontdesk_rules(),
        }


class BusinessBooksyPayOnboardingCompatibility(BaseXVersionCompatibility):
    DEFAULT_VALUE: bool = False

    @staticmethod
    def _get_business_rules(
        flag: dict[AppType, dict[SourceName, str]],
    ) -> dict[SourceName, CompatibilityRule]:
        app_versions = flag.get(BookingSources.BUSINESS_APP) or {}
        rules_helper = BusinessRulesHelper(app_versions)

        return rules_helper.get_rules()

    @classmethod
    def get_client_version(cls, request: RequestType | None = None) -> Version | None:
        """
        Unfortunately, Frontdesk apps do not support X-Version header and provide `X-App-Version`
        header instead, which is hardcoded to `3.0`. Therefore, in order to be able to utilize
        this feature (to avoid adding a "Frontdesk snowflake" in the code), the logic for getting
        a client version provided on the request needs to be adjusted for the Frontdesk case.
        """
        booking_source = sget_v2(request, ['booking_source'])

        if booking_source and booking_source.is_salon_app():
            x_app_version = request.headers.get('X-App-Version') or ''

            try:
                return parse_version(x_app_version)
            except InvalidVersion:
                return None

        return super().get_client_version(request)

    @classmethod
    def get_rules(
        cls, request: RequestType | None = None
    ) -> dict[AppType, dict[SourceName, CompatibilityRule]]:  # pylint: disable=unused-argument
        """
        Example value of the BusinessBooksyPayOnboardingXVersionCompatibilityFlag:
        ```
        {
            "B": {
                "Android": "3.28.1_634",
                "iPhone": "3.28.3"
            }
        }
        ```
        IMPORTANT: it's case-sensitive.
        """
        flag = BusinessBooksyPayOnboardingXVersionCompatibilityFlag() or {}

        return {
            BookingSources.BUSINESS_APP: cls._get_business_rules(flag),
            BookingSources.CUSTOMER_APP: {},
        }
