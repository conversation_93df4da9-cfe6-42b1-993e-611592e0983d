#!/usr/bin/env python
import datetime
import os
import gc
import signal
import logging
import time
from typing import (
    Dict,
    List,
    Optional,
    Tuple,
)
from functools import (
    partial,
    wraps,
)

import redis
from celery import Celery
from celery.exceptions import ImproperlyConfigured
from celery.signals import (
    beat_init,
    celeryd_after_setup,
    worker_before_create_process,
    heartbeat_sent,
)
from celery.utils.log import get_logger
from celery.worker.consumer import Consumer

from django.db import transaction, OperationalError
from django.db.transaction import get_connection

from lib.db import (
    database_cleanup,
    is_replica_synced,
    PRIMARY_DB,
    READ_ONLY_DB,
    REPLICA_READS_DELAY,
    using_db_for_reads,
)

from lib.locks import DjangoCacheLock
from lib.sensi.sensidb import execute_on_db
from lib.utils import stop_freeze_time
from lib.tasks.consts import (
    ETA_CELERY_BODY_BY_ID,
    ETA_CELERY_QUEUE_BY_ID,
    ETA_CELERY_ID_BY_TIME_KEY,
)
from lib.tools import tznow
from django.conf import settings


class PostTransactionTaskRetryException(Exception):
    pass


def get_task_modules_to_discover():
    return [task.rsplit('.', 1)[0] for task in settings.CELERY_ROUTES]


def make_celery(app_name, config_path):
    app = Celery(app_name)
    app.config_from_object(config_path)
    app.autodiscover_tasks(packages=get_task_modules_to_discover(), related_name=None)
    return app


app = make_celery('celery_app', 'settings.celery.BooksyCeleryConfig')
BaseCeleryTask = app.Task
base_celery_task = app.task


def is_task_in_configured(task, configured_tasks):
    """Is task on list.

    :param task: task name
    :param configured_tasks: configured tasks list or dict

    :returns: True if task is in the list
    :raises: ImproperlyConfigured if task is not on a configured list

    """
    if task in configured_tasks:
        return True

    msg = (
        'Task "%s" is not assigned to any queue. ' 'Please update CELERY_ROUTES in settings.'
    ) % (task,)

    raise ImproperlyConfigured(msg)


def is_queue_in_configured(queue: str, queues_configured: list):
    """Is queue registered.

    :param queue: queue name
    :param configured_queues: configured queues list

    :returns: True if queue is in the set
    :raises: ImproperlyConfigured if queue is not on a configured list

    """
    if queue in queues_configured:
        return True

    msg = (
        'Queue "%s" is not registered in Celery Queues. ' 'Please update CELERY_QUEUES in settings.'
    ) % (queue,)

    raise ImproperlyConfigured(msg)


def get_unregistered_task_errors(tasks_configured, tasks_registered):
    errors = []
    for task in tasks_registered:
        # not messing with Celery internal tasks
        if task.startswith('celery.'):
            continue
        try:
            is_task_in_configured(task, tasks_configured)
        except ImproperlyConfigured as err:
            errors.append(err)
    return errors


def get_unregistered_queue_errors(tasks_configured: dict, queues_configured: list):
    # check queue registered in Celery
    route_queues = {tasks_configured[task]['queue'] for task in tasks_configured}
    errors = []
    queues = [queue.name for queue in queues_configured]
    for queue in route_queues:
        try:
            is_queue_in_configured(queue, queues)
        except ImproperlyConfigured as err:
            errors.append(err)
    return errors


@celeryd_after_setup.connect
def check_tasks_configuration(sender, instance, conf, **kwargs):
    """Tasks queue configuration validation.

    If ImproperlyConfigured exception is caught, the SIGINT is sent to worker.
    Worker internally handles the SIGINT.
    """
    logger = get_logger(__name__)
    tasks_configured = conf.get('CELERY_ROUTES', {})
    tasks_registered = instance.app.tasks.keys()
    errors = get_unregistered_task_errors(tasks_configured, tasks_registered)

    if errors:
        logger.error('\n'.join(str(err) for err in errors))
        os.kill(os.getpid(), signal.SIGINT)


class BooksyCeleryTask(BaseCeleryTask):
    """Base class for all celery tasks in Booksy.

    ACHTUNG: advanced stuff - it delays any task that makes reads from replica db.
    See @using_db_for_reads for details on how it works.
    This is done to prevent sync issues due to network latency between
    primary and replica databases.
    If task can wait for a few seconds, then it can read data from replica db.
    If it requires immediate execution or is very important, then
    it shouldn't use replica db at all.

    """

    abstract = True

    if settings.PYTEST:
        # add some call debug when in tests
        def __call__(self, *args, **kwargs):
            call_signature = ', '.join(
                [repr(arg) for arg in list(args)]
                + ['{}={!r}'.format(key, value) for key, value in list(kwargs.items())]
            )
            print(
                'celery task: {}.{}({})'.format(
                    self.__module__,
                    self.__name__,
                    call_signature,
                )
            )
            ret = super(BooksyCeleryTask, self).__call__(*args, **kwargs)
            print('celery task returned: {!r}'.format(ret))
            return ret

    def apply_async(
        self,
        args=None,
        kwargs=None,
        task_id=None,
        producer=None,
        link=None,
        link_error=None,
        **options,
    ):
        """Check if run method is wrapped in @using_db_for_reads and
        add countdown specified in REPLICA_READS_DELAY."""
        using_db_for_reads = getattr(self.run, '_using_db_for_reads', lambda: False)()

        if (
            using_db_for_reads
            and using_db_for_reads in settings.DATABASES
            and using_db_for_reads != PRIMARY_DB
            and not settings.CELERY_ALWAYS_EAGER
        ):
            if 'eta' in options and options['eta'] is not None:
                with stop_freeze_time():
                    options['eta'] = max(
                        options['eta'],
                        tznow() + datetime.timedelta(seconds=REPLICA_READS_DELAY),
                    )
            else:
                options['countdown'] = max(
                    options.get('countdown') or 0,
                    REPLICA_READS_DELAY,
                )
        with stop_freeze_time():
            return super(BooksyCeleryTask, self).apply_async(
                args=args,
                kwargs=kwargs,
                task_id=task_id,
                producer=producer,
                link=link,
                link_error=link_error,
                **options,
            )

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        ret = super().on_failure(exc, task_id, args, kwargs, einfo)
        database_cleanup()
        return ret

    def on_success(self, retval, task_id, args, kwargs):
        ret = super().on_success(retval, task_id, args, kwargs)
        database_cleanup()
        return ret


class PostTransactionTask(BooksyCeleryTask):
    abstract = True

    def original_apply_async(
        self,
        args=None,
        kwargs=None,
        task_id=None,
        producer=None,
        link=None,
        link_error=None,
        **options,
    ):
        """Shortcut method to reach real implementation
        of celery.Task.apply_sync
        """
        return super(PostTransactionTask, self).apply_async(
            args=args,
            kwargs=kwargs,
            task_id=task_id,
            producer=producer,
            link=link,
            link_error=link_error,
            **options,
        )

    def apply_async(
        self,
        args=None,
        kwargs=None,
        task_id=None,
        producer=None,
        link=None,
        link_error=None,
        **options,
    ):
        connection = get_connection()
        if connection.in_atomic_block and not settings.CELERY_ALWAYS_EAGER:
            transaction.on_commit(
                lambda: self.original_apply_async(
                    args=args,
                    kwargs=kwargs,
                    task_id=task_id,
                    producer=producer,
                    link=link,
                    link_error=link_error,
                    **options,
                )
            )
        else:
            return self.original_apply_async(
                args=args,
                kwargs=kwargs,
                task_id=task_id,
                producer=producer,
                link=link,
                link_error=link_error,
                **options,
            )


class RetryPostTransactionTask(PostTransactionTask):
    abstract = True

    def apply_async(
        self,
        args=None,
        kwargs=None,
        task_id=None,
        producer=None,
        link=None,
        link_error=None,
        **options,
    ):
        kwargs['spawn_time'] = self._spawn_time_or_now(kwargs.get('spawn_time'))
        return super().apply_async(
            args=args,
            kwargs=kwargs,
            task_id=task_id,
            producer=producer,
            link=link,
            link_error=link_error,
            **options,
        )

    @staticmethod
    def _spawn_time_or_now(spawn_time=None):
        if not spawn_time:
            with stop_freeze_time():
                spawn_time = tznow().isoformat()
        return spawn_time


class AnalyticsRetryPostTransactionTask(RetryPostTransactionTask):
    abstract = True

    def apply_async(
        self,
        args=None,
        kwargs=None,
        task_id=None,
        producer=None,
        link=None,
        link_error=None,
        **options,
    ):
        from webapps.kill_switch.models import KillSwitch
        from webapps.segment.utils import is_analytics_func_killed

        if KillSwitch.killed(KillSwitch.MarTech.MARTECH_ANALYTICS) or (
            is_analytics_func_killed(self.run)
        ):
            return

        return super().apply_async(
            args=args,
            kwargs=kwargs,
            task_id=task_id,
            producer=producer,
            link=link,
            link_error=link_error,
            **options,
        )


def single_instance_task(timeout):
    """Decorator ensuring only one instance of given celery task can be run at a time.

    Reference: https://stackoverflow.com/questions/4095940/running-unique-tasks-with-celery

    :param timeout: amount of time given task can be executed as single instance
    :return: decorator
    """

    def task_exc(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            lock_id = "celery-single-instance-" + func.__name__

            if DjangoCacheLock.basic_lock(lock_id, "true", timeout):
                try:
                    func(*args, **kwargs)
                finally:
                    DjangoCacheLock.basic_unlock(lock_id)

        return wrapper

    return task_exc


def segment_analytics_task(func, **kwargs):
    """
    Decorator for standardizing celery tasks that are meant just for gathering
    and sending analytics data to Segment.

    SegmentAnalyticsWrapper instance will be passed as the first arg.
    """
    from functools import update_wrapper

    @using_db_for_reads(READ_ONLY_DB)
    def _analytics_task(*args, **kwargs):
        from webapps.kill_switch.models import KillSwitch
        from lib.segment_analytics.api import SegmentAnalyticsWrapper
        from lib.segment_analytics.context import AnalyticsContext
        from webapps.segment.utils import is_analytics_func_killed

        if KillSwitch.killed(KillSwitch.MarTech.MARTECH_ANALYTICS) or (
            is_analytics_func_killed(func)
        ):
            return
        context = kwargs.pop('context', None) or {}
        context = AnalyticsContext.deserialize(context)
        event_name = func.__name__
        context.event_name = event_name

        analytics = SegmentAnalyticsWrapper(context)

        spawn_time = kwargs.pop('spawn_time', None)
        if spawn_time and not is_replica_synced(
            timestamp=datetime.datetime.fromisoformat(spawn_time),
            using=READ_ONLY_DB,
        ):
            raise PostTransactionTaskRetryException(
                f'Retry event_name={event_name}',
            )

        try:
            func(analytics=analytics, *args, **kwargs)
        finally:
            analytics.flush()

    update_wrapper(_analytics_task, func)

    task = post_transaction_task(**kwargs)(_analytics_task)
    task.run = _analytics_task

    return task


def branchio_analytics_task(func, **kwargs):
    from functools import update_wrapper

    @using_db_for_reads(READ_ONLY_DB)
    def _analytics_task(*args, **kwargs):
        from webapps.kill_switch.models import KillSwitch
        from lib.segment_analytics.api import BranchIOAnalyticsWrapper
        from lib.segment_analytics.context import AnalyticsContext
        from webapps.segment.utils import is_analytics_func_killed

        if KillSwitch.killed(KillSwitch.MarTech.MARTECH_ANALYTICS) or (
            is_analytics_func_killed(func)
        ):
            return
        context = kwargs.pop('context', None) or {}
        context = AnalyticsContext.deserialize(context)
        event_name = func.__name__
        context.event_name = event_name

        analytics = BranchIOAnalyticsWrapper(context)

        spawn_time = kwargs.pop('spawn_time', None)
        if spawn_time and not is_replica_synced(
            timestamp=datetime.datetime.fromisoformat(spawn_time),
            using=READ_ONLY_DB,
        ):
            raise PostTransactionTaskRetryException(
                f'Retry event_name={event_name}',
            )

        func(analytics=analytics, *args, **kwargs)

    update_wrapper(_analytics_task, func)

    task = post_transaction_task(**kwargs)(_analytics_task)
    task.run = _analytics_task

    return task


def gtm_analytics_task(func, **kwargs):
    """
    Decorator for standardizing celery tasks that are meant just for gathering
    and sending analytics data to GTM.

    GTMAnalyticsWrapper instance will be passed as the first arg.
    """
    from functools import update_wrapper

    @using_db_for_reads(READ_ONLY_DB)
    def _analytics_task(*args, **kwargs):
        from lib.segment_analytics.context import AnalyticsContext
        from lib.segment_analytics.api import GTMAnalyticsWrapper
        from webapps.kill_switch.models import KillSwitch
        from webapps.segment.utils import is_analytics_func_killed, build_firebase_auth_data

        if KillSwitch.killed(KillSwitch.MarTech.MARTECH_ANALYTICS) or (
            is_analytics_func_killed(func)
        ):
            return

        context = kwargs.pop('context', None) or {}
        auth_data = kwargs.pop('auth_data', None) or {}

        if auth_data:
            if 'booking_source_id' in auth_data:
                context['source_id'] = auth_data.get('booking_source_id')
            context = AnalyticsContext.deserialize(context)
        else:
            context = AnalyticsContext.deserialize(context)
            auth_data = build_firebase_auth_data(context)

        event_name = func.__name__
        context.event_name = event_name
        analytics = GTMAnalyticsWrapper(auth_data, context=context)
        if not is_replica_synced(
            timestamp=datetime.datetime.fromisoformat(kwargs['spawn_time']),
            using=READ_ONLY_DB,
        ):
            raise PostTransactionTaskRetryException(
                f'Retry event_name={event_name}',
            )

        kwargs.pop('spawn_time', None)

        func(analytics=analytics, *args, **kwargs)

    update_wrapper(_analytics_task, func)

    task = post_transaction_task(**kwargs)(_analytics_task)
    task.run = _analytics_task

    return task


def retry_task(func, **kwargs):
    """
    Decorator for standardizing celery tasks that need to be reliable
    and database resistant.
    """
    from functools import update_wrapper

    def _retry_task(*args, **kwargs):
        if not is_replica_synced(
            timestamp=datetime.datetime.fromisoformat(kwargs['spawn_time']),
            using=READ_ONLY_DB,
        ):
            raise PostTransactionTaskRetryException(
                f'Retry event_name={func.__name__}',
            )

        kwargs.pop('spawn_time', None)

        try:
            func(*args, **kwargs)
        finally:
            pass

    update_wrapper(_retry_task, func)

    task = post_transaction_task(**kwargs)(_retry_task)
    task.run = _retry_task

    return task


# Replacement decorators.
celery_task = partial(base_celery_task, base=BooksyCeleryTask)
post_transaction_task = partial(base_celery_task, base=PostTransactionTask)
retry_post_transaction_task = partial(
    retry_task,
    base=RetryPostTransactionTask,
    default_retry_delay=20,
    autoretry_for=(PostTransactionTaskRetryException, OperationalError),
    retry_kwargs={'max_retries': 3},
)
segment_analytics_retry_post_transaction_task = partial(
    segment_analytics_task,
    base=AnalyticsRetryPostTransactionTask,
    default_retry_delay=20,
    autoretry_for=(PostTransactionTaskRetryException, OperationalError),
    retry_kwargs={'max_retries': 3},
)

gtm_analytics_retry_post_transaction_task = partial(
    gtm_analytics_task,
    base=AnalyticsRetryPostTransactionTask,
    default_retry_delay=20,
    autoretry_for=(PostTransactionTaskRetryException, OperationalError),
    retry_kwargs={'max_retries': 3},
)
branchio_analytics_retry_post_transaction_task = partial(
    branchio_analytics_task,
    base=AnalyticsRetryPostTransactionTask,
    default_retry_delay=20,
    autoretry_for=(PostTransactionTaskRetryException, OperationalError),
    retry_kwargs={'max_retries': 3},
)


def get_broker():
    """Get a connection to default redis used as celery broker."""
    import redis

    host_port, db = settings.BROKER_URL.split('/')[2:]
    host, port = host_port.split(':')
    return redis.StrictRedis(host=host, port=port, db=db)


def check_queues(
    queues: Optional[List[str]] = None,
) -> Tuple[int, Dict[str, int], Dict[str, int], Dict[str, int]]:
    if queues is None:
        queues = settings.CELERY_QUEUES
    r = get_broker()
    ret = {}
    for queue in queues:
        try:
            queue_length = r.llen(queue)
        except redis.ResponseError:
            pass
        else:
            ret[queue] = queue_length

    by_priority = {
        priority: sum(count for queue, count in ret.items() if queue in queues)
        for priority, queues in settings.CELERY_QUEUES_BY_PRIORITY.items()
    }
    by_app = {
        app_name: sum(count for queue, count in ret.items() if queue in queues)
        for app_name, queues in settings.CELERY_QUEUES_BY_APP.items()
    }
    pipeline = r.pipeline()
    pipeline.hlen(ETA_CELERY_BODY_BY_ID)
    pipeline.hlen(ETA_CELERY_QUEUE_BY_ID)
    pipeline.zcard(ETA_CELERY_ID_BY_TIME_KEY)
    eta_task_structures_len = pipeline.execute()
    return sum(ret.values()), ret, by_priority, by_app, eta_task_structures_len


def get_queues_lengh_by_priority(priority: str):
    queues = settings.CELERY_QUEUES_BY_PRIORITY.get(priority)
    if queues:
        redis_client = get_broker()
        counter = 0
        for queue in queues:
            try:
                counter += redis_client.llen(queue)
            except redis.ResponseError:
                pass
        redis_client.close()
        return counter


def get_unacked_tasks():
    with get_broker() as r:
        return r.hlen('unacked')


gc_freezed_at_start = False


@worker_before_create_process.connect
def prefork_celery_listener(**kwargs: object) -> None:
    from env_secrets.v1.secrets_manager import ProductionSecretsProvider
    from env_secrets.v2.secrets_providers import GCPClientRegistry

    global gc_freezed_at_start
    if not gc_freezed_at_start:
        gc_freezed_at_start = True
        gc.freeze()

    ProductionSecretsProvider.close_client()
    GCPClientRegistry.close_all()


# without shelve it is not needed
# @beat_init.connect
def beat_init_listener(**kwargs):
    kwargs['sender'].scheduler._store.dict.reorganize()


@beat_init.connect
def create_redis_queue_check(**kwargs):
    from monitoring.emit_redis_metrics import INSTRUCTION_PATH
    from pathlib import Path

    Path(INSTRUCTION_PATH).parent.mkdir(parents=True, exist_ok=True)
    if Path(INSTRUCTION_PATH).exists():
        return
    from lib.email.backend import BooksyQueueEmailBackendRedisFIFO
    from lib.rivers import River
    import json

    rivers = River.values()
    rivers.remove('availability_indexed')
    check_instruction = {
        'celery': {
            'api_country': settings.API_COUNTRY,
            'server': settings.BROKER_URL,
            'queues': settings.CELERY_QUEUES,
            'app_mapping': settings.CELERY_QUEUES_BY_APP,
            'priority_mapping': settings.CELERY_QUEUES_BY_PRIORITY,
            'eta': {
                'id_by_time': ETA_CELERY_ID_BY_TIME_KEY,
                'id_queue': ETA_CELERY_QUEUE_BY_ID,
                'id_body': ETA_CELERY_BODY_BY_ID,
            },
        },
        'river': {'api_country': settings.API_COUNTRY, 'server': settings.REDIS_RIVER, 'queues': rivers},
        'email': {
            'api_country': settings.API_COUNTRY,
            'server': BooksyQueueEmailBackendRedisFIFO.CACHE._server,
            'queue': BooksyQueueEmailBackendRedisFIFO.email_queue_name,
        },
    }
    with open(INSTRUCTION_PATH, 'w') as file:
        json.dump(check_instruction, file)


class RepeatableUnregisteredCeleryConsumer(Consumer):
    def on_unknown_task(self, body, message, exc):
        from celery.worker.consumer.consumer import UNKNOWN_TASK_ERROR, dump_body, error, logger

        requeue = False
        if (not_registered_retry := message.headers.get('not_registered_retry', 0)) < 6:
            new_eta = datetime.datetime.fromisoformat(
                message.headers.get('eta') or datetime.datetime.now().isoformat()
            ) + datetime.timedelta(seconds=300)
            message.headers['eta'] = new_eta.isoformat()
            message.headers['not_registered_retry'] = not_registered_retry + 1
            requeue = True
        UNKNOWN_TASK_ERROR = UNKNOWN_TASK_ERROR.replace(
            'The message has been ignored and discarded.',
            f'Will be retried? {requeue}',
        )
        error(
            UNKNOWN_TASK_ERROR,
            exc,
            dump_body(message, body),
            message.headers,
            message.delivery_info,
            exc_info=True,
        )
        message.reject_log_error(logger, self.connection_errors)
        if requeue:
            super(message.channel.__class__, message.channel)._restore(message)


def db_highly_utilized(busy_threshold=50, busy_ratio_threshold=0.30) -> bool:
    query = f"""
        SELECT
            (SELECT COUNT(*) FROM pg_stat_activity WHERE state = 'active') AS active_connections,
            (SELECT COUNT(*) FROM pg_stat_activity WHERE state = 'idle in transaction') AS idle_in_transaction_connections, 
            (SELECT COUNT(*) FROM pg_stat_activity) AS total_connections;
    """
    [(active_connections, idle_in_transaction_connections, total_connections)] = execute_on_db(
        query,
        returning=True,
    )
    connection_ratio = (active_connections + idle_in_transaction_connections) / total_connections
    busy_connections = active_connections + idle_in_transaction_connections

    if busy_connections > busy_threshold and connection_ratio > busy_ratio_threshold:
        logging.getLogger('booksy.db').error(
            f'booksy-{settings.API_COUNTRY} db was highly utilized with connection_ratio {connection_ratio}'
        )
        return True
    return False


last_db_busy_check = time.monotonic()
TIME_BETWEEN_CHECKS = 10


@heartbeat_sent.connect
def shutdown_on_busy_db(**_):
    global last_db_busy_check
    if time.monotonic() - last_db_busy_check > TIME_BETWEEN_CHECKS:
        last_db_busy_check = time.monotonic()
        if db_highly_utilized():
            from celery.worker import state

            state.should_stop = True


@celeryd_after_setup.connect
def celery_startup_delay_on_stressed_db(sender, instance, conf, **kwargs):
    from lib.probes.celery_probes import HEARTBEAT_FILE

    while db_highly_utilized():
        time.sleep(TIME_BETWEEN_CHECKS)
        HEARTBEAT_FILE.touch()  # liveness probe should pass to omit many pod restarts
        logging.getLogger('booksy.db').error('Celery startup time delayed due to stressed db')
