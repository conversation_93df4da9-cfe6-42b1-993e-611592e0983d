import responses
from rest_framework import status

from lib.search_service.client import (
    Endpoint,
    get_url,
    make_request,
)

_SUGGESTIONS_URL = 'http://search_service:8080/v1/suggestions'


def test_get_url():
    assert get_url(endpoint=Endpoint.SUGGESTIONS) == _SUGGESTIONS_URL


@responses.activate
def test_make_request():
    responses.add(responses.GET, _SUGGESTIONS_URL, json=dict())
    response = make_request(endpoint=Endpoint.SUGGESTIONS)
    assert response.status_code == status.HTTP_200_OK
