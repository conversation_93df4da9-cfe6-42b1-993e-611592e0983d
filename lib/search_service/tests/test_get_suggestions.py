import pytest
import responses

from lib.elasticsearch.consts import ESIndex
from lib.search_service.client import (
    SuggestionObjectType,
    get_suggestions,
)
from webapps.business.elasticsearch import BusinessDocument

_SUGGESTIONS_URL = 'http://search_service:8080/v1/suggestions'

BUSINESS_DATA = [
    {
        'id': 98,
        'name': '<PERSON>',
        'slug': 'barber',
        'sitemap_url': '98_barber',
        'thumbnail_photo': 'barber.jpeg',
        'address': 'ulica Ostrobramska 132, 123, 04-026, Warszawa, Praga-Południe',
        'is_b_listing': True,
    },
    {
        'id': 99,
        'name': 'Barber 2',
        'slug': 'barber-2',
        'sitemap_url': '99_barber',
        'thumbnail_photo': None,
        'address': 'Lecha <PERSON> 22, 80-365, Gdańsk',
        'is_b_listing': True,
    },
]


@pytest.fixture(scope='function', autouse=False)
def create_businesses(clean_index_module_fixture):
    index = clean_index_module_fixture(ESIndex.BUSINESS)
    for data in BUSINESS_DATA:
        data = data.copy()

        address = data.pop('address')
        BusinessDocument(
            _id=f"business:{data['id']}",
            active=True,
            visible=True,
            business_location={'address': address},
            join='business',
            **data,
        ).save()

    index.refresh()


@responses.activate
@pytest.mark.django_db
def test_skip_unknown_object_type():
    """
    The main goal is to skip category and b-listing, because the logic for them is not implemented.
    """
    responses.add(
        responses.GET,
        _SUGGESTIONS_URL,
        json={
            'suggestions': [
                {
                    'object_type': 'category',
                    'label': 'label',
                    'query_parameters': {
                        'id': 1,
                        'name': 'name',
                        'slug': 'slug',
                        'type': 'C',
                    },
                }
            ]
        },
    )
    response = get_suggestions(query='foo')

    assert response == {
        'suggestions': [],
        'number_category_suggestions': 0,
    }


@responses.activate
@pytest.mark.django_db
def test_process_treatments():
    input_treatment_data = {
        'object_type': SuggestionObjectType.TREATMENT,
        'label': 'label',
        'query_parameters': {
            'id': 1,
            'name': 'name',
            'slug': 'slug',
            'type': 'T',
        },
    }

    responses.add(
        responses.GET,
        _SUGGESTIONS_URL,
        json={'suggestions': [input_treatment_data]},
    )
    response = get_suggestions(query='foo')

    expected_output_treatment_data = input_treatment_data.copy()
    expected_output_treatment_data['query_parameters']['icon_v2'] = ''
    assert response == {
        'suggestions': [expected_output_treatment_data],
        'number_category_suggestions': 1,
    }


@responses.activate
@pytest.mark.django_db
@pytest.mark.usefixtures('create_businesses')
def test_process_businesses():
    responses.add(
        responses.GET,
        _SUGGESTIONS_URL,
        json={
            'suggestions': [
                {
                    'object_type': SuggestionObjectType.BUSINESS,
                    'label': 'label',
                    'query_parameters': {
                        'id': id_,
                        'type': 'B',
                    },
                }
                for id_ in [99, 98]
            ]
        },
    )
    response = get_suggestions(query='foo')

    expected_response = {
        'suggestions': [
            {
                'object_type': SuggestionObjectType.BUSINESS,
                'label': 'label',
                'query_parameters': {
                    'id': 99,
                    'name': 'Barber 2',
                    'slug': 'barber-2',
                    'url': '99_barber',
                    'thumbnail_photo': None,
                    'address': 'Lecha Kaczyńskiego 22, 80-365, Gdańsk',
                    'is_b_listing': True,
                },
            },
            {
                'object_type': SuggestionObjectType.BUSINESS,
                'label': 'label',
                'query_parameters': {
                    'id': 98,
                    'name': 'Barber',
                    'slug': 'barber',
                    'url': '98_barber',
                    'thumbnail_photo': 'http://localhost:8888/media/barber.jpeg?size=100x100',
                    'address': 'ulica Ostrobramska 132, 123, 04-026, Warszawa, Praga-Południe',
                    'is_b_listing': True,
                },
            },
        ],
        'number_category_suggestions': 0,
    }
    assert response == expected_response
