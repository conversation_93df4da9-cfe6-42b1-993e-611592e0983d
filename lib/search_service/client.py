import logging
import typing as t
import urllib

import requests
from rest_framework import serializers

import settings
from lib.enums import StrEnum
from service.search.serializers import BusinessQueryHintSerializer
from webapps.business.business_categories.cache import CategoryCache
from webapps.business.elasticsearch import BusinessDocument
from webapps.business.models.category import get_business_category_image_url
from webapps.images.enums import BusinessCategoryPlaceholderType

logger = logging.getLogger('booksy.search_service')

_TIMEOUT = 1
_PROTOCOL = 'http://'


class Version(StrEnum):
    V1 = 'v1'


class Endpoint(StrEnum):
    SUGGESTIONS = 'suggestions'


class SuggestionObjectType(StrEnum):
    TREATMENT = 'treatment'
    BUSINESS = 'business'


class LocationGeoSerializer(serializers.Serializer):
    lat = serializers.FloatField(required=True)
    lon = serializers.FloatField(required=True)


class SuggestionsParamsSerializer(serializers.Serializer):
    query = serializers.CharField(required=True)
    location_geo = LocationGeoSerializer(required=False, allow_null=True)
    experiment_variant = serializers.CharField(required=False, allow_null=True)

    @property
    def formatted_params(self) -> dict:
        validated_data = self.validated_data
        params = {'text': validated_data['query']}

        if location := validated_data.get('location_geo'):
            params['location_geo'] = f"{location['lat']},{location['lon']}"

        if variant := validated_data.get('experiment_variant'):
            params['experiment_variant'] = variant

        return params


def get_url(endpoint: Endpoint, version: t.Optional[Version] = Version.V1) -> str:
    return urllib.parse.urljoin(
        f'{_PROTOCOL}{settings.SEARCH_SERVICE_URL}', f'{version}/{endpoint}'
    )


def make_request(endpoint: Endpoint, params: t.Optional[dict] = None) -> requests.Response:
    url = get_url(endpoint)

    try:
        response = requests.get(url, params=params, timeout=_TIMEOUT)
        response.raise_for_status()
    except requests.RequestException as e:
        logger.error('Request to search service failed: %s', e)
        raise e

    return response


def _include_icon_v2(suggestion: dict, return_empty_field=False):
    image_url = ''
    if return_empty_field:
        suggestion['query_parameters']['icon_v2'] = image_url
        return suggestion

    if suggestion['query_parameters']['type'] == 'C':
        if category := CategoryCache.get_by_id(suggestion['query_parameters']['id']):
            image_url = (
                get_business_category_image_url(
                    category.get('internal_name'),
                    BusinessCategoryPlaceholderType.ICON_V2,
                )
                or ''
            )

    suggestion['query_parameters']['icon_v2'] = image_url
    return suggestion


def get_treatment_suggestions(query: str) -> dict:
    params = {'text': query}
    response = make_request(Endpoint.SUGGESTIONS, params)
    response_data = response.json()

    response_data['suggestions'] = [
        _include_icon_v2(suggestion, return_empty_field=True)
        for suggestion in response_data['suggestions']
    ]
    return response_data


def _extract_business_ids(suggestion_api_response: dict) -> list[int]:
    return [
        suggestion['query_parameters']['id']
        for suggestion in suggestion_api_response.get('suggestions', [])
        if suggestion.get('object_type') == 'business'
    ]


def _fetch_business_data_by_id(business_ids: list[int]) -> dict[int, dict]:
    """Retrieves rich business data and returns it as a dictionary mapped by ID."""
    if not business_ids:
        return {}

    docs_to_get = [{"_id": f"business:{id_}", "routing": str(id_)} for id_ in business_ids]
    business_documents = BusinessDocument.mget(
        docs=docs_to_get,
        _source=[
            'id',
            'name',
            'slug',
            'sitemap_url',
            'thumbnail_photo',
            'is_b_listing',
            'business_location',
        ],
    )
    return {doc['id']: doc for doc in business_documents if doc}


def _enrich_response_data(suggestion_api_response: dict) -> dict:
    business_ids = _extract_business_ids(suggestion_api_response)
    business_data_by_id = _fetch_business_data_by_id(business_ids)

    enriched_suggestions = []
    treatment_suggestion_count = 0
    for suggestion in suggestion_api_response.get('suggestions', []):
        obj_type = suggestion.get('object_type')

        if obj_type == SuggestionObjectType.BUSINESS:
            business_id = suggestion.get('query_parameters', {}).get('id')
            if business_id in business_data_by_id:
                business_document = business_data_by_id[business_id]
                suggestion['query_parameters'] = BusinessQueryHintSerializer(
                    instance=business_document
                ).data
                enriched_suggestions.append(suggestion)
            else:
                logger.warning('BusinessDocument for _id: %s was not found', business_id)

        elif obj_type == SuggestionObjectType.TREATMENT:
            suggestion['query_parameters']['icon_v2'] = ''
            enriched_suggestions.append(suggestion)
            treatment_suggestion_count += 1

    return {
        'suggestions': enriched_suggestions,
        'number_category_suggestions': treatment_suggestion_count,
    }


def get_suggestions(
    query: str, location_geo: t.Optional[str] = None, experiment_variant: t.Optional[str] = None
) -> dict:
    serializer = SuggestionsParamsSerializer(
        data={
            'query': query,
            'location_geo': location_geo,
            'experiment_variant': experiment_variant,
        }
    )
    serializer.is_valid(raise_exception=True)
    params = serializer.formatted_params

    response = make_request(Endpoint.SUGGESTIONS, params)
    raw_api_response = response.json()
    return _enrich_response_data(raw_api_response)
