#!/usr/bin/env python
# pylint: disable=invalid-name
from django.conf import settings
from django.contrib import admin
from django.urls import (
    include,
    re_path as url,
)
from django.views.i18n import set_language
from django_dbz_admin.admin import DataStreamAdmin, HeartbeatAdmin, SignalAdmin
from django_dbz_admin.models import DataStream, Heartbeat, Signal
from bo_django_outbox.admin import OutboxEventAdmin
from bo_django_outbox.models import OutboxEvent

from drf_api.service.other.debug.views import warmup
from drf_api.urls import urlpatterns
from webapps.admin_extra.healthcheck import HealthcheckView
from webapps.admin_extra.quick_reports import QuickReportsView
from webapps.admin_extra.sites import BooksyAdminSitePlus
from webapps.admin_extra.views.admin_tools import AdminToolsView, MigrationsAndImportersView
from webapps.admin_extra.views.apple_map import AppleMapTokenView
from webapps.admin_extra.views.apps_flyer import (
    AppsflyerGenericLinkOperationsView,
    AppsflyerLinkOperationsView,
)
from webapps.admin_extra.views.authenticator_code_generator import (
    GenerateSuperuserAuthenticatorCodeView,
)
from webapps.admin_extra.views.autocomplete import (
    AddressAutocompleteView,
    RegionAutoCompleteView,
    SeoRegionAutoCompleteView,
    TreatmentAutocompleteView,
    ZipAutoCompleteView,
    StateAutoCompleteView,
)
from webapps.admin_extra.views.best_of_booksy import BestOfBooksyBusinessAwardsView
from webapps.admin_extra.views.billing import (
    MassBillingBusinessOfferView,
    MassBillingMerchantsSwitcherView,
    MassBillingOfferPurchaseView,
    MassBillingOffersChangerView,
    MassBillingPurchaseFlowView,
    MassBillingSubscriptionSmsChanger,
    MassBusinessDiscountView,
    MassSubscriptionProductImport,
    MassSwitchMerchantsPaymentProcessorView,
    MassSwitchMerchantsToBillingView,
    MassSwitchOfflineToBillingView,
    MassBillingStripeMigrator,
    MassBusinessExtendTrialByGivenDaysView,
)
from webapps.admin_extra.views.blisting_import import BListingImportView
from webapps.admin_extra.views.booking_remove import BookingRemoveView
from webapps.admin_extra.views.booksy_gift_cards.report import BooksyGiftCardsFinancialReportView
from webapps.admin_extra.views.booksy_pay import BooksyPayAppointmentView
from webapps.admin_extra.views.boost_change_commission_mass_tool import (
    BoostChangeCommissionMassTool,
)
from webapps.admin_extra.views.boost_change_payment_source_mass_tool import (
    BoostChangePaymentSourceView,
)
from webapps.admin_extra.views.boost_claims_processing import (
    BoostClaimsProcessingView,
)
from webapps.admin_extra.views.boost_status_mass_tool import BoostChangeStatusView
from webapps.admin_extra.views.boost_switch import BoostSwitchView

from webapps.admin_extra.views.business import (
    BusinessChurnSwitchView,
    BusinessDisableOldFizjoView,
    BusinessFreezeSwitchView,
    BusinessMassRestoreFromInvalidView,
    BusinessReTrialSwitchView,
    BusinessStatusChangeView,
    BusinessTrialExtendView,
)
from webapps.admin_extra.views.business_work_schedule import (
    BusinessesWorkScheduleView,
)
from webapps.admin_extra.views.businesspartnerdata_tool import BusinessPartnerDataView
from webapps.admin_extra.views.celery_task_status import CeleryTaskStatusView
from webapps.admin_extra.views.check_agreement import CheckAgreementView
from webapps.admin_extra.views.custom_push import CustomPushSendView
from webapps.admin_extra.views.customer import CustomerImportView
from webapps.admin_extra.views.customer_consents import RemoveCustomerConsentsView
from webapps.admin_extra.views.deeplink import (
    DeepLinkDetailsView,
    DeepLinkGeneratorView,
    DeepLinkListView,
    GeneralDeepLinkGeneratorView,
)
from webapps.admin_extra.views.email import MockEmailSendView
from webapps.admin_extra.views.enterprise_batch_import import (
    EnterpriseDataImportView,
)
from webapps.admin_extra.views.enterprise_batch_update import (
    AboutUsServicesView,
    BatchUpdateBusinessOpeningHoursView,
    BatchUpdateBusinessSecuritySettingsView,
    BatchUpdateBusinessVisibilityView,
    BatchUpdateImageBundlesView,
    BatchUpdateLogosView,
    BatchUpdateResourcesView,
    BatchUpdateServicesView,
    CalendarVisibilityEditView,
)
from webapps.admin_extra.views.exception_debug_view import ExceptionDebugTestView
from webapps.admin_extra.views.feature_flags import FeatureFlagsView
from webapps.admin_extra.views.fiscal_archive_signature_verification import (
    FiscalArchiveSignatureVerification,
)
from webapps.admin_extra.views.frontdesk_access import FrontdeskAccessView
from webapps.admin_extra.views.gdpr_data_import import (
    BusinessGDPRDataImportView,
)
from webapps.admin_extra.views.geocode import ReverseGeocodeView
from webapps.admin_extra.views.images import ImagesImportView
from webapps.admin_extra.views.mail_pdf_sender import MailPdfSender
from webapps.admin_extra.views.monitoring import (
    MonitoringView,
    ThrottleRatesView,
)
from webapps.admin_extra.views.navision.invoice_cycles_paid_in_next_month import (
    InvoiceCyclesPaidInNextMonth,
)
from webapps.admin_extra.views.notification_admin import ManualAdminNotificationView
from webapps.admin_extra.views.navision import (
    BuyerMerchantTaxIDChangeView,
    DryRunInvoicesPerBCStartReport,
    DryRunOfflineBoostInvoicing,
    ImportSubscriptionBuyersView,
    InvoicesBillingCycle,
    OfflineInvoicingforMigrationsView,
    OfflineInvoicingView,
    UpdateSubscriptionBuyersView,
)
from webapps.admin_extra.views.password import PasswordChangeView
from webapps.admin_extra.views.password_reset import EnforcePasswordResetChangeView
from webapps.admin_extra.views.payment_providers.import_stripe_transfer_funds import (
    ImportStripeManualTransferFundView,
)
from webapps.admin_extra.views.payment_providers.stripe_transfer_funds import (
    StripeManualTransferFundView,
)
from webapps.admin_extra.views.payout import (
    B2BRewardPayoutView,
)
from webapps.admin_extra.views.posplans_batch_change import PosPlansBatchChangesView
from webapps.admin_extra.views.products_import import ProductsImportView
from webapps.admin_extra.views.push import PushSendView
from webapps.admin_extra.views.qr_code_regeneration import QrCodeRegenerationView
from webapps.admin_extra.views.refresh_statistics import RefreshStatisticsView
from webapps.admin_extra.views.report import (
    ReportDoubleSubscriptionView,
    ReportGenerateStatisticView,
    ReportInvoiceSummaryView,
)
from webapps.admin_extra.views.review import (
    ReviewImportView,
)
from webapps.admin_extra.views.safety_rules import SafetyRulesSetView
from webapps.admin_extra.views.settings_view import SettingsView
from webapps.admin_extra.views.sms import (
    PrepaidSMSPackageUpdaterView,
    SmsLimitChangerView,
    SmsSendView,
)
from webapps.admin_extra.views.stats_and_reports import (
    DownloadStatisticsReportsView,
)
from webapps.admin_extra.views.subscription import (
    AddSubscriptionView,
    AppleSubscriptionsCheckView,
    ImportOfflineView,
    SubscriptionBatchEditView,
    SubscriptionImportView,
    SubscriptionListingView,
)
from webapps.admin_extra.views.subscriptions_migration import SubscriptionsMigrationView
from webapps.admin_extra.views.superuser_login_export import SuperuserLoginExportView
from webapps.admin_extra.views.timezone import TimeZoneView
from webapps.admin_extra.views.treatment import TreatmentMatchView
from webapps.admin_extra.views.trial_end_changer import TrialEndChangerView
from webapps.admin_extra.views.update_padding_time import UpdatePaddingTimeView
from webapps.admin_extra.views.versum import (
    VersumDataImportView,
)
from webapps.admin_extra.views.waitlist import WaitListView
from webapps.admin_extra.views.warehouse import WarehouseCommoditiesImportView
from webapps.admin_extra.views.zipcode_areas import ZipCodeAreaImportView
from webapps.billing.admin_views import (
    AddBillingSubscriptionView,
    AddPaymentMethodView,
    BillingRefundTransactionView,
    BillingSubscriptionCancellationView,
    BusinessBillingSwitchView,
    MigrateStripeTransactionView,
)
from webapps.experiment_v3.views.experiment_data_import_view import (
    ExperimentDataImportView,
)
from webapps.purchase.views import cancel_subscription
from webapps.subdomain_grpc.admin import SubdomainEditView, SubdomainsSearchView

admin.site = BooksyAdminSitePlus()
admin.autodiscover()


# Register bo_django_outbox models
admin.site.register(OutboxEvent, OutboxEventAdmin)

admin.site.register(DataStream, DataStreamAdmin)
admin.site.register(Signal, SignalAdmin)
admin.site.register(Heartbeat, HeartbeatAdmin)

admin.site.register_view(
    'boost_change_status_mass_tool',
    name="Boost Change Status",
    urlname="boost_change_status",
    view=BoostChangeStatusView.as_view(),
)

admin.site.register_view(
    'boost_change_payment_source_mass_tool',
    name="Boost Change Payment Source",
    urlname="boost_change_payment_source",
    view=BoostChangePaymentSourceView.as_view(),
)

admin.site.register_view(
    'boost_change_commission_mass_tool',
    name="Boost Change Commission",
    urlname="boost_change_commission",
    view=BoostChangeCommissionMassTool.as_view(),
)

admin.site.register_view(
    'fiscal_archive_signature_verification_tool',
    name='Fiscal Archive Verification',
    urlname='fiscal_archive_signature_verification',
    view=FiscalArchiveSignatureVerification.as_view(),
)

admin.site.register_view(
    'quick_reports_2',
    name='Quick Reports',
    urlname='quick_reports_2',
    view=QuickReportsView.as_view(),
)
admin.site.register_view(
    'customer_import',
    name='Customer Import',
    urlname='customer_import',
    view=CustomerImportView.as_view(),
)
admin.site.register_view(
    'business_trial_extension',
    name='Business Trial Extension',
    urlname='business_trial_extension',
    view=BusinessTrialExtendView.as_view(),
)
admin.site.register_view(
    'best_of_booksy_business_awards_upload',
    name='Best of Booksy Business Awards Upload',
    urlname='best_of_booksy_business_awards_upload',
    view=BestOfBooksyBusinessAwardsView.as_view(),
)
admin.site.register_view(
    'change_password',
    name='Change Password',
    urlname='change_password',
    visible=False,
    view=PasswordChangeView.as_view(),
)
admin.site.register_view(
    'email_send_form',
    name='Send mock email',
    urlname='email_send_form',
    visible=True,
    view=MockEmailSendView.as_view(),
)
admin.site.register_view(
    'settings', name='Settings', urlname='settings', visible=False, view=SettingsView.as_view()
)
admin.site.register_view(
    'monitoring',
    name='Monitoring',
    urlname='monitoring',
    visible=False,
    view=MonitoringView.as_view(),
)
admin.site.register_view(
    'rates', name='Throttle rates', urlname='rates', visible=False, view=ThrottleRatesView.as_view()
)
admin.site.register_view(
    'business_status_changer',
    name='Business Status Changer',
    urlname='business_status_changer',
    visible=False,
    view=BusinessStatusChangeView.as_view(),
)
admin.site.register_view(
    'business_retrial_switch',
    name='Business ReTrial switch',
    urlname='business_retrial_switch',
    view=BusinessReTrialSwitchView.as_view(),
)
admin.site.register_view(
    'generate_statistics_report',
    name='Generate Statistics Report',
    urlname='generate_statistics_report',
    view=ReportGenerateStatisticView.as_view(),
)

admin.site.register_view(
    'download_reports',
    name='Generate Statistics Reports 3.0',
    urlname='download_reports',
    view=DownloadStatisticsReportsView.as_view(),
)
admin.site.register_view(
    'summary_report',
    name='Summary Report',
    urlname='summary_report',
    view=ReportInvoiceSummaryView.as_view(),
)
admin.site.register_view(
    'booksy_gift_card_report',
    name='Booksy Gift Card Report',
    urlname='booksy_gift_card_report',
    view=BooksyGiftCardsFinancialReportView.as_view(),
)
admin.site.register_view(
    r'subscriptions/cancel/(?P<sub_id>\d+)',
    name='Cancel Subscription',
    urlname='cancel_subscription',
    visible=False,
    view=cancel_subscription,
)
admin.site.register_view(
    'subscriptions/import',
    name='Import subscription transactions',
    urlname='import_subscriptions',
    visible=True,
    view=ImportOfflineView.as_view(),
)
admin.site.register_view(
    'warehouse_commodities_import',
    name='Warehouse Commodities Import',
    urlname='warehouse_commodities_import',
    view=WarehouseCommoditiesImportView.as_view(),
)
admin.site.register_view(
    'product_import',
    name='Product (Inventory) Import',
    urlname='product_import',
    view=ProductsImportView.as_view(),
)
admin.site.register_view(
    'images_import', name='Images Import', urlname='images_import', view=ImagesImportView.as_view()
)

admin.site.register_view(
    'versum_import',
    name='Versum Data Import',
    urlname='versum_import',
    view=VersumDataImportView.as_view(),
)

admin.site.register_view(
    'enterprise_import',
    name='Enterprise Data Import',
    urlname='enterprise_import',
    view=EnterpriseDataImportView.as_view(),
)

admin.site.register_view(
    'bookings_remover',
    name='Bookings Remover',
    urlname='bookings_remover',
    view=BookingRemoveView.as_view(),
)

admin.site.register_view(
    'businesses_freeze_switch',
    name='Businesses freeze switch',
    urlname='businesses_freeze_switch',
    view=BusinessFreezeSwitchView.as_view(),
)
admin.site.register_view(
    'businesses_churn_switch',
    name='Businesses churn switch',
    urlname='businesses_churn_switch',
    view=BusinessChurnSwitchView.as_view(),
)
admin.site.register_view(
    'sms_limit_changer',
    name='SMS Limit Changer',
    urlname='sms_limit_changer',
    visible=True,
    view=SmsLimitChangerView.as_view(),
)
admin.site.register_view(
    'sms_package_updater',
    name='SMS Prepaid Package Updater',
    urlname='sms_package_updater',
    visible=False,
    view=PrepaidSMSPackageUpdaterView.as_view(),
)
admin.site.register_view(
    'sms_tool', name='SMS Tool', urlname='sms_tool', visible=False, view=SmsSendView.as_view()
)

admin.site.register_view(
    'zip_autocomplete',
    name='Zip Autocomplete',
    urlname='zip_autocomplete',
    visible=False,
    view=ZipAutoCompleteView.as_view(),
)
admin.site.register_view(
    'state_autocomplete',
    name='State Autocomplete',
    urlname='state_autocomplete',
    visible=False,
    view=StateAutoCompleteView.as_view(),
)
admin.site.register_view(
    'region_autocomplete',
    name='Region Autocomplete',
    urlname='region_autocomplete',
    visible=False,
    view=RegionAutoCompleteView.as_view(),
)
admin.site.register_view(
    'seo_region_autocomplete',
    name='SEO Region Autocomplete',
    urlname='seo_region_autocomplete',
    visible=False,
    view=SeoRegionAutoCompleteView.as_view(),
)

admin.site.register_view(
    'review_import', name='Review Import', urlname='review_import', view=ReviewImportView.as_view()
)

admin.site.register_view(
    'treatment_autocomplete',
    name='Treatment Autocomplete',
    urlname='treatment_autocomplete',
    visible=False,
    view=TreatmentAutocompleteView.as_view(),
)

admin.site.register_view(
    'match_treatment',
    name='Match treatment',
    urlname='match_treatment',
    view=TreatmentMatchView.as_view(),
)

admin.site.register_view(
    'check_agreement',
    name='Check Agreement',
    urlname='check_agreement',
    visible=False,
    view=CheckAgreementView.as_view(),
)

admin.site.register_view(
    'zipcodes_areas_importer',
    name='Zip Codes To Areas Importer',
    urlname='zipcodes_areas_importer',
    view=ZipCodeAreaImportView.as_view(),
)

admin.site.register_view(
    'experiment_data_importer',
    name='Experiment data importer',
    urlname='experiment_data_importer',
    view=ExperimentDataImportView.as_view(),
)

admin.site.register_view(
    'appsflyer_operations',
    name='Appsflyer Operations',
    urlname='appsflyer_operations',
    view=AppsflyerLinkOperationsView.as_view(),
)

admin.site.register_view(
    'appsflyer_generic_operations',
    name='Appsflyer Generic Operations',
    urlname='appsflyer_generic_operations',
    view=AppsflyerGenericLinkOperationsView.as_view(),
)

admin.site.register_view(
    'deeplink_generator',
    name='Generate deeplink',
    urlname='deeplink_generator',
    view=DeepLinkGeneratorView.as_view(),
)
admin.site.register_view(
    'deeplink_list', name='Deeplink list', urlname='deeplink_list', view=DeepLinkListView.as_view()
)

admin.site.register_view(
    'deeplink_details',
    name='Deeplink details',
    urlname='deeplink_details',
    view=DeepLinkDetailsView.as_view(),
)

admin.site.register_view(
    'custom_push_send_view',
    name='Custom Push Send View',
    urlname='custom_push_send_view',
    view=CustomPushSendView.as_view(),
)


admin.site.register_view(
    'general_deeplink_generator',
    name='Generate general deeplink',
    urlname='general_deeplink_generator',
    view=GeneralDeepLinkGeneratorView.as_view(),
)

admin.site.register_view(
    'map_token',
    name='Map Token',
    urlname='map_token',
    visible=False,
    view=AppleMapTokenView.as_view(),
)

admin.site.register_view(
    'address_autocomplete',
    name='Address Autocomplete',
    urlname='address_autocomplete',
    visible=False,
    view=AddressAutocompleteView.as_view(),
)

admin.site.register_view(
    'reverse_geocode',
    name='Reverse Geocode',
    urlname='reverse_geocode',
    visible=False,
    view=ReverseGeocodeView.as_view(),
)

admin.site.register_view(
    'subscription_listing_preview',
    name='Subscription Listing Preview',
    urlname='subscription_listing_preview',
    visible=False,
    view=SubscriptionListingView.as_view(),
)

admin.site.register_view(
    'add_subscription',
    name='Add subscription',
    urlname='add_subscription',
    visible=False,
    view=AddSubscriptionView.as_view(),
    main_menu_app='purchase',
)

admin.site.register_view(
    'apple_subscription_check',
    name='Apple subscription check',
    urlname='apple_subscription_check',
    view=AppleSubscriptionsCheckView.as_view(),
)

admin.site.register_view(
    'double_subscriptions_report',
    name='Double Subscriptions Report',
    urlname='double_subscriptions_report',
    visible=False,
    view=ReportDoubleSubscriptionView.as_view(),
)

admin.site.register_view(
    'businesses_work_schedule',
    name='Businesses Work Schedule',
    urlname='businesses_work_schedule',
    view=BusinessesWorkScheduleView.as_view(),
)

admin.site.register_view(
    'wait_list',
    name='Wait List',
    urlname='wait_list',
    view=WaitListView.as_view(),
)

admin.site.register_view(
    'b2breferral_reward_payout',
    name='B2B Referral Reward  payout',
    urlname='b2breferral_reward_payout',
    view=B2BRewardPayoutView.as_view(),
)

admin.site.register_view(
    'stripe_manual_transfer_fund',
    name='Stripe Manual Fund Transfer',
    urlname='stripe_manual_transfer_fund',
    view=StripeManualTransferFundView.as_view(),
)

admin.site.register_view(
    'import_stripe_manual_transfer_fund',
    name='Import Stripe Manual Fund Transfer',
    urlname='import_stripe_manual_transfer_fund',
    view=ImportStripeManualTransferFundView.as_view(),
)


admin.site.register_view(
    'set_safety_rules',
    name='Set Safety Rules',
    urlname='set_safety_rules',
    view=SafetyRulesSetView.as_view(),
)

admin.site.register_view(
    'push_sender', name='Push Sender', urlname='push_sender', view=PushSendView.as_view()
)


admin.site.register_view(
    'admin_tools',
    name='Admin Tools',
    urlname='admin_tools',
    visible=False,
    view=AdminToolsView.as_view(),
)

admin.site.register_view(
    'migrations_and_importers',
    name='Migrations & importers',
    urlname='migrations_and_importers',
    visible=True,
    view=MigrationsAndImportersView.as_view(),
)

admin.site.register_view(
    r'add_payment_method/(?P<business_id>\d+)/?',
    'Add payment method',
    visible=False,
    view=AddPaymentMethodView.as_view(),
    urlname='add_payment_method',
)

admin.site.register_view(
    'add_billing_subscription',
    'Add billing subscription',
    visible=False,
    view=AddBillingSubscriptionView.as_view(),
    urlname='add_billing_subscription',
)

admin.site.register_view(
    r'billing_refund_transaction/(?P<transaction_id>\d+)/?',
    'Refund Transaction',
    visible=False,
    view=BillingRefundTransactionView.as_view(),
    urlname='billing_refund_transaction',
)

admin.site.register_view(
    r'celery_task_status/(?P<uuid>[a-z0-9-]+)/?',
    visible=False,
    view=CeleryTaskStatusView.as_view(),
    urlname='celery_task_status',
)

admin.site.register_view(
    r'migrate_stripe_transaction/(?P<business_id>\d+)/?',
    'Migrate Stripe Transaction',
    visible=False,
    view=MigrateStripeTransactionView.as_view(),
    urlname='migrate_stripe_transaction',
)

admin.site.register_view(
    r'billing_business_switch/(?P<business_id>\d+)/?',
    name='Switch business new/old billing',
    visible=False,
    view=BusinessBillingSwitchView.as_view(),
    urlname='billing_business_switch',
)

admin.site.register_view(
    r'billing_subscription_cancellation/(?P<subscription_id>\d+)/?',
    name='Cancel the subscription',
    visible=False,
    view=BillingSubscriptionCancellationView.as_view(),
    urlname='billing_subscription_cancellation',
)

admin.site.register_view(
    'batch_update_services',
    name='Batch Update Services',
    urlname='batch_update_services',
    visible=False,
    view=BatchUpdateServicesView.as_view(),
)

admin.site.register_view(
    'batch_update_resources',
    name='Batch Update Resources',
    urlname='batch_update_resources',
    visible=False,
    view=BatchUpdateResourcesView.as_view(),
)
admin.site.register_view(
    'batch_update_business_visibility',
    name='Batch Update Business Visibility',
    urlname='batch_update_business_visibility',
    visible=False,
    view=BatchUpdateBusinessVisibilityView.as_view(),
)
admin.site.register_view(
    'business_restore_from_invalid',
    name='Business Restore From Invalid',
    urlname='business_restore_from_invalid',
    view=BusinessMassRestoreFromInvalidView.as_view(),
)
admin.site.register_view(
    'batch_update_logos',
    name='Batch Update Logos',
    urlname='batch_update_logos',
    visible=False,
    view=BatchUpdateLogosView.as_view(),
)
admin.site.register_view(
    'batch_update_business_security_settings',
    name='Batch Update Business Security Settings',
    urlname='batch_update_business_security_settings',
    visible=False,
    view=BatchUpdateBusinessSecuritySettingsView.as_view(),
)
admin.site.register_view(
    'batch_update_business_opening_hours',
    'Batch Update Business Opening Hours',
    visible=False,
    view=BatchUpdateBusinessOpeningHoursView.as_view(),
)
admin.site.register_view(
    'batch_update_image_bundles',
    'Batch Update Image Bundles',
    visible=False,
    view=BatchUpdateImageBundlesView.as_view(),
)

admin.site.register_view(
    'update_padding_time',
    name='Update Padding Time',
    urlname='update_padding_time',
    view=UpdatePaddingTimeView.as_view(),
)
admin.site.register_view(
    'business_GDPR_data_import',
    name='Import business GDPR data',
    urlname='business_GDPR_data_import',
    view=BusinessGDPRDataImportView.as_view(),
)
admin.site.register_view(
    'subscription_import',
    name='Subscription import',
    urlname='subscription_import',
    view=SubscriptionImportView.as_view(),
)
admin.site.register_view(
    'update_subscription_buyers',
    name='Update subscription buyers',
    urlname='update_subscription_buyers',
    view=UpdateSubscriptionBuyersView.as_view(),
)
admin.site.register_view(
    'import_subscription_buyers',
    name='Import subscription buyers',
    urlname='import_subscription_buyers',
    view=ImportSubscriptionBuyersView.as_view(),
)
admin.site.register_view(
    'mass_switch_merchants_to_billing',
    name='Mass switch Merchants to Billing',
    urlname='mass_switch_merchants_to_billing',
    view=MassSwitchMerchantsToBillingView.as_view(),
)
admin.site.register_view(
    'mass_switch_merchants_payment_processor',
    name='Mass switch Merchants Payment Processor',
    urlname='mass_switch_merchants_payment_processor',
    view=MassSwitchMerchantsPaymentProcessorView.as_view(),
)
admin.site.register_view(
    'mass_billing_merchants_switcher',
    name='Mass switch Merchants between old and new subscriptions',
    urlname='mass_billing_merchants_switcher',
    view=MassBillingMerchantsSwitcherView.as_view(),
)
admin.site.register_view(
    'mass_sms_cost_and_limit_changer',
    name='Mass Billing subscription sms cost and limit changer',
    urlname='mass_sms_cost_and_limit_changer',
    view=MassBillingSubscriptionSmsChanger.as_view(),
)
admin.site.register_view(
    'mass_stripe_migration_tool',
    name='Mass Stripe migrator tool.',
    urlname='mass_stripe_migration_tool',
    view=MassBillingStripeMigrator.as_view(),
)
admin.site.register_view(
    'mass_billing_discounts',
    name='Mass billing discounts tool',
    urlname='mass_billing_discounts',
    view=MassBusinessDiscountView.as_view(),
)
admin.site.register_view(
    'mass_billing_business_offer',
    name='Mass billing Merchants offers',
    urlname='mass_billing_business_offer',
    view=MassBillingBusinessOfferView.as_view(),
)
admin.site.register_view(
    'mass_billing_offer_purchase',
    name='Mass billing offer purchase',
    urlname='mass_billing_offer_purchase',
    view=MassBillingOfferPurchaseView.as_view(),
)
admin.site.register_view(
    'mass_subscription_product_import',
    name='Mass Subscription Product Import',
    urlname='mass_subscription_product_import',
    view=MassSubscriptionProductImport.as_view(),
)
admin.site.register_view(
    'mass_billing_offers_changer',
    name='Mass billing offers changer',
    urlname='mass_billing_offers_changer',
    view=MassBillingOffersChangerView.as_view(),
)
admin.site.register_view(
    'mass_billing_purchase_flow_changer',
    name='Mass billing purchase flow changer',
    urlname='mass_billing_purchase_flow_changer',
    view=MassBillingPurchaseFlowView.as_view(),
)
admin.site.register_view(
    'mass_switch_offline_businesses_to_billing',
    name='Mass switch offline businesses to billing',
    urlname='mass_switch_offline_businesses_to_billing',
    view=MassSwitchOfflineToBillingView.as_view(),
)
admin.site.register_view(
    'subscription_batch_edit', 'Subscription batch edit', view=SubscriptionBatchEditView.as_view()
)
admin.site.register_view(
    'about_us_update',
    'Edit about us',
    visible=False,
    view=AboutUsServicesView.as_view(),
)
admin.site.register_view(
    'boost_switch',
    name='Boost Switch',
    urlname='boost_switch',
    view=BoostSwitchView.as_view(),
)
admin.site.register_view(
    'boost_claims_processing',
    name='Boost claims mass processing',
    urlname='boost_claims_processing',
    view=BoostClaimsProcessingView.as_view(),
    visible=settings.API_COUNTRY == 'pl',
)

admin.site.register_view(
    'calendar_visibility',
    'Calendar Visibility Edit',
    visible=False,
    view=CalendarVisibilityEditView.as_view(),
)

admin.site.register_view(
    'exception_debug_view',
    'EXCEPTION_DEBUG_VIEW',
    visible=False,
    urlname='exception_debug_view',
    view=ExceptionDebugTestView.as_view(),
)

admin.site.register_view(
    'refresh_statistics',
    name='Refresh Statistics',
    urlname='refresh_statistics',
    view=RefreshStatisticsView.as_view(),
)

admin.site.register_view(
    'frontdesk_access',
    name='Frontdesk Access',
    urlname='frontdesk_access',
    view=FrontdeskAccessView.as_view(),
)

admin.site.register_view(
    'manual_admin_notification',
    name='Send Notification From Admin',
    urlname='manual_admin_notification',
    view=ManualAdminNotificationView.as_view(),
)

admin.site.register_view(
    'manual_user_timezone',
    name='Update user timezone',
    urlname='manual_user_timezone',
    view=TimeZoneView.as_view(),
)

admin.site.register_view(
    'subdomains',
    name='Subdomains',
    urlname='subdomains',
    view=SubdomainsSearchView.as_view(),
)

admin.site.register_view(
    'subdomain_edit/(?P<uuid>[a-z0-9-]+)/?',
    name='Subdomain edit',
    urlname='subdomain_edit',
    view=SubdomainEditView.as_view(),
    visible=False,
)

admin.site.register_view(
    'dry_run_offline_invoicing',
    name='Dry Run Offline Invoicing',
    urlname='dry_run_offline_invoicing',
    view=DryRunOfflineBoostInvoicing.as_view(),
)

admin.site.register_view(
    'offline_invoicing',
    name='Offline Invoicing',
    urlname='offline_invoicing',
    view=OfflineInvoicingView.as_view(),
)

admin.site.register_view(
    'offline_invoicing_for_migrations',
    name='Offline Invoicing for Migrations',
    urlname='offline_invoicing_for_migrations',
    view=OfflineInvoicingforMigrationsView,
)

admin.site.register_view(
    'subscriptions_migration',
    name='Subscriptions Migration',
    urlname='subscriptions_migration',
    view=SubscriptionsMigrationView,
)

admin.site.register_view(
    'dry_run_invoices_report_bc_start',
    name='Dry Run Booksy Billing Invoicing Report',
    urlname='dry_run_invoices_report_bc_start',
    view=DryRunInvoicesPerBCStartReport.as_view(),
)

admin.site.register_view(
    'invoice_new_saas_online',
    name='Invoice SaaS Online based on Billing Cycle',
    urlname='invoice_new_saas_online',
    view=InvoicesBillingCycle.as_view(),
)

admin.site.register_view(
    'invoice_cycles_before_migration',
    name='Invoice cycles started before migration but paid after migration',
    urlname='invoice_cycles_before_migration',
    view=InvoiceCyclesPaidInNextMonth.as_view(),
)

admin.site.register_view(
    'public_partners_businesspartnerdata_upload',
    name='Business Partner Data Upload',
    urlname='public_partners_businesspartnerdata_upload',
    view=BusinessPartnerDataView.as_view(),
)

admin.site.register_view(
    'trial_end_changer',
    name='Trial End Changer',
    urlname='trial_end_changer',
    view=TrialEndChangerView.as_view(),
)

admin.site.register_view(
    'posplans_batch_change',
    name='POS plans batch change',
    urlname='posplans_batch_change',
    view=PosPlansBatchChangesView.as_view(),
)

admin.site.register_view(
    'feature_flags',
    name='Feature Flags',
    urlname='feature_flags',
    visible=True,
    view=FeatureFlagsView.as_view(),
)

admin.site.register_view(
    'mail_pdf_sender',
    name='Mail pdf sender',
    urlname='mail_pdf_sender',
    visible=False,
    view=MailPdfSender.as_view(),
)

admin.site.register_view(
    'customer_consents',
    name="Customer Consents",
    urlname="customer_consents",
    view=RemoveCustomerConsentsView.as_view(),
)
admin.site.register_view(
    'change_tax_id',
    name='Change buyer and merchant Tax ID',
    urlname='change_tax_id',
    view=BuyerMerchantTaxIDChangeView.as_view(),
    visible=False,
)

admin.site.register_view(
    'b_listing_import',
    name='Business Listing Import',
    urlname='b_listing_import',
    view=BListingImportView.as_view(),
)

admin.site.register_view(
    'generate_authenticator_code',
    name='Generate Authenticator Code',
    urlname='generate_authenticator_code',
    view=GenerateSuperuserAuthenticatorCodeView.as_view(),
)

admin.site.register_view(
    'superuser_login_export',
    name='Superuser Login Export',
    urlname='superuser_login_export',
    view=SuperuserLoginExportView.as_view(),
)

admin.site.register_view(
    'qrcode_regeneration',
    name='QR Code Regeneration',
    urlname='qrcode_regeneration',
    view=QrCodeRegenerationView.as_view(),
)

admin.site.register_view(
    r'booksy_pay_appointment/(?P<appointment_id>\d+)/?',
    'Booksy Pay',
    visible=False,
    view=BooksyPayAppointmentView.as_view(),
    urlname='booksy_pay_appointment',
)

admin.site.register_view(
    'disable_old_fizjo',
    name='Mass business Old fizjo disabler',
    urlname='disable_old_fizjo',
    view=BusinessDisableOldFizjoView.as_view(),
)

admin.site.register_view(
    'trial_by_given_day_extender',
    name='Extend Trial by Given Days',
    urlname='trial_by_given_day_extender',
    view=MassBusinessExtendTrialByGivenDaysView.as_view(),
)

admin.site.register_view(
    'enforce_password_reset',
    name="Enorce Password Reset",
    urlname="enforce_password_reset",
    view=EnforcePasswordResetChangeView.as_view(),
)

prefix = f'^admin/{settings.API_COUNTRY}/'
if settings.ENABLED_GOOGLE_SIGN_IN:
    urlpatterns.append(
        url(
            prefix + r'',
            include(
                'webapps.allauth_google.urls',
            ),
        )
    )
    urlpatterns.append(
        url(
            prefix + r'',
            include(
                'webapps.saml_auth.urls',
            ),
        )
    )
urlpatterns += [
    url(
        prefix + r'set_language/',
        set_language,
        name='set_language',
    ),
    url(f'{prefix}healthcheck', HealthcheckView.as_view(), name='admin-healthcheck'),
    url(f'{prefix}warmup', warmup, name='admin-warmup'),
    # Place all url patterns before this comment, because Django's AdminSite has catch-all view.
    url(
        prefix + r'',
        include(
            arg=(admin.site.get_urls(), 'admin'),
            namespace=admin.site.name,
        ),
    ),
]
