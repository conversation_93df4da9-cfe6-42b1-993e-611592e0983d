#!/usr/bin/env python
import argparse
import csv

import django

django.setup()  # noqa
from django.conf import settings
from pprint import pprint

from webapps.market_pay import enums
from webapps.adyen.helpers import make_request as adyen_make_request

api_urls = {
    'list': '/getNotificationConfigurationList',
    'create': '/createNotificationConfiguration',
    'update': '/updateNotificationConfiguration',
    'test': '/testNotificationConfiguration',
    'delete': '/deleteNotificationConfigurations',
}


def make_request(data, url):
    return adyen_make_request(
        data,
        '{}{}'.format(settings.MARKET_PAY_NOTIFICATION_URL, url),
        market_pay=True,
    )


def get_events():
    return [
        dict(
            NotificationEventConfiguration=dict(
                eventType=event_type,
                includeMode=('INCLUDE'),
            )
        )
        for event_type in enums.NotificationEvent.values()
    ]


def get_configuration(host, notification_id=None):
    return dict(
        configurationDetails=dict(
            active=True,
            apiVersion=5,
            description='MarketPay Standard Notifications',
            eventConfigs=get_events(),
            messageFormat='JSON',
            notificationId=notification_id,
            notifyURL=host,
            notifyUsername=settings.ADYEN_NOTIFICATION_AUTH[0],
            notifyPassword=settings.ADYEN_NOTIFICATION_AUTH[1],
        ),
    )


def get_notification_id(quiet=True):
    response = make_request({}, api_urls['list'])
    response_data = response.json()
    if not quiet:
        pprint(response_data)

    configurations = response_data.get('configurations')
    if not configurations:
        return None
    if len(configurations) > 1:
        print('WARNING: more than one configuration configured')
        for config in configurations:
            pprint(config['NotificationConfigurationDetails'])

    return configurations[0]['NotificationConfigurationDetails']['notificationId']


def main(action, host):
    quiet = action != 'get'
    notification_id = get_notification_id(quiet=quiet)
    if action == 'get':
        return

    data = {}
    if action == 'set':
        if notification_id is None:
            url = api_urls['create']
            print('Creating new notification configuration')
        else:
            url = api_urls['update']
            print('Updating existing configuration', notification_id)
        data = get_configuration(host, notification_id=notification_id)
    elif action == 'test':
        if notification_id is None:
            print('No configuration available. Create one to test.')
            return
        data = dict(notificationId=notification_id)
        url = api_urls['test']

    response = make_request(data, url)
    response_data = response.json()
    pprint(response_data)


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="Adyen Notification Configuration Service.")
    parser.add_argument(
        'action',
        nargs='?',
        choices=['get', 'set', 'test'],
        default='get',
    )
    parser.add_argument(
        # should point to /marketpay_notifications/ view
        # e.g. https://booksy.com/api/us/2/marketpay/notifications/
        'host',
        nargs='?',
    )
    args = parser.parse_args()

    if args.action == 'set':
        assert args.host is not None, 'Provide host address'

    main(args.action, args.host)
