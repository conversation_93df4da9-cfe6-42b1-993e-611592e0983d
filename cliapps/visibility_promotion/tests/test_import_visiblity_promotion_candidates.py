import argparse
import random
import typing
from dataclasses import astuple, dataclass, fields
from unittest.mock import patch

import pytest
from mock.mock import Mock
from model_bakery import baker

from cliapps.visibility_promotion.import_visibility_promotion_candidates import run
from webapps.visibility_promotion.models import VisibilityPromotionCandidate


@dataclass
class VisibilityPromotionCandidateData:
    business_id: int
    id: str = ''
    active: bool = False


def _get_row(visiblity_promotion_candidate_data: VisibilityPromotionCandidateData):
    row = [str(field_value) for field_value in astuple(visiblity_promotion_candidate_data)]

    return ','.join(row) + '\n'


def _get_mocked_input_file(
    visibility_promotion_candidate_list: typing.List[VisibilityPromotionCandidateData],
):
    columns = [field.name for field in fields(VisibilityPromotionCandidateData)]
    input_file = f"{','.join(columns)}\n"
    for visibility_promotion_candidate in visibility_promotion_candidate_list:
        input_file += _get_row(visibility_promotion_candidate)

    return input_file.encode()


@pytest.mark.django_db
def test_import_all_rows(gcs_client_mock):
    rows = [VisibilityPromotionCandidateData(business_id=random.randint(1, 1000)) for i in range(5)]
    gcs_client_mock.return_value.get_data = Mock(return_value=_get_mocked_input_file(rows))

    args = argparse.Namespace(
        filename='name.csv',
        ticket='TICKET-ID',
        dry_run=False,
        force_delete=False,
    )
    with patch('argparse.ArgumentParser.parse_args', return_value=args):
        run()

    assert VisibilityPromotionCandidate.objects.all().count() == 5
    assert VisibilityPromotionCandidate.objects.filter(active=False).count() == 5


@pytest.mark.django_db
def test_import_active_rows(gcs_client_mock):
    updated_rows = [
        VisibilityPromotionCandidateData(business_id=random.randint(1, 1000), active=True)
    ]
    gcs_client_mock.return_value.get_data = Mock(return_value=_get_mocked_input_file(updated_rows))
    args = argparse.Namespace(
        filename='name.csv',
        ticket='TICKET-ID',
        dry_run=False,
        force_delete=False,
    )
    with patch('argparse.ArgumentParser.parse_args', return_value=args):
        run()

    assert VisibilityPromotionCandidate.objects.all().count() == 1
    assert VisibilityPromotionCandidate.objects.filter(active=True).count() == 1


@pytest.mark.django_db
def test_import_inactive_rows(gcs_client_mock):
    updated_rows = [
        VisibilityPromotionCandidateData(business_id=random.randint(1, 1000), active=False)
    ]
    gcs_client_mock.return_value.get_data = Mock(return_value=_get_mocked_input_file(updated_rows))
    args = argparse.Namespace(
        filename='name.csv',
        ticket='TICKET-ID',
        dry_run=False,
        force_delete=False,
    )
    with patch('argparse.ArgumentParser.parse_args', return_value=args):
        run()

    assert VisibilityPromotionCandidate.objects.all().count() == 1
    assert VisibilityPromotionCandidate.objects.filter(active=False).count() == 1


@pytest.mark.django_db
def test_update(gcs_client_mock):
    business_id = 1234
    visibility_promotion_candidate = baker.make(
        VisibilityPromotionCandidate, business_id=business_id
    )
    updated_rows = [
        VisibilityPromotionCandidateData(
            business_id=business_id, id=visibility_promotion_candidate.id, active=True
        )
    ]
    gcs_client_mock.return_value.get_data = Mock(return_value=_get_mocked_input_file(updated_rows))

    # Before update:
    assert VisibilityPromotionCandidate.objects.all().count() == 1
    assert VisibilityPromotionCandidate.objects.first().business_id == business_id
    assert VisibilityPromotionCandidate.objects.filter(active=False).count() == 1

    args = argparse.Namespace(
        filename='name.csv',
        ticket='TICKET-ID',
        dry_run=False,
        force_delete=False,
    )
    with patch('argparse.ArgumentParser.parse_args', return_value=args):
        run()

    # After update:
    assert VisibilityPromotionCandidate.objects.all().count() == 1
    assert VisibilityPromotionCandidate.objects.first().business_id == business_id
    assert VisibilityPromotionCandidate.objects.filter(active=True).count() == 1


@pytest.mark.django_db
def test_delete(gcs_client_mock):
    business_id = 1234
    visibility_promotion_candidate = baker.make(
        VisibilityPromotionCandidate, business_id=business_id
    )
    updated_rows = [
        VisibilityPromotionCandidateData(
            business_id=business_id, id=visibility_promotion_candidate.id, active=True
        )
    ]
    gcs_client_mock.return_value.get_data = Mock(return_value=_get_mocked_input_file(updated_rows))

    # Before update:
    assert VisibilityPromotionCandidate.objects.all().count() == 1
    assert VisibilityPromotionCandidate.objects.first().business_id == business_id
    assert VisibilityPromotionCandidate.objects.filter(active=False).count() == 1

    args = argparse.Namespace(
        filename='name.csv',
        ticket='TICKET-ID',
        dry_run=False,
        force_delete=True,
    )
    with patch('argparse.ArgumentParser.parse_args', return_value=args):
        run()

    # After update:
    assert VisibilityPromotionCandidate.objects.all().count() == 0
