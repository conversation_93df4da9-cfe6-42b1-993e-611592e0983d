import argparse

# pylint: disable=logging-fstring-interpolation
import logging
from io import BytesIO
from pathlib import PosixPath

import django
import pandas as pd

django.setup()  # noqa
# pylint: disable=wrong-import-position
from rest_framework import serializers
from tqdm import tqdm
from lib import gcs
from lib.tools import tznow
from webapps.visibility_promotion.models import VisibilityPromotionCandidate


logger = logging.getLogger('booksy.boost_warnings')


class VisibilityPromotionCandidateSerializer(serializers.ModelSerializer):
    class Meta:
        model = VisibilityPromotionCandidate
        fields = ('business_id', 'active')


def _get_data(filename, ticket_id):
    storage = gcs.GCSClient()

    filepath = str(PosixPath(ticket_id) / f'{filename}.csv')
    data = storage.get_data(filepath, as_bytes=True)
    if not data:
        raise RuntimeError(  # pylint: disable=broad-exception-raised
            f'The file {filepath} does not exist.'
        )

    return pd.read_csv(BytesIO(data), keep_default_na=False).to_dict(orient='records')


def _upload_report(filename, ticket_id, data):
    storage = gcs.GCSClient()

    time_ = str(tznow().timestamp()).replace('.', '')
    storage.save_data(str(PosixPath(ticket_id) / f'{filename}_report_{time_}.csv'), data)


def _get_parser():
    parser = argparse.ArgumentParser()
    parser.add_argument('filename')
    parser.add_argument('--ticket', type=str, required=True)
    parser.add_argument('--dry-run', action='store_true')
    parser.add_argument('--force-delete', action='store_true')
    return parser


def _save_in_db(data_row):
    candidate_id = data_row.pop('id', None)
    candidate_id = None if candidate_id == '' else candidate_id
    instance = (
        VisibilityPromotionCandidate.objects.filter(id=candidate_id).first()
        if candidate_id
        else None
    )

    serializer = VisibilityPromotionCandidateSerializer(instance=instance, data=data_row)
    serializer.is_valid(raise_exception=True)
    instance = serializer.save()
    data_row['id'] = instance.id


def _delete_from_db(data_row):
    business_id = str(data_row['business_id'])
    candidate_to_delete = VisibilityPromotionCandidate.objects.filter(business_id=business_id)
    logger.info(f'Deleting: {candidate_to_delete}...')
    candidate_to_delete.delete()


def run():
    parser = _get_parser()
    args = parser.parse_args()

    data = _get_data(filename=args.filename, ticket_id=args.ticket)

    for row in tqdm(data):
        if args.dry_run:
            logger.info(f'[dry-run] Would save data row: {row}...')
        elif args.force_delete:
            logger.info(f'Deleting data row: {row}...')
            _delete_from_db(row)
        else:
            logger.info(f'Saving data row: {row}...')
            _save_in_db(row)

    _upload_report(
        filename=args.filename,
        ticket_id=args.ticket,
        data=pd.DataFrame(data).to_csv(index=False),
    )


if __name__ == '__main__':
    run()
