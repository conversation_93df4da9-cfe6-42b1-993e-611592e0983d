import pytest

from cliapps.booking.remove_old_services import clear_old_business_ids
from lib.tools import tznow
from webapps.business.baker_recipes import business_recipe, service_recipe, service_variant_recipe


@pytest.mark.django_db
def test_clear_old_business_ids():
    business = business_recipe.make()
    services = service_recipe.make(business=business, active=False, _quantity=4)
    services[3].active = True
    services[3].save()
    deleted_service = service_recipe.make(business=business, active=True, deleted=tznow())
    active_not_active_service = service_variant_recipe.make(service=services[0])
    not_active_not_active_service = service_variant_recipe.make(service=services[1], active=False)
    not_active_not_active_service2 = service_variant_recipe.make(service=services[1], active=False)
    not_active_not_active_service_with_variants = service_variant_recipe.make(
        service=services[2], active=False
    )
    active_not_active_service_with_variants = service_variant_recipe.make(service=services[2])
    not_active_active_service = service_variant_recipe.make(service=services[3], active=False)
    active_deleted_service = service_variant_recipe.make(service=deleted_service, active=True)
    not_active_deleted_service = service_variant_recipe.make(service=deleted_service, active=True)
    service_variants = [
        active_not_active_service,
        not_active_active_service,
        not_active_not_active_service,
        not_active_not_active_service2,
        not_active_not_active_service_with_variants,
        active_not_active_service_with_variants,
        not_active_active_service,
        active_deleted_service,
        not_active_deleted_service,
    ]
    clear_old_business_ids(business_id=business.id, dry_run=True)
    refresh(services, service_variants)
    assert all(service.deleted is None for service in services)
    assert all(variant.deleted is None for variant in service_variants)
    clear_old_business_ids(business_id=business.id, dry_run=False)
    refresh(services, service_variants)
    assert active_not_active_service.deleted
    assert not_active_not_active_service.deleted
    assert not_active_not_active_service2.deleted
    assert not_active_not_active_service_with_variants.deleted
    assert active_not_active_service_with_variants.deleted
    assert not_active_active_service.deleted is None
    assert active_deleted_service.deleted
    assert not_active_deleted_service.deleted
    assert services[3].deleted is None
    for i in [0, 1, 2]:
        assert services[i].deleted
    assert deleted_service.deleted


@pytest.mark.django_db
def test_clear_old_business_ids_with_service_variants():
    business = business_recipe.make()
    services = service_recipe.make(business=business, active=True, _quantity=3)
    inactive_service_variants, active_service_variants = [], []
    for _ in range(2):
        inactive_service_variants.append(
            service_variant_recipe.make(service=services[0], active=False)
        )
        active_service_variants.append(
            service_variant_recipe.make(service=services[0], active=True)
        )
        active_service_variants.append(
            service_variant_recipe.make(service=services[1], active=True)
        )
        inactive_service_variants.append(
            service_variant_recipe.make(service=services[2], active=False)
        )
    clear_old_business_ids(business_id=business.id, dry_run=True, with_service_variants=True)
    refresh(services, active_service_variants + inactive_service_variants)
    assert all(service.deleted is None for service in services)
    assert all(
        variant.deleted is None for variant in active_service_variants + inactive_service_variants
    )
    clear_old_business_ids(business_id=business.id, dry_run=False, with_service_variants=True)
    refresh(services, active_service_variants + inactive_service_variants)
    assert all(service.deleted is None for service in services[:2])
    assert services[2].deleted is not None
    assert all(variant.deleted is None for variant in active_service_variants)
    assert all(variant.deleted for variant in inactive_service_variants)


def refresh(services, service_variants):
    for service in services:
        service.refresh_from_db()
    for service_variant in service_variants:
        service_variant.refresh_from_db()
