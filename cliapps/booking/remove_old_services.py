import argparse
import django
from tqdm import tqdm

django.setup()  # noqa

# pylint: disable=wrong-import-position

from webapps.business.models import Service, ServiceVariant


# pylint: enable=wrong-import-position
def main():
    parser = argparse.ArgumentParser(
        description="""
                Script for removing old services for business. Services that are not active
                and have only non-active service variants without booking transactions and vouchers
                connected will be removed. Service variants are removed with services.
            """
    )
    parser.add_argument('--dry-run', action='store_true')
    parser.add_argument(
        '--business_id',
        type=int,
        required=True,
        help='id of the business that should be have old services removed',
    )
    parser.add_argument(
        '--with_service_variants',
        action='store_true',
        help='should inactive service variants belonging to active services be removed',
    )
    args = parser.parse_args()
    clear_old_business_ids(
        business_id=args.business_id,
        dry_run=args.dry_run,
        with_service_variants=args.with_service_variants,
    )


def clear_old_business_ids(
    business_id: int, dry_run: bool = False, with_service_variants: bool = False
):  # pylint: disable=too-many-branches
    inactive_services = Service.objects.filter(business_id=business_id, active=False)
    deleted_services = Service.all_objects.filter(deleted__isnull=False, business_id=business_id)
    services_to_remove_cnt, variants_to_remove_cnt = 0, 0
    services_removed_cnt, variants_removed_cnt = 0, 0

    for service in tqdm(deleted_services):
        for variant in service.service_variants.all():
            if variant.can_be_safely_deleted():
                variants_to_remove_cnt += 1
            if not dry_run:
                variants_removed_cnt += int(remove_variant(variant))

    for service in tqdm(inactive_services):
        can_be_safely_deleted = True

        for variant in service.service_variants.all():
            if not variant.can_be_safely_deleted():
                can_be_safely_deleted = False
            else:
                variants_to_remove_cnt += 1
                if not dry_run:
                    variants_removed_cnt += int(remove_variant(variant))
        if can_be_safely_deleted:
            services_to_remove_cnt += 1
            if not dry_run:
                services_removed_cnt += int(remove_service(service))

    print(
        f"{services_to_remove_cnt} services and {variants_to_remove_cnt}"
        f" service variants should be removed"
    )
    if not dry_run:
        print(
            f"{services_removed_cnt} services and {variants_removed_cnt}"
            f" service variants were removed"
        )

    if with_service_variants:
        remove_service_variants(business_id, dry_run)


def remove_service_variants(business_id: int, dry_run: bool = False):
    inactive_service_variants = ServiceVariant.objects.filter(
        service__business_id=business_id, active=False, service__active=True
    )
    additional_variants_to_remove_cnt, additional_variants_removed_cnt = 0, 0
    additional_services_removed_cnt = 0
    service_ids = set()
    for service_variant in tqdm(inactive_service_variants):
        if service_variant.can_be_safely_deleted():
            additional_variants_to_remove_cnt += 1
            service_ids.add(service_variant.service_id)
            if not dry_run:
                additional_variants_removed_cnt += bool(remove_variant(service_variant))
    if not dry_run:
        for service in tqdm(Service.objects.filter(id__in=service_ids)):
            if not service.active_variants:
                remove_service(service)
                additional_services_removed_cnt += 1

    print(
        f"Additional {additional_variants_to_remove_cnt} service variants "
        f"from {len(service_ids)} services should be removed."
    )
    if not dry_run:
        print(
            f"Additional {additional_variants_removed_cnt} service variants "
            f"and {additional_services_removed_cnt} services were removed"
        )


def remove_service(service: Service) -> bool:
    service.safe_delete()
    return bool(service.deleted)


def remove_variant(variant: ServiceVariant) -> bool:
    variant.safe_delete()
    return bool(variant.deleted)


if __name__ == '__main__':
    main()
