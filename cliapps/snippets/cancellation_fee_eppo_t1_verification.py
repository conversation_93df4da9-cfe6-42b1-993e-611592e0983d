import argparse
import json
import random
import sys

from lib.feature_flag.adapter import UserData
from lib.feature_flag.enums import AppDomains, SubjectType
from lib.feature_flag.feature.customer import (
    CancellationFeeCustomerBookingFlowTestFlag,
    CancellationFeeEppoExperimentTest,
)


def parse_bool(value: str) -> bool:
    normalized = value.strip().lower()
    if normalized in {"1", "true", "t", "yes", "y"}:
        return True
    if normalized in {"0", "false", "f", "no", "n"}:
        return False
    raise argparse.ArgumentTypeError(f"Invalid boolean value: {value}")


def eval_flag(biz_id: int, cust_id: int, enabled: bool) -> dict:
    user_data = UserData(
        subject_key=cust_id,  # randomization by customer
        subject_type=SubjectType.USER_ID.value,
        app_domain=AppDomains.CUSTOMER.value,
        custom={
            "biz_id": biz_id,
            "cust_id": cust_id,
            "is_cancellation_fee_enabled": enabled,
            "is_experiment": True,
        },
    )
    return CancellationFeeCustomerBookingFlowTestFlag(user_data) or {}


def run_random(n: int = 5, enabled: bool | None = None) -> None:
    """Print n random evaluations. Useful in Django shell.

    Example:
        from cliapps.snippets.test_cancellation_fee_eppo import run_random
        run_random(5, enabled=True)
    """
    print("experiment_gate_on:", bool(CancellationFeeEppoExperimentTest()))
    for _ in range(n):
        biz_id = random.randint(1000, 9999)
        cust_id = random.randint(100_000, 999_999)
        is_enabled = enabled if enabled is not None else random.choice([True, False])
        decision = eval_flag(biz_id, cust_id, is_enabled)
        print(
            json.dumps(
                {
                    "biz_id": biz_id,
                    "cust_id": cust_id,
                    "enabled": is_enabled,
                    "decision": decision,
                },
                indent=2,
                sort_keys=True,
            )
        )


def main(argv: list[str]) -> int:
    parser = argparse.ArgumentParser(
        description=(
            "Evaluate CancellationFeeCustomerBookingFlowTestFlag with given biz_id, "
            "cust_id and is_cancellation_fee_enabled."
        )
    )
    parser.add_argument("--biz-id", type=int, required=True, help="Business ID (int)")
    parser.add_argument("--cust-id", type=int, required=True, help="Customer ID (int)")
    parser.add_argument(
        "--enabled",
        type=parse_bool,
        required=True,
        help="is_cancellation_fee_enabled (true/false)",
    )
    args = parser.parse_args(argv)

    # Experiment gate – allow running even if off; prints both results
    experiment_on = bool(CancellationFeeEppoExperimentTest())
    decision = eval_flag(args.biz_id, args.cust_id, args.enabled)

    print(
        json.dumps(
            {
                "experiment_on": experiment_on,
                "input": {
                    "biz_id": args.biz_id,
                    "cust_id": args.cust_id,
                    "is_cancellation_fee_enabled": args.enabled,
                },
                "decision": decision,
            },
            indent=2,
            sort_keys=True,
        )
    )
    return 0


if __name__ == "__main__":
    raise SystemExit(main(sys.argv[1:]))

"""
In [10]: run_random(5, enabled=True)
{
  "biz_id": 6337,
  "cust_id": 853436,
  "decision": {
    "show_hold_message": "false",
    "show_loader": "false"
  },
  "enabled": true
}
{
  "biz_id": 8747,
  "cust_id": 971673,
  "decision": {
    "show_hold_message": "true",
    "show_loader": "true"
  },
  "enabled": true
}
{"deployment_level": "payment-solutions.t1.booksy.pm", "api_country": "pl", "log_name": "datadog.dogstatsd", "log_level": "WARNING", "severity": "WARNING", "published_date": "2025-08-08T08:58:44.284518", "message": "Error submitting packet: [Errno 111] Connection refused, dropping the packet and closing the socket", "dd.version": "PSE-841-a-b-tests-based-on-cx-1aaf7993", "dd.env": "payment-solutions-pl", "dd.service": "core-tools", "dd.trace_id": "6895bc44000000009204f1262376ff6c", "dd.span_id": "17911678942227544539"}
{
  "biz_id": 7133,
  "cust_id": 791207,
  "decision": {
    "show_hold_message": "true",
    "show_loader": "true"
  },
  "enabled": true
}
{
  "biz_id": 1339,
  "cust_id": 799199,
  "decision": {
    "show_hold_message": "false",
    "show_loader": "false"
  },
  "enabled": true
}
{"deployment_level": "payment-solutions.t1.booksy.pm", "api_country": "pl", "log_name": "datadog.dogstatsd", "log_level": "WARNING", "severity": "WARNING", "published_date": "2025-08-08T08:58:44.285238", "message": "Error submitting packet: [Errno 111] Connection refused, dropping the packet and closing the socket", "dd.version": "PSE-841-a-b-tests-based-on-cx-1aaf7993", "dd.env": "payment-solutions-pl", "dd.service": "core-tools", "dd.trace_id": "6895bc4400000000ae5b3213b5b0ca6e", "dd.span_id": "3688871252045497514"}
{
  "biz_id": 3133,
  "cust_id": 642402,
  "decision": {
    "show_hold_message": "false",
    "show_loader": "false"
  },
  "enabled": true
}
"""
