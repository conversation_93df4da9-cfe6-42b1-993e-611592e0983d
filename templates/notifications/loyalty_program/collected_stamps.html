{% extends "email_base_business.html" %}
{% import "email_macros.html" as macros %}

{# Required variables below 👇 #}
{# They should be be provided during rendering. #}
{# Uncommented them to render the template locally #}
{#
{% set BUSINESS_NAME = "Booksy Barber" %}
{% set BOOK_APPOINTMENT_URL = "/" %}
{% set GO_TO_CARDS_URL = "/" %}
{% set AVATAR_URL = "https://i.pravatar.cc/150?img=12" %}
{% set AVATAR_NAME = "John Doe" %}
{% set CARD_BACKGROUND_URL = "https://d2zdpiztbgorvt.cloudfront.net/region1/giftcard_default_backgrounds/Background_16.jpg" %}
{% set LOYALTY_PROGRAM_TITLE = "test" %}
{% set LOYALTY_PROGRAM_DESCRIPTION = "Collect 8 stamps to earn a reward" %}
{% set LOYALTY_PROGRAM_POINTS_COLLECTED = 8 %}
{% set LOYALTY_PROGRAM_POINTS_TOTAL = 8 %}
#}


{% block email_title %}
{{ _('Reward unlocked!') }}
{% endblock email_title %}

{% block email_content %}
   {% block inserted_content %}
      <table>
         <tr>
            <td style="padding: 0 48px;">
               <table style="width: 100%; border-collapse: collapse">
                  <tr>
                     <td>&nbsp;</td>
                     <td style="width: 320px">
                        <table style="background: #ccc{% if CARD_BACKGROUND_URL %} url({{ CARD_BACKGROUND_URL }}) no-repeat center/cover{% endif %}; width: 100%; border-radius: 16px; border-collapse: collapse">
                           <tr>
                              <td style="padding: 16px">
                                 <table style="width: 100%; border-collapse: collapse">
                                    <tr>
                                       <td style="color: #fff; font-family: arial; font-size: 14px; line-height: 24px;">
                                          {{ _('Loyalty card') }}
                                       </td>
                                       <td
                                          style="text-align: right; color: #fff; font-family: arial; font-size: 14px; line-height: 24px;">
                                          {{ _('Collected') }}
                                       </td>
                                    </tr>
                                    <tr>
                                       <td style="color: #fff; font-family: arial; font-size: 14px; line-height: 24px;">
                                          <table style="border-collapse: collapse; border-radius: 50px; background: #E2F6EA;">
                                             <tr>
                                                <td
                                                   style="color: #fff; font-family: arial; font-size: 12px; line-height: 16px; color: #00A34B; padding: 2px 8px;">
                                                   {{ _('Completed') }}</td>
                                             </tr>
                                          </table>
                                       </td>
                                       <td
                                          style="text-align: right; color: #fff; font-family: arial; font-size: 18px; line-height: 24px;">
                                          <strong>{{ LOYALTY_PROGRAM_POINTS_COLLECTED }}/{{ LOYALTY_PROGRAM_POINTS_TOTAL }}</strong>
                                       </td>
                                    </tr>
                                    <tr>
                                       <td colspan="2"
                                          style="color: #fff; font-family: arial; font-size: 16px; line-height: 24px; padding: 16px 0 0">
                                          <strong>{{ LOYALTY_PROGRAM_TITLE }}</strong>
                                       </td>
                                    </tr>
                                    <tr>
                                       <td colspan="2"
                                          style="color: #fff; font-family: arial; font-size: 14px; line-height: 24px; padding: 0 0 16px">
                                          {{ LOYALTY_PROGRAM_DESCRIPTION }}
                                       </td>
                                    </tr>
                                    <tr>
                                       <td colspan="2">
                                          <img src="{{ AVATAR_URL}}"
                                             style="width: 32px; height: 32px; border-radius: 50%; display: block" />
                                       </td>
                                    </tr>
                                    <tr>
                                       <td colspan="2" style="color: #fff; font-family: arial; font-size: 12px; line-height: 16px;">
                                          {{ AVATAR_NAME}}
                                       </td>
                                    </tr>
                                 </table>
                              </td>
                           </tr>
                        </table>
                     </td>
                     <td>&nbsp;</td>
                  </tr>
               </table>
            </td>
         </tr>
         <tr>
            <td style="padding: 24px 48px 0;">
               <p style="font: bold 18px/30px Arial, Helvetica, sans-serif; text-align: center; font-weight: 600;">
                  {{ _('Reward unlocked!') }}
               </p>
               
               {% call macros.paragraph() %}
               {{ _('Great news! You\'ve successfully collected all your stamps and earned a reward from {business_name}.').format(business_name=BUSINESS_NAME) }}
               {% endcall %}
               
               {% call macros.paragraph() %}
               {{ _('The reward is customized by {business_name} and will be applied during your upcoming appointment.').format(business_name=BUSINESS_NAME) }}
               {% endcall %}
               
               {% call macros.paragraph() %}
               <strong>{{ _('To redeem it, simply ask about it when you visit.') }}</strong>
               {% endcall %}
               
               {% call macros.paragraph() %}
               {{ _('Please note, you might not see the reward details reflected in the booking process itself.') }}
               {% endcall %}
               
               {% call macros.paragraph() %}
               {{ _('We appreciate your loyalty! Get ready to enjoy your special perk by booking your next appointment now.') }}
               {% endcall %}

               {% call macros.paragraph() %}{% endcall %}
               
               {% call macros.extended_button(url=BOOK_APPOINTMENT_URL, display_style="block", font_size="16px", lines_after=1, border_radius="8px") %}
               {{ _('Book appointment') }}
               {% endcall %}
               
               {% call macros.extended_button(url=GO_TO_CARDS_URL, background_color="#ffffff", color="#000000", display_style="block", font_size="16px", border=true, border_radius="8px") %}
               {{ _('Go to your loyalty cards') }}
               {% endcall %}
            </td>
         </tr>
      </table>
   {% endblock inserted_content %}
{% endblock email_content %}