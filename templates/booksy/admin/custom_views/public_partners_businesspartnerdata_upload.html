{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}Business Partner Data Upload{% endblock title %}

{% block extrastyle %}
{{ block.super }}
<link rel="stylesheet" type="text/css" href="{% static "admin/css/forms.css" %}" />

{% endblock extrastyle %}

{% block content %}
<h3>BusinessPartnerData Importer</h3>
<ol>
    <li>Filling form
        <ul>
            <li>Fill file fields:
                <ul>
                    <li><code>A</code> Business ID (business_id - <code>number field</code>)
                    <li><code>B</code> Import UID (import_uid - <code>char field</code>) - Business id from partner old database.
                    <li><code>C</code> Importer Name (importer_name - <code>char field</code>) - <strong>Optional</strong>. Name of the importer/source. If empty or missing, will use "Versum" as default.
                    <li> Do not include columns headers. Only values.
                </ul>
            </li>
        </ul>
    </li>
    <li>Upload the file in XSLX format.</li>
    <li>Click "Import" button.</li>
    <li>If something goes wrong, you will see the summary message with errors. If file is valid you will see information about it under the button.</li>
</ol>
<form action="" method="POST" enctype="multipart/form-data">
    {% csrf_token %}
    {{ form.as_table }}<br>
    <input type="submit" name="import" value="Upload businesses" class="btn btn-primary" />
</form>

{% if errors %}
    {% for error in errors %}
    <div class="alert alert-danger" role="alert">
        <span class="glyphicon glyphicon-exclamation-sign" aria-hidden="true"></span>
        <span class="sr-only">Error:</span>
        {{ error }}
    </div><br>
    {% endfor %}
{% endif %}

{% if warnings %}
    {% for warning in warnings %}
    <div class="alert alert-warning" role="alert">
        <span class="glyphicon glyphicon-exclamation-sign" aria-hidden="true"></span>
        <span class="sr-only">Warning:</span>
        {{ warning }}
    </div><br>
    {% endfor %}
{% endif %}

{% if success_message %}
    <div class="alert alert-success" role="alert">
        {{ success_message }}
    </div>
{% endif %}

{% if error_message %}
    <div class="alert alert-danger" role="alert">
        {{ error_message }}
    </div>
{% endif %}

{% endblock content %}