syntax = "proto3";

package payments_service.pubsub.v1;

message PaymentSucceededEvent {
  string event_id = 1;
  string event_type = 2;
  string event_version = 3;
  int64 created = 4;
  PaymentSucceededEventData data = 5;

  message PaymentSucceededEventData {
    string basket_id = 1;
    string basket_payment_id = 2;
    string balance_transaction_id = 3;
    int64 business_id = 4;
    int64 user_id = 5;
    string payment_method = 6;
    string source = 7;
    string status = 8;
    int64 created = 9;
  }
}
