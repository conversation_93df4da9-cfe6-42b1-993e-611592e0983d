syntax = "proto3";

package payments_service.pubsub.v1;

message PaymentProcessEvent {
  string event_id = 1;
  string event_type = 2;
  string event_version = 3;
  int64 created = 4;
  PaymentProcessEventData data = 5;

  message PaymentMethod {
    string type = 1;
    oneof payment_details {
      string token = 2;
      string tokenized_pm_id = 3;
    }
  }

  message Tip {
    string type = 1;
    int64 value = 2;
  }

  message PaymentProcessEventData {
    string basket_id = 1;
    PaymentMethod payment_method = 2;
    Tip tip = 3;
  }
}
