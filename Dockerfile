ARG CURRENT_UID
ARG CURRENT_GUID
FROM europe-west1-docker.pkg.dev/bks-ar-pkg/images/python:3.11.13-slim-bookworm
# Upgrade base image system packages as those are separate from packages defined
# in requirements.txt files and bumping a package version there does not always
# fix the vulnerabilities found by dependency audit.
RUN pip install --upgrade \
    setuptools==78.1.1;

ENV TIMEZONE UTC
RUN ln -snf /usr/share/zoneinfo/$TIMEZONE /etc/localtime && \
    echo $TIMEZONE > /etc/timezone

# install system deps
RUN apt-get update && apt-get install --no-install-recommends -y \
    build-essential \
    bash \
    telnet \
    tmux \
    vim \
    less \
    htop \
    strace \
    curl \
    ca-certificates  \
    gnupg \
    git \
    procps \
    wget \
    net-tools \
    tcpdump \
    iproute2 \
    # core specific packages
    gettext \
    libgettextpo-dev \
    redis \
    libffi-dev \
    libgdk-pixbuf2.0-0 \
    libgeos-dev \
    libpangocairo-1.0-0 \
    openssl \
    xmlsec1 \
    libpcre3-dev \
    apache2-utils \
    libexpat1 \
    libtasn1-6=4.19.0-2+deb12u1 \
    gnutls-bin=3.7.9-2+deb12u4 \
    perl=5.36.0-7+deb12u2


# install postgresql-client-14 to match our postgres version
RUN curl https://www.postgresql.org/media/keys/ACCC4CF8.asc \
    | gpg --dearmor \
    | tee /etc/apt/trusted.gpg.d/apt.postgresql.org.gpg >/dev/null
RUN sh -c 'echo "deb http://apt.postgresql.org/pub/repos/apt/ bookworm-pgdg main" \
    > /etc/apt/sources.list.d/postgresql.list'
RUN apt update && apt install -y \
    libpq-dev \
    postgresql-client-14

# clean apt stuff to save space
RUN apt-get clean  \
    && rm -rf /var/lib/apt/lists/*

# create user that map host user uid and gid
ARG CURRENT_UID=${CURRENT_UID}
ARG CURRENT_GUID=${CURRENT_GUID}
COPY commands/validate_uid_and_guid.sh validate_uid_and_guid.sh
RUN ./validate_uid_and_guid.sh
RUN rm ./validate_uid_and_guid.sh
RUN getent group $CURRENT_GUID || groupadd booksy -g $CURRENT_GUID
RUN useradd --create-home booksy -u $CURRENT_UID -g $CURRENT_GUID -s /bin/bash
ENV BASE_PATH "/home/<USER>"
WORKDIR "${BASE_PATH}/code"
USER booksy
# create directories
RUN mkdir -p "${BASE_PATH}/media"
RUN mkdir -p "${BASE_PATH}/business-files"
RUN mkdir -p "${BASE_PATH}/logs"
RUN mkdir -p "${BASE_PATH}/feeds"
RUN mkdir -p "${BASE_PATH}/smtp_sink_emails"

# prevent pytest create __pycache__ and .pytest_cache
ENV PYTHONUNBUFFERED 1
ENV PYTHONDONTWRITEBYTECODE 1
# set python path and bash path
ENV PYTHONPATH "${PYTHONPATH}:${BASE_PATH}/code"
ENV PATH "${PATH}:${BASE_PATH}/code:${BASE_PATH}/.local/bin"
# django settings
ENV DJANGO_SETTINGS_MODULE settings
# SRE-2378: the env var set here prevents ddtrace from pushing traces to localhost when using custom instrumentation
ENV DD_TRACE_ENABLED "false"

# PY-1257 PY-1264 - remove this after all proto files are updated
# https://developers.google.com/protocol-buffers/docs/news/2022-05-06#python-updates'
ENV PROTOCOL_BUFFERS_PYTHON_IMPLEMENTATION=python

# Added remporarely due to lock in datadog
ENV DD_TRACE_SPAN_AGGREGATOR_RLOCK=True

# install uv
RUN pip install uv==0.8.6
ENV UV_KEYRING_PROVIDER=subprocess
ENV UV_BREAK_SYSTEM_PACKAGES=True
ENV UV_COMPILE_BYTECODE=True
ENV UV_NO_CACHE=True

# AR auth requirements
COPY requirements-pre-init.txt requirements-pre-init.txt
RUN uv pip install \
    --no-cache-dir \
    --prefix ~/.local \
    -r "${BASE_PATH}/code/requirements-pre-init.txt"

# install initial python deps
COPY requirements-initial.txt requirements-initial.txt
RUN --mount=type=secret,id=gcp,mode=0444,target=/home/<USER>/.config/gcloud/application_default_credentials.json \
    uv pip install \
    --prefix ~/.local \
    --no-cache-dir \
    -r "${BASE_PATH}/code/requirements-initial.txt"

RUN mkdir -p "${BASE_PATH}/tmp"

# install both requirements.txt and requirements-dev.txt at the same time
# to ensure that one doesn't secretly upgrade other's dependencies
# e.g. notebook upgrades tornado to an unsupported version
COPY requirements.txt requirements.txt
COPY requirements-dev.txt requirements-dev.txt
COPY requirements-booking.txt requirements-booking.txt
RUN --mount=type=secret,id=gcp,mode=0444,target=/home/<USER>/.config/gcloud/application_default_credentials.json \
    uv pip install \
    --prefix ~/.local \
    --no-build-isolation \
    --trusted-host pypi.python.org \
    -r "${BASE_PATH}/code/requirements.txt" \
    -r "${BASE_PATH}/code/requirements-dev.txt" \
    -r "${BASE_PATH}/code/requirements-booking.txt"

COPY requirements-booksy.txt requirements-booksy.txt 
RUN --mount=type=secret,id=gcp,mode=0444,target=/home/<USER>/.config/gcloud/application_default_credentials.json \
    uv pip install --prefix ~/.local -r "${BASE_PATH}/code/requirements-booksy.txt"

COPY . "${BASE_PATH}/code"
